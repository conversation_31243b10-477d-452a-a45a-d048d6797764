﻿/***************************************************************************
 * 
 *       功能：     仓库区域规划持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// WH_AREA
	/// </summary>
	public class P_WH_AREA : P_Base_House
	{
		public P_WH_AREA ()
		{
			//
			// TODO: 此处添加WH_AREA的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<WH_AREA> GetList()
		{
			return ExecuteQueryForList<WH_AREA>("WH_AREA_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(WH_AREA wh_area)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("WH_AREA");
                wh_area.AREA_ID = id;
            }

            return ExecuteInsert("WH_AREA_INSERT",wh_area);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(WH_AREA wh_area)
		{
			return ExecuteUpdate("WH_AREA_UPDATE",wh_area);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public WH_AREA GetModel(System.Int32 AREA_ID)
		{
			return ExecuteQueryForObject<WH_AREA>("WH_AREA_SELECT_BY_ID",AREA_ID);
		}

        public object GetModelObject(System.Int32 AREA_ID)
        {
            return ExecuteQueryForObject("WH_AREA_SELECT_BY_ID", AREA_ID);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 AREA_ID)
		{
			return ExecuteDelete("WH_AREA_DELETE",AREA_ID);
		}
		

	}
}
