﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 四楼入库分配货位申请
    /// </summary>
    public class ApplyAllocateCell : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCell.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);

                if(mMANAGE_MAIN!=null&& mSTORAGE_MAIN!=null)
                {
                    bResult = false;
                    sResult = string.Format("申请条码同时存在库存和任务，请根据箱子实际情况将多余的任务删除或将异常的库存出库");
                    return bResult;
                }
                else if (mMANAGE_MAIN != null && mMANAGE_MAIN.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManagePick.ToString())
                {
                    //出库的异常任务有可能被调度送到条码读头
                    if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.Error.ToString()
                        || mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.ExceptionComplete.ToString())
                    {
                        bResult = false;
                        sResult = string.Format("申请条码指向的任务状态异常");
                        return bResult;
                    }

                    string applyStation1 = string.Empty;
                    string applyStation2 = string.Empty;
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageStockIn.ToString() &&
                        this._S_SystemService.GetSysParameter("AllocateCellApplyStation-1", out applyStation1) &&
                        this._S_SystemService.GetSysParameter("AllocateCellApplyStation-2", out applyStation2) &&
                        (applyStation1 == mIO_CONTROL_APPLY.DEVICE_CODE || applyStation2 == mIO_CONTROL_APPLY.DEVICE_CODE))
                    {
                        //如果是空箱入库路过货位分配申请口时，要判断空箱缓存线是否需要补给空箱
                        string supplyEmptyBoxFromFloorOneConfig = string.Empty;
                        if(this._S_SystemService.GetSysParameter("SupplyEmptyBoxFromFloorOneConfig",out supplyEmptyBoxFromFloorOneConfig) &&
                            supplyEmptyBoxFromFloorOneConfig.Contains(mIO_CONTROL_APPLY.DEVICE_CODE))
                        {
                            string[] configs = supplyEmptyBoxFromFloorOneConfig.Split('|').Where(r => r.Contains(mIO_CONTROL_APPLY.DEVICE_CODE)).First().Split('-');

                            int ibufferThresHold = 0;
                            int iCurrentBufferValue = 0;
                            //string bufferStation = string.Empty;

                            if (configs[0] == Enum.FLAG.Enable.ToString() &&
                                int.TryParse(configs[1], out ibufferThresHold) &&
                                this._S_ManageService.GetCurrentBufferValue(configs[2], out iCurrentBufferValue) &&
                                (ibufferThresHold > iCurrentBufferValue))
                            {
                                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(configs[2]);
                                mMANAGE_MAIN.END_CELL_ID = mWH_CELL_END.CELL_ID;
                                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                            }
                        }
                    }

                    //验证入库的物料是否存在盘点计划
                    IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);
                    if (lsMANAGE_LIST != null && lsMANAGE_LIST.Count > 0)
                    {
                        StringBuilder goodsIdCombine = new StringBuilder();
                        foreach (var mMANAGE_LIST in lsMANAGE_LIST)
                        {
                            goodsIdCombine.Append(mMANAGE_LIST.GOODS_ID);
                            goodsIdCombine.Append(",");
                        }
                        string goodsIdSql = (string.IsNullOrEmpty(goodsIdCombine.ToString().TrimEnd(','))) ? "" : string.Format("and GOODS_ID in ({0})", goodsIdCombine.ToString().TrimEnd(','));

                        DataTable dtPlanListCheck = this.GetList(string.Format("select 0 from V_PLAN_LIST where PLAN_TYPE_CODE = 'PlanCheck' and PLAN_STATUS!='Finish' {0}", goodsIdSql));
                        if (dtPlanListCheck != null && dtPlanListCheck.Rows.Count > 0)
                        {
                            bResult = false;
                            sResult = string.Format("箱中至少存在一种物料正在进行盘点_不允许入库_箱条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                            return bResult;
                        }
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                    {
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoadTrans", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), false }, out sResult);
                    }
                    else
                    {
                        bool wsNoticeWcs = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE == "3";
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoad", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), true, wsNoticeWcs }, out sResult);
                    }
                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                else if (mSTORAGE_MAIN != null)
                {
                    //IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    //Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel("emptyBox");

                    //wdz add 2018-04-18
                    if (mSTORAGE_MAIN.IS_EXCEPTION == "1")
                    {
                        bResult = false;
                        sResult = string.Format("箱条码[{0}]库存已被标记为异常", mSTORAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    //如果库存为待见选箱，则认为是异常剔除
                    if (mSTORAGE_MAIN.CELL_MODEL == Enum.CellModel.PickingBox.ToString("d"))
                    {
                        bResult = false;
                        sResult = string.Format("箱条码[{0}]为待见选箱，不应该通过入库扫码器", mSTORAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    //mMANAGE_MAIN.MANAGE_OPERATOR = string.Format("{0}申请", mIO_CONTROL_APPLY.DEVICE_CODE);
                    mMANAGE_MAIN.MANAGE_OPERATOR = mSTORAGE_MAIN.STORAGE_PICK_OPERATOR;
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.START_CELL_ID = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE).CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                    mMANAGE_MAIN.END_CELL_ID = 0;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    string manageLevel = string.Empty;
                    mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("ApplyTaskLevel", out manageLevel) ? manageLevel : "0";

                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();   //上架不需要使用TASK_SOURCE判断接口

                    /////////////需要更改

                    if (mSTORAGE_MAIN.CELL_MODEL == Enum.CellModel.KitBox.ToString("d") && mSTORAGE_MAIN.KITBOX_UP_COMPLETE != "1")
                    {
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageKitUp.ToString();
                    }
                    else
                    {
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageUp.ToString();
                    }
                    //mMANAGE_MAIN.MANAGE_TYPE_CODE = mSTORAGE_MAIN.CELL_MODEL == Enum.CellModel.KitBox.ToString("d") ?
                    //    Enum.MANAGE_TYPE.ManageKitUp.ToString() : Enum.MANAGE_TYPE.ManageUp.ToString();

                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";

                    bool wsNoticeWcs = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE == "3";
                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                    bResult = this._S_ManageService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, true, true, wsNoticeWcs, false }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                else
                {
                    string scanAutoEmptyBoxIn = string.Empty;
                    if (this._S_SystemService.GetSysParameter("InitializeEmptyBox", out scanAutoEmptyBoxIn) && scanAutoEmptyBoxIn == Enum.FLAG.Enable.ToString())
                    {
                        mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                        mMANAGE_MAIN.END_CELL_ID = 0;
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                        string manageLevel = string.Empty;
                        mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("ApplyTaskLevel", out manageLevel) ? manageLevel : "0";
                        mMANAGE_MAIN.MANAGE_OPERATOR = "空箱扫码入库";
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageStockIn.ToString();
                        mMANAGE_MAIN.PLAN_ID = 0;
                        mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                        mMANAGE_MAIN.START_CELL_ID = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE).CELL_ID;
                        mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;

                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();

                        mMANAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                        mMANAGE_LIST.GOODS_PROPERTY1 = "0";//箱型
                        mMANAGE_LIST.GOODS_PROPERTY2 = "green";//颜色
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = 1;

                        Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, new List<Model.MANAGE_LIST> { mMANAGE_LIST }, true, true, false, true, true }, out sResult);
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("申请条码不存在库存和入库上架任务_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_ApplyTaskIn", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult? Enum.LOG_LEVEL.Information: Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCell.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
