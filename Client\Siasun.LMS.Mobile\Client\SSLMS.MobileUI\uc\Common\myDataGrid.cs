﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Xml;

namespace SSLMS.MobileUI
{
    public class MyDataGrid : DataGrid
    {
        private DataGridTableStyle _dgts;

        public DataGridTableStyle dgts
        {
            get { return this._dgts; }
            set { this._dgts = value; }
        }

        public MyDataGrid()
        {
            //this.Font = new Font("宋体", 8, FontStyle.Regular);

            this._dgts = new DataGridTableStyle();

            this.TableStyles.Add(this._dgts);
        }

        public void AddCheck()
        {
            if (!this._dgts.GridColumnStyles.Contains("check"))
            {
                XmlDocument xmlDoc = new XmlDocument();

                string str = string.Empty;

                str += "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>";

                str += "<Table Name=\"1\">";

                str += "<Field Column=\"check\" dbType=\"String\" Header=\"选择\" fieldType=\"bool\"  bedit=\"1\" index=\"0\" />"; ;

                str += "</Table>";

                xmlDoc.LoadXml(str);

                XmlNode xn = xmlDoc.SelectSingleNode(@"/Table[@Name='" + "1" + "']");

                DataTable dt = this.GetDataTable(xn);

                this.TransCol(dt);
            }
        }

        public void AddOrder(string sXml)
        {
            XmlDocument xmlDoc = new XmlDocument();

            string str = string.Empty;

            str += "<?xml version=\"1.0\" encoding=\"UTF-8\" ?>";

            str += "<Table Name=\"1\">";

            str += sXml;

            str += "</Table>";

            xmlDoc.LoadXml(str);

            XmlNode xn = xmlDoc.SelectSingleNode("descendant::Table[@Name='" + "1" + "']");

            DataTable dt = this.GetDataTable(xn);

            this.TransCol(dt);
        }

        private void TransCol(DataTable dt)
        {
            if (null == dt)
            {
                return;
            }

            foreach (DataRow dr in dt.Rows)
            {
                if (string.IsNullOrEmpty(dr["Header"].ToString()))
                {
                    continue;
                }

                if (this._dgts.GridColumnStyles.Contains(dr["Column"].ToString().TrimEnd()))
                {
                    continue;
                }

                switch (dr["fieldType"].ToString().ToLower())
                {
                    case "text":

                        DataGridCustomTextBoxColumn dgvTextCol = new DataGridCustomTextBoxColumn();

                        dgvTextCol.Owner = this;

                        dgvTextCol.Width = 60;

                        dgvTextCol.MappingName = dr["Column"].ToString().TrimEnd();

                        dgvTextCol.HeaderText = dr["Header"].ToString().TrimEnd();

                        dgvTextCol.NullText = string.Empty;

                        dgvTextCol.ReadOnly = true;

                        dgvTextCol.AlternatingBackColor = SystemColors.ControlLight;

                        dgvTextCol.Alignment = HorizontalAlignment.Left;

                        this._dgts.GridColumnStyles.Add(dgvTextCol);

                        break;

                    case "bool":

                        DataGridCustomCheckBoxColumn dgvBoolCol = new DataGridCustomCheckBoxColumn();

                        dgvBoolCol.Owner = this;

                       dgvBoolCol.Width = 40;

                        //dgvBoolCol.Width = dr["Header"].ToString().Length * 16;

                        dgvBoolCol.MappingName = dr["Column"].ToString().TrimEnd();

                        dgvBoolCol.HeaderText = dr["Header"].ToString().TrimEnd();

                        dgvBoolCol.NullText = string.Empty;

                        dgvBoolCol.AlternatingBackColor = SystemColors.ControlLight;

                        dgvBoolCol.Alignment = HorizontalAlignment.Center;

                        this._dgts.GridColumnStyles.Add(dgvBoolCol);

                        break;

                    case "combox":

                        DataGridCustomComboBoxColumn dgvCmbCol = new DataGridCustomComboBoxColumn();

                        dgvCmbCol.Owner = this;

                        //dgvCmbCol.Width = dr["Header"].ToString().Length * 16;

                        dgvCmbCol.MappingName = dr["Column"].ToString().TrimEnd();

                        dgvCmbCol.HeaderText = dr["Header"].ToString().TrimEnd();

                        dgvCmbCol.NullText = string.Empty;

                        dgvCmbCol.AlternatingBackColor = SystemColors.ControlLight;

                        this._dgts.GridColumnStyles.Add(dgvCmbCol);

                        ComboBox cb = (ComboBox)dgvCmbCol.HostedControl;

                        cb.DataSource =  Program._I_PDAService.GetList(string.Format(" selelct * from sys_item_list where  ITEM_ID IN(SELECT ITEM_ID FROM SYS_ITEM WHERE ITEM_CODE= '{0}') ORDER BY ITEM_LIST_ORDER",string.Format(dr["key"].ToString(), SSLMS.MobileUI.Common.GetSplitStr(this.Name, new char[] { '_' }, 1))));
                        
                        cb.DisplayMember = "name";

                        cb.ValueMember = "vlaue";

                        break;
                }
            }
        }

        public void AddHeader(string tableName)
        {
            XmlDocument xd = new XmlDocument();

            xd.Load(System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().GetName().CodeBase) + @"\App.config");

            XmlElement xe = xd.DocumentElement;

            XmlNode xn = xe.SelectSingleNode("descendant::Table[@Name='" + tableName + "']");

            DataTable dt = this.GetDataTable(xn);

            this.TransCol(dt);
        }

        public DataRow GetCurDataRow()
        {

            try
            {
                CurrencyManager cm = (CurrencyManager)this.BindingContext[this.DataSource];

                DataRowView rowView = (DataRowView)cm.Current;

                return rowView.Row;

            }
            catch
            {
                return null;
            }
        }

        private DataTable GetDataTable(XmlNode xn)
        {
            string sXml = string.Empty;

            sXml += xn.OuterXml;

            XmlParserContext context = new XmlParserContext(null, null, null, XmlSpace.None);

            XmlTextReader reader = new XmlTextReader(sXml, XmlNodeType.Document, context);

            DataSet ds = new DataSet();

            ds.ReadXml(reader);

            return (ds.Tables.Count > 1) ? ds.Tables[1] : null;
        }

        public bool SelectDataGridRow(string columnName, string columnValue)
        {
            bool bFinded = false;

            CurrencyManager cm = (CurrencyManager)this.BindingContext[this.DataSource];

            for (int i = 0; i < cm.Count; i++)
            {
                cm.Position = i;

                if ((cm.List[cm.Position] as DataRowView)[columnName].ToString() == columnValue)
                {
                    this.Select(cm.Position);

                    bFinded = true;

                    break;
                }

                if (cm.Position < cm.Count - 1)
                {
                    cm.Position++;
                }
            }

            return bFinded;
        }

        public string GetCheckColumns(string CheckColumn, string ResultColumn, char[] Split)
        {
            StringBuilder sb = new StringBuilder();

            foreach (DataRow dr in (this.DataSource as DataTable).Rows)
            {
                if (DBNull.Value.Equals(dr[CheckColumn]))
                {
                    continue;
                }

                if (Convert.ToBoolean(dr[CheckColumn]))
                {
                    sb.Append(dr[ResultColumn].ToString());

                    sb.Append(Split);
                }
            }

            return sb.ToString().Trim(Split);
        }


        public void SetEditColumns(string sColName, Color c)
        {
            if(this.dgts.GridColumnStyles.Contains(sColName))
            {
                (this.dgts.GridColumnStyles[sColName] as DataGridCustomColumnBase).ReadOnly = false;

               // (this.dgts.GridColumnStyles[sColName] as DataGridCustomColumnBase).
            }
        }
    }
}
