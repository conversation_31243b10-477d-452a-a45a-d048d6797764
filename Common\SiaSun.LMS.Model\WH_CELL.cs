﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// WH_CELL 
	/// </summary>
	[Serializable]
    [DataContract]
	public class WH_CELL
	{
		public WH_CELL()
		{
			
		}
        private int _warehouse_id;
		private int _cell_id;
		private int _area_id;
		private int _logic_id;
		private string _cell_name;
		private string _cell_code;
		private string _cell_type;
		private string _device_code;
		private int _cell_z;
		private int _cell_x;
		private int _cell_y;
		private string _cell_inout;
		private string _cell_model;
		private string _cell_status;
		private string _run_status;
		private string _cell_fork_type;
		private string _cell_logical_name;
		private string _lane_way;
		private string _cell_group;
		private string _cell_flag;
		private string _shelf_type;
		private string _shelf_neighbour;
		private string _cell_storage_type;
        private int _cell_width;
        private int _cell_height;
        private string _lock_device_code;
        private string _cell_property;

        private double _cell_score;
        private string _score_update_time;


        [DataMember]
        public int WAREHOUSE_ID
        {
            get { return _warehouse_id; }
            set { _warehouse_id = value; }
        }

		///<sumary>
		/// 货位索引
		///</sumary>
		[DataMember]
        public int CELL_ID
		{
			get{return _cell_id;}
			set{_cell_id = value;}
		}
		///<sumary>
		/// 库区索引
		///</sumary>
        [DataMember]
        public int AREA_ID
		{
			get{return _area_id;}
			set{_area_id = value;}
		}
		///<sumary>
		/// 存储区索引
		///</sumary>
        [DataMember]
        public int LOGIC_ID
		{
			get{return _logic_id;}
			set{_logic_id = value;}
		}
		///<sumary>
		/// 货位名称
		///</sumary>
        [DataMember]
        public string CELL_NAME
		{
			get{return _cell_name;}
			set{_cell_name = value;}
		}
		///<sumary>
		/// 货位编码
		///</sumary>
        [DataMember]
        public string CELL_CODE
		{
			get{return _cell_code;}
			set{_cell_code = value;}
		}
		///<sumary>
		/// 货位类型
		///</sumary>
        [DataMember]
        public string CELL_TYPE
		{
			get{return _cell_type;}
			set{_cell_type = value;}
		}
		///<sumary>
		/// 设备编号
		///</sumary>
        [DataMember]
        public string DEVICE_CODE
		{
			get{return _device_code;}
			set{_device_code = value;}
		}
		///<sumary>
		/// 排
		///</sumary>
        [DataMember]
        public int CELL_Z
		{
			get{return _cell_z;}
			set{_cell_z = value;}
		}
		///<sumary>
		/// 列
		///</sumary>
        [DataMember]
        public int CELL_X
		{
			get{return _cell_x;}
			set{_cell_x = value;}
		}
		///<sumary>
		/// 层
		///</sumary>
        [DataMember]
        public int CELL_Y
		{
			get{return _cell_y;}
			set{_cell_y = value;}
		}
		///<sumary>
		/// 出入库类型
		///</sumary>
        [DataMember]
        public string CELL_INOUT
		{
			get{return _cell_inout;}
			set{_cell_inout = value;}
		}
		///<sumary>
		/// 货位尺寸
		///</sumary>
        [DataMember]
        public string CELL_MODEL
		{
			get{return _cell_model;}
			set{_cell_model = value;}
		}
		///<sumary>
		/// 货位状态
		///</sumary>
        [DataMember]
        public string CELL_STATUS
		{
			get{return _cell_status;}
			set{_cell_status = value;}
		}
		///<sumary>
		/// 运行状态
		///</sumary>
        [DataMember]
        public string RUN_STATUS
		{
			get{return _run_status;}
			set{_run_status = value;}
		}
		///<sumary>
		/// 货叉类型
		///</sumary>
        [DataMember]
        public string CELL_FORK_TYPE
		{
			get{return _cell_fork_type;}
			set{_cell_fork_type = value;}
		}
		///<sumary>
		/// 逻辑排
		///</sumary>
        [DataMember]
        public string CELL_LOGICAL_NAME
		{
			get{return _cell_logical_name;}
			set{_cell_logical_name = value;}
		}
		///<sumary>
		/// 巷道
		///</sumary>
        [DataMember]
        public string LANE_WAY
		{
			get{return _lane_way;}
			set{_lane_way = value;}
		}
		///<sumary>
		/// 货位组
		///</sumary>
        [DataMember]
        public string CELL_GROUP
		{
			get{return _cell_group;}
			set{_cell_group = value;}
		}
		///<sumary>
		/// 启用标识
		///</sumary>
        [DataMember]
        public string CELL_FLAG
		{
			get{return _cell_flag;}
			set{_cell_flag = value;}
		}
		///<sumary>
		/// 货架类型
		///</sumary>
        [DataMember]
        public string SHELF_TYPE
		{
			get{return _shelf_type;}
			set{_shelf_type = value;}
		}
		///<sumary>
		/// 双深相临排
		///</sumary>
        [DataMember]
        public string SHELF_NEIGHBOUR
		{
			get{return _shelf_neighbour;}
			set{_shelf_neighbour = value;}
		}
		///<sumary>
		/// 库存类型 单托盘 多托盘
		///</sumary>
        [DataMember]
        public string CELL_STORAGE_TYPE
		{
			get{return _cell_storage_type;}
			set{_cell_storage_type = value;}
		}

        ///<sumary>
        /// 货位宽度
        ///</sumary>
        [DataMember]
        public int CELL_WIDTH
        {
            get { return _cell_width; }
            set { _cell_width = value; }
        }

        ///<sumary>
        /// 货位高度
        ///</sumary>
        [DataMember]
        public int CELL_HEIGHT
        {
            get { return _cell_height; }
            set { _cell_height = value; }
        }
        ///<sumary>
        /// 锁定设备号
        ///</sumary>
        public string LOCK_DEVICE_CODE
        {
            get { return _lock_device_code; }
            set { _lock_device_code = value; }
        }
        ///<sumary>
        /// 货位属性
        ///</sumary>
        public string CELL_PROPERTY
        {
            get { return _cell_property; }
            set { _cell_property = value; }
        }


        ///<sumary>
        /// 给出库效率打分
        ///</sumary>
        [DataMember]
        public double CELL_SCORE
        {
            get { return _cell_score; }
            set { _cell_score = value; }
        }

        ///<sumary>
        /// 分数更新时间
        ///</sumary>
        [DataMember]
        public string SCORE_UPDATE_TIME
        {
            get { return _score_update_time; }
            set { _score_update_time = value; }
        }
    }
}
