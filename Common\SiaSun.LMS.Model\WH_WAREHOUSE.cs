﻿/***************************************************************************
 * 
 *       功能：     仓库实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// WH_WAREHOUSE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class WH_WAREHOUSE
	{
		public WH_WAREHOUSE()
		{
			
		}
		
		private int _warehouse_id;
		private string _warehouse_code;
		private string _warehouse_name;
		private string _warehouse_remark;
		private string _warehouse_flag;
		private string _warehouse_type;
		private int _warehouse_order;
		
		///<sumary>
		/// 仓库编号
        ///</sumary>
        [DataMember]
		public int WAREHOUSE_ID
		{
			get{return _warehouse_id;}
			set{_warehouse_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string WAREHOUSE_CODE
		{
			get{return _warehouse_code;}
			set{_warehouse_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string WAREHOUSE_NAME
		{
			get{return _warehouse_name;}
			set{_warehouse_name = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string WAREHOUSE_REMARK
		{
			get{return _warehouse_remark;}
			set{_warehouse_remark = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string WAREHOUSE_FLAG
		{
			get{return _warehouse_flag;}
			set{_warehouse_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string WAREHOUSE_TYPE
		{
			get{return _warehouse_type;}
            set { _warehouse_type = value; }
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int WAREHOUSE_ORDER
		{
			get{return _warehouse_order;}
			set{_warehouse_order = value;}
		}
	}
}
