﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="RECORD_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="RECORD_LIST" type="SiaSun.LMS.Model.RECORD_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="RECORD_LIST">
			<result property="RECORD_LIST_ID" column="record_list_id" />
			<result property="RECORD_ID" column="record_id" />
			<result property="PLAN_LIST_ID" column="plan_list_id" />
			<result property="RECORD_LIST_QUANTITY" column="record_list_quantity" />
			<result property="GOODS_ID" column="goods_id" />
			<result property="RECORD_LIST_REMARK" column="record_list_remark" />
			<result property="GOODS_PROPERTY1" column="goods_property1" />
			<result property="GOODS_PROPERTY2" column="goods_property2" />
			<result property="GOODS_PROPERTY3" column="goods_property3" />
			<result property="GOODS_PROPERTY4" column="goods_property4" />
			<result property="GOODS_PROPERTY5" column="goods_property5" />
			<result property="GOODS_PROPERTY6" column="goods_property6" />
			<result property="GOODS_PROPERTY7" column="goods_property7" />
			<result property="GOODS_PROPERTY8" column="goods_property8" />
			<result property="BOX_BARCODE" column="box_barcode" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="RECORD_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  record_list_id,
				  record_id,
				  plan_list_id,
				  record_list_quantity,
				  goods_id,
				  record_list_remark,
				  goods_property1,
				  goods_property2,
				  goods_property3,
				  goods_property4,
				  goods_property5,
				  goods_property6,
				  goods_property7,
				  goods_property8,
				  box_barcode
			From RECORD_LIST
		</select>
		
		<select id="RECORD_LIST_SELECT_BY_ID" parameterClass="int" extends = "RECORD_LIST_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					record_list_id=#RECORD_LIST_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="RECORD_LIST_INSERT" parameterClass="RECORD_LIST">
      Insert Into RECORD_LIST (
      record_list_id,
      record_id,
      plan_list_id,
      record_list_quantity,
      goods_id,
      record_list_remark,
      goods_property1,
      goods_property2,
      goods_property3,
      goods_property4,
      goods_property5,
      goods_property6,
      goods_property7,
      goods_property8,
      box_barcode
      )Values(
      #RECORD_LIST_ID#,
      #RECORD_ID#,
      #PLAN_LIST_ID#,
      #RECORD_LIST_QUANTITY#,
      #GOODS_ID#,
      #RECORD_LIST_REMARK#,
      #GOODS_PROPERTY1#,
      #GOODS_PROPERTY2#,
      #GOODS_PROPERTY3#,
      #GOODS_PROPERTY4#,
      #GOODS_PROPERTY5#,
      #GOODS_PROPERTY6#,
      #GOODS_PROPERTY7#,
      #GOODS_PROPERTY8#,
      #BOX_BARCODE#
      )
      <!--<selectKey  resultClass="int" type="post" property="RECORD_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="RECORD_LIST_UPDATE" parameterClass="RECORD_LIST">
      Update RECORD_LIST Set
      record_list_id=#RECORD_LIST_ID#,
      record_id=#RECORD_ID#,
      plan_list_id=#PLAN_LIST_ID#,
      record_list_quantity=#RECORD_LIST_QUANTITY#,
      goods_id=#GOODS_ID#,
      record_list_remark=#RECORD_LIST_REMARK#,
      goods_property1=#GOODS_PROPERTY1#,
      goods_property2=#GOODS_PROPERTY2#,
      goods_property3=#GOODS_PROPERTY3#,
      goods_property4=#GOODS_PROPERTY4#,
      goods_property5=#GOODS_PROPERTY5#,
      goods_property6=#GOODS_PROPERTY6#,
      goods_property7=#GOODS_PROPERTY7#,
      goods_property8=#GOODS_PROPERTY8#,
      box_barcode=#BOX_BARCODE#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					record_list_id=#RECORD_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="RECORD_LIST_DELETE" parameterClass="int">
			Delete From RECORD_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					record_list_id=#RECORD_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>