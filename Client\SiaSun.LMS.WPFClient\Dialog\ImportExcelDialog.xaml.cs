﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// 导入类型
    /// </summary>
    public enum ImportType
    { 
        Excel,
        Xml
    }

    /// <summary>
    /// ImportWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ImportExcelDialog : Window
    {
        ImportType emImportType = ImportType.Excel;
        bool _IsCommit = true;          //是否提交保存
        string _tableConverterCode = null;
        DataSet dsImport = null;

        public DataTable dt = null;

        /// <summary>
        /// 导入文件类型
        /// </summary>
        public ImportType U_ImportType
        {
            set { emImportType = value; }
        }

        /// <summary>
        /// 导入的数据集集合
        /// </summary>
        public DataSet U_ImportDataSet
        {
            get { return dsImport; }
            set { dsImport = value; }
        }

        #region     ------构造函数

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImportExcelDialog(bool IsCommit)
        {
            InitializeComponent();
            this._IsCommit = IsCommit;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ImportExcelDialog(bool IsCommit,string TableConverterCode):this(IsCommit)
        {
            this._tableConverterCode = TableConverterCode;
            this.cmbTemplateName.IsEnabled = false;
        }

        #endregion

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            //加载所有模板
            this.LoadTemplate();
        }

        /// <summary>
        /// 加载所有模板
        /// </summary>
        private void LoadTemplate()
        {
            try
            {
                this.cmbTemplateName.DisplayMemberPath = "TABLE_CONVERTER_NAME";
                this.cmbTemplateName.SelectedValuePath = "TABLE_CONVERTER_CODE";
                this.cmbTemplateName.ItemsSource = MainApp._I_SystemService.TABLE_CONVERTER_GetList();

                //设置默认值
                if (!string.IsNullOrEmpty(this._tableConverterCode))
                {
                    this.cmbTemplateName.SelectedValue = this._tableConverterCode;
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnOpen":
                        this.OpenFile();
                        break;
                    case "btnImport":
                        this.ImportFile();
                        break;
                    case "btnClose":
                        this.Close();
                        break;
                }
            }
        }

        /// <summary>
        /// 选择导入文件
        /// </summary>
        private void OpenFile()
        {
            System.Windows.Forms.OpenFileDialog opDig = new System.Windows.Forms.OpenFileDialog();

            //判断导入文件类型
            switch (emImportType)
            {
                case ImportType.Excel:
                    break;
                case ImportType.Xml:
                    this.ImportXml();
                    break;
            }

            if (opDig.ShowDialog() == System.Windows.Forms.DialogResult.OK)
            {
                this.txtFileName.Text = opDig.FileName;
            }
        }

        /// <summary>
        /// 导入文件
        /// </summary>
        private void ImportFile()
        {
            if (string.IsNullOrEmpty(this.txtFileName.Text) )
            {
                MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Null, string.Format("[{0}/{1}]", this.lblTemplateName.Text, this.lblFileName.Text));
                return;
            }

            //判断导入文件类型
            switch (emImportType)
            {
                case ImportType.Excel:
                    this.ImportGoodsExcel();
                    break;
                case ImportType.Xml:
                    this.ImportXml();
                    break;
            }
        }
        
        /// <summary>
        /// 导入EXCEL文件
        /// </summary>
        private void ImportExcel()
        {
            try
            {
                //获得Excel数据源
                using (DataTable tableExcel = new SiaSun.LMS.Common.Excel().ImportToDataTable(this.txtFileName.Text, true))
                {
                    if (tableExcel.Rows.Count > 0)
                    {
                        tableExcel.TableName = "ExcelImport";

                        //获得转换信息实例
                        Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER = this.cmbTemplateName.SelectedItem as Model.SYS_TABLE_CONVERTER;
                        if (mTABLE_CONVERTER != null)
                        {
                            //向父级表和子级表中导入数据
                            string strResult = string.Empty;
                            this.dsImport = MainApp._I_SystemService.SYS_TABLE_CONVERTER_Import(this.cmbTemplateName.SelectedValue.ToString(), tableExcel, out strResult);

                            //是否提交导入数据
                            if (string.IsNullOrEmpty(strResult))
                            {
                                if (this._IsCommit)
                                {
                                    int intAffect = MainApp._I_SystemService.TABLE_CONVERTER_Save(mTABLE_CONVERTER, dsImport, out strResult);
                                    if (intAffect == 0)
                                    {
                                        MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.AffectCount);
                                    }
                                    else
                                    {
                                        MainApp._MessageDialog.Show(true);
                                    }
                                    return;
                                }
                            }
                            else
                            {
                                MainApp._MessageDialog.ShowResult(false, strResult);
                            }
                            this.DialogResult = true;
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void ImportGoodsExcel()
        {
            try
            {
                //获得Excel数据源
                dt = new SiaSun.LMS.Common.Excel().ImportToDataTable(this.txtFileName.Text, true);

                if (dt.Rows.Count > 0)
                    {
                        dt.TableName = "ExcelImport";
                        
                        this.DialogResult = true;
                    }
               
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 导入XML文件
        /// </summary>
        private void ImportXml()
        {
            if (this.dsImport != null)
            {
                this.dsImport.ReadXml(this.txtFileName.Text);

                //判断是否提交保存
                if (this._IsCommit)
                {
                    int intAffect = MainApp._I_BaseService.Save(dsImport.Tables[0], dsImport.Tables[0].TableName);
                    if (intAffect == 0)
                    {
                        MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.AffectCount);
                    }
                    else
                    {
                        MainApp._MessageDialog.Show(true);
                    }
                    return;
                }

                this.DialogResult = true;
                return;
            }
        }
    }
}
