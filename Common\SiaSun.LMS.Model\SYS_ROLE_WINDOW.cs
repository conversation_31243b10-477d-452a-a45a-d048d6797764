﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_ROLE_WINDOW 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_ROLE_WINDOW
	{
		public SYS_ROLE_WINDOW()
		{
			
		}
		
		private int _role_window_id;
		private int _menu_id;
		private string _control_name;
		private string _control_header;
		private int _flag;
		private int _role_id;
		
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int ROLE_WINDOW_ID
		{
			get{return _role_window_id;}
			set{_role_window_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int MENU_ID
		{
			get{return _menu_id;}
			set{_menu_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CONTROL_NAME
		{
			get{return _control_name;}
			set{_control_name = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CONTROL_HEADER
		{
			get{return _control_header;}
			set{_control_header = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int FLAG
		{
			get{return _flag;}
			set{_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int ROLE_ID
		{
			get{return _role_id;}
			set{_role_id = value;}
		}
	}
}
