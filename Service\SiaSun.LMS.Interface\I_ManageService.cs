﻿using System;
using System.Collections.Generic;
using System.Text;
using System.ServiceModel;
using System.Data;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.SYS_USER))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_LIST))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_MAIN))]
    [ServiceKnownType(typeof(List<MANAGE_LIST>))]
    [ServiceKnownType(typeof(List<PLAN_LIST>))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_TYPE_PARAM))]
    public partial interface I_ManageService
    {
        [OperationContract]
        void ControlTranslate(string WAREHOUSE);

        [OperationContract]
        bool RecordCreate(int MANAGE_ID, out string sResult);


        [OperationContract]
        IList<SiaSun.LMS.Model.MANAGE_TYPE_PARAM> ManageTypeParamGetList(int MANAGE_TYPE_ID);

        /// <summary>
        /// 增加申请
        /// xcjt add 2017-01-13
        /// </summary>
        [OperationContract]
        bool ControlApplyAdd(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, bool bTrans, out string sResult);


        /// <summary>
        /// 使用web服务方式处理调度申请
        /// wdz add 2018-03-01
        /// </summary>
        [OperationContract]
        string WsControlApply(string controlApplyType, string deviceCode, string stockBarcode, string controlApplyPara);


        /// <summary>
        /// 下达货架指定排起始列到终止列内物料的下架任务
        /// 修货架期间专用批量下架方法
        /// </summary>
        [OperationContract]
        string ContinusManageDown(Model.SYS_USER user, int row, int startColumn, int endColumn, string endPosition, string storageType);


        /// <summary>
        /// 开始人工拣选 2022-08-24 15:36:45.231
        /// </summary>
        [OperationContract]
        string ManualPickStart(Model.SYS_USER user, string stockBarcode, string uniqueCode, string pickStation);


        /// <summary>
        /// 生成人工拣选任务 2022-08-24 15:37:10.386
        /// </summary>
        [OperationContract]
        string ManualCreatePickTask(Model.SYS_USER user, string stockBarcode, string pickStation, string planUniqueCode, out string boxMap);

        /// <summary>
        /// 人工拣选任务完成 2022-10-08 12:39:04.158
        /// </summary>
        /// <param name="user"></param>
        /// <param name="sourceBox"></param>
        /// <param name="targetBox"></param>
        /// <param name="gridNo"></param>
        /// <returns></returns>
        [OperationContract]
        string ManualPickGoodsFinish(Model.SYS_USER user, string sourceBox, string targetBox, string gridNo, decimal updatedQty, string pickStation);


        /// <summary>
        /// 本地盘点下达下架任务 2023-09-23
        /// </summary>
        /// <param name="user"></param>
        /// <param name="planCode"></param>
        /// <param name="goodsCodeList"></param>
        /// <param name="endCellCode"></param>
        /// <returns></returns>
        [OperationContract]
        string CreateCheckDownTask(Model.SYS_USER user, string planCode, List<string> goodsCodeList, string endCellCode);
    }
}
