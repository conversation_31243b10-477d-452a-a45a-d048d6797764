﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// DateBetweenQueryWindow.xaml 的交互逻辑
    /// </summary>
    public partial class DateSectQueryWindow : AvalonDock.DocumentContent
    {
        string strTableName = string.Empty;
        string strWhere = string.Empty;
        string strOrder = string.Empty;
        string strStartDateTimeColumn = string.Empty;
        string strEndDateTimeColumn = string.Empty;

        public DateSectQueryWindow(string _StartDateTimeColumn, string _EndDateTimeColumn,string _TableName, string _Where, string _Order)
        {
            InitializeComponent();

            strStartDateTimeColumn = _StartDateTimeColumn;
            strEndDateTimeColumn = _EndDateTimeColumn;
            strTableName = _TableName;
            strWhere = _Where;
            strOrder = _Order;

            //是否显示时间段
            this.panelDateTime.Visibility = (string.IsNullOrEmpty(this.strStartDateTimeColumn) && string.IsNullOrEmpty(this.strEndDateTimeColumn)) ? Visibility.Collapsed : System.Windows.Visibility.Visible;

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQuery_U_Query);
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //标题
            this.grpbHeader.Header = string.Format(this.grpbHeader.Header.ToString(),this.Title.ToString());

            //默认日期
            this.dtpStart.SelectedDate = DateTime.Now;
            this.dtpEnd.SelectedDate = DateTime.Now;

            //初始化查询控件
            this.InitQueryControl();
            //开始查询
            this.ucQuery_U_Query(null);
        }

   

        #region     ------查询

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            MainWindow.mainWin.Cursor = Cursors.Wait;
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = strTableName;
            this.ucQuery.U_InitControl();
            MainWindow.mainWin.Cursor = Cursors.Arrow;
        }

        /// <summary>
        /// 开始查询
        /// </summary>
        void ucQuery_U_Query(string QueryWhere)
        {
            string strDateWhere = (this.panelDateTime.Visibility == System.Windows.Visibility.Visible ? this.GetDateStr() : string.Empty);
            this.gridQuery.U_AppendWhere = string.Format("{0} AND {1}",
                                                                            string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere,
                                                                            string.IsNullOrEmpty(strDateWhere) ? "1=1" : strDateWhere);

            this.gridQuery.U_TableName = strTableName;
            this.gridQuery.U_XmlTableName = strTableName;
            this.gridQuery.U_WindowName = this.GetType().Name;
            this.gridQuery.U_Where = strWhere;
            this.gridQuery.U_OrderField = strOrder;

            this.gridQuery.U_AllowOperatData = false;
            this.gridQuery.U_AllowChecked = false;
            this.gridQuery.U_InitControl();
        }

        /// <summary>
        /// 获得时间查询条件
        /// </summary>
        private string GetDateStr()
        {
            string strDateWhere = string.Empty;

            //判断是否添加时间查询
            if (chkboxDate.IsChecked == true)
            {
                //判断选择的值
                if(this.dtpStart.SelectedDate.HasValue && !this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00')",
                                                                               strStartDateTimeColumn,
                                                                               this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if(!this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}<='{1} 24:60:60')",                                                                              
                                                                              strEndDateTimeColumn,
                                                                              this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if(this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00' AND {2}<='{3} 24:60:60')",
                                                                            strStartDateTimeColumn,
                                                                            this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"),
                                                                            strEndDateTimeColumn,
                                                                            this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));
                                                                               
                }
            }
            return strDateWhere;
        }

        #endregion
    }
}
