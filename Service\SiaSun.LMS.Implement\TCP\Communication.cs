﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading;
using System.ComponentModel;

namespace SiaSun.LMS.Implement.TCP
{
    public class Communication : IDisposable
    {
        private const int SYNC_INTERVAL = 2000;//同步间隔（毫秒）
        private const int LOOP_SLEEP = 10;//循环间隔（毫秒）
        private const int OUT_TIME = 2000;//超时时间（毫秒）
        private const int RETRY_TIMES = 3;//重试次数

        private bool alive;
        private TcpSocket TcpSocket;
        private Protocol protocol;
        private BackgroundWorker dispatcher, command;
        private List<byte> buffer = new List<byte>();
        private List<int> cache = null;

        private object[] boxLeaveResponse;
        private object[] lightsStatusResponse;

        /// <summary>
        /// done
        /// 接收到PLC传给WMS的箱子到位信号后 WMS进行业务处理的事件
        /// </summary>
        public event PlcEventHandler BoxArrived;

        /// <summary>
        /// done
        /// </summary>
        /// <param name="ip"></param>
        /// <param name="port"></param>
        public Communication(string ip, int port)
        {
            TcpSocket = new TcpSocket(ip, port);
            TcpSocket.OpenSuccess += () =>
            {
                TcpSocket.Receive();
            };
            TcpSocket.OpenFail += (ex) =>
            {
                Thread.Sleep(OUT_TIME);
                TcpSocket.Open();
            };
            TcpSocket.Disconnect += (ex) =>
            {
                //TcpSocket.Open();
                Thread.Sleep(60000); //一分钟重连一次
                Launch();//确保断线可以被重连
            };
            TcpSocket.ReceiveSuccess += (bytes) => { buffer.AddRange(bytes); };

            protocol = new Protocol();
            protocol.BoxArriveRequest += protocol_BoxArriveRequest;
            protocol.BoxLeaveResponse += (e) => { boxLeaveResponse = e.Data; };
            protocol.LightsStatusResponse += (e) => { lightsStatusResponse = e.Data; };

            dispatcher = new BackgroundWorker();
            dispatcher.WorkerSupportsCancellation = true;
            dispatcher.DoWork += new DoWorkEventHandler(dispatcher_DoWork);
            dispatcher.RunWorkerCompleted += new RunWorkerCompletedEventHandler(dispatcher_RunWorkerCompleted);
        }

        public void Launch()
        {
            alive = true;
            TcpSocket.Open();
            dispatcher.RunWorkerAsync();
        }

        public void Dispose()
        {
            alive = false;
            TcpSocket.Close();
        }

        /// <summary>
        /// 解析协议
        /// done
        /// </summary>
        private void dispatcher_DoWork(object sender, DoWorkEventArgs e)
        {
            while (alive)
            {
                //至少2帧才能校验协议头
                if (buffer.Count > 1)
                {
                    int length = protocol.Check(buffer[0], buffer[1]);
                    if (length == -1)
                    {
                        //协议头校验失败，删除首帧，继续校验
                        buffer.RemoveAt(0);
                    }
                    else
                    {
                        //协议头校验成功后，按长度取帧
                        if (buffer.Count >= length)
                        {
                            byte[] array = new byte[length];
                            buffer.CopyTo(0, array, 0, length);
                            //校验整个协议，并处理
                            if (protocol.Deal(array))
                            {
                                //校验通过，处理后清除该部分帧
                                buffer.RemoveRange(0, length);
                            }
                            else
                            {
                                //校验未通过，删除首帧
                                buffer.RemoveAt(0);
                            }
                        }
                    }
                }
                Thread.Sleep(LOOP_SLEEP);
            }
        }
        private void dispatcher_RunWorkerCompleted(object sender, RunWorkerCompletedEventArgs e)
        {
            dispatcher.CancelAsync();
            dispatcher.Dispose();
        }

        /// <summary>
        /// 收到料箱到位通知
        /// done
        /// 在接受了PLC传给WMS的位置到位信号后，回传给PLC更改的信息
        /// </summary>
        private void protocol_BoxArriveRequest(ProtocolEventArgs e)
        {
            if ((int)e.Data[0] == 1)
            {
                short plcNo = (short)e.Data[1];
                short taskNo = (short)e.Data[2];
                short status = (short)e.Data[3];
                int deviceNo = (int)e.Data[4];
                string stockBarcode = (string)e.Data[5];
                TcpSocket.Send(protocol.BoxArriveResponse(plcNo, taskNo, status, deviceNo, stockBarcode));
                PlcEventArgs args = new PlcEventArgs(plcNo, taskNo, status, deviceNo, stockBarcode);
                if (BoxArrived != null)
                {
                    foreach (PlcEventHandler item in BoxArrived.GetInvocationList())
                    {
                        item.BeginInvoke(args, null, null);
                    }
                }
            }
        }

        /// <summary>
        /// 下达料箱离开命令
        /// done
        /// </summary>
        public bool BoxLeave(short plcNo, short taskNo, short command, int deviceNo, string stockBarcode)
        {
            return boxLeave(plcNo, taskNo, command, deviceNo, stockBarcode, 1);
        }

        /// <summary>
        /// done
        /// </summary>
        /// <param name="plcNo"></param>
        /// <param name="taskNo"></param>
        /// <param name="command"></param>
        /// <param name="deviceNo"></param>
        /// <param name="stockBarcode"></param>
        /// <param name="tryTime"></param>
        /// <returns></returns>
        private bool boxLeave(short plcNo, short taskNo, short command, int deviceNo, string stockBarcode, int tryTime)
        {
            boxLeaveResponse = null;
            TcpSocket.Send(protocol.BoxLeaveRequest(plcNo, taskNo, command, deviceNo, stockBarcode));
            DateTime timestamp = DateTime.Now;
            //while (boxLeaveResponse == null || (int)boxLeaveResponse[0] != 2)
            while (boxLeaveResponse == null)
            {
                if (DateTime.Now.Subtract(timestamp).TotalMilliseconds > OUT_TIME)
                {
                    if (tryTime < RETRY_TIMES)
                    {
                        return boxLeave(plcNo, taskNo, command, deviceNo, stockBarcode, tryTime + 1);
                    }
                    else
                    {
                        return false;
                    }
                }
                Thread.Sleep(LOOP_SLEEP);
            }
            return true;
        }

        /// <summary>
        /// 设置指示灯状态
        /// done
        /// </summary>
        public bool LightsStatus(short plcNo, bool[] status, int deviceNo, string stockBarcode)
        {
            return lightsStatus(plcNo, status, deviceNo, stockBarcode, 1);
        }

        /// <summary>
        /// done
        /// </summary>
        /// <param name="plcNo"></param>
        /// <param name="status"></param>
        /// <param name="deviceNo"></param>
        /// <param name="stockBarcode"></param>
        /// <param name="tryTime"></param>
        /// <returns></returns>
        private bool lightsStatus(short plcNo, bool[] status, int deviceNo, string stockBarcode, int tryTime)
        {
            lightsStatusResponse = null;
            TcpSocket.Send(protocol.LightsStatusRequest(plcNo, status, deviceNo, stockBarcode));
            DateTime timestamp = DateTime.Now;
            //while (lightsStatusResponse == null || (int)boxLeaveResponse[0] != 3)
            while (lightsStatusResponse == null)
            {
                if (DateTime.Now.Subtract(timestamp).TotalMilliseconds > OUT_TIME)
                {
                    if (tryTime < RETRY_TIMES)
                    {
                        return lightsStatus(plcNo, status, deviceNo, stockBarcode, tryTime + 1);
                    }
                    else
                    {
                        return false;
                    }
                }
                Thread.Sleep(LOOP_SLEEP);
            }
            return true;
        }
    }

    public class PlcEventArgs : EventArgs
    {
        public PlcEventArgs(short plcNo, short taskNo, short status, int deviceNo, string stockBarcode)
        {
            PlcNo = plcNo;
            TaskNo = taskNo;
            Status = status;
            DeviceNo = deviceNo;
            StockBarcode = stockBarcode;
        }
        public short PlcNo { get; set; }
        public short TaskNo { get; set; }
        public short Status { get; set; }
        public int DeviceNo { get; set; }
        public string StockBarcode { get; set; }
    }

    public delegate void PlcEventHandler(PlcEventArgs e);
}
