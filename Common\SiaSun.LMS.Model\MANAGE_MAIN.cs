﻿/***************************************************************************
 * 
 *       功能：     管理任务实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// MANAGE_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MANAGE_MAIN
	{
		public MANAGE_MAIN()
		{
			
		}
		
		private int _manage_id;
		private int _goods_template_id;
        private int _plan_id;
        private string _plan_type_code;
		private string _manage_type_code;
		private string _manage_status;
        private string _stock_barcode;
        private string _full_flag;
		private string _cell_model;
		private int _start_cell_id;
		private int _end_cell_id;
		private string _manage_operator;
		private string _manage_begin_time;
		private string _manage_end_time;
		private string _manage_level;
		private string _manage_remark;
		private string _manage_confirm_time;

        private string _manage_source;
        private string _manage_relate_code;
		private string _cross_flag;

		private string _backup_field1;
		private string _backup_field2;
		private string _backup_field3;
		private string _backup_field4;
		private string _backup_field5;

		///<sumary>
		/// 任务编号
		///</sumary>
		[DataMember]
		public int MANAGE_ID
		{
			get{return _manage_id;}
			set{_manage_id = value;}
		}
		///<sumary>
		/// 配盘编号
        ///</sumary>
        [DataMember]
		public int GOODS_TEMPLATE_ID
		{
            get { return _goods_template_id; }
            set { _goods_template_id = value; }
		}
		///<sumary>
		/// 计划编号
        ///</sumary>
        [DataMember]
		public int PLAN_ID
		{
			get{return _plan_id;}
			set{_plan_id = value;}
		}
        ///<sumary>
        /// 计划类型
        ///</sumary>
        [DataMember]
        public string PLAN_TYPE_CODE
        {
            get { return this._plan_type_code; }
            set { this._plan_type_code = value; }
        }
		///<sumary>
		/// 任务类型编号
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_CODE
		{
			get{return _manage_type_code;}
			set{_manage_type_code = value;}
		}
		///<sumary>
		/// 任务状态
        ///</sumary>
        [DataMember]
		public string MANAGE_STATUS
		{
			get{return _manage_status;}
			set{_manage_status = value;}
		}
		///<sumary>
		/// 托盘条码
        ///</sumary>
        [DataMember]
		public string STOCK_BARCODE
		{
			get{return _stock_barcode;}
			set{_stock_barcode = value;}
		}     
        ///<sumary>
        /// 满货位标识
        ///</sumary>
        [DataMember]
        public string FULL_FLAG
        {
            get { return this._full_flag; }
            set { this._full_flag = value; }
        }
		///<sumary>
		/// 物料尺寸
        ///</sumary>
        [DataMember]
		public string CELL_MODEL
		{
			get{return _cell_model;}
			set{_cell_model = value;}
		}
		///<sumary>
		/// 开始位置
        ///</sumary>
        [DataMember]
		public int START_CELL_ID
		{
			get{return _start_cell_id;}
			set{_start_cell_id = value;}
		}
		///<sumary>
		/// 终止位置
        ///</sumary>
        [DataMember]
		public int END_CELL_ID
		{
			get{return _end_cell_id;}
			set{_end_cell_id = value;}
		}
		///<sumary>
		/// 操作者
        ///</sumary>
        [DataMember]
		public string MANAGE_OPERATOR
		{
			get{return _manage_operator;}
			set{_manage_operator = value;}
		}
		///<sumary>
		/// 开始时间
        ///</sumary>
        [DataMember]
		public string MANAGE_BEGIN_TIME
		{
			get{return _manage_begin_time;}
			set{_manage_begin_time = value;}
		}
		///<sumary>
		/// 结束时间
        ///</sumary>
        [DataMember]
		public string MANAGE_END_TIME
		{
			get{return _manage_end_time;}
			set{_manage_end_time = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MANAGE_LEVEL
		{
			get{return _manage_level;}
			set{_manage_level = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string MANAGE_REMARK
		{
			get{return _manage_remark;}
			set{_manage_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MANAGE_CONFIRM_TIME
		{
			get{return _manage_confirm_time;}
			set{_manage_confirm_time = value;}
		}

        ///<sumary>
        /// 任务来源
        ///</sumary>
        [DataMember]
        public string MANAGE_SOURCE
        {
            get { return _manage_source; }
            set { _manage_source = value; }
        }
        ///<sumary>
        /// 关联单号
        ///</sumary>
        [DataMember]
        public string MANAGE_RELATE_CODE
        {
            get { return _manage_relate_code; }
            set { _manage_relate_code = value; }
        }

		///<sumary>
		/// 越库标识 2020-09-22 16:22:58
		///</sumary>
		[DataMember]
		public string CROSS_FLAG
		{
			get { return _cross_flag; }
			set { _cross_flag = value; }
		}

		///<sumary>
		/// 备用字段
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD1
		{
			get { return _backup_field1; }
			set { _backup_field1 = value; }
		}
		///<sumary>
		/// 备用字段
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD2
		{
			get { return _backup_field2; }
			set { _backup_field2 = value; }
		}
		///<sumary>
		/// 备用字段
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD3
		{
			get { return _backup_field3; }
			set { _backup_field3 = value; }
		}
		///<sumary>
		/// 备用字段
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD4
		{
			get { return _backup_field4; }
			set { _backup_field4 = value; }
		}
		///<sumary>
		/// 备用字段
		///</sumary>
		[DataMember]
		public string BACKUP_FIELD5
		{
			get { return _backup_field5; }
			set { _backup_field5 = value; }
		}
	}
}
