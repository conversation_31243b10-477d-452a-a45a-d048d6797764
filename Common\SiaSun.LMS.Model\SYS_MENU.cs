﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_MENU 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_MENU
	{
		public SYS_MENU()
		{
			
		}
		
		private int _menu_id;
		private int _menu_parent_id;
		private string _menu_name;
        private string _menu_class;
		private string _menu_parameter;
        private string _menu_parameter_sl;
		private string _menu_sysflag;
		private string _menu_selectedflag;
		private string _menu_image;
        private string _menu_image_sl;
		private string _menu_childnodeflag;
		private int _menu_order;
		private string _menu_remark;
		private int _menu_group;
		private string _menu_developflag;
		
		///<sumary>
		/// 菜单编号
        ///</sumary>
        [DataMember]
		public int MENU_ID
		{
			get{return _menu_id;}
			set{_menu_id = value;}
		}
		///<sumary>
		/// 菜单父编号
        ///</sumary>
        [DataMember]
		public int MENU_PARENT_ID
		{
			get{return _menu_parent_id;}
			set{_menu_parent_id = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string MENU_NAME
		{
			get{return _menu_name;}
			set{_menu_name = value;}
		}

        ///<sumary>
        /// 对应窗体类
        ///</sumary>
        [DataMember]
        public string MENU_CLASS
        {
            get { return _menu_class; }
            set { _menu_class = value; }
        }

		///<sumary>
		/// 参数
        ///</sumary>
        [DataMember]
		public string MENU_PARAMETER
		{
			get{return _menu_parameter;}
			set{_menu_parameter = value;}
		}

        ///<sumary>
        /// 参数
        ///</sumary>
        [DataMember]
        public string MENU_PARAMETER_SL
        {
            get { return _menu_parameter_sl; }
            set { _menu_parameter_sl = value; }
        }

		///<sumary>
		/// 工具栏
        ///</sumary>
        [DataMember]
		public string MENU_SYSFLAG
		{
			get{return _menu_sysflag;}
            set { _menu_sysflag = value; }
		}
		///<sumary>
		/// 快速启动
        ///</sumary>
        [DataMember]
		public string MENU_SELECTEDFLAG
		{
			get{return _menu_selectedflag;}
			set{_menu_selectedflag = value;}
		}
		///<sumary>
		/// 图片
        ///</sumary>
        [DataMember]
		public string MENU_IMAGE
		{
			get{return _menu_image;}
			set{_menu_image = value;}
		}

        ///<sumary>
        /// 图片
        ///</sumary>
        [DataMember]
        public string MENU_IMAGE_SL
        {
            get { return _menu_image_sl; }
            set { _menu_image_sl = value; }
        }
		///<sumary>
		/// 热键
        ///</sumary>
        [DataMember]
		public string MENU_CHILDNODEFLAG
		{
			get{return _menu_childnodeflag;}
			set{_menu_childnodeflag = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int MENU_ORDER
		{
			get{return _menu_order;}
			set{_menu_order = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string MENU_REMARK
		{
			get{return _menu_remark;}
			set{_menu_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int MENU_GROUP
		{
			get{return _menu_group;}
			set{_menu_group = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MENU_DEVELOPFLAG
		{
			get{return _menu_developflag;}
			set{_menu_developflag = value;}
		}
	}
}
