﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using RYB_PTL_API;

namespace SiaSun.LMS.Implement.DZBQ
{
    /// <summary>
    /// Jacky He
    /// 201711
    /// 电子标签操作类
    /// </summary>
    public class DZBQ
    {
       
        private string _ControlerKey;
        private  string _IP;

        /// <summary>
        /// 电子标签控制器IP地址对应的key
        /// </summary>
        public string ControlerKey
        {
            get
            {
                return _ControlerKey;
            }

            set
            {
                _ControlerKey = value;
            }
        }

        /// <summary>
        /// 电子标签控制器IP
        /// </summary>
        public string IP
        {
            get
            {
                return _IP;
            }

            set
            {
                _IP = value;
            }
        }

        /// <summary>
        /// 电子标签按钮按键后返回事件
        /// </summary>
        public event RYB_PTL_API.RYB_PTL.UserResultAvailableEventHandler CallBackEvent
        {
            add
            {
                if (value != null)
                {
                    RYB_PTL.UserResultAvailable += value;
                }
            }
            remove { RYB_PTL.UserResultAvailable -= value; }
        }



        public DZBQ()
        {
            
        }

        /// <summary>
        /// Jacky He
        /// 电子标签操作类构造函数
        /// </summary>
        /// <param name="controlerKey">电子标签控制器IP地址对应的key</param>
        public DZBQ(string controlerKey):this()
        {
            
            this._ControlerKey = controlerKey;
            this._IP=  Common.StringUtil.GetConfig(this._ControlerKey);
        }

        private void RYB_PTL_UserResultAvailable(RYB_PTL.RtnValueStruct rs)
        {

            //MessageBox.Show(rs.Tagid + "完成拣选");
            string str = string.Format("tagid:{0}\nIP:{1}\nNumber:{2}\nLocator:{3}\nKeyCode:{4}", rs.Tagid, rs.Ip, rs.Number, rs.Locator, rs.KeyCode) + "完成拣选";
 
        }

        /// <summary>
        /// Jacky He
        /// 连接电子标签控制器
        /// 默认端口6020
        /// </summary>
        /// <returns></returns>
        public string Connect()
        {
            return RYB_PTL.RYB_PTL_Connect(this._IP, 6020).ToString();
        }

        /// <summary>
        /// Jacky He
        /// 与电子标签控制器断开
        /// </summary>
        /// <returns></returns>
        public bool DisConnect()
        {
            return RYB_PTL.RYB_PTL_Disconnect(this._IP);
        }

        /// <summary>
        /// Jacky He
        /// 发送给电子标签显示内容
        /// 1
        /// </summary>
        /// <param name="id">标签对应的id 例如"0001"</param>
        /// <param name="disNumber">显示的5位数字</param>
        /// <param name="imode">标签按钮灯光显示模式 默认为0，0-7 数字越大闪烁频率越快</param>
        /// <param name="icolorIndex">标签按钮灯光颜色 默认为6, 0-7</param>
        /// <returns></returns>
        public bool Send(string id, int disNumber, int imode = 0, int icolorIndex = 6)
        {
            return RYB_PTL.RYB_PTL_DspDigit5(this._IP,id,disNumber,imode,icolorIndex);
        }

        /// <summary>
        /// 废弃
        /// </summary>
        public void op()
        {
            bool b = RYB_PTL.RYB_PTL_DspDigit5(this._IP, "0001", 12345, 0, 6);
            bool c = RYB_PTL.RYB_PTL_DspDigit5(this._IP, "0002", 54321, 3, 7);

        }
    }
}
