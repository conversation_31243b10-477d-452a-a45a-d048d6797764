﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.FLOW_ACTION.CONTROL_ACTION"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="CONTROL_ACTION" Height="300" Width="300"  Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

        <uc:ucQuickQuery x:Name="ucQueryControl" Margin="1,1,1,3" Grid.Row="0" BorderBrush="Black"></uc:ucQuickQuery>

        <GroupBox Margin="1,5,1,1"  Header="任务指令" Grid.Row="1">
            <uc:ucCommonDataGrid x:Name="gridControl" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>        

        <GroupBox Grid.Row="2" Header="操作区-请根据流程点击操作按钮"   Margin="1,5,1,1" >
            <uc:ucStatusFlowActionsPanel x:Name="ucFlowControlAction" ></uc:ucStatusFlowActionsPanel>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
