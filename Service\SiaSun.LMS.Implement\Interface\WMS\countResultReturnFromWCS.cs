﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 盘点结果回传接口
    /// </summary>
    public class countResultReturnFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _uniqueCode = string.Empty;            //唯一码
            private string _countCode = string.Empty;      //盘点计划号
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源

            public List<FirstDetails> firstDetails { get; set; }     //一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 盘点计划号
            /// </summary>
            public string countCode
            {
                get { return _countCode; }
                set { _countCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }

        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _itemCode = string.Empty;   //物料号
            private string _quantity = string.Empty;  //数量
            
            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(int paraPlanId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();
            List<FirstDetails> lsFirstDetails = new List<FirstDetails>();

            try
            {
                Model.PLAN_MAIN mPLAN_MIAN = this._P_PLAN_MAIN.GetModel(paraPlanId);
                if (mPLAN_MIAN == null)
                {
                    bResult = false;
                    sResult = string.Format("countResultReturnFromWCS.NotifyMethod:未找到计划信息 计划ID_{0}", paraPlanId);
                    return bResult;
                }

                IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(paraPlanId);
                if(lsPLAN_LIST == null ||lsPLAN_LIST.Count==0)
                {
                    bResult = false;
                    sResult = string.Format("countResultReturnFromWCS.NotifyMethod:计划列表不能为空 计划ID_{0} 计划编码_{1}", paraPlanId,mPLAN_MIAN.PLAN_CODE);
                    return bResult;
                }

                foreach(Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                {
                    FirstDetails firstDetails = new FirstDetails();
                    firstDetails.itemCode = this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID).GOODS_CODE;
                    firstDetails.quantity = mPLAN_LIST.PLAN_LIST_QUANTITY_APPEND.ToString();
                    lsFirstDetails.Add(firstDetails);
                }

                inputPara.uniqueCode = mPLAN_MIAN.PLAN_CODE;
                inputPara.countCode = mPLAN_MIAN.PLAN_RELATIVE_CODE;
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";
                inputPara.firstDetails = lsFirstDetails;

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);
                bResult = this._S_WESJsonService.countResultReturnFromWCS(strInputParaJson, out sResult);
                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if(outputPara.responseCode!="1")
                    {
                        bResult = false;
                        sResult = string.Format(" countResultReturnFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("countResultReturnFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }

    }
}
