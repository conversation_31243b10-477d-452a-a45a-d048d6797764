﻿using System;
using System.Collections;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Reflection;
using System.ServiceModel;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, InstanceContextMode = InstanceContextMode.Single, ConcurrencyMode = ConcurrencyMode.Multiple, MaxItemsInObjectGraph = int.MaxValue)]
    public partial class S_CellService : SiaSun.LMS.Implement.S_BaseService, SiaSun.LMS.Interface.I_CellService
    {
        private static readonly object lockObj = new object();

        public S_CellService()
        {
        }

        /// <summary>获得库房
        /// 获得库房
        /// </summary>
        /// <param name="WAREHOUSE_CODE">库房编码</param>
        /// <returns>返回值</returns>
        public DataTable WAREHOUSE_GetList(int USER_ID, string WAREHOUSE_TYPE)
        {
            DataTable dt = this.GetList(string.Format(@"SELECT WAREHOUSE_ID,WAREHOUSE_NAME FROM WH_WAREHOUSE 
                                                        WHERE {0} {1}",
                                                        USER_ID >= 0 ? "1=1" : string.Format(@"(WAREHOUSE_ID IN(SELECT RELATION_ID2 
                                                        FROM V_SYS_RELATION WHERE RELATION_CODE='SYS_USER-WH_WAREHOUSE'
                                                        AND RELATION_ID1= {0}))", USER_ID),
                                                        WAREHOUSE_TYPE == string.Empty ? string.Empty : string.Format("AND WAREHOUSE_TYPE = '{0}'", WAREHOUSE_TYPE)));

            return dt;
        }

        /// <summary>获得库区
        /// 获得库区
        /// </summary>
        /// <param name="WAREHOUSE_ID">库房编号</param>
        /// <param name="AREA_TYPE">库区类型</param>
        /// <returns>返回值</returns>
        public DataTable AREA_GetList(int WAREHOUSE_ID, string AREA_TYPE)
        {
            string sSQL = "SELECT AREA_ID,AREA_CODE,AREA_NAME,AREA_TYPE FROM WH_AREA WHERE 1=1 {0} {1}";

            DataTable dt = this.GetList(string.Format(sSQL,
                //(0 != WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString()),
                (0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString()),
                string.IsNullOrEmpty(AREA_TYPE) ? string.Empty : string.Format(" AND AREA_TYPE = '{0}'", AREA_TYPE)));

            return dt;
        }

        /// <summary>获得排
        /// 获得排
        /// </summary>
        /// <param name="WAREHOUSE_ID">库房编号</param>
        /// <param name="AREA_ID">库区编号</param>
        /// <param name="LOGIC_ID">存储区编号</param>
        /// <param name="CELL_TYPE">货位类型</param>
        /// <param name="CELL_INOUT">出入库方向</param>
        /// <returns></returns>
        public DataTable CELL_Z_GetList(int WAREHOUSE_ID)
        {
            string sSQL = "SELECT * FROM V_WH_CELL_Z WHERE 1=1 {0} ORDER BY CELL_Z";

            DataTable dt = this.GetList(string.Format(sSQL,
                (0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString())
                ));

            return dt;
        }
        /// <summary>获得排
        /// 获得排
        /// </summary>
        /// <param name="WAREHOUSE_ID">库房编号</param>
        /// <param name="AREA_ID">库区编号</param>
        /// <param name="LOGIC_ID">存储区编号</param>
        /// <param name="CELL_TYPE">货位类型</param>
        /// <param name="CELL_INOUT">出入库方向</param>
        /// <returns></returns>
        public DataTable CELL_Z_GetList_AREA(int WAREHOUSE_ID, int AREA_ID)
        {
            string sSQL = "SELECT * FROM V_WH_CELL_Z WHERE 1=1 {0} {1} ORDER BY CELL_Z";

            DataTable dt = this.GetList(string.Format(sSQL,
                (0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND WAREHOUSE_ID = {0}", WAREHOUSE_ID.ToString()),
                (0 == AREA_ID) ? string.Empty : string.Format(" AND AREA_ID = {0}", AREA_ID.ToString())
                ));

            return dt;
        }

        public IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z(int WAREHOUSE_ID, string CELL_Z)
        {
            return this._P_WH_CELL.CELL_GetList_Z(WAREHOUSE_ID, CELL_Z);
        }

        public IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z_ByArea(int AREA_ID, string CELL_Z)
        {
            return this._P_WH_CELL.CELL_GetList(AREA_ID.ToString(), CELL_Z);
        }

        public bool CellInit()
        {
            //防止误操作
            //return true;

            bool bResult = true;

            try
            {
                //IList<SiaSun.LMS.Model.WH_DESCRIPTION> lsWH_DESCRIPTION = this._P_WH_DESCRIPTION.GetList();
                IList<SiaSun.LMS.Model.WH_DESCRIPTION> lsWH_DESCRIPTION = this._P_WH_DESCRIPTION.GetList().Where(r => r.DESCRIPTION_FLAG == "1").ToList();

                foreach (SiaSun.LMS.Model.WH_DESCRIPTION mWH_DESCRIPTION in lsWH_DESCRIPTION)
                {
                    if (mWH_DESCRIPTION.CELL_TYPE == SiaSun.LMS.Enum.CELL_TYPE.Cell.ToString())
                    {
                        bResult = this.CellCreate(mWH_DESCRIPTION.WAREHOUSE_ID,
                                                  mWH_DESCRIPTION.AREA_ID,
                                                  mWH_DESCRIPTION.LOGIC_ID,
                                                  mWH_DESCRIPTION.START_Z,
                                                  mWH_DESCRIPTION.END_Z,
                                                  mWH_DESCRIPTION.START_X,
                                                  mWH_DESCRIPTION.END_X,
                                                  mWH_DESCRIPTION.START_Y,
                                                  mWH_DESCRIPTION.END_Y,
                                                  mWH_DESCRIPTION.DEVICE_CODE,
                                                  mWH_DESCRIPTION.LANE_WAY,
                                                  mWH_DESCRIPTION.SHELF_TYPE,
                                                  mWH_DESCRIPTION.SHELF_NEIGHBOUR,
                                                  mWH_DESCRIPTION.CELL_MODEL,
                                                  mWH_DESCRIPTION.CELL_LOGICAL_NAME,
                                                  mWH_DESCRIPTION.CELL_INOUT,
                                                  mWH_DESCRIPTION.CELL_TYPE,
                                                  mWH_DESCRIPTION.CELL_STORAGE_TYPE,
                                                  mWH_DESCRIPTION.CELL_FORK_TYPE,
                                                  Convert.ToInt32(mWH_DESCRIPTION.CELL_FORK_COUNT),
                                                  mWH_DESCRIPTION.CELL_WIDTH,
                                                  mWH_DESCRIPTION.CELL_HEIGHT
                                                  );
                    }
                    else
                    {
                        bResult = this.StationCreate(
                                         mWH_DESCRIPTION.WAREHOUSE_ID,
                                         mWH_DESCRIPTION.AREA_ID,
                                         mWH_DESCRIPTION.LOGIC_ID,
                                         mWH_DESCRIPTION.DEVICE_CODE,
                                         mWH_DESCRIPTION.DEVICE_NAME,
                                         mWH_DESCRIPTION.LANE_WAY,
                                         mWH_DESCRIPTION.SHELF_TYPE,
                                         mWH_DESCRIPTION.SHELF_NEIGHBOUR,
                                         mWH_DESCRIPTION.CELL_MODEL,
                                         mWH_DESCRIPTION.CELL_LOGICAL_NAME,
                                         mWH_DESCRIPTION.CELL_INOUT,
                                         mWH_DESCRIPTION.CELL_TYPE,
                                         mWH_DESCRIPTION.CELL_STORAGE_TYPE,
                                         mWH_DESCRIPTION.CELL_FORK_TYPE
                                         );

                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;

            }


            return bResult;
        }

        /// <summary>获得-新建
        /// 获得-新建
        /// </summary>
        /// <param name="AREA_ID"></param>
        /// <param name="z_begin"></param>
        /// <param name="z_end"></param>
        /// <param name="x_begin"></param>
        /// <param name="x_end"></param>
        /// <param name="y_begin"></param>
        /// <param name="y_end"></param>
        /// <param name="WAREHOUSE_CODE"></param>
        /// <param name="AREA_TYPE"></param>
        /// <param name="LANE_WAY"></param>
        /// <param name="CELL_MODEL"></param>
        /// <param name="SHELF_TYPE"></param>
        /// <param name="SHELF_NEIGHBOUR"></param>
        /// <param name="CELL_LOGICAL_NAME"></param>
        /// <param name="CELL_STATUS"></param>
        /// <param name="RUN_STATUS"></param>
        /// <param name="CELL_TYPE"></param>
        /// <param name="DEVICE_CODE"></param>
        /// <returns></returns>
        public bool CellCreate(int WAREHOUSE_ID, int AREA_ID, int LOGIC_ID, int z_begin, int z_end, int x_begin, int x_end, int y_begin, int y_end, string DEVICE_CODE, string LANE_WAY, string SHELF_TYPE, string SHELF_NEIGHBOUR, string CELL_MODEL, string CELL_LOGICAL_NAME, string CELL_INOUT, string CELL_TYPE, string CELL_STORAGE_TYPE, string CELL_FORK_TYPE, int CELL_FORK_COUNT, int CELL_WIDTH, int CELL_HEIGHT)
        {
            bool bResult = true;

            SiaSun.LMS.Model.WH_CELL mST_CELL = new SiaSun.LMS.Model.WH_CELL();

            for (int z = z_begin; z <= z_end; z++)
            {
                for (int x = x_begin; x <= x_end; x++)
                {
                    for (int y = y_begin; y <= y_end; y++)
                    {
                        mST_CELL.WAREHOUSE_ID = WAREHOUSE_ID;
                        mST_CELL.AREA_ID = AREA_ID;
                        mST_CELL.LOGIC_ID = LOGIC_ID;
                        mST_CELL.CELL_CODE = z.ToString().PadLeft(2, '0') + "-" + x.ToString().PadLeft(2, '0') + "-" + y.ToString().PadLeft(2, '0');
                        mST_CELL.CELL_NAME = z.ToString().PadLeft(2, '0') + "排" + x.ToString().PadLeft(2, '0') + "列" + y.ToString().PadLeft(2, '0') + "层";
                        mST_CELL.LANE_WAY = LANE_WAY;
                        mST_CELL.DEVICE_CODE = DEVICE_CODE;
                        mST_CELL.SHELF_TYPE = SHELF_TYPE;
                        mST_CELL.SHELF_NEIGHBOUR = SHELF_NEIGHBOUR;
                        mST_CELL.CELL_MODEL = CELL_MODEL;
                        mST_CELL.CELL_LOGICAL_NAME = CELL_LOGICAL_NAME;
                        mST_CELL.CELL_INOUT = CELL_INOUT;
                        mST_CELL.CELL_TYPE = CELL_TYPE;
                        mST_CELL.CELL_STORAGE_TYPE = CELL_STORAGE_TYPE;
                        mST_CELL.CELL_FORK_TYPE = CELL_FORK_TYPE;
                        mST_CELL.CELL_Z = z;
                        mST_CELL.CELL_X = x;
                        mST_CELL.CELL_Y = y;
                        mST_CELL.CELL_STATUS = SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString();
                        mST_CELL.RUN_STATUS = SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString();
                        mST_CELL.CELL_WIDTH = CELL_WIDTH;
                        mST_CELL.CELL_HEIGHT = CELL_HEIGHT;
                        mST_CELL.CELL_FLAG = "1";

                        //mST_CELL.CELL_GROUP = z.ToString().PadLeft(2, '0') + "-" + (x % CELL_FORK_COUNT == 0 ? (x / CELL_FORK_COUNT) : (x / CELL_FORK_COUNT) + 1).ToString().PadLeft(2, '0') + "-" + y.ToString().PadLeft(2, '0');
                        //双深货位CELL_GROUP生成算法
                        if (CELL_FORK_COUNT == 2)
                        {
                            mST_CELL.CELL_GROUP = (z % CELL_FORK_COUNT == 1 ? (z + 1) : (z - 1)).ToString().PadLeft(2, '0') + "-" + x.ToString().PadLeft(2, '0') + "-" + y.ToString().PadLeft(2, '0');
                        }
                        else
                        {
                            mST_CELL.CELL_GROUP = z.ToString().PadLeft(2, '0') + "-" + x.ToString().PadLeft(2, '0') + "-" + y.ToString().PadLeft(2, '0');
                        }


                        SiaSun.LMS.Model.WH_CELL temp_st_cell = this._P_WH_CELL.GetModel(AREA_ID, mST_CELL.CELL_CODE);

                        if (null == temp_st_cell)
                        {
                            this._P_WH_CELL.Add(mST_CELL);
                        }
                        else
                        {
                            mST_CELL.CELL_ID = temp_st_cell.CELL_ID;

                            mST_CELL.CELL_STATUS = temp_st_cell.CELL_STATUS;

                            mST_CELL.RUN_STATUS = temp_st_cell.RUN_STATUS;

                            this._P_WH_CELL.Update(mST_CELL);
                        }
                    }
                }
            }

            return bResult;
        }

        /// <summary>获得-新建
        /// 获得-新建
        /// </summary>
        /// <param name="AREA_ID"></param>
        /// <param name="CELL_CODE"></param>
        /// <param name="CELL_NAME"></param>
        /// <param name="WAREHOUSE_CODE"></param>
        /// <param name="AREA_TYPE"></param>
        /// <param name="LANE_WAY"></param>
        /// <param name="CELL_MODEL"></param>
        /// <param name="SHELF_TYPE"></param>
        /// <param name="SHELF_NEIGHBOUR"></param>
        /// <param name="CELL_LOGICAL_NAME"></param>
        /// <param name="CELL_STATUS"></param>
        /// <param name="RUN_STATUS"></param>
        /// <param name="CELL_TYPE"></param>
        /// <param name="DEVICE_CODE"></param>
        /// <returns></returns>
        public bool StationCreate(int WAREHOUSE_ID, int AREA_ID, int LOGIC_ID, string DEVICE_CODE, string DEVICE_NAME, string LANE_WAY, string SHELF_TYPE, string SHELF_NEIGHBOUR, string CELL_MODEL, string CELL_LOGICAL_NAME, string CELL_INOUT, string CELL_TYPE, string CELL_STORAGE_TYPE, string CELL_FORK_TYPE)
        {
            bool bResult = true;

            SiaSun.LMS.Model.WH_CELL mST_CELL = new SiaSun.LMS.Model.WH_CELL();

            mST_CELL.WAREHOUSE_ID = WAREHOUSE_ID;
            mST_CELL.AREA_ID = AREA_ID;
            mST_CELL.LOGIC_ID = LOGIC_ID;
            mST_CELL.CELL_CODE = DEVICE_CODE;
            mST_CELL.CELL_NAME = DEVICE_NAME;
            mST_CELL.LANE_WAY = LANE_WAY;
            mST_CELL.DEVICE_CODE = DEVICE_CODE;
            mST_CELL.SHELF_TYPE = SHELF_TYPE;
            mST_CELL.SHELF_NEIGHBOUR = SHELF_NEIGHBOUR;
            mST_CELL.CELL_MODEL = CELL_MODEL;
            mST_CELL.CELL_LOGICAL_NAME = CELL_LOGICAL_NAME;
            mST_CELL.CELL_INOUT = CELL_INOUT;
            mST_CELL.CELL_TYPE = CELL_TYPE;
            mST_CELL.CELL_STORAGE_TYPE = CELL_STORAGE_TYPE;
            mST_CELL.CELL_FORK_TYPE = CELL_FORK_TYPE;
            mST_CELL.CELL_Z = 0;
            mST_CELL.CELL_X = 0;
            mST_CELL.CELL_Y = 0;
            mST_CELL.CELL_STATUS = SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString();
            mST_CELL.RUN_STATUS = SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString();
            mST_CELL.CELL_FLAG = "1";


            SiaSun.LMS.Model.WH_CELL temp_st_cell = this._P_WH_CELL.GetModel(AREA_ID, mST_CELL.CELL_CODE);

            if (null == temp_st_cell)
            {
                this._P_WH_CELL.Add(mST_CELL);
            }
            else
            {
                mST_CELL.CELL_ID = temp_st_cell.CELL_ID;

                mST_CELL.CELL_STATUS = temp_st_cell.CELL_STATUS;

                mST_CELL.RUN_STATUS = temp_st_cell.RUN_STATUS;

                this._P_WH_CELL.Update(mST_CELL);
            }

            return bResult;
        }

        /// <summary>获得入库货位
        /// 获得入库货位
        /// </summary>
        /// <param name="WAREHOUSE_ID">库存编号</param>
        /// <param name="AREA_ID">库区编号</param>
        /// <param name="LOGIC_ID">存储区编号</param>
        /// <param name="START_CELL_ID">起始货位</param>
        /// <param name="CELL_STATUS">货位状态</param>
        /// <param name="RUN_STATUS">运行状态</param>
        /// <param name="CELL_MODEL">货位尺寸</param>
        /// <param name="STOCK_BARCODE">托盘条码</param>
        /// <param name="END_CELL_ID">终止货位</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool CellInAllocate(string START_DEVICE_CODE, string CELL_MODEL, int MANAGE_ID, int FORK_COUNT, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            int LANE_WAY = 0;
            sResult = string.Empty;
            END_CELL_ID = 0;

            Model.WH_CELL mWH_CELL_END = null;

            //使用存储过程实现货位分配
            //END_CELL_ID = this._P_WH_CELL.CellInAllocate(START_DEVICE_CODE, CELL_MODEL, MANAGE_ID, FORK_COUNT, out sResult);

            //wdz add 2018-05-20
            try
            {
                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(START_DEVICE_CODE);
                if (mWH_CELL_START == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到起始货位 入参货位编码-{0}", START_DEVICE_CODE);
                    return bResult;
                }

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);
                if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未能获取任务列表 任务ID-{0}", MANAGE_ID);
                    return bResult;
                }

                Hashtable htGOODS_PROPERTY = null;

                if (lsMANAGE_LIST.Count == 1)
                {
                    //获取设备属性
                    //bResult = this._S_Goods.GoodsPropertyGetHashtable(lsMANAGE_LIST[0].GOODS_ID, lsMANAGE_LIST[0], out htGOODS_PROPERTY, out sResult);
                }

                if (string.IsNullOrEmpty(CELL_MODEL))
                {
                    CELL_MODEL = "%";
                }
                //如果有锁定货位 则分配
                mWH_CELL_END = this._P_WH_CELL.GetModel_LockDeviceCode(START_DEVICE_CODE);
                //对于锁定货位 要验证路径是否可用
                if (mWH_CELL_END != null && this.GetList(string.Format("select count(0) from IO_CONTROL_ROUTE where START_DEVICE={0} and END_DEVICE={1} and CONTROL_ROUTE_STATUS=1", mWH_CELL_START.DEVICE_CODE, mWH_CELL_END.DEVICE_CODE)).Rows.Count <= 0)
                {
                    mWH_CELL_END = null;
                }

                if (mWH_CELL_END == null)
                {
                    DataTable dtLANE_WAY = null;
                    //获取巷道
                    bResult = this.InLaneWayGetList(mWH_CELL_START.CELL_ID, lsMANAGE_LIST[0].GOODS_ID, htGOODS_PROPERTY, Enum.CELL_STATUS.Nohave, out dtLANE_WAY, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    DataView dvLANE_WAY = dtLANE_WAY.DefaultView;

                    dvLANE_WAY.Sort = "MANAGE_IN_QUANTITY ,CELL_QUANTITY desc, DEVICE_CODE desc";

                    if (dvLANE_WAY.Count > 0)
                    {
                        LANE_WAY = Convert.ToInt32(dvLANE_WAY[0]["LANE_WAY"]);
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("未能获取可用巷道，请查看设备状态");
                        return bResult;
                    }

                    //获取货位
                    bResult = this.CellGetIn(LANE_WAY, 0, mWH_CELL_START.CELL_ID, lsMANAGE_LIST[0].GOODS_ID, htGOODS_PROPERTY, out END_CELL_ID, out sResult);
                    if (!bResult)
                    {
                        sResult = "分配货位错误" + sResult;
                        return bResult;
                    }

                    //更新货位状态
                    bResult = this.CellUpdateStatus(END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Run.ToString(), out sResult);
                    if (!bResult)
                    {
                        sResult = "更新货位状态错误 " + sResult;
                        return bResult;
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("分配货位时发生异常 {0}", ex.Message);
            }


            return bResult;
        }

        /// <summary>
        /// 根据起始位置分配终止位置，适合终止位置为固定授货台
        /// </summary>
        /// <param name="START_DEVICE_CODE"></param>
        /// <param name="END_CELL_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool CellOutAllocate(string START_DEVICE_CODE, out int END_CELL_ID, out string sResult)
        {
            bool bResult = false;

            sResult = string.Empty;

            END_CELL_ID = 0;

            //出库-》站台

            string strSQL = string.Format(@"SELECT CELL_ID,CELL_CODE FROM WH_CELL
                                            WHERE CELL_TYPE ='Station' 
                                            AND CELL_INOUT = 'Out'
                                            AND LANE_WAY = (SELECT LANE_WAY FROM WH_CELL WHERE CELL_CODE = '{0}')"
                                             , START_DEVICE_CODE);

            DataTable dt = this.GetList(strSQL);

            bResult = dt.Rows.Count > 0;

            if (bResult)
            {
                END_CELL_ID = Convert.ToInt32(dt.Rows[0]["CELL_ID"]);
            }
            else
            {
                sResult = string.Format("未找到出库货位");

                return false;
            }


            if (END_CELL_ID == 0)
                return false;
            else
                return true;
        }

        /// <summary>获得出库货位
        /// 四川电力 根据巷道 物料及物料属性 获得出库货位
        /// </summary>
        public bool CellOutAllocate(int LANE_WAY, int END_CELL_ID, int GOODS_ID, string GOODS_PROPERTY, decimal MANAGE_LIST_QUANTITY, out int START_CELL_ID, out string STOCK_BARCODE, out decimal STORAGE_LIST_QUANTITY, out string sResult)
        {
            bool bResult = false;

            sResult = string.Empty;

            START_CELL_ID = 0;

            STORAGE_LIST_QUANTITY = 0;

            STOCK_BARCODE = string.Empty;

            SiaSun.LMS.Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(END_CELL_ID);

            bResult = null != mWH_CELL;

            if (!bResult)
            {
                sResult = "未找到出库站台";

                return bResult;
            }

            string sSQL = string.Format(@"SELECT  STORAGE_MAIN.CELL_ID ,STOCK_BARCODE ,
                         ISNULL(SUM(STORAGE_LIST_QUANTITY),0) AS MANAGE_LIST_QUANTITY,
                         max(GOODS_PROPERTY1) as PRIOROUT_WEIGTH
                         FROM  STORAGE_LIST 
                         INNER JOIN STORAGE_MAIN ON (STORAGE_LIST.STORAGE_ID = STORAGE_MAIN.STORAGE_ID )
                         INNER JOIN WH_CELL ON (STORAGE_MAIN.CELL_ID = WH_CELL.CELL_ID) 
                         left join STORAGE_LOCK on STORAGE_LOCK.STORAGE_LIST_ID = STORAGE_LIST.STORAGE_LIST_ID
                         WHERE GOODS_ID = {2} 
                               /*{0}*/
                               {1}
                               {3}
                           AND LANE_WAY = {4}
                           AND CELL_TYPE = 'Cell'

                       GROUP BY STOCK_BARCODE,
                                STORAGE_MAIN.CELL_ID,
                                CELL_Y,
                                CELL_X                      
                      ORDER BY PRIOROUT_WEIGTH  desc,
                               max(ENTRY_TIME),
                               ABS({5}-ISNULL(SUM(STORAGE_LIST_QUANTITY),0)),
                               MANAGE_LIST_QUANTITY DESC,
                               CELL_Y,
                               CELL_X",
                /*0*/string.Format(" AND CELL_STATUS = '{0}'", SiaSun.LMS.Enum.CELL_STATUS.Full.ToString()),
                /*1*/string.Format(" AND RUN_STATUS = '{0}'", SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString()),
                /*2*/GOODS_ID,
                /*3*/ GOODS_PROPERTY,
                /*4*/LANE_WAY,
                /*5*/MANAGE_LIST_QUANTITY
                );
            //max(substring(ENTRY_TIME,0,11)),修改为max(ENTRY_TIME),

            sSQL = Persistence.P_Base_House._isOracelProvider ? sSQL.Replace("ISNULL", "NVL").Replace("substring", "substr") : sSQL;

            DataTable dt = this.GetList(sSQL);

            bResult = dt.Rows.Count > 0;

            if (bResult)
            {
                START_CELL_ID = Convert.ToInt32(dt.Rows[0]["CELL_ID"]);

                STOCK_BARCODE = Convert.ToString(dt.Rows[0]["STOCK_BARCODE"]);

                STORAGE_LIST_QUANTITY = Convert.ToDecimal(dt.Rows[0]["MANAGE_LIST_QUANTITY"]);
            }
            else
            {
                START_CELL_ID = 0;

                sResult = string.Format("未找到出库货位");
            }

            return bResult;
        }

        /// <summary>
        /// 根据物料及物料属性获得出库巷道
        /// </summary>
        public bool OutLaneWayGetList(int END_CELL_ID, int GOODS_ID, string GOODS_PROPERTY_CONDITION, out DataTable dtLANEWAY, out string sResult)
        {
            bool bResult = true;

            dtLANEWAY = null;

            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(END_CELL_ID);

            bResult = null != mWH_CELL;

            if (!bResult)
            {
                sResult = "未找到出库站台";

                return bResult;
            }

            string sSQL = string.Format(@"SELECT DEVICE_CODE,
                    LANE_WAY ,
                   SUM(STORAGE_LIST_QUANTITY) AS STORAGE_BALANCE,
                    nvl((select count(0) from MANAGE_MAIN left join WH_CELL on MANAGE_MAIN.START_CELL_ID = WH_CELL.CELL_ID where WH_CELL.DEVICE_CODE= V_STORAGE_LIST.DEVICE_CODE),0) as OUT_TASK_BALANCE,
                    substr(to_char( avg(to_date(substr(ENTRY_TIME,0,10),'yyyy-MM-dd')-to_date('2017-11-04','yyyy-MM-dd')) ) ,0,10) as dateWeight,
                    count(0) as boxCount,
                    {5} as PRIOR_WEIGHT
                    FROM V_STORAGE_LIST
                    WHERE GOODS_ID = {2}
                    {3}
                    AND CELL_TYPE = 'Cell'
                    AND DEVICE_CODE IN (SELECT START_DEVICE FROM IO_CONTROL_ROUTE WHERE END_DEVICE='{4}' and CONTROL_ROUTE_STATUS=1 AND  CONTROL_ROUTE_MANAGE=1)
                     {0}
                     {1}                    
                    GROUP BY LANE_WAY,DEVICE_CODE",
                /*0*/string.Format(" AND ( CELL_STATUS = '{0}' or CELL_STATUS = '{1}') ", SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(), SiaSun.LMS.Enum.CELL_STATUS.Pallet.ToString()),
                /*1*/string.Format(" AND RUN_STATUS = '{0}'", SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString()),
                /*2*/GOODS_ID,
                /*3*/GOODS_PROPERTY_CONDITION,
                /*4*/mWH_CELL.DEVICE_CODE,
                /*5*/GOODS_ID > 10 ? "nvl(sum(GOODS_PROPERTY1),0)" : "0"
                      );

            dtLANEWAY = this.GetList(sSQL);

            bResult = dtLANEWAY.Rows.Count > 0;

            if (!bResult)
            {
                sResult = string.Format("可用巷道内库存不足(可能原因:库存不足/设备报警/货位状态异常)");

                return bResult;
            }

            return bResult;
        }

        /// <summary>货位-更改状态
        /// 货位-更改状态
        /// </summary>
        public bool CellUpdateStatus(int CELL_ID, string CELL_STATUS, string RUN_STATUS, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                if (0 == CELL_ID)
                {
                    return bResult;
                }

                SiaSun.LMS.Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(CELL_ID);
                if (mWH_CELL == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到货位{0}", CELL_ID.ToString());
                    return bResult;
                }

                //对于三楼线边仓，将货位置空时同时更新所在货位组状态
                if (mWH_CELL.AREA_ID == 4 && Enum.CELL_STATUS.Nohave.ToString().Equals(CELL_STATUS) && Enum.RUN_STATUS.Enable.ToString().Equals(RUN_STATUS))
                {
                    string wh_cell_group_code = mWH_CELL.CELL_CODE.Substring(3);
                    WH_CELL_GROUP mWH_CELL_GROUP = _P_WH_CELL_GROUP.GetModelByGroupCode(wh_cell_group_code);
                    if (mWH_CELL_GROUP == null)
                    {
                        bResult = false;
                        sResult = $"未找到{mWH_CELL.CELL_CODE}所在货位组{wh_cell_group_code}";
                        return bResult;
                    }
                    mWH_CELL_GROUP.AVAIL_LOC_NUM += 1;
                    if (mWH_CELL_GROUP.AVAIL_LOC_NUM == 4)
                    {
                        mWH_CELL_GROUP.GROUP_STATUS = "";
                    }
                    mWH_CELL_GROUP.PICK_NO = "";
                    _P_WH_CELL_GROUP.Update(mWH_CELL_GROUP);
                }

                if (!mWH_CELL.CELL_TYPE.Equals(Enum.CELL_TYPE.Cell.ToString()))
                {
                    return bResult;
                }
                if (!string.IsNullOrEmpty(CELL_STATUS))
                {
                    mWH_CELL.CELL_STATUS = CELL_STATUS;
                }
                if (!string.IsNullOrEmpty(RUN_STATUS))
                {
                    mWH_CELL.RUN_STATUS = RUN_STATUS;
                }
                this._P_WH_CELL.Update(mWH_CELL);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }


        /// <summary>
        /// 获得巷道-终止站台 物料 物料属性 货位状态
        /// </summary>
        public bool InLaneWayGetList(int START_CELL_ID, int GOODS_ID, Hashtable htGOODS_PROPERTY, Enum.CELL_STATUS eCELL_STATUS, out DataTable dtLANEWAY, out string sResult)
        {
            dtLANEWAY = null;
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(START_CELL_ID);

            bResult = null != mWH_CELL || START_CELL_ID.Equals(0);
            if (!bResult)
            {
                sResult = string.Format("未找到站台{0}", START_CELL_ID);
                return bResult;
            }
            string sGOODS_PROPERTY_SQL = SiaSun.LMS.Common.StringUtil.GetGoodsPropertySql(htGOODS_PROPERTY);

            string sSQL = string.Format(@"
                select LANE_WAY,
                DEVICE_CODE,
                nvl((select sum(STORAGE_LIST_QUANTITY) from V_STORAGE_LIST where V_STORAGE_LIST.LANE_WAY = V_LANE_WAY.LANE_WAY and GOODS_ID={2} and RUN_STATUS='{0}' and CELL_STATUS='{5}' {3}),0) as LANEWAY_QUANTITY,
                nvl((select count(0) from V_MANAGE where END_LANE_WAY=V_LANE_WAY.LANE_WAY),0) as MANAGE_IN_QUANTITY,
                nvl((select count(0) from MANAGE_MAIN inner join WH_CELL on (MANAGE_MAIN.START_CELL_ID=WH_CELL.CELL_ID) where WH_CELL.LANE_WAY = V_LANE_WAY.LANE_WAY and MANAGE_STATUS='Executing'),0) as MANAGE_OUT_QUANTITY,
                (select count(0) from WH_CELL where CELL_TYPE ='Cell'  and RUN_STATUS = '{0}' and CELL_STATUS = '{1}' and LANE_WAY =V_LANE_WAY.LANE_WAY) as CELL_QUANTITY 
                from V_LANE_WAY 
                where LANE_WAY in (select LANE_WAY from WH_CELL where RUN_STATUS = '{0}' and CELL_STATUS = '{1}' and CELL_TYPE ='Cell') {4} order by LANE_WAY",
                /*0*/Enum.RUN_STATUS.Enable.ToString(),
                /*1*/eCELL_STATUS.ToString(),
                /*2*/GOODS_ID,
                /*3*/sGOODS_PROPERTY_SQL,
                /*4*/START_CELL_ID.Equals(0) ? string.Empty : string.Format("and DEVICE_CODE in (select {1}_DEVICE from IO_CONTROL_ROUTE where {2}_DEVICE='{0}' and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE = 1)",
                    /*40*/mWH_CELL.DEVICE_CODE,
                    /*41*/eCELL_STATUS.Equals(Enum.CELL_STATUS.Full) ? "START" : "END",
                    /*42*/eCELL_STATUS.Equals(Enum.CELL_STATUS.Full) ? "END" : "START"),
                /*5*/Enum.CELL_STATUS.Full.ToString()
                );

            dtLANEWAY = this._P_Base_House.GetDataTable(sSQL);
            bResult = dtLANEWAY.Rows.Count > 0;

            if (!bResult)
            {
                sResult = string.Format("未找到可用的巷道");
                return bResult;
            }
            return bResult;
        }


        /// <summary>
        /// 获得入库货位
        /// </summary>
        public bool CellGetIn(int LANE_WAY, int LOGIC_ID, int START_CELL_ID, int GOODS_ID, Hashtable htGOODS_PROPERTY, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            END_CELL_ID = 0;

            SiaSun.LMS.Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(START_CELL_ID);
            bResult = null != mWH_CELL;
            if (!bResult)
            {
                sResult = string.Format("未找到入库站台{0}", START_CELL_ID);
                return bResult;
            }

            SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(GOODS_ID);
            bResult = null != mGOODS_MAIN;
            if (!bResult)
            {
                sResult = string.Format("未找到设备{0}", GOODS_ID);
                return bResult;
            }


            //string sSQL = string.Format(@"select CELL_ID from WH_CELL where CELL_ID in
            //                                (select CELL_ID from 
            //                                   (select CELL_ID {5}
            //                                    from V_WH_CELL 
            //                                    where CELL_TYPE = 'Cell' and CELL_STATUS = '{0}' and RUN_STATUS = '{1}' and LANE_WAY = {2} {3}
            //                                    order by {6} abs(CELL_X-40) {4}, CELL_Y, CELL_Z)
            //                                 where rownum=1) for update",
            //    /*0*/  SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
            //    /*1*/  SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
            //    /*2*/LANE_WAY,
            //    /*3*/(0 == LOGIC_ID) ? string.Empty : string.Format(" AND LOGIC_ID ={0}", LOGIC_ID.ToString()),
            //    /*4升序*/(mGOODS_MAIN.GOODS_ID.Equals(-1)) ? "desc" : string.Empty,
            //    ///*5*/(LANE_WAY > 1) ? string.Format(@" ,case mod(cell_x,3) when 0 then 'a' when 2 then 'b' when 1 then 'c' end cell_x_mod") : string.Empty,
            //    ///*6*/(LANE_WAY > 1) ? "cell_x_mod," : string.Empty);
            //    /*5*/string.Empty,
            //    /*6*/string.Empty);

            string sSQL = string.Format(@"select CELL_ID from WH_CELL where CELL_ID in
                                            (select CELL_ID from 
                                               (select CELL_ID {5}
                                                from V_WH_CELL 
                                                where CELL_TYPE = 'Cell' and CELL_STATUS = '{0}' and RUN_STATUS = '{1}' and LANE_WAY = {2} {3}
                                                order by {6} CELL_X ,CELL_Y {4}, CELL_Z)
                                             where rownum=1) for update",
                                        /*0*/  SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                        /*1*/  SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                        /*2*/LANE_WAY,
                                        /*3*/(0 == LOGIC_ID) ? string.Empty : string.Format(" AND LOGIC_ID ={0}", LOGIC_ID.ToString()),
                                        /*4升序*/(mGOODS_MAIN.GOODS_ID.Equals(-1)) ? "desc" : string.Empty,
                                        ///*5*/(LANE_WAY > 1) ? string.Format(@" ,case mod(cell_x,3) when 0 then 'a' when 2 then 'b' when 1 then 'c' end cell_x_mod") : string.Empty,
                                        ///*6*/(LANE_WAY > 1) ? "cell_x_mod," : string.Empty);
                                        /*5*/string.Empty,
                                        /*6*/string.Empty);

            DataTable dtCELL = this.GetList(sSQL);
            bResult = dtCELL.Rows.Count > 0;
            if (!bResult)
            {
                sResult = string.Format("无可用货位");
                return bResult;
            }

            END_CELL_ID = Convert.ToInt32(dtCELL.Rows[0]["CELL_ID"].ToString());
            return bResult;
        }


        /// <summary>
        /// 计算货位的出库效率
        /// wdz add 2019-07-12 17:37:36
        /// </summary>
        /// <returns></returns>
        public string CalculateCellOutScore()
        {
            string message = string.Empty;
            const int COUNT_THRESHOLD = 5;
            const double SCORE_THRESHOLD = 0.95;

            try
            {
                //获取下架到41001的RECORD信息 
                string queryTrain = string.Format("select LANE_WAY,CELL_X ,INTERVAL_SECONDS from (select LANE_WAY, CELL_X, CELL_Y, round(min(INTERVAL_SECONDS), 0) as INTERVAL_SECONDS, count(0) as COUNTER from V_A_DOWN_TO_41001 t where MANAGE_END_TIME  > '{0}' group by LANE_WAY, CELL_X, CELL_Y) where COUNTER > {1} order by LANE_WAY, CELL_X, CELL_Y", DateTime.Now.AddMonths(-6).ToString("yyyy-MM-dd"), COUNT_THRESHOLD);
                DataTable dtTrainData = this.GetList(queryTrain);
                //获取所有货位的数据
                string queryPredict = string.Format("select LANE_WAY,CELL_X,CELL_ID from WH_CELL where AREA_ID = 1 and CELL_TYPE = 'Cell' ");
                DataTable dtPredictData = this.GetList(queryPredict);

                if (dtTrainData != null && dtTrainData.Rows.Count > 0 && dtPredictData != null && dtPredictData.Rows.Count > 0)
                {
                    //实例化线性回归模型
                    Common.LinearRegression model = new Common.LinearRegression(
                        dtTrainData, new Common.LinearRegression.Mask[] { Common.LinearRegression.Mask.X_DICT, Common.LinearRegression.Mask.X_DIRECT, Common.LinearRegression.Mask.YorKEY_DIRECT },
                        dtPredictData, new Common.LinearRegression.Mask[] { Common.LinearRegression.Mask.X_DICT, Common.LinearRegression.Mask.X_DIRECT, Common.LinearRegression.Mask.YorKEY_DIRECT });

                    //训练
                    if (!string.IsNullOrEmpty(model.Fit()))
                    {
                        message = "训练结果不正确";
                        return message;
                    }

                    //评价训练结果的可信程度
                    if (model.Score < SCORE_THRESHOLD)
                    {
                        message = string.Format("训练的评价指标[{0}]低于阈值[{1}]_未更新WH_CELL数据", model.Score, SCORE_THRESHOLD);
                        return message;
                    }

                    //开始预测
                    Dictionary<string, double> predictResult = null;
                    message = model.Predict(out predictResult);

                    //更细WH_CELL表
                    if (string.IsNullOrEmpty(message) && predictResult != null && predictResult.Count > 0)
                    {
                        //min_max归一化
                        //List<double> valueList = predictResult.Values.ToList();
                        //double max = valueList.Max();
                        //double min = valueList.Min();
                        //double max_min = Math.Round(max - min, 2);

                        //wdz alter 2019-08-06
                        var sortPredictResult = from r in predictResult orderby r.Value select r;
                        double max = sortPredictResult.Count();
                        double min = 1;
                        double max_min = max - min;

                        //清除历史数据
                        this._P_Base_House.ExecuteNonQuery(string.Format("update WH_CELL set CELL_SCORE = 0 , SCORE_UPDATE_TIME = '{0}' where AREA_ID = 1 and CELL_TYPE = 'Cell'", Common.StringUtil.GetDateTime()));

                        int refCount = 0;
                        string dateTime = Common.StringUtil.GetDateTime();
                        int i = 1;
                        foreach (var item in sortPredictResult)
                        {
                            double weight = Math.Round((max - i) / max_min, 8);
                            string updateSql = string.Format("update WH_CELL set CELL_SCORE = {0} ,SCORE_UPDATE_TIME = '{1}' where CELL_ID = {2}", weight, dateTime, item.Key);
                            refCount = refCount + this.ExecuteNonQuery_ReturnInt(updateSql);
                            i++;
                        }

                        this.CreateSysLog(Enum.LogThread.System, "System", Enum.LOG_LEVEL.Information, string.Format("CalculateCellOutScore():更新WH_CELL出库效率分数成功_更新行数[{0}]", refCount));
                    }
                    else
                    {
                        message = "预测结果不正确";
                    }
                }


            }
            catch (Exception ex)
            {
                message = string.Format("发生异常_信息[{0}]", ex.Message);
            }
            finally
            {
                this.CreateSysLog(Enum.LogThread.System, "System", string.IsNullOrEmpty(message), string.Format("CalculateCellOutScore():根据近半年的任务计算货位出库效率{0}_信息[{1}]", string.IsNullOrEmpty(message) ? "成功" : "失败", message));
            }

            return message;
        }

        /// <summary>
        /// wdz add 2019-08-06 15:43:59
        /// 入库货位分配，直接选定货位，主要原则是物料分数与货位分数匹配
        /// </summary>
        /// <param name="startDeviceCode"></param>
        /// <param name="cellModel"></param>
        /// <param name="endCellId"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool CellInAllocate(string startDeviceCode, double maxGoodsScore, string cellModel, out int endCellId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            endCellId = 0;

            //作为除数对结果进行分段，加入到order
            int manageInLevelParam = 3;
            int manageOutLevelParam = 10;
            int enableCellLevelParam = 200;

            string levelParam = string.Empty;
            if (this._S_SystemService.GetSysParameter("IntelligentAllcateCellLevelParam", out levelParam) &&
                !string.IsNullOrEmpty(levelParam))
            {
                string[] paramArray = levelParam.Split('-');
                if (paramArray.Count() == 3)
                {
                    int.TryParse(paramArray[0], out manageInLevelParam);
                    int.TryParse(paramArray[1], out manageOutLevelParam);
                    int.TryParse(paramArray[2], out enableCellLevelParam);
                }
            }

            try
            {
                string querySql = string.Format(@"select * from (
                    select * from (
                    select t.CELL_ID ,t.CELL_CODE ,t.CELL_SCORE ,t.DEVICE_CODE ,t.RUN_STATUS ,t.CELL_STATUS ,t.CELL_TYPE ,t.CELL_MODEL,
                        floor(nvl((select count(0) from V_MANAGE a where t.LANE_WAY = a.END_LANE_WAY),0)/{3}) as MANAGE_IN_COUNT_LEVEL,
                        floor(nvl((select count(0) from V_MANAGE b where t.LANE_WAY = b.START_LANE_WAY and b.MANAGE_STATUS = 'Executing'),0)/{4}) as MANAGE_OUT_COUNT_LEVEL,
                        floor(nvl((select count(0) from WH_CELL c where c.CELL_TYPE = 'Cell'  and c.RUN_STATUS = 'Enable' and c.CELL_STATUS = 'Nohave' and c.LANE_WAY = t.LANE_WAY),0)/{5}) as CELL_QUANTITY_LEVEL
                    from WH_CELL t) d
                    where d.CELL_TYPE = 'Cell' and d.CELL_STATUS = 'Nohave' and d.RUN_STATUS = 'Enable' and {0}
                        d.DEVICE_CODE in (select END_DEVICE from IO_CONTROL_ROUTE where START_DEVICE = '{1}' and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE = 1) 
                    order by MANAGE_IN_COUNT_LEVEL, MANAGE_OUT_COUNT_LEVEL, CELL_QUANTITY_LEVEL desc, abs(d.CELL_SCORE - {2})) where rownum = 1",
                    /*0*/ string.IsNullOrEmpty(cellModel) ? "" : string.Format("d.CELL_MODEL like '{0}' and ", cellModel),
                    /*1*/ startDeviceCode,
                    /*2*/ maxGoodsScore,
                    /*3*/ manageInLevelParam,
                    /*4*/ manageOutLevelParam,
                    /*5*/ enableCellLevelParam
                    );

                DataTable dtOptimalCell = this.GetList(querySql);
                if (dtOptimalCell == null || dtOptimalCell.Rows.Count < 1)
                {
                    bResult = false;
                    sResult = string.Format("未找到可用货位_请查看路径状态是否可用");
                    return bResult;
                }

                endCellId = int.Parse(dtOptimalCell.Rows[0]["CELL_ID"].ToString());

                //更新货位状态
                bResult = this.CellUpdateStatus(endCellId, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Run.ToString(), out sResult);
                if (!bResult)
                {
                    sResult = string.Format("更新货位状态错误_信息[{0}]", sResult);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("分配入库货位时异常_异常信息[{0}]", ex.Message);
            }

            return bResult;
        }

        ///<summary>
        ///@mxh add 2020-09-23 14:09
        /// 入库双深货位分配
        /// 此入库货位分配方法，仅限于三期双深立体库（三楼）使用，
        /// 使用情景仅限于5楼齐套箱入库至3楼，空箱不入到3楼立库中
        /// 
        /// V_WH_CELL_DOUBLE_TO_NORMAL视图仅包含三楼双深立体库的货位
        /// </summary>
        //TODO:STORAGE改MANAGE
        public bool CellInAllocate(int WAREHOUSE_ID,
                                      int START_CELL_ID,
                                      string GOODS_PROPERTY6,
                                      string GOODS_PROPERTY7,
                                      string GOODS_PROPERTY8,
                                      out int END_CELL_ID,
                                      out string sResult)
        {
            sResult = string.Empty;
            bool bResult = false;
            END_CELL_ID = 0;
            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mSTART_WH_CELL = null;

            lock (lockObj)
            {
                try
                {
                    mSTART_WH_CELL = this._P_WH_CELL.GetModel(START_CELL_ID);

                    if (mSTART_WH_CELL == null)
                    {
                        bResult = false;
                        sResult = string.Format("起始位置索引[{0}]不存在", START_CELL_ID);
                        return bResult;
                    }

                    #region 优先级【1】：Double有货无任务且物料一致，选择Normal空货位
                    string strCell = string.Format(@"SELECT NORMAL_CELL_ID AS CELL_ID 
                                                    FROM V_WH_CELL_DOUBLE_TO_NORMAL
                                                    WHERE 1=1
                                                    {0}
                                                    {1}
                                                    {2}
                                                    {3}
                                                    {4}
                                                    {5}
                                                    {6}
                                                    {7}
                                                    ORDER BY NORMAL_CELL_X,NORMAL_CELL_Y,NORMAL_CELL_Z
                                                    FETCH NEXT 1 ROWS ONLY",
                            /*0.仓库*/(0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND NORMAL_WAREHOUSE_ID = {0}", WAREHOUSE_ID),
                            /*1.NORMAL货位类型为Cell*/string.Format(" AND NORMAL_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                            /*2.Normal为空*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                            /*3.Normal可用*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                            /*4.Double有货*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                            /*5.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                            /*6.Double库存为齐套箱*/string.Format(" AND DOUBLE_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                            /*7.与Double物料一致*/string.Format(@"AND DOUBLE_STORAGE_GOODS_PROPERTY6 = '{0}'
                                                             AND DOUBLE_STORAGE_GOODS_PROPERTY7 = '{1}'
                                                             AND DOUBLE_STORAGE_GOODS_PROPERTY8 = '{2}'", GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8));
                    DataTable dtCell = this.GetList(strCell);
                    #endregion

                    bResult = dtCell.Rows.Count > 0;

                    if (bResult)
                    {
                        END_CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);

                    }
                    else
                    {
                        #region 优先级【2】：Double无货有任务且物料一致，选择Normal空货位  
                        //TODO:5楼移库有库存下列SQL有效，1楼越库无库存下列SQL无效 




                        strCell = string.Format(@"SELECT NORMAL_CELL_ID AS CELL_ID 
                                                FROM V_WH_CELL_DOUBLE_TO_NORMAL
                                                WHERE 1=1
                                                {0}
                                                {1}
                                                {2}
                                                {3}
                                                {4}
                                                {5}
                                                {6}
                                              
                                                ORDER BY NORMAL_CELL_X,NORMAL_CELL_Y,NORMAL_CELL_Z
                                                FETCH NEXT 1 ROWS ONLY",
                        /*0.仓库*/(0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND NORMAL_WAREHOUSE_ID = {0}", WAREHOUSE_ID),
                        /*1.NORMAL货位类型为Cell*/string.Format(" AND NORMAL_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                        /*2.Normal为空*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                        /*3.Normal可用*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                        /*4.Double无货*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                        /*5.Double有任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Selected.ToString()),

                        /*6.与Double物料一致*/string.Format(@"AND DOUBLE_MANAGE_GOODS_PROPERTY6 = '{0}'
                                                            AND DOUBLE_MANAGE_GOODS_PROPERTY7 = '{1}'
                                                            AND DOUBLE_MANAGE_GOODS_PROPERTY8 = '{2}'", GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8));


                        dtCell = this.GetList(strCell);
                        #endregion

                        bResult = dtCell.Rows.Count > 0;

                        if (bResult)
                        {
                            END_CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);

                        }
                        else
                        {
                            #region 优先级【3】：Double和Normal全空无任务，选择Double空货位
                            strCell = string.Format(@"SELECT DOUBLE_CELL_ID AS CELL_ID 
                                                    FROM V_WH_CELL_DOUBLE_TO_NORMAL
                                                    WHERE 1=1
                                                    {0}
                                                    {1}
                                                    {2}
                                                    {3}
                                                    {4}
                                                    {5}
                                                    ORDER BY DOUBLE_CELL_X,DOUBLE_CELL_Y,DOUBLE_CELL_Z",
                            /*0.仓库*/(0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND NORMAL_WAREHOUSE_ID = {0}", WAREHOUSE_ID),
                            /*1.Double货位类型为Cell*/string.Format(" AND DOUBLE_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                            /*2.Normal为空*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                            /*3.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                            /*4.Double为空*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                            /*5.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()));

                            dtCell = this.GetList(strCell);
                            #endregion

                            //对于双空货位，至少要保留一对用于倒库
                            bResult = dtCell.Rows.Count > 1;

                            if (bResult)
                            {
                                END_CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);

                            }
                            else
                            {
                                #region  优先级【4】：当不满足以上3条，选取Double无任务（有货或无货均可，不可为选中状态），选择Normal空货位
                                strCell = string.Format(@"SELECT NORMAL_CELL_ID AS CELL_ID 
                                                    FROM V_WH_CELL_DOUBLE_TO_NORMAL
                                                    WHERE 1=1
                                                    {0}
                                                    {1}
                                                    {2}
                                                    {3}
                                                    {4}
                                                    ORDER BY NORMAL_CELL_X,NORMAL_CELL_Y,NORMAL_CELL_Z
                                                    FETCH NEXT 1 ROWS ONLY",
                                /*0.仓库*/(0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND NORMAL_WAREHOUSE_ID = {0}", WAREHOUSE_ID),
                                /*1.Normal货位类型为Cell*/string.Format(" AND NORMAL_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                                /*2.Normal为空*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                                /*3.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                                /*4.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()));

                                dtCell = this.GetList(strCell);
                                #endregion

                                bResult = dtCell.Rows.Count > 0;

                                if (bResult)
                                {
                                    END_CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);

                                }
                                else
                                {
                                    //无可用货位
                                    bResult = false;

                                    sResult = string.Format("货位不足,请确认货位占用情况");

                                    #region 计算是否存在Normal有货的Double空货位，返回提示进行【双深货位整理】
                                    //string sCheckDouble = string.Format(@"SELECT DOUBLE_CELL_CODE,NORMAL_CELL_CODE 
                                    //                    FROM V_WH_CELL_DOUBLE_TO_NORMAL
                                    //                    WHERE 1=1
                                    //                    {0}
                                    //                    {1}
                                    //                    {2}
                                    //                    {3}
                                    //                    {4}
                                    //                    {5}
                                    //                    {6}
                                    //                    {7}
                                    //                    ORDER BY NORMAL_CELL_X,NORMAL_CELL_Y,NORMAL_CELL_Z
                                    //                    FETCH NEXT 1 ROWS ONLY",
                                    ///*0.仓库*/(0 == WAREHOUSE_ID) ? string.Empty : string.Format(" AND NORMAL_WAREHOUSE_ID = {0}", WAREHOUSE_ID),
                                    ///*1.库区*/(0 == AREA_ID) ? string.Empty : string.Format(" AND NORMAL_AREA_ID = {0}", AREA_ID),
                                    ///*2.逻辑区*/string.IsNullOrEmpty(LOGIC_IDs) ? string.Empty : string.Format(" AND NORMAL_LOGIC_ID IN ({0})", LOGIC_IDs),
                                    ///*3.Double货位类型为Cell*/string.Format(" AND DOUBLE_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                                    ///*4.Normal有货*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                                    ///*5.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                                    ///*6.Double为空*/ string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                                    ///*7.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()));

                                    //DataTable dtCHECK_DOUBLE = base.GetList(sCheckDouble);

                                    //bResult = dtCHECK_DOUBLE.Rows.Count > 0;

                                    //if (bResult)
                                    //{
                                    //    sResult = string.Format("双深货位[{0}]空闲，但相邻普通货位[{1]]已占用，请进行货位整理", dtCHECK_DOUBLE.Rows[0]["DOUBLE_CELL_CODE"], dtCHECK_DOUBLE.Rows[0]["NORMAL_CELL_CODE"]);
                                    //    return bResult;
                                    //}
                                    #endregion


                                }
                            }
                        }

                    }

                    //锁定货位状态
                    SiaSun.LMS.Model.WH_CELL mEND_WH_CELL = this._P_WH_CELL.GetModel(END_CELL_ID);
                    mEND_WH_CELL.RUN_STATUS = Enum.RUN_STATUS.Selected.ToString();
                    _P_WH_CELL.Update(mEND_WH_CELL);
                }
                catch (Exception ex)
                {
                    bResult = false;

                    sResult = ex.Message;

                    this._log.Fatal(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);
                }
            }

            return bResult;
        }

        ///<summary>
        ///@mxh add 2025-04-08 14:09
        /// 入库双深货位分配
        /// 此入库货位分配方法，仅限于三期双深立体库（三楼）使用，
        /// 使用情景仅限于5楼齐套箱入库至3楼以及原材料箱到3楼立库中
        /// ！！！需适配原材料箱流程！！！
        /// V_WH_CELL_DOUBLE_TO_NORMAL视图仅包含三楼双深立体库的货位
        /// </summary>
        public bool CellInAllocate_ThirdFloor_4save(int WAREHOUSE_ID,
                                      int START_CELL_ID,
                                      string GOODS_PROPERTY6,
                                      string GOODS_PROPERTY7,
                                      string GOODS_PROPERTY8,
                                      out int END_CELL_ID,
                                      out string sResult)
        {
            sResult = string.Empty;
            bool bResult = false;
            END_CELL_ID = 0;
            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mSTART_WH_CELL = null;

            lock (lockObj)
            {
                try
                {
                    mSTART_WH_CELL = this._P_WH_CELL.GetModel(START_CELL_ID);

                    if (mSTART_WH_CELL == null)
                    {
                        bResult = false;
                        sResult = string.Format("起始位置索引[{0}]不存在", START_CELL_ID);
                        return bResult;
                    }

                    //根据拣选单号获取现有绑定同拣选单号货位组
                    WH_CELL_GROUP mWH_CELL_GROUP = this._P_WH_CELL_GROUP.GetModelByPickNo(GOODS_PROPERTY6);

                    if (mWH_CELL_GROUP != null && mWH_CELL_GROUP.AVAIL_LOC_NUM > 0)
                    {
                        //AssignWholeCellSlot 分配整存货位组内货位
                        bResult = AssignCellGroupSlot(mWH_CELL_GROUP, false, GOODS_PROPERTY6, out END_CELL_ID, out sResult);
                    }
                    else
                    {
                        //原料箱/倒库
                        bool isMove = (mSTART_WH_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString() && mSTART_WH_CELL.AREA_ID == 4); //倒库
                        bool isMaterial = string.IsNullOrEmpty(GOODS_PROPERTY6); //原料箱
                        //判断拣选单不在三楼的库存数量是否为1
                        string pickStorageNum = $"SELECT COUNT(0) AS NUM FROM (SELECT DISTINCT(STORAGE_ID) FROM V_STORAGE_LIST WHERE GOODS_PROPERTY6 = '{GOODS_PROPERTY6}' AND AREA_ID != 4)";
                        DataTable dtPickStorageNum = GetList(pickStorageNum);
                        if (isMove || isMaterial || (dtPickStorageNum != null && dtPickStorageNum.Rows.Count > 0 && dtPickStorageNum.Rows[0]["NUM"].ToString().Equals("1")))
                        {
                            //混放流程
                            //判断是否还有可用的混放货位组(需要结合是否允许入3号货位一起判断)
                            int mixedAvailThreshold = 2;
                            int mixWhCellGroupID = 0;
                            string sqlAllowAllocateThirdSlot = $"SELECT PARAMETER_VALUE FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'AllowAllocateThirdSlot'";
                            DataTable dtAllowAllocateThirdSlot = GetList(sqlAllowAllocateThirdSlot);
                            if (dtAllowAllocateThirdSlot != null && dtAllowAllocateThirdSlot.Rows.Count > 0 && "Yes" == dtAllowAllocateThirdSlot.Rows[0]["PARAMETER_VALUE"].ToString())
                            {
                                mixedAvailThreshold = 1;
                            }

                            string sqlGetAvailMixedCellGroup = $"SELECT GROUP_ID FROM WH_CELL_GROUP WHERE AVAIL_LOC_NUM >= {mixedAvailThreshold} AND GROUP_STATUS = 'Mixed'";
                            DataTable dtGetAvailMixedCellGroup = GetList(sqlGetAvailMixedCellGroup);
                            if (dtGetAvailMixedCellGroup != null && dtGetAvailMixedCellGroup.Rows.Count > 0)
                            {
                                mixWhCellGroupID = Convert.ToInt32(dtGetAvailMixedCellGroup.Rows[0]["GROUP_ID"].ToString());
                            }

                            //GetAvailMixedCellGroup != null
                            if (mixWhCellGroupID != 0)
                            {
                                //存在可用混放货位组
                                //AssignMixedCellSlot 分配混放货位组内货位
                                bResult = AssignCellGroupSlot(_P_WH_CELL_GROUP.GetModel(mixWhCellGroupID), false, GOODS_PROPERTY6, out END_CELL_ID, out sResult);

                                //if (!bResult)
                                //{
                                //    //不存在可用混放货位组,新分配混放货位组内货位
                                //    bResult = AllocateNewCellGroupAndAssignSlot("Mixed", GOODS_PROPERTY6, mixedAvailThreshold == 1, out END_CELL_ID, out sResult);
                                //}
                            }
                            else
                            {
                                //不存在可用混放货位组,新分配混放货位组内货位
                                bResult = AllocateNewCellGroupAndAssignSlot("Mixed", GOODS_PROPERTY6, mixedAvailThreshold == 1, out END_CELL_ID, out sResult);
                            }
                        }
                        else
                        {
                            int wholeWhCellGroupID = 0;
                            //整存流程
                            //判断是否有剩余2货位的整存货位组 getCellGroupsByAvailSlots(2, Whole)
                            string sqlGetAvailWholeCellGroup = $"SELECT GROUP_ID FROM WH_CELL_GROUP WHERE AVAIL_LOC_NUM = 2 AND GROUP_STATUS = 'Whole'";
                            DataTable dtGetAvailWholeCellGroup = GetList(sqlGetAvailWholeCellGroup);
                            if (dtGetAvailWholeCellGroup != null && dtGetAvailWholeCellGroup.Rows.Count > 0)
                            {
                                wholeWhCellGroupID = Convert.ToInt32(dtGetAvailWholeCellGroup.Rows[0]["GROUP_ID"].ToString());
                            }
                            if (wholeWhCellGroupID != 0)
                            {
                                //AssignWholeCellSlot 分配整存货位组内货位
                                bResult = AssignCellGroupSlot(_P_WH_CELL_GROUP.GetModel(wholeWhCellGroupID), false, GOODS_PROPERTY6, out END_CELL_ID, out sResult);

                                //if (!bResult)
                                //{
                                //    //不存在可用整存货位组,新分配整存货位组内货位
                                //    bResult = AllocateNewCellGroupAndAssignSlot("Whole", GOODS_PROPERTY6, false, out END_CELL_ID, out sResult);
                                //}
                            }
                            else
                            {
                                //不存在可用整存货位组,新分配整存货位组内货位
                                bResult = AllocateNewCellGroupAndAssignSlot("Whole", GOODS_PROPERTY6, false, out END_CELL_ID, out sResult);
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    bResult = false;

                    sResult = ex.Message;

                    this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);

                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"CellInAllocate_ThirdFloor-{ex.Message}-{ex.StackTrace}-{ex.InnerException}");

                }
            }

            return bResult;
        }

        ///<summary>
        ///@mxh add 2025-04-08 14:09
        /// 入库双深货位分配
        /// 此入库货位分配方法，仅限于三期双深立体库（三楼）使用，
        /// 使用情景仅限于5楼齐套箱入库至3楼以及原材料箱到3楼立库中
        /// ！！！需适配原材料箱流程！！！
        /// V_WH_CELL_DOUBLE_TO_NORMAL视图仅包含三楼双深立体库的货位
        /// </summary>
        public bool CellInAllocate_ThirdFloor(int WAREHOUSE_ID,
                                      int START_CELL_ID,
                                      string GOODS_PROPERTY6,
                                      string GOODS_PROPERTY7,
                                      string GOODS_PROPERTY8,
                                      out int END_CELL_ID,
                                      out string sResult)
        {
            sResult = string.Empty;
            bool bResult = true;
            END_CELL_ID = 0;
            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mSTART_WH_CELL = null;

            lock (lockObj)
            {
                try
                {
                    mSTART_WH_CELL = this._P_WH_CELL.GetModel(START_CELL_ID);

                    if (mSTART_WH_CELL == null)
                    {
                        bResult = false;
                        sResult = string.Format("起始位置索引[{0}]不存在", START_CELL_ID);
                        return bResult;
                    }

                    bool isMove = false;
                    if (mSTART_WH_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                    {
                        isMove = true;
                    }

                    //根据拣选单号获取现有绑定同拣选单号货位组
                    int groupID = -1;
                    string sql4WhCellGroup = $"SELECT GROUP_ID FROM V_WH_CELL_GROUP WHERE GROUP_TYPE = 'Open' AND AVAIL_LOC_NUM > 0 AND PICK_NO = '{GOODS_PROPERTY6}'";
                    DataTable dtWhCellGroup = GetList(sql4WhCellGroup);
                    if (dtWhCellGroup == null && dtWhCellGroup.Rows.Count > 0)
                    {
                        groupID = Convert.ToInt32(dtWhCellGroup.Rows[0]["GROUP_ID"].ToString());
                    }

                    WH_CELL_GROUP mWH_CELL_GROUP = this._P_WH_CELL_GROUP.GetModel(groupID);

                    if (mWH_CELL_GROUP != null)
                    {
                        //AssignWholeCellSlot 分配货位组内货位
                        bResult = AssignCellGroupSlot(mWH_CELL_GROUP, isMove, GOODS_PROPERTY6, out END_CELL_ID, out sResult);
                    }
                    else
                    {
                        //尝试开辟新货位组
                        bResult = AllocateNewCellGroupAndAssignSlot("", GOODS_PROPERTY6, false, out END_CELL_ID, out sResult);

                        //开辟新货位组失败，尝试在现有已用货位组内分配货位
                        if (!bResult)
                        {
                            //判断是否还有可用的混放货位组(需要结合是否允许入3号货位一起判断)
                            int mixedAvailThreshold = 2;
                            int mixWhCellGroupID = 0;
                            string sqlAllowAllocateThirdSlot = $"SELECT PARAMETER_VALUE FROM SYS_PARAMETER WHERE PARAMETER_KEY = 'AllowAllocateThirdSlot'";
                            DataTable dtAllowAllocateThirdSlot = GetList(sqlAllowAllocateThirdSlot);
                            if (dtAllowAllocateThirdSlot != null && dtAllowAllocateThirdSlot.Rows.Count > 0 && "Yes" == dtAllowAllocateThirdSlot.Rows[0]["PARAMETER_VALUE"].ToString())
                            {
                                mixedAvailThreshold = 1;
                            }

                            string sqlGetAvailMixedCellGroup = $"SELECT GROUP_ID FROM V_WH_CELL_GROUP WHERE GROUP_TYPE = 'Open' AND AVAIL_LOC_NUM >= {mixedAvailThreshold}";
                            DataTable dtGetAvailMixedCellGroup = GetList(sqlGetAvailMixedCellGroup);
                            if (dtGetAvailMixedCellGroup != null && dtGetAvailMixedCellGroup.Rows.Count > 0)
                            {
                                mixWhCellGroupID = Convert.ToInt32(dtGetAvailMixedCellGroup.Rows[0]["GROUP_ID"].ToString());
                            }

                            if (mixWhCellGroupID != 0)
                            {
                                //存在可用混放货位组,AssignMixedCellSlot 分配混放货位组内货位
                                bResult = AssignCellGroupSlot(_P_WH_CELL_GROUP.GetModel(mixWhCellGroupID), isMove, GOODS_PROPERTY6, out END_CELL_ID, out sResult);
                            }
                            else
                            {
                                bResult = false;
                                sResult = $"未找到可用货位";
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    bResult = false;

                    sResult = ex.Message;

                    this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);

                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"CellInAllocate_ThirdFloor-{ex.Message}-{ex.StackTrace}-{ex.InnerException}");

                }
            }

            return bResult;
        }

        /// <summary>
        /// 开辟新货位组并分配其中货位
        /// </summary>
        /// <param name="cellGroupStatus"></param>
        /// <param name="pickNo"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool AllocateNewCellGroupAndAssignSlot(string cellGroupStatus, string pickNo, bool AllowAllocateThirdSlot, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            END_CELL_ID = 0;
            sResult = string.Empty;

            try
            {
                ///this._P_Base_House.BeginTransaction();
                //寻找新货位组需要增加排除预留货位组条件
                int groupID = -1;
                string sql4WH_CELL_GROUP = $"SELECT GROUP_ID FROM V_WH_CELL_GROUP WHERE GROUP_TYPE = 'Open' AND AVAIL_LOC_NUM = 4";
                DataTable dtWhCellGroup = GetList(sql4WH_CELL_GROUP);
                if (dtWhCellGroup != null && dtWhCellGroup.Rows.Count > 0)
                {
                    groupID = Convert.ToInt32(dtWhCellGroup.Rows[0]["GROUP_ID"].ToString());
                }
                WH_CELL_GROUP mWH_CELL_GROUP = _P_WH_CELL_GROUP.GetModel(groupID);
                if (mWH_CELL_GROUP != null)
                {
                    WH_CELL mEND_WH_CELL = _P_WH_CELL.GetModel($"51-{mWH_CELL_GROUP.GROUP_CODE}");    //开辟新货位组优先存放到51排
                    if (mEND_WH_CELL != null && mEND_WH_CELL.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString() && mEND_WH_CELL.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString())
                    {
                        //锁定货位状态
                        mEND_WH_CELL.RUN_STATUS = Enum.RUN_STATUS.Selected.ToString();
                        _P_WH_CELL.Update(mEND_WH_CELL);

                        END_CELL_ID = mEND_WH_CELL.CELL_ID;

                        //更新货位组状态
                        if ("Mixed" != cellGroupStatus) { mWH_CELL_GROUP.PICK_NO = pickNo; }
                        mWH_CELL_GROUP.AVAIL_LOC_NUM = 3;
                        mWH_CELL_GROUP.GROUP_STATUS = cellGroupStatus;
                        _P_WH_CELL_GROUP.Update(mWH_CELL_GROUP);
                    }
                    else
                    {
                        bResult = false;
                        sResult = $"AllocateNewCellGroupAndAssignSlot 51-{mWH_CELL_GROUP.GROUP_CODE}货位状态异常";
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AllocateNewCellGroupAndAssignSlot-{sResult}");
                    }
                }
                else
                {
                    bResult = false;
                    sResult = $"三楼线边仓空货位组分配失败！";
                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AllocateNewCellGroupAndAssignSlot-{sResult}");
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);

                this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AllocateNewCellGroupAndAssignSlot-{ex.Message}-{ex.StackTrace}-{ex.InnerException}");
            }

            return bResult;
        }

        /// <summary>
        /// 在已使用货位组中分配货位
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool AssignCellGroupSlot_4save(WH_CELL_GROUP mWH_CELL_GROUP, string pickNo, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            END_CELL_ID = 0;
            sResult = string.Empty;

            try
            {
                bool isMixed = "Mixed" == mWH_CELL_GROUP.GROUP_STATUS;
                int availLocNum = mWH_CELL_GROUP.AVAIL_LOC_NUM;
                int endZ = 0;
                WH_CELL cell1 = _P_WH_CELL.GetModel($"51-{mWH_CELL_GROUP.GROUP_CODE}"); //1号位
                WH_CELL cell2 = _P_WH_CELL.GetModel($"52-{mWH_CELL_GROUP.GROUP_CODE}"); //2号位
                WH_CELL cell3 = _P_WH_CELL.GetModel($"53-{mWH_CELL_GROUP.GROUP_CODE}"); //3号位
                WH_CELL cell4 = _P_WH_CELL.GetModel($"54-{mWH_CELL_GROUP.GROUP_CODE}"); //4号位
                if (cell1 == null || cell2 == null || cell3 == null || cell4 == null)
                {
                    bResult = false;
                    sResult = $"AssignCellGroupSlot-查找{mWH_CELL_GROUP.GROUP_CODE}货位失败";
                    return bResult;
                }

                switch (availLocNum)
                {
                    case 4:
                        if (cell1.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString() && cell1.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                        {
                            endZ = 51;
                        }
                        break;
                    case 3:
                        //看哪边有货放哪边

                        //1号位有货&&空闲
                        if (cell1.CELL_STATUS == Enum.CELL_STATUS.Full.ToString() && cell1.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                        {
                            endZ = 52;
                        }
                        //1号位无货，4号位有货&&空闲
                        else if (cell4.CELL_STATUS == Enum.CELL_STATUS.Full.ToString() && cell4.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                        {
                            endZ = 53;
                        }
                        else
                        {
                            //重新分配货位，在上一层处理返回失败的情况
                            bResult = false;
                            sResult = $"AssignCellGroupSlot-case3尝试分配货位组{mWH_CELL_GROUP.GROUP_CODE}时错误,可能存在远深无货近深有货情况";
                            this._log.Debug(sResult);
                            return bResult;
                        }
                        break;
                    case 2:
                        //1号位空&&空闲
                        if (cell1.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString() && cell1.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                        {
                            endZ = 51;
                        }
                        //1号位有货
                        else
                        {
                            //1号位有货&&空闲 && 2号位空&&空闲
                            if (cell1.CELL_STATUS == Enum.CELL_STATUS.Full.ToString() && cell1.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString()
                                && cell2.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString() && cell2.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                            {
                                if (!isMixed)
                                {
                                    //整存模式需要对比1号位、4号位哪个拣选单号和待入库的一致
                                    STORAGE_MAIN storageMain1 = _P_STORAGE_MAIN.GetModelCellID(cell1.CELL_ID);
                                    STORAGE_MAIN storageMain4 = _P_STORAGE_MAIN.GetModelCellID(cell4.CELL_ID);
                                    if (storageMain1 != null && storageMain4 != null)
                                    {
                                        IList<STORAGE_LIST> storageList1s = _P_STORAGE_LIST.GetListStorageID(storageMain1.STORAGE_ID);
                                        IList<STORAGE_LIST> storageList4s = _P_STORAGE_LIST.GetListStorageID(storageMain4.STORAGE_ID);

                                        if (storageList1s != null && storageList4s != null)
                                        {
                                            if (pickNo == storageList1s[0].GOODS_PROPERTY6)
                                            {
                                                endZ = 52;
                                            }
                                            else if (pickNo == storageList4s[0].GOODS_PROPERTY6)
                                            {
                                                endZ = 53;
                                            }
                                            else
                                            {
                                                endZ = 52; //托底
                                            }
                                        }
                                    }
                                }

                                endZ = 52;
                            }
                            else
                            {
                                endZ = 54;
                            }
                        }

                        break;
                    case 1:
                        //判断货位组类型，预留货位组不进入下面逻辑
                        if ("Open" == mWH_CELL_GROUP.GROUP_TYPE)
                        {
                            //在2、3号空里取
                            if (cell2.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString() && cell2.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString())
                            {
                                endZ = 52;
                            }
                            else
                            {
                                endZ = 53;
                            }

                        }
                        else
                        {
                            bResult = false;
                            sResult = $"AssignCellGroupSlot-分配到预留货位组";
                            this._log.Debug(sResult);
                            return bResult;
                        }
                        break;
                    default:
                        break;
                }

                WH_CELL mEND_WH_CELL = _P_WH_CELL.GetModel($"{endZ}-{mWH_CELL_GROUP.GROUP_CODE}");
                string cellCode = $"{endZ}-{mWH_CELL_GROUP.GROUP_CODE}";
                this._log.Debug($"{endZ}-{mWH_CELL_GROUP.GROUP_CODE}");
                if (mEND_WH_CELL != null && mEND_WH_CELL.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString() && mEND_WH_CELL.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString())
                {
                    //锁定货位状态
                    mEND_WH_CELL.RUN_STATUS = Enum.RUN_STATUS.Selected.ToString();
                    _P_WH_CELL.Update(mEND_WH_CELL);

                    END_CELL_ID = mEND_WH_CELL.CELL_ID;

                    //更新货位组状态
                    mWH_CELL_GROUP.AVAIL_LOC_NUM -= 1;
                    mWH_CELL_GROUP.PICK_NO = pickNo;
                    int refCount = _P_WH_CELL_GROUP.Update(mWH_CELL_GROUP);
                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AssignCellGroupSlot-{mEND_WH_CELL.CELL_CODE}-{mWH_CELL_GROUP.GROUP_CODE}-{refCount}");
                }
                else
                {
                    bResult = false;
                    sResult = $"AssignCellGroupSlot {cellCode}货位状态异常";
                    this._log.Debug(sResult);
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);

                this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AssignCellGroupSlot-{ex.Message}-{ex.StackTrace}-{ex.InnerException}");
            }

            return bResult;
        }

        /// <summary>
        /// 在已使用货位组中分配货位
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool AssignCellGroupSlot(WH_CELL_GROUP mWH_CELL_GROUP, bool isMove, string pickNo, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            END_CELL_ID = 0;
            sResult = string.Empty;

            try
            {
                bool isMixed = "Mixed" == mWH_CELL_GROUP.GROUP_STATUS;
                int availLocNum = mWH_CELL_GROUP.AVAIL_LOC_NUM;
                int endZ = 0;
                WH_CELL cell1 = _P_WH_CELL.GetModel($"51-{mWH_CELL_GROUP.GROUP_CODE}"); //1号位
                WH_CELL cell2 = _P_WH_CELL.GetModel($"52-{mWH_CELL_GROUP.GROUP_CODE}"); //2号位
                WH_CELL cell3 = _P_WH_CELL.GetModel($"53-{mWH_CELL_GROUP.GROUP_CODE}"); //3号位
                WH_CELL cell4 = _P_WH_CELL.GetModel($"54-{mWH_CELL_GROUP.GROUP_CODE}"); //4号位
                if (cell1 == null || cell2 == null || cell3 == null || cell4 == null)
                {
                    bResult = false;
                    sResult = $"AssignCellGroupSlot-查找{mWH_CELL_GROUP.GROUP_CODE}货位失败";
                    return bResult;
                }

                int endCellID = -1;
                string sql4endCell = $"SELECT CELL_ID, CELL_CODE, " +
                                    $"ABS(CELL_Z - (SELECT TMP_CELL_Z FROM (SELECT CELL_CODE, SUBSTR(CELL_CODE, 1, 2) TMP_CELL_Z FROM V_STORAGE_LIST WHERE SUBSTR(V_STORAGE_LIST.CELL_CODE,4,5) = '{mWH_CELL_GROUP.GROUP_CODE}' AND GOODS_PROPERTY6 = '{pickNo}') WHERE ROWNUM = 1)) DISTANCE, " +
                                    $"DECODE(CELL_Z, 51, 1, 54, 2, 52, 3, 53, 4, 100) SEQ " +
                                    $"FROM WH_CELL " +
                                    $"WHERE " +
                                    $"AREA_ID = 4 " +
                                    $"AND RUN_STATUS = 'Enable' " +
                                    $"AND CELL_STATUS = 'Nohave' " +
                                    $"AND SUBSTR(WH_CELL.CELL_CODE,4,5 ) = '{mWH_CELL_GROUP.GROUP_CODE}'" +
                                    $"AND ((shelf_type = 'Double' AND NOT EXISTS (SELECT 1 FROM V_STORAGE_LIST s WHERE s.CELL_CODE = WH_CELL.CELL_GROUP AND s.CELL_CODE IS NOT NULL)) OR (shelf_type = 'Normal' AND NOT EXISTS (SELECT 1 FROM v_manage WHERE start_position = WH_CELL.cell_group AND start_position IS NOT NULL)))" +
                                    $" ORDER BY DISTANCE, SEQ";
                if (isMove)
                {
                    sql4endCell = $"SELECT CELL_ID, CELL_CODE, " +
                                    $"DECODE(CELL_Z, 51, 1, 54, 2, 52, 3, 53, 4, 100) SEQ " +
                                    $"FROM WH_CELL " +
                                    $"WHERE " +
                                    $"AREA_ID = 4 " +
                                    $"AND RUN_STATUS = 'Enable' " +
                                    $"AND CELL_STATUS = 'Nohave' " +
                                    $"AND SUBSTR(WH_CELL.CELL_CODE,4,5 ) = '{mWH_CELL_GROUP.GROUP_CODE}'" +
                                    $"AND ((shelf_type = 'Double' AND NOT EXISTS (SELECT 1 FROM V_STORAGE_LIST s WHERE s.CELL_CODE = WH_CELL.CELL_GROUP AND s.CELL_CODE IS NOT NULL)) OR (shelf_type = 'Normal' AND NOT EXISTS (SELECT 1 FROM v_manage WHERE (start_position = WH_CELL.cell_group AND start_position IS NOT NULL) OR (end_position = WH_CELL.cell_group AND end_position IS NOT NULL))))" +
                                    $" ORDER BY SEQ";
                }
                DataTable dtEndCell = GetList(sql4endCell);
                if (dtEndCell != null && dtEndCell.Rows.Count > 0)
                {
                    endCellID = Convert.ToInt32(dtEndCell.Rows[0]["CELL_ID"].ToString());

                    string cellCode = dtEndCell.Rows[0]["CELL_CODE"].ToString();

                    WH_CELL mEND_WH_CELL = _P_WH_CELL.GetModel(endCellID);

                    if (mEND_WH_CELL != null && mEND_WH_CELL.RUN_STATUS == Enum.RUN_STATUS.Enable.ToString() && mEND_WH_CELL.CELL_STATUS == Enum.CELL_STATUS.Nohave.ToString())
                    {
                        //锁定货位状态
                        mEND_WH_CELL.RUN_STATUS = Enum.RUN_STATUS.Selected.ToString();
                        _P_WH_CELL.Update(mEND_WH_CELL);

                        END_CELL_ID = mEND_WH_CELL.CELL_ID;

                        //更新货位组状态
                        mWH_CELL_GROUP.AVAIL_LOC_NUM -= 1;
                        mWH_CELL_GROUP.PICK_NO = pickNo;
                        int refCount = _P_WH_CELL_GROUP.Update(mWH_CELL_GROUP);
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AssignCellGroupSlot-{mEND_WH_CELL.CELL_CODE}-{mWH_CELL_GROUP.GROUP_CODE}-{refCount}");
                    }
                    else
                    {
                        bResult = false;
                        sResult = $"AssignCellGroupSlot {cellCode}货位状态异常 CELL_STATUS-{mEND_WH_CELL.CELL_STATUS} RUN_STATUS-{mEND_WH_CELL.RUN_STATUS}";
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AssignCellGroupSlot-{sResult}");
                    }
                }
                else
                {
                    bResult = false;
                    sResult = $"AssignCellGroupSlot 为找到{mWH_CELL_GROUP.GROUP_CODE}组内可用货位";
                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AssignCellGroupSlot-{sResult}");
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);

                this.CreateSysLog(Enum.LogThread.Control, "System", bResult, $"AssignCellGroupSlot-{ex.Message}-{ex.StackTrace}-{ex.InnerException}");
            }

            return bResult;
        }

        /// <summary>
        /// 在货位组内倒库
        /// </summary>
        /// <param name="mWH_CELL_GROUP"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool RelocateInGroup_4save(WH_CELL mWH_CELL_START, WH_CELL_GROUP mWH_CELL_GROUP, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            END_CELL_ID = -1;
            sResult = string.Empty;

            try
            {
                //1. 2号货位倒库
                if (mWH_CELL_START.CELL_Z == 52)
                {
                    //1.检查4号货位
                    WH_CELL mWH_CELL4 = _P_WH_CELL.GetModel($"54-{mWH_CELL_GROUP.GROUP_CODE}");
                    if (mWH_CELL4 != null && mWH_CELL4.CELL_STATUS.Equals(Enum.CELL_STATUS.Nohave.ToString()) && mWH_CELL4.RUN_STATUS.Equals(Enum.RUN_STATUS.Enable.ToString()))
                    {
                        END_CELL_ID = mWH_CELL4.CELL_ID;
                        return bResult = true;
                    }

                    //2.检查3号货位
                    WH_CELL mWH_CELL3 = _P_WH_CELL.GetModel($"53-{mWH_CELL_GROUP.GROUP_CODE}");
                    if (mWH_CELL3 != null && mWH_CELL3.CELL_STATUS.Equals(Enum.CELL_STATUS.Nohave.ToString()) && mWH_CELL3.RUN_STATUS.Equals(Enum.RUN_STATUS.Enable.ToString()))
                    {
                        END_CELL_ID = mWH_CELL3.CELL_ID;
                        return bResult = true;
                    }

                    //3.组内无可用货位
                    string sqlStorage = $"select GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8 from V_STORAGE_LIST where cell_id = {mWH_CELL_START.CELL_ID} ";
                    DataTable dtStorage = GetList(sqlStorage);
                    if (dtStorage != null && dtStorage.Rows.Count > 0)
                    {
                        string GOODS_PROPERTY6 = dtStorage.Rows[0]["GOODS_PROPERTY6"].ToString();
                        string GOODS_PROPERTY7 = dtStorage.Rows[0]["GOODS_PROPERTY7"].ToString();
                        string GOODS_PROPERTY8 = dtStorage.Rows[0]["GOODS_PROPERTY8"].ToString();
                        CellInAllocate_ThirdFloor(3, mWH_CELL_START.CELL_ID, GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8, out END_CELL_ID, out sResult);
                    }
                    else
                    {
                        sResult = $"倒库时未找到{mWH_CELL_START.CELL_CODE}库存";
                        this._log.Debug(sResult);
                        return bResult = false;
                    }

                }

                //2. 3号货位倒库
                if (mWH_CELL_START.CELL_Z == 53)
                {
                    //1.检查1号货位
                    WH_CELL mWH_CELL1 = _P_WH_CELL.GetModel($"51-{mWH_CELL_GROUP.GROUP_CODE}");
                    if (mWH_CELL1 != null && mWH_CELL1.CELL_STATUS.Equals(Enum.CELL_STATUS.Nohave.ToString()) && mWH_CELL1.RUN_STATUS.Equals(Enum.RUN_STATUS.Enable.ToString()))
                    {
                        END_CELL_ID = mWH_CELL1.CELL_ID;
                        return bResult = true;
                    }

                    //2.检查2号货位
                    WH_CELL mWH_CELL2 = _P_WH_CELL.GetModel($"52-{mWH_CELL_GROUP.GROUP_CODE}");
                    if (mWH_CELL2 != null && mWH_CELL2.CELL_STATUS.Equals(Enum.CELL_STATUS.Nohave.ToString()) && mWH_CELL2.RUN_STATUS.Equals(Enum.RUN_STATUS.Enable.ToString()))
                    {
                        END_CELL_ID = mWH_CELL2.CELL_ID;
                        return bResult = true;
                    }

                    //3.组内无可用货位
                    string sqlStorage = $"select GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8 from V_STORAGE_LIST where cell_id = {mWH_CELL_START.CELL_ID} ";
                    DataTable dtStorage = GetList(sqlStorage);
                    if (dtStorage != null && dtStorage.Rows.Count > 0)
                    {
                        string GOODS_PROPERTY6 = dtStorage.Rows[0]["GOODS_PROPERTY6"].ToString();
                        string GOODS_PROPERTY7 = dtStorage.Rows[0]["GOODS_PROPERTY7"].ToString();
                        string GOODS_PROPERTY8 = dtStorage.Rows[0]["GOODS_PROPERTY8"].ToString();
                        CellInAllocate_ThirdFloor(3, mWH_CELL_START.CELL_ID, GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8, out END_CELL_ID, out sResult);
                    }
                    else
                    {
                        sResult = $"倒库时未找到{mWH_CELL_START.CELL_CODE}库存";
                        this._log.Debug(sResult);
                        return bResult = false;
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);
            }

            return bResult;
        }
        /// <summary>
        /// 在货位组内倒库
        /// </summary>
        /// <param name="mWH_CELL_GROUP"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool RelocateInGroup(WH_CELL mWH_CELL_START, WH_CELL_GROUP mWH_CELL_GROUP, out int END_CELL_ID, out string sResult)
        {
            bool bResult = true;
            END_CELL_ID = -1;
            sResult = string.Empty;

            try
            {
                //获取起始货位库存拣选单号等信息
                string pickNo = string.Empty;
                string GOODS_PROPERTY6 = string.Empty;
                string GOODS_PROPERTY7 = string.Empty;
                string GOODS_PROPERTY8 = string.Empty;
                string sqlStorage = $"select GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8 from V_STORAGE_LIST where cell_id = {mWH_CELL_START.CELL_ID} ";
                DataTable dtStorage = GetList(sqlStorage);
                if (dtStorage != null && dtStorage.Rows.Count > 0)
                {
                    GOODS_PROPERTY6 = dtStorage.Rows[0]["GOODS_PROPERTY6"].ToString();
                    GOODS_PROPERTY7 = dtStorage.Rows[0]["GOODS_PROPERTY7"].ToString();
                    GOODS_PROPERTY8 = dtStorage.Rows[0]["GOODS_PROPERTY8"].ToString();

                    pickNo = GOODS_PROPERTY6;
                }
                else
                {
                    sResult = $"倒库时未找到{mWH_CELL_START.CELL_CODE}库存";
                    this._log.Debug(sResult);
                    return bResult = false;
                }

                //在货位组内分配货位
                bResult = AssignCellGroupSlot(mWH_CELL_GROUP, true, pickNo, out END_CELL_ID, out sResult);
                if (!bResult)
                {
                    bResult = CellInAllocate_ThirdFloor(3, mWH_CELL_START.CELL_ID, GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8, out END_CELL_ID, out sResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this._log.Error(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);
            }

            return bResult;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="LANE_WAY"></param>
        /// <param name="eOUT_RULES"></param>
        /// <param name="sGOODS_PROPERTY_CONDITION"></param>
        /// <param name="MANAGE_LIST_QUANTITY"></param>
        /// <param name="END_CELL_ID"></param>
        /// <param name="OutPara"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool CellOutAllocate(int WAREHOUSE_ID,
                       int AREA_ID,
                       int LANE_WAY,
                       string sGOODS_PROPERTY_CONDITION,
                       int END_CELL_ID,
                       out int CELL_ID,
                       out string sResult)
        {
            bool bResult = false;

            sResult = string.Empty;

            CELL_ID = 0;

            SiaSun.LMS.Model.WH_CELL mEND_WH_CELL = null;

            try
            {
                mEND_WH_CELL = this._P_WH_CELL.GetModel(END_CELL_ID);

                if (mEND_WH_CELL == null)
                {
                    bResult = false;

                    sResult = string.Format("起始位置索引[{0}]不存在", mEND_WH_CELL);

                    CELL_ID = 0;

                    return bResult;
                }

                #region 步骤1，Double空，Normal有货且与下架物料一致，选择Normal实盘货位
                string strCell = string.Format(@"SELECT NORMAL_CELL_ID AS CELL_ID
                                                                  FROM V_WH_CELL_DOUBLE_TO_NORMAL 
                                                                    WHERE 1=1 
                                                                            {0}
                                                                            {1}
                                                                            {2}
                                                                            {3}
                                                                            {4}
                                                                            {5}
                                                                            {6}
                                                                      ORDER BY MIN(SUBSTRING(NORMAL_ENTRY_TIME,1,10))
                                                                      FETCH NEXT 1 ROWS ONLY",
                        /*0.NORMAL货位类型为Cell*/string.Format(" AND NORMAL_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                        /*1.Normal有货*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                        /*2.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                        /*3.Double为空*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                        /*4.Normal库存为齐套箱*/string.Format(" AND NORMAL_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                        /*5.物料属性一致*/string.IsNullOrEmpty(sGOODS_PROPERTY_CONDITION) ? string.Empty : string.Format(" AND {0}", sGOODS_PROPERTY_CONDITION.Replace("TARGET_GOODS_PROPERTY", "NORMAL_GOODS_PROPERTY")),
                        /*6.Normal货位巷道号*/(LANE_WAY == 0) ? string.Empty : string.Format(" AND NORMAL_LANE_WAY={0}", mEND_WH_CELL.LANE_WAY));

                DataTable dtCell = this.GetList(strCell);


                #endregion
                bResult = dtCell.Rows.Count > 0;

                if (bResult)
                {
                    CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);
                }
                else
                {
                    #region 步骤2，Normal空无任务，Double有货无任务且与下架物料一致，选择Double实盘货位

                    strCell = string.Format(@"SELECT NORMAL_CELL_ID AS CELL_ID
                                                                  FROM V_WH_CELL_DOUBLE_TO_NORMAL 
                                                                    WHERE 1=1 
                                                                            {0}
                                                                            {1}
                                                                            {2}
                                                                            {3}
                                                                            {4}
                                                                            {5}
                                                                            {6}
                                                                            {7}
                                                                      ORDER BY MIN(SUBSTRING(DOUBLE_ENTRY_TIME,1,10))
                                                                      FETCH NEXT 1 ROWS ONLY",
                        /*0.DOUBLE货位类型为Cell*/string.Format(" AND DOUBLE_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                        /*1.Normal空*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                        /*2.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                        /*3.Double有货*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                        /*4.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                        /*5.Double库存为齐套箱*/string.Format(" AND DOUBLE_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                        /*6.物料属性一致*/string.IsNullOrEmpty(sGOODS_PROPERTY_CONDITION) ? string.Empty : string.Format(" AND {0}", sGOODS_PROPERTY_CONDITION.Replace("TARGET_GOODS_PROPERTY", "DOUBLE_GOODS_PROPERTY")),
                        /*7.Double货位巷道号*/(LANE_WAY == 0) ? string.Empty : string.Format(" AND DOUBLE_LANE_WAY={0}", mEND_WH_CELL.LANE_WAY));

                    dtCell = this.GetList(strCell);

                    #endregion
                    bResult = dtCell.Rows.Count > 0;

                    if (bResult)
                    {
                        CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);
                    }
                    else
                    {
                        #region 步骤3，Normal有货有任务，Double有货且与下架物料一致，选择Double实盘货位

                        strCell = string.Format(@"SELECT DOUBLE_CELL_ID AS CELL_ID
                                                                  FROM V_WH_CELL_DOUBLE_TO_NORMAL 
                                                                    WHERE 1=1 
                                                                            {0}
                                                                            {1}
                                                                            {2}
                                                                            {3}
                                                                            {4}
                                                                            {5}
                                                                            {6}
                                                                      ORDER BY MIN(SUBSTRING(DOUBLE_ENTRY_TIME,1,10))
                                                                      FETCH NEXT 1 ROWS ONLY",
                        /*0.DOUBLE货位类型为Cell*/string.Format(" AND DOUBLE_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                        /*1.Normal空*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Nohave.ToString()),
                        /*2.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                        /*3.Double有货*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                        /*4.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                        /*5.Double库存为齐套箱*/string.Format(" AND DOUBLE_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                        /*6.Double物料属性一致*/string.IsNullOrEmpty(sGOODS_PROPERTY_CONDITION) ? string.Empty : string.Format(" AND {0}", sGOODS_PROPERTY_CONDITION.Replace("TARGET_GOODS_PROPERTY", "DOUBLE_GOODS_PROPERTY")),
                        /*7.Double货位巷道号*/(LANE_WAY == 0) ? string.Empty : string.Format(" AND DOUBLE_LANE_WAY={0}", mEND_WH_CELL.LANE_WAY));

                        dtCell = this.GetList(strCell);

                        #endregion

                        bResult = dtCell.Rows.Count > 0;

                        if (bResult)
                        {
                            CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);
                        }
                        else
                        {
                            #region 步骤4，Normal有货无任务，且与下架物料一致，选择Normal实盘货位

                            strCell = string.Format(@"SELECT NORMAL_CELL_ID AS CELL_ID
                                                                  FROM V_WH_CELL_DOUBLE_TO_NORMAL 
                                                                    WHERE 1=1 
                                                                            {0}
                                                                            {1}
                                                                            {2}
                                                                            {3}
                                                                            {4}
                                                                            {5}
                                                                            {6}
                                                                      ORDER BY MIN(SUBSTRING(NORMAL_ENTRY_TIME,1,10))
                                                                      FETCH NEXT 1 ROWS ONLY",
                            /*0.Normal货位类型为Cell*/string.Format(" AND NORMAL_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                            /*1.Normal有货*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                            /*2.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                            /*5.Normal库存为齐套箱*/string.Format(" AND NORMAL_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                            /*6.Normal物料属性一致*/string.IsNullOrEmpty(sGOODS_PROPERTY_CONDITION) ? string.Empty : string.Format(" AND {0}", sGOODS_PROPERTY_CONDITION.Replace("TARGET_GOODS_PROPERTY", "NORMAL_GOODS_PROPERTY")),
                            /*7.Normal货位巷道号*/(LANE_WAY == 0) ? string.Empty : string.Format(" AND NORMAL_LANE_WAY={0}", mEND_WH_CELL.LANE_WAY));

                            dtCell = this.GetList(strCell);

                            #endregion

                            bResult = dtCell.Rows.Count > 0;

                            if (bResult)
                            {
                                CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);
                            }
                            else
                            {
                                #region 步骤5，Double有货无任务，且与下架物料一致，Normal有货无任务，且与下架物料不一致，选择Double实盘货位

                                strCell = string.Format(@"SELECT DOUBLE_CELL_ID AS CELL_ID
                                                                  FROM V_WH_CELL_DOUBLE_TO_NORMAL 
                                                                    WHERE 1=1 
                                                                            {0}
                                                                            {1}
                                                                            {2}
                                                                            {3}
                                                                            {4}
                                                                            {5}
                                                                            {6}
                                                                      ORDER BY MIN(SUBSTRING(DOUBLE_ENTRY_TIME,1,10))
                                                                      FETCH NEXT 1 ROWS ONLY",
                                /*0.DOUBLE货位类型为Cell*/string.Format(" AND DOUBLE_CELL_TYPE = '{0}'", Enum.CELL_TYPE.Cell),
                                /*1.Normal有货*/ string.Format(" AND NORMAL_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                                /*2.Normal无任务*/string.Format(" AND NORMAL_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                                /*5.Normal库存为齐套箱*/string.Format(" AND NORMAL_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                                /*6.Normal物料属性不一致*/string.IsNullOrEmpty(sGOODS_PROPERTY_CONDITION) ? string.Empty : string.Format(" AND {0}", sGOODS_PROPERTY_CONDITION.Replace("TARGET_GOODS_PROPERTY", "DOUBLE_GOODS_PROPERTY").Replace("=", "!=")),
                                /*3.Double有货*/string.Format(" AND DOUBLE_CELL_STATUS = '{0}'", Enum.CELL_STATUS.Full.ToString()),
                                /*4.Double无任务*/string.Format(" AND DOUBLE_RUN_STATUS = '{0}'", Enum.RUN_STATUS.Enable.ToString()),
                                /*5.Double库存为齐套箱*/string.Format(" AND DOUBLE_STORAGE_CELL_MODEL = '{0}'", Enum.CellModel.KitBox.ToString("d")),
                                /*6.Double物料属性一致*/string.IsNullOrEmpty(sGOODS_PROPERTY_CONDITION) ? string.Empty : string.Format(" AND {0}", sGOODS_PROPERTY_CONDITION.Replace("TARGET_GOODS_PROPERTY", "DOUBLE_GOODS_PROPERTY")),
                                /*7.Double货位巷道号*/(LANE_WAY == 0) ? string.Empty : string.Format(" AND DOUBLE_LANE_WAY={0}", mEND_WH_CELL.LANE_WAY));

                                dtCell = this.GetList(strCell);

                                #endregion

                                bResult = dtCell.Rows.Count > 0;

                                if (bResult)
                                {
                                    CELL_ID = Convert.ToInt32(dtCell.Rows[0]["CELL_ID"]);
                                }
                                else
                                {
                                    bResult = false;

                                    CELL_ID = 0;

                                    sResult = string.Format("未找到下架货位，请确认库存是否充足");

                                    return bResult;
                                }
                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._log.Fatal(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);
            }

            return bResult;
        }

        /// <summary>
        /// 双深货位，校验DOUBLE货位关联的NORMAL货位是否需要移库
        /// 有货、无任务则需要移库
        /// true-需移库
        /// false-不需移库
        /// 若返回结果为false，需要进一步查看Normal是否为无货、有任务，此时Double不允许下架
        /// </summary>
        /// <param name="DOUBLE_CELL_ID"></param>
        /// <param name="NORMAL_CELL_ID"></param>
        /// <param name="STOCK_BARCODE"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public void CheckNormalMove(int DOUBLE_CELL_ID, out int NORMAL_CELL_ID, out string STOCK_BARCODE, out STORAGE_MAIN mNORMAL_STORAGE_MAIN, out IList<STORAGE_LIST> lsNORMAL_STORAGE_LIST, out string sResult)
        {
            bool bMove = false;
            NORMAL_CELL_ID = 0;
            STOCK_BARCODE = string.Empty;
            int NORMAL_STORAGE_ID = 0;
            mNORMAL_STORAGE_MAIN = null;
            lsNORMAL_STORAGE_LIST = null;
            sResult = string.Empty;
            try
            {
                WH_CELL mWH_CELL_DOUBLE = _P_WH_CELL.GetModel(DOUBLE_CELL_ID);
                WH_CELL mWH_CELL_NORMAL = _P_WH_CELL.GetModel(mWH_CELL_DOUBLE.CELL_GROUP);
                //外侧有货
                if (mWH_CELL_NORMAL.CELL_STATUS.Equals(Enum.CELL_STATUS.Full.ToString()))
                {
                    string sqlOutTask = $"select count(0) as num from v_manage where start_cell_id = {mWH_CELL_NORMAL.CELL_ID}";
                    DataTable dtOutTask = this.GetList(sqlOutTask);
                    //无出库任务
                    if (dtOutTask != null && dtOutTask.Rows.Count > 0 && 0 == Convert.ToInt32(dtOutTask.Rows[0]["num"].ToString()))
                    {
                        //横向倒库
                        bMove = true;
                    }
                    //有出库任务
                    else
                    {
                        //不倒库
                    }
                }
                //外侧无货
                else
                {
                    string sqlInTask = $"select count(0) as num from v_manage where end_cell_id = {mWH_CELL_NORMAL.CELL_ID}";
                    DataTable dtInTask = this.GetList(sqlInTask);
                    //无入库任务
                    if (dtInTask != null && dtInTask.Rows.Count > 0 && 0 == Convert.ToInt32(dtInTask.Rows[0]["num"].ToString()))
                    {
                        ///不倒库
                    }
                    //有入库任务
                    else
                    {
                        //不能出库
                        sResult = "outerInbound";
                    }
                }

                if (bMove)
                {
                    string strNormal = string.Format(@"SELECT NORMAL_STORAGE_ID,NORMAL_CELL_ID,NORMAL_STOCK_BARCODE FROM V_WH_CELL_DOUBLE_TO_NORMAL 
                                                                            WHERE  NORMAL_CELL_STATUS!='{0}'
                                                                            AND NORMAL_RUN_STATUS='{1}'
                                                                            AND DOUBLE_CELL_ID='{2}'
                                                                            FETCH NEXT 1 ROWS ONLY",
                                                                                                   Enum.CELL_STATUS.Nohave.ToString(),
                                                                                                   Enum.RUN_STATUS.Enable.ToString(),
                                                                                                   DOUBLE_CELL_ID);
                    DataTable dtNormal = this.GetList(strNormal);
                    if (dtNormal.Rows.Count > 0)
                    {

                        NORMAL_CELL_ID = Convert.ToInt32(dtNormal.Rows[0]["NORMAL_CELL_ID"]);
                        STOCK_BARCODE = Convert.ToString(dtNormal.Rows[0]["NORMAL_STOCK_BARCODE"]);
                        NORMAL_STORAGE_ID = Convert.ToInt32(dtNormal.Rows[0]["NORMAL_STORAGE_ID"]);
                    }
                    mNORMAL_STORAGE_MAIN = _P_STORAGE_MAIN.GetModel(NORMAL_STORAGE_ID);
                    lsNORMAL_STORAGE_LIST = _P_STORAGE_LIST.GetListStorageID(NORMAL_STORAGE_ID);
                }
            }
            catch (Exception ex)
            {

                sResult = ex.Message;
                this._log.Fatal(string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);
            }

        }
    }
}
