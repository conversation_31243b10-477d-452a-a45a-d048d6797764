﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.ServiceModel;
using System.Drawing;
using System.IO;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
     InstanceContextMode = InstanceContextMode.Single,
     ConcurrencyMode = ConcurrencyMode.Multiple,
     MaxItemsInObjectGraph = int.MaxValue)]
    public class S_GoodsService : S_BaseService, SiaSun.LMS.Interface.I_GoodsService
    {
        public S_GoodsService()
        {
        }


        /// <summary> 
        /// 
        /// </summary>
        /// <param name="GOODS_ID">物料编号</param>
        /// <returns></returns>
        public SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsID(int GOODS_ID)
        {
            return this._P_GOODS_MAIN.GetModel(GOODS_ID);
        }

        public SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCode(string GOODS_CODE)
        {
            return this._P_GOODS_MAIN.GetModel(GOODS_CODE);
        }

        public SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCodeContract(string GOODS_CODE, string CONTRACT)
        {
            return this._P_GOODS_MAIN.GetModel(GOODS_CODE, CONTRACT);
        }

        public SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassCode(string GOODS_CLASS_CODE)
        {
            return this._P_GOODS_CLASS.GetModel(GOODS_CLASS_CODE);
        }
        /// <summary> 
        /// 
        /// </summary>
        /// <param name="GOODS_ID">物料编号</param>
        /// <returns></returns>
        public SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassID(int GOODS_CLASS_ID)
        {
            return this._P_GOODS_CLASS.GetModel(GOODS_CLASS_ID);
        }



        /// <summary> 
        /// 
        /// </summary>
        /// <param name="GOODS_ID">物料编号</param>
        /// <returns></returns>
        public IList<SiaSun.LMS.Model.GOODS_MAIN> GoodsGetListGoodsClassID(int GOODS_CLASS_ID)
        {
            return this._P_GOODS_MAIN.GetListGoodsClassID(GOODS_CLASS_ID);
        }



        public IList<SiaSun.LMS.Model.GOODS_PROPERTY> GoodsPropertyGetListGoodsTypeID(int GOODS_TYPE_ID)
        {
            return this._P_GOODS_PROPERTY.GetListGoodsTypeID(GOODS_TYPE_ID);
        }

        /// <summary>
        /// 四川电力   数据库物料属性动态赋值
        /// </summary>
        /// <typeparam name="T"></typeparam>
        /// <param name="GOODS_ID"></param>
        /// <param name="objIn"></param>
        /// <param name="objSource"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool GoodsPropertySetValue<T>(int GOODS_ID, T objTarget, object objSource, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(GOODS_ID);

            SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = this._P_GOODS_CLASS.GetModel(mGOODS_MAIN.GOODS_CLASS_ID);

            IList<SiaSun.LMS.Model.GOODS_PROPERTY> lsGOODS_PROPERTY = this._P_GOODS_PROPERTY.GetListGoodsTypeID(mGOODS_CLASS.GOODS_TYPE_ID);

            string GOODS_PROPERTY_CODE = string.Empty;

            string GOODS_PROPERTY_SOURCE = string.Empty;

            string GOODS_PROPERTY_VALUE = string.Empty;

            foreach (SiaSun.LMS.Model.GOODS_PROPERTY mGOODS_PROPERTY in lsGOODS_PROPERTY)
            {
                try
                {
                    GOODS_PROPERTY_CODE = mGOODS_PROPERTY.GOODS_PROPERTY_FIELD;

                    GOODS_PROPERTY_SOURCE = mGOODS_PROPERTY.GOODS_PROPERTY_CODE;

                    if (string.IsNullOrEmpty(GOODS_PROPERTY_CODE))
                    {
                        continue;
                    }

                    PropertyInfo[] propertys_source = objSource.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys_source)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_CODE, StringComparison.CurrentCultureIgnoreCase))
                        {
                            GOODS_PROPERTY_VALUE = pi.GetValue(objSource, null) == null ? string.Empty : pi.GetValue(objSource, null).ToString();
                        }
                    }

                    if (string.IsNullOrEmpty(GOODS_PROPERTY_VALUE))
                    {
                        continue;
                    }
                    PropertyInfo[] propertys_in = objTarget.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys_in)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_CODE, StringComparison.CurrentCultureIgnoreCase))
                        {
                            pi.SetValue(objTarget, GOODS_PROPERTY_VALUE, null);
                        }
                    }
                }
                catch (Exception ex)
                {
                    bResult = false;

                    sResult = ex.StackTrace;
                }
            }

            return bResult;
        }



        /// <summary>
        /// 
        /// </summary>
        /// <param name="GOODS_ID"></param>
        /// <param name="mObj"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool GoodsPropertyCheck(int GOODS_ID, Object mObj, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(GOODS_ID);

            SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = this._P_GOODS_CLASS.GetModel(mGOODS_MAIN.GOODS_CLASS_ID);

            IList<SiaSun.LMS.Model.GOODS_PROPERTY> lsGOODS_PROPERTY = this._P_GOODS_PROPERTY.GetListGoodsTypeID(mGOODS_CLASS.GOODS_TYPE_ID);

            string GOODS_PROPERTY_CODE = string.Empty;

            string GOODS_PROPERTY_VALUE = string.Empty;

            foreach (SiaSun.LMS.Model.GOODS_PROPERTY mGOODS_PROPERTY in lsGOODS_PROPERTY)
            {
                try
                {
                    GOODS_PROPERTY_CODE = mGOODS_PROPERTY.GOODS_PROPERTY_CODE;

                    PropertyInfo[] propertys = mObj.GetType().GetProperties();

                    foreach (PropertyInfo pi in propertys)
                    {
                        if (pi.Name.Equals(GOODS_PROPERTY_CODE))
                        {
                            GOODS_PROPERTY_VALUE = pi.GetValue(mObj, null).ToString();
                        }
                    }

                    //是自定义属性
                    if ("1".Equals(mGOODS_PROPERTY.GOODS_PROPERTY_VALID) && (string.IsNullOrEmpty(GOODS_PROPERTY_VALUE) || GOODS_PROPERTY_VALUE.Equals("%")))
                    {
                        bResult = false;

                        sResult = string.Format("{0}为必填项", mGOODS_PROPERTY.GOODS_PROPERTY_NAME);

                        return bResult;
                    }
                }
                catch (Exception ex)
                {
                    sResult += ex.Message;
                }
            }

            return bResult;
        }


        public SiaSun.LMS.Model.GOODS_TEMPLATE GoodsTemplateGetModel(int TEMPLATE_ID)
        {
            return this._P_GOODS_TEMPLETE.GetModel(TEMPLATE_ID);
        }

        public IList<SiaSun.LMS.Model.GOODS_TEMPLATE> GoodsTemplateGetList(int GOODS_ID)
        {
            return this._P_GOODS_TEMPLETE.GetList(GOODS_ID);
        }


        public IList<SiaSun.LMS.Model.GOODS_TEMPLATE_LIST> GoodsTemplateListGetList(int TEMPLATE_ID)
        {
            return this._P_GOODS_TEMPLETE_LIST.GetList(TEMPLATE_ID);
        }


        public Bitmap GetGoodsPositionTemplateImage(string parmFileUrl)
        {
            Bitmap resultBitmap = null;

            try
            {
                if (File.Exists(parmFileUrl))
                {
                    resultBitmap = new Bitmap(parmFileUrl);
                }
            }
            catch (Exception ex)
            {

            }
            return resultBitmap;
        }
        
    }
}
