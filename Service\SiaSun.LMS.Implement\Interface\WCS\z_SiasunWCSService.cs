﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.42000
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

namespace SiaSun.LMS.Implement.Interface
{
    using System.Runtime.Serialization;
    
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="SectionStatus", Namespace="http://schemas.datacontract.org/2004/07/WcfServiceLibrary1")]
    public enum SectionStatus : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Unknown = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InProduction = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InProductionWithFault = 3,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Stopped = 4,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InMaintenance = 6,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        ProcesssingStableStop = 8,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        InProductionAndSatured = 66,
    }
    
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="CancelOutputRequestEnum", Namespace="http://schemas.datacontract.org/2004/07/WcfServiceLibrary1")]
    public enum CancelOutputRequestEnum : int
    {
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        Cancelled = 0,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        UnknownRequestNumber = 1,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        OutputMissionAlreadyInProgress = 2,
        
        [System.Runtime.Serialization.EnumMemberAttribute()]
        CancelledButMechanicalIssues = 3,
    }
}
namespace SiaSun.LMS.Implement.Interface
{
    using System.Runtime.Serialization;
    
    
    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.Runtime.Serialization", "*******")]
    [System.Runtime.Serialization.DataContractAttribute(Name="WMSTask", Namespace="http://schemas.datacontract.org/2004/07/WcfControlMonitorLib")]
    public partial class WMSTask : object, System.Runtime.Serialization.IExtensibleDataObject
    {
        
        private System.Runtime.Serialization.ExtensionDataObject extensionDataField;
        
        private string BarcodeField;
        
        private string BegtimeField;
        
        private int ControlTaskTypeField;
        
        private string EndCellField;
        
        private string EndDeviceField;
        
        private string EndDeviceCodeField;
        
        private int FIDField;
        
        private int ManageIDField;
        
        private int ManageTaskKindIndexField;
        
        private string PalletBarcodeField;
        
        private int Relative_Control_IDField;
        
        private string StartCellField;
        
        private string StartDeviceField;
        
        private string StartDeviceCodeField;
        
        private int StatusField;
        
        private int TaskLevelField;
        
        public System.Runtime.Serialization.ExtensionDataObject ExtensionData
        {
            get
            {
                return this.extensionDataField;
            }
            set
            {
                this.extensionDataField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string Barcode
        {
            get
            {
                return this.BarcodeField;
            }
            set
            {
                this.BarcodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string Begtime
        {
            get
            {
                return this.BegtimeField;
            }
            set
            {
                this.BegtimeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int ControlTaskType
        {
            get
            {
                return this.ControlTaskTypeField;
            }
            set
            {
                this.ControlTaskTypeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EndCell
        {
            get
            {
                return this.EndCellField;
            }
            set
            {
                this.EndCellField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string EndDevice
        {
            get
            {
                return this.EndDeviceField;
            }
            set
            {
                this.EndDeviceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string EndDeviceCode
        {
            get
            {
                return this.EndDeviceCodeField;
            }
            set
            {
                this.EndDeviceCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int FID
        {
            get
            {
                return this.FIDField;
            }
            set
            {
                this.FIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int ManageID
        {
            get
            {
                return this.ManageIDField;
            }
            set
            {
                this.ManageIDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int ManageTaskKindIndex
        {
            get
            {
                return this.ManageTaskKindIndexField;
            }
            set
            {
                this.ManageTaskKindIndexField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string PalletBarcode
        {
            get
            {
                return this.PalletBarcodeField;
            }
            set
            {
                this.PalletBarcodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int Relative_Control_ID
        {
            get
            {
                return this.Relative_Control_IDField;
            }
            set
            {
                this.Relative_Control_IDField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StartCell
        {
            get
            {
                return this.StartCellField;
            }
            set
            {
                this.StartCellField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute()]
        public string StartDevice
        {
            get
            {
                return this.StartDeviceField;
            }
            set
            {
                this.StartDeviceField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public string StartDeviceCode
        {
            get
            {
                return this.StartDeviceCodeField;
            }
            set
            {
                this.StartDeviceCodeField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int Status
        {
            get
            {
                return this.StatusField;
            }
            set
            {
                this.StatusField = value;
            }
        }
        
        [System.Runtime.Serialization.DataMemberAttribute(IsRequired=true)]
        public int TaskLevel
        {
            get
            {
                return this.TaskLevelField;
            }
            set
            {
                this.TaskLevelField = value;
            }
        }
    }
}


[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.ServiceModel.ServiceContractAttribute(ConfigurationName="z_ISiasunWCSService")]
public interface z_ISiasunWCSService
{
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusString", ReplyAction="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusStringResponse")]
    System.Collections.Generic.Dictionary<int, string> GetAllSectionStatusString(string systemNumber);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusString", ReplyAction="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusStringResponse")]
    System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<int, string>> GetAllSectionStatusStringAsync(string systemNumber);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusEnum", ReplyAction="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusEnumResponse")]
    System.Collections.Generic.Dictionary<int, SiaSun.LMS.Implement.Interface.SectionStatus> GetAllSectionStatusEnum(string systemNumber);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusEnum", ReplyAction="http://tempuri.org/z_ISiasunWCSService/GetAllSectionStatusEnumResponse")]
    System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<int, SiaSun.LMS.Implement.Interface.SectionStatus>> GetAllSectionStatusEnumAsync(string systemNumber);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/CancelOutputRequest", ReplyAction="http://tempuri.org/z_ISiasunWCSService/CancelOutputRequestResponse")]
    SiaSun.LMS.Implement.Interface.CancelOutputRequestEnum CancelOutputRequest(string requestNumber);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/CancelOutputRequest", ReplyAction="http://tempuri.org/z_ISiasunWCSService/CancelOutputRequestResponse")]
    System.Threading.Tasks.Task<SiaSun.LMS.Implement.Interface.CancelOutputRequestEnum> CancelOutputRequestAsync(string requestNumber);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/SendInputTask", ReplyAction="http://tempuri.org/z_ISiasunWCSService/SendInputTaskResponse")]
    string SendInputTask(SiaSun.LMS.Implement.Interface.WMSTask inputTask);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/SendInputTask", ReplyAction="http://tempuri.org/z_ISiasunWCSService/SendInputTaskResponse")]
    System.Threading.Tasks.Task<string> SendInputTaskAsync(SiaSun.LMS.Implement.Interface.WMSTask inputTask);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/SendInputTaskByID", ReplyAction="http://tempuri.org/z_ISiasunWCSService/SendInputTaskByIDResponse")]
    string SendInputTaskByID(int iManageTaskID);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/SendInputTaskByID", ReplyAction="http://tempuri.org/z_ISiasunWCSService/SendInputTaskByIDResponse")]
    System.Threading.Tasks.Task<string> SendInputTaskByIDAsync(int iManageTaskID);
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/IsAlive", ReplyAction="http://tempuri.org/z_ISiasunWCSService/IsAliveResponse")]
    bool IsAlive();
    
    [System.ServiceModel.OperationContractAttribute(Action="http://tempuri.org/z_ISiasunWCSService/IsAlive", ReplyAction="http://tempuri.org/z_ISiasunWCSService/IsAliveResponse")]
    System.Threading.Tasks.Task<bool> IsAliveAsync();
}

[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public interface z_ISiasunWCSServiceChannel : z_ISiasunWCSService, System.ServiceModel.IClientChannel
{
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public partial class z_ISiasunWCSServiceClient : System.ServiceModel.ClientBase<z_ISiasunWCSService>, z_ISiasunWCSService
{
    
    public z_ISiasunWCSServiceClient()
    {
    }
    
    public z_ISiasunWCSServiceClient(string endpointConfigurationName) : 
            base(endpointConfigurationName)
    {
    }
    
    public z_ISiasunWCSServiceClient(string endpointConfigurationName, string remoteAddress) : 
            base(endpointConfigurationName, remoteAddress)
    {
    }
    
    public z_ISiasunWCSServiceClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(endpointConfigurationName, remoteAddress)
    {
    }
    
    public z_ISiasunWCSServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(binding, remoteAddress)
    {
    }
    
    public System.Collections.Generic.Dictionary<int, string> GetAllSectionStatusString(string systemNumber)
    {
        return base.Channel.GetAllSectionStatusString(systemNumber);
    }
    
    public System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<int, string>> GetAllSectionStatusStringAsync(string systemNumber)
    {
        return base.Channel.GetAllSectionStatusStringAsync(systemNumber);
    }
    
    public System.Collections.Generic.Dictionary<int, SiaSun.LMS.Implement.Interface.SectionStatus> GetAllSectionStatusEnum(string systemNumber)
    {
        return base.Channel.GetAllSectionStatusEnum(systemNumber);
    }
    
    public System.Threading.Tasks.Task<System.Collections.Generic.Dictionary<int, SiaSun.LMS.Implement.Interface.SectionStatus>> GetAllSectionStatusEnumAsync(string systemNumber)
    {
        return base.Channel.GetAllSectionStatusEnumAsync(systemNumber);
    }
    
    public SiaSun.LMS.Implement.Interface.CancelOutputRequestEnum CancelOutputRequest(string requestNumber)
    {
        return base.Channel.CancelOutputRequest(requestNumber);
    }
    
    public System.Threading.Tasks.Task<SiaSun.LMS.Implement.Interface.CancelOutputRequestEnum> CancelOutputRequestAsync(string requestNumber)
    {
        return base.Channel.CancelOutputRequestAsync(requestNumber);
    }
    
    public string SendInputTask(SiaSun.LMS.Implement.Interface.WMSTask inputTask)
    {
        return base.Channel.SendInputTask(inputTask);
    }
    
    public System.Threading.Tasks.Task<string> SendInputTaskAsync(SiaSun.LMS.Implement.Interface.WMSTask inputTask)
    {
        return base.Channel.SendInputTaskAsync(inputTask);
    }
    
    public string SendInputTaskByID(int iManageTaskID)
    {
        return base.Channel.SendInputTaskByID(iManageTaskID);
    }
    
    public System.Threading.Tasks.Task<string> SendInputTaskByIDAsync(int iManageTaskID)
    {
        return base.Channel.SendInputTaskByIDAsync(iManageTaskID);
    }
    
    public bool IsAlive()
    {
        return base.Channel.IsAlive();
    }
    
    public System.Threading.Tasks.Task<bool> IsAliveAsync()
    {
        return base.Channel.IsAliveAsync();
    }
}
