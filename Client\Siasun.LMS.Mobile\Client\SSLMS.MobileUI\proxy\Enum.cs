﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SSLMS.Model
{
    public enum OPERATOR
    {
        自动申请
    }

    public enum AREA_TYPE
    {
        liku,
        xuniku,
        all
    }

    public enum SAP_TYPE
    {
        SAP_IN,
        SAP_OUT
    }

    public enum PLAN_TYPE_CODE
    {
        IN = 1,
        OUT = 2
    }

    public enum PLAN_STATUS
    {
        Waitting,
        Executing,
        Suspend,
        Complete
    }

    public enum MANAGE_TYPE_CODE
    {
        IN = 1,
        STOCKADJUST = 110,
        OUT = 2,
        MOVE = 3,
        MOVEPALLET = 301,
        MOVEDEEPOUTER = 302,
        MOVEDEEPINNER = 303,
        MOVEDEEPOUTERBACK = 304,
        MOVEDEEPOUTEROUT = 305,
        MOVEDEEPINNEROUT = 306,
        SAP_IN = 4,
        SAP_OUT = 5,
    }

    public enum MANAGE_MODE
    {
        AUTO = 1,
        HAND = 2
    }

    public enum MANAGE_STATUS
    {
        Waitting,
        WaitingSend,
        <PERSON>rror,
        Cancel,
        Finish,
        Executing,
        WaitConfirm,
        ConfirmFinish
    }



    public enum CONTROL_STATUS
    {
        IN = 1,
        OUT = 2,
        MOVE = 3
    }


    public enum CELL_TYPE
    {
        Cell,
        Station
    }

    public enum SHELF_TYPE
    {
        normal,
        deep
    }

    public enum CELL_STATUS
    {
        have,
        nohave,
        exception,
        pallet
    }

    public enum CELL_INOUT
    {
        IN,
        OUT,
        INOUT,
        ALL
    }

    public enum CELL_FORK_TYPE
    {
        far,
        normal,
        near
    }

    public enum RUN_STATUS
    {
        disable,
        enable,
        run,
        selected
    }

    public enum STORAGE_TYPE
    {
        multiple,
        single,
        none
    }

    public enum GOODS_TYPE_ID
    {
        pallet = 0
    }

}
