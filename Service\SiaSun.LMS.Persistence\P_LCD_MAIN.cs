﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     SiaSun
 *       日期：     2018/1/4
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;

    using SiaSun.LMS.Model;

    /// <summary>
    /// LCD_MAIN
    /// </summary>
    public class P_LCD_MAIN : P_Base_House
    {
        public P_LCD_MAIN()
        {
            //
            // TODO: 此处添加LCD_MAIN的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<LCD_MAIN> GetList()
        {
            return ExecuteQueryForList<LCD_MAIN>("LCD_MAIN_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(LCD_MAIN lcd_main)
        {
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("LCD_MAIN");
                lcd_main.LCD_ID = id;
            }

            ExecuteInsert("LCD_MAIN_INSERT", lcd_main);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(LCD_MAIN lcd_main)
        {
            ExecuteUpdate("LCD_MAIN_UPDATE", lcd_main);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public LCD_MAIN GetModel(int LCD_ID)
        {
            return ExecuteQueryForObject<LCD_MAIN>("LCD_MAIN_SELECT_BY_ID", LCD_ID);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public LCD_MAIN GetModel(string LCD_CODE)
        {
            return ExecuteQueryForObject<LCD_MAIN>("LCD_MAIN_SELECT_BY_LCD_CODE", LCD_CODE);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete(int LCD_ID)
        {
            ExecuteDelete("LCD_MAIN_DELETE", LCD_ID);
        }


    }
}
