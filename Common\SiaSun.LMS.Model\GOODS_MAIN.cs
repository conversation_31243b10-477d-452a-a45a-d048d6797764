﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_MAIN
	{
		public GOODS_MAIN()
		{
			
		}
		
		private int _goods_id;
		private int _goods_class_id;
		private int _logic_id;
		private string _goods_code;
		private string _goods_name;
		private string _goods_units;
		private string _goods_const_property1;
		private string _goods_const_property2;
		private string _goods_const_property3;
		private string _goods_const_property4;
		private string _goods_const_property5;
		private string _goods_const_property6;
		private string _goods_const_property7;
		private string _goods_const_property8;
		private System.Decimal _goods_limit_upper_quantity;
		private System.Decimal _goods_limit_lower_quantity;
		private string _goods_remark;
		private int _goods_order;
		private string _goods_flag;
		private string _goods_color;
		
		///<sumary>
		/// 物料编号
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}
		///<sumary>
		/// 物料类别编号
        ///</sumary>
        [DataMember]
		public int GOODS_CLASS_ID
		{
			get{return _goods_class_id;}
			set{_goods_class_id = value;}
		}

		///<sumary>
		/// 逻辑存储区
        ///</sumary>
        [DataMember]
		public int LOGIC_ID
		{
			get{return _logic_id;}
			set{_logic_id = value;}
		}
		///<sumary>
		/// 物料编码
        ///</sumary>
        [DataMember]
		public string GOODS_CODE
		{
			get{return _goods_code;}
			set{_goods_code = value;}
		}
		///<sumary>
		/// 物料名称
        ///</sumary>
        [DataMember]
		public string GOODS_NAME
		{
			get{return _goods_name;}
			set{_goods_name = value;}
		}
		///<sumary>
		/// 物料单位
        ///</sumary>
        [DataMember]
		public string GOODS_UNITS
		{
			get{return _goods_units;}
			set{_goods_units = value;}
		}
		///<sumary>
		/// 物料型号规格
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY1
		{
			get{return _goods_const_property1;}
			set{_goods_const_property1 = value;}
		}
		///<sumary>
		/// 是否SN码拣选
        /// 1是SN码拣选
        /// 0是正常拣选
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY2
		{
			get{return _goods_const_property2;}
			set{_goods_const_property2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY3
		{
			get{return _goods_const_property3;}
			set{_goods_const_property3 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY4
		{
			get{return _goods_const_property4;}
			set{_goods_const_property4 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY5
		{
			get{return _goods_const_property5;}
			set{_goods_const_property5 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY6
		{
			get{return _goods_const_property6;}
			set{_goods_const_property6 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY7
		{
			get{return _goods_const_property7;}
			set{_goods_const_property7 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_CONST_PROPERTY8
		{
			get{return _goods_const_property8;}
			set{_goods_const_property8 = value;}
		}
		///<sumary>
		/// 物料库存上限
        ///</sumary>
        [DataMember]
		public System.Decimal GOODS_LIMIT_UPPER_QUANTITY
		{
			get{return _goods_limit_upper_quantity;}
			set{_goods_limit_upper_quantity = value;}
		}
		///<sumary>
		/// 物料库存下限
        ///</sumary>
        [DataMember]
		public System.Decimal GOODS_LIMIT_LOWER_QUANTITY
		{
			get{return _goods_limit_lower_quantity;}
			set{_goods_limit_lower_quantity = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string GOODS_REMARK
		{
			get{return _goods_remark;}
			set{_goods_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int GOODS_ORDER
		{
			get{return _goods_order;}
			set{_goods_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string GOODS_FLAG
		{
			get{return _goods_flag;}
			set{_goods_flag = value;}
		}
		///<sumary>
		/// 库存助记色
        ///</sumary>
        [DataMember]
		public string GOODS_COLOR
		{
			get{return _goods_color;}
			set{_goods_color = value;}
		}
	}
}
