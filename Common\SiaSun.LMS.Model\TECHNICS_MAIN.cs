﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// TECHNICS_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class TECHNICS_MAIN
	{
		public TECHNICS_MAIN()
		{
			
		}
		
		private int _technics_id;
		private string _technics_description;
		private int _relavtive_technics_id;
		private int _goods_class_id;
		private string _plan_type_code;
		private string _manage_type_code;
		private string _cell_model;
		private int _start_area_id;
		private int _end_area_id;
		private int _start_position;
		private int _end_position;
		private int _same_laneway;
		private string _sp_start_cell_status;
		private string _sp_start_runs_status;
		private string _ep_start_cell_status;
		private string _ep_start_run_status;
		private string _sp_end_cell_status;
		private string _sp_end_runs_status;
		private string _ep_end_cell_status;
		private string _ep_end_run_status;
		private int _check_start_position_flag;
		private int _check_end_position_flag;
		private string _start_status;
		private string _finish_status;
		private int _has_storage_flag;
		private int _auto_create_control;
		private int _create_storage_flag;
		private int _move_stroage_flag;
		private int _cancel_bind_barcode;
		private int _cancel_storage_flag;
		private int _plan_change_flag;
		private int _station_sort_flag;
		private int _station_project_flag;
		private string _data_order;
		private string _technics_actions;
		private string _technics_remark;
		private string _end_logic_status;
		private string _end_goods_status;
		private string _cell_inout;
		private string _cell_class;
		private int _init_storage_flag;
		private int _next_stock_technics_id;
		private int _next_goods_technics_id;
		
		///<sumary>
		/// 流程编号
        ///</sumary>
        [DataMember]
		public int TECHNICS_ID
		{
			get{return _technics_id;}
			set{_technics_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string TECHNICS_DESCRIPTION
		{
			get{return _technics_description;}
			set{_technics_description = value;}
		}
		///<sumary>
		/// 关联流程编号
        ///</sumary>
        [DataMember]
		public int RELAVTIVE_TECHNICS_ID
		{
			get{return _relavtive_technics_id;}
			set{_relavtive_technics_id = value;}
		}
		///<sumary>
		/// 物料类别
        ///</sumary>
        [DataMember]
		public int GOODS_CLASS_ID
		{
			get{return _goods_class_id;}
			set{_goods_class_id = value;}
		}
		///<sumary>
		/// 业务类型
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_CODE
		{
			get{return _plan_type_code;}
			set{_plan_type_code = value;}
		}
		///<sumary>
		/// 任务类型
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_CODE
		{
			get{return _manage_type_code;}
			set{_manage_type_code = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CELL_MODEL
		{
			get{return _cell_model;}
			set{_cell_model = value;}
		}
		///<sumary>
		/// 起始库区
        ///</sumary>
        [DataMember]
		public int START_AREA_ID
		{
			get{return _start_area_id;}
			set{_start_area_id = value;}
		}
		///<sumary>
		/// 终止库区
        ///</sumary>
        [DataMember]
		public int END_AREA_ID
		{
			get{return _end_area_id;}
			set{_end_area_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int START_POSITION
		{
			get{return _start_position;}
			set{_start_position = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int END_POSITION
		{
			get{return _end_position;}
			set{_end_position = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int SAME_LANEWAY
		{
			get{return _same_laneway;}
			set{_same_laneway = value;}
		}
		///<sumary>
		/// 起始位置开始时存储状态
        ///</sumary>
        [DataMember]
		public string SP_START_CELL_STATUS
		{
			get{return _sp_start_cell_status;}
			set{_sp_start_cell_status = value;}
		}
		///<sumary>
		/// 起始位置开始时运行状态
        ///</sumary>
        [DataMember]
		public string SP_START_RUNS_STATUS
		{
			get{return _sp_start_runs_status;}
			set{_sp_start_runs_status = value;}
		}
		///<sumary>
		/// 结束位置开始时存储状态
        ///</sumary>
        [DataMember]
		public string EP_START_CELL_STATUS
		{
			get{return _ep_start_cell_status;}
			set{_ep_start_cell_status = value;}
		}
		///<sumary>
		/// 结束位置开始时运行状态
        ///</sumary>
        [DataMember]
		public string EP_START_RUN_STATUS
		{
			get{return _ep_start_run_status;}
			set{_ep_start_run_status = value;}
		}
		///<sumary>
		/// 开始位置结束时存储状态
        ///</sumary>
        [DataMember]
		public string SP_END_CELL_STATUS
		{
			get{return _sp_end_cell_status;}
			set{_sp_end_cell_status = value;}
		}
		///<sumary>
		/// 开始位置结束时运行状态
        ///</sumary>
        [DataMember]
		public string SP_END_RUNS_STATUS
		{
			get{return _sp_end_runs_status;}
			set{_sp_end_runs_status = value;}
		}
		///<sumary>
		/// 终止位置结束时存储状态
        ///</sumary>
        [DataMember]
		public string EP_END_CELL_STATUS
		{
			get{return _ep_end_cell_status;}
			set{_ep_end_cell_status = value;}
		}
		///<sumary>
		/// 终止位置结束时运行状态
        ///</sumary>
        [DataMember]
		public string EP_END_RUN_STATUS
		{
			get{return _ep_end_run_status;}
			set{_ep_end_run_status = value;}
		}
		///<sumary>
		/// 检查起始位置状态
        ///</sumary>
        [DataMember]
		public int CHECK_START_POSITION_FLAG
		{
			get{return _check_start_position_flag;}
			set{_check_start_position_flag = value;}
		}
		///<sumary>
		/// 检查结束位置状态
        ///</sumary>
        [DataMember]
		public int CHECK_END_POSITION_FLAG
		{
			get{return _check_end_position_flag;}
			set{_check_end_position_flag = value;}
		}
		///<sumary>
		/// 任务开始状态
        ///</sumary>
        [DataMember]
		public string START_STATUS
		{
			get{return _start_status;}
			set{_start_status = value;}
		}
		///<sumary>
		/// 任务完成状态
        ///</sumary>
        [DataMember]
		public string FINISH_STATUS
		{
			get{return _finish_status;}
			set{_finish_status = value;}
		}
		///<sumary>
		/// 是否已存在库存
        ///</sumary>
        [DataMember]
		public int HAS_STORAGE_FLAG
		{
			get{return _has_storage_flag;}
			set{_has_storage_flag = value;}
		}
		///<sumary>
		/// 是否自动生成IO_CONTROL
        ///</sumary>
        [DataMember]
		public int AUTO_CREATE_CONTROL
		{
			get{return _auto_create_control;}
			set{_auto_create_control = value;}
		}
		///<sumary>
		/// 创建库存
        ///</sumary>
        [DataMember]
		public int CREATE_STORAGE_FLAG
		{
			get{return _create_storage_flag;}
			set{_create_storage_flag = value;}
		}
		///<sumary>
        /// 转移库存
        [DataMember]
		///</sumary>
		public int MOVE_STROAGE_FLAG
		{
			get{return _move_stroage_flag;}
			set{_move_stroage_flag = value;}
		}
		///<sumary>
		/// 解除条码绑定
        ///</sumary>
        [DataMember]
		public int CANCEL_BIND_BARCODE
		{
			get{return _cancel_bind_barcode;}
			set{_cancel_bind_barcode = value;}
		}
		///<sumary>
		/// 移除任务库存
        ///</sumary>
        [DataMember]
		public int CANCEL_STORAGE_FLAG
		{
			get{return _cancel_storage_flag;}
			set{_cancel_storage_flag = value;}
		}
		///<sumary>
		/// 是否更新计划
        ///</sumary>
        [DataMember]
		public int PLAN_CHANGE_FLAG
		{
			get{return _plan_change_flag;}
			set{_plan_change_flag = value;}
		}
		///<sumary>
		/// 是否站台上拣选
        ///</sumary>
        [DataMember]
		public int STATION_SORT_FLAG
		{
			get{return _station_sort_flag;}
			set{_station_sort_flag = value;}
		}
		///<sumary>
		/// 是否站台上配盘
        ///</sumary>
        [DataMember]
		public int STATION_PROJECT_FLAG
		{
			get{return _station_project_flag;}
			set{_station_project_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string DATA_ORDER
		{
			get{return _data_order;}
			set{_data_order = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string TECHNICS_ACTIONS
		{
			get{return _technics_actions;}
			set{_technics_actions = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string TECHNICS_REMARK
		{
			get{return _technics_remark;}
			set{_technics_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string END_LOGIC_STATUS
		{
			get{return _end_logic_status;}
			set{_end_logic_status = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string END_GOODS_STATUS
		{
			get{return _end_goods_status;}
			set{_end_goods_status = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CELL_INOUT
		{
			get{return _cell_inout;}
			set{_cell_inout = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CELL_CLASS
		{
			get{return _cell_class;}
			set{_cell_class = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int INIT_STORAGE_FLAG
		{
			get{return _init_storage_flag;}
			set{_init_storage_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int NEXT_STOCK_TECHNICS_ID
		{
			get{return _next_stock_technics_id;}
			set{_next_stock_technics_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int NEXT_GOODS_TECHNICS_ID
		{
			get{return _next_goods_technics_id;}
			set{_next_goods_technics_id = value;}
		}
	}
}
