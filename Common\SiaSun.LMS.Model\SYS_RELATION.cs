﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_RELATION 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_RELATION
	{
		public SYS_RELATION()
		{
			
		}
		
		private int _relation_id;
		private string _relation_code;
		private string _relation_name;
		private string _relation_id1;
		private string _relation_id2;
		private string _relation_remark;
		private string _relaton_name1;
		private string _relaton_name2;
		
		///<sumary>
		/// 关系编号
        ///</sumary>
        [DataMember]
		public int RELATION_ID
		{
			get{return _relation_id;}
			set{_relation_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string RELATION_CODE
		{
			get{return _relation_code;}
			set{_relation_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string RELATION_NAME
		{
			get{return _relation_name;}
			set{_relation_name = value;}
		}
		///<sumary>
		/// 关联编号1
        ///</sumary>
        [DataMember]
		public string RELATION_ID1
		{
			get{return _relation_id1;}
			set{_relation_id1 = value;}
		}
		///<sumary>
		/// 关联编号2
        ///</sumary>
        [DataMember]
		public string RELATION_ID2
		{
			get{return _relation_id2;}
			set{_relation_id2 = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string RELATION_REMARK
		{
			get{return _relation_remark;}
			set{_relation_remark = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string RELATON_NAME1
		{
			get{return _relaton_name1;}
			set{_relaton_name1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string RELATON_NAME2
		{
			get{return _relaton_name2;}
			set{_relaton_name2 = value;}
		}
	}
}
