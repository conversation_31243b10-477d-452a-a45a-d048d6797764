﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="MANAGE_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="MANAGE_MAIN" type="SiaSun.LMS.Model.MANAGE_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="MANAGE_MAIN">
			<result property="MANAGE_ID" column="manage_id" />
			<result property="GOODS_TEMPLATE_ID" column="goods_template_id" />
			<result property="PLAN_ID" column="plan_id" />
      <result property="PLAN_TYPE_CODE" column="plan_type_code" />
			<result property="MANAGE_TYPE_CODE" column="manage_type_code" />
			<result property="MANAGE_STATUS" column="manage_status" />
			<result property="STOCK_BARCODE" column="stock_barcode" />
      <result property="FULL_FLAG" column="full_flag" />
			<result property="CELL_MODEL" column="cell_model" />
			<result property="START_CELL_ID" column="start_cell_id" />
			<result property="END_CELL_ID" column="end_cell_id" />
			<result property="MANAGE_OPERATOR" column="manage_operator" />
			<result property="MANAGE_BEGIN_TIME" column="manage_begin_time" />
			<result property="MANAGE_END_TIME" column="manage_end_time" />
			<result property="MANAGE_LEVEL" column="manage_level" />
			<result property="MANAGE_REMARK" column="manage_remark" />
			<result property="MANAGE_CONFIRM_TIME" column="manage_confirm_time" />

      <result property="MANAGE_SOURCE" column="manage_source" />
      <result property="MANAGE_RELATE_CODE" column="manage_relate_code" />
      <result property="CROSS_FLAG" column="cross_flag" />
      <result property="BACKUP_FIELD1" column="backup_field1" />
      <result property="BACKUP_FIELD2" column="backup_field2" />
      <result property="BACKUP_FIELD3" column="backup_field3" />
      <result property="BACKUP_FIELD4" column="backup_field4" />
      <result property="BACKUP_FIELD5" column="backup_field5" />

    </resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="MANAGE_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        manage_id,
        goods_template_id,
        plan_id,
        plan_type_code,
        manage_type_code,
        manage_status,
        stock_barcode,
        full_flag,
        cell_model,
        start_cell_id,
        end_cell_id,
        manage_operator,
        manage_begin_time,
        manage_end_time,
        manage_level,
        manage_remark,
        manage_confirm_time,
        manage_source,
        manage_relate_code,
        cross_flag,
        backup_field1,
        backup_field2,
        backup_field3,
        backup_field4,
        backup_field5
        From MANAGE_MAIN
      </select>

    <select id="MANAGE_MAIN_SELECT_BY_ID" parameterClass="int" extends = "MANAGE_MAIN_SELECT" resultMap="SelectResult">		
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_id=#MANAGE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>


    <select id="MANAGE_MAIN_SELECT_BY_STOCK_BARCODE" parameterClass="int" extends = "MANAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          stock_barcode LIKE  #STOCK_BARCODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_MAIN_SELECT_BY_PLAN_ID" parameterClass="int" extends = "MANAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_id=#PLAN_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_MAIN_SELECT_BY_STOCK_BARCODE_MANAGE_TYPE_CODE" parameterClass="int" extends = "MANAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          stock_barcode=#STOCK_BARCODE# and  manage_type_code=#MANAGE_TYPE_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_MAIN_SELECT_BY_RELATE_CODE" parameterClass="int" extends = "MANAGE_MAIN_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_relate_code=#RELATE_CODE#
        </isParameterPresent>
      </dynamic>
    </select>
					
		<insert id="MANAGE_MAIN_INSERT" parameterClass="MANAGE_MAIN">
      Insert Into MANAGE_MAIN (
      manage_id,
      goods_template_id,
      plan_id,
      plan_type_code,
      manage_type_code,
      manage_status,
      stock_barcode,
      full_flag,
      cell_model,
      start_cell_id,
      end_cell_id,
      manage_operator,
      manage_begin_time,
      manage_end_time,
      manage_level,
      manage_remark,
      manage_confirm_time,
      manage_source,
      manage_relate_code,
      cross_flag,
      backup_field1,
      backup_field2,
      backup_field3,
      backup_field4,
      backup_field5
      )Values(
      #MANAGE_ID#,
      #GOODS_TEMPLATE_ID#,
      #PLAN_ID#,
      #PLAN_TYPE_CODE#,
      #MANAGE_TYPE_CODE#,
      #MANAGE_STATUS#,
      #STOCK_BARCODE#,
      #FULL_FLAG#,
      #CELL_MODEL#,
      #START_CELL_ID#,
      #END_CELL_ID#,
      #MANAGE_OPERATOR#,
      #MANAGE_BEGIN_TIME#,
      #MANAGE_END_TIME#,
      #MANAGE_LEVEL#,
      #MANAGE_REMARK#,
      #MANAGE_CONFIRM_TIME#,
      #MANAGE_SOURCE#,
      #MANAGE_RELATE_CODE#,
      #CROSS_FLAG#,
      #BACKUP_FIELD1#,
      #BACKUP_FIELD2#,
      #BACKUP_FIELD3#,
      #BACKUP_FIELD4#,
      #BACKUP_FIELD5#
      )
      <!--<selectKey  resultClass="int" type="post" property="MANAGE_ID">
        select @@IDENTITY as value
    </selectKey>-->     
		</insert>
		
		<update id="MANAGE_MAIN_UPDATE" parameterClass="MANAGE_MAIN">
      Update MANAGE_MAIN Set
      manage_id=#MANAGE_ID#,
      goods_template_id=#GOODS_TEMPLATE_ID#,
      plan_id=#PLAN_ID#,
      plan_type_code=#PLAN_TYPE_CODE#,
      manage_type_code=#MANAGE_TYPE_CODE#,
      manage_status=#MANAGE_STATUS#,
      stock_barcode=#STOCK_BARCODE#,
      full_flag=#FULL_FLAG#,
      cell_model=#CELL_MODEL#,
      start_cell_id=#START_CELL_ID#,
      end_cell_id=#END_CELL_ID#,
      manage_operator=#MANAGE_OPERATOR#,
      manage_begin_time=#MANAGE_BEGIN_TIME#,
      manage_end_time=#MANAGE_END_TIME#,
      manage_level=#MANAGE_LEVEL#,
      manage_remark=#MANAGE_REMARK#,
      manage_confirm_time=#MANAGE_CONFIRM_TIME#,
      manage_source=#MANAGE_SOURCE#,
      manage_relate_code=#MANAGE_RELATE_CODE#,
      cross_flag=#CROSS_FLAG#,
      backup_field1=#BACKUP_FIELD1#,
      backup_field2=#BACKUP_FIELD2#,
      backup_field3=#BACKUP_FIELD3#,
      backup_field4=#BACKUP_FIELD4#,
      backup_field5=#BACKUP_FIELD5#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					manage_id=#MANAGE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="MANAGE_MAIN_DELETE" parameterClass="int">
			Delete From MANAGE_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_id=#MANAGE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>