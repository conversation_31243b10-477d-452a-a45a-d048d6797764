﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Text;
using System.Windows.Forms;

using SSLMS.MobileUI.uc;

namespace SSLMS.MobileUI
{
    public partial class FrmCellSelect : Form
    {

        public WH_CELL mWH_CELL = null;

        ucCell ucCellSelect = new ucCell();

        public FrmCellSelect()
        {
            InitializeComponent();
            this.ucCellSelect.Dock = DockStyle.Fill;

            this.Controls.Add(this.ucCellSelect);

            this.ucCellSelect.Init();

            this.ucCellSelect.CellDoubleClick += new ucCell.CELLDoubleClickHandler(ucCellSelect_CellDoubleClick);

        }

        void ucCellSelect_CellDoubleClick(WH_CELL mST_CELL)
        {
            this.mWH_CELL = mST_CELL;

            this.DialogResult = DialogResult.OK;
        }



        private void FrmCellSelect_Load(object sender, EventArgs e)
        {

        }

        private void FrmCellSelect_Closed(object sender, EventArgs e)
        {
            this.DialogResult = DialogResult.Cancel;
        }
    }
}