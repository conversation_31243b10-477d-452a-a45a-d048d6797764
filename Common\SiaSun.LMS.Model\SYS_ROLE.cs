﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_ROLE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_ROLE
	{
		public SYS_ROLE()
		{
			
		}
		
		private int _role_id;
		private string _role_code;
		private string _role_name;
		private int _role_start_menu_id;
		private string _role_remark;
		private int _role_order;
		private string _role_flag;
		
		///<sumary>
		/// 角色编号
        ///</sumary>
        [DataMember]
		public int ROLE_ID
		{
			get{return _role_id;}
			set{_role_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string ROLE_CODE
		{
			get{return _role_code;}
			set{_role_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string ROLE_NAME
		{
			get{return _role_name;}
			set{_role_name = value;}
		}
		///<sumary>
		/// 起始页面编号
        ///</sumary>
        [DataMember]
		public int ROLE_START_MENU_ID
		{
			get{return _role_start_menu_id;}
			set{_role_start_menu_id = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string ROLE_REMARK
		{
			get{return _role_remark;}
			set{_role_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int ROLE_ORDER
		{
			get{return _role_order;}
			set{_role_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string ROLE_FLAG
		{
			get{return _role_flag;}
			set{_role_flag = value;}
		}
	}
}
