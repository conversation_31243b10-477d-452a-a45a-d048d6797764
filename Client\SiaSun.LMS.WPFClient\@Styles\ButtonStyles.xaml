﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <!--确认按钮样式-->
    <ControlTemplate x:Key="templateImageButtonConfirm" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/ok.png" Width="16" Height="16"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="确认"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
    <!--加载按钮样式-->
    <ControlTemplate x:Key="templateImageButtonLoad" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/feed_add.png" Width="16" Height="16"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="加载"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--添加按钮样式-->
    <ControlTemplate x:Key="templateImageButtonAdd" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/add.png" Width="25" Height="25"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="添加"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="False">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>   
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--编辑按钮样式-->
    <ControlTemplate x:Key="templateImageButtonEdit" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/edit.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="编辑"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>   

    <!--保存按钮样式-->
    <ControlTemplate x:Key="templateImageButtonSave" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/save.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="保存"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>    
    
    <!--删除按钮样式-->
    <ControlTemplate x:Key="templateImageButtonDelete" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/delete.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="删除"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--取消按钮样式-->
    <ControlTemplate x:Key="templateImageButtonCancel" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/cancel.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="取消"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--刷新按钮样式-->
    <ControlTemplate x:Key="templateImageButtonUpdate" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0" >
            <Image Source="/@Images/refresh.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="刷新"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--查询按钮样式-->
    <ControlTemplate x:Key="templateImageButtonSearch" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/search.png" Width="16" Height="16" Margin="1"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="查询"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--打印按钮样式-->
    <ControlTemplate x:Key="templateImageButtonPrint" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/printer.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="打印"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--导出Excel按钮样式-->
    <ControlTemplate x:Key="templateImageButtonExportExcel" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/export_excel.png" Width="25" Height="25" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="导出Excel"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="False">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
    <!--明细按钮样式-->
    <ControlTemplate x:Key="templateImageButtonDetail" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/list.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="显示明细"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--首页按钮样式-->
    <ControlTemplate x:Key="templateImageButtonFirst" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/first.png" Width="25" Height="25" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="首页"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--前一页按钮样式-->
    <ControlTemplate x:Key="templateImageButtonBack" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/back.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="前一页"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--下一页按钮样式-->
    <ControlTemplate x:Key="templateImageButtonNext" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/next.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="下一页"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>    
 
    <!--末页按钮样式-->
    <ControlTemplate x:Key="templateImageButtonLast" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/last.png" Width="16" Height="16" ></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="末页"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--实际尺寸按钮样式-->
    <ControlTemplate x:Key="templateImageButtonZoom" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/trimsize.png" Width="16" Height="16"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="实际尺寸"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
    <!--放大按钮样式-->
    <ControlTemplate x:Key="templateImageButtonZoomOut" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/zoom_out.png" Width="16" Height="16"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="缩小"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <!--缩小按钮样式-->
    <ControlTemplate x:Key="templateImageButtonZoomIn" TargetType="Button">
        <StackPanel Orientation="Horizontal" Margin="1,0,5,0">
            <Image Source="/@Images/zoom_in.png" Width="16" Height="16"></Image>
            <TextBlock Text="{TemplateBinding Button.Content}"  Foreground="{TemplateBinding Button.Foreground}" VerticalAlignment="Center" ToolTip="{TemplateBinding Button.Content}"></TextBlock>
        </StackPanel>
        <ControlTemplate.Triggers>
            <Trigger Property="Button.Content" Value="">
                <Setter Property="Button.ToolTip" Value="缩小"></Setter>
            </Trigger>
            <Trigger Property="Image.IsMouseOver" Value="True">
                <Setter Property="StackPanel.Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
                <Setter Property="Button.Opacity" Value="0.5"></Setter>
                <Setter Property="StackPanel.Margin" Value="5,0,5,0"></Setter>
                <Setter Property="StackPanel.Cursor" Value="Hand"></Setter>
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
    
</ResourceDictionary>