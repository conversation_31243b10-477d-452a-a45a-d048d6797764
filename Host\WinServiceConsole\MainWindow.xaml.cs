﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Diagnostics;
using System.ServiceProcess;



namespace WinServiceConsole
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {

        private System.Windows.Forms.NotifyIcon notifyIcon = null;

        public MainWindow()
        {
            InitializeComponent();

            this.InitialTray();

            this.Closing += new System.ComponentModel.CancelEventHandler(MainWindow_Closing);

            this.MouseLeftButtonDown += new MouseButtonEventHandler(MainWindow_MouseLeftButtonDown);

        }

        void MainWindow_MouseLeftButtonDown(object sender, MouseButtonEventArgs e)
        {
            this.DragMove();
        }

        private void Window_Loaded(object sender, RoutedEventArgs e)
        {
            this.ResizeMode = System.Windows.ResizeMode.NoResize;
            ServiceController serviceController = new ServiceController("WMSService");

            try
            {

                if (serviceController.Status == ServiceControllerStatus.Running)
                {

                    this.serviceStatus.Fill = Brushes.Green;
                    this.lblText.Content = "服务运行中!";
                }

                if (serviceController.Status == ServiceControllerStatus.Stopped)
                {
                    this.serviceStatus.Fill = Brushes.Red;
                    this.lblText.Content = "服务已停止!";
                }

                this.btnInstall.IsEnabled = true;

                this.btnUninstall.IsEnabled = true;
            }
            catch (Exception ex)
            {
                this.btnStart.IsEnabled = false;

                this.btnStop.IsEnabled = false;


            }
        }


        void MainWindow_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            e.Cancel = true;

            this.Visibility = Visibility.Hidden;

            this.notifyIcon.ShowBalloonTip(3, "提示", "程序隐藏在后台运行", System.Windows.Forms.ToolTipIcon.Info);
        }



        private void InitialTray()
        {
            notifyIcon = new System.Windows.Forms.NotifyIcon();
            notifyIcon.Text = "托盘图标";
            notifyIcon.BalloonTipText = "程序运行中";
            notifyIcon.Visible = true;
            notifyIcon.Icon = new System.Drawing.Icon(System.Windows.Forms.Application.StartupPath + @"\SiasunWMS.ico");
            notifyIcon.ShowBalloonTip(2000);
            notifyIcon.MouseClick += new System.Windows.Forms.MouseEventHandler(notifyIcon_MouseClick);
           
            System.Windows.Forms.MenuItem menuExit = new System.Windows.Forms.MenuItem("退出");

            System.Windows.Forms.MenuItem[] contextMenu = new System.Windows.Forms.MenuItem[] { menuExit };

            notifyIcon.ContextMenu = new System.Windows.Forms.ContextMenu(contextMenu);

            this.StateChanged += new EventHandler(MainWindow_StateChanged);

            menuExit.Click += new EventHandler(menuExit_Click);


        }

        void menuExit_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("是否退出系统？", "系统提示", MessageBoxButton.YesNo, MessageBoxImage.Question, MessageBoxResult.Yes) == MessageBoxResult.Yes)
            {
                this.notifyIcon.Dispose();
                Application.Current.Shutdown();
            }
        }

        void MainWindow_StateChanged(object sender, EventArgs e)
        {
            if (this.WindowState == WindowState.Minimized)
            {
                this.Visibility = System.Windows.Visibility.Hidden;
            } 
        }

        void notifyIcon_MouseClick(object sender, System.Windows.Forms.MouseEventArgs e)
        {
            if (e.Button == System.Windows.Forms.MouseButtons.Left)
            {
                if (this.Visibility == System.Windows.Visibility.Visible)
                {
                    this.Visibility = Visibility.Hidden;
                }
                else
                {
                    this.Visibility = Visibility.Visible;
                    
                    this.Activate();
                    
                }
            }
        }

        private void btnInstall_Click(object sender, RoutedEventArgs e)
        {
            string CurrentDirectory = System.Environment.CurrentDirectory;
            System.Environment.CurrentDirectory = CurrentDirectory + "\\Service";
            Process process = new Process();
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.FileName = "Install.bat";
            process.StartInfo.CreateNoWindow = true;
            process.Start();
            System.Environment.CurrentDirectory = CurrentDirectory;

            this.btnInstall.IsEnabled = false;

            this.btnUninstall.IsEnabled = true;

            this.btnStart.IsEnabled = true;

            this.btnStop.IsEnabled = true;

        }

        private void btnUninstall_Click(object sender, RoutedEventArgs e)
        {
            string CurrentDirectory = System.Environment.CurrentDirectory;
            System.Environment.CurrentDirectory = CurrentDirectory + "\\Service";
            Process process = new Process();
            process.StartInfo.UseShellExecute = false;
            process.StartInfo.FileName = "Uninstall.bat";
            process.StartInfo.CreateNoWindow = true;
            process.Start();
            System.Environment.CurrentDirectory = CurrentDirectory;

            this.btnInstall.IsEnabled = true ;

            this.btnUninstall.IsEnabled = false;
        }

        private void btnStart_Click(object sender, RoutedEventArgs e)
        {
            ServiceController serviceController = new ServiceController("WMSService");

            if (serviceController.Status == ServiceControllerStatus.Paused || serviceController.Status == ServiceControllerStatus.Stopped)
            {
                serviceController.Start();
                this.serviceStatus.Fill = Brushes.Green;
                this.lblText.Content = "服务运行中!";

            }
           
        }

       

        private void btnStop_Click(object sender, RoutedEventArgs e)
        {
            ServiceController serviceController = new ServiceController("WMSService");
            if (serviceController.CanStop)
            {
                this.serviceStatus.Fill = Brushes.Red;
                this.lblText.Content = "服务已停止!";
                serviceController.Stop();
            }
        }

        private void btnMinsize_Click(object sender, RoutedEventArgs e)
        {
            this.Close();
        }

        



    }
}
