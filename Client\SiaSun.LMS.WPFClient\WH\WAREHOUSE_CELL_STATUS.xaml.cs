﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.WH
{
    /// <summary>
    /// WAREHOUSE_CELL_STATUS.xaml 的交互逻辑
    /// </summary>
    public partial class WAREHOUSE_CELL_STATUS : AvalonDock.DocumentContent
    {
        /// <summary>
        /// 构造函数
        /// </summary>
        public WAREHOUSE_CELL_STATUS()
        {
            InitializeComponent();
            this.ucCellQuery.U_CellClick +=new UC.ucStoreCell.U_CELLClickHandler(ucCellQuery_U_CellClick);
        }

        /// <summary>
        /// 构造函数
        /// 用于支持缩放尺度_西充九天_YWZ_2016-12-02
        /// </summary>
        public WAREHOUSE_CELL_STATUS(string strZoomScale):this()
        {
            this.ucCellQuery.U_ZoomScale = strZoomScale;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                this.ucCellQuery.U_InitControl(string.Empty);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        //点击货位显示库存信息
        void ucCellQuery_U_CellClick(Model.WH_CELL ST_CELL)
        {

            StorageListBind(ST_CELL.CELL_ID);

        }

        /// <summary>
        /// 显示库存信息
        /// </summary>
        private void StorageListBind(int CELL_ID)
        {
            this.gridStorageList.U_Clear();

            this.gridStorageList.U_AllowChecked = false;
            this.gridStorageList.U_AllowShowPage = false;
            this.gridStorageList.U_AllowOperatData = false;

            this.gridStorageList.U_TableName = "V_STORAGE_LIST";
            this.gridStorageList.U_XmlTableName = "V_STORAGE_LIST";
            this.gridStorageList.U_OrderField = "STOCK_BARCODE";
            this.gridStorageList.U_Where = string.Format("CELL_ID={0}", CELL_ID);
            this.gridStorageList.U_TotalColumnName = "STORAGE_LIST_QUANTITY";

            this.gridStorageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridStorageList.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.gridStorageList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridStorageList.U_SplitPropertyType = "GOODS_TYPE";

            this.gridStorageList.U_DetailTableName = "STORAGE_DETAIL";
            this.gridStorageList.U_DetailRelatvieColumn = "STORAGE_LIST_ID";

            try
            {
                this.gridStorageList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

    }
}
