﻿using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class AutoMoveKitboxJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                MainApp.BaseService._S_ManageService.AutoMoveKitbox();
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("AutoMoveKitboxJob.Execute:异常", ex);
            }
        }
    }
}
