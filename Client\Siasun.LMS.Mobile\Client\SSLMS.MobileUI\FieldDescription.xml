﻿<?xml version="1.0" encoding="utf-8" ?>
<Fields>
  <Table Name="SiaSunSrvUrl">
    <Field Name="http://10.11.16.164:8000/Service">
    </Field>
  </Table>
  <Table Name="V_IO_PLAN">
    <Field Column="PLAN_BILL_NO" dbType="string" Header="计划单号" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="PLAN_CREATE_TIME" dbType="string" Header="制单时间" FieldType="text" key="" Remark="" index ="50">
    </Field>
    <Field Column="PLAN_BEGIN_TIME" dbType="string" Header="开始时间" FieldType="text" key="" Remark="" index ="60">
    </Field>
    <Field Column="PLAN_END_TIME" dbType="string" Header="完成时间" FieldType="text" key="" Remark="" index ="70">
    </Field>
    <Field Column="PLAN_STATUS" dbType="string" Header="计划状态" FieldType="combox" key="PLAN_STATUS" Remark="" index ="75">
    </Field>
    <Field Column="PLAN_OPERATOR" dbType="string" Header="操作员" FieldType="text" key="" Remark="" index ="80">
    </Field>
    <Field Column="PLAN_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="90">
    </Field>
  </Table>

  <Table Name="V_IO_PLAN_SAP_IN">
    <Field Column="PLAN_BILL_NO" dbType="string" Header="收货凭证" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="DocumentYear" dbType="string" Header="凭证年度" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="Plant" dbType="string" Header="工厂" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="Stock" dbType="string" Header="库存地点" FieldType="text" key="" Remark="" index ="40">
    </Field>
    <Field Column="PLAN_CREATE_TIME" dbType="string" Header="制单时间" FieldType="text" key="" Remark="" index ="50">
    </Field>
    <Field Column="PLAN_BEGIN_TIME" dbType="string" Header="开始时间" FieldType="text" key="" Remark="" index ="60">
    </Field>
    <Field Column="PLAN_END_TIME" dbType="string" Header="完成时间" FieldType="text" key="" Remark="" index ="70">
    </Field>
    <Field Column="PLAN_STATUS" dbType="string" Header="计划状态" FieldType="combox" key="PLAN_STATUS" Remark="" index ="75">
    </Field>
    <Field Column="PLAN_OPERATOR" dbType="string" Header="操作员" FieldType="text" key="" Remark="" index ="80">
    </Field>
    <Field Column="PLAN_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="90">
    </Field>
  </Table>

  <Table Name="V_IO_PLAN_SAP_OUT">
    <Field Column="PLAN_BILL_NO" dbType="string" Header="生产批次" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="Plant" dbType="string" Header="工厂" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="Stock" dbType="string" Header="库存地点" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="PLAN_CREATE_TIME" dbType="string" Header="制单时间" FieldType="text" key="" Remark="" index ="50">
    </Field>
    <Field Column="PLAN_BEGIN_TIME" dbType="string" Header="开始时间" FieldType="text" key="" Remark="" index ="60">
    </Field>
    <Field Column="PLAN_END_TIME" dbType="string" Header="完成时间" FieldType="text" key="" Remark="" index ="70">
    </Field>
    <Field Column="PLAN_STATUS" dbType="string" Header="计划状态" FieldType="combox" key="PLAN_STATUS" Remark="" index ="75">
    </Field>
    <Field Column="PLAN_OPERATOR" dbType="string" Header="操作员" FieldType="text" key="" Remark="" index ="80">
    </Field>
    <Field Column="PLAN_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="90">
    </Field>
  </Table>

  <Table Name="V_IO_PLAN_LIST">
    <Field Column="GOODS_CODE" dbType="string" Header="物料编码" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="物料名称" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_ID" dbType="string" Header="" FieldType="combox" key="GOODS_{0}" Remark="" index ="10">
    </Field>
    <Field Column="PLAN_LIST_QUANTITY" dbType="decimal" Header="数量" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="GOODS_UNITS" dbType="string" Header="单位" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="PLAN_LIST_ORDERED_QUANTITY" dbType="decimal" Header="下达数量" FieldType="text" key="" Remark="" index ="25">
    </Field>
    <Field Column="PLAN_LIST_FINISHED_QUANTITY" dbType="decimal" Header="完成数量" FieldType="text" key="" Remark="" index ="26">
    </Field>
    <Field Column="GOODS_PROPERTY" dbType="string" Header="" FieldType="text" key="" Remark="" index ="100">
    </Field>
    <Field Column="PLAN_LIST_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="200">
    </Field>
  </Table>

  <Table Name="V_IO_PLAN_LIST_SAP_IN">
    <Field Column="GOODS_CODE" dbType="string" Header="物料编码" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="物料名称" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_ID" dbType="string" Header="" FieldType="combox" key="GOODS_{0}" Remark="" index ="10">
    </Field>
    <Field Column="PLAN_LIST_QUANTITY" dbType="decimal" Header="收货数量" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="QuantityUnit" dbType="string" Header="单位" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="PLAN_LIST_ORDERED_QUANTITY" dbType="decimal" Header="下达数量" FieldType="text" key="" Remark="" index ="25">
    </Field>
    <Field Column="PLAN_LIST_FINISHED_QUANTITY" dbType="decimal" Header="完成数量" FieldType="text" key="" Remark="" index ="26">
    </Field>
    <Field Column="GOODS_PROPERTY" dbType="string" Header="" FieldType="text" key="" Remark="" index ="100">
    </Field>
    <Field Column="PurchasingOrder" dbType="string" Header="采购订单" FieldType="text" key="" Remark="" index ="210">
    </Field>
    <Field Column="ReceiveDocItem" dbType="string" Header="收货凭证行项目" FieldType="text" key="" Remark="" index ="220">
    </Field>
    <Field Column="InspectionLot" dbType="string" Header="检验批" FieldType="text" key="" Remark="" index ="230">
    </Field>
    <Field Column="DocumentDate" dbType="string" Header="收货日期" FieldType="text" key="" Remark="" index ="240">
    </Field>
    <Field Column="PLAN_LIST_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="250">
    </Field>
  </Table>

  <Table Name="V_IO_PLAN_LIST_SAP_OUT">
    <Field Column="GOODS_CODE" dbType="string" Header="物料编码" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="物料名称" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_ID" dbType="string" Header="" FieldType="combox" key="GOODS_{0}" Remark="" index ="10">
    </Field>
    <Field Column="PLAN_LIST_QUANTITY" dbType="decimal" Header="需求数量" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="QuantityUnit" dbType="string" Header="单位" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="PLAN_LIST_ORDERED_QUANTITY" dbType="decimal" Header="下达数量" FieldType="text" key="" Remark="" index ="25">
    </Field>
    <Field Column="GOODS_PROPERTY" dbType="string" Header="" FieldType="text" key="" Remark="" index ="100">
    </Field>
    <Field Column="PLAN_LIST_FINISHED_QUANTITY" dbType="decimal" Header="完成数量" FieldType="text" key="" Remark="" index ="26">
    </Field>
    <Field Column="GOODS_PROPERTY" dbType="string" Header="" FieldType="text" key="" Remark="" index ="100">
    </Field>
    <Field Column="ProductID" dbType="string" Header="主机编号" FieldType="text" key="" Remark="" index ="210">
    </Field>
    <Field Column="ProductDesc" dbType="string" Header="主机描述" FieldType="text" key="" Remark="" index ="220">
    </Field>
    <Field Column="UnitQuantity" dbType="decimal" Header="单台用量" FieldType="text" key="" Remark="" index ="250">
    </Field>
    <Field Column="PLAN_LIST_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="260">
    </Field>
  </Table>


  <Table Name="V_ST_STORAGE_LIST">
    <Field Column="WAREHOUSE_CODE" dbType="string" Header="" FieldType="combox" key="WAREHOUSE" Remark="" index ="50">
    </Field>
    <Field Column="AREA_CODE" dbType="string" Header="" FieldType="combox" key="AREA" Remark="" index ="60">
    </Field>
    <Field Column="CELL_ID" dbType="string" Header="" FieldType="combox" key="CELL" Remark="" index ="70">
    </Field>
    <Field Column="STOCK_BARCODE" dbType="string" Header="" FieldType="text" key="" Remark="" index ="80">
    </Field>
    <Field Column="STORAGE_LIST_ID" dbType="string" Header="" FieldType="text" key="" Remark="" index ="1">
    </Field>
    <Field Column="STORAGE_ID" dbType="string" Header="" FieldType="text" key="" Remark="" index ="2">
    </Field>
    <Field Column="PLAN_LIST_ID" dbType="string" Header="" FieldType="text" key="" Remark="" index ="3">
    </Field>
    <Field Column="GOODS_ID" dbType="string" Header="" FieldType="combox" key="GOODS_{0}" Remark="" index ="10">
    </Field>
    <Field Column="GOODS_CODE" dbType="string" Header="物料编码" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="物料名称" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="STORAGE_LIST_QUANTITY" dbType="string" Header="库存数量" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="GOODS_PROPERTY" dbType="string" Header="物料属性" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="GOODS_UNITS" dbType="string" Header="物料单位" FieldType="text" key="" Remark="" index ="40">
    </Field>
    <Field Column="ENTRY_TIME" dbType="string" Header="" FieldType="text" key="" Remark="" index ="90">
    </Field>
    <Field Column="UPDATE_TIME" dbType="string" Header="" FieldType="text" key="" Remark="" index ="100">
    </Field>
    <Field Column="STORAGE_LIST_REMARK" dbType="string" Header="" FieldType="text" key="" Remark="" index ="110">
    </Field>
  </Table>
  
</Fields>
