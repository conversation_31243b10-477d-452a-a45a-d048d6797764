﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
//using System.Windows.Media;
//using System.Windows.Media.Imaging;
using System.Drawing;
using System.Drawing.Printing;
using System.Data;
using SiaSun.LMS.WPFClient.PICK.Model;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_CHECK.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_CHECK : AvalonDock.DocumentContent
    {

        public MANAGE_CHECK()
        {
            InitializeComponent();
        }


        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            LoadEndPosition();
        }

        private void btnCreateDownTask_Click(object sender, RoutedEventArgs e)
        {
            var goodsCodeList = this.tbxGoodsCode.Text.Split(',').ToList();
            if(goodsCodeList.Count <= 0)
            {
                MainApp._MessageDialog.ShowResult(false, "输入的物料编码为空");
                return;
            }

            var boxCount = MainApp._I_BaseService.GetList($@"select count(distinct STOCK_BARCODE) from V_STORAGE_LIST 
                                                             where CELL_MODEL in ('1','2','3','4','5','6')  and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null
                                                             and STOCK_BARCODE not in (select STOCK_BARCODE from MANAGE_MAIN)
                                                             and GOODS_CODE in ('{string.Join("','",goodsCodeList)}') ");

            if (boxCount == null || boxCount.Rows.Count < 1)
            {
                MainApp._MessageDialog.ShowResult(false, "输入的物料编码不存在可盘点库存");
                return;
            }

            if(this.cbxStationCode.SelectedValue == null)
            {
                MainApp._MessageDialog.ShowResult(false, "请选择盘点站台");
                return;
            }

            var message = MainApp._I_ManageService.CreateCheckDownTask(MainApp._USER, this.tbxPlanCode.Text, goodsCodeList, this.cbxStationCode.SelectedValue.ToString());
            if(string.IsNullOrEmpty(message))
            {
                MainApp._MessageDialog.ShowResult(true, "下达成功");

                this.tbxGoodsCode.Text = "";
                this.tbxPlanCode.Text = "";
            }
            else
            {
                MainApp._MessageDialog.ShowResult(false, $"下达失败_信息[{message}]");
            }

        }

        private void LoadEndPosition()
        {

            var endPosition = MainApp._I_BaseService.GetList($@"select CELL_NAME,CELL_CODE from WH_CELL t where cell_type='Station' and cell_inout like '%Out' and CELL_FLAG=1");
            if (endPosition == null)
            {
                return;
            }

            this.cbxStationCode.DisplayMemberPath = "CELL_NAME";
            this.cbxStationCode.SelectedValuePath = "CELL_CODE";
            this.cbxStationCode.ItemsSource = endPosition.DefaultView;
        }

        private void btnLoadStorage_Click(object sender, RoutedEventArgs e)
        {
            this.gridStorageList.U_Clear();

            if(string.IsNullOrEmpty(this.tbxStockBarcode.Text))
            {
                MainApp._MessageDialog.ShowResult(false, "箱条码不能为空");
                return;
            }

            var manageCheck = MainApp._I_BaseService.GetList($"select STOCK_BARCODE from MANAGE_MAIN where STOCK_BARCODE ='{this.tbxStockBarcode.Text}'");
            if(manageCheck!=null && manageCheck.Rows.Count >0)
            {
                MainApp._MessageDialog.ShowResult(false, "箱条码存在管理任务");
                return;
            }

            var controlCheck = MainApp._I_BaseService.GetList($"select STOCK_BARCODE from IO_CONTROL where STOCK_BARCODE ='{this.tbxStockBarcode.Text}'");
            if (controlCheck != null && controlCheck.Rows.Count > 0)
            {
                MainApp._MessageDialog.ShowResult(false, "箱条码存在控制任务");
                return;
            }

            var storageCheck = MainApp._I_BaseService.GetList($"select * from V_STORAGE_LIST where AREA_TYPE='XuNiKu' and STOCK_BARCODE ='{this.tbxStockBarcode.Text}'");
            if(storageCheck==null || storageCheck.Rows.Count < 1)
            {
                MainApp._MessageDialog.ShowResult(false, "箱条码无库存或者库存不在暂存区");
                return;
            }

            var recordInfo = MainApp._I_BaseService.GetList($"select PLAN_CODE from RECORD_MAIN where STOCK_BARCODE='{this.tbxStockBarcode.Text}' and MANAGE_TYPE_CODE='ManageCheckDownLocal' order by RECORD_ID desc");
            if (recordInfo == null || recordInfo.Rows.Count < 1)
            {
                MainApp._MessageDialog.ShowResult(false, $"未找到箱号[{this.tbxStockBarcode.Text}]的记录信息");
                return;
            }
            var planInfo = MainApp._I_BaseService.GetList($"select * from PLAN_MAIN PM inner join PLAN_LIST PL on PM.PLAN_ID=PL.PLAN_ID where PM.PLAN_CODE ='{recordInfo.Rows[0]["PLAN_CODE"].ToString()}'");
            if(recordInfo == null || recordInfo.Rows.Count < 1)
            {
                MainApp._MessageDialog.ShowResult(false, $"未找到箱号[{this.tbxStockBarcode.Text}]的计划信息");
                return;
            }

            var goodsList = planInfo.AsEnumerable().Select(r => r["GOODS_ID"].ToString()).ToList();

            this.gridStorageList.U_WindowName = this.GetType().Name;
            this.gridStorageList.U_TableName = "V_STORAGE_LIST";
            this.gridStorageList.U_XmlTableName = "V_STORAGE_LIST";
            this.gridStorageList.U_OrderField = "STOCK_BARCODE";
            this.gridStorageList.U_Where = string.Format("STOCK_BARCODE='{0}' and GOODS_ID in ({1})", this.tbxStockBarcode.Text, string.Join(",", goodsList));
            this.gridStorageList.U_AllowOperatData = true;
            this.gridStorageList.U_AllowSave = Visibility.Collapsed;
            this.gridStorageList.U_AllowDelete = Visibility.Collapsed;
            this.gridStorageList.U_AllowAdd = Visibility.Collapsed;
            this.gridStorageList.U_AllowChecked = false;
            this.gridStorageList.U_InitControl();

            foreach (DataRowView item in this.gridStorageList.U_DataSource)
            {
                item["MANAGE_LIST_QUANTITY"] = item["STORAGE_LIST_QUANTITY"];
            }
        }


        private void btnSaveChange_Click(object sender, RoutedEventArgs e)
        {
            if (this.gridStorageList.U_DataSource == null)
            {
                MainApp._MessageDialog.ShowResult(false, "请点击[加载库存信息]按钮查询库存信息");
                return;
            }

            var checkPlanInfo = "";
            var recordInfo = MainApp._I_BaseService.GetList($"select PLAN_CODE from RECORD_MAIN where STOCK_BARCODE='{this.tbxStockBarcode.Text}' and MANAGE_TYPE_CODE='ManageCheckDownLocal' order by RECORD_ID desc");
            if (recordInfo == null || recordInfo.Rows.Count < 1)
            {
                MainApp._MessageDialog.ShowResult(false, $"未找到箱号[{this.tbxStockBarcode.Text}]的记录信息");
                return;
            }
            checkPlanInfo = recordInfo.Rows[0]["PLAN_CODE"].ToString();

            this.gridStorageList.U_EndCurrentEdit();

            var checkedData = this.gridStorageList.U_DataSource;

            foreach (DataRowView item in checkedData)
            {
                var refCount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt($@"update STORAGE_LIST set GOODS_PROPERTY9='{checkPlanInfo}' ,  
                                                                                                           GOODS_PROPERTY10='{Common.StringUtil.GetDateTime()}',
                                                                                                           GOODS_PROPERTY11='{item["STORAGE_LIST_QUANTITY"].ToString()}',
                                                                                                           GOODS_PROPERTY12='{item["MANAGE_LIST_QUANTITY"].ToString()}'
                                                                                                           {(item["STORAGE_LIST_QUANTITY"].ToString()!= item["MANAGE_LIST_QUANTITY"].ToString()? ",GOODS_PROPERTY2='1'":"")}
                                                                                   where STORAGE_LIST_ID={item["STORAGE_LIST_ID"].ToString()}");
                if(refCount < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, $"更新物料[{item["GOODS_NAME"].ToString()}]的盘点信息失败");
                }
            }

            MainApp._MessageDialog.ShowResult(true, $"更新库存完成");

            this.tbxStockBarcode.Text = "";
            this.gridStorageList.U_Clear();
        }
    }
}
