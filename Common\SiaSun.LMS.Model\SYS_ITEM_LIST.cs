﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_ITEM_LIST 
	/// </summary>
	[Serializable]
    [DataContract]
	public class SYS_ITEM_LIST
	{
		public SYS_ITEM_LIST()
		{
			
		}
		
		private int _item_list_id;
		private int _item_id;
		private string _item_list_code;
		private string _item_list_name;
		private int _item_list_order;
		private string _item_list_remark;
		private string _item_list_flag;
		
		///<sumary>
		/// 码表列表编号
		///</sumary>
		[DataMember]
        public int ITEM_LIST_ID
		{
			get{return _item_list_id;}
			set{_item_list_id = value;}
		}
		///<sumary>
		/// 码表编号
		///</sumary>
        [DataMember]
        public int ITEM_ID
		{
			get{return _item_id;}
			set{_item_id = value;}
		}
		///<sumary>
		/// 编码
		///</sumary>
        [DataMember]
        public string ITEM_LIST_CODE
		{
			get{return _item_list_code;}
			set{_item_list_code = value;}
		}
		///<sumary>
		/// 名称
		///</sumary>
        [DataMember]
        public string ITEM_LIST_NAME
		{
			get{return _item_list_name;}
			set{_item_list_name = value;}
		}
		///<sumary>
		/// 排序
		///</sumary>
        [DataMember]
        public int ITEM_LIST_ORDER
		{
			get{return _item_list_order;}
			set{_item_list_order = value;}
		}
		///<sumary>
		/// 备注
		///</sumary>
        [DataMember]
        public string ITEM_LIST_REMARK
		{
			get{return _item_list_remark;}
			set{_item_list_remark = value;}
		}
		///<sumary>
		/// 标记
		///</sumary>
        [DataMember]
        public string ITEM_LIST_FLAG
		{
			get{return _item_list_flag;}
			set{_item_list_flag = value;}
		}
	}
}
