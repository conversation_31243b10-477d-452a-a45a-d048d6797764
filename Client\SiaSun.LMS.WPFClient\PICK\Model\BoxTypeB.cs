﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using System.Windows.Media;

namespace SiaSun.LMS.WPFClient.PICK.Model
{
    public class BoxTypeB : Box
    {
        private static readonly List<Brush> defaultPartBackGround = new List<Brush>() {
            new SolidColorBrush(Colors.LightCyan),
            new SolidColorBrush(Colors.LightBlue)
        };

        public BoxTypeB(string stock_barcode) : base(1, 2, Box_Type.B,stock_barcode)
        {
            this.PartBackGround.Clear();

            this.PartBackGround = defaultPartBackGround;

            this.OnChangeBackGround += BoxTypeB_OnChangeBackGround; ;
        }

        private void BoxTypeB_OnChangeBackGround()
        {
            if (this.Select_part_index == -1)
            {
                this.PartBackGround = defaultPartBackGround;

            }
            else if (this.Select_part_index > (this.Part_count - 1))
            {
                this.PartBackGround = defaultPartBackGround;
            }
            else
            {
                List<Brush> brushes = new List<Brush>();
                brushes.AddRange(defaultPartBackGround);
                brushes[Select_part_index] = new SolidColorBrush(Colors.Yellow);
                this.PartBackGround = brushes;
            }
        }
    }
}
