﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 执行结果回传接口
    /// </summary>
    public class handleResultReturnFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _uniqueCode = string.Empty;            //唯一码
            private string _resultCode = string.Empty;      //结果代码
            private string _resultMessage = string.Empty;        //回传消息
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 结果代码
            /// </summary>
            public string resultCode
            {
                get { return _resultCode; }
                set { _resultCode = value; }
            }

            /// <summary>
            /// 回传消息
            /// </summary>
            public string resultMessage
            {
                get { return _resultMessage; }
                set { _resultMessage = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string paraUniqueCode, bool actionResult,string errorText, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();

            try
            {
                inputPara.uniqueCode = paraUniqueCode;
                if (actionResult)
                {
                    inputPara.resultCode = "1";
                    inputPara.resultMessage = "新松提示 成功";
                }
                else
                {
                    inputPara.resultCode = "0";
                    inputPara.resultMessage = string.Format("新松提示 失败 {0}", errorText);
                }
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);
                bResult = this._S_WESJsonService.handleResultReturnFromWCS(strInputParaJson, out sResult);
                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if(outputPara.responseCode!="1")
                    {
                        bResult = false;
                        sResult = string.Format(" handleResultReturnFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("handleResultReturnFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }


        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string paraUniqueCode, out string sResult)
        {
            return this.NotifyMethod(paraUniqueCode, true, string.Empty, out sResult);
        }
    }
}
