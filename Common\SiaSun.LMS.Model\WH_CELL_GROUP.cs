﻿/***************************************************************************
 * 
 *       功能：     仓库货位组实体类
 *       作者：     Siasun
 *       日期：     2025/4/08
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// WH_CELL_GROUP 
	/// </summary>
    [Serializable]
    [DataContract]
	public class WH_CELL_GROUP
	{
		public WH_CELL_GROUP()
		{
			
		}
		
		private int _group_id;		
		private string _pick_no;		
		private int _avail_loc_num;
		private string _group_status;
		private string _group_code;
		private string _group_type;
		
		
		///<sumary>
		/// 主键
        ///</sumary>
        [DataMember]
		public int GROUP_ID
		{
			get{return _group_id;}
			set{_group_id = value;}
		}
		
		///<sumary>
		/// 拣选单号
        ///</sumary>
        [DataMember]
		public string PICK_NO
		{
			get{return _pick_no;}
			set{_pick_no = value;}
		}
		
		///<sumary>
		/// 可用货位数量
        ///</sumary>
        [DataMember]
		public int AVAIL_LOC_NUM
        {
			get{return _avail_loc_num;}
			set{_avail_loc_num = value;}
		}
		///<sumary>
		/// 货位组状态
        ///</sumary>
        [DataMember]
		public string GROUP_STATUS
        {
			get{return _group_status;}
			set{_group_status = value;}
		}

        ///<sumary>
        /// 货位组状态
        ///</sumary>
        [DataMember]
        public string GROUP_CODE
        {
            get { return _group_code; }
            set { _group_code = value; }
        }

        ///<sumary>
        /// 货位组类型（开放/预留）
        ///</sumary>
        [DataMember]
        public string GROUP_TYPE
        {
            get { return _group_type; }
            set { _group_type = value; }
        }
    }
}
