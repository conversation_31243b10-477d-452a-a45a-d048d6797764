﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 一楼申请：710113分配楼层
    /// </summary>
    public class ApplyAllocateFloor : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateFloor.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("条码{0}未下达任务 请查看当前任务中是否有此条码的记录", mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd());
                    return bResult;
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                Model.MANAGE_LIST mMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID)[0];

                //没有分配下一站台，则根据任务类型决定去哪个楼层
                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                {
                    //wdz add 2019-04-10 写在配置里
                    string noneMiniloadEndPositionConfig = string.Empty;
                    if (!this._S_SystemService.GetSysParameter("NoneMiniloadEndPositionConfig", out noneMiniloadEndPositionConfig) ||
                        string.IsNullOrEmpty(noneMiniloadEndPositionConfig))
                    {
                        bResult = false;
                        sResult = "未找到系统参数[NoneMiniloadEndPositionConfig]_无法判断非立库输送任务的下一个站台";
                        return bResult;
                    }

                    if (mMANAGE_LIST != null && !noneMiniloadEndPositionConfig.Contains(mMANAGE_LIST.GOODS_PROPERTY8))
                    {
                        //非立库输送任务下达关联管理任务的Control任务
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoadTrans", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), false }, out sResult);
                        return bResult;
                    }
                }

                //2020-09-22 16:26:56 根据越库标记来决定取三楼还是五楼
                ////其他任务类型下达到货位申请站台的Control任务
                //string cellAllocateStation = string.Empty;
                //bResult = this._S_SystemService.GetSysParameter("MoveToFloor5EndStation", out cellAllocateStation);
                //if (!bResult)
                //{
                //    sResult = "分配楼层时未找到默认的货位申请站台";
                //    return bResult;
                //}
                string cellAllocateStation = string.Empty;
                if (!this._S_SystemService.GetSysParameter(mMANAGE_MAIN.CROSS_FLAG == "3" ? "MoveToFloor3EndStation" : "MoveToFloor5EndStation", out  cellAllocateStation))
                {
                    bResult = false;
                    sResult = $"分配楼层时未找到默认的货位申请站台_配置项[{(mMANAGE_MAIN.CROSS_FLAG == "3" ? "MoveToFloor3EndStation" : "MoveToFloor5EndStation")}]";
                    return bResult;
                }

                bResult = this._S_ManageService.ControlCreate(3, mIO_CONTROL_APPLY.STOCK_BARCODE, "1", mIO_CONTROL_APPLY.DEVICE_CODE, "1", cellAllocateStation, "5", out sResult, false, true);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_Apply1", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                //wdz add 2018-04-12 分配楼层申请处理失败时更新任务状态
                if (mMANAGE_MAIN != null)
                {
                    if (bResult)
                    {
                        if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.WaitingExecute.ToString())
                        {
                            mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Executing.ToString();
                            this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                        }
                    }
                    else
                    {
                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();
                        mMANAGE_MAIN.MANAGE_REMARK = sResult;
                        this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                    }
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateFloor.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
