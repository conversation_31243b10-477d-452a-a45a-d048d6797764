﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{   

    /// <summary>
    /// ucManageInInput.xaml 的交互逻辑
    /// </summary>
    public partial class ucManageInInput : UserControl
    {
        /// <summary>
        /// 定义显示类型
        /// </summary>
        public enum ShowType
        {
            //Goods = 1,                              //单一显示物料
            //ClassGoods = 2,                     //根据类别显示物料
            //PlanGoods = 3,                      //计划物料
            //StorageGoods = 4,               //库存物料
            //TemplateGoods = 5             //配盘模板物料
            Goods,                              //单一显示物料
            ClassGoods,                     //根据类别显示物料
            PlanGoods,                      //计划物料
            StorageGoods,               //库存物料
            TemplateGoods              //配盘模板物料
        }

        #region     ------定义变量属性

        private string strWindowName = string.Empty;
        private SiaSun.LMS.Enum.PLAN_INOUT enumPlanInOut = SiaSun.LMS.Enum.PLAN_INOUT.In;
        private int intPlanID = 0;
        private DataRowView[] arDataRow = null;
        private string enumShowType = ShowType.Goods.ToString();
        //tzyg add 2017-03-16
        //控制是否显示物料位置模板图片窗体
        private bool isShowGoodsPositionTemplate = false;
        //tzyg add 2017-03-23
        //记录是否已经初始化查询空间ucQuery
        private bool isInitUcQuery = false;

        public delegate void U_CheckedDataGridRowsHandler();
        public event U_CheckedDataGridRowsHandler U_CheckedDataGridRows;


        /// <summary>
        /// 显示类型
        /// </summary>
        public string U_ShowType
        {
            get { return this.enumShowType; }
            set { this.enumShowType = value; }
        }

        /// <summary>
        /// 窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return strWindowName; }
            set { strWindowName = value; }
        }

        /// <summary>
        /// 计划出入类型
        /// </summary>
        public SiaSun.LMS.Enum.PLAN_INOUT U_PLAN_OUT
        {
            get { return enumPlanInOut; }
            set { enumPlanInOut = value; }
        }

        /// <summary>
        /// 计划编号
        /// </summary>
        public int U_PLAN_ID
        {
            get { return intPlanID; }
            set { intPlanID = value; }
        }

        /// <summary>
        /// 选中的记录行
        /// </summary>
        public DataRowView[] U_CheckedDataRowViews
        {
            get { return arDataRow; }
            set { arDataRow = value; }
        }

        /// <summary>
        /// tzyg add 2017-03-16
        /// 控制是否显示物料位置模板图片窗体
        /// </summary>
        public bool U_IsShowGoodsPositionTemplate
        {
            get { return isShowGoodsPositionTemplate; }
            set { isShowGoodsPositionTemplate = value; }
        }

        #endregion

        public ucManageInInput()
        {
            InitializeComponent();

            this.ucQuery.U_Query += new ucQuickQuery.U_QueryEventHandler(ucQuery_U_Query);
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //默认选择绑定数据
            //this.Data_Bind(string.Empty);       

            this.grpbGrid.Content = null;

            //判断控件
            switch (this.enumShowType)
            {
                case "Goods":
                    this.InitQueryControl("GOODS_MAIN");
                    break;
                case "ClassGoods":
                    this.InitQueryControl("GOODS_MAIN");
                    break;
                case "PlanGoods":
                    this.InitQueryControl("V_PLAN_LIST");
                    break;
                case "StorageGoods":
                    this.InitQueryControl("V_STORAGE_LIST");
                    break;
                case "TemplateGoods":
                    this.InitQueryControl("V_GOODS_TEMPLETE_LIST");
                    break;
            }
            //tzyg add 2-17-03-23
            this.isInitUcQuery = true;
        }

        #region     -------查询控件

        /// <summary>
        /// 添加确认按钮
        /// </summary>
        private void AddConfirmButton()
        {
            //添加确认按钮
            Button btnConfirm = new Button();
            btnConfirm.Width = 70;
            btnConfirm.Content = "添加选中";
            btnConfirm.Template = (ControlTemplate)MainApp.GetStyleResource("templateImageButtonAdd");
            btnConfirm.Click += new RoutedEventHandler(btnConfirm_Click);
            this.ucQuery.U_AddControl(btnConfirm);
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl(string TableName)
        {
            this.ucQuery.U_WindowName = strWindowName;
            this.ucQuery.U_XmlTableName = TableName;
            this.ucQuery.U_InitControl();

            //添加确认按钮
            this.AddConfirmButton();
        }

        /// <summary>
        /// 查询控件
        /// </summary>
        void ucQuery_U_Query(string QueryWhere)
        {
               this.Data_Bind(QueryWhere);
        }

        #endregion

        /// <summary>
        /// 控件数据绑定
        /// </summary>
        public void Data_Bind(string QueryWhere)
        {
            //清除所有控件
            this.grpbGrid.Content = null;

            //判断控件
            switch (this.enumShowType)
            { 
                case "Goods":
                    this.LoadGoodsControl(QueryWhere);
                    break;
                case "ClassGoods":
                    this.LoadClassGoodsControl(QueryWhere);
                    break;
                case "PlanGoods":
                    this.LoadPlanGoodsControl(QueryWhere);
                    break;
                case "StorageGoods":
                    this.LoadStorageGoodsControl(QueryWhere);                   
                    break;
                case "TemplateGoods":
                    this.LoadTemplateGoodsControl(QueryWhere);
                    break;
            }        
        }
        
        #region     ------根据不同显示类型，显示不同控件

        /// <summary>
        /// 加载物料信息
        /// </summary>
        private void LoadGoodsControl(string QueryWhere)
        {
            //tzyg add 2017-03-23
            if (!isInitUcQuery)
            {
                //设置查询控件 
                this.InitQueryControl("GOODS_MAIN");
            }

            ucCommonDataGrid control = new ucCommonDataGrid();
            control.U_XmlTableName = "GOODS_MAIN";
            control.U_TableName = "V_GOODS";
            control.U_Where = "GOODS_CODE!='tuopan' AND GOODS_FLAG=1 " + (QueryWhere.Length == 0 ? string.Empty : " AND " + QueryWhere);
            
            control.U_AllowOperatData = false;
            control.U_AllowChecked = true;
            control.U_InitControl();
            //tzyg add 2017-03-16
            control.U_IsShowGoodsPositionTemplate = this.isShowGoodsPositionTemplate;

            //双击事件
            control.gridApp.MouseDoubleClick+=new MouseButtonEventHandler(gridApp_MouseDoubleClick);
            //加载控件
            this.grpbGrid.Content = control;
        }

        /// <summary>
        /// 加载物料类别依据物料
        /// </summary>
        private void LoadClassGoodsControl(string QueryWhere)
        {
            //设置查询控件 
            this.InitQueryControl("GOODS_MAIN");

            //设置控件属性
            ucMdiTreeViewDataGridControl control = new ucMdiTreeViewDataGridControl();
            //注册双节事件
            control.U_DoubleClick += new ucMdiTreeViewDataGridControl.U_DoubleClickEventHandler(control_U_DoubleClick);
            this.grpbGrid.Content = control;

            control.U_AllowDoubleClick = true;
            control.U_AllowChecked = true;
            control.U_AllowOperation = false;

            control.U_LeftHeader = "物料类别";
            control.U_LeftTable = "GOODS_CLASS";
            control.U_LeftWhere = "GOODS_CLASS_FLAG=1";
            control.U_LeftNodeTagColumn = "GOODS_CLASS_ID";
            control.U_LeftNodeTextColumn = "GOODS_CLASS_NAME";
            control.U_LeftNodeRelativeColumn = "GOODS_CLASS_PARENT_ID";

            control.U_RightTable = "GOODS_MAIN";
            control.U_RightWhere = "GOODS_FLAG=1 " + (QueryWhere.Length == 0 ? string.Empty : " AND " + QueryWhere);
            control.U_RigthTagColumn = "GOODS_CLASS_ID";

            //初始化
            control.U_InitControl();

            //定义双击事件
            this.Init_Mdi_DoubleClick(control);

            //加载控件
            this.grpbGrid.Content = control;
        }

        /// <summary>
        /// 加载配盘模板物料
        /// </summary>
        private void LoadTemplateGoodsControl(string QueryWhere)
        {
            //设置查询控件 
            this.InitQueryControl("V_GOODS_TEMPLETE_LIST");

            //设置控件属性
            ucMdiTreeViewDataGridControl control = new ucMdiTreeViewDataGridControl();
            //注册双节事件
            control.U_DoubleClick += new ucMdiTreeViewDataGridControl.U_DoubleClickEventHandler(control_U_DoubleClick);
            this.grpbGrid.Content = control;

            control.U_AllowDoubleClick = true;
            control.U_AllowChecked = true;
            control.U_AllowOperation = false; 

            control.U_LeftHeader = "配盘方案列表";
            control.U_LeftTable = "GOODS_TEMPLETE";
            control.U_LeftWhere = "GOODS_TEMPLETE_FLAG=1";
            control.U_LeftNodeTagColumn = "GOODS_TEMPLETE_ID";
            control.U_LeftNodeTextColumn = "GOODS_TEMPLETE_NAME";
            control.U_LeftNodeRelativeColumn = "GOODS_TEMPLETE_ID";
            control.U_LeftNodeRelativeColumn = string.Empty;

            control.U_RightTable = "V_GOODS_TEMPLETE_LIST";
            control.U_RightWhere = this.ucQuery.U_GetQuery();
            control.U_RigthTagColumn = "GOODS_TEMPLETE_ID";
            control.U_RightWhere = QueryWhere;

            control.U_SplitGroupColumn = "GOODS_TYPE_ID";
            control.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            control.U_SplitPropertyColumn = "GOODS_PROPERTY";
            control.U_SplitPropertyType = "GOODS_TYPE";           

            //初始化
            control.U_InitControl();

            //定义双击事件
            this.Init_Mdi_DoubleClick(control);

            //加载控件
            this.grpbGrid.Content = control;
        }

        /// <summary>
        /// 加载计划依据物料
        /// </summary>
        private void LoadPlanGoodsControl(string QueryWhere)
        {
            //设置查询控件 
            this.InitQueryControl("V_PLAN_LIST");

            ucSplitPropertyGridTab control = new ucSplitPropertyGridTab();
            this.grpbGrid.Content = control;

            control.U_AllowChecked = true;
            control.U_AllowOperatData = false;

            control.U_TableName = "V_PLAN_LIST";
            control.U_XmlTableName = "V_PLAN_LIST";
            control.U_OrderField = "PLAN_LIST_ID";
            control.U_Where = intPlanID > 0 ? string.Format("PLAN_ID={0}", intPlanID) : string.Empty;
            //control.U_Where = QueryWhere;
            control.U_AppendWhere = QueryWhere;

            control.U_SplitGroupColumn = "GOODS_TYPE_ID";
            control.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            control.U_SplitPropertyColumn = "GOODS_PROPERTY";
            control.U_SplitPropertyType = "GOODS_TYPE";

            //初始化
            control.U_InitControl();

            //定义双击事件
            Init_DataGrid_DoubleClick(control);

            //加载控件
            this.grpbGrid.Content = control;
        }

        /// <summary>
        /// 加载库存依据物料
        /// </summary>
        private void LoadStorageGoodsControl(string QueryWhere)
        {
            //设置查询控件 
            //this.InitQueryControl("V_STORAGE_LIST");

            //获得查询条件
            string strWhere = this.ucQuery.U_GetQuery();
            //strWhere = (intPlanID > 0) ? string.Format("GOODS_ID IN(SELECT GOODS_ID FROM PLAN_LIST WHERE PLAN_ID={0}){1}", intPlanID, (string.IsNullOrEmpty(strWhere) ? string.Empty : string.Format(" AND {0}", strWhere))) : strWhere;

            ucSplitPropertyGridTab control = new ucSplitPropertyGridTab();
            this.grpbGrid.Content = control;

            control.U_AllowChecked = true;
            control.U_AllowOperatData = false;

            control.U_TableName = "V_STORAGE_LIST";
            control.U_XmlTableName = "V_STORAGE_LIST";
            control.U_OrderField = "STORAGE_LIST_ID";
            control.U_Where = string.Format("AREA_TYPE = '{0}'", "XuNiKu");
            control.U_AppendWhere = QueryWhere;

            control.U_SplitGroupColumn = "GOODS_TYPE_ID";
            control.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            control.U_SplitPropertyColumn = "GOODS_PROPERTY";
            control.U_SplitPropertyType = "GOODS_TYPE";

            //初始化
            control.U_InitControl();

            //定义双击事件
            Init_DataGrid_DoubleClick(control);

            //加载控件
            this.grpbGrid.Content = control;
        }

        #endregion


        #region     ------双击事件

        /// <summary>
        /// 初始化控件的双击事件
        /// </summary>
        private void Init_DataGrid_DoubleClick(ucSplitPropertyGridTab control)
        {
            foreach (TabItem pageItem in control.tabSplitProperty.Items)
            {
                if (pageItem.Content != null)
                {
                    ucCommonDataGrid grid = pageItem.Content as UC.ucCommonDataGrid;
                    if (grid != null)
                    {
                        grid.gridApp.MouseDoubleClick += new MouseButtonEventHandler(gridApp_MouseDoubleClick);
                    }
                }
            }
        }

        /// <summary>
        /// 初始化双击事件
        /// </summary>
        private void Init_Mdi_DoubleClick(ucMdiTreeViewDataGridControl control)
        {
            if (control.grpboxChild.HasContent)
            {
                if (string.IsNullOrEmpty(control.U_SplitPropertyColumn))
                {
                    ucCommonDataGrid ucGrid = control.grpboxChild.Content as ucCommonDataGrid;
                    if (ucGrid != null)
                    {
                        ucGrid.gridApp.MouseDoubleClick += new MouseButtonEventHandler(gridApp_MouseDoubleClick);
                    }
                }
                else
                {
                    ucSplitPropertyGridTab ucSplitGrid = control.grpboxChild.Content as ucSplitPropertyGridTab;
                    if (ucSplitGrid != null)
                    {
                        this.Init_DataGrid_DoubleClick(ucSplitGrid);
                    }
                }
            }
        }

        /// <summary>
        /// 双击选项，添加选项
        /// </summary>
        void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            DataGridTemplate grid = e.Source as DataGridTemplate;
            if (grid != null && grid.SelectedItem != null)
            {
                //获得数据绑定行
                grid.U_CheckedRow(true, (grid.SelectedItem as DataRowView));

                //确定返回
                this.Confirm();
            }
        }

        //双击添加记录
        void control_U_DoubleClick()
        {
            this.Confirm();
        }

        #endregion

        /// <summary>
        /// 确认按钮
        /// </summary>
        void btnConfirm_Click(object sender, RoutedEventArgs e)
        {
            this.Confirm();
        }

        /// <summary>
        /// 确认选择
        /// </summary>
        private void Confirm()
        {
            if (this.grpbGrid.Content != null)
            {
                object objControl = this.grpbGrid.Content;

                //判断控件类型
                if (objControl is ucCommonDataGrid)
                {
                    ucCommonDataGrid control = objControl as ucCommonDataGrid;
                    if (control != null)
                    {
                        arDataRow = control.U_GetCheckedDataRows();
                        control.U_RejectCheckedRow();
                    }
                }

                if (objControl is ucSplitPropertyGridTab)      
                {
                    ucSplitPropertyGridTab control = grpbGrid.Content as ucSplitPropertyGridTab;
                    if (control != null)
                    {
                        arDataRow = control.U_GetCheckedDataRows();
                        control.U_RejectCheckedRow();
                    }
                }

                if (objControl is ucMdiTreeViewDataGridControl)
                {
                    ucMdiTreeViewDataGridControl control = grpbGrid.Content as ucMdiTreeViewDataGridControl;
                    arDataRow = control.U_GetCheckedDataRows();
                    control.U_RejectCheckedRow();
                }

                //选定记录
                if (this.U_CheckedDataGridRows != null)
                {
                    this.U_CheckedDataGridRows();
                }
            }
        }
    }
}
