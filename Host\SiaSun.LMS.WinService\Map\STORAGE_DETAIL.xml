﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="STORAGE_DETAIL" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="STORAGE_DETAIL" type="SiaSun.LMS.Model.STORAGE_DETAIL, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="STORAGE_DETAIL">
			<result property="STORAGE_DETAIL_ID" column="storage_detail_id" />
			<result property="STORAGE_LIST_ID" column="storage_list_id" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_BARCODE" column="goods_barcode" />
			<result property="STORAGE_DETAIL_REMARK" column="storage_detail_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	<select id="STORAGE_DETAIL_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  storage_detail_id,
				  storage_list_id,
				  box_barcode,
				  goods_barcode,
				  storage_detail_remark
			From STORAGE_DETAIL
	</select>
		
		<select id="STORAGE_DETAIL_SELECT_BY_ID" parameterClass="int" extends = "STORAGE_DETAIL_SELECT" resultMap="SelectResult">		
			<dynamic prepend="WHERE">
				<isParameterPresent>
					storage_detail_id=#STORAGE_DETAIL_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="STORAGE_DETAIL_SELECT_BY_GOODS_BARCODE" parameterClass="string" extends = "STORAGE_DETAIL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          GOODS_BARCODE = #GOODS_BARCODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_DETAIL_SELECT_BY_STORAGE_LIST_ID" parameterClass="int" extends = "STORAGE_DETAIL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_list_id=#STORAGE_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="STORAGE_DETAIL_INSERT" parameterClass="STORAGE_DETAIL">
      Insert Into STORAGE_DETAIL (
      storage_detail_id,
      storage_list_id,
      box_barcode,
      goods_barcode,
      storage_detail_remark
      )Values(
      #STORAGE_DETAIL_ID#,
      #STORAGE_LIST_ID#,
      #BOX_BARCODE#,
      #GOODS_BARCODE#,
      #STORAGE_DETAIL_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="STORAGE_DETAIL_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="STORAGE_DETAIL_UPDATE" parameterClass="STORAGE_DETAIL">
      Update STORAGE_DETAIL Set
      storage_detail_id=#STORAGE_DETAIL_ID#,
      storage_list_id=#STORAGE_LIST_ID#,
      box_barcode=#BOX_BARCODE#,
      goods_barcode=#GOODS_BARCODE#,
      storage_detail_remark=#STORAGE_DETAIL_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					storage_detail_id=#STORAGE_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="STORAGE_DETAIL_DELETE" parameterClass="int">
			Delete From STORAGE_DETAIL
			<dynamic prepend="WHERE">
				<isParameterPresent>
					storage_detail_id=#STORAGE_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

    <delete id="STORAGE_DETAIL_DELETE_STORAGE_LIST_ID" parameterClass="int">
      Delete From STORAGE_DETAIL
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_list_id=#STORAGE_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </delete>
		
	</statements>
</sqlMap>