﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_ROLE
	/// </summary>
	public class P_SYS_ROLE : P_Base_House
	{
		public P_SYS_ROLE ()
		{
			//
			// TODO: 此处添加SYS_ROLE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_ROLE> GetList()
		{
			return ExecuteQueryForList<SYS_ROLE>("SYS_ROLE_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_ROLE> GetList(int USER_ID)
        {
            return this.ExecuteQueryForList<SYS_ROLE>("SYS_ROLE_SELECT_BY_USER_ID", USER_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_ROLE sys_role)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("SYS_ROLE");
                sys_role.ROLE_ID = id;
            }

            return ExecuteInsert("SYS_ROLE_INSERT",sys_role);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_ROLE sys_role)
		{
			return ExecuteUpdate("SYS_ROLE_UPDATE",sys_role);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_ROLE GetModel(System.Int32 ROLE_ID)
		{
			return ExecuteQueryForObject<SYS_ROLE>("SYS_ROLE_SELECT_BY_ID",ROLE_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 ROLE_ID)
		{
			return ExecuteDelete("SYS_ROLE_DELETE",ROLE_ID);
		}
		

	}
}
