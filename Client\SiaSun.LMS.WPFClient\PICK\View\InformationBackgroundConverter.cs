﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Windows;

namespace SiaSun.LMS.WPFClient.PICK.View
{
    public class InformationBackgroundConverter : System.Windows.Data.IValueConverter
    {
        public System.Windows.Media.Brush HighlightBrush
        {
            get;set;
        }
        public  System.Windows.Media.Brush DefaultBrush
        {
            get;set;
        }
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isHighlight = (bool)value;
            if (isHighlight)
            {
                return HighlightBrush;
            }
            else
            {
                return DefaultBrush;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }

    public class VisiabilityConverter: System.Windows.Data.IValueConverter
    {
        
        public object Convert(object value, Type targetType, object parameter, CultureInfo culture)
        {
            bool isShow = (bool)value;
            if (isShow)
            {
                return Visibility.Visible;
            }
            else
            {
                return Visibility.Collapsed;
            }
        }

        public object ConvertBack(object value, Type targetType, object parameter, CultureInfo culture)
        {
            throw new NotImplementedException();
        }
    }
}
