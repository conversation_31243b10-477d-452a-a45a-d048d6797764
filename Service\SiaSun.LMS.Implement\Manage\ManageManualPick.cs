﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 人工拣选任务
    /// </summary>
    public class ManageManualPick : ManageBase
    {
        //public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        //{
        //    bool bResult = true;
        //    sResult = string.Empty;

        //    try
        //    {
        //        this._P_Base_House.BeginTransaction(bTrans);

        //        Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
        //        if (mMANAGE_MAIN == null)
        //        {
        //            bResult = false;
        //            sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
        //            return bResult;
        //        }
        //        List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

        //        if (MANAGE_LISTS.Count != 1)
        //        {
        //            bResult = false;
        //            sResult = string.Format("任务ID{0}对应的任务明细不符合规定", MANAGE_ID.ToString());
        //            return bResult;
        //        }

             

        //        //更新计划已拣选数量
        //        foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
        //        {
        //            Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

        //            if (mPLAN_LIST != null)
        //            {
        //                mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;
        //                this._P_PLAN_LIST.Update(mPLAN_LIST);
        //            }

        //            //删除或修改锁定表数量
        //            var storageLock = this._P_STORAGE_LOCK.GetModel(mMANAGE_LIST.STORAGE_LOCK_ID);
        //            if (storageLock == null)
        //            {
        //                bResult = false;
        //                sResult = string.Format("未找到锁定列表{0}", mMANAGE_LIST.STORAGE_LOCK_ID);
        //                return bResult;
        //            }

        //            this._log.DebugFormat(@"[人工拣选] 拣选任务完成时，单据列表{0}锁定列表{1}库存列表{2}锁定数量{3}单据数量{4},条码{5}"
        //                    , mMANAGE_LIST.PLAN_LIST_ID
        //                    , mMANAGE_LIST.STORAGE_LOCK_ID
        //                    , mMANAGE_LIST.STORAGE_LIST_ID
        //                    , storageLock.STORAGE_LOCK_QUANTITY
        //                    , mMANAGE_LIST.MANAGE_LIST_QUANTITY
        //                    , mMANAGE_MAIN.STOCK_BARCODE);

        //            if (storageLock.STORAGE_LOCK_QUANTITY.Equals(mMANAGE_LIST.MANAGE_LIST_QUANTITY))
        //            {
        //                this._P_STORAGE_LOCK.Delete(mMANAGE_LIST.STORAGE_LOCK_ID);
        //            }
        //            else
        //            {
        //                storageLock.STORAGE_LOCK_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
        //                this._P_STORAGE_LOCK.Update(storageLock);
        //            }
        //        }

        //        bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
        //    }
        //    catch (Exception ex)
        //    {
        //        bResult = false;
        //        sResult = string.Format("ManagePick完成时发生异常 {0},EX203", ex.Message);
        //    }
        //    finally
        //    {
        //        if (bResult)
        //        {
        //            this._P_Base_House.CommitTransaction(bTrans);
        //        }
        //        else
        //        {
        //            this._P_Base_House.RollBackTransaction(bTrans);
        //        }
        //    }
        //    return bResult;
        //}

        public new  bool ManageCancel(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                    return bResult;
                }
                List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

                if (MANAGE_LISTS.Count==0)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务明细不符合规定", MANAGE_ID.ToString());
                    return bResult;
                }

                foreach(Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
                {
                    List<Model.MANAGE_DETAIL> MANAGE_DETAILS = this._P_MANAGE_DETAIL.GetListManageListID(mMANAGE_LIST.MANAGE_LIST_ID).ToList();

                    if (MANAGE_DETAILS.Count > 0)
                    {
                        foreach(Model.MANAGE_DETAIL mMANAGE_DETAIL in MANAGE_DETAILS)
                        {
                            this._P_MANAGE_DETAIL.Delete(mMANAGE_DETAIL.MANAGE_DETAIL_ID);
                        }
                    }

                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);

                    var planList = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);
                    if(planList != null)
                    {
                        planList.PLAN_LIST_ORDERED_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        this._P_PLAN_LIST.Update(planList);
                    }
                }

                this._P_MANAGE_MAIN.Delete(mMANAGE_MAIN.MANAGE_ID);

             
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManagePick取消时发生异常 {0},EX204", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }
    }
}
