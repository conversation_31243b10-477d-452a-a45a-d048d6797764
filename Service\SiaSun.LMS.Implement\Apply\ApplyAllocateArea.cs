﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 五楼分配库区申请
    /// </summary>
    public class ApplyAllocateArea : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateArea.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                string strStorage = string.Format("select STORAGE_ID from STORAGE_MAIN where STOCK_BARCODE = '{0}'", mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd());
                DataTable dtStorage = GetList(strStorage);

                if (mMANAGE_MAIN != null )
                {
                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                    if (mMANAGE_TYPE == null)
                    {
                        bResult = false;
                        sResult = string.Format("未找到任务类型_箱条码[{0}]_任务类型编码[{1}]", mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.MANAGE_TYPE_CODE);
                        return bResult;
                    }

                    IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);
                    if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count < 1)
                    {
                        bResult = false;
                        sResult = string.Format("未找到申请条码对应的任务列表信息_箱条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    //没有分配下一站台，则根据任务类型决定去哪个楼层
                    string nextStation = string.Empty;
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                    {
                        string noneMiniloadEndPositionConfig = string.Empty;
                        if (!this._S_SystemService.GetSysParameter("NoneMiniloadEndPositionConfig", out noneMiniloadEndPositionConfig) ||
                            string.IsNullOrEmpty(noneMiniloadEndPositionConfig))
                        {
                            bResult = false;
                            sResult = "未找到系统参数[NoneMiniloadEndPositionConfig]_无法判断非立库输送任务的下一个站台";
                            return bResult;
                        }

                        if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count < 1)
                        {
                            bResult = false;
                            sResult = string.Format("任务列表信息有误");
                            return bResult;
                        }

                        foreach (var item in noneMiniloadEndPositionConfig.Split(';'))
                        {
                            if (item.Contains(lsMANAGE_LIST[0].GOODS_PROPERTY8))
                            {
                                nextStation = item.Split('-')[1];
                                break;
                            }
                        }
                    }
                    else
                    {
                        //获取库区分配使能状态
                        string miniloadAreaStatus = string.Empty;
                        if (!this._S_SystemService.GetSysParameter("MiniloadAreaStatus", out miniloadAreaStatus))
                        {
                            miniloadAreaStatus = "'LKQ_01','LKQ_02'";
                        }

                        //准备校验路径
                        string allocateCellApplyStation1 = string.Empty;
                        string allocateCellApplyStation2 = string.Empty;
                        if (!this._S_SystemService.GetSysParameter("AllocateCellApplyStation-1", out allocateCellApplyStation1) ||
                            !this._S_SystemService.GetSysParameter("AllocateCellApplyStation-2", out allocateCellApplyStation2))
                        {
                            bResult = false;
                            sResult = string.Format("未获取库区的申请站台");
                            return bResult;
                        }

                        //Dictionary<string, string> dicAllocateCellApplyStation = new Dictionary<string, string>();
                        //dicAllocateCellApplyStation.Add("12001", allocateCellApplyStation1);
                        //dicAllocateCellApplyStation.Add("1")

                        //其他任务类型下达到货位申请站台的Control任务
                        //获取箱中每个物料的分布情况
                        List<string> lsAreaCode = new List<string>();
                        foreach (var item in lsMANAGE_LIST)
                        {
                            string sql = string.Format(@"select * from (
                                            select AREA_CODE,
                                                   NVL((select count(0) from STORAGE_LIST A left join STORAGE_MAIN B on A.STORAGE_ID = B.STORAGE_ID left join WH_CELL C on B.CELL_ID = C.CELL_ID left join WH_AREA D on C.AREA_ID = D.AREA_ID where D.AREA_ID=T.AREA_ID and B.CELL_MODEL not in ('99' ,'100') and GOODS_ID = {0} ),0) as BOX_COUNT,
                                                   NVL((select count(0) from WH_CELL E where E.AREA_ID = T.AREA_ID and CELL_TYPE='Cell' and CELL_STATUS='Nohave' and RUN_STATUS='Enable' and E.DEVICE_CODE in (select END_DEVICE from IO_CONTROL_ROUTE where START_DEVICE in {1} and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE = 1)), 0) as ENABLE_CELL,
                                                   NVL((select count(0) from IO_CONTROL_ROUTE where CONTROL_ROUTE_CODE = T.ROUTE_RELA and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE = 1),0) as ENABLE_ROUTE_COUNT
                                            from WH_AREA T)
                                         where ENABLE_CELL > 0 and AREA_CODE in ({2}) and ENABLE_ROUTE_COUNT>0
                                         order by BOX_COUNT ,ENABLE_CELL desc",
                                                 item.GOODS_ID,
                                                 string.Format("('{0}','{1}')", allocateCellApplyStation1, allocateCellApplyStation2),
                                                 miniloadAreaStatus
                                                 );

                            DataTable dtAreaSelect = this.GetList(sql);
                            if (dtAreaSelect == null || dtAreaSelect.Rows.Count < 1)
                            {
                                bResult = false;
                                sResult = string.Format("未能找到可用库区_请查看路径状态和库区的使能状态_物料ID[{0}]", item.GOODS_ID);
                                return bResult;
                            }

                            lsAreaCode.Add(dtAreaSelect.Rows[0]["AREA_CODE"].ToString());
                        }
                        lsAreaCode.Sort();
                        string selectedArea = lsAreaCode[lsAreaCode.Count / 2];

                        foreach (var item in applyTypeParam.U_NextStation.Split(';'))
                        {
                            if (item.Contains(selectedArea))
                            {
                                nextStation = item.Split('-')[1];
                                break;
                            }
                        }
                    }

                    if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.WaitingExecute.ToString() ||
                        mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.WaitingSend.ToString())
                    {
                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Executing.ToString();
                        this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                    }


                    if (string.IsNullOrEmpty(nextStation))
                    {
                        bResult = false;
                        sResult = string.Format("未能找到下一站台");
                        return bResult;
                    }
                    bResult = this._S_ManageService.ControlCreate(3, mIO_CONTROL_APPLY.STOCK_BARCODE, "1", mIO_CONTROL_APPLY.DEVICE_CODE, "1", nextStation, "5", out sResult, false, false);
                }
                //不存在管理任务，存在库存，3楼-5楼齐套箱退库
                
                else if(dtStorage != null && dtStorage.Rows.Count > 0)
                {
                    //比较1期2期空货位数，分配到空货位多的一侧
                    string nextStation = string.Empty;
                    string strLKQ_01Enable = string.Format("select ENABLE_QTY,AREA_CODE from V_CELL_THRESHOLD_AREA where AREA_ID = 1");
                    int LKQ_01Enable = 0;
                    DataTable dtLKQ_01 = GetList(strLKQ_01Enable);
                    if(dtLKQ_01 != null && dtLKQ_01.Rows.Count > 0)
                    {
                        LKQ_01Enable = Convert.ToInt32(dtLKQ_01.Rows[0]["ENABLE_QTY"].ToString());
                    }

                    string strLKQ_02Enable = string.Format("select ENABLE_QTY,AREA_CODE from V_CELL_THRESHOLD_AREA where AREA_ID = 3");
                    int LKQ_02Enable = 0;
                    DataTable dtLKQ_02 = GetList(strLKQ_02Enable);
                    if (dtLKQ_02 != null && dtLKQ_02.Rows.Count > 0)
                    {
                        LKQ_02Enable = Convert.ToInt32(dtLKQ_02.Rows[0]["ENABLE_QTY"].ToString());
                    }

                    string AreaCode = string.Empty;
                    if (LKQ_01Enable > LKQ_02Enable)
                    {
                        AreaCode = dtLKQ_01.Rows[0]["AREA_CODE"].ToString();
                    }
                    else
                    {
                        AreaCode = dtLKQ_02.Rows[0]["AREA_CODE"].ToString();
                    }

                    string noneMiniloadEndPositionConfig = string.Empty;
                    if (!this._S_SystemService.GetSysParameter("NoneMiniloadEndPositionConfig", out noneMiniloadEndPositionConfig) ||
                        string.IsNullOrEmpty(noneMiniloadEndPositionConfig))
                    {
                        bResult = false;
                        sResult = "未找到系统参数[NoneMiniloadEndPositionConfig]_无法判断非立库输送任务的下一个站台";
                        return bResult;
                    }

                    foreach (var item in noneMiniloadEndPositionConfig.Split(';'))
                    {
                        if (item.Contains(AreaCode))
                        {
                            nextStation = item.Split('-')[1];
                            break;
                        }
                    }

                    

                    bResult = this._S_ManageService.ControlCreate(3, mIO_CONTROL_APPLY.STOCK_BARCODE, "1", mIO_CONTROL_APPLY.DEVICE_CODE, "1", nextStation, "5", out sResult, false, false);
                }
                else
                {
                    bResult = false;

                    sResult = string.Format("箱条码_{0}不存在库存和任务，请检查相应信息");
                }
                

                
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常_异常信息[{0}]", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateArea.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
