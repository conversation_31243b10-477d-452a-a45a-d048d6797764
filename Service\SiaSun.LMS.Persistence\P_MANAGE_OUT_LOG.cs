﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     tzyg
 *       日期：     2017-03-02
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// P_MANAGE_OUT_LOG
    /// </summary>
    public class P_MANAGE_OUT_LOG : P_Base_House
    {
        public P_MANAGE_OUT_LOG()
        {
        }

        /// <summary>
        /// 得到模型
        /// </summary>
        public MANAGE_OUT_LOG GetModel(System.Int32 MANAGE_OUT_LOG_ID)
        {
            return this.ExecuteQueryForObject<MANAGE_OUT_LOG>("MANAGE_OUT_LOG_SELECT_BY_ID", MANAGE_OUT_LOG_ID);
        }

        /// <summary>
        /// 得到模型
        /// </summary>
        public MANAGE_OUT_LOG GetModelManageId(System.Int32 MANAGE_ID)
        {
            return this.ExecuteQueryForObject<MANAGE_OUT_LOG>("MANAGE_OUT_LOG_SELECT_BY_MANAGE_ID", MANAGE_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MANAGE_OUT_LOG> GetList()
        {
            return this.ExecuteQueryForList<MANAGE_OUT_LOG>("MANAGE_OUT_LOG_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(MANAGE_OUT_LOG MANAGE_OUT_LOG)
        {
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("MANAGE_OUT_LOG");
                MANAGE_OUT_LOG.MANAGE_OUT_LOG_ID = id;
            }

            this.ExecuteInsert("MANAGE_OUT_LOG_INSERT", MANAGE_OUT_LOG);
        }

        /// <summary>
        /// 修改
        /// </summary>
        public void Update(MANAGE_OUT_LOG MANAGE_OUT_LOG)
        {
            this.ExecuteUpdate("MANAGE_OUT_LOG_UPDATE", MANAGE_OUT_LOG);
        }

        /// <summary>
        /// 删除
        /// </summary>
        public void Delete(System.Int32 MANAGE_OUT_LOG_ID)
        {
            this.ExecuteDelete("MANAGE_OUT_LOG_DELETE", MANAGE_OUT_LOG_ID);
        }
    }
}
