﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    //DisallowConcurrentExecution特性，程序会等任务执行完毕以后再去执行，否则会在任务的时间间隔 [Interval]时再启用新的线程执行
    [DisallowConcurrentExecution]
    public class ClearReturnTaskJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                int refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt("delete from IO_CONTROL where MANAGE_ID = 0 and (CONTROL_STATUS = 999 or CONTROL_STATUS=900)");
                if (refCount > 0)
                {
                    Program.sysLog.InfoFormat("ClearReturnTaskJob.Execute:清除完成或删除的非关联Control任务成功 处理条目数-{0}", refCount);
                }
            }
            catch( Exception ex)
            {
                Program.sysLog.Error("ClearReturnTaskJob.Execute:清除完成或删除的非关联Control任务异常", ex);
                throw;
            }
        }
    }
}
