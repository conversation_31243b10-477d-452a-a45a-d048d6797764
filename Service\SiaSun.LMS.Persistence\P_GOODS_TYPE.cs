﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// GOODS_TYPE
	/// </summary>
	public class P_GOODS_TYPE : P_Base_House
	{
		public P_GOODS_TYPE ()
		{
			//
			// TODO: 此处添加GOODS_TYPE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<GOODS_TYPE> GetList()
		{
			return ExecuteQueryForList<GOODS_TYPE>("GOODS_TYPE_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(GOODS_TYPE goods_type)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("GOODS_TYPE");
                goods_type.GOODS_TYPE_ID = id;
            }

            return ExecuteInsert("GOODS_TYPE_INSERT",goods_type);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(GOODS_TYPE goods_type)
		{
			return ExecuteUpdate("GOODS_TYPE_UPDATE",goods_type);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public GOODS_TYPE GetModel(System.Int32 GOODS_TYPE_ID)
		{
			return ExecuteQueryForObject<GOODS_TYPE>("GOODS_TYPE_SELECT_BY_ID",GOODS_TYPE_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GOODS_TYPE_ID)
		{
			return ExecuteDelete("GOODS_TYPE_DELETE",GOODS_TYPE_ID);
		}
		

	}
}
