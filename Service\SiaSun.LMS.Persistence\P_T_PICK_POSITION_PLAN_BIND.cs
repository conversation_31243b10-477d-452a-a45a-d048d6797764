﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Jacky He
 *       日期：     2017/9/19
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;

    using SiaSun.LMS.Model;

    /// <summary>
    /// T_PICK_POSITION
    /// </summary>
    public class P_T_PICK_POSITION_PLAN_BIND : P_Base_House
    {
        public P_T_PICK_POSITION_PLAN_BIND()
        {
            //
            // TODO: 此处添加T_PICK_POSITION_PLAN_BIND的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<T_PICK_POSITION_PLAN_BIND> GetList()
        {
            return ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT", null);
        }

        /// <summary>
        /// 得到数据表
        /// </summary>
        public DataTable GetTable()
        {
            return ExecuteQueryForDataTable("T_PICK_POSITION_PLAN_BIND_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(T_PICK_POSITION_PLAN_BIND t_pick_position_plan_bind)
        {
            int id = this.GetPrimaryID("T_PICK_POSITION_PLAN_BIND");
            t_pick_position_plan_bind.PICK_POSITION_PLAN_BIND_ID = id;

            ExecuteInsert("T_PICK_POSITION_PLAN_BIND_INSERT", t_pick_position_plan_bind);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(T_PICK_POSITION_PLAN_BIND t_pick_position_plan_bind)
        {
            ExecuteUpdate("T_PICK_POSITION_PLAN_BIND_UPDATE", t_pick_position_plan_bind);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION_PLAN_BIND GetModel(System.Int32 PICK_POSITION_PLAN_BIND_ID)
        {
            return ExecuteQueryForObject<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_ID", PICK_POSITION_PLAN_BIND_ID);
        }

        /// <summary>
        /// 由于预绑定功能进行排序优化
        /// </summary>
        /// <param name="PICK_POSITION_ID"></param>
        /// <returns></returns>
        public T_PICK_POSITION_PLAN_BIND GetModel_By_PICK_POSITION_ID(int PICK_POSITION_ID)
        {
            //return ExecuteQueryForObject<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID", PICK_POSITION_ID);
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            T_PICK_POSITION_PLAN_BIND bind = new T_PICK_POSITION_PLAN_BIND();
            IList<T_PICK_POSITION_PLAN_BIND> lists=  ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_FORLIST", ht);
            if (lists.Count > 0)
            {
                List<T_PICK_POSITION_PLAN_BIND> ms = new List<T_PICK_POSITION_PLAN_BIND>();
                foreach(T_PICK_POSITION_PLAN_BIND fT_PICK_POSITION_PLAN_BIND in lists)
                {
                    ms.Add(fT_PICK_POSITION_PLAN_BIND);
                }

                ms.Sort((a,b)=> a.BIND_TIME.CompareTo(b.BIND_TIME));

                bind = ms[0];
            }
            return bind;
        }

        /// <summary>
        /// 根据拣选工作点获得其与WBS计划的绑定信息
        /// </summary>
        /// <param name="PICK_POSITION_ID"></param>
        /// <param name="FLAG"></param>
        /// <returns></returns>
        public T_PICK_POSITION_PLAN_BIND GetModel_By_PICK_POSITION_ID_FLAG(int PICK_POSITION_ID,string FLAG="0")
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            ht.Add("FLAG", FLAG);
            return ExecuteQueryForObject<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_FLAG", ht);
        }

        /// <summary>
        /// 预绑定
        /// TESTING
        /// </summary>
        /// <param name="PICK_POSITION_ID"></param>
        /// <param name="PLAN_LIST_ID"></param>
        /// <param name="FLAG"></param>
        /// <returns></returns>
        public T_PICK_POSITION_PLAN_BIND GetModel_By_PICK_POSITION_ID_PLAN_LIST_ID_FLAG(int PICK_POSITION_ID,int PLAN_LIST_ID, string FLAG = "0")
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            ht.Add("PLAN_LIST_ID",PLAN_LIST_ID);
            ht.Add("FLAG", FLAG);
            return ExecuteQueryForObject<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_PLAN_LIST_ID_FLAG", ht);
        }

        ///<summary>
        /// 通过拣选工作站ID
        /// 得到列表
        /// </summary>
        /// <param name="PICK_STATION_ID">拣选工作站ID</param>
        /// <param name="FLAG">拣选工作站标志</param>
        /// <returns>绑定结果序列</returns>
        public IList<T_PICK_POSITION_PLAN_BIND> GetListByPICK_STATION_ID(int PICK_STATION_ID,String FLAG="0")
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_STATION_ID", PICK_STATION_ID);
            ht.Add("FLAG", FLAG);
            return ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_FLAG", ht);
        }

        public IList<T_PICK_POSITION_PLAN_BIND> GetListByPICK_POSITION_ID(int PICK_POSITION_ID, String FLAG = "0")
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            ht.Add("FLAG", FLAG);
            return ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_FLAG_NEW", ht);
        }

        ///<summary>
        /// 通过拣选工作站ID
        /// 得到列表
        /// </summary>
        /// <param name="PICK_STATION_ID">拣选工作站ID</param>
        /// <param name="FLAG">拣选工作站标志</param>
        /// <returns>绑定结果序列</returns>
        public IList<T_PICK_POSITION_PLAN_BIND> GetListByPICK_STATION_IDPartly(int PICK_STATION_ID)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_STATION_ID", PICK_STATION_ID);
            return ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_PARTLY", ht);
        }

        public IList<T_PICK_POSITION_PLAN_BIND> GetListByPICK_POSITION_IDPartly(int PICK_POSITION_ID)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            return ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_PARTLY_NEW", ht);
        }

        ///<summary>
        /// 通过拣选工作站ID
        /// 得到列表
        /// </summary>
        /// <param name="PICK_STATION_ID">拣选工作站ID</param>
        /// <param name="FLAG">拣选工作站标志</param>
        /// <returns>绑定结果序列</returns>
        public IList<T_PICK_POSITION_PLAN_BIND> GetListByPICK_STATION_IDPrevPartly(int PICK_STATION_ID,string PrevPlanGroup)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_STATION_ID", PICK_STATION_ID);
            ht.Add("PLAN_GROUP", PrevPlanGroup);
            return ExecuteQueryForList<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_PLAN_GROUP_PARTLY", ht);
        }

        /// <summary>
        /// 通过拣选工作站和锁定库存的计划明细
        /// 查询拣选工作站的订单绑定内容
        /// </summary>
        /// <param name="PICK_STATION_ID">拣选工作站ID</param>
        /// <param name="PLAN_LIST_ID">锁定库存对应的计划明细ID</param>
        /// <returns>返回的订单绑定实例</returns>
        public T_PICK_POSITION_PLAN_BIND GetModelByPICK_STATION_ID_PLAN_LIST_ID(int PICK_STATION_ID, int  PLAN_LIST_ID)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_STATION_ID", PICK_STATION_ID);
            ht.Add("PLAN_LIST_ID", PLAN_LIST_ID);
            return ExecuteQueryForObject<T_PICK_POSITION_PLAN_BIND>("T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_PLAN_LIST_ID", ht);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete(System.Int32 PICK_POSITION_PLAN_BIND_ID)
        {
            ExecuteDelete("T_PICK_POSITION_PLAN_BIND_DELETE", PICK_POSITION_PLAN_BIND_ID);
        }
        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete_By_PICK_STATION_ID(System.Int32 PICK_STATION_ID)
        {
            ExecuteDelete("T_PICK_POSITION_PLAN_BIND_DELETE_BY_PICK_STATION_ID", PICK_STATION_ID);
        }


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete_By_PICK_POSITION_ID(System.Int32 PICK_STATION_ID)
        {
            ExecuteDelete("T_PICK_POSITION_PLAN_BIND_DELETE_BY_PICK_POSITION_ID", PICK_STATION_ID);
        }
    }
}
