﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    public class ManageIn:ManageBase
    {

        /// <summary>
        /// 生成计划或无计划入库任务
        /// 生成托盘入库任务
        /// 调用界面 MANAGE_PLAN_IN.XAML MANAGE_IN.XAML MANAGE_STOCK_IN.XAML
        /// </summary>
        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN    mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM,
                                 out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

                if (mMANAGE_TYPE == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                    return bResult;
                }

                if (mMANAGE_MAIN.STOCK_BARCODE != string.Empty && this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.LiKu.ToString(), out sResult))
                {
                    sResult = string.Format("托盘{0}已经在立库区", mMANAGE_MAIN.STOCK_BARCODE);
                    return false;
                }

                if (mMANAGE_MAIN.STOCK_BARCODE != string.Empty && this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.XuNiKu.ToString(), out sResult) && mMANAGE_TYPE_PARAM.U_CheckStockExistStorage)
                {
                    sResult = string.Format("托盘{0}库存校验失败\n {1} ", mMANAGE_MAIN.STOCK_BARCODE, "已经存在库存");
                    return false;
                }

                if (mMANAGE_MAIN.STOCK_BARCODE != string.Empty && this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    bResult = false;

                    sResult = string.Format("托盘条码{0}已经存在任务", mMANAGE_MAIN.STOCK_BARCODE);

                    return bResult;
                }

                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    SiaSun.LMS.Model.PLAN_LIST mIO_PLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                    if (null != mIO_PLAN_LIST)
                    {
                        mIO_PLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        if (mIO_PLAN_LIST.PLAN_LIST_ORDERED_QUANTITY > mIO_PLAN_LIST.PLAN_LIST_QUANTITY)
                        {
                            bResult = false;

                            sResult = string.Format("物料组盘数量超过计划数量！");

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;

                        }

                        this._P_PLAN_LIST.Update(mIO_PLAN_LIST);
                    }

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }



                if (mMANAGE_TYPE_PARAM.U_AutoCompleteTask)
                {
                    bResult = _S_ManageService.Invoke(
                                                     mMANAGE_TYPE.MANAGE_TYPE_CLASS,
                                                     "ManageComplete",
                                                     new object[] { mMANAGE_MAIN.MANAGE_ID, false },
                                                     out sResult
                                                     );


                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }


                }
                else
                {
                    if (mWH_CELL_START != null)
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }

                    if (mWH_CELL_END != null)
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }
                }

                if ( mMANAGE_TYPE_PARAM.U_AutoDownloadControlTask )
                {
                    bResult = this.ManageDownLoad(mMANAGE_MAIN.MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }


                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }


        /// <summary>
        /// 任务完成
        /// </summary>
        public bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (mMANAGE_MAIN == null)
            {
                bResult = false;
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
            if (mMANAGE_TYPE == null)
            {
                bResult = false;
                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }

                if (!bResult)
                {
                    sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());
                    return bResult;
                }
                if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());
                    return bResult;
                }

                bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageCreate", new object[] { MANAGE_ID }, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("库存处理错误-{0}", sResult);
                    return bResult;
                }

                //2020-09-24 12:39:16 如果是越库的齐套箱类型入库，则将“齐套箱首次上架”标记置为1，防止触发接口调用
                if (mMANAGE_MAIN.CELL_MODEL == Enum.CellModel.KitBox.ToString("d")
                    && mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString())
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                    if (mSTORAGE_MAIN != null)
                    {
                        mSTORAGE_MAIN.KITBOX_UP_COMPLETE = "1";
                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                    }
                }

                //接口调用
                string isNoticeWms = string.Empty;
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    bResult = new Interface.handleResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManageIn完成时发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }
            return bResult;
        }

        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                         List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                         bool bTrans,
                         bool bCheckStorage,
                         bool bComplete,
                         bool bAutoSendControl,
                         bool bCheckManage,
                         out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int MANAGE_ID = 0;

            try
            {
                SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

                if (mMANAGE_TYPE == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                    return bResult;
                }

                if (this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.LiKu.ToString(), out sResult))
                {
                    sResult = string.Format("托盘{0}已经在立库区", mMANAGE_MAIN.STOCK_BARCODE);
                    return false;
                }

                if (this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.XuNiKu.ToString(), out sResult) && bCheckStorage)
                {
                    sResult = string.Format("托盘{0}库存校验失败\n {1} ", mMANAGE_MAIN.STOCK_BARCODE, "已经存在库存");
                    return false;
                }
                //if (bCheckStorage)
                //{
                //    foreach (Model.MANAGE_LIST itemMANAGE_LIST in lsMANAGE_LIST)
                //    {
                //        if (this._S_StorageService.StorageCheckGoods(itemMANAGE_LIST.GOODS_ID, out sResult))
                //        {
                //            bResult = false;
                //            sResult = string.Format("物料{0}库存校验失败\n {1} ", itemMANAGE_LIST.GOODS_ID, "已经存在库存");
                //            return bResult;
                //        }
                //    }
                //}

                //foreach (Model.MANAGE_LIST itemMANAGE_LIST in lsMANAGE_LIST)
                //{
                //    int tempCount = this._P_Base_House.GetDataTable(string.Format("select * from MANAGE_LIST where GOODS_ID={0}", itemMANAGE_LIST.GOODS_ID)).Rows.Count;
                //    if (tempCount>0)
                //    {
                //        bResult = false;
                //        sResult = string.Format("物料{0}已经在任务中\n  ", itemMANAGE_LIST.GOODS_ID);
                //        return bResult;
                //    }
                //}

                if (bCheckManage && this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    bResult = false;

                    sResult = string.Format("托盘条码{0}已经存在任务", mMANAGE_MAIN.STOCK_BARCODE);

                    return bResult;
                }

                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    if (mPLAN_MAIN != null)
                    {
                        SiaSun.LMS.Model.PLAN_LIST mIO_PLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (null != mIO_PLAN_LIST)
                        {
                            mIO_PLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                            if (mIO_PLAN_LIST.PLAN_LIST_ORDERED_QUANTITY > mIO_PLAN_LIST.PLAN_LIST_QUANTITY)
                            {
                                bResult = false;

                                sResult = string.Format("物料组盘数量超过计划数量！");

                                this._P_Base_House.RollBackTransaction(bTrans);

                                return bResult;

                            }

                            this._P_PLAN_LIST.Update(mIO_PLAN_LIST);
                        }
                    }
                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }


                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                if (bComplete)
                {
                    bResult = _S_ManageService.Invoke(
                                                     mMANAGE_TYPE.MANAGE_TYPE_CLASS,
                                                     "ManageComplete",
                                                     new object[] { MANAGE_ID, false },
                                                     out sResult
                                                     );


                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }


                }
                else
                {
                    if (mWH_CELL_START != null)
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }

                    if (mWH_CELL_END != null)
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }
                }

                if (bAutoSendControl)
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }


                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        public bool ManageCreate(Model.MANAGE_MAIN mMANAGE_MAIN,
                        List<Model.MANAGE_LIST> lsMANAGE_LIST,
                        bool bTrans,
                        bool bCheckStorage,
                        bool bComplete,
                        bool bAutoSendControl,                        
                        out string sResult)
        {
            return this.ManageCreate(mMANAGE_MAIN, lsMANAGE_LIST, bTrans, bCheckStorage, bComplete, bAutoSendControl, true, out sResult);
        }
    }
}
