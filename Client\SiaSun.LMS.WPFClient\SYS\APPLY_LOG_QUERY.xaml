﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.APPLY_LOG_QUERY"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="APPLY_LOG_QUERY" Height="300" Width="300"  Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <!--<WrapPanel Name="panelQuery" Grid.Row="0" HorizontalAlignment="Left"  Visibility="Visible" ButtonBase.Click="panelQuery_Click">
            <Label>申请类型：</Label>
            <TextBox x:Name="tbxApplyType" Width="80" Margin="0,0,20,0"></TextBox>
            <Label>库房编码：</Label>
            <TextBox x:Name="tbxWhCode" Width="80" Margin="0,0,20,0"></TextBox>
            <Label>托盘条码：</Label>
            <TextBox x:Name="tbxBarcode" Width="80" Margin="0,0,20,0"></TextBox>
            <Label>申请参数：</Label>
            <TextBox x:Name="tbxPara" Width="80" Margin="0,0,20,0"></TextBox>
            
            <Button Name="btnQuery" Width="50" Margin="8" >查询</Button>
            <Button Name="btnReset" Width="50" Margin="8" >重置</Button>
        </WrapPanel>-->

        <uc:ucQuickQuery x:Name="ucQueryControl" Margin="1,1,1,3" Grid.Row="0" BorderBrush="Black" Grid.ColumnSpan="2"></uc:ucQuickQuery>

        <GroupBox Header="申请历史列表"  Margin="1,5,1,1" Grid.Row="1" Grid.ColumnSpan="2">
            <uc:ucCommonDataGrid x:Name="ucApplyDataGrid" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>

        <GroupBox Header="操作区" Margin="1,5,1,1" Grid.Row="2" Grid.ColumnSpan="2">
            <Border>
                <WrapPanel Name="panelButton" HorizontalAlignment="Center" Visibility="Visible" ButtonBase.Click="WrapPanel_Click">
                    <Button Name="btnApplyAgain"  Width="80" Margin="8" >再次申请</Button>
                </WrapPanel>
            </Border>
        </GroupBox>

    </Grid>
</ad:DocumentContent>
