﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Runtime.Serialization;
using System.Reflection;

namespace SiaSun.LMS.Model
{
    [DataContract]
    [KnownType("GetKnownType")]
    public class ObjectT
    {
        //增加构造函数方便使用
        public ObjectT(object requestObj)
        {
            RequestObject = requestObj;
        }
        [DataMember]
        public object RequestObject;

        private static Type[] GetKnownType()
        {
            //将自定义对象的程序集下的所有类型标记为KnownType。
            return Assembly.Load("SiaSun.LMS.Model").GetTypes();
        }
    } 


}
