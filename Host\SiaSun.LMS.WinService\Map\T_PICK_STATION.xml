﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="T_PICK_STATION" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="T_PICK_STATION" type="SiaSun.LMS.Model.T_PICK_STATION, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="T_PICK_STATION">
      <result property="STATION_ID" column="station_id" />
      <result property="STATION_CODE" column="station_code" />
      <result property="STATION_NAME" column="station_name" />
      <result property="STATION_IP" column="station_ip" />
      <result property="REMARK" column="remark" />
      <result property="POSITION_COUNT" column="position_count" />
      <result property="PLAN_GROUP_CODE" column="plan_group_code" />
      <result property="PLAN_GROUP_NAME" column="plan_group_name" />
      <result property="WH_CELL_ID" column="wh_cell_id" />
      <result property="STATION_MAC" column="station_mac" />
      <result property="FLAG" column="flag" />
      <result property="FLAG_OS" column="flag_os" />
      <result property="PROPERTY1" column="property1" />
      <result property="PROPERTY2" column="property2" />
      <result property="PROPERTY3" column="property3" />
      <result property="PROPERTY4" column="property4" />
      <result property="PROPERTY5" column="property5" />
		<result property="PROPERTY6" column="property6" />
		<result property="PROPERTY7" column="property7" />
		<result property="PROPERTY8" column="property8" />
		<result property="PROPERTY9" column="property9" />
	</resultMap>
  </resultMaps>

  <statements>

    <select id="T_PICK_STATION_SELECT" parameterClass="int" resultMap="SelectResult">
		Select
		station_id,
		station_code,
		station_name,
		station_ip,
		remark,
		position_count,
		plan_group_code,
		plan_group_name,
		wh_cell_id,
		station_mac,
		flag,
		flag_os,
		property1,
		property2,
		property3,
		property4,
		property5,
		property6,
		property7,
		property8,
		property9
		From T_PICK_STATION
	</select>

    <select id="T_PICK_STATION_SELECT_BY_ID" parameterClass="int" extends = "T_PICK_STATION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          station_id=#STATION_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="T_PICK_STATION_SELECT_BY_PROPERTY1" parameterClass="int" extends = "T_PICK_STATION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          property1=#STATION_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="T_PICK_STATION_SELECT_BY_IP" parameterClass="string" extends = "T_PICK_STATION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          station_ip=#STATION_IP#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_STATION_SELECT_BY_STATION_CODE" parameterClass="string" extends = "T_PICK_STATION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          station_code=#STATION_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_STATION_SELECT_BY_CELL_ID" parameterClass="int" extends = "T_PICK_STATION_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          wh_cell_id=#CELL_ID#
        </isParameterPresent>
      </dynamic>
    </select>


    <insert id="T_PICK_STATION_INSERT" parameterClass="T_PICK_STATION">
      Insert Into T_PICK_STATION (
      <!--station_id-->,
      station_code,
      station_name,
      station_ip,
      remark,
      position_count,
      plan_group_code,
      plan_group_name,
      wh_cell_id,
      station_mac,
      flag,
      flag_os,
      property1,
      property2,
      property3,
      property4,
      property5,
	  property6,
	  property7,
	  property8,
	  property9
      )Values(
      <!--#STATION_ID#,-->
      #STATION_CODE#,
      #STATION_NAME#,
      #STATION_IP#,
      #REMARK#,
      #POSITION_COUNT#,
      #PLAN_GROUP_CODE#,
      #PLAN_GROUP_NAME#,
      #WH_CELL_ID#,
      #STATION_MAC#,
      #FLAG#,
      #FLAG_OS#,
      #PROPERTY1#,
      #PROPERTY2#,
      #PROPERTY3#,
      #PROPERTY4#,
      #PROPERTY5#,
	  #PROPERTY6#,
	  #PROPERTY7#,
	  #PROPERTY8#,
	  #PROPERTY9#
      )
    </insert>

    <update id="T_PICK_STATION_UPDATE" parameterClass="T_PICK_STATION">
      Update T_PICK_STATION Set
      <!--station_id=#STATION_ID#,-->
      station_code=#STATION_CODE#,
      station_name=#STATION_NAME#,
      station_ip=#STATION_IP#,
      remark=#REMARK#,
      position_count=#POSITION_COUNT#,
      plan_group_code=#PLAN_GROUP_CODE#,
      plan_group_name=#PLAN_GROUP_NAME#,
      wh_cell_id=#WH_CELL_ID#,
      station_mac=#STATION_MAC#,
      flag=#FLAG#,
      flag_os=#FLAG_OS#,
      property1=#PROPERTY1#,
      property2=#PROPERTY2#,
      property3=#PROPERTY3#,
      property4=#PROPERTY4#,
      property5=#PROPERTY5#,	  
      property6=#PROPERTY6#,
      property7=#PROPERTY7#,
      property8=#PROPERTY8#,
      property9=#PROPERTY9#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          station_id=#STATION_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="T_PICK_STATION_DELETE" parameterClass="int">
      Delete From T_PICK_STATION
      <dynamic prepend="WHERE">
        <isParameterPresent>
          station_id=#STATION_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>