﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// IO_CONTROL
	/// </summary>
	public class P_IO_CONTROL : P_Base_House
	{
		public P_IO_CONTROL ()
		{
			//
			// TODO: 此处添加IO_CONTROL的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<IO_CONTROL> GetList()
		{
			return ExecuteQueryForList<IO_CONTROL>("IO_CONTROL_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<IO_CONTROL> GetList(string STOCK_BARCODE)
        {
            return ExecuteQueryForList<IO_CONTROL>("IO_CONTROL_SELECT_BY_STOCK_BARCODE", STOCK_BARCODE);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<IO_CONTROL> GetListrRetrunTask()
        {
            return ExecuteQueryForList<IO_CONTROL>("IO_CONTROL_SELECT_RETURN_TASK", null);
        }

        /// <summary>
        /// 得到列表
        /// wdz add 2018-11-27 
        /// 参数要随便写点123，要不会与IO_CONTROL_SELECT有相同的参数列表，导致按IO_CONTROL_SELECT执行
        /// </summary>
        public IList<IO_CONTROL> GetListChanged()
        {
            return ExecuteQueryForList<IO_CONTROL>("IO_CONTROL_SELECT_CHANGED", 123);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(IO_CONTROL io_control)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("IO_CONTROL");
                io_control.CONTROL_ID = id;
            }
            return ExecuteInsert("IO_CONTROL_INSERT",io_control);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(IO_CONTROL io_control)
		{
			return ExecuteUpdate("IO_CONTROL_UPDATE",io_control);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IO_CONTROL GetModel(System.Int32 CONTROL_ID)
		{
			return ExecuteQueryForObject<IO_CONTROL>("IO_CONTROL_SELECT_BY_ID",CONTROL_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IO_CONTROL GetModelManageID(System.Int32 MANAGE_ID)
        {
            return ExecuteQueryForObject<IO_CONTROL>("IO_CONTROL_SELECT_BY_MANAGE_ID", MANAGE_ID);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int DeleteManageID(System.Int32 MANAGE_ID)
		{
            return ExecuteDelete("IO_CONTROL_DELETE_BY_MANAGE_ID", MANAGE_ID);
		}

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 CONTROL_ID)
        {
            return ExecuteDelete("IO_CONTROL_DELETE", CONTROL_ID);
        }
    }
}
