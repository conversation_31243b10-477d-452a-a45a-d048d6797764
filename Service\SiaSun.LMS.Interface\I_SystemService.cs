﻿using System;
using System.Collections.Generic;
using System.Text;

using System.Data;
using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public partial interface I_SystemService
    {

        #region     ------USER
        /// <summary>
        /// 用户登录
        /// </summary>
        /// <param name="USER_CODE">用户编码</param>
        /// <param name="USER_PASSWORD">用户密码</param>
        /// <param name="USER">用户实体</param>
        /// <returns>是否成功</returns>
        /// 
        [OperationContract]
        bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SiaSun.LMS.Model.SYS_USER USER);

        /// <summary>
        /// 修改密码
        /// </summary>
        /// <param name="USER_CODE"></param>
        /// <param name="USER_PASSWORD_OLD"></param>
        /// <param name="USER_PASSWORD_NEW"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        [OperationContract]
        bool USER_PASSWORD(string USER_CODE, string USER_PASSWORD_OLD, string USER_PASSWORD_NEW, out string sResult);

        #endregion

        #region     ------ROLE

        [OperationContract]
        IList<SiaSun.LMS.Model.SYS_ROLE> ROLE_GetList(int USER_ID);

        #endregion

        #region         ------ROLE_WINDOW

        /// <summary>
        /// 获得角色的窗体控件设置列表
        /// </summary>
        [OperationContract]
        IList<Model.SYS_ROLE_WINDOW> ROLE_WINDOW_GetList_ROLE_MENU(int ROLE_ID, int MENU_ID);

        /// <summary>
        /// 根据菜单ID和控件名称获得角色窗体实例
        /// </summary>
        [OperationContract]
        Model.SYS_ROLE_WINDOW ROLE_WINDOW_GetModel_MENU_CONTROL(int ROLE_ID, int MENU_ID, string CONTROL_NAME);

        /// <summary>
        /// 保存ROLE_WINDOW更改
        /// </summary>
        [OperationContract]
        bool ROLE_WINDOW_Save(int ROLE_ID, int MENU_ID, IList<Model.SYS_ROLE_WINDOW> listROLE_WINDOW, out string strResult);

        #endregion

        #region     ------ITEM

        /// <summary>
        /// 根据编码获得值对集合
        /// </summary>
        [OperationContract]
        DataTable ITEM_LIST_GetDictionary(string ITEM_CODE);

        /// <summary>
        /// 根据编码获得列表
        /// </summary>
        [OperationContract]
        IList<SiaSun.LMS.Model.SYS_ITEM_LIST> ITEM_LIST_GetList_ITEM_CODE(string ITEM_CODE);
        
        #endregion

        #region     ------MENU

        /// <summary>
        /// 菜单-获取列表
        /// </summary>
        [OperationContract()]
        IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList();

         /// <summary>
        /// 菜单-获取列表
        /// </summary>
        /// <param name="ROLE_ID">角色编号</param>
        [OperationContract()]
        IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList_ROLE_Select(int ROLE_ID, bool bSelect);

        [OperationContract]
        SiaSun.LMS.Model.SYS_MENU MENU_GetModel(int MENU_ID);

        #endregion

        #region     ------RELATION

        /// <summary>
        /// 获得关系实例
        /// </summary>
        [OperationContract]
        Model.SYS_RELATION RELATION_GetModel(string RELATION_CODE);



        /// <summary>
        /// 获得关系列表
        /// </summary>
        [OperationContract]
        IList<Model.SYS_RELATION_LIST> RELATION_LIST_GetList_ID1(int RELATION_ID, int RELATION_ID1);

        /// <summary>
        /// 添加关系明细
        /// </summary>
        /// <returns></returns>
        [OperationContract]
        bool RELATION_LIST_Add(string RELATION_CODE, int RELATION_ID1, int[] List_RELATION_ID2, out string Result);

        #endregion


        #region     ------SYS_TABLE_CONVERTER_LIST

        /// <summary>
        /// 获得所有映射列表
        /// </summary>
        [OperationContract]
        IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList();

        ///// <summary>
        ///// 根据转换编码获得所有转换器信息
        ///// </summary>
        //[OperationContract]
        //IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList_ConverterCode(string TABLE_CONVERTER_CODE);

        ///// <summary>
        ///// 根据转换编号获得所有转换映射列表
        ///// </summary>
        //[OperationContract]
        //IList<Model.SYS_TABLE_CONVERTER_LIST> TABLE_CONVERTER_LIST_GetList_ConverterID(int TABLE_CONVERTER_ID);

        /// <summary>
        /// 根据数据导入模板导入数据
        /// </summary>
        [OperationContract]
        DataSet SYS_TABLE_CONVERTER_Import(string TABLE_CONVERTER_CODE, DataTable tableImport, out string strResult);

        /// <summary>
        /// 提交保存导入数据
        /// </summary>
        [OperationContract]
        int TABLE_CONVERTER_Save(Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER, DataSet dsImport, out string strResult);

        #endregion


        /// <summary>
        /// 获取系统参数
        /// tzyg add 2017-02-27
        /// </summary>
        [OperationContract]
        Dictionary<string, string> GetSysParameter();

        /// <summary>
        /// 连接拣选工作站 不能断线重连前使用的过渡方案
        /// </summary>
        [OperationContract]
        string ConnectToPickStation(Model.SYS_USER user);
    }
}
