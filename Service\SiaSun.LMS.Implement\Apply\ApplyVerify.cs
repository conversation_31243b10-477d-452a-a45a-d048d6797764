﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 一楼申请：71002验证是否存在任务
    /// </summary>
    public class ApplyVerify : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyVerify.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //超重校验
                if(mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER == "1")
                {
                    bResult = false;
                    sResult = string.Format("ApplyVerify.ApplyHandle:物料超重 箱条码-{0}", mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd());
                    return bResult;
                }

                string scanAutoEmptyBoxIn = string.Empty;
                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                if (mMANAGE_MAIN == null)
                {
                    if (this._S_SystemService.GetSysParameter("InitializeEmptyBox", out scanAutoEmptyBoxIn) && scanAutoEmptyBoxIn == Enum.FLAG.Enable.ToString())
                    {
                        //开启自动生成空箱入库任务
                        mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                        mMANAGE_MAIN.END_CELL_ID = 0;
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                        string manageLevel = string.Empty;
                        mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("ApplyTaskLevel", out manageLevel) ? manageLevel : "0";
                        mMANAGE_MAIN.MANAGE_OPERATOR = "空箱扫码入库";
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageStockIn.ToString();
                        mMANAGE_MAIN.PLAN_ID = 0;
                        mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                        mMANAGE_MAIN.START_CELL_ID = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE).CELL_ID;
                        mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;

                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();

                        mMANAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                        mMANAGE_LIST.GOODS_PROPERTY1 = "0";//箱型
                        mMANAGE_LIST.GOODS_PROPERTY2 = "green";//颜色
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = 1;

                        Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, new List<Model.MANAGE_LIST> { mMANAGE_LIST }, true, true, false, false }, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("条码{0}未下达任务 请查看当前任务中是否有此条码的记录", mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd());
                        return bResult;
                    }
                }
                else if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManagePick.ToString())
                {
                    bResult = false;
                    sResult = string.Format("条码[{0}]存在未完成的拣选任务", mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd());
                    return bResult;
                }

                string[] arrNextStation = this.applyTypeParam.U_NextStation.Split(';');

                var nextStationGroup = from r in arrNextStation
                                       where r.StartsWith(mIO_CONTROL_APPLY.DEVICE_CODE)
                                       select r.Split('-')[1];

                string strNextStation = string.Empty;
                if (nextStationGroup.Count()>0)
                {
                    strNextStation = nextStationGroup.First();
                }

                if (!string.IsNullOrEmpty(strNextStation))
                {
                    //如果配置了下一站台，则直接分配到下一站台
                    bResult = this._S_ManageService.ControlCreate(3, mIO_CONTROL_APPLY.STOCK_BARCODE, "1", mIO_CONTROL_APPLY.DEVICE_CODE, "1", strNextStation, "5", out sResult ,false);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.WaitingExecute.ToString()||
                    mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.WaitingSend.ToString())
                {
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Executing.ToString();
                    this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_Apply1", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyVerify.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
