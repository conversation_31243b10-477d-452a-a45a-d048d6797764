﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Xml;
using System.Data;
using System.Collections;

namespace SSLMS.Common
{
    public class StringUtil
    {
        public static Control GetControl(Control ctrl, string cName)
        {
            Control ctlChooseResult = new Control();
            Control ctlResult = new Control();

            foreach (Control c in ctrl.Controls)
            {
                if (c.Name == cName)
                {
                    ctlChooseResult = c;
                    //return ctlResult;
                }
                else
                {
                    if (c.Controls.Count != 0)
                    {
                        ctlChooseResult = GetControl(c, cName);

                    }
                }
                if (ctlChooseResult.Name == cName)
                {
                    ctlResult = ctlChooseResult;
                    break;
                }
            }

            return ctlResult;
        }

        public static string GetUrl(string sKey)
        {
            string strUrl = string.Empty;

            try
            {
                XmlDocument xd = new XmlDocument();

                xd.Load(System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().GetName().CodeBase) + @"\FieldDescription.xml");
                XmlElement xe = xd.DocumentElement;
                XmlNode xn = xe.SelectSingleNode(string.Format("descendant::Table[@Name='{0}']", sKey));
                XmlNodeList xnl = xn.ChildNodes;
                foreach (XmlNode xmlField in xnl)
                {
                    strUrl = xmlField.Attributes["Name"].InnerText.Trim();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "服务器地址读取错误,请检查FieldDescription.xml");

                Application.Exit();
            }

            return strUrl;
        }

        public static string GetSplitStr(string ControlName, char[] ch, int index)
        {
            string[] arrTmp = ControlName.Split(ch);

            return arrTmp[(1 == arrTmp.Length) ? 0 : index];
        }

        public static DataTable AddAll(DataTable dt, Hashtable ht)
        {
            if (0 == ht.Count)
            {
                ht.Add("name", "-");

                ht.Add("value", "");
            }

            DataTable dtResult = dt;

            if (null == dtResult)
            {
                dtResult = new DataTable();

                foreach (DictionaryEntry d in ht)
                {
                    dtResult.Columns.Add(d.Key.ToString());
                }
            }

            if (0 == dtResult.Columns.Count)
            {
                dtResult = new DataTable();

                foreach (DictionaryEntry d in ht)
                {
                    dtResult.Columns.Add(d.Key.ToString());
                }
            }

            DataRow dr = dtResult.NewRow();

            foreach (DictionaryEntry d in ht)
            {
                dr[d.Key.ToString()] = d.Value;
            }

            dtResult.Rows.InsertAt(dr, 0);

            return dtResult;
        }

        public static void SetBarcode(Barcode.Barcode bc)
        {
            bc.DecoderParameters.CODABAR = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.CODE128 = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.CODE39 = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.CODE39Params.Code32Prefix = false;
            bc.DecoderParameters.CODE39Params.Concatenation = false;
            bc.DecoderParameters.CODE39Params.ConvertToCode32 = false;
            bc.DecoderParameters.CODE39Params.FullAscii = false;
            bc.DecoderParameters.CODE39Params.Redundancy = false;
            bc.DecoderParameters.CODE39Params.ReportCheckDigit = false;
            bc.DecoderParameters.CODE39Params.VerifyCheckDigit = false;
            bc.DecoderParameters.D2OF5 = Barcode.DisabledEnabled.Disabled;
            bc.DecoderParameters.EAN13 = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.EAN8 = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.I2OF5 = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.KOREAN_3OF5 = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.MSI = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.UPCA = Barcode.DisabledEnabled.Enabled;
            bc.DecoderParameters.UPCE0 = Barcode.DisabledEnabled.Enabled;
            bc.EnableScanner = false;
            bc.EnableScanner = true;
            bc.ScanParameters.BeepFrequency = 2670;
            bc.ScanParameters.BeepTime = 200;
            bc.ScanParameters.CodeIdType = Barcode.CodeIdTypes.None;
            bc.ScanParameters.LedTime = 3000;
            bc.ScanParameters.ScanType = Barcode.ScanTypes.Foreground;
            bc.ScanParameters.WaveFile = "";
        }
    }
}
