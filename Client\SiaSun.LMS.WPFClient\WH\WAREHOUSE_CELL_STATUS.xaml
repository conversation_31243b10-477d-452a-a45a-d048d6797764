﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.WH.WAREHOUSE_CELL_STATUS"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock" 
        Title="WAREHOUSE_CELL_STATUS" Height="451" Width="450" Loaded="DocumentContent_Loaded">

    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="70*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="30*"></RowDefinition>
        </Grid.RowDefinitions>
        <uc:ucStoreCell x:Name="ucCellQuery" Grid.Row="0" Margin="1"></uc:ucStoreCell>
        <GridSplitter Name="spliter" Grid.Row="1"  HorizontalAlignment="Stretch"  Height="2"></GridSplitter>
        <GroupBox Name="grpbStorageList"  Grid.Row="2" MaxHeight="400" Tag=" {0}-库存信息">
            <uc:ucSplitPropertyGridTab x:Name="gridStorageList"></uc:ucSplitPropertyGridTab>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
