﻿/***************************************************************************
 * 
 *       功能：     计划列表持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// PLAN_LIST
	/// </summary>
	public class P_PLAN_LIST : P_Base_House
	{
		public P_PLAN_LIST ()
		{
			//
			// TODO: 此处添加PLAN_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<PLAN_LIST> GetList()
		{
			return ExecuteQueryForList<PLAN_LIST>("PLAN_LIST_SELECT",null);
		}


        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<PLAN_LIST> GetListPlanID(int PLAN_ID )
        {
            return ExecuteQueryForList<PLAN_LIST>("PLAN_LIST_SELECT_BY_PLAN_ID", PLAN_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<PLAN_LIST> GetListNoComplete(int PLAN_ID)
        {
            return ExecuteQueryForList<PLAN_LIST>("PLAN_LIST_SELECT_BY_PLAN_ID_NO_COMPLETE", PLAN_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(PLAN_LIST plan_list)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("PLAN_LIST");
                plan_list.PLAN_LIST_ID = id;
            }

            return ExecuteInsert("PLAN_LIST_INSERT",plan_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(PLAN_LIST plan_list)
		{
			return ExecuteUpdate("PLAN_LIST_UPDATE",plan_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public PLAN_LIST GetModel(System.Int32 PLAN_LIST_ID)
		{
			return ExecuteQueryForObject<PLAN_LIST>("PLAN_LIST_SELECT_BY_ID",PLAN_LIST_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 PLAN_LIST_ID)
		{
			return ExecuteDelete("PLAN_LIST_DELETE",PLAN_LIST_ID);
		}


        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int DeletePlanID(System.Int32 PLAN_ID)
        {
            return ExecuteDelete("PLAN_LIST_DELETE_BY_PLAN_ID", PLAN_ID);
        }
		
		

	}
}
