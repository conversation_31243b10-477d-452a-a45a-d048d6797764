﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_ADJUST"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_ADJUST" Height="470" Width="500" Loaded="DocumentContent_Loaded">
        <GroupBox Grid.Column="2" Header="库存调整" >
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>
            <uc:ucSplitPropertyGridTab Grid.Row="1" x:Name="gridStorageList" ></uc:ucSplitPropertyGridTab>
            <WrapPanel Grid.Row="2" Orientation="Horizontal" HorizontalAlignment="Left" VerticalAlignment="Center" Margin="3" ButtonBase.Click="WrapPanel_Click">
                <uc:ucManagePosition x:Name="ucManagePosition" Grid.Row="0"></uc:ucManagePosition>
                <Button Name="btnConfirm"  Width="60" Margin="0,0,10,0">保存</Button>
                <Button Name="btnRefresh"  Width="60" Margin="0,0,20,0">刷新</Button>
                <Label Content="扫描[容器条码]点击[刷新]，在准备调整的行前打勾并修改[实际数量]，点击[保存]完成操作"/>
            </WrapPanel>
        </Grid>
    </GroupBox>

</ad:DocumentContent>
