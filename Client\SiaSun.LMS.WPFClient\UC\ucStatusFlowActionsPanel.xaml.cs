﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucStatusFlowActionsPanel.xaml 的交互逻辑
    /// </summary>
    public partial class ucStatusFlowActionsPanel : UserControl
    {
        public delegate void ExecuteUpdateEventHandler();
        public event ExecuteUpdateEventHandler U_ExecuteUpdate;

        const string strFlowTpye = "SYS_ROLE-FLOW_ACTION";

        #region     -----定义属性

        string strFlowType = string.Empty;
        DataRow rowSource = null;
        DataTable tableAction = null;
        int intRoleID = 0;
        bool boolAddLog = false;

        /// <summary>
        /// 是否添加日志
        /// </summary>
        public bool U_AddLog
        {
            get { return boolAddLog; }
            set { boolAddLog = value; }
        }

        /// <summary>
        /// 角色编号
        /// </summary>
        public int U_ROLE_ID
        {
            get { return intRoleID; }
            set { intRoleID = value; }
        }

        /// <summary>
        /// 流程类型
        /// </summary>
        public string U_FLOW_TYPE
        {
            get { return strFlowType; }
            set { strFlowType = value; }
        }

        /// <summary>
        /// 当前流程的数据源的行
        /// </summary>
        public DataRow U_FLOW_SOURCE_ROW
        {
            get { return rowSource; }
            set { rowSource = value; }
        }

        /// <summary>
        /// 所有动作列表
        /// </summary>
        public DataTable U_DataSource
        {
            get { return tableAction; }
            set { tableAction = value; }
        }

        #endregion

        public ucStatusFlowActionsPanel()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //清空所有项
            this.panelActions.Children.Clear();

            //判断数据源是否空
            if (tableAction == null) 
                return;               

            //参数值列表
            List<string> listParamValue = new List<string>();
            try
            {
                //获得参数值信息
                IList<SiaSun.LMS.Model.FLOW_PARA> listFLOW_PARA = MainApp._I_FlowService.FlowGetParameters(strFlowType);
                foreach (SiaSun.LMS.Model.FLOW_PARA mFLOW_PARA in listFLOW_PARA)
                {
                    if (mFLOW_PARA.FLOW_PARA_CODE.Length > 0)
                    {
                        listParamValue.Add(rowSource[mFLOW_PARA.FLOW_PARA_CODE].ToString());
                    }
                }

                //添加当前操作用户
                listParamValue.Add(MainApp._USER.USER_ID.ToString());
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
                return;
            }

            try
            {
                //获得关系实例
                Model.SYS_RELATION mSYS_RELATION = MainApp._I_SystemService.RELATION_GetModel(strFlowTpye);
                if (mSYS_RELATION == null)
                {
                    MainApp._MessageDialog.Show("Relation.CheckRelationType", new object[] { strFlowTpye });
                    return;
                }

                //获得该角色下得所有操作
                IList<Model.SYS_RELATION_LIST> listRELATION_LIST = MainApp._I_SystemService.RELATION_LIST_GetList_ID1(mSYS_RELATION.RELATION_ID, MainApp._ROLE.ROLE_ID);
                if (listRELATION_LIST.Count == 0)
                    return;                    

                //遍历所有节点，添加显示按钮
                foreach (DataRow rowAction in tableAction.Rows)
                {
                    //判断该节点是否在管理列表中
                    if (listRELATION_LIST.Count(r => r.RELATION_ID2 == Convert.ToInt32(rowAction["FLOW_ACTION_ID"])) > 0)
                    {
                        Button btnAction = new Button();
                        btnAction.Margin = new Thickness(5, 5, 5, 5);
                        btnAction.Padding = new Thickness(5, 0, 5, 0);
                        this.panelActions.Children.Add(btnAction);

                        //设置显示文本
                        btnAction.Content = rowAction["FLOW_ACTION_NAME"].ToString();
                        //获得动作实例
                        btnAction.Tag = string.Format(rowAction["FLOW_ACTION_EVENT"].ToString(), listParamValue.ToArray());
                        //设置图标
                        if (!rowAction.IsNull("FLOW_ACTION_IMAGE") && !string.IsNullOrEmpty(rowAction["FLOW_ACTION_IMAGE"].ToString()))
                        {
                            string strImagePath = MainApp._APP_PATH + @"@Images\" + rowAction["FLOW_ACTION_IMAGE"].ToString();
                            if (System.IO.File.Exists(strImagePath))
                            {
                                //获得图像路径
                                System.Windows.Media.Imaging.BitmapImage imageURI = new System.Windows.Media.Imaging.BitmapImage();
                                imageURI.BeginInit();
                                imageURI.UriSource = new Uri(strImagePath, UriKind.Relative);
                                imageURI.EndInit();

                                ImageBrush imageBrush = new ImageBrush();
                                imageBrush.ImageSource = imageURI;

                                if (imageBrush != null)
                                {
                                    btnAction.Background = imageBrush;
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
               MainApp._MessageDialog.ShowException(ex.Message);
            }
        }
        
        //点击按钮处理事件
        private void panelActions_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                Button btnAction = e.OriginalSource as Button;
                if (btnAction == null)
                    return;

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmExecute, btnAction.Content.ToString()) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    string sResult = string.Empty;

                    //动态执行动作关联的代码
                    bool bResult = MainApp.EVENT_Execute(btnAction.Tag.ToString(), out sResult);

                    //xcjt comment 2016-12-29
                    ////判断结果
                    //if (bResult)
                    //{
                    //    //判断是否添加日志
                    //    if (boolAddLog)
                    //    {
                    //        //添加日志
                    //        //MainApp._I_BaseService.AddLog(this.GetLogMessage(btnAction.Content.ToString(), btnAction.Tag.ToString()));
                    //    }
                    //}
                    //else
                    //{
                    //    MainApp._MessageDialog.ShowException(sResult);
                    //}

                    //xcjt add 2016-12-29
                    if(!bResult)
                    {
                        MainApp._MessageDialog.ShowException(sResult);
                    }
                    //是否添加日志
                    if (boolAddLog)
                    {
                        switch (strFlowType)
                        {
                            case "FLOW_MANAGE":
                                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Task, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical,
                                        string.Format("ucStatusFlowActionsPanel.panelActions_Click():任务ID[{0}]_任务类型[{1}]_任务状态[{2}]_条码[{3}]_操作[{4}]_结果[{5} {6}]",
                                                      rowSource["MANAGE_ID"],
                                                      rowSource["MANAGE_TYPE_CODE"],
                                                      rowSource["MANAGE_STATUS"],
                                                      rowSource["STOCK_BARCODE"],
                                                      btnAction.Content.ToString().Replace(" ",""),
                                                      bResult ? "成功" : "失败 ",
                                                      sResult));
                                break;

                            case "FLOW_PLAN":
                                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Plan, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical,
                                        string.Format("ucStatusFlowActionsPanel.panelActions_Click():计划ID[{0}]_计划CODE[{1}]_计划类型[{2}]_计划状态[{3}]_操作[{4}]_结果[{5} {6}]",
                                                      rowSource["PLAN_ID"],
                                                      rowSource["PLAN_CODE"],
                                                      rowSource["PLAN_TYPE_CODE"],
                                                      rowSource["PLAN_STATUS"],
                                                      btnAction.Content.ToString().Replace(" ", ""),
                                                      bResult ? "成功" : "失败 ",
                                                      sResult));
                                break;

                            default:
                                break;
                        }
                    }

                    //执行事件后刷新
                    if (U_ExecuteUpdate != null)
                    {
                        U_ExecuteUpdate();
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }
 
        /// <summary>
        /// 获得消息记录
        /// </summary>
        private string GetLogMessage(string MessageHeader, string ExecuteMethodName)
        {
            StringBuilder strMessage = new StringBuilder();
            strMessage.Append(string.Format("用户：{0}.", MainApp._USER.USER_NAME));
            strMessage.Append(string.Format("操作：{0}.", MessageHeader));
            strMessage.Append(string.Format("调用方法：{0}.", ExecuteMethodName));
            return strMessage.ToString();
        }

        /// <summary>
        /// 清空
        /// </summary>
        public void U_Clear()
        {
            this.panelActions.Children.Clear();
        }
    }
}
