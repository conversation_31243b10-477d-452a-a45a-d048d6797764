﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     siasun
 *       日期：     2014/11/3
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;

    /// <summary>
    /// APPLY_TYPE 
    /// </summary>
    [Serializable]
    public class APPLY_TYPE
    {
        public APPLY_TYPE()
        {

        }

        private int _apply_id;
        private string _apply_type_code;
        private string _apply_type_name;
        private string _apply_type_class;
        private int _apply_type_order;
        private string _apply_type_flag;
        private string _apply_type_property;      

        ///<sumary>
        /// 
        ///</sumary>
        public int APPLY_ID
        {
            get { return _apply_id; }
            set { _apply_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        public string APPLY_TYPE_CODE
        {
            get { return _apply_type_code; }
            set { _apply_type_code = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        public string APPLY_TYPE_NAME
        {
            get { return _apply_type_name; }
            set { _apply_type_name = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        public string APPLY_TYPE_CLASS
        {
            get { return _apply_type_class; }
            set { _apply_type_class = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        public int APPLY_TYPE_ORDER
        {
            get { return _apply_type_order; }
            set { _apply_type_order = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        public string APPLY_TYPE_FLAG
        {
            get { return _apply_type_flag; }
            set { _apply_type_flag = value; }
        }

        public string APPLY_TYPE_PROPERTY
        {
            get { return _apply_type_property; }
            set { _apply_type_property = value; }
        }


    }
}
