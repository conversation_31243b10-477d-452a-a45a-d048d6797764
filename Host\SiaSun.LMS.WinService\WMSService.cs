﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Runtime.InteropServices;
using System.Threading;
using Quartz;
using Quartz.Impl;


namespace SiaSun.LMS.WinService
{
    public partial class WMSService : ServiceBase
    {

        ISchedulerFactory schedulerFactory = new StdSchedulerFactory();

        IScheduler scheduler;

        public WMSService()
        {
            InitializeComponent();
        }

        protected override void OnStart(string[] args)
        {
            try
            {
                SiaSun.LMS.Implement.TCP.CommunicationOperation.Launch();
                SiaSun.LMS.Implement.DZBQ.DZBQOpration.Connect();
                SiaSun.LMS.Implement.YiChuanPtlDevice.PtlDeviceCommOperation.Connect();

                Program.sysLog.Error(string.Format("与电子标签控制器通讯或者与工作站通讯成功"));
            }
            catch (Exception ex)
            {
                Program.sysLog.Error(string.Format("与电子标签控制器通讯或者与工作站通讯失败 {0}", ex.Message));
                throw;
            }

            try
            {
                Program._HouseUrl = SiaSun.LMS.Common.StringUtil.GetConfig("SiaSunSrvUrl");
                Program._InterfaceUrl = SiaSun.LMS.Common.StringUtil.GetConfig("InterfaceUrl");

                ServiceHostGroup.StartAllConfiguredServices("House", Program._HouseUrl);
                ServiceHostGroup.StartAllConfiguredServices("All", Program._InterfaceUrl);

                Program.sysLog.Error(string.Format("启动新松立库系统应用服务成功"));
            }
            catch (Exception ex)
            {
                Program.sysLog.Error(string.Format("启动新松立库系统应用服务失败 {0}", ex.Message));
                throw;
            }
                       
            try
            {
                scheduler = schedulerFactory.GetScheduler();
                scheduler.Start();

                Program.sysLog.Info("启动定时调度作业成功。");
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("启动定时调度作业失败 "+ex.Message);
                throw;
            }
        }

        protected override void OnStop()
        {
            try
            {
                SiaSun.LMS.Implement.TCP.CommunicationOperation.Dispose();

                Program.sysLog.Info("停止与工作站连接成功。");
            }
            catch (Exception ex)
            {
                Program.sysLog.Info("停止与工作站连接失败。" + ex.Message);
            }

            try
            {
                scheduler.Shutdown();

                Program.sysLog.Info("停止定时调度作业成功。");
            }
            catch (Exception ex)
            {
                Program.sysLog.Info("停止定时调度作业失败。" + ex.Message);
            }

            try
            {
                ServiceHostGroup.CloseAllServices();

                Program.sysLog.Info(string.Format("停止新松立库系统应用服务成功"));
            }
            catch (Exception ex)
            {
                Program.sysLog.Info(string.Format("停止新松立库系统应用服务失败。 {0}", ex.Message));
            }
        }

    }
}
