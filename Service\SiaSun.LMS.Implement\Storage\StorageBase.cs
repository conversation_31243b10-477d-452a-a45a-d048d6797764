﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{

    public class StorageBase : S_BaseService
    {
        /// <summary>
        /// 生成库存
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageCreate(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

                if (null == mMANAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                    return bResult;
                }

                IList<MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN;

                SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST;

                mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

                if (null == mSTORAGE_MAIN)
                {
                    mSTORAGE_MAIN = new SiaSun.LMS.Model.STORAGE_MAIN();

                    mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                    mSTORAGE_MAIN.GOODS_TEMPLATE_ID = mMANAGE_MAIN.GOODS_TEMPLATE_ID;

                    mSTORAGE_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;

                    mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;

                    mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;

                    mSTORAGE_MAIN.STORAGE_REMARK = mMANAGE_MAIN.MANAGE_REMARK;

                    this._P_STORAGE_MAIN.Add(mSTORAGE_MAIN);
                }
                else
                {
                    mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                    mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;

                    this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                }


                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);


                    if (mSTORAGE_LIST != null)
                    {
                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        //wdz alter 2018-09-12 STORAGE_LIST_QUANTITY=0时 不删除STORAGE_LIST
                        //if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY == 0)
                        //    this._P_STORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);
                        //else
                        //    this._P_STORAGE_LIST.Update(mSTORAGE_LIST);

                        if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY == 0)
                        {
                            this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                        }
                        else
                        {
                            this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                        }
                    }
                    else
                    {

                        mSTORAGE_LIST = new SiaSun.LMS.Model.STORAGE_LIST();

                        mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;

                        mSTORAGE_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;

                        mSTORAGE_LIST.BOX_BARCODE = mMANAGE_LIST.BOX_BARCODE;

                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        mSTORAGE_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;

                        bResult = this._S_GoodsService.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mSTORAGE_LIST, mMANAGE_LIST, out sResult);

                        if (!bResult)
                        {
                            return bResult;
                        }

                        mSTORAGE_LIST.GOODS_PROPERTY1 = mMANAGE_LIST.GOODS_PROPERTY1;
                        mSTORAGE_LIST.GOODS_PROPERTY2 = mMANAGE_LIST.GOODS_PROPERTY2;
                        mSTORAGE_LIST.GOODS_PROPERTY3 = mMANAGE_LIST.GOODS_PROPERTY3;
                        mSTORAGE_LIST.GOODS_PROPERTY4 = mMANAGE_LIST.GOODS_PROPERTY4;
                        mSTORAGE_LIST.GOODS_PROPERTY5 = mMANAGE_LIST.GOODS_PROPERTY5;
                        mSTORAGE_LIST.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                        mSTORAGE_LIST.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                        mSTORAGE_LIST.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                        //保存原始入库时间 2020-10-15 19:35:10
                        //mSTORAGE_LIST.ENTRY_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                        DateTime dateTime;
                        if(DateTime.TryParse(mMANAGE_LIST.ORIGIN_ENTRY_TIME,out dateTime))
                        {
                            mSTORAGE_LIST.ENTRY_TIME = mMANAGE_LIST.ORIGIN_ENTRY_TIME;
                        }
                        else
                        {
                            mSTORAGE_LIST.ENTRY_TIME = Common.StringUtil.GetDateTime();
                        }

                        mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        mSTORAGE_LIST.STORAGE_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                        this._P_STORAGE_LIST.Add(mSTORAGE_LIST);
                    }

                }

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN_EXIST = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

                if (mSTORAGE_MAIN_EXIST != null)
                {
                    if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_EXIST.STORAGE_ID).Count == 0)
                    {
                        this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN_EXIST.STORAGE_ID);
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 删除库存
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageDelete(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;

                sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                return bResult;
            }


            IList<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

            SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = null;

            SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST = null;

            try
            {
                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (null == mSTORAGE_LIST)
                    {
                        bResult = false;

                        sResult = string.Format("库存索引 {0} 不存在", mMANAGE_LIST.STORAGE_LIST_ID);

                        return bResult;
                    }

                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0)
                    {
                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        this._P_STORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);
                        //wdz add 2018-04-29
                        this._P_STORAGE_DETAIL.DeleteStorageListId(mSTORAGE_LIST.STORAGE_LIST_ID);

                        if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_LIST.STORAGE_ID).Count == 0)
                        {
                            mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                            if (null != mSTORAGE_MAIN)
                            {
                                if (this._P_STORAGE_MAIN.GetListCellID(mSTORAGE_MAIN.CELL_ID).Count == 0)
                                {
                                    this._S_CellService.CellUpdateStatus(mSTORAGE_MAIN.CELL_ID, SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(), string.Empty, out sResult);
                                }
                                if (mSTORAGE_MAIN.STORAGE_ID > 3)
                                {
                                    this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);
                                }
                            }
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 库存转移
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageMove(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

                if (null == mMANAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                    return bResult;
                }

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN;

                SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST;

                IList<MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (null == mSTORAGE_LIST)
                    {
                        bResult = false;

                        sResult = string.Format("库存索引 {0} 不存在", mMANAGE_LIST.STORAGE_LIST_ID);

                        return bResult;
                    }


                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0)
                    {
                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        this._P_STORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);

                        if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_LIST.STORAGE_ID).Count == 0)
                        {
                            mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                            if (null != mSTORAGE_MAIN)
                            {
                                if (this._P_STORAGE_MAIN.GetListCellID(mSTORAGE_MAIN.CELL_ID).Count == 0)
                                {
                                    this._S_CellService.CellUpdateStatus(mSTORAGE_MAIN.CELL_ID, SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(), string.Empty, out sResult);
                                }

                                this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);
                            }

                        }
                    }

                    mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelCellIDStockBarcode(mMANAGE_MAIN.END_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE);

                    if (null == mSTORAGE_MAIN)
                    {
                        mSTORAGE_MAIN = new SiaSun.LMS.Model.STORAGE_MAIN();
                        mSTORAGE_MAIN.GOODS_TEMPLATE_ID = mMANAGE_MAIN.GOODS_TEMPLATE_ID;
                        mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;
                        mSTORAGE_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;
                        mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                        mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;
                        mSTORAGE_MAIN.STORAGE_REMARK = mMANAGE_MAIN.MANAGE_REMARK;

                        this._P_STORAGE_MAIN.Add(mSTORAGE_MAIN);
                    }
                    else
                    {
                        //xcjt add 2017-01-04
                        if (!string.IsNullOrEmpty(mMANAGE_MAIN.CELL_MODEL))
                        {
                            mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                        }
                        if (!string.IsNullOrEmpty(mMANAGE_MAIN.FULL_FLAG))
                        {
                            mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;
                        }

                        mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                    }


                    mSTORAGE_LIST = new SiaSun.LMS.Model.STORAGE_LIST();

                    mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;

                    mSTORAGE_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;

                    mSTORAGE_LIST.BOX_BARCODE = mMANAGE_LIST.BOX_BARCODE;

                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    mSTORAGE_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;

                    bResult = this._S_GoodsService.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mSTORAGE_LIST, mMANAGE_LIST, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    mSTORAGE_LIST.GOODS_PROPERTY1 = mMANAGE_LIST.GOODS_PROPERTY1;
                    mSTORAGE_LIST.GOODS_PROPERTY2 = mMANAGE_LIST.GOODS_PROPERTY2;
                    mSTORAGE_LIST.GOODS_PROPERTY3 = mMANAGE_LIST.GOODS_PROPERTY3;
                    mSTORAGE_LIST.GOODS_PROPERTY4 = mMANAGE_LIST.GOODS_PROPERTY4;
                    mSTORAGE_LIST.GOODS_PROPERTY5 = mMANAGE_LIST.GOODS_PROPERTY5;
                    mSTORAGE_LIST.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                    mSTORAGE_LIST.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                    mSTORAGE_LIST.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                    //wdz comment 2018-02-06
                    //mSTORAGE_LIST.ENTRY_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mSTORAGE_LIST.STORAGE_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                    this._P_STORAGE_LIST.Add(mSTORAGE_LIST);
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }


        /// <summary>
        /// 库存转移
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageMove(string STOCK_BARCODE, int START_CELL_ID, int END_CELL_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = null;

                mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(STOCK_BARCODE);

                if (null == mSTORAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("未找到库存{0}", STOCK_BARCODE);

                    return bResult;
                }

                mSTORAGE_MAIN.CELL_ID = END_CELL_ID;

                this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 整理下架后将库存移动到虚拟箱中
        /// 将
        /// wdz add 2019-02-25
        /// </summary>
        public bool VirtualStorageMoveIn(Enum.ArrangeWorkMode workMode, string virtualAddress, string stockBarcode, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                Model.WH_CELL mEND_CELL = this._P_WH_CELL.GetModel(virtualAddress.TrimStart('V'));
                if (mEND_CELL == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到整理工位_编码[{0}]", virtualAddress.TrimStart('V'));
                    return bResult;
                }
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到库存信息_条码[{0}]", stockBarcode);
                    return bResult;
                }

                IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count < 1)
                {
                    bResult = false;
                    sResult = string.Format("未找到库存列表_条码[{0}]_库存ID[{1}]", stockBarcode, mSTORAGE_MAIN.STORAGE_ID);
                    return bResult;
                }

                Model.STORAGE_MAIN mSTORAGE_MAIN_V = this._P_STORAGE_MAIN.GetModelStockBarcode(virtualAddress);
                if (mSTORAGE_MAIN_V == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到整理箱工位虚拟货位库存信息_条码[{0}]", virtualAddress);
                    return bResult;
                }

                foreach (var item in lsSTORAGE_LIST)
                {
                    item.STORAGE_ID = mSTORAGE_MAIN_V.STORAGE_ID;
                    item.UPDATE_TIME = Common.StringUtil.GetDateTime();     //2020-05-06 16:26:09
                    this._P_STORAGE_LIST.Update(item);
                }

                //wdz add 2019-07-28
                //如果是整理未满箱模式则要合并库存
                if (workMode == Enum.ArrangeWorkMode.ArrangeGoods)
                {
                    var lsStorageListForMerge = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_V.STORAGE_ID);
                    if (lsStorageListForMerge == null || lsStorageListForMerge.Count < 1)
                    {
                        bResult = false;
                        sResult = string.Format("未找到整理箱工位虚拟库存[{0}]的库存列表信息", virtualAddress);
                        return bResult;
                    }

                    //将相同物料的STORAGE_LIST合并
                    var storgaeListGroup = lsStorageListForMerge.GroupBy(r => r.GOODS_ID);
                    foreach (var groupItem in storgaeListGroup)
                    {
                        if (groupItem.Count() > 1)
                        {
                            Model.STORAGE_LIST retainStorageList = null;
                            foreach (var storageListItem in groupItem.OrderBy(r => r.ENTRY_TIME))
                            {
                                if (retainStorageList == null)
                                {
                                    retainStorageList = storageListItem;
                                }
                                else
                                {
                                    retainStorageList.STORAGE_LIST_QUANTITY += storageListItem.STORAGE_LIST_QUANTITY;
                                    this._P_STORAGE_LIST.Update(retainStorageList);

                                    this._P_STORAGE_LIST.Delete(storageListItem.STORAGE_LIST_ID);

                                    //记日志
                                    this.CreateSysLog(Enum.LogThread.Storage, "System", Enum.LOG_LEVEL.Critical, string.Format("整理未满箱时将库存单(ID[{0}])的[{1}]个物料(ID[{2}])合并到了库存单(ID[{3}])中_存单(ID[{0}])删除",
                                        storageListItem.STORAGE_LIST_ID, storageListItem.STORAGE_LIST_QUANTITY, storageListItem.GOODS_ID, retainStorageList.STORAGE_LIST_ID));
                                }
                            }
                        }
                    }
                }

                //原箱变空箱库存
                mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                mSTORAGE_MAIN.CELL_ID = mEND_CELL.CELL_ID;
                this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                Model.STORAGE_LIST mSTORAGE_LIST = new STORAGE_LIST();
                mSTORAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                mSTORAGE_LIST.GOODS_PROPERTY1 = "0";//箱型
                mSTORAGE_LIST.GOODS_PROPERTY2 = "green";//颜色
                mSTORAGE_LIST.GOODS_PROPERTY3 = "1";//整理置空标记
                mSTORAGE_LIST.STORAGE_LIST_QUANTITY = 1;
                mSTORAGE_LIST.STORAGE_LIST_REMARK = "整理原箱下架任务完成后标记为空箱库存";
                mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;
                mSTORAGE_LIST.ENTRY_TIME = Common.StringUtil.GetCurDateTimeString();
                this._P_STORAGE_LIST.Add(mSTORAGE_LIST);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("发生异常_异常信息[{0}]", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this.CreateSysLog(Enum.LogThread.Storage, "System", Enum.LOG_LEVEL.Information, string.Format("移库到整理箱工位虚拟货位成功_虚拟货位[{0}]_箱条码[{1}]", virtualAddress, stockBarcode));
                }
                else
                {
                    this.CreateSysLog(Enum.LogThread.Storage, "System", Enum.LOG_LEVEL.Error, string.Format("移库到整理箱工位虚拟货位成功_虚拟货位[{0}]_箱条码[{1}]_信息[{2}]", virtualAddress, stockBarcode, sResult));
                }
            }
            return bResult;
        }

    }
}
