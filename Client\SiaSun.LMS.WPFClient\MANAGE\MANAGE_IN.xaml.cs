﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{


    public partial class MANAGE_IN : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;
        string _goodsTypeCode = string.Empty;

        public MANAGE_IN(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.ucQueryGoods.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQueryGoods_U_Query);

            this.gridGoods.gridApp.MouseDoubleClick += new MouseButtonEventHandler(gridApp_MouseDoubleClick);
        
            this.mMANAGE_TYPE =(Model.MANAGE_TYPE) MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;
        }

        public MANAGE_IN(string manageTypeCode,string goodsTypeCode):this(manageTypeCode)
        {
            this._goodsTypeCode = goodsTypeCode;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                this.InitQueryControl();

                this.GoodsBind(string.Empty);

                this.InitManagePosition();

                this.ManageList_Bind();

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void InitManagePosition()
        {
            try
            {
                this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void InitQueryControl()
        {
            this.ucQueryGoods.U_WindowName = this.GetType().Name;
            this.ucQueryGoods.U_XmlTableName = "GOODS_MAIN";
            this.ucQueryGoods.U_InitControl();
        }

        private void GoodsBind(string QueryWhere)
        {
            try
            {
                this.gridGoods.U_WindowName = this.GetType().Name;
                this.gridGoods.U_TableName = "V_GOODS";
                this.gridGoods.U_XmlTableName = "GOODS_MAIN";
                this.gridGoods.U_OrderField = "GOODS_ORDER,GOODS_CODE";
                this.gridGoods.U_Where = string.Format("GOODS_TYPE_CODE ='{0}' and {1}", this._goodsTypeCode, string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere);
                this.gridGoods.U_AllowOperatData = false;
                this.gridGoods.U_AllowChecked = false;
                this.gridGoods.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        void ucQueryGoods_U_Query(string QueryWhere)
        {
            this.GoodsBind(QueryWhere);
        }


        void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (gridGoods.gridApp.SelectedItem == null)
                return;

            //获得物料记录
            DataRowView rowViewGoods = this.gridGoods.gridApp.SelectedItem as DataRowView;

            //获得添加行
            DataTable tablePlanList = this.MANAGE_LIST_Add_By_GOODS( new string[] { rowViewGoods["GOODS_ID"].ToString() });

            //添加行数据
            this.gridManageList.U_AddTabPageRows(tablePlanList.Rows.Cast<DataRow>().ToArray());

            //注册事件
            Register_DataTable_Event();
        }


        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.gridManageList.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.RowChanged -= new DataRowChangeEventHandler(tableSource_RowChanged);
                    tableSource.RowChanged += new DataRowChangeEventHandler(tableSource_RowChanged);

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }


        /// <summary>
        /// 表单数据校验
        /// </summary>
        void tableSource_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            if (e.Row.RowState == DataRowState.Added || e.Row.RowState == DataRowState.Modified)
            {
                //判断数量
                if (e.Row.IsNull("MANAGE_LIST_QUANTITY") || Convert.ToDecimal(e.Row["MANAGE_LIST_QUANTITY"]) <= 0 || string.IsNullOrEmpty(e.Row["MANAGE_LIST_QUANTITY"].ToString()))
                {
                    e.Row.RowError = string.Format("请检查数量是否合法!");
                }

            }
        }

        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "MANAGE_LIST_QUANTITY":
                    bResult = (string.Empty != e.ProposedValue.ToString() && !string.IsNullOrEmpty(e.ProposedValue.ToString()) && Convert.ToInt32(e.ProposedValue) > 0);

                    sResult = string.Format("{0}{1}不能小于等于0!", e.Column.ColumnName, e.ProposedValue.ToString());
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                    }
                    break;
            }


        }

        /// <summary>
        /// 添加计划信息返回表单集合
        /// </summary>
        public DataTable MANAGE_LIST_Add_By_GOODS( IList<string> listGOODS_ID)
        {
            //创建集合
            string strSql = string.Format("SELECT * FROM V_MANAGE_LIST WHERE MANAGE_ID={0}", 0);
            using (DataTable tableManageList = MainApp._I_BaseService.GetList(strSql))
            {
                foreach (string strGoodsID in listGOODS_ID)
                {
                    //获得物料实体
                    SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = MainApp._I_GoodsService.GoodsGetModelGoodsID(Convert.ToInt32(strGoodsID));

                    SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = MainApp._I_GoodsService.GoodsClassGetModelGoodsClassID(mGOODS_MAIN.GOODS_CLASS_ID);

                    if (mGOODS_MAIN != null && mGOODS_CLASS != null)
                    {
                        DataRow rowManageList = tableManageList.NewRow();
                        rowManageList["GOODS_ID"] = mGOODS_MAIN.GOODS_ID;
                        rowManageList["GOODS_CODE"] = mGOODS_MAIN.GOODS_CODE;
                        rowManageList["GOODS_NAME"] = mGOODS_MAIN.GOODS_NAME;                       
                        rowManageList["GOODS_TYPE_ID"] = mGOODS_CLASS.GOODS_TYPE_ID;
                        rowManageList["GOODS_UNITS"] = mGOODS_MAIN.GOODS_UNITS;

                        rowManageList["GOODS_CONST_PROPERTY1"] = mGOODS_MAIN.GOODS_CONST_PROPERTY1;
                        rowManageList["GOODS_CONST_PROPERTY2"] = mGOODS_MAIN.GOODS_CONST_PROPERTY2;
                        rowManageList["GOODS_CONST_PROPERTY3"] = mGOODS_MAIN.GOODS_CONST_PROPERTY3;
                        rowManageList["GOODS_CONST_PROPERTY4"] = mGOODS_MAIN.GOODS_CONST_PROPERTY4;
                        rowManageList["GOODS_CONST_PROPERTY5"] = mGOODS_MAIN.GOODS_CONST_PROPERTY5;
                        rowManageList["GOODS_CONST_PROPERTY6"] = mGOODS_MAIN.GOODS_CONST_PROPERTY6;
                        rowManageList["GOODS_CONST_PROPERTY7"] = mGOODS_MAIN.GOODS_CONST_PROPERTY7;
                        rowManageList["GOODS_CONST_PROPERTY8"] = mGOODS_MAIN.GOODS_CONST_PROPERTY8;

                        rowManageList["MANAGE_LIST_QUANTITY"] = 0;

                        tableManageList.Rows.Add(rowManageList);
                    }
                }
                return tableManageList;
            }
        }


        /// <summary>
        /// 任务列表绑定
        /// </summary>
        private void ManageList_Bind()
        {
            this.gridManageList.U_Clear();

            //设置属性
            this.gridManageList.U_WindowName = this.GetType().Name;
            this.gridManageList.U_TableName = "V_MANAGE_LIST";
            this.gridManageList.U_XmlTableName = "V_MANAGE_LIST";
            this.gridManageList.U_OrderField = "MANAGE_LIST_ID";
            this.gridManageList.U_Where = "MANAGE_LIST_ID=0";

            this.gridManageList.U_AllowAdd = System.Windows.Visibility.Collapsed;
            this.gridManageList.U_AllowSave = System.Windows.Visibility.Collapsed;
            this.gridManageList.U_AllowEdit = System.Windows.Visibility.Collapsed;
            this.gridManageList.U_AllowChecked = false;
            this.gridManageList.U_AllowShowPage = false;

            this.gridManageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridManageList.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.gridManageList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridManageList.U_SplitPropertyColumn = "GOODS_PROPERTY";

            try
            {
                this.gridManageList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnSave":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 确认
        /// </summary>
        private void CreateTask()
        {
            bool bResult = false;
            string sResult = string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //结束当前编辑操作
                this.gridManageList.U_EndCurrentEdit();

                //检验数据是否合法
                DataTable tableSource = this.gridManageList.U_DataSource;

                if (tableSource == null)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Input);
                    return;
                }

                //根据数据源获得数据列表
                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableSource, null);

                //判断是否填写数据
                if (listMANAGE_LIST.Count <= 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Input);
                    return;
                }

                //判断数据是否合法
                if (tableSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }

                //检查拆分属性填写是否合法
                if (!this.gridManageList.U_Check_Split_Property("GOODS_PROPERTY", out sResult))
                {
                    MainApp._MessageDialog.ShowResult(false, sResult);
                    return;
                }

                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                {
                    return;
                }

                //wdz add 2018-01-20 校验是否符合箱格逻辑和要求
                if(listMANAGE_LIST.Where(r=>string.IsNullOrEmpty(r.BOX_BARCODE)).Count()>0)
                {
                    MainApp._MessageDialog.ShowResult(false, "请确保组箱物料所在格以全部选定");
                    return;
                }
                if (listMANAGE_LIST.GroupBy(r=>r.BOX_BARCODE).Count()!=listMANAGE_LIST.GroupBy(r=>r.GOODS_ID).Count())
                {
                    MainApp._MessageDialog.ShowResult(false, "请确保不同物料分格放置并且同种物料只占一格");
                    return;
                }
                int cellModel = 0;
                if(!int.TryParse(this.ucManagePosition.U_CELL_MODEL,out cellModel) || cellModel<listMANAGE_LIST.Count)
                {
                    MainApp._MessageDialog.ShowResult(false, "容器规格有误或者所选规格容器不足以容纳所有组箱物料");
                    return;
                }
                int maxGridNo = 0;
                if(!int.TryParse(listMANAGE_LIST.Max(r=>r.BOX_BARCODE).ToString(),out maxGridNo) || maxGridNo>cellModel)
                {
                    MainApp._MessageDialog.ShowResult(false, "物料格子号有误或者所选格子号超过了箱中所有格子数");
                    return;
                }

                //提示确认
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmAssembly, this.ucManagePosition.U_STOCK_BARCODE) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //调用入库函数配盘入库

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = this.ucManagePosition.U_CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingSend.ToString();
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE;
                                        
                    bResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                             "ManageCreate",
                                                              new object[] { mMANAGE_MAIN,
                                                                               listMANAGE_LIST, 
                                                                               true, 
                                                                               this.ucManagePosition.U_CheckStockExistStorage,
                                                                               this.ucManagePosition.U_AutoCompleteTask,
                                                                               this.ucManagePosition.U_AutoDownloadControlTask, 
                                                                               },
                                                               out sResult);


                    //检验执行结果
                    if (bResult)
                    {
                        if (!this.rbRefresh.IsChecked.Value)
                        {
                            this.gridManageList.U_Update();
                        }
                        this.Refresh();
                    }

                    MainApp._MessageDialog.ShowResult(bResult, sResult);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            this.ucManagePosition.U_Refresh();
        }



    }
}
