﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 整理未满箱任务下发
    /// </summary>
    public class ArrangeEmptyBoxTask : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _boxType = string.Empty;            //箱型
            private string _emptyQuantity = string.Empty;      //空格数
            private string _boxQuantity = string.Empty;        //箱数
            private string _uniqueCode = string.Empty;          //唯一码
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源

            /// <summary>
            /// 箱型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 空格数
            /// </summary>
            public string emptyQuantity
            {
                get { return _emptyQuantity; }
                set { _emptyQuantity = value; }
            }

            /// <summary>
            /// 箱数
            /// </summary>
            public string boxQuantity
            {
                get { return _boxQuantity; }
                set { _boxQuantity = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            int iPlanId = 0;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                //wdz add 2019-06-29
                bResult = false;
                outJson = "ArrangeEmptyBoxTask.NotifyMethod:整理箱接口已停用";
                return bResult;

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.boxQuantity) || string.IsNullOrEmpty(taskInfo.uniqueCode) || string.IsNullOrEmpty(taskInfo.boxType) || string.IsNullOrEmpty(taskInfo.emptyQuantity))
                {
                    bResult = false;
                    outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:入参存在空值");
                    return bResult;
                }

                int boxQuantity = 0, emptyQuantity = 0;
                if (!(int.TryParse(taskInfo.boxQuantity, out boxQuantity) && int.TryParse(taskInfo.emptyQuantity, out emptyQuantity)))
                {
                    bResult = false;
                    outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:传入的箱数或空格数格式不正确 传入箱数_{0} 传入空格数_{1}", taskInfo.boxQuantity, taskInfo.emptyQuantity);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModelPlanCode(taskInfo.uniqueCode, string.Empty, string.Empty);
                if (mPLAN_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:传入唯一码已存在计划 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                string defaultPlanOutStation = string.Empty;
                if(!this._S_SystemService.GetSysParameter("DefaultArrangeOutStation",out defaultPlanOutStation))
                {
                    bResult = false;
                    outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:默认整理不满箱出库站台未配置_请联系立库软件工程师");
                    return bResult;
                }

                mPLAN_MAIN = new Model.PLAN_MAIN();
                mPLAN_MAIN.PLAN_CODE = taskInfo.uniqueCode;
                mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mPLAN_MAIN.PLAN_CREATE_TIME = Common.StringUtil.GetDateTime();
                mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString();
                mPLAN_MAIN.PLAN_INOUT_STATION = defaultPlanOutStation;
                string planLevel = string.Empty;
                mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("ArrangeOutPlanLevel", out planLevel) ? planLevel : "0";

                Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                mPLAN_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("arrangeTask").GOODS_ID;
                mPLAN_LIST.GOODS_PROPERTY1 = taskInfo.boxType;
                mPLAN_LIST.GOODS_PROPERTY2 = taskInfo.emptyQuantity;
                mPLAN_LIST.PLAN_LIST_QUANTITY = boxQuantity;

                List<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();
                lsPLAN_LIST.Add(mPLAN_LIST);

                object[] invokeOutParams = new object[] { };
                bResult = this.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST, iPlanId, outJson }, out invokeOutParams);
                int.TryParse(invokeOutParams[2].ToString(), out iPlanId);
                if (!bResult)
                {
                    outJson = invokeOutParams[3].ToString();
                    return bResult;
                }

                bResult = this.Invoke("PlanBase", "PlanOutDownLoad", new object[] { iPlanId }, out outJson);
                if (!bResult)
                {
                    string strTemp = string.Empty;
                    if (!this.Invoke("PlanBase", "PlanCancel", new object[] { iPlanId ,false}, out strTemp))
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("ArrangeEmptyBoxTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划失败_{1}_计划ID[{2}]", outJson, strTemp, iPlanId));
                    }
                    else
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("ArrangeEmptyBoxTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划成功_计划ID[{1}]", outJson, iPlanId));
                    }

                    outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:执行计划失败 {0}", outJson);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("ArrangeEmptyBoxTask.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
