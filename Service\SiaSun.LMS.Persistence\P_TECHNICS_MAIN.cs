﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// TECHNICS_MAIN
	/// </summary>
	public class P_TECHNICS_MAIN : P_Base_House
	{
		public P_TECHNICS_MAIN ()
		{
			//
			// TODO: 此处添加TECHNICS_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<TECHNICS_MAIN> GetList()
		{
			return ExecuteQueryForList<TECHNICS_MAIN>("TECHNICS_MAIN_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(TECHNICS_MAIN technics_main)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("TECHNICS_MAIN");
                technics_main.TECHNICS_ID = id;
            }

            return ExecuteInsert("TECHNICS_MAIN_INSERT",technics_main);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(TECHNICS_MAIN technics_main)
		{
			return ExecuteUpdate("TECHNICS_MAIN_UPDATE",technics_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public TECHNICS_MAIN GetModel(System.Int32 TECHNICS_ID)
		{
			return ExecuteQueryForObject<TECHNICS_MAIN>("TECHNICS_MAIN_SELECT_BY_ID",TECHNICS_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 TECHNICS_ID)
		{
			return ExecuteDelete("TECHNICS_MAIN_DELETE",TECHNICS_ID);
		}
		

	}
}
