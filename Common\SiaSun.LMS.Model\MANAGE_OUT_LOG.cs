﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     tzyg
 *       日期：     2017-03-02
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/

namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// MANAGE_OUT_LOG 
    /// </summary>
    [Serializable]
    [DataContract]
    public class MANAGE_OUT_LOG
    {
        public MANAGE_OUT_LOG()
        {

        }

        private int _manage_out_log_id;
        private int _manage_id;
        private string _manage_out_log_stock;
        private string _manage_out_log_goods;
        private string _manage_out_log_date;
        private string _manage_out_log_flag;
        private string _manage_out_log_remark;

        ///<sumary>
        /// 记录ID
        ///</sumary>
        [DataMember]
        public int MANAGE_OUT_LOG_ID
        {
            get { return _manage_out_log_id; }
            set { _manage_out_log_id = value; }
        }

        ///<sumary>
        /// 原任务ID
        ///</sumary>
        [DataMember]
        public int MANAGE_ID
        {
            get { return _manage_id; }
            set { _manage_id = value; }
        }

        ///<sumary>
        /// 托盘条码
        ///</sumary>
        [DataMember]
        public string MANAGE_OUT_LOG_STOCK
        {
            get { return _manage_out_log_stock; }
            set { _manage_out_log_stock = value; }
        }

        ///<sumary>
        /// 物料条码
        ///</sumary>
        [DataMember]
        public string MANAGE_OUT_LOG_GOODS
        {
            get { return _manage_out_log_goods; }
            set { _manage_out_log_goods = value; }
        }

        ///<sumary>
        /// 日期
        ///</sumary>
        [DataMember]
        public string MANAGE_OUT_LOG_DATE
        {
            get { return _manage_out_log_date; }
            set { _manage_out_log_date = value; }
        }

        ///<sumary>
        /// 标记
        ///</sumary>
        [DataMember]
        public string MANAGE_OUT_LOG_FLAG
        {
            get { return _manage_out_log_flag; }
            set { _manage_out_log_flag = value; }
        }

        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string MANAGE_OUT_LOG_REMARK
        {
            get { return _manage_out_log_remark; }
            set { _manage_out_log_remark = value; }
        }
    }
}
