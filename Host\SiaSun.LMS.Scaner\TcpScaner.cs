﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Xml.Serialization;

namespace SiaSun.LMS.Scaner
{
    public class TcpScaner
    {
        [XmlAttribute("clientName")]
        public string clientName;

        [XmlAttribute("deviceType")]
        public string deviceType;

        [XmlAttribute("connType")]
        public string connType;

        [XmlAttribute("clientIp")]
        public string clientIp;

        [XmlAttribute("clientPort")]
        public int clientPort;

        [XmlAttribute("stationCode")]
        public string stationCode;

        [XmlAttribute("applyType")]
        public string applyType;
    }


    [XmlRoot("TcpScaners")]
    public class TcpScaners
    {
        [XmlElement("client", typeof(TcpScaner))]
        public List<TcpScaner> Clients = new List<TcpScaner>();
    }

}
