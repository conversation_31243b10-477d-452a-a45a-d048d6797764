<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="CheckBoxFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border>
                        <Rectangle Margin="15,0,0,0" Stroke="#60000000" StrokeThickness="1" StrokeDashArray="1 2" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type CheckBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource CheckBoxFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />
        <Setter Property="Template" Value="{DynamicResource CheckBoxTemplate}" />
    </Style>

    <ControlTemplate x:Key="CheckBoxTemplate" TargetType="{x:Type CheckBox}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="Opacity" To="1" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="Opacity" To="0" />
            </Storyboard>
            <Storyboard x:Key="PressedOn">
                <ColorAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[1].(GradientStop.Color)" To="#FFF28A27" />
                <ColorAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[0].(GradientStop.Color)" To="#FFF4D9BE" />
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="Opacity" To="1" />
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgroundFill" Storyboard.TargetProperty="Opacity" To="1" />
            </Storyboard>
            <Storyboard x:Key="PressedOff">
                <ColorAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[1].(GradientStop.Color)" To="#FFFDDA81" />
                <ColorAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="(Shape.Stroke).(GradientBrush.GradientStops)[0].(GradientStop.Color)" To="#FFFCE7AF" />
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BoxOver" Storyboard.TargetProperty="Opacity" To="0" />
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgroundFill" Storyboard.TargetProperty="Opacity" To="0" />
            </Storyboard>
            <Storyboard x:Key="DisabledOn">
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledVisualElement" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>

            </Storyboard>
            <Storyboard x:Key="CheckedOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BoxPress" Storyboard.TargetProperty="Opacity" To="1" />
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="CheckIcon" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="CheckedOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BoxPress" Storyboard.TargetProperty="Opacity" To="0" />
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="CheckIcon" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.4000000" Value="{x:Static Visibility.Collapsed}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="IndeterminateOn">

                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="IndeterminateIcon" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="IndeterminateOff">

                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="IndeterminateIcon" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Collapsed}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusedVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusedVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <BulletDecorator Background="Transparent">
            <BulletDecorator.Bullet>
                <Grid>
                    <Rectangle x:Name="Background" Margin="1" Width="13" Height="13" RadiusX="0" RadiusY="0" Fill="{DynamicResource CheckBoxBackgroundBrush}" Stroke="{DynamicResource CheckBoxBorderBrush}" StrokeThickness="1" />
                    <Rectangle x:Name="BoxFill" Width="9" Height="9" RadiusX="0" RadiusY="0" Fill="{DynamicResource CheckBoxInnerBoxBackgroundBrush}" StrokeThickness="1" Stroke="{DynamicResource CheckBoxInnerBoxBorderBrush}"/>
                    <Rectangle x:Name="BackgroundFill" Margin="1" Width="13" Height="13" RadiusX="0" RadiusY="0" Fill="{DynamicResource CheckBoxBackgroundFillBrush}" Stroke="#FF5577A3" StrokeThickness="1" Opacity="0" />
                    <Rectangle x:Name="BoxOver" Margin="3" Width="9" Height="9" RadiusX="0" RadiusY="0" Fill="{DynamicResource CheckBoxMouseOverBrush}" StrokeThickness="1" Opacity="0">
                        <Rectangle.Stroke>
                            <LinearGradientBrush EndPoint="0.055,0.119" StartPoint="0.886,0.808">
                                <GradientStop Color="#FFFCE7AF" />
                                <GradientStop Color="#FFFDDA81" Offset="1" />
                            </LinearGradientBrush>
                        </Rectangle.Stroke>
                    </Rectangle>
                    <Rectangle x:Name="BoxPress" Margin="3" Width="9" Height="9" RadiusX="0" RadiusY="0" StrokeThickness="1" Opacity="0" Stroke="{DynamicResource CheckBoxPressBorderBrush}"/>
                    <Rectangle x:Name="BoxGradient" Width="7" Height="7" RadiusX="0" RadiusY="0" StrokeThickness="1" Fill="{DynamicResource CheckBoxInnerBoxGradientBrush}"/>
                    <Rectangle x:Name="IndeterminateIcon" Width="5" Height="2" Fill="{DynamicResource GlyphBrush}" HorizontalAlignment="Center" VerticalAlignment="Center" Visibility="Collapsed" />
                    <Path x:Name="CheckIcon" Margin="0,3.333,3.833,0" Width="7" Height="9" Fill="{DynamicResource GlyphBrush}" Stretch="Fill" VerticalAlignment="Top" HorizontalAlignment="Right" Data="M103.78572,598.96112 L105.09846,597.5661 L107.00806,600.16229 C107.00806,600.16229 109.91004,592.74463 109.91004,592.74463 C109.91004,592.74463 111.74678,593.79761 111.74678,593.79761 C111.74678,593.79761 107.88566,602.75848 107.88566,602.75848 L106.60118,602.75848 z" Visibility="Collapsed" />
                    <Rectangle x:Name="FocusedVisualElement" RadiusX="0" RadiusY="0" Stroke="{DynamicResource FocusBrush}" StrokeThickness="1" Opacity="0" />
                    <Rectangle x:Name="DisabledVisualElement" Margin="1" RadiusX="0" RadiusY="0" Fill="{DynamicResource DisabledBackgroundBrush}" Visibility="Collapsed" />
                </Grid>
            </BulletDecorator.Bullet>
            <ContentPresenter Grid.Column="1" x:Name="contentPresenter" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" />
        </BulletDecorator>

        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOn}" x:Name="FocusedOn_BeginStoryboard" />
                </Trigger.EnterActions>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsChecked" Value="True" />
                    <Condition Property="IsThreeState" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.EnterActions>
                    <BeginStoryboard x:Name="CheckedOff_BeginStoryboard" Storyboard="{StaticResource CheckedOff}" />
                </MultiTrigger.EnterActions>
                <MultiTrigger.ExitActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard" Storyboard="{StaticResource CheckedOn}" />
                </MultiTrigger.ExitActions>
            </MultiTrigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsChecked" Value="{x:Null}" />
                    <Condition Property="IsThreeState" Value="True" />
                </MultiTrigger.Conditions>
                <MultiTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource IndeterminateOn}" />
                </MultiTrigger.EnterActions>
                <MultiTrigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource IndeterminateOff}" />
                </MultiTrigger.ExitActions>
                <Setter Property="Opacity" TargetName="CheckIcon" Value="0" />
            </MultiTrigger>
            <Trigger Property="IsChecked" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard2" Storyboard="{StaticResource CheckedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="CheckedOn_BeginStoryboard1" Storyboard="{StaticResource CheckedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource DisabledOn}" />
                </Trigger.EnterActions>
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
</ResourceDictionary>