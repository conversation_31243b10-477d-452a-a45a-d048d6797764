﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="TECHNICS_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="TECHNICS_MAIN" type="SiaSun.LMS.Model.TECHNICS_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="TECHNICS_MAIN">
			<result property="TECHNICS_ID" column="technics_id" />
			<result property="TECHNICS_DESCRIPTION" column="technics_description" />
			<result property="RELAVTIVE_TECHNICS_ID" column="relavtive_technics_id" />
			<result property="GOODS_CLASS_ID" column="goods_class_id" />
			<result property="PLAN_TYPE_CODE" column="plan_type_code" />
			<result property="MANAGE_TYPE_CODE" column="manage_type_code" />
			<result property="CELL_MODEL" column="cell_model" />
			<result property="START_AREA_ID" column="start_area_id" />
			<result property="END_AREA_ID" column="end_area_id" />
			<result property="START_POSITION" column="start_position" />
			<result property="END_POSITION" column="end_position" />
			<result property="SAME_LANEWAY" column="same_laneway" />
			<result property="SP_START_CELL_STATUS" column="sp_start_cell_status" />
			<result property="SP_START_RUNS_STATUS" column="sp_start_runs_status" />
			<result property="EP_START_CELL_STATUS" column="ep_start_cell_status" />
			<result property="EP_START_RUN_STATUS" column="ep_start_run_status" />
			<result property="SP_END_CELL_STATUS" column="sp_end_cell_status" />
			<result property="SP_END_RUNS_STATUS" column="sp_end_runs_status" />
			<result property="EP_END_CELL_STATUS" column="ep_end_cell_status" />
			<result property="EP_END_RUN_STATUS" column="ep_end_run_status" />
			<result property="CHECK_START_POSITION_FLAG" column="check_start_position_flag" />
			<result property="CHECK_END_POSITION_FLAG" column="check_end_position_flag" />
			<result property="START_STATUS" column="start_status" />
			<result property="FINISH_STATUS" column="finish_status" />
			<result property="HAS_STORAGE_FLAG" column="has_storage_flag" />
			<result property="AUTO_CREATE_CONTROL" column="auto_create_control" />
			<result property="CREATE_STORAGE_FLAG" column="create_storage_flag" />
			<result property="MOVE_STROAGE_FLAG" column="move_stroage_flag" />
			<result property="CANCEL_BIND_BARCODE" column="cancel_bind_barcode" />
			<result property="CANCEL_STORAGE_FLAG" column="cancel_storage_flag" />
			<result property="PLAN_CHANGE_FLAG" column="plan_change_flag" />
			<result property="STATION_SORT_FLAG" column="station_sort_flag" />
			<result property="STATION_PROJECT_FLAG" column="station_project_flag" />
			<result property="DATA_ORDER" column="data_order" />
			<result property="TECHNICS_ACTIONS" column="technics_actions" />
			<result property="TECHNICS_REMARK" column="technics_remark" />
			<result property="END_LOGIC_STATUS" column="end_logic_status" />
			<result property="END_GOODS_STATUS" column="end_goods_status" />
			<result property="CELL_INOUT" column="cell_inout" />
			<result property="CELL_CLASS" column="cell_class" />
			<result property="INIT_STORAGE_FLAG" column="init_storage_flag" />
			<result property="NEXT_STOCK_TECHNICS_ID" column="next_stock_technics_id" />
			<result property="NEXT_GOODS_TECHNICS_ID" column="next_goods_technics_id" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="TECHNICS_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  technics_id,
				  technics_description,
				  relavtive_technics_id,
				  goods_class_id,
				  plan_type_code,
				  manage_type_code,
				  cell_model,
				  start_area_id,
				  end_area_id,
				  start_position,
				  end_position,
				  same_laneway,
				  sp_start_cell_status,
				  sp_start_runs_status,
				  ep_start_cell_status,
				  ep_start_run_status,
				  sp_end_cell_status,
				  sp_end_runs_status,
				  ep_end_cell_status,
				  ep_end_run_status,
				  check_start_position_flag,
				  check_end_position_flag,
				  start_status,
				  finish_status,
				  has_storage_flag,
				  auto_create_control,
				  create_storage_flag,
				  move_stroage_flag,
				  cancel_bind_barcode,
				  cancel_storage_flag,
				  plan_change_flag,
				  station_sort_flag,
				  station_project_flag,
				  data_order,
				  technics_actions,
				  technics_remark,
				  end_logic_status,
				  end_goods_status,
				  cell_inout,
				  cell_class,
				  init_storage_flag,
				  next_stock_technics_id,
				  next_goods_technics_id
			From TECHNICS_MAIN
		</select>
		
		<select id="TECHNICS_MAIN_SELECT_BY_ID" parameterClass="int" extends = "TECHNICS_MAIN_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					technics_id=#TECHNICS_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="TECHNICS_MAIN_INSERT" parameterClass="TECHNICS_MAIN">
      Insert Into TECHNICS_MAIN (
      technics_id,
      technics_description,
      relavtive_technics_id,
      goods_class_id,
      plan_type_code,
      manage_type_code,
      cell_model,
      start_area_id,
      end_area_id,
      start_position,
      end_position,
      same_laneway,
      sp_start_cell_status,
      sp_start_runs_status,
      ep_start_cell_status,
      ep_start_run_status,
      sp_end_cell_status,
      sp_end_runs_status,
      ep_end_cell_status,
      ep_end_run_status,
      check_start_position_flag,
      check_end_position_flag,
      start_status,
      finish_status,
      has_storage_flag,
      auto_create_control,
      create_storage_flag,
      move_stroage_flag,
      cancel_bind_barcode,
      cancel_storage_flag,
      plan_change_flag,
      station_sort_flag,
      station_project_flag,
      data_order,
      technics_actions,
      technics_remark,
      end_logic_status,
      end_goods_status,
      cell_inout,
      cell_class,
      init_storage_flag,
      next_stock_technics_id,
      next_goods_technics_id
      )Values(
      #TECHNICS_ID#,
      #TECHNICS_DESCRIPTION#,
      #RELAVTIVE_TECHNICS_ID#,
      #GOODS_CLASS_ID#,
      #PLAN_TYPE_CODE#,
      #MANAGE_TYPE_CODE#,
      #CELL_MODEL#,
      #START_AREA_ID#,
      #END_AREA_ID#,
      #START_POSITION#,
      #END_POSITION#,
      #SAME_LANEWAY#,
      #SP_START_CELL_STATUS#,
      #SP_START_RUNS_STATUS#,
      #EP_START_CELL_STATUS#,
      #EP_START_RUN_STATUS#,
      #SP_END_CELL_STATUS#,
      #SP_END_RUNS_STATUS#,
      #EP_END_CELL_STATUS#,
      #EP_END_RUN_STATUS#,
      #CHECK_START_POSITION_FLAG#,
      #CHECK_END_POSITION_FLAG#,
      #START_STATUS#,
      #FINISH_STATUS#,
      #HAS_STORAGE_FLAG#,
      #AUTO_CREATE_CONTROL#,
      #CREATE_STORAGE_FLAG#,
      #MOVE_STROAGE_FLAG#,
      #CANCEL_BIND_BARCODE#,
      #CANCEL_STORAGE_FLAG#,
      #PLAN_CHANGE_FLAG#,
      #STATION_SORT_FLAG#,
      #STATION_PROJECT_FLAG#,
      #DATA_ORDER#,
      #TECHNICS_ACTIONS#,
      #TECHNICS_REMARK#,
      #END_LOGIC_STATUS#,
      #END_GOODS_STATUS#,
      #CELL_INOUT#,
      #CELL_CLASS#,
      #INIT_STORAGE_FLAG#,
      #NEXT_STOCK_TECHNICS_ID#,
      #NEXT_GOODS_TECHNICS_ID#
      )
      <!--<selectKey  resultClass="int" type="post" property="TECHNICS_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="TECHNICS_MAIN_UPDATE" parameterClass="TECHNICS_MAIN">
      Update TECHNICS_MAIN Set
      technics_id=#TECHNICS_ID#,
      technics_description=#TECHNICS_DESCRIPTION#,
      relavtive_technics_id=#RELAVTIVE_TECHNICS_ID#,
      goods_class_id=#GOODS_CLASS_ID#,
      plan_type_code=#PLAN_TYPE_CODE#,
      manage_type_code=#MANAGE_TYPE_CODE#,
      cell_model=#CELL_MODEL#,
      start_area_id=#START_AREA_ID#,
      end_area_id=#END_AREA_ID#,
      start_position=#START_POSITION#,
      end_position=#END_POSITION#,
      same_laneway=#SAME_LANEWAY#,
      sp_start_cell_status=#SP_START_CELL_STATUS#,
      sp_start_runs_status=#SP_START_RUNS_STATUS#,
      ep_start_cell_status=#EP_START_CELL_STATUS#,
      ep_start_run_status=#EP_START_RUN_STATUS#,
      sp_end_cell_status=#SP_END_CELL_STATUS#,
      sp_end_runs_status=#SP_END_RUNS_STATUS#,
      ep_end_cell_status=#EP_END_CELL_STATUS#,
      ep_end_run_status=#EP_END_RUN_STATUS#,
      check_start_position_flag=#CHECK_START_POSITION_FLAG#,
      check_end_position_flag=#CHECK_END_POSITION_FLAG#,
      start_status=#START_STATUS#,
      finish_status=#FINISH_STATUS#,
      has_storage_flag=#HAS_STORAGE_FLAG#,
      auto_create_control=#AUTO_CREATE_CONTROL#,
      create_storage_flag=#CREATE_STORAGE_FLAG#,
      move_stroage_flag=#MOVE_STROAGE_FLAG#,
      cancel_bind_barcode=#CANCEL_BIND_BARCODE#,
      cancel_storage_flag=#CANCEL_STORAGE_FLAG#,
      plan_change_flag=#PLAN_CHANGE_FLAG#,
      station_sort_flag=#STATION_SORT_FLAG#,
      station_project_flag=#STATION_PROJECT_FLAG#,
      data_order=#DATA_ORDER#,
      technics_actions=#TECHNICS_ACTIONS#,
      technics_remark=#TECHNICS_REMARK#,
      end_logic_status=#END_LOGIC_STATUS#,
      end_goods_status=#END_GOODS_STATUS#,
      cell_inout=#CELL_INOUT#,
      cell_class=#CELL_CLASS#,
      init_storage_flag=#INIT_STORAGE_FLAG#,
      next_stock_technics_id=#NEXT_STOCK_TECHNICS_ID#,
      next_goods_technics_id=#NEXT_GOODS_TECHNICS_ID#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					technics_id=#TECHNICS_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="TECHNICS_MAIN_DELETE" parameterClass="int">
			Delete From TECHNICS_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					technics_id=#TECHNICS_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>