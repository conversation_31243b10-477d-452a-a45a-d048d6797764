﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.HANDLE_EXCEPTION"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="HANDLE_EXCEPTION" Height="500" Width="1000"
                    Loaded="DocumentContent_Loaded">
    <Grid>
        <TabControl x:Name="tbcExceptionTab" >
            <TabItem x:Name="ResetPlan" Header="重置计划" >
                <Grid Background="#FFE5E5E5" >
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel  Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                        <Label Content="计划ID:"/>
                        <ComboBox x:Name="cbxPlanId"  Margin="10,0,0,0" Width="100"/>
                        <Button x:Name="btnReset" Content="重置" Margin="10,0,0,0" Width="50" Click="btnReset_Click"/>
                        <Label Margin="10,0,0,0" Content="注意:重置计划功能将改变计划的分配数量和计划状态，请在开发人员指导下使用！"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem x:Name="StorageException" Header="异常库存">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="50"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel  Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                        <Label Content="箱条码："/>
                        <TextBox x:Name="tbxStockBarcode" Width="100" Margin="10 0"/>
                        <Label Content="是否异常："/>
                        <ComboBox x:Name="cbxValue" Width="60" Margin="10 0"/>
                        <Button x:Name="btnSetStorageException" Content="设置整箱异常" Width="120" Click="btnSetStorageException_Click"/>
                    </StackPanel>

                    <StackPanel  Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Visibility="Visible">
                        <Label Content="箱条码："/>
                        <TextBox x:Name="tbxStockBarcode1" Width="100" Margin="10 0"/>
                        <Label Content="物料编码："/>
                        <TextBox x:Name="tbxGoodsCode" Width="100" Margin="10 0"/>
                        <Label Content="异常描述："/>
                        <TextBox x:Name="tbxExceptionRemark" Width="300" Margin="10 0"/>
                        <Button x:Name="btnSetStorageException1" Content="设置单格异常" Width="120" Click="btnSetStorageException1_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem x:Name="SetGoodsPriorOut" Header="设置优先出库">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel  Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                        <Label Content="箱条码："/>
                        <TextBox x:Name="tbxStockBarcodeGoodsPriorOut" Width="100" Margin="10 0"/>
                        <Label Content="物料编码："/>
                        <TextBox x:Name="tbxGoodsCodeGoodsPriorOut" Width="100" Margin="10 0"/>
                        <Label Content="优先出库："/>
                        <ComboBox x:Name="cbxValueGoodsPriorOut" Width="60" Margin="10 0"/>
                        <Button x:Name="btnSetGoodsPriorOut" Content="设置" Width="60" Click="btnSetGoodsPriorOut_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem x:Name="ConnectPickStation" Header="连接拣选工作站">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>
                    <StackPanel  Grid.Row="1" Grid.Column="1" Orientation="Horizontal">
                        <Button x:Name="btnConnectPickStation" Content="连接" Width="60" Click="btnConnectPickStation_Click"/>
                    </StackPanel>
                </Grid>
            </TabItem>

            <TabItem x:Name="ContinuousManageDown" Header="连续货位下架">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" Margin="10">
                        <TextBlock Text="排："/>
                        <ComboBox Width="100" x:Name="cbxManageDownRow"/>
                        <TextBlock Text="（范围：1-24）"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Grid.Row="2" Grid.Column="1" Margin="10">
                        <TextBlock Text="起始列："/>
                        <TextBox Width="50" x:Name="tbxManageDownColumnStart"/>
                        <TextBlock Text="终止列：" Margin="20 0 0 0"/>
                        <TextBox Width="50" x:Name="tbxManageDownColumnEnd"/>
                        <TextBlock Text="（范围：2-85，起始列要小于终止列）"/>
                    </StackPanel>
                    <StackPanel  Grid.Row="3" Grid.Column="1" Orientation="Horizontal" Margin="10">
                        <TextBlock Text="下架位置："/>
                        <ComboBox x:Name="cbxStationName" Width="200"/>
                    </StackPanel>
                    <StackPanel  Grid.Row="4" Grid.Column="1" Orientation="Horizontal" Margin="10" HorizontalAlignment="Left">
                        <CheckBox x:Name="cbxIsContainKitandenpty"/>
                        <TextBlock Text="选中将不包含齐套箱和空箱"/>
                    </StackPanel>
                    <Button x:Name="btnContinusManageDown" Content="将所选排的起始列到终止列货架所包含的物料下架" 
                            Grid.Row="5" Grid.Column="1" Margin="10" Click="btnContinusManageDown_Click"/>

                </Grid>
            </TabItem>

            <TabItem x:Name="RunSQLCommand" Header="执行SQL语句">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" Margin="10">
                        <TextBlock Text="请输入准许码："/>
                        <TextBox Width="50" x:Name="tbxAllowCode"/>
                        <TextBlock Text="此功能需在开发人员帮助下使用！" Margin="20 0 0 0"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Grid.Row="2" Grid.Column="1" Margin="10">
                        <TextBlock Text="请输入SQL命令："/>
                        <TextBox x:Name="tbxSqlCommandText" TextWrapping="Wrap"  AcceptsReturn="True"
                                 VerticalScrollBarVisibility="Visible" Width="600" Height="100" />
                    </StackPanel>

                    <Button x:Name="btnRunSqlCommand" Content="开始执行（请校准输入，任何字符的错误可能引起数据库混乱）" 
                            Grid.Row="3" Grid.Column="1" Margin="10" Click="btnRunSqlCommand_Click"/>
                </Grid>
            </TabItem>
            
            <TabItem x:Name="UnlockPickExceptionStorage" Header="拣选异常原料箱解锁">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="Auto"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" Margin="10">
                        <Label Content="箱条码：" Margin="10"/>
                        <TextBox x:Name="tbxUnlockBoxBarcode"  Width="100" Margin="10"/>
                        <Label Content="      物料条码："/>
                        <TextBox x:Name="tbxUnlockGoodsBarcode"  Width="120" Margin="10"/>

                        <Button x:Name="btnUnlockExceptionStorage" Content="确定" Width="80" Margin="10" Click="btnUnlockExceptionStorage_Click"/>

                    </StackPanel>

                </Grid>
            </TabItem>
            
            <TabItem x:Name="ArrangeStationWorkMode" Header="合箱工位工作模式">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" Margin="10" Grid.ColumnSpan="2">
                        <Label Content="一期整理工位21098工作模式：" Margin="10"/>
                        <RadioButton Content="紧急出库模式" x:Name="rbtn21098Emerg"  Margin="10"/>
                        <RadioButton Content="合箱模式[常规整理]" x:Name="rbtn21098Arrange"  Margin="10"/>
                        <RadioButton Content="整理不满箱模式[非极端情况不建议使用]" x:Name="rbtn21098ArrangeGoods"  Margin="10"/>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal" Grid.Row="2" Grid.Column="1" Margin="10" Grid.ColumnSpan="2">
                        <Label Content="二期整理工位22141工作模式：" Margin="10"/>
                        <RadioButton Content="紧急出库模式" x:Name="rbtn22141Emerg"  Margin="10"/>
                        <RadioButton Content="合箱模式[常规整理]" x:Name="rbtn22141Arrange"  Margin="10"/>
                        <RadioButton Content="整理不满箱模式[非极端情况不建议使用]" x:Name="rbtn22141ArrangeGoods"  Margin="10"/>
                    </StackPanel>
                    <Button Content="保存" Grid.Row="3" Grid.Column="1" Width="150" Margin="10" HorizontalAlignment="Left" 
                            x:Name="btnArrangeSattionWorkMode" Click="BtnArrangeSattionWorkMode_Click"/>

                </Grid>
            </TabItem>

            <TabItem x:Name="DeleteStorageMain" Header="删除残留库存">
                <Grid Background="#FFE5E5E5">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="20"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                        <RowDefinition Height="Auto"/>
                    </Grid.RowDefinitions>
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="20"/>
                        <ColumnDefinition Width="1*"/>
                    </Grid.ColumnDefinitions>

                    <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" Margin="10" Grid.ColumnSpan="2">
                        <Label Content="容器条码：" Margin="10"/>
                        <TextBox x:Name="tbxStockBarcodeToDelet" Width="150"/>
                    </StackPanel>
                    <Button Content="删除" Grid.Row="3" Grid.Column="1" Width="150" Margin="10" HorizontalAlignment="Left" 
                            x:Name="btnDeleteStorageMain" Click="btnDeleteStorageMain_Click"/>

                </Grid>
            </TabItem>


        </TabControl>
    </Grid>
</ad:DocumentContent>
