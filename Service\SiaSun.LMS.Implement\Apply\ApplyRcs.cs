﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 五楼申请：RCS车将托盘放到输送线后申请
    /// </summary>
    public class ApplyRcs : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyRcs.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                var manageMain = this.GetList($"select MANAGE_ID from MANAGE_MAIN where BACKUP_FIELD1 = {mIO_CONTROL_APPLY.CONTROL_APPLY_PARA01}");
                if(manageMain == null || manageMain.Rows.Count !=1)
                {
                    bResult = false;
                    sResult = string.Format("未找到管理任务信息或者信息不唯一_RCS任务号[{0}]", mIO_CONTROL_APPLY.CONTROL_APPLY_PARA01);
                    return bResult;
                }

                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(int.Parse(manageMain.Rows[0]["MANAGE_ID"].ToString()));
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到ID为[{0}]的管理任务", manageMain.Rows[0]["MANAGE_ID"].ToString());
                    return bResult;
                }
                if(mIO_CONTROL_APPLY.STOCK_BARCODE != mMANAGE_MAIN.STOCK_BARCODE)
                {
                    bResult = false;
                    sResult = string.Format("任务条码有误_申请上报条码[{0}]_管理任务条码[{1}]", mIO_CONTROL_APPLY.STOCK_BARCODE, mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }
                if(mMANAGE_MAIN.MANAGE_STATUS != Enum.MANAGE_STATUS.WaitingExecute.ToString())
                {
                    bResult = false;
                    sResult = string.Format("任务状态[{0}]不是等待执行", mMANAGE_MAIN.MANAGE_STATUS);
                    return bResult;
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                Model.MANAGE_LIST mMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID)[0];

                //非立库输送任务下达关联管理任务的Control任务
                bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoadTrans", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), false }, out sResult);

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_Apply1", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateFloor.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
