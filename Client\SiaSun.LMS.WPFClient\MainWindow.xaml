﻿<f:RibbonWindow x:Class="SiaSun.LMS.WPFClient.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:f="clr-namespace:Fluent;assembly=Fluent"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="MainWindow" Height="400" Width="800" WindowState="Maximized"
        IsIconVisible="False" Icon="/SiaSun.LMS.WPFClient;component/@Images/SiasunWMS.ico">
    <f:RibbonWindow.Resources>
        <LinearGradientBrush x:Key="ButtonHoverOuterBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
            <GradientStop Color="#FFF1CA58" Offset="0"/>
            <GradientStop Color="#FFF4D549" Offset="1"/>
            <GradientStop Color="#FFF2CF50" Offset="0.75"/>
        </LinearGradientBrush>
        <LinearGradientBrush x:Key="ButtonHoverInnerBackgroundBrush" EndPoint="0.5,1" StartPoint="0.5,0">
            <GradientStop Color="#FFFDEEB3" Offset="0"/>
            <GradientStop Color="#FFFBF2CD" Offset="1"/>
            <GradientStop Color="#FFFCE48A" Offset="0.5"/>
            <GradientStop Color="#FFFBE388" Offset="0.75"/>
        </LinearGradientBrush>
        <LinearGradientBrush x:Key="ButtonHoverInnerBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
            <GradientStop Color="#FFFDF9E8" Offset="0"/>
            <GradientStop Color="#FFFDFDEB" Offset="1"/>
            <GradientStop Color="#FFFDF4D7" Offset="0.75"/>
        </LinearGradientBrush>

        <SolidColorBrush x:Key="ButtonPressedOuterBackgroundBrush" Color="#FFDCA351"/>

        <Style TargetType="{x:Type ad:DocumentPane}">
            <Setter Property="FocusVisualStyle" Value="{x:Null}"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="{x:Type ad:DocumentPane}">
                        <ControlTemplate.Resources>
                            <ContextMenu x:Key="DocumentsListMenu" StaysOpen="True"/>
                        </ControlTemplate.Resources>
                        <Border Focusable="False">
                            <Grid>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="5"/>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="*"/>
                                </Grid.RowDefinitions>
                                <Grid x:Name="extGrid" Margin="0,-1,0,0" Grid.Row="2">
                                    <Border x:Name="intBorder2" BorderBrush="#FF8A9199" BorderThickness="0,1,0,0" Focusable="False">
                                        <ContentPresenter x:Name="contentPresenter" Content="{Binding SelectedItem.Content, RelativeSource={RelativeSource TemplatedParent}}" KeyboardNavigation.DirectionalNavigation="Contained" Margin="2" KeyboardNavigation.TabNavigation="Local"/>
                                    </Border>
                                </Grid>
                                <Border x:Name="PART_Header" BorderThickness="1,1,1,0" Focusable="False" Height="20" Grid.Row="1">
                                    <DockPanel LastChildFill="True">
                                        <Button x:Name="btnClose" CommandTarget="{TemplateBinding SelectedItem}" Command="ad:ManagedContentCommands.Close" DockPanel.Dock="Right" Height="18" Margin="2,0" Opacity="0.6" ToolTip="Close" VerticalAlignment="Center" Width="18">
                                            <Button.Style>
                                                <Style TargetType="{x:Type Button}">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="{x:Type Button}">
                                                                <Border x:Name="border" BorderThickness="1" CornerRadius="2" Height="Auto" HorizontalAlignment="Left" VerticalAlignment="Stretch">
                                                                    <Border x:Name="border1" Height="Auto" BorderThickness="1" CornerRadius="2">
                                                                        <ContentPresenter ContentTemplate="{TemplateBinding ContentTemplate}" Content="{TemplateBinding Content}" ContentStringFormat="{TemplateBinding ContentStringFormat}"/>
                                                                    </Border>
                                                                </Border>
                                                                <ControlTemplate.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="BorderBrush" TargetName="border1" Value="{DynamicResource ButtonHoverInnerBorderBrush}"/>
                                                                        <Setter Property="Background" TargetName="border1" Value="{DynamicResource ButtonHoverInnerBackgroundBrush}"/>
                                                                        <Setter Property="Background" TargetName="border" Value="{DynamicResource ButtonHoverOuterBackgroundBrush}"/>
                                                                        <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource ButtonHoverOuterBorderBrush}"/>
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </Button.Style>
                                            <Image Height="13" Stretch="None" Width="13">
                                                <Image.Source>
                                                    <BitmapImage UriSource="pack://application:,,,/AvalonDock;component/resources/Images/PinClose.png"/>
                                                </Image.Source>
                                            </Image>
                                        </Button>
                                        <Button x:Name="PART_ShowContextMenuButton" DockPanel.Dock="Right" Height="18" Width="18">
                                            <Button.Style>
                                                <Style TargetType="{x:Type Button}">
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="{x:Type Button}">
                                                                <Border x:Name="border" BorderThickness="1" CornerRadius="2" Height="Auto" HorizontalAlignment="Left" VerticalAlignment="Stretch">
                                                                    <Border x:Name="border1" Height="Auto" BorderThickness="1" CornerRadius="2">
                                                                        <ContentPresenter ContentTemplate="{TemplateBinding ContentTemplate}" Content="{TemplateBinding Content}" ContentStringFormat="{TemplateBinding ContentStringFormat}"/>
                                                                    </Border>
                                                                </Border>
                                                                <ControlTemplate.Triggers>
                                                                    <Trigger Property="IsMouseOver" Value="True">
                                                                        <Setter Property="BorderBrush" TargetName="border1" Value="{DynamicResource ButtonHoverInnerBorderBrush}"/>
                                                                        <Setter Property="Background" TargetName="border1" Value="{DynamicResource ButtonHoverInnerBackgroundBrush}"/>
                                                                        <Setter Property="Background" TargetName="border" Value="{DynamicResource ButtonHoverOuterBackgroundBrush}"/>
                                                                        <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource ButtonHoverOuterBorderBrush}"/>
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </Button.Style>
                                            <ad:ImageEx x:Name="ShowContextMenuIcon" Width="13">
                                                <ad:ImageEx.Source>
                                                    <BitmapImage UriSource="pack://application:,,,/AvalonDock;component/resources/Images/PinMenu.png"/>
                                                </ad:ImageEx.Source>
                                            </ad:ImageEx>
                                        </Button>
                                        <ad:DocumentTabPanel x:Name="paneTabsPanel" IsItemsHost="True" Panel.ZIndex="1">
                                            <ad:DocumentTabPanel.TabItemStyle>
                                                <Style TargetType="{x:Type ad:ManagedContent}">
                                                    <Setter Property="Background" Value="Transparent"/>
                                                    <Setter Property="Template">
                                                        <Setter.Value>
                                                            <ControlTemplate TargetType="{x:Type ad:ManagedContent}">
                                                                <Border x:Name="PART_DragArea" BorderBrush="#FF8A9199" Margin="0,0,-10,0" SnapsToDevicePixels="True">
                                                                    <Grid>
                                                                        <Grid.ColumnDefinitions>
                                                                            <ColumnDefinition Width="20"/>
                                                                            <ColumnDefinition Width="*"/>
                                                                        </Grid.ColumnDefinitions>
                                                                        <Image Source="{TemplateBinding Icon}" Width="0"/>
                                                                        <Path x:Name="tabItemIntPathBackground" Data="M20,0.5Q16,0.5 10,10 5,19.5 0,19.5L20,19.5">
                                                                            <Path.Fill>
                                                                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                                                                    <GradientStop Color="#FFF0F4F8" />
                                                                                    <GradientStop Color="#FFD7DBE1" Offset="0.8" />
                                                                                    <GradientStop Color="#FF8A9199" Offset="1" />
                                                                                </LinearGradientBrush>
                                                                            </Path.Fill>
                                                                        </Path>
                                                                        <Path x:Name="tabItemIntPathBorder" Data="M20,0.5Q16,0.5 10,10 5,19.5 0,19.5" Stroke="#FF8A9199" />
                                                                        <Border x:Name="tabItemIntBorder" BorderBrush="#FF8A9199" BorderThickness="0,1,1,0" Grid.Column="1" CornerRadius="0,1,0,0" Margin="-0.5,0,0,0">
                                                                            <Border.Background>
                                                                                <LinearGradientBrush StartPoint="0.5,0" EndPoint="0.5,1">
                                                                                    <GradientStop Color="#FFF0F4F8" />
                                                                                    <GradientStop Color="#FFD7DBE1" Offset="0.8" />
                                                                                    <GradientStop Color="#FF8A9199" Offset="1" />
                                                                                </LinearGradientBrush>
                                                                            </Border.Background>
                                                                            <StackPanel Margin="5,0" Orientation="Horizontal">
                                                                                <TextBlock x:Name="tabItemTitle" TextWrapping="NoWrap" Text="{Binding Title, RelativeSource={RelativeSource TemplatedParent}, StringFormat='\{0\} '}" TextTrimming="CharacterEllipsis"/>
                                                                                <ad:ImageEx x:Name="PART_LockedIcon" Margin="2,0,0,0" Visibility="Collapsed" VerticalAlignment="Center" Width="6">
                                                                                    <ad:ImageEx.Source>
                                                                                        <BitmapImage UriSource="pack://application:,,,/AvalonDock;component/resources/Images/Locked.png"/>
                                                                                    </ad:ImageEx.Source>
                                                                                </ad:ImageEx>
                                                                                <Button x:Name="PART_CloseButton" Command="ad:ManagedContentCommands.Close" Margin="6,2,2,2" Opacity="0.8" ToolTip="Close" Visibility="Hidden">
                                                                                    <Button.Style>
                                                                                        <Style TargetType="{x:Type Button}">
                                                                                            <Setter Property="Template">
                                                                                                <Setter.Value>
                                                                                                    <ControlTemplate TargetType="{x:Type Button}">
                                                                                                        <Border x:Name="border" BorderThickness="1" CornerRadius="2" Height="Auto">
                                                                                                            <Border x:Name="border1" Height="Auto" BorderThickness="1" CornerRadius="2">
                                                                                                                <ContentPresenter HorizontalAlignment="Center" VerticalAlignment="Center" ContentTemplate="{TemplateBinding ContentTemplate}" Content="{TemplateBinding Content}" ContentStringFormat="{TemplateBinding ContentStringFormat}"/>
                                                                                                            </Border>
                                                                                                        </Border>
                                                                                                        <ControlTemplate.Triggers>
                                                                                                            <Trigger Property="IsMouseOver" Value="True">
                                                                                                                <Setter Property="BorderBrush" TargetName="border1" Value="{DynamicResource ButtonHoverInnerBorderBrush}"/>
                                                                                                                <Setter Property="Background" TargetName="border1" Value="{DynamicResource ButtonHoverInnerBackgroundBrush}"/>
                                                                                                                <Setter Property="Background" TargetName="border" Value="{DynamicResource ButtonHoverOuterBackgroundBrush}"/>
                                                                                                                <Setter Property="BorderBrush" TargetName="border" Value="{DynamicResource ButtonHoverOuterBorderBrush}"/>
                                                                                                            </Trigger>
                                                                                                        </ControlTemplate.Triggers>
                                                                                                    </ControlTemplate>
                                                                                                </Setter.Value>
                                                                                            </Setter>
                                                                                        </Style>
                                                                                    </Button.Style>
                                                                                    <ad:ImageEx HorizontalAlignment="Center" VerticalAlignment="Center" Width="10" Height="10">
                                                                                        <ad:ImageEx.Source>
                                                                                            <BitmapImage UriSource="pack://application:,,,/AvalonDock;component/resources/Images/PinClose.png"/>
                                                                                        </ad:ImageEx.Source>
                                                                                    </ad:ImageEx>
                                                                                </Button>
                                                                            </StackPanel>
                                                                        </Border>
                                                                    </Grid>
                                                                </Border>
                                                                <ControlTemplate.Triggers>
                                                                    <Trigger Property="Selector.IsSelected" Value="True">
                                                                        <Setter Property="Background" TargetName="tabItemIntBorder" Value="White"/>
                                                                        <Setter Property="Fill" TargetName="tabItemIntPathBackground" Value="White"/>
                                                                        <Setter Property="Visibility" TargetName="PART_CloseButton" Value="Visible"/>
                                                                    </Trigger>
                                                                    <DataTrigger Binding="{Binding IsActiveDocument, RelativeSource={RelativeSource Self}}" Value="True">
                                                                        <Setter Property="FontWeight" TargetName="tabItemTitle" Value="Bold"/>
                                                                    </DataTrigger>
                                                                    <MultiTrigger>
                                                                        <MultiTrigger.Conditions>
                                                                            <Condition Property="IsMouseOver" SourceName="tabItemIntBorder" Value="True"/>
                                                                            <Condition Property="Selector.IsSelected" Value="False"/>
                                                                        </MultiTrigger.Conditions>
                                                                        <Setter Property="Visibility" TargetName="PART_CloseButton" Value="Visible"/>
                                                                    </MultiTrigger>
                                                                    <Trigger Property="IsLocked" Value="True">
                                                                        <Setter Property="Visibility" TargetName="PART_LockedIcon" Value="Visible"/>
                                                                    </Trigger>
                                                                </ControlTemplate.Triggers>
                                                            </ControlTemplate>
                                                        </Setter.Value>
                                                    </Setter>
                                                </Style>
                                            </ad:DocumentTabPanel.TabItemStyle>
                                        </ad:DocumentTabPanel>
                                    </DockPanel>
                                </Border>
                            </Grid>
                        </Border>
                        <ControlTemplate.Triggers>
                            <Trigger Property="ShowHeader" Value="False">
                                <Setter Property="Visibility" TargetName="PART_Header" Value="Collapsed"/>
                                <Setter Property="BorderThickness" TargetName="intBorder2" Value="0"/>
                                <Setter Property="Margin" TargetName="intBorder2" Value="0"/>
                                <Setter Property="Margin" TargetName="contentPresenter" Value="0"/>
                                <Setter Property="Margin" TargetName="extGrid" Value="0"/>
                            </Trigger>
                            <Trigger Property="HasItems" Value="False">
                                <Setter Property="Visibility" Value="Hidden"/>
                            </Trigger>
                            <DataTrigger Binding="{Binding IsMainDocumentPane, RelativeSource={RelativeSource Self}}" Value="True">
                                <Setter Property="Source" TargetName="ShowContextMenuIcon">
                                    <Setter.Value>
                                        <BitmapImage UriSource="pack://application:,,,/AvalonDock;component/resources/Images/PinDocMenu.png"/>
                                    </Setter.Value>
                                </Setter>
                            </DataTrigger>
                            <EventTrigger RoutedEvent="FrameworkElement.Loaded">
                                <BeginStoryboard>
                                    <Storyboard>
                                        <DoubleAnimation Duration="0:0:0.2" From="0" To="1" Storyboard.TargetProperty="Opacity"/>
                                    </Storyboard>
                                </BeginStoryboard>
                            </EventTrigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </f:RibbonWindow.Resources>
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
            <RowDefinition Height="Auto" />
        </Grid.RowDefinitions>
        <f:Ribbon x:Name="ribbon">
            <f:Ribbon.QuickAccessItems>
            </f:Ribbon.QuickAccessItems>
        </f:Ribbon>
        <ad:DockingManager x:Name="DockManager" IsAnimationEnabled="True" Grid.Row="1" Background="Transparent" Loaded="DockManager_Loaded">
            <ad:ResizingPanel>
                <ad:DocumentPane x:Name="DocumentHost" Background="Transparent" />
            </ad:ResizingPanel>
        </ad:DockingManager>
        <f:StatusBar Grid.Row="2">
            <f:StatusBarItem Name="sbitemNote" HorizontalAlignment="Left" />
        </f:StatusBar>
    </Grid>
</f:RibbonWindow>
