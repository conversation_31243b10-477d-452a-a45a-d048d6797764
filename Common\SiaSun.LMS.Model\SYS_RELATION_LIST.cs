﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_RELATION_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_RELATION_LIST
	{
		public SYS_RELATION_LIST()
		{
			
		}
		
		private int _relation_list_id;
		private int _relation_id;
		private int _relation_list_flag;
		private int _relation_id1;
		private int _relation_id2;
		private string _relation_list_remark;
		
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int RELATION_LIST_ID
		{
			get{return _relation_list_id;}
			set{_relation_list_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int RELATION_ID
		{
			get{return _relation_id;}
			set{_relation_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int RELATION_LIST_FLAG
		{
			get{return _relation_list_flag;}
			set{_relation_list_flag = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int RELATION_ID1
		{
			get{return _relation_id1;}
			set{_relation_id1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int RELATION_ID2
		{
			get{return _relation_id2;}
			set{_relation_id2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string RELATION_LIST_REMARK
		{
			get{return _relation_list_remark;}
			set{_relation_list_remark = value;}
		}
	}
}
