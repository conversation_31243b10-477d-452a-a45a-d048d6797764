﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_TEMPLATE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_TEMPLATE
	{
        public GOODS_TEMPLATE()
		{
			
		}
		
		private int _goods_template_id;
		private string _goods_template_code;
		private string _goods_template_name;
        private int _goods_id;
		private string _goods_template_remark;
		private int _goods_template_order;
		private string _goods_template_flag;
		
		///<sumary>
		/// 物料模板编号
        ///</sumary>
        [DataMember]
		public int GOODS_TEMPLATE_ID
		{
			get{return _goods_template_id;}
			set{_goods_template_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string GOODS_TEMPLATE_CODE
		{
			get{return _goods_template_code;}
			set{_goods_template_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string GOODS_TEMPLATE_NAME
		{
			get{return _goods_template_name;}
			set{_goods_template_name = value;}
		}

        [DataMember]
        public int GOODS_ID
        {
            get { return _goods_id; }
            set { _goods_id = value; }
        }

		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string GOODS_TEMPLATE_REMARK
		{
			get{return _goods_template_remark;}
			set{_goods_template_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int GOODS_TEMPLATE_ORDER
		{
			get{return _goods_template_order;}
			set{_goods_template_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string GOODS_TEMPLATE_FLAG
		{
			get{return _goods_template_flag;}
			set{_goods_template_flag = value;}
		}
	}
}
