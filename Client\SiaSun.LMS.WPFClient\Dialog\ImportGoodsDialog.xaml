﻿<Window x:Class="SiaSun.LMS.WPFClient.Dialog.ImportGoodsDialog"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="Excel文件导入" MaxWidth="400"  SizeToContent="WidthAndHeight" WindowStyle="None" WindowStartupLocation="CenterScreen" Loaded="Window_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <uc:ucWindowTopHeader x:Name="ucWinHeader" Grid.Row="0"></uc:ucWindowTopHeader>
        <WrapPanel Grid.Row="1" Margin="5,10,5,5">
            <StackPanel Orientation="Horizontal">
                <TextBlock Name="lblTemplateName" VerticalAlignment="Center" Foreground="White">模板名称：</TextBlock>
                <ComboBox Name="cmbTemplateName"  Width="250" MinHeight="23"></ComboBox>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <TextBlock Name="lblFileName" VerticalAlignment="Center" Foreground="White">导入文件：</TextBlock>
                <TextBox Name="txtFileName" Width="250"  IsReadOnly="True"></TextBox>
                <Button Name="btnOpen"  Click="WrapPanel_Click">浏览</Button>
            </StackPanel>
        </WrapPanel>
        <WrapPanel Grid.Row="2" HorizontalAlignment="Center" Margin="5" ButtonBase.Click="WrapPanel_Click">
            <Button Name="btnImport" Margin="5">开始导入</Button>
            <Button Name="btnClose"  Margin="5" Width="60">关闭</Button>
        </WrapPanel>
    </Grid>
</Window>
