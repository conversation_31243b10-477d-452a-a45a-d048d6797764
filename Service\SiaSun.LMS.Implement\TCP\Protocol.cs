﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.TCP
{
    /// <summary>
    /// Protocol
    /// done
    /// </summary>
    public partial class Protocol
    {
        /// <summary>
        /// short转为byte[2]
        /// done
        /// </summary>
        private byte[] ShortToBytes(short value)
        {
            return new byte[2]
            {
                (byte)(value >> 8),
                (byte)(value & 255)
            };
        }

        /// <summary>
        /// int转为byte[4]
        /// done
        /// </summary>
        private byte[] IntToBytes(int value)
        {
            return new byte[4]
            {
                (byte)(value >> 24),
                (byte)((value >> 16) & 255),
                (byte)((value >> 8) & 255),
                (byte)(value & 255)
            };
        }

        /// <summary>
        /// string转为byte[10]
        /// done
        /// </summary>
        private byte[] StringToBytes(string value)
        {
            if (value.Length > 10)
            {
                throw new IndexOutOfRangeException();
            }
            byte[] bytes = new byte[10];
            Encoding.ASCII.GetBytes(value, 0, value.Length, bytes, 0);
            return bytes;
        }

        /// <summary>
        /// bool[]转为byte[2]
        /// done
        /// </summary>
        private byte[] BooleanArrayToBytes(bool[] value)
        {
            if (value.Length > 8)
            {
                throw new IndexOutOfRangeException();
            }
            short bytes = 0;
            for (int i = 0; i < value.Length; i++)
            {
                bytes += (short)(Convert.ToByte(value[i]) << (16 - (i + 1)));
            }
            return this.ShortToBytes(bytes);
        }

        /// <summary>
        /// byte[2]转为short
        /// done
        /// </summary>
        private short BytesToShort(params byte[] value)
        {
            if (value.Length != 2)
            {
                throw new IndexOutOfRangeException();
            }
            return (short)((value[0] << 8) + value[1]);
        }

        /// <summary>
        /// byte[4]转为int
        /// done
        /// </summary>
        private int BytesToInt(params byte[] value)
        {
            if (value.Length != 4)
            {
                throw new IndexOutOfRangeException();
            }
            return (value[0] << 24) + (value[1] << 16) + (value[2] << 8) + value[3];
        }

        /// <summary>
        /// byte[10]转为string
        /// done
        /// </summary>
        private string BytesToString(params byte[] value)
        {
            if (value.Length > 10)
            {
                throw new IndexOutOfRangeException();
            }
            return Encoding.ASCII.GetString(value);
        }

        /// <summary>
        /// byte[2]转为bool[]
        /// done
        /// </summary>
        private bool[] BytesToBooleanArray(params byte[] value)
        {
            if (value.Length > 8)
            {
                throw new IndexOutOfRangeException();
            }
            short bytes = this.BytesToShort(value);
            List<bool> list = new List<bool>();
            for (int i = 16; i > 0; i--)
            {
                list.Add(Convert.ToBoolean(bytes >> (i - 1)));
            }
            return list.ToArray();
        }

        /// <summary>
        /// 加校验和
        /// done
        /// </summary>
        private byte[] Check(IList<byte> value)
        {
            short sum = 0;
            //foreach (byte item in value) 
            //{
            //    sum += item; //校验和为 从第2个字节到第25个字节的累加和  ?此处需要更改？？
            //}
            for(int i=2;i<value.Count;i++)
            {
                sum += value[i]; //校验和为 从第2个字节到第25个字节的累加和  ?此处需要更改？？
            }
            byte[] bytes = this.ShortToBytes(sum);
            value.Add(bytes[0]); //26
            value.Add(bytes[1]); //27
            value.Add(0xfd);  //28
            value.Add(0xfd);  //29
            return value.ToArray();
        }

        /// <summary>
        /// 和校验
        /// done
        /// 校验和
        /// check
        /// </summary>
        private bool Check(byte[] value)
        {
            short sum = 0;
            for (int i = 2; i < value.Length - 4; i++)// 表头不算 从第2个字节开始 第0、1字节为协议头
            {
                sum += value[i];
            }
            return sum == this.BytesToShort(value[value.Length - 4], value[value.Length - 3]);
        }
    }

    /// <summary>
    /// ProtocolEventArgs
    /// done
    /// </summary>
    public class ProtocolEventArgs : EventArgs
    {
        public ProtocolEventArgs(object[] data)
        {
            Data = data;
        }
        public object[] Data { get; set; }
    }

    /// <summary>
    /// ProtocolEventHandler
    /// done
    /// </summary>
    /// <param name="e">ProtocolEventArgs</param>
    public delegate void ProtocolEventHandler(ProtocolEventArgs e);
}
