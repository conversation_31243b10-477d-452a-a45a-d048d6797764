﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// TECHINCS_ROUTE_CONFIG.xaml 的交互逻辑
    /// </summary>
    public partial class TECHINCS_ROUTE_CONFIG : AvalonDock.DocumentContent
    {
        public TECHINCS_ROUTE_CONFIG()
        {
            InitializeComponent();

            this.tvwBusinessType.U_ItemSelectedChanged+=new UC.ucTreeView.U_ItemSelectedChangedHandler(tvwBusinessType_U_ItemSelectedChanged);
            this.gridTechnics.gridApp.SelectionChanged += new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.LoadBussinessType();
        }

        /// <summary>
        /// 加载输送任务类型列表
        /// </summary>
        private void LoadBussinessType()
        {
            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //加载输送任务类型
                this.tvwBusinessType.U_Header = "输送任务类型列表";
                this.tvwBusinessType.U_AllowCheck = false;
                this.tvwBusinessType.U_LoadTreeViewItems(null, "PLAN_TYPE", "PLAN_TYPE_NAME", "PLAN_TYPE_CODE", "", "PLAN_TYPE_ORDER");
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 点击输送任务类型节点，显示流程列表
        /// </summary>
        void tvwBusinessType_U_ItemSelectedChanged(TreeViewItem ItemSelected)
        {
            if (ItemSelected != null)
            {
                this.grpbTechnics.Header = string.Format(this.grpbTechnics.Tag.ToString(), ItemSelected.Header.ToString());
                this.TechnicsBind(ItemSelected.Tag.ToString());
            }
        }

        /// <summary>
        /// 输送流程绑定
        /// </summary>
        private void TechnicsBind(string PLAN_TYPE_CODE)
        {    
            try
            {
                //设置属性
                this.gridTechnics.U_WindowName = "TECHNICS_MAIN";
                this.gridTechnics.U_TableName = "TECHNICS_MAIN";
                //this.gridTechnics.U_Where = string.Format("PLAN_TYPE_CODE='{0}'", PLAN_TYPE_CODE);
                this.gridTechnics.U_OrderField = "TECHNICS_ID";

                this.gridTechnics.U_AllowPage = false;
                this.gridTechnics.U_AllowChecked = false;
                this.gridTechnics.U_AllowOperatData = true;

                //设置默认值
                //Dictionary<string, string> dicDefault = new Dictionary<string, string>();
                //dicDefault.Add("PLAN_TYPE_CODE", PLAN_TYPE_CODE);
                //this.gridTechnics.U_DefaultRowValues = dicDefault;


                //初始化控件
                this.gridTechnics.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //点击流程，显示路线
        void gridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            DataGrid grid = e.Source as DataGrid;
            if (grid != null)
            {
                if (grid.SelectedItem == null)
                {
                    this.gridRoute.U_DataSource = null;
                    return;
                }
                else
                {
                    DataRowView rowViewTechnics = grid.SelectedItem as DataRowView;
                    if (rowViewTechnics != null && !rowViewTechnics.Row.IsNull("TECHNICS_ID"))
                    {
                        this.grpbRoute.Header = string.Format(this.grpbRoute.Tag.ToString(), rowViewTechnics["TECHNICS_DESCRIPTION"].ToString());
                        this.RouteBind(Convert.ToInt32(rowViewTechnics["TECHNICS_ID"]));
                    }
                }
            }
        }

        /// <summary>
        /// 显示输送流程路线列表
        /// </summary>
        private void RouteBind(int TECHNICS_ID)
        {
            try
            {
                this.gridRoute.U_TableName = "TECHNICS_ROUTE";
                this.gridRoute.U_WindowName = "TECHNICS_ROUTE";
                this.gridRoute.U_Where = string.Format("TECHNICS_ID={0}", TECHNICS_ID);
                this.gridRoute.U_OrderField = "ROUTE_ORDER";

                this.gridRoute.U_AllowPage = false;
                this.gridRoute.U_AllowChecked = false;
                this.gridRoute.U_AllowOperatData = true;

                //设置默认值
                Dictionary<string, string> dicDefault = new Dictionary<string, string>();
                dicDefault.Add("TECHNICS_ID", TECHNICS_ID.ToString());
                this.gridRoute.U_DefaultRowValues = dicDefault;

                //初始化控件
                this.gridRoute.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //上下文菜单
        private void ContextMenu_Click(object sender, RoutedEventArgs e)
        {
            MenuItem menuItem = e.OriginalSource as MenuItem;
            if (menuItem != null)
            {
                switch (menuItem.Name)
                {
                    case "menuItemRefresh":
                        this.LoadBussinessType();
                        break;
                }
            }
        }
    }
}
