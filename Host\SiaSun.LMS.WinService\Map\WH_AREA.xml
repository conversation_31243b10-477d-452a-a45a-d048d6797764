﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="WH_AREA" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="WH_AREA" type="SiaSun.LMS.Model.WH_AREA, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="WH_AREA">
			<result property="AREA_ID" column="area_id" />
			<result property="WAREHOUSE_ID" column="warehouse_id" />
			<result property="AREA_TYPE" column="area_type" />
			<result property="AREA_CODE" column="area_code" />
			<result property="AREA_NAME" column="area_name" />
			<result property="AREA_ORDER" column="area_order" />
			<result property="AREA_FLAG" column="area_flag" />
			<result property="AREA_REMARK" column="area_remark" />
			<result property="AREA_GROUP" column="area_group" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="WH_AREA_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  area_id,
				  warehouse_id,
				  area_type,
				  area_code,
				  area_name,
				  area_order,
				  area_flag,
				  area_remark,
				  area_group
			From WH_AREA
		</select>
		
		<select id="WH_AREA_SELECT_BY_ID" parameterClass="int" extends = "WH_AREA_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					area_id=#AREA_ID# 
				</isParameterPresent>
			</dynamic>
		</select>


    <select id="WH_AREA_SELECT_BY_AREA_TYPE" parameterClass="string" extends = "WH_AREA_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          area_type=#AREA_TYPE#
        </isParameterPresent>
      </dynamic>
    </select>

				
		<insert id="WH_AREA_INSERT" parameterClass="WH_AREA">
      Insert Into WH_AREA (
      area_id,
      warehouse_id,
      area_type,
      area_code,
      area_name,
      area_order,
      area_flag,
      area_remark,
      area_group
      )Values(
      #AREA_ID#,
      #WAREHOUSE_ID#,
      #AREA_TYPE#,
      #AREA_CODE#,
      #AREA_NAME#,
      #AREA_ORDER#,
      #AREA_FLAG#,
      #AREA_REMARK#,
      #AREA_GROUP#
      )
      <!--<selectKey  resultClass="int" type="post" property="AREA_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="WH_AREA_UPDATE" parameterClass="WH_AREA">
      Update WH_AREA Set
      area_id=#AREA_ID#,
      warehouse_id=#WAREHOUSE_ID#,
      area_type=#AREA_TYPE#,
      area_code=#AREA_CODE#,
      area_name=#AREA_NAME#,
      area_order=#AREA_ORDER#,
      area_flag=#AREA_FLAG#,
      area_remark=#AREA_REMARK#,
      area_group=#AREA_GROUP#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					area_id=#AREA_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="WH_AREA_DELETE" parameterClass="int">
			Delete From WH_AREA
			<dynamic prepend="WHERE">
				<isParameterPresent>
					area_id=#AREA_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>