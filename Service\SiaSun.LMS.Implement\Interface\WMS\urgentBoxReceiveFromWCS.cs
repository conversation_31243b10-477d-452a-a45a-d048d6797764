﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 紧急配料\整理未满箱任务下发
    /// </summary>
    public class urgentBoxReceiveFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public List<FirstDetails> firstDetails { get; set; }//一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _boxNo = string.Empty;       //箱号
            private string _boxType = string.Empty;           //箱子类型
            public List<SecondDetails> secondDetails { get; set; }//二级明细

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }
        }
        /// <summary>
        /// 入参_二级明细
        /// </summary>
        public class SecondDetails
        {
            private string _gridNo = string.Empty;        //格子号
            private string _itemCode = string.Empty;     //物料号
            private string _quantity = string.Empty;     //数量

            private string _ertryTime = string.Empty;     //原始入库时间 2020-10-15 18:52:17
            private string _exceptionFlag = string.Empty;     //原始异常标记 2021-05-13 09:31:07

            private string _invFollow = string.Empty;           //质量追溯码  2025-06-26新增

            /// <summary>
            /// 格子号
            /// </summary>
            public string gridNo
            {
                get { return _gridNo; }
                set { _gridNo = value; }
            }

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }

            /// <summary>
            /// 最初入库时间 2020-10-15 18:52:45
            /// </summary>
            public string ertryTime
            {
                get { return _ertryTime; }
                set { _ertryTime = value; }
            }

            /// <summary>
            /// 原始异常标记 2021-05-13 09:31:07
            /// </summary>
            public string exceptionFlag
            {
                get { return _exceptionFlag; }
                set { _exceptionFlag = value; }
            }

            /// <summary>
            /// 质量追溯码  2025-06-26新增
            /// </summary>
            public string invFollow
            {
                get { return _invFollow; }
                set { _invFollow = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(int paraManageId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();

            try
            {
                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(paraManageId);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("urgentBoxReceiveFromWCS.NotifyMethod:未能获取任务信息 任务ID_{0}", paraManageId);
                    return bResult;
                }
                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(paraManageId);
                if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("urgentBoxReceiveFromWCS.NotifyMethod:未能获取任务列表 任务ID_{0}", paraManageId);
                    return bResult;
                }

                List<FirstDetails> lsFirstDetails = new List<FirstDetails>();
                List<SecondDetails> lsSecondDetails = new List<SecondDetails>();

                foreach (Model.MANAGE_LIST itemMANAGE_LIST in lsMANAGE_LIST)
                {
                    SecondDetails secondDetails = new SecondDetails();
                    secondDetails.gridNo = itemMANAGE_LIST.BOX_BARCODE;
                    secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(itemMANAGE_LIST.GOODS_ID).GOODS_CODE;
                    secondDetails.quantity = itemMANAGE_LIST.MANAGE_LIST_QUANTITY.ToString();
                    //将最初的入库时间传给WMS 2020-10-15 18:58:47
                    secondDetails.ertryTime = itemMANAGE_LIST.ORIGIN_ENTRY_TIME;
                    //将异常标记传给WMS 2021-05-13 10:21:33
                    secondDetails.exceptionFlag = itemMANAGE_LIST.GOODS_PROPERTY2;

                    secondDetails.invFollow = itemMANAGE_LIST.GOODS_PROPERTY5;   //质量追溯码  2025-06-26
                    lsSecondDetails.Add(secondDetails);
                }

                FirstDetails firstDetails = new FirstDetails();
                firstDetails.boxNo = mMANAGE_MAIN.STOCK_BARCODE;
                firstDetails.boxType = mMANAGE_MAIN.CELL_MODEL;
                firstDetails.secondDetails = lsSecondDetails;
                lsFirstDetails.Add(firstDetails);
                
                inputPara.uniqueCode = mMANAGE_MAIN.MANAGE_RELATE_CODE;
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";
                inputPara.firstDetails = lsFirstDetails;

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);
                bResult = this._S_WESJsonService.urgentBoxReceiveFromWCS(strInputParaJson, out sResult);
                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if(outputPara.responseCode!="1")
                    {
                        bResult = false;
                        sResult = string.Format(" urgentBoxReceiveFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("urgentBoxReceiveFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }

    }
}
