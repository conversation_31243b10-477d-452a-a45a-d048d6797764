﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucEditPanel.xaml 的交互逻辑
    /// </summary>
    public partial class ucEditPanel : UserControl
    {
        string strTableName = null;
        string windowName = null;

        /// <summary>
        /// 关联XML表名
        /// </summary>
        public string U_XmlTableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// 关联窗体名称
        /// </summary>
        public string U_WindowName
        {
            get { return windowName; }
            set { windowName = value; }
        }

        public ucEditPanel()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 根据DataGrid以及DataRow初始化控件
        /// </summary>
        public void U_InitControl(bool IsAutoDock,DataGrid gridApp,DataRow rowSource)
        {
            this.gridControls.Children.Clear();
            this.gridControls.RowDefinitions.Clear();

            this.DataContext = rowSource;

            //开始初始化
            this.BeginInit();

            //如果自动布局停靠则添加面板
            WrapPanel panelAuto = null;
            if (IsAutoDock)
            {
                panelAuto = new WrapPanel();
                this.gridControls.Children.Add(panelAuto);
                Grid.SetColumnSpan(panelAuto, 2);
            }

            foreach (DataGridColumn col in gridApp.Columns)
            {
                if (col.Visibility == System.Windows.Visibility.Visible)
                {
                    //判断类型
                    if (col is DataGridTextColumn)
                    {
                        AddTextBox(panelAuto,(col as DataGridTextColumn), rowSource);
                    }
                    else if (col is DataGridCheckBoxColumn)
                    {
                        AddCheckBox(panelAuto, (col as DataGridCheckBoxColumn), rowSource);
                    }
                    else if (col is DataGridComboBoxColumn)
                    {
                        AddComboBox(panelAuto, (col as DataGridComboBoxColumn), rowSource);
                    }
                }
            }

            //结束初始化
            this.EndInit();
        }

        #region     ------添加控件

        /// <summary>
        /// 添加标题
        /// </summary>
        private void AddHeader(WrapPanel panelDock, DataGridColumn col, int colIndex, int rowIndex)
        {
            //显示标题
            TextBlock txtHeader = new TextBlock();
            txtHeader.Margin = new Thickness(5, 0, 2, 0);
            txtHeader.MinWidth = 65;
            txtHeader.VerticalAlignment = System.Windows.VerticalAlignment.Center;

            //判断是否自动布局
            if (panelDock == null)
            {
                this.gridControls.Children.Add(txtHeader);
                Grid.SetColumn(txtHeader, colIndex);
                Grid.SetRow(txtHeader, rowIndex);
            }
            else
            {
                panelDock.Children.Add(txtHeader);
            }

            //绑定标题
            Binding bind = new Binding("Header");
            bind.Source = col;
            txtHeader.SetBinding(TextBlock.TextProperty, bind);
        }

        /// <summary>
        /// 添加文本编辑
        /// </summary>
        private void AddTextBox(WrapPanel panelDock,DataGridTextColumn colText,DataRow rowSource)
        {
            //获得列绑定对象
            Binding bind = colText.Binding as Binding;
            if (bind == null)
                return;

            string strPath = bind.Path.Path;

            //判断是否自动布局
            if (panelDock == null)
            {
                //添加行定义
                RowDefinition rowDefine = new RowDefinition();
                rowDefine.Height = GridLength.Auto;
                this.gridControls.RowDefinitions.Add(rowDefine);
            }

            //显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(panelDock, colText, 0, intRowIndex);

            //添加内容控件
            TextBox txtContent = new TextBox();
            
            txtContent.Style = (Style)MainApp.GetStyleResource("styleDefaultTextBox");
            txtContent.Margin = new Thickness(10, 2, 5, 2);

            //判断是否自动布局
            if (panelDock == null)
            {
                this.gridControls.Children.Add(txtContent);
                Grid.SetColumn(txtContent, 1);
                Grid.SetRow(txtContent, intRowIndex);
            }
            else
            {
                panelDock.Children.Add(txtContent);
            }

            //设置是否只读
            txtContent.IsEnabled = !colText.IsReadOnly;

            //设置绑定关系
            Binding bindContent = new Binding( string.Format("[{0}]", strPath));
            txtContent.SetBinding(TextBox.TextProperty,bindContent);
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddCheckBox(WrapPanel panelDock, DataGridCheckBoxColumn colChk, DataRow rowSource)
        {
            //获得列绑定对象
            Binding bind = colChk.Binding as Binding;
            if (bind == null)
                return;

            string strPath = bind.Path.Path;

              //判断是否自动布局
            if (panelDock == null)
            {
                //添加行定义
                RowDefinition rowDefine = new RowDefinition();
                rowDefine.Height = GridLength.Auto;
                this.gridControls.RowDefinitions.Add(rowDefine);
            }

            //设置显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(panelDock,colChk, 0, intRowIndex);

            //控件定义
            CheckBox chkBox = new CheckBox();
            chkBox.IsThreeState = false;
            chkBox.Margin = new Thickness(10, 2, 5,2);

              //判断是否自动布局
            if (panelDock == null)
            {
                Grid.SetColumn(chkBox, 1);
                Grid.SetRow(chkBox, intRowIndex);
                this.gridControls.Children.Add(chkBox);
            }
            else
            {
                panelDock.Children.Add(chkBox);
            }

            //设置是否只读
            chkBox.IsEnabled = !colChk.IsReadOnly;

            //设置绑定关系
            Binding bindContent = new Binding();
            chkBox.SetBinding(CheckBox.IsCheckedProperty, string.Format("[{0}]", strPath));
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddComboBox(WrapPanel panelDock, DataGridComboBoxColumn colCmb, DataRow rowSource)
        {
            //获得列绑定对象
            Binding bind = colCmb.SelectedValueBinding as Binding;
            if (bind == null)
                return;
            string strPath = bind.Path.Path;

              //判断是否自动布局
            if (panelDock == null)
            {
                //添加行定义
                RowDefinition rowDefine = new RowDefinition();
                rowDefine.Height = GridLength.Auto;
                this.gridControls.RowDefinitions.Add(rowDefine);
            }

            //设置显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(panelDock,colCmb, 0, intRowIndex);

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Margin = new Thickness(10, 2, 5,2);
            cmb.DisplayMemberPath = colCmb.DisplayMemberPath;
            cmb.SelectedValuePath = colCmb.SelectedValuePath;
            cmb.ItemsSource = colCmb.ItemsSource;

              //判断是否自动布局
            if (panelDock == null)
            {
                this.gridControls.Children.Add(cmb);
                Grid.SetColumn(cmb, 1);
                Grid.SetRow(cmb, intRowIndex);
            }
            else
            {
                panelDock.Children.Add(cmb);
            }

            //设置是否只读
            cmb.IsEnabled = !colCmb.IsReadOnly;
            
            //设置绑定关系
            Binding bindContent = new Binding();
            cmb.SetBinding(ComboBox.SelectedValueProperty, string.Format("[{0}]", strPath));
        }

        #endregion

        /// <summary>
        /// 根据Model实例对象初始化控件
        /// </summary>
        public void U_InitControl<T>(bool IsAddNew,string WindowName, string XmlTableName, T Model)
        {
            this.windowName = WindowName;
            this.strTableName = XmlTableName;

            this.gridControls.Children.Clear();
            this.gridControls.RowDefinitions.Clear();

            this.DataContext = Model;

            try
            {
                CustomerDescriptions cusDescription = new CustomerDescriptions();
                using (DataTable tableFieldDescription = string.IsNullOrEmpty(WindowName) ? cusDescription.GetStyleDataTable(XmlTableName) : cusDescription.GetFormStyleDataTable(WindowName, XmlTableName))
                {
                    //根据序号排序并添加列显示
                    DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                    var queryField = from row in arRowField orderby Convert.ToInt32(row["Order"].ToString()) select row;

                    //判断控件类型设置显示列样式
                    foreach (DataRow rowField in queryField)
                    {
                        if (!rowField.IsNull("Header") && !String.IsNullOrEmpty(rowField["Header"].ToString()))
                        {
                            string strControlType = rowField["ControlType"].ToString().ToLower();
                            //判断类型
                            switch (strControlType)
                            {
                                case "checkbox":
                                    AddCheckBox<T>(IsAddNew, Model, rowField);
                                    break;
                                case "combobox":
                                    AddComboBox<T>(IsAddNew, Model, rowField);
                                    break;
                                case "elementcombox":
                                    AddElmentComboBox(IsAddNew, Model, rowField);
                                    break;
                                default:
                                    AddTextBox<T>(IsAddNew, Model, rowField);
                                    break;
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        #region     ------添加控件

        /// <summary>
        /// 添加标题
        /// </summary>
        private void AddHeader(string Header, int colIndex, int rowIndex)
        {
            //显示标题
            TextBlock txtHeader = new TextBlock();
            txtHeader.VerticalAlignment = System.Windows.VerticalAlignment.Center;
            txtHeader.MinWidth = 65;
            this.gridControls.Children.Add(txtHeader);
            Grid.SetColumn(txtHeader, colIndex);
            Grid.SetRow(txtHeader, rowIndex);

            //绑定标题
            txtHeader.Text = Header;
        }

        /// <summary>
        /// 添加文本编辑
        /// </summary>
        private void AddTextBox<T>(bool IsAddNew, T Model, DataRow rowField)
        {
            //添加行定义
            RowDefinition rowDefine = new RowDefinition();
            rowDefine.Height = GridLength.Auto;
            this.gridControls.RowDefinitions.Add(rowDefine);

            //显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(rowField["Header"].ToString(), 0, intRowIndex);

            //添加内容控件
            TextBox txtContent = new TextBox();
            txtContent.Margin = new Thickness(10, 2, 5, 2);
            txtContent.Style = (Style)MainApp.GetStyleResource("styleDefaultTextBox");
            this.gridControls.Children.Add(txtContent);
            Grid.SetColumn(txtContent, 1);
            Grid.SetRow(txtContent, intRowIndex);

            //设置是否只读
            txtContent.IsEnabled = (!rowField.IsNull("ReadOnly") && !Convert.ToBoolean(Convert.ToInt32(rowField["ReadOnly"])));

            //设置绑定关系
            Binding bindContent = new Binding(rowField["Column"].ToString());
            //设置校验规则
            if (txtContent.IsEnabled)
            {
                this.AddValidateRule(bindContent, rowField["Validation"].ToString());
            }
            txtContent.SetBinding(TextBox.TextProperty, bindContent);

            //设置默认值
            if (IsAddNew)
            {
                if (string.IsNullOrEmpty(txtContent.Text) && !rowField.IsNull("DefaultValue"))
                {
                    txtContent.Text = rowField["DefaultValue"].ToString();
                }
            }
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddCheckBox<T>(bool IsAddNew, T Model, DataRow rowField)
        {
            //添加行定义
            RowDefinition rowDefine = new RowDefinition();
            rowDefine.Height = GridLength.Auto;
            this.gridControls.RowDefinitions.Add(rowDefine);

            //设置显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(rowField["Header"].ToString(), 0, intRowIndex);

            //控件定义
            CheckBox chkBox = new CheckBox();
            chkBox.IsThreeState = false;
            chkBox.Margin = new Thickness(10, 2, 5, 2);
            Grid.SetColumn(chkBox, 1);
            Grid.SetRow(chkBox, intRowIndex);
            this.gridControls.Children.Add(chkBox);

            //设置是否只读
            chkBox.IsEnabled = (!rowField.IsNull("ReadOnly") && !Convert.ToBoolean(Convert.ToInt32(rowField["ReadOnly"])));
                       
            //设置绑定关系
            Binding bindContent = new Binding(rowField["Column"].ToString());
            if (chkBox.IsEnabled)
            {
                this.AddValidateRule(bindContent,rowField["Validation"].ToString());
            }
            chkBox.SetBinding(CheckBox.IsCheckedProperty, bindContent);

            //设置默认值
            if (IsAddNew)
            {
                if (!rowField.IsNull("DefaultValue"))
                {
                    chkBox.IsChecked = string.IsNullOrEmpty(rowField["DefaultValue"].ToString()) ? false : Convert.ToBoolean(Convert.ToInt32(rowField["DefaultValue"].ToString()));
                }
            }
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddComboBox<T>(bool IsAddNew, T Model, DataRow rowField)
        {
            //添加行定义
            RowDefinition rowDefine = new RowDefinition();
            rowDefine.Height = GridLength.Auto;
            this.gridControls.RowDefinitions.Add(rowDefine);

            //设置显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(rowField["Header"].ToString(), 0, intRowIndex);

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Margin = new Thickness(10, 2, 5, 2);
            //wdz alter 2017-08-16
            //cmb.DisplayMemberPath = "NAME".ToLower();
            //cmb.SelectedValuePath = "VALUE".ToLower();
            cmb.DisplayMemberPath = MainApp._SYS_PARAMETER["DbProvider"].ToLower()== "oracle" ? "NAME":"name";
            cmb.SelectedValuePath = MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? "VALUE" : "value";
            cmb.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString()).DefaultView;

            this.gridControls.Children.Add(cmb);
            Grid.SetColumn(cmb, 1);
            Grid.SetRow(cmb, intRowIndex);

            //设置是否只读
            cmb.IsEnabled = (!rowField.IsNull("ReadOnly") && !Convert.ToBoolean(Convert.ToInt32(rowField["ReadOnly"])));

            //设置绑定关系
            Binding bindContent = new Binding(rowField["Column"].ToString());
            if (cmb.IsEnabled)
            {
                this.AddValidateRule(bindContent, rowField["Validation"].ToString());
            }
            cmb.SetBinding(ComboBox.SelectedValueProperty,bindContent );

            //设置默认值
            if (IsAddNew)
            {
                if (!rowField.IsNull("DefaultValue"))
                {
                    cmb.SelectedValue = rowField["DefaultValue"];
                }
            }
        }

        /// <summary>
        /// 添加选中框
        /// </summary>
        private void AddElmentComboBox<T>(bool IsAddNew, T Model, DataRow rowField)
        {
            //添加行定义
            RowDefinition rowDefine = new RowDefinition();
            rowDefine.Height = GridLength.Auto;
            this.gridControls.RowDefinitions.Add(rowDefine);

            //设置显示标题
            int intRowIndex = gridControls.RowDefinitions.Count - 1;
            AddHeader(rowField["Header"].ToString(), 0, intRowIndex);

            //设置内容控件
            ComboBox cmb = new ComboBox();
            cmb.Margin = new Thickness(10, 2, 5, 2);
            this.gridControls.Children.Add(cmb);
            if (!rowField.IsNull("DataBind"))
            {
                string[] arStr = rowField["DataBind"].ToString().Split('|');
                cmb.ItemsSource = arStr;
            }
            Grid.SetColumn(cmb, 1);
            Grid.SetRow(cmb, intRowIndex);

            //设置是否只读
            cmb.IsEnabled = (!rowField.IsNull("ReadOnly") && !Convert.ToBoolean(Convert.ToInt32(rowField["ReadOnly"])));
            
            //设置绑定关系
            Binding bindContent = new Binding(rowField["Column"].ToString());
            if (cmb.IsEnabled)
            {
                this.AddValidateRule(bindContent, rowField["Validation"].ToString());
            }
            cmb.SetBinding(ComboBox.SelectedValueProperty, bindContent);

            //设置默认值
            if (IsAddNew)
            {
                if (!rowField.IsNull("DefaultValue"))
                {
                    cmb.SelectedItem = rowField["DefaultValue"].ToString();
                }
            }
        }

        #endregion

        #region     ------校验规则

        /// <summary>
        /// 添加校验规则
        /// </summary>
        private void AddValidateRule(Binding bind,string ValidateFlag)
        { 
            string[] arStringValidate = ValidateFlag.ToString().ToLower().Split('|');
            foreach (string strValidateFlag in arStringValidate)
            {
                //是否空
                if (strValidateFlag.Contains("nonenull"))
                {
                    bind.ValidationRules.Add(new Validate.ValidateNullEmptyRule());
                    bind.UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged;
                }

                //长度范围
                if (strValidateFlag.Contains("lengthrange"))
                {
                    string[] arValidateLength = strValidateFlag.Split('=');
                    if (arValidateLength.Length == 2)
                    {
                        string[] arLengthRange = arValidateLength[1].Split('-');
                        if (arLengthRange.Length == 2)
                        {
                            Validate.ValidateLengthRule validateLengthRule = new Validate.ValidateLengthRule();
                            validateLengthRule.MinLength = Convert.ToInt32(arLengthRange[0]);
                            validateLengthRule.MaxLength = Convert.ToInt32(arLengthRange[1]);
                            bind.ValidationRules.Add(validateLengthRule);
                            bind.UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged;
                        }
                    }
                }

                //值范围
                if (strValidateFlag.Contains("valuerange"))
                {
                    string[] arValidateValueRange = strValidateFlag.Split('=');
                    if (arValidateValueRange.Length == 2)
                    {
                        string[] arValueRange = arValidateValueRange[1].Split('-');
                        if (arValueRange.Length == 2)
                        {
                            Validate.ValidateNumberRangeRule validateValueRangeRule = new Validate.ValidateNumberRangeRule();
                            validateValueRangeRule.Min = Convert.ToInt32(arValueRange[0]);
                            validateValueRangeRule.Max = Convert.ToInt32(arValueRange[1]);
                            bind.ValidationRules.Add(validateValueRangeRule);
                            bind.UpdateSourceTrigger = UpdateSourceTrigger.PropertyChanged;
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 检查输入是否合法
        /// </summary>
        /// <returns></returns>
        public bool U_IsValidate(out string ErrorMessage)
        {
            return this.IsValid(this.gridControls, out ErrorMessage);
        }

        /// <summary>
        /// 校验是否合法
        /// </summary>
        private bool IsValid(DependencyObject parent,out string ErrorMessage)
        {  
            // Validate all the bindings on the parent        
            bool valid = true;
            ErrorMessage = string.Empty;

            LocalValueEnumerator localValues = parent.GetLocalValueEnumerator();
            while (localValues.MoveNext())
            {
                LocalValueEntry entry = localValues.Current;
                if (BindingOperations.IsDataBound(parent, entry.Property))
                {
                    Binding binding = BindingOperations.GetBinding(parent, entry.Property);
                    foreach (ValidationRule rule in binding.ValidationRules)
                    {
                        ValidationResult result = rule.Validate(parent.GetValue(entry.Property), null);
                        if (!result.IsValid)
                        {
                            BindingExpression expression = BindingOperations.GetBindingExpression(parent, entry.Property);
                            System.Windows.Controls.Validation.MarkInvalid(expression, new ValidationError(rule, expression, result.ErrorContent, null));
                            valid = false;
                            ErrorMessage = result.ErrorContent.ToString();
                            return valid;
                        }
                    }
                }
            }

            // Validate all the bindings on the children        
            for (int i = 0; i != VisualTreeHelper.GetChildrenCount(parent); ++i)
            {
                DependencyObject child = VisualTreeHelper.GetChild(parent, i);
                if (!IsValid(child, out ErrorMessage))
                {
                    valid = false;
                    return valid;
                }
            }

            return valid;
        }

        #endregion

        /// <summary>
        /// 更新
        /// </summary>
        public void U_Clear()
        {
            this.gridControls.Children.Clear();
        }
    }
}
