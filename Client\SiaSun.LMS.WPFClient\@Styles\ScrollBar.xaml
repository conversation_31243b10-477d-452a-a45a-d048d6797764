﻿<ResourceDictionary  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
  mc:Ignorable="d"
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="NuclearRepeatButton" d:IsControlPart="True" TargetType="{x:Type RepeatButton}" BasedOn="{x:Null}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="Highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value=".5" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="Highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.4000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Rectangle x:Name="Background" RadiusX="1" RadiusY="1" StrokeThickness="1" Stroke="#FF8A9199">
                            <Rectangle.Fill>
                                <LinearGradientBrush StartPoint="0,0.5" EndPoint="1,0.5">
                                    <GradientStop Color="#FFF0F4F8" />
                                    <GradientStop Color="#FFD7DBE1" Offset="1" />
                                </LinearGradientBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                        <Rectangle x:Name="Highlight" RadiusX="1" RadiusY="1" Margin="1" Opacity="0" Fill="White" IsHitTestVisible="false"/>
                        <ContentPresenter HorizontalAlignment="Center" x:Name="ContentPresenter" VerticalAlignment="Center" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" ContentTemplateSelector="{TemplateBinding ContentTemplateSelector}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" x:Name="HoverOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" TargetName="Background" Value="0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="NuclearThumbStyle" d:IsControlPart="True" TargetType="{x:Type Thumb}" BasedOn="{x:Null}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="Highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value=".5" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="Highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.4000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Rectangle x:Name="Background" RadiusX="1" RadiusY="1" StrokeThickness="1" Stroke="#FF8A9199">
                            <Rectangle.Fill>
                                <LinearGradientBrush StartPoint="0,0.5" EndPoint="1,0.5">
                                    <GradientStop Color="#FFF0F4F8" />
                                    <GradientStop Color="#FFD7DBE1" Offset="1" />
                                </LinearGradientBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                        <Rectangle x:Name="Highlight" RadiusX="1" RadiusY="1" Margin="1" Opacity="0" Fill="White" IsHitTestVisible="false"/>
                        <Path Margin="0,-6,0,0" Width="11" Height="1" Stretch="Fill" Stroke="#FF848485" StrokeThickness="1" Data="M4.8333325,7.2499995 L12.012101,7.2499995" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Path.Fill>
                                <LinearGradientBrush EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
                                    <GradientStop Color="#FFC8C9CC" Offset="0.487" />
                                    <GradientStop Color="#FFF0F0F0" Offset="0.518" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path Margin="0,-2,0,0" Width="11" Height="1" Stretch="Fill" Stroke="#FF848485" StrokeThickness="1" Data="M4.8333325,7.2499995 L12.012101,7.2499995" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Path.Fill>
                                <LinearGradientBrush EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
                                    <GradientStop Color="#FFC8C9CC" Offset="0.487" />
                                    <GradientStop Color="#FFF0F0F0" Offset="0.518" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path Margin="0,0,0,-2" Width="11" Height="1" Stretch="Fill" Stroke="#FF848485" StrokeThickness="1" Data="M4.8333325,7.2499995 L12.012101,7.2499995" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Path.Fill>
                                <LinearGradientBrush EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
                                    <GradientStop Color="#FFC8C9CC" Offset="0.487" />
                                    <GradientStop Color="#FFF0F0F0" Offset="0.518" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path Margin="0,0,0,-6" Width="11" Height="1" Stretch="Fill" Stroke="#FF848485" StrokeThickness="1" Data="M4.8333325,7.2499995 L12.012101,7.2499995" VerticalAlignment="Center" HorizontalAlignment="Center">
                            <Path.Fill>
                                <LinearGradientBrush EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
                                    <GradientStop Color="#FFC8C9CC" Offset="0.487" />
                                    <GradientStop Color="#FFF0F0F0" Offset="0.518" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" x:Name="HoverOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" TargetName="Background" Value="0" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ScrollBar}">
        <Setter Property="Stylus.IsFlicksEnabled" Value="false" />
        <!--<Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.ControlTextBrushKey}}" />-->
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollBar}">
                    <Grid x:Name="GridRoot" Width="{DynamicResource {x:Static SystemParameters.VerticalScrollBarWidthKey}}" RenderTransformOrigin="0.5,0.5">
                        <Grid.Background>
                            <LinearGradientBrush StartPoint="0,0.5" EndPoint="1,0.5">
                                <GradientStop Color="#FFD5DAE0" />
                                <GradientStop Color="#DFE4EB" Offset="1" />
                            </LinearGradientBrush>
                        </Grid.Background>
                        <Grid.RowDefinitions>
                            <RowDefinition MaxHeight="18" />
                            <RowDefinition Height="*" />
                            <RowDefinition MaxHeight="18" />
                        </Grid.RowDefinitions>
                        <RepeatButton x:Name="DecreaseRepeat" Style="{DynamicResource NuclearRepeatButton}" Command="ScrollBar.LineUpCommand">
                            <Grid>
                                <Path Data="F1 M 541.537,173.589L 531.107,173.589L 536.322,167.49L 541.537,173.589 Z " Height="6" Width="10" HorizontalAlignment="Center" VerticalAlignment="Center" Stretch="Uniform" IsHitTestVisible="False">
                                    <Path.Fill>
                                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                            <GradientStop Color="#FF5E6D91" />
                                            <GradientStop Color="#FF2B3B60" Offset="1" />
                                        </LinearGradientBrush>
                                    </Path.Fill>
                                </Path>
                            </Grid>
                        </RepeatButton>

                        <Track Grid.Row="1" x:Name="PART_Track" Orientation="Vertical" IsDirectionReversed="true">
                            <Track.Thumb>
                                <Thumb Style="{DynamicResource NuclearThumbStyle}" />
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton x:Name="PageUp" Style="{DynamicResource NuclearScrollRepeatButtonStyle}" Command="ScrollBar.PageDownCommand" />
                            </Track.IncreaseRepeatButton>
                            <Track.DecreaseRepeatButton>
                                <RepeatButton x:Name="PageDown" Style="{DynamicResource NuclearScrollRepeatButtonStyle}" Command="ScrollBar.PageUpCommand" />
                            </Track.DecreaseRepeatButton>
                        </Track>

                        <RepeatButton Grid.Row="2" x:Name="IncreaseRepeat" Style="{DynamicResource NuclearRepeatButton}" Command="ScrollBar.LineDownCommand">
                            <Grid>
                                <Path Data="F1 M 531.107,321.943L 541.537,321.943L 536.322,328.042L 531.107,321.943 Z " Grid.Row="4" Height="6" Width="10" HorizontalAlignment="Center" VerticalAlignment="Center" Stretch="Uniform" IsHitTestVisible="False">
                                    <Path.Fill>
                                        <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                            <GradientStop Color="#FF5E6D91" Offset="0.004" />
                                            <GradientStop Color="#FF2B3B60" Offset="0.996" />
                                        </LinearGradientBrush>
                                    </Path.Fill>
                                </Path>
                            </Grid>
                        </RepeatButton>
                    </Grid>

                    <ControlTemplate.Triggers>
                        <Trigger Property="Orientation" Value="Horizontal">

                            <Setter Property="LayoutTransform" TargetName="GridRoot">
                                <Setter.Value>
                                    <TransformGroup>
                                        <RotateTransform Angle="90" />
                                        <ScaleTransform ScaleY="1" ScaleX="-1"/>
                                    </TransformGroup>
                                </Setter.Value>
                            </Setter>

                            <Setter TargetName="PART_Track" Property="Orientation" Value="Vertical" />

                            <Setter Property="Command" Value="ScrollBar.LineLeftCommand" TargetName="DecreaseRepeat" />
                            <Setter Property="Command" Value="ScrollBar.LineRightCommand" TargetName="IncreaseRepeat" />
                            <Setter Property="Command" Value="ScrollBar.PageLeftCommand" TargetName="PageDown" />
                            <Setter Property="Command" Value="ScrollBar.PageRightCommand" TargetName="PageUp" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ScrollViewer}" BasedOn="{x:Null}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ScrollViewer}">
                    <Grid Background="{TemplateBinding Background}">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="Auto" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>
                        <ScrollContentPresenter Grid.Column="0" Grid.Row="0" Margin="{TemplateBinding Padding}" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" CanContentScroll="{TemplateBinding CanContentScroll}" />

                        <ScrollBar Visibility="{TemplateBinding ComputedHorizontalScrollBarVisibility}" Grid.Column="0" Grid.Row="1" x:Name="PART_HorizontalScrollBar" Orientation="Horizontal" Value="{Binding Path=HorizontalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" ViewportSize="{TemplateBinding ViewportWidth}" Minimum="0" Maximum="{TemplateBinding ScrollableWidth}" AutomationProperties.AutomationId="HorizontalScrollBar" />
                        <ScrollBar Visibility="{TemplateBinding ComputedVerticalScrollBarVisibility}" Grid.Column="1" Grid.Row="0" x:Name="PART_VerticalScrollBar" Orientation="Vertical" Value="{Binding Path=VerticalOffset, Mode=OneWay, RelativeSource={RelativeSource TemplatedParent}}" ViewportSize="{TemplateBinding ViewportHeight}" Minimum="0" Maximum="{TemplateBinding ScrollableHeight}" AutomationProperties.AutomationId="VerticalScrollBar" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>