﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using SSLMS.MobileUI.uc;

namespace SSLMS.MobileUI
{
    public partial class ucGrid : UserControl
    {
        private MyDataGrid _dgv = null;

        public MyDataGrid dgv
        {
            get
            {
                return _dgv;
            }
        }

        private DataTable _dt = new DataTable();

        public DataTable dt
        {
            get { return _dt; }
            set { _dt = value; }
        }

        private string _sTable;

        public string sTable
        {
            get { return _sTable; }
            set { _sTable = value; }
        }

        private string _sXml;

        public string sXml
        {
            get { return _sXml; }
            set { _sXml = value; }
        }

        private string _sWhere;

        public string sWhere
        {
            get { return _sWhere; }
            set { _sWhere = value; }
        }

        private string _listColumn;

        public string listColumn
        {
            get { return _listColumn; }
            set { _listColumn = value; }
        }

        private bool _bCheck = false;

        public bool bCheck
        {
            get { return _bCheck; }
            set { _bCheck = value; }
        }

        private string _colSplit = string.Empty;

        public string colSplit
        {
            get { return _colSplit; }
            set { _colSplit = value; }
        }

        private string _colGroup;

        public string colGroup
        {
            get { return _colGroup; }
            set { _colGroup = value; }
        }

        private string _colDefaultValue;

        public string colDefaultValue
        {
            get { return _colDefaultValue; }
            set { _colDefaultValue = value; }
        }

        public ucGrid()
        {
            InitializeComponent();

            this._dgv = new MyDataGrid();

            this.Controls.Add(_dgv);

            this._dgv.Dock = DockStyle.Fill;

        }

        public void Init()
        {
            this._sXml = string.IsNullOrEmpty(this._sXml) ? this._sTable : this._sXml;

            if (this._bCheck)
            {
                this._dgv.AddCheck();
            }

            if (!string.IsNullOrEmpty(this._colSplit))
            {
                this._dgv.Name = "dgv_" + SSLMS.MobileUI.Common.GetSplitStr(this.Name, new char[] { '_' }, 1);
            }

            this.dgv_Bind();
        }

        private void dgv_Bind()
        {
            string sql = string.Format("select * from {0} where 1=1 {1}", sTable, sWhere);

            if (this._dt.Rows.Count > 0)
            {
                this._dt.Clear();
            }

            this._dt.Merge(Program._I_PDAService.GetList(sql));

            this._dt.TableName = this._sTable;

            if (this._bCheck && !this._dt.Columns.Contains("check"))
            {
                this._dt.Columns.Add("check", System.Type.GetType("System.Boolean"));

                this._dt.Columns["check"].DefaultValue = false;
            }

            if (!string.IsNullOrEmpty(this.listColumn))
            {
                this._dgv.AddOrder(this.listColumn);
            }

            this._dgv.AddHeader(this._sXml);

            if (!string.IsNullOrEmpty(this._colGroup))
            {
                //this.SplitCol();
            }

            this._dgv.dgts.MappingName = this._dt.TableName;

            this._dgv.DataSource = this._dt;
        }



        public string GetCheckColumns(string sResultColumn, char[] Split)
        {
            return this._dgv.GetCheckColumns("check", sResultColumn, Split);
        }

        public void SetEditColumns(string sColName, Color c)
        {
            this._dgv.SetEditColumns(sColName, c);
        }
    }
}
