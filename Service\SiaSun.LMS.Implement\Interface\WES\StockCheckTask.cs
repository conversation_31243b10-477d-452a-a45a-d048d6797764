﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 盘点任务下发
    /// </summary>
    public class StockCheckTask : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _countCode = string.Empty;            //盘点计划号
            private string _uniqueCode = string.Empty;           //唯一码
            private string _interfaceType = string.Empty;        //接口类型
            private string _interfaceSource = string.Empty;      //接口来源
            public List<FirstDetails> firstDetails { get; set; } //一级明细

            /// <summary>
            /// 盘点计划号
            /// </summary>
            public string countCode
            {
                get { return _countCode; }
                set { _countCode = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _itemCode = string.Empty;        //物料号

            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            int iPlanId = 0;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.countCode) || string.IsNullOrEmpty(taskInfo.uniqueCode) || taskInfo.firstDetails.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:入参存在空值");
                    return bResult;
                }

                if (taskInfo.firstDetails.GroupBy(r => r.itemCode).Count() != taskInfo.firstDetails.Count())
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:一级明细中存在重复值");
                    return bResult;
                }

                string sMultiCheckTaskCount;
                int iMultiCheckTaskCount;
                if (!this._S_SystemService.GetSysParameter("MultiCheckTaskCount", out sMultiCheckTaskCount))
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:未能成功获取盘点任务的配置信息");
                    return bResult;
                }
                if (!int.TryParse(sMultiCheckTaskCount, out iMultiCheckTaskCount) || iMultiCheckTaskCount<taskInfo.firstDetails.Count)
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:盘点无法执行_请选择[{0}]种以下物料作为盘点目标_或者在[运行参数]中修改盘点任务的配置",sMultiCheckTaskCount);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModelPlanCode(taskInfo.uniqueCode, string.Empty, string.Empty);
                if (mPLAN_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:传入唯一码已存在计划 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                /////////////////////自动选择拣选工作站出库位置作为盘点下架的位置
                IList<Model.T_PICK_STATION> lsT_PICK_STATION = this._P_T_PICK_STATION.GetList();
                var enableStationGroup = lsT_PICK_STATION.Where(r => r.PROPERTY3 == Enum.FLAG.Enable.ToString("d"));
                if(enableStationGroup.Count()<1)
                {
                    bResult = false;
                    outJson = string.Format("StockCheckTask.NotifyMethod:当前无可用工作站，请再拣选任务结束后执行盘点操作");
                    return bResult;
                }

                mPLAN_MAIN = new Model.PLAN_MAIN();
                mPLAN_MAIN.PLAN_CODE = taskInfo.uniqueCode;
                mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mPLAN_MAIN.PLAN_CREATE_TIME = Common.StringUtil.GetDateTime();
                mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mPLAN_MAIN.PLAN_RELATIVE_CODE = taskInfo.countCode;
                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanCheck.ToString();
                mPLAN_MAIN.PLAN_INOUT_STATION = this._P_WH_CELL.GetModel(enableStationGroup.First().WH_CELL_ID).CELL_CODE;
                string planLevel = string.Empty;
                mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("StockCheckPlanLevel", out planLevel) ? planLevel : "0";

                List<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();

                foreach(FirstDetails item in taskInfo.firstDetails)
                {
                    if (string.IsNullOrEmpty(item.itemCode))
                    {
                        bResult = false;
                        outJson = string.Format("StockCheckTask.NotifyMethod:入参存在空值");
                        return bResult;
                    }

                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(item.itemCode);
                    if (mGOODS_MAIN == null)
                    {
                        bResult = false;
                        outJson = string.Format("StockCheckTask.NotifyMethod:传入物料编码有误 物料编码_{0}", item.itemCode);
                        return bResult;
                    }

                    Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                    mPLAN_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;
                    lsPLAN_LIST.Add(mPLAN_LIST);
                }

                object[] invokeOutParams = new object[] { };
                bResult = this.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST, iPlanId, outJson }, out invokeOutParams);
                int.TryParse(invokeOutParams[2].ToString(), out iPlanId);
                if (!bResult)
                {
                    outJson = invokeOutParams[3].ToString();
                    return bResult;
                }

                bResult = this.Invoke("PlanBase", "PlanOutDownLoad", new object[] { iPlanId }, out outJson);
                if (!bResult)
                {
                    string strTemp = string.Empty;
                    if (!this.Invoke("PlanBase", "PlanCancel", new object[] { iPlanId ,false}, out strTemp))
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("StockCheckTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划失败_{1}_计划ID[{2}]", outJson, strTemp, iPlanId));
                    }
                    else
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("StockCheckTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划成功_计划ID[{1}]", outJson, iPlanId));
                    }

                    outJson = string.Format("StockCheckTask.NotifyMethod:执行计划失败 {0}", outJson);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("StockCheckTask.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
