﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="WH_DESCRIPTION" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="WH_DESCRIPTION" type="SiaSun.LMS.Model.WH_DESCRIPTION, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="WH_DESCRIPTION">
      <result property="WAREHOUSE_ID" column="warehouse_id" />
      <result property="DESCRIPTION_ID" column="description_id" />
			<result property="AREA_ID" column="area_id" />
			<result property="LOGIC_ID" column="logic_id" />
			<result property="START_Z" column="start_z" />
			<result property="END_Z" column="end_z" />
			<result property="START_X" column="start_x" />
			<result property="END_X" column="end_x" />
			<result property="START_Y" column="start_y" />
			<result property="END_Y" column="end_y" />
			<result property="DEVICE_CODE" column="device_code" />
			<result property="DEVICE_NAME" column="device_name" />
			<result property="LANE_WAY" column="lane_way" />
			<result property="SHELF_TYPE" column="shelf_type" />
			<result property="SHELF_NEIGHBOUR" column="shelf_neighbour" />
			<result property="CELL_MODEL" column="cell_model" />
			<result property="CELL_LOGICAL_NAME" column="cell_logical_name" />
			<result property="CELL_INOUT" column="cell_inout" />
			<result property="CELL_TYPE" column="cell_type" />
			<result property="CELL_STORAGE_TYPE" column="cell_storage_type" />
			<result property="CELL_FORK_TYPE" column="cell_fork_type" />
			<result property="CELL_FORK_COUNT" column="cell_fork_count" />
      <result property="CELL_WIDTH" column="cell_width" />
      <result property="CELL_HEIGHT" column="cell_height" />
			<result property="DESCRIPTION_FLAG" column="description_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="WH_DESCRIPTION_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        warehouse_id,
        description_id,
        area_id,
        logic_id,
        start_z,
        end_z,
        start_x,
        end_x,
        start_y,
        end_y,
        device_code,
        device_name,
        lane_way,
        shelf_type,
        shelf_neighbour,
        cell_model,
        cell_logical_name,
        cell_inout,
        cell_type,
        cell_storage_type,
        cell_fork_type,
        cell_fork_count,
        cell_width,
        cell_height,
        description_flag
        From WH_DESCRIPTION
      </select>
    
    <select id="WH_DESCRIPTION_SELECT_ORDER_BY_ID" parameterClass="int" extends = "WH_DESCRIPTION_SELECT" resultMap="SelectResult">
      ORDER BY description_id
    </select>
		
		<select id="WH_DESCRIPTION_SELECT_BY_ID" parameterClass="int" extends = "WH_DESCRIPTION_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					description_id=#DESCRIPTION_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="WH_DESCRIPTION_INSERT" parameterClass="WH_DESCRIPTION">
      Insert Into WH_DESCRIPTION (
      warehouse_id,
      area_id,
      logic_id,
      start_z,
      end_z,
      start_x,
      end_x,
      start_y,
      end_y,
      device_code,
      device_name,
      lane_way,
      shelf_type,
      shelf_neighbour,
      cell_model,
      cell_logical_name,
      cell_inout,
      cell_type,
      cell_storage_type,
      cell_fork_type,
      cell_fork_count,
      cell_width,
      cell_height,
      description_flag
      )Values(
      #WAREHOUSE_ID#,
      #AREA_ID#,
      #LOGIC_ID#,
      #START_Z#,
      #END_Z#,
      #START_X#,
      #END_X#,
      #START_Y#,
      #END_Y#,
      #DEVICE_CODE#,
      #DEVICE_NAME#,
      #LANE_WAY#,
      #SHELF_TYPE#,
      #SHELF_NEIGHBOUR#,
      #CELL_MODEL#,
      #CELL_LOGICAL_NAME#,
      #CELL_INOUT#,
      #CELL_TYPE#,
      #CELL_STORAGE_TYPE#,
      #CELL_FORK_TYPE#,
      #CELL_FORK_COUNT#,
      #CELL_WIDTH#,
      #CELL_HEIGHT#,
      #DESCRIPTION_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="DESCRIPTION_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="WH_DESCRIPTION_UPDATE" parameterClass="WH_DESCRIPTION">
      Update WH_DESCRIPTION Set
      warehouse_id=#WAREHOUSE_ID#,
      area_id=#AREA_ID#,
      logic_id=#LOGIC_ID#,
      start_z=#START_Z#,
      end_z=#END_Z#,
      start_x=#START_X#,
      end_x=#END_X#,
      start_y=#START_Y#,
      end_y=#END_Y#,
      device_code=#DEVICE_CODE#,
      device_name=#DEVICE_NAME#,
      lane_way=#LANE_WAY#,
      shelf_type=#SHELF_TYPE#,
      shelf_neighbour=#SHELF_NEIGHBOUR#,
      cell_model=#CELL_MODEL#,
      cell_logical_name=#CELL_LOGICAL_NAME#,
      cell_inout=#CELL_INOUT#,
      cell_type=#CELL_TYPE#,
      cell_storage_type=#CELL_STORAGE_TYPE#,
      cell_fork_type=#CELL_FORK_TYPE#,
      cell_fork_count=#CELL_FORK_COUNT#,
      cell_width=#CELL_WIDTH#,
      cell_height=#CELL_HEIGHT#,
      description_flag=#DESCRIPTION_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					description_id=#DESCRIPTION_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="WH_DESCRIPTION_DELETE" parameterClass="int">
			Delete From WH_DESCRIPTION
			<dynamic prepend="WHERE">
				<isParameterPresent>
					description_id=#DESCRIPTION_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>