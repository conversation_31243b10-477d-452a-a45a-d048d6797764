﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B820FA47-3514-49DB-8D33-96367A80234E}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.Persistence</RootNamespace>
    <AssemblyName>SiaSun.LMS.Persistence</AssemblyName>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <PublishUrl>http://localhost/SiaSun.LMS.Persistence/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="IBatisNet.Common">
      <HintPath>..\..\Lib\IBatisNet.Common.dll</HintPath>
    </Reference>
    <Reference Include="IBatisNet.DataMapper">
      <HintPath>..\..\Lib\IBatisNet.DataMapper.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess">
      <HintPath>..\..\Lib\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="ERP\P_MID_GOODS.cs" />
    <Compile Include="P_Base_ERP.cs" />
    <Compile Include="P_LCD_MAIN.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="P_APPLY_TYPE.cs" />
    <Compile Include="P_Base.cs" />
    <Compile Include="P_Base_House.cs" />
    <Compile Include="P_FIELD_DESCRIPTION.cs" />
    <Compile Include="P_FLOW_ACTION.cs" />
    <Compile Include="P_FLOW_NODE.cs" />
    <Compile Include="P_FLOW_PARA.cs" />
    <Compile Include="P_FLOW_TYPE.cs" />
    <Compile Include="P_GOODS_CLASS.cs" />
    <Compile Include="P_GOODS_MAIN.cs" />
    <Compile Include="ERP\P_MID_TASK.cs" />
    <Compile Include="P_GOODS_PROPERTY.cs" />
    <Compile Include="P_GOODS_TEMPLATE.cs" />
    <Compile Include="P_GOODS_TEMPLATE_LIST.cs" />
    <Compile Include="P_GOODS_TYPE.cs" />
    <Compile Include="P_IO_CONTROL.cs" />
    <Compile Include="P_IO_CONTROL_APPLY.cs" />
    <Compile Include="P_IO_CONTROL_APPLY_HIS.cs" />
    <Compile Include="P_IO_CONTROL_ROUTE.cs" />
    <Compile Include="P_LED_LIST.cs" />
    <Compile Include="P_LED_MAIN.cs" />
    <Compile Include="P_MANAGE_DETAIL.cs" />
    <Compile Include="P_MANAGE_LIST.cs" />
    <Compile Include="P_MANAGE_MAIN.cs" />
    <Compile Include="P_MANAGE_TYPE.cs" />
    <Compile Include="P_MANAGE_TYPE_PARAM.cs" />
    <Compile Include="P_PLAN_DETAIL.cs" />
    <Compile Include="P_PLAN_LIST.cs" />
    <Compile Include="P_PLAN_MAIN.cs" />
    <Compile Include="P_PLAN_TYPE.cs" />
    <Compile Include="P_RECORD_DETAIL.cs" />
    <Compile Include="P_RECORD_LIST.cs" />
    <Compile Include="P_RECORD_MAIN.cs" />
    <Compile Include="P_STORAGE_DETAIL.cs" />
    <Compile Include="P_STORAGE_LIST.cs" />
    <Compile Include="P_STORAGE_LOCK.cs" />
    <Compile Include="P_STORAGE_MAIN.cs" />
    <Compile Include="P_SYS_ITEM.cs" />
    <Compile Include="P_SYS_ITEM_LIST.cs" />
    <Compile Include="P_MANAGE_OUT_LOG.cs" />
    <Compile Include="P_SYS_LOG.cs" />
    <Compile Include="P_SYS_MENU.cs" />
    <Compile Include="P_SYS_RELATION.cs" />
    <Compile Include="P_SYS_RELATION_LIST.cs" />
    <Compile Include="P_SYS_ROLE.cs" />
    <Compile Include="P_SYS_ROLE_WINDOW.cs" />
    <Compile Include="P_SYS_TABLE_CONVERTER_LIST.cs" />
    <Compile Include="P_SYS_USER.cs" />
    <Compile Include="P_TECHNICS_MAIN.cs" />
    <Compile Include="P_TECHNICS_ROUTE.cs" />
    <Compile Include="P_T_PICK_POSITION.cs" />
    <Compile Include="P_T_PICK_POSITION_PLAN_BIND.cs" />
    <Compile Include="P_T_PICK_STATION.cs" />
    <Compile Include="P_WH_CELL_GROUP.cs" />
    <Compile Include="P_WH_AREA.cs" />
    <Compile Include="P_WH_CELL.cs" />
    <Compile Include="P_WH_DESCRIPTION.cs" />
    <Compile Include="P_WH_LOGIC.cs" />
    <Compile Include="P_WH_WAREHOUSE.cs" />
    <Compile Include="P_SYS_TABLE_CONVERTER.cs" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>