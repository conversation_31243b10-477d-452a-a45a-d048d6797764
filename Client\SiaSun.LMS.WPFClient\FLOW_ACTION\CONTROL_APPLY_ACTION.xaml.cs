﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.FLOW_ACTION
{
    /// <summary>
    /// CONTROL_ACTION.xaml 的交互逻辑
    /// </summary>
    public partial class CONTROL_APPLY_ACTION : AvalonDock.DocumentContent
    {
        bool boolAllowFlowAction = false;

        #region     ------构造函数

        public CONTROL_APPLY_ACTION()
        {
            InitializeComponent();

            this.ucFlowControlAction.U_ExecuteUpdate += new UC.ucStatusFlowActionsPanel.ExecuteUpdateEventHandler(ucFlowControlAction_U_ExecuteUpdate);
            this.ucQueryControl.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQueryControl_U_Query);
        }

        public CONTROL_APPLY_ACTION(bool _AllowFlowAction)
            : this()
        {
            boolAllowFlowAction = _AllowFlowAction;

            //判断是否允许流程控制
            if (boolAllowFlowAction)
            {
                this.gridControl.gridApp.SelectedCellsChanged += new SelectedCellsChangedEventHandler(gridApp_SelectedCellsChanged);
            }
        }

        #endregion

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitQueryControl();
            this.Control_Bind();
        }

        #region     ------查询

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            //初始化查询控件
            this.ucQueryControl.U_WindowName = this.GetType().Name;
            this.ucQueryControl.U_XmlTableName = "IO_CONTROL_APPLY";
            this.ucQueryControl.U_InitControl();
        }

        //查询
        void ucQueryControl_U_Query(string QueryWhere)
        {
            //清空操作
            this.ucFlowControlAction.U_Clear();

            //获得查询条件
            this.gridControl.U_AppendWhere = QueryWhere;
            this.gridControl.U_InitControl();
        }

        #endregion

        /// <summary>
        /// 加载任务指令
        /// </summary>
        private void Control_Bind()
        {
            //初始化数据控件
            this.gridControl.U_WindowName = this.GetType().Name;
            this.gridControl.U_TableName = "IO_CONTROL_APPLY";
            this.gridControl.U_OrderField = "CONTROL_APPLY_ID";

            this.gridControl.U_AllowChecked = false;
            this.gridControl.U_AllowPage = false;
            this.gridControl.U_AllowOperatData = false;

            this.gridControl.U_InitControl();
        }
        
        //点击选择任务指令记录
        void gridApp_SelectedCellsChanged(object sender, SelectedCellsChangedEventArgs e)
        {
            if (gridControl.gridApp.SelectedItem == null)
                return;

            DataRowView rowViewControl = gridControl.gridApp.SelectedItem as DataRowView;
            ControlActionBind(rowViewControl);
        }

        #region     ------流程操作
        
        //流程状态控制
        void ucFlowControlAction_U_ExecuteUpdate()
        {
            if (this.gridControl.gridApp.SelectedItem == null)
                return;
            try
            {
                //记录编号
                string strControlApplyID = (this.gridControl.gridApp.SelectedItem as DataRowView)["CONTROL_APPLY_ID"].ToString();

                //从新加载数据
                this.gridControl.U_Update();

                //找到原来的记录
                this.gridControl.U_SelectDataGridViewRow("CONTROL_APPLY_ID", strControlApplyID);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //绑定并显示动作按钮
        private void ControlActionBind(DataRowView rowViewControl)
        {
            try
            {
                //this.ucFlowControlAction.U_FLOW_TYPE = "FLOW_CONTROL_APPLY";
                //this.ucFlowControlAction.U_DataSource = MainApp._I_FlowService.CONTROL_APPLY_ACTION_GetTable(rowViewControl["CONTROL_APPLY_ID"].ToString());
                //this.ucFlowControlAction.U_FLOW_SOURCE_ROW = rowViewControl.Row;
                //this.ucFlowControlAction.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        #endregion
    }
}
