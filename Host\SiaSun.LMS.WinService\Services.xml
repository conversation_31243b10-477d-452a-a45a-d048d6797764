﻿<configuredServices>

  <service type="SiaSun.LMS.Implement.S_BaseService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_BaseService" binding="basicHttpBinding" url ="{0}/BaseService" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_SystemService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_SystemService" binding="basicHttpBinding" url ="{0}/SystemService" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_GoodsService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_GoodsService" binding="basicHttpBinding" url ="{0}/GoodsService" render ="House"/>

  <service type="SiaSun.LMS.Implement.S_FlowService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_FlowService" binding="basicHttpBinding" url ="{0}/FlowService" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_CellService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_CellService" binding="basicHttpBinding" url ="{0}/CellService" render ="House"/>

  <service type="SiaSun.LMS.Implement.S_PlanService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_PlanService" binding="basicHttpBinding" url ="{0}/PlanService" render ="House"/>
	<service type="SiaSun.LMS.Implement.S_ManageService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_ManageService" binding="basicHttpBinding" url ="{0}/ManageService" render ="House"/>
  <service type="SiaSun.LMS.Implement.S_StorageService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_StorageService" binding="basicHttpBinding" url ="{0}/StorageService" render ="House"/>

  <!--<service type="SiaSun.LMS.Implement.S_LEDService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_LEDService" binding="basichttpbinding" url ="{0}/LEDService"/>-->
  <!--<service type="SiaSun.LMS.Implement.S_PDAService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_PDAService" binding="basichttpbinding" url ="{0}/PDAService"/>-->
  <service type="SiaSun.LMS.Implement.S_LCDService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_LCDService" binding="basichttpbinding" url ="{0}/LCDService" render ="House"/>

  <service type="SiaSun.LMS.Implement.S_WESJsonService, SiaSun.LMS.Implement" itype ="SiaSun.LMS.Interface.I_WESJsonService" binding="basichttpbinding" url ="{0}/WESJsonService" render ="Interface"/>

</configuredServices>
