﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.KIT_MODE"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="KIT_MODE" Height="500" Width="1000"
                    Loaded="DocumentContent_Loaded">
    <Grid>

        <Grid Background="#FFE5E5E5">
            <Grid.RowDefinitions>
                <RowDefinition Height="20"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
                <RowDefinition Height="Auto"/>
            </Grid.RowDefinitions>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="20"/>
                <ColumnDefinition Width="1*"/>
                <ColumnDefinition Width="1*"/>
            </Grid.ColumnDefinitions>

            <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="1" Margin="10" Grid.ColumnSpan="1">
                <Label Content="一楼齐套出库工作模式：" Margin="10"/>
                <RadioButton Content="人工模式" x:Name="rbtnManual1"  Margin="10"/>
                <RadioButton Content="自动模式[机械手]" x:Name="rbtnAuto1"  Margin="10"/>

            </StackPanel>
            <StackPanel Orientation="Horizontal" Grid.Row="1" Grid.Column="2" Margin="10" Grid.ColumnSpan="1">
                <Label Content="一楼出库单据模式：" Margin="10"/>
                <RadioButton Content="混单模式" x:Name="rbtnMix1"  Margin="10"/>
                <RadioButton Content="单一模式" x:Name="rbtnOne1"  Margin="10"/>

            </StackPanel>
            <StackPanel Orientation="Horizontal" Grid.Row="2" Grid.Column="1" Margin="10" Grid.ColumnSpan="2" Visibility="Collapsed">
                <Label Content="二楼齐套出库工作模式：" Margin="10"/>
                <RadioButton Content="人工模式" x:Name="rbtnManual2"  Margin="10"/>
                <RadioButton Content="自动模式[机械手]" x:Name="rbtnAuto2"  Margin="10"/>

            </StackPanel>
            <StackPanel Orientation="Horizontal" Grid.Row="3" Grid.Column="1" Margin="10" Grid.ColumnSpan="2" Visibility="Collapsed">
                <Label Content="三楼齐套出库工作模式：" Margin="10"/>
                <RadioButton Content="人工模式" x:Name="rbtnManual3"  Margin="10"/>
                <RadioButton Content="自动模式[按批次顺序]" x:Name="rbtnAuto3"  Margin="10"/>

            </StackPanel>
            <StackPanel Orientation="Horizontal" Grid.Row="3" Grid.Column="2" Margin="10" Grid.ColumnSpan="1">
                <Label Content="三楼出库单据模式：" Margin="10"/>
                <RadioButton Content="混单模式" x:Name="rbtnMix3"  Margin="10"/>
                <RadioButton Content="单一模式" x:Name="rbtnOne3"  Margin="10"/>

            </StackPanel>
            <StackPanel Orientation="Horizontal" Grid.Row="4" Grid.Column="1" Margin="10" Grid.ColumnSpan="2" Visibility="Collapsed">
                <Label Content="四楼齐套出库工作模式：" Margin="10"/>
                <RadioButton Content="人工模式" x:Name="rbtnManual4"  Margin="10"/>
                <RadioButton Content="自动模式[机械手]" x:Name="rbtnAuto4"  Margin="10"/>

            </StackPanel>
            <StackPanel Orientation="Horizontal" Grid.Row="5" Grid.Column="1" Margin="10" Grid.ColumnSpan="2" Visibility="Collapsed">
                <Label Content="五楼齐套出库工作模式：" Margin="10"/>
                <RadioButton Content="人工模式" x:Name="rbtnManual5"  Margin="10"/>
                <RadioButton Content="自动模式[机械手]" x:Name="rbtnAuto5"  Margin="10"/>

            </StackPanel>
            <Button Content="保存" Grid.Row="6" Grid.Column="1" Width="150" Margin="10" HorizontalAlignment="Left" 
                            x:Name="btnWorkMode" Click="BtnWorkMode_Click"/>

        </Grid>


    </Grid>
</ad:DocumentContent>
