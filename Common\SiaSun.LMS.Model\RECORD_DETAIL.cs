﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// RECORD_DETAIL 
	/// </summary>
    [Serializable]
    [DataContract]
	public class RECORD_DETAIL
	{
		public RECORD_DETAIL()
		{
			
		}
		
		private int _record_detail_id;
		private int _record_list_id;
		private string _box_barcode;
		private string _goods_barcode;
		private string _record_detail_remark;
		
		///<sumary>
		/// 记录明细编号
        ///</sumary>
        [DataMember]
		public int RECORD_DETAIL_ID
		{
			get{return _record_detail_id;}
			set{_record_detail_id = value;}
		}
		///<sumary>
		/// 记录列表编号
        ///</sumary>
        [DataMember]
		public int RECORD_LIST_ID
		{
			get{return _record_list_id;}
			set{_record_list_id = value;}
		}
		///<sumary>
		/// 箱条码
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 设备条码
        ///</sumary>
        [DataMember]
		public string GOODS_BARCODE
		{
			get{return _goods_barcode;}
			set{_goods_barcode = value;}
		}
		///<sumary>
		/// 记录明细备注
        ///</sumary>
        [DataMember]
		public string RECORD_DETAIL_REMARK
		{
			get{return _record_detail_remark;}
			set{_record_detail_remark = value;}
		}
	}
}
