﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// CURRENT_APPLY.xaml 的交互逻辑
    /// tzyg add 2017-03-22
    /// </summary>
    public partial class CURRENT_APPLY : AvalonDock.DocumentContent
    {
        public CURRENT_APPLY()
        {
            InitializeComponent();

            this.ucQueryControl.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(QueryButtonAction);
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            InitQueryControl();
            ApplyListBind();
        }

        /// <summary>
        /// 点击“查询”按钮的处理方法
        /// </summary>
        private void QueryButtonAction(string strQueryWhere)
        {
            try
            {
                this.ucApplyDataGrid.U_AppendWhere = strQueryWhere;
                this.ucApplyDataGrid.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            try
            {
                //this.ucQueryControl.U_AddButtonVisable = true;
                this.ucQueryControl.U_XmlTableName = "IO_CONTROL_APPLY";
                this.ucQueryControl.U_WindowName = this.GetType().Name;
                this.ucQueryControl.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 绑定申请列表数据
        /// </summary>
        private void ApplyListBind()
        {
            try
            {
                this.ucApplyDataGrid.U_WindowName = this.GetType().Name;
                this.ucApplyDataGrid.U_TableName = "IO_CONTROL_APPLY";
                this.ucApplyDataGrid.U_OrderField = "CONTROL_APPLY_ID desc";

                this.ucApplyDataGrid.U_AllowChecked = true;
                this.ucApplyDataGrid.U_AllowOperatData = true;

                this.ucApplyDataGrid.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

    }
}
