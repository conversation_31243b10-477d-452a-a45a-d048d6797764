﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_RELATION_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_RELATION_LIST" type="SiaSun.LMS.Model.SYS_RELATION_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_RELATION_LIST">
			<result property="RELATION_LIST_ID" column="relation_list_id" />
			<result property="RELATION_ID" column="relation_id" />
			<result property="RELATION_LIST_FLAG" column="relation_list_flag" />
			<result property="RELATION_ID1" column="relation_id1" />
			<result property="RELATION_ID2" column="relation_id2" />
			<result property="RELATION_LIST_REMARK" column="relation_list_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_RELATION_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  relation_list_id,
				  relation_id,
				  relation_list_flag,
				  relation_id1,
				  relation_id2,
				  relation_list_remark
			From SYS_RELATION_LIST
		</select>
		
		<select id="SYS_RELATION_LIST_SELECT_BY_ID" parameterClass="int" extends = "SYS_RELATION_LIST_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					relation_list_id=#RELATION_LIST_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="SYS_RELATION_LIST_SELECT_BY_RELATION_ID1" parameterClass="Hashtable" extends = "SYS_RELATION_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          relation_id=#RELATION_ID# AND relation_id1=#RELATION_ID1#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="SYS_RELATION_LIST_SELECT_BY_RELATION_ID_RELATION_ID1_RELATION_ID2" parameterClass="Hashtable" extends = "SYS_RELATION_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          relation_id=#RELATION_ID# AND relation_id1=#RELATION_ID1# and relation_id2=#RELATION_ID2#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="SYS_RELATION_LIST_INSERT" parameterClass="SYS_RELATION_LIST">
      Insert Into SYS_RELATION_LIST (
      relation_list_id,
      relation_id,
      relation_list_flag,
      relation_id1,
      relation_id2,
      relation_list_remark
      )Values(
      #RELATION_LIST_ID#,
      #RELATION_ID#,
      #RELATION_LIST_FLAG#,
      #RELATION_ID1#,
      #RELATION_ID2#,
      #RELATION_LIST_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="RELATION_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_RELATION_LIST_UPDATE" parameterClass="SYS_RELATION_LIST">
      Update SYS_RELATION_LIST Set
      relation_list_id=#RELATION_LIST_ID#,
      relation_id=#RELATION_ID#,
      relation_list_flag=#RELATION_LIST_FLAG#,
      relation_id1=#RELATION_ID1#,
      relation_id2=#RELATION_ID2#,
      relation_list_remark=#RELATION_LIST_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					relation_list_id=#RELATION_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_RELATION_LIST_DELETE" parameterClass="int">
			Delete From SYS_RELATION_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					relation_list_id=#RELATION_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>