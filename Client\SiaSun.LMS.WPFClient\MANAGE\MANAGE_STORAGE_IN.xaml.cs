﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_STORAGE_IN : AvalonDock.DocumentContent
    {
        int intPlanID = 0;

        Dictionary<string, bool> dicStoageLoad = new Dictionary<string, bool>();

        Model.PLAN_MAIN mPLAN_MAIN = null;

        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        string xmlTableName = string.Empty;

        DataTable tableManageList = null;

        Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM = null;

        public MANAGE_STORAGE_IN(int PLAN_ID, string MANAGE_TYPE_CODE,string xmlTableName):this( PLAN_ID, MANAGE_TYPE_CODE)
        {
            this.xmlTableName = xmlTableName;
        }

        public MANAGE_STORAGE_IN(int PLAN_ID, string MANAGE_TYPE_CODE)
        {
            InitializeComponent();
            
            this.intPlanID = PLAN_ID;

            

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            this.ucManageIn.U_CheckedDataGridRows += new UC.ucManageInInput.U_CheckedDataGridRowsHandler
                (() =>
                 {
                     try
                     {
                         DataRowView[] arRow = this.ucManageIn.U_CheckedDataRowViews;

                         if (arRow.Length == 0)
                         {
                             MainApp._MessageDialog.Show("Manage_CheckAssemblyGoodsCount", null);
                             return;
                         }

                         if (this.ucManagePosition.U_AllowShowStockBarcode && this.ucManagePosition.U_STOCK_BARCODE == string.Empty)
                         {
                             MainApp._MessageDialog.Show("Manage_InPutStockBarcode", null);
                             return;
                         }

                         if (!this.dicStoageLoad.ContainsKey(this.ucManagePosition.U_STOCK_BARCODE.TrimEnd()))
                         {
                             this.StorageListBind();
                         }

                         //if (this.gridStorageList.U_DataSource == null || this.gridStorageList.U_DataSource.Rows.Count == 0)
                         //{
                         //    MainApp._MessageDialog.Show("空托盘不能进行补盘", null);
                         //    return;
                         //}

                         List<DataRow> listRowManageList = new List<DataRow>();
                         
                         foreach (DataRowView rowView in arRow)
                         {
                             int intPlanListID = rowView.Row.Table.Columns.Contains("PLAN_LIST_ID") ? Convert.ToInt32(rowView["PLAN_LIST_ID"]) : 0;

                             if (rowView.Row.Table.Columns.Contains("STOCK_BARCODE")&& rowView["STOCK_BARCODE"].ToString() == this.ucManagePosition.U_STOCK_BARCODE.TrimEnd())
                                 return;

                             DataRow rowManageList;

                             if( tableManageList==null)
                             tableManageList = MainApp._I_BaseService.GetList(string.Format("SELECT * FROM V_MANAGE_LIST WHERE MANAGE_ID={0}", -1));

                             //using ( tableManageList = MainApp._I_BaseService.GetList(string.Format("SELECT * FROM V_MANAGE_LIST WHERE MANAGE_ID={0}", -1)))
                             //{                                
                                 new CustomerDescriptions().SetDefaultValue(tableManageList, this.GetType().Name, "V_MANAGE_LIST");
                                 
                                 rowManageList = tableManageList.NewRow();

                                 Dictionary<string, object> dicDefault = new Dictionary<string, object>();
                                 dicDefault.Add("MANAGE_LIST_QUANTITY", 1);
                                 dicDefault.Add("PLAN_LIST_ID", intPlanListID);
                                 new SiaSun.LMS.Common.CloneObjectValues().CloneDataRowValue(rowView.Row, rowManageList, dicDefault);

                                 //rowManageList["GOODS_ID"] = rowView.Row["GOODS_ID"];
                                 //rowManageList["GOODS_CODE"] = rowView.Row["GOODS_CODE"];
                                 //rowManageList["GOODS_NAME"] = rowView.Row["GOODS_NAME"];
                                 //rowManageList["GOODS_TYPE_ID"] = rowView.Row["GOODS_TYPE_ID"];
                                 //rowManageList["GOODS_UNITS"] = rowView.Row["GOODS_UNITS"];
                             
                                 //rowManageList["GOODS_PROPERTY1"] = rowView.Row["GOODS_CONST_PROPERTY1"];
                                 //rowManageList["GOODS_PROPERTY2"] = rowView.Row["GOODS_CONST_PROPERTY2"];
                                 //rowManageList["GOODS_PROPERTY3"] = rowView.Row["GOODS_CONST_PROPERTY3"];
                                 //rowManageList["GOODS_PROPERTY4"] = rowView.Row["GOODS_CONST_PROPERTY4"];
                                 //rowManageList["GOODS_PROPERTY5"] = rowView.Row["GOODS_CONST_PROPERTY5"];
                                 //rowManageList["GOODS_PROPERTY6"] = rowView.Row["GOODS_CONST_PROPERTY6"];
                                 //rowManageList["GOODS_PROPERTY7"] = rowView.Row["GOODS_CONST_PROPERTY7"];
                                 //rowManageList["GOODS_PROPERTY8"] = rowView.Row["GOODS_CONST_PROPERTY8"];

                                 //rowManageList["MANAGE_LIST_QUANTITY"] = 1;
                                 
                             if (this.ucManagePosition.U_AssembleResource == "StorageGoods")
                                 {
                                     tableManageList.PrimaryKey = new DataColumn[] { tableManageList.Columns["STORAGE_LIST_ID"] };
                                     if (!tableManageList.Rows.Contains(rowManageList["STORAGE_LIST_ID"]))
                                     {
                                         tableManageList.Rows.Add(rowManageList);

                                         listRowManageList.Add(rowManageList);
                                     }
                                 }
                                 if (this.ucManagePosition.U_AssembleResource == "Goods")
                                 {
                                     tableManageList.Rows.Add(rowManageList);

                                     listRowManageList.Add(rowManageList);
                                 }

                                 if (this.ucManagePosition.U_AssembleResource == "PlanGoods")
                                 {
                                     tableManageList.PrimaryKey = new DataColumn[] { tableManageList.Columns["PLAN_LIST_ID"] };
                                     if (!tableManageList.Rows.Contains(rowManageList["PLAN_LIST_ID"]))
                                     {
                                         tableManageList.Rows.Add(rowManageList);

                                         listRowManageList.Add(rowManageList);
                                     }
                                 }
                             //}
                         }


                         

                         this.gridStorageList.U_AddTabPageRows(listRowManageList.ToArray());

                         foreach (TabItem page in this.gridStorageList.tabSplitProperty.Items)
                         {
                             if (page.HasContent)
                             {
                                 
                                 using (DataTable tableSource = (page.Content as UC.ucCommonDataGrid).U_DataSource.Table)
                                 {
                                     tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                                     tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                                 }
                             }
                         }
                     }
                     catch (Exception ex)
                     {
                         MainApp._MessageDialog.ShowException(ex);
                     }
                 }

                );

        }

        void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;

            switch (e.Column.ColumnName)
            {
                case "STORAGE_LIST_QUANTITY":
                    bResult = (string.Empty != e.ProposedValue.ToString()) && Convert.ToInt32(e.ProposedValue) > 0;
                    sResult = "填写数量必须是大于0!";

                    if (bResult)
                    {
                        e.Row.RowError = null;
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.RowError = "该行数据有错误!";
                        e.Row.SetColumnError(e.Column, sResult);
                    }
                    break;
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                if (intPlanID > 0)
                {
                    mPLAN_MAIN = MainApp._I_PlanService.PlanGetModel(intPlanID);

                    if (mPLAN_MAIN == null)
                    {
                        MainApp._MessageDialog.Show("Plan.CheckPlanExists",new object[]{ intPlanID});
                    }              
                }

                mMANAGE_TYPE_PARAM =  this.InitManagePosition();

                if (mMANAGE_TYPE_PARAM == null)
                    return;

                this.ucManagePosition.U_StockBarcodeKeyDown += new UC.ucManagePosition.U_StockBarcodeKeyDownHandler
                    (() =>
                      {
                        this.StorageListBind();
                      }
                    );

                this.InitManageInControl();

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private Model.MANAGE_TYPE_PARAM InitManagePosition()
        {
            try
            {
                return this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
                return null;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void InitManageInControl()
        {
            this.ucManageIn.U_WindowName = this.GetType().ToString();
            this.ucManageIn.U_PLAN_ID = this.intPlanID;
            this.ucManageIn.U_PLAN_OUT = Enum.PLAN_INOUT.In;
            this.ucManageIn.U_ShowType =  this.ucManagePosition.U_AssembleResource;

            //是否显示摆放图片
            this.ucManageIn.U_IsShowGoodsPositionTemplate = true;
            
            this.ucManageIn.U_InitControl();

            this.ucManageIn.Data_Bind(string.Empty);
        }


        /// <summary>
        /// 库存明细绑定
        /// </summary>
        private void StorageListBind()
        {
            if( !dicStoageLoad.ContainsKey(this.ucManagePosition.U_STOCK_BARCODE.TrimEnd()))
            {
                dicStoageLoad.Clear();
                dicStoageLoad.Add(this.ucManagePosition.U_STOCK_BARCODE.TrimEnd(), true);
            }


            this.gridStorageList.U_WindowName = this.GetType().Name;
            this.gridStorageList.U_TableName = "V_STORAGE_LIST";
            this.gridStorageList.U_XmlTableName = this.xmlTableName== string.Empty?"V_STORAGE_LIST":this.xmlTableName;
            this.gridStorageList.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.gridStorageList.U_OrderField = "STORAGE_LIST_ID";
            this.gridStorageList.U_Where = string.Format("STOCK_BARCODE ='{0}' AND AREA_TYPE= 'XuNiKu'", this.ucManagePosition.U_STOCK_BARCODE);
            
            //this.gridStorageList.U_AllowOperatData = true;
            this.gridStorageList.U_AllowChecked = false;
            this.gridStorageList.U_AllowShowPage = false;

            this.gridStorageList.U_AllowAdd = System.Windows.Visibility.Collapsed;
            //this.gridStorageList.U_AllowEdit = System.Windows.Visibility.Collapsed;
            this.gridStorageList.U_AllowSave = System.Windows.Visibility.Collapsed; 

            //拆分列属性
            this.gridStorageList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridStorageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridStorageList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridStorageList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";

            this.gridStorageList.U_IsShowGoodsPositionTemplate = true;

            this.gridStorageList.U_InitControl();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnSave":
                        this.Confirm();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 确认
        /// </summary>
        private void Confirm()
        {
            string strResult = string.Empty;

            try
            {
                if (this.ucManagePosition.U_AllowShowStockBarcode && this.ucManagePosition.U_STOCK_BARCODE == string.Empty)
                {
                    MainApp._MessageDialog.Show("Manage_InPutStockBarcode", null);
                    return;
                }

                MainWindow.mainWin.Cursor = Cursors.Wait;

                this.gridStorageList.U_EndCurrentEdit();

                DataTable tableSource = this.gridStorageList.U_DataSource;

                //tzyg add 2017-02-28
                //foreach (DataRow dr in tableSource.Rows)
                //{
                //    if (Convert.ToDecimal(dr["MANAGE_LIST_QUANTITY"]) > 0)
                //       dr["MANAGE_LIST_QUANTITY"] = dr["STORAGE_LIST_QUANTITY"];
                //}

                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableSource, null);

                var list = from v in listMANAGE_LIST
                           where v.MANAGE_LIST_QUANTITY > 0
                           select v;

                listMANAGE_LIST = list.ToList<Model.MANAGE_LIST>();

                if (tableSource == null || tableSource.Rows.Count == 0 || listMANAGE_LIST.Count == 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Input);
                    return;
                }

                if (tableSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }

                if (!this.gridStorageList.U_Check_Split_Property("GOODS_PROPERTY", out strResult))
                {
                    MainApp._MessageDialog.ShowResult(false, strResult);
                    return;
                }

                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmAssembly, this.ucManagePosition.U_STOCK_BARCODE)== Sid.Windows.Controls.TaskDialogResult.Ok)
                {

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.PLAN_ID = this.intPlanID;
                    
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                    mMANAGE_MAIN.STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE;

                    mMANAGE_MAIN.CELL_MODEL = this.ucManagePosition.U_CELL_MODEL;

                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;

                    if (mMANAGE_TYPE.MANAGE_TYPE_CODE == "ManageInLocal")
                    {
                        mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                    }
                    else
                    {
                        mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;
                    }
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;

                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();

                    mMANAGE_MAIN.FULL_FLAG = this.ucManagePosition.U_OCCUPY_PERCENT;

                    mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                    bool bResult = false;

                    if (mMANAGE_MAIN.END_CELL_ID != 0)
                    {
                        //验证高低货位
                        Model.WH_CELL mWH_CELL_END = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", mMANAGE_MAIN.END_CELL_ID).RequestObject;
                        if (mWH_CELL_END == null)
                        {
                            MainApp._MessageDialog.ShowResult(false, "用户指定终点位置不合法");
                            return;
                        }
                        if (mMANAGE_MAIN.CELL_MODEL == "Height" && mWH_CELL_END.CELL_MODEL == "Height%")
                        {
                            MainApp._MessageDialog.ShowResult(false, "高尺寸物料在指定终点位置时请选择高货位");
                            return;
                        }
                        //验证库区
                        Model.WH_CELL mWH_CELL_START = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", mMANAGE_MAIN.START_CELL_ID).RequestObject;
                        if(mWH_CELL_START==null)
                        {
                            MainApp._MessageDialog.ShowResult(false, "起始位置位置不合法");
                            return;
                        }
                        if(mWH_CELL_START.WAREHOUSE_ID!=mWH_CELL_END.WAREHOUSE_ID)
                        {
                            MainApp._MessageDialog.ShowResult(false, "指定终点位置的库区不正确");
                            return;
                        }
                    }

                    //bResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                    //                                         "ManageCreate",
                    //                                          new object[] { mMANAGE_MAIN,
                    //                                                           listMANAGE_LIST, 
                    //                                                           true, 
                    //                                                           mMANAGE_TYPE_PARAM
                    //                                                           },
                    //                                           out strResult);

                    //tzyg add 2017-03-20
                    var manageListCheck = from v in listMANAGE_LIST select v.GOODS_ID;
                    if (manageListCheck.Distinct().Count() < listMANAGE_LIST.Count)
                    {
                        MainApp._MessageDialog.ShowResult(false, "组盘物料中存在重复值");
                        return;
                    }

                    bResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                             "ManageCreate",
                                                              new object[] { mMANAGE_MAIN,
                                                                               listMANAGE_LIST, 
                                                                               true, 
                                                                               this.ucManagePosition.U_CheckStockExistStorage,
                                                                               this.ucManagePosition.U_AutoCompleteTask,
                                                                               this.ucManagePosition.U_AutoDownloadControlTask, 
                                                                               },
                                                               out strResult);


                    //检验执行结果
                    if (bResult)
                    {
                        if( !this.rbRefresh.IsChecked.Value)
                            this.gridStorageList.U_Update();

                        this.Refresh();

                        this.tableManageList.Clear();
                    }

                    MainApp._MessageDialog.ShowResult(bResult, strResult);

                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void Refresh()
        {
            this.ucManagePosition.U_Refresh();
            this.ucManageIn.Data_Bind(string.Empty);
        }


    }
}
