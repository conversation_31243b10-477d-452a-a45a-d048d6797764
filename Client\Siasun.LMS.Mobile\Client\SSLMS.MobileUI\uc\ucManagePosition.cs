﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Reflection;
using SSLMS.Model;
using System.Text.RegularExpressions;

namespace SSLMS.MobileUI.uc
{
    public partial class ucManagePosition : UserControl
    {
        //判断是否加载完成
        bool boolIsLoaded = false;
        bool boolAllowChooseStartPosition = false;
        bool boolAllowChooseEndPosition = false;

        #region     ------定义代理和事件

        public delegate void U_StockBarcodeKeyDownHandler();

        public event U_StockBarcodeKeyDownHandler U_StockBarcodeKeyDown;

        public delegate void U_SaveButtonClickHandler();

        public event U_SaveButtonClickHandler U_SaveButtonClicked;

        /// <summary>
        /// 输送位置更改
        /// </summary>
        public delegate void U_StartPositionValueChangedHandler();

        //输送位置更改
        public event U_StartPositionValueChangedHandler U_StartPositionChanged;

        /// <summary>
        /// 输送位置更改
        /// </summary>
        public delegate void U_EndPositionValueChangedHandler();

        //输送位置更改
        public event U_EndPositionValueChangedHandler U_EndPositionChanged;

        /// <summary>
        /// 货位规格类型更改
        /// </summary>
        public delegate void U_CellModelValueChangedHandler();

        //货位规格类型更改
        public event U_CellModelValueChangedHandler U_CellModelChanged;


        //public ucCell ucCell = new ucCell();


        #endregion

        #region     ------可在数据库表MANAGE_TYPE_PARAM中设备的属性

        bool boolShowStockBarcode = true;

        /// <summary>
        /// 是否显示托盘条码
        /// </summary>
        public bool U_AllowShowStockBarcode
        {
            get { return boolShowStockBarcode; }
            set
            {
                boolShowStockBarcode = value;
                this.panelStockBarcode.Visible = this.boolShowStockBarcode ? true : false;
            }
        }


        bool boolAllowShowCellModel = true;

        /// <summary>
        /// 是否显示货位规格
        /// </summary>
        public bool U_AllowShowCellModel
        {
            get { return boolAllowShowCellModel; }
            set { boolAllowShowCellModel = value; this.panelCellModel.Visible = this.boolAllowShowCellModel ? true : false; }
        }


        bool boolShowStartPostion = true;

        /// <summary>
        /// 是否显示起始位置
        /// </summary>
        public bool U_AllowShowStartPosition
        {
            get { return boolShowStartPostion; }
            set
            {
                boolShowStartPostion = value;
                this.panelStartPosition.Visible = value ? true : false;
            }
        }

        bool boolShowEndPosition = true;

        /// <summary>
        /// 是否显示终止位置
        /// </summary>
        public bool U_AllowShowEndPosition
        {
            get { return boolShowEndPosition; }
            set
            {
                boolShowEndPosition = value;
                this.panelEndPosition.Visible = value ? true : false;
            }
        }

        string strAssembleResource = "1";

        public string U_AssembleResource
        {
            get { return this.strAssembleResource; }
            set { this.strAssembleResource = value; }
        }


        string strStartCellType = string.Empty;

        public string U_StartCellType
        {
            get { return this.strStartCellType; }
            set { this.strStartCellType = value; }
        }


        string strStartCellInOut = string.Empty;

        public string U_StartCellInOut
        {
            get { return this.strStartCellInOut; }
            set { this.strStartCellInOut = value; }
        }

        string strEndCellType = string.Empty;

        public string U_EndCellType
        {
            get { return this.strEndCellType; }
            set { this.strEndCellType = value; }
        }


        string strEndCellInOut = string.Empty;

        public string U_EndCellInOut
        {
            get { return this.strEndCellInOut; }
            set { this.strEndCellInOut = value; }
        }


        bool boolCheckStockExistStorage = false;

        public bool U_CheckStockExistStorage
        {
            get { return this.boolCheckStockExistStorage; }
            set { this.boolCheckStockExistStorage = value; }
        }


        bool boolAutoDownloadControlTask = false;


        public bool U_AutoDownloadControlTask
        {
            get { return this.boolAutoDownloadControlTask; }
            set { this.boolAutoDownloadControlTask = value; }
        }


        bool boolAutoCompleteTask = false;

        public bool U_AutoCompleteTask
        {
            get { return this.boolAutoCompleteTask; }
            set { this.boolAutoCompleteTask = value; }
        }

        bool boolAllowAutoStartPostion = false;

        /// <summary>
        /// 是否允许自动选择起始位置
        /// </summary>
        public bool U_AllowAutoStartPostion
        {
            get { return boolAllowAutoStartPostion; }
            set { boolAllowAutoStartPostion = value; }
        }

        bool boolAllowAutoEndPostion = false;

        /// <summary>
        /// 是否允许自动选择终止位置
        /// </summary>
        public bool U_AllowAutoEndPostion
        {
            get { return boolAllowAutoEndPostion; }
            set { boolAllowAutoEndPostion = value; }
        }

        int intStartPositionDefault = 0;
        /// <summary>
        ///
        /// </summary>
        public int U_StartPositionDefault
        {
            get { return this.intStartPositionDefault; }
            set
            {
                this.cmbStartPosition.Enabled = !(Convert.ToInt32(value) > 0);
                this.intStartPositionDefault = value;
            }
        }

        int intEndPositionDefault = 0;

        /// <summary>
        /// 设置目标位置是否可用
        /// </summary>
        public int U_EndPositionDefault
        {
            get { return this.intEndPositionDefault; }
            set
            {
                this.cmbEndPosition.Enabled = !(Convert.ToInt32(value) > 0);
                this.intEndPositionDefault = value;
            }
        }


        string strCellModelDefault = string.Empty;
        /// <summary>
        /// 设置货位规格是否可用
        /// </summary>
        public string U_CellModelDefault
        {
            get { return strCellModelDefault; }
            set
            {
                if (!String.IsNullOrEmpty(value))
                {
                    this.cmbCellModel.Enabled = false;
                    this.strCellModelDefault = value;
                }
            }
        }


        string strWarehouseType = string.Empty;
        /// <summary>
        /// 加载货位选择时的仓库类型
        /// </summary>
        public string U_WarehouseType
        {
            get { return strWarehouseType; }
            set { strWarehouseType = value; }
        }

        #endregion




        /// <summary>
        /// 货位规格
        /// </summary>
        public string U_CELL_MODEL
        {
            get { return this.cmbCellModel.SelectedValue == null ? string.Empty : this.cmbCellModel.SelectedValue.ToString(); }
            set { if (value != null)this.cmbCellModel.SelectedValue = value; }
        }


        /// <summary>
        /// 起始输送位置编号
        /// </summary>
        public int U_START_POSITION_ID
        {
            get { return cmbStartPosition.SelectedValue == null ? 0 : Convert.ToInt32(this.cmbStartPosition.SelectedValue); }
            set { this.cmbStartPosition.SelectedValue = value; }
        }

        /// <summary>
        /// 终止输送位置编号
        /// </summary>
        public int U_END_POSITION_ID
        {
            get { return cmbEndPosition.SelectedValue == null ? 0 : Convert.ToInt32(this.cmbEndPosition.SelectedValue); }
            set { this.cmbEndPosition.SelectedValue = value; }
        }

        public string U_STOCK_BARCODE
        {
            get { return this.txtStockBarcode.Text.TrimEnd(); }
            set { this.txtStockBarcode.Text = value; }
        }


        public ucManagePosition()
        {
            InitializeComponent();

            this.cmbCellModel.SelectedValueChanged += new EventHandler(cmbCellModel_SelectedValueChanged);

            this.cmbStartPosition.SelectedValueChanged+=new EventHandler(cmbStartPosition_SelectedValueChanged);

            this.cmbEndPosition.SelectedValueChanged +=new EventHandler(cmbEndPosition_SelectedValueChanged);
        }


        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl(int MANAGE_TYPE_ID)
        {
            IList<MANAGE_TYPE_PARAM> lsMANAGE_TYPE_PARAM = Program._I_PDAService.ManageTypeParamGetList(MANAGE_TYPE_ID);

            foreach (MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM in lsMANAGE_TYPE_PARAM)
            {
                if (string.IsNullOrEmpty(mMANAGE_TYPE_PARAM.PARAM_VALUE))
                {
                    continue;
                }

                PropertyInfo[] propertys_info = this.GetType().GetProperties();

                foreach (PropertyInfo pi in propertys_info)
                {
                    if (pi.Name.Equals(mMANAGE_TYPE_PARAM.PARAM_CODE, StringComparison.CurrentCultureIgnoreCase))
                    {
                        pi.SetValue(this, Convert.ChangeType(mMANAGE_TYPE_PARAM.PARAM_VALUE, pi.PropertyType,null), null);
                    }
                }
            }
            this.boolIsLoaded = false;

            //货位规格
            this.CELL_MODEL_BIND();

            this.START_POSITION_BIND();

            this.END_POSITION_BIND();

            //设置默认值
            this.LoadDefaultValue();
            //设置是否显示选择位置按钮
            this.SetEnableChoosePositon();
        }


        /// <summary>
        /// 设置是否允许设置显示选择位置按钮
        /// </summary>
        private void SetEnableChoosePositon()
        {
            try
            {

                this.boolAllowChooseStartPosition = this.U_StartCellType == SSLMS.Model.CELL_TYPE.Cell.ToString();
                this.btnStartPosition.Visible = this.boolAllowChooseStartPosition ? true : false;
                this.cmbStartPosition.Enabled = !this.boolAllowChooseStartPosition;


                this.boolAllowChooseEndPosition = this.U_EndCellType == CELL_TYPE.Cell.ToString();
                this.btnEndPosition.Visible = this.boolAllowChooseEndPosition ? true : false;
                this.cmbEndPosition.Enabled = !this.boolAllowChooseEndPosition;


            }
            catch (Exception ex)
            {
               
            }
        }




        #region     ------根据加载规则，加载起始库区和目标库区

        /// <summary>
        /// 清除数据绑定
        /// </summary>
        private void DataBindClear()
        {
            this.cmbStartPosition.DataSource = null;

            this.cmbEndPosition.DataSource = null;
        }


        /// <summary>
        /// 加载默认的库区
        /// </summary>
        private void LoadDefaultValue()
        {
            this.boolIsLoaded = false;

            //CellModel
            if (this.cmbCellModel.Items.Count>0 && this.cmbCellModel.SelectedValue == null)
            {
                if (!string.IsNullOrEmpty(this.strCellModelDefault))
                    this.cmbCellModel.SelectedValue = strCellModelDefault;
            }

            if (this.cmbStartPosition.Items.Count > 0 && this.cmbStartPosition.SelectedValue == null)
            {
                if (this.intStartPositionDefault != 0)
                    this.cmbStartPosition.SelectedValue = this.intStartPositionDefault;
            }


            if (this.cmbEndPosition.Items.Count>0 && this.cmbEndPosition.SelectedValue == null)
            {
                if (this.intEndPositionDefault != 0)
                    this.cmbEndPosition.SelectedValue = this.intEndPositionDefault;
            }


            this.boolIsLoaded = true;
        }

        /// <summary>
        /// 加载默认的库区
        /// </summary>
        public void U_Refresh()
        {
            this.boolIsLoaded = false;

            this.txtStockBarcode.Text = string.Empty;

            this.cmbCellModel.SelectedValue = string.Empty;

            this.cmbStartPosition.SelectedValue = -1;

            this.cmbEndPosition.SelectedValue = -1;

            this.boolIsLoaded = true;
        }

        #endregion


        #region     ------CELL_MODEL绑定及变更

        /// <summary>
        /// 货位规格绑定
        /// </summary>
        private void CELL_MODEL_BIND()
        {
            try
            {

                DataTable dtCellModel = Program._I_PDAService.GetList(string.Format(" select * from sys_item_list where  ITEM_ID IN(SELECT ITEM_ID FROM SYS_ITEM WHERE ITEM_CODE= '{0}') ORDER BY ITEM_LIST_ORDER", "CELL_MODEL"));

                this.cmbCellModel.DisplayMember = "ITEM_LIST_NAME";
                this.cmbCellModel.ValueMember = "ITEM_LIST_CODE";
                this.cmbCellModel.DataSource = dtCellModel;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        /// <summary>
        /// CELL_MODEL变更，更改其他流程属性
        /// </summary>
        private void cmbCellModel_SelectedValueChanged(object sender, EventArgs e)
        {
            //如果没有加载完成，不处理
            if (this.boolIsLoaded)
            {
                //默认值
                this.LoadDefaultValue();

                //激活注册事件
                if (this.U_CellModelChanged != null)
                {
                    this.U_CellModelChanged();
                }
            }
        }

        #endregion



        #region     ------START_POSITION绑定及变更

        /// <summary>
        /// 根据流程加载起始位置
        /// </summary>
        private void START_POSITION_BIND()
        {

            this.cmbStartPosition.DisplayMember = "CELL_CODE";
            this.cmbStartPosition.ValueMember = "CELL_ID";


            if (this.U_StartCellType != CELL_TYPE.Cell.ToString())
            {
                this.cmbStartPosition.DataSource = Program._I_PDAService.GetList(
                                                                                  string.Format("select * from wh_cell where 1=1 {0} {1}",
                                                                                  this.U_StartCellType == string.Empty ? string.Empty : string.Format(" and cell_type like '%{0}%' ", this.U_StartCellType),
                                                                                  this.U_StartCellInOut == string.Empty ? string.Empty : string.Format(" and cell_inout like '%{0}%' ", this.U_StartCellInOut))
                                                                                  ).DefaultView;
            }
            //设置起始位置
            this.SetStartPositionDefault();
            //判断是否显示选择位置的按钮
            btnStartPosition.Visible = (this.boolAllowChooseStartPosition && cmbStartPosition.Items.Count > 0) ? true : false;
        }

        /// <summary>
        /// 设置默认起始位置值
        /// </summary>
        private void SetStartPositionDefault()
        {
            if (this.cmbStartPosition.Items.Count>0 && this.cmbStartPosition.SelectedValue == null)
            {
                //判断是否存在默认值
                if (this.intStartPositionDefault > 0)
                {
                    this.cmbStartPosition.SelectedValue = this.intStartPositionDefault;
                }

            }
        }

        /// <summary>
        /// 起始位置变更
        /// </summary>
        private void cmbStartPosition_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.boolIsLoaded)
            {
                //判断是否注册相应的事件
                if (this.U_StartPositionChanged != null)
                {
                    this.U_StartPositionChanged();
                }
            }
        }

        #endregion



        #region     ------END_POSITION绑定及变更

        /// <summary>
        /// 根据流程加载目标位置
        /// </summary>
        private void END_POSITION_BIND()
        {



            this.cmbEndPosition.DisplayMember = "CELL_NAME";
            this.cmbEndPosition.ValueMember = "CELL_ID";

            if (this.U_EndCellType != CELL_TYPE.Cell.ToString())
            {
                this.cmbEndPosition.DataSource = Program._I_PDAService.GetList(
                                                                                  string.Format("select * from wh_cell where 1=1 {0} {1}",
                                                                                  this.U_EndCellType == string.Empty ? string.Empty : string.Format(" and cell_type like '%{0}%' ", this.U_EndCellType),
                                                                                  this.U_EndCellInOut == string.Empty ? string.Empty : string.Format(" and cell_inout like '%{0}%' ", this.U_EndCellInOut))
                                                                                  ).DefaultView;
            }
            //设置目标位置
            this.SetEndPositionDefault();

            //判断是否显示选择位置的按钮
            btnEndPosition.Visible = (this.boolAllowChooseEndPosition && cmbEndPosition.Items.Count > 0) ?true: false;
        }

        /// <summary>
        /// 设置默认目标位置值
        /// </summary>
        private void SetEndPositionDefault()
        {
            if (this.cmbEndPosition.Items.Count>0 && this.cmbEndPosition.SelectedValue == null)
            {
                //判断是否存在默认值
                if (this.intEndPositionDefault > 0)
                {
                    this.cmbEndPosition.SelectedValue = this.intEndPositionDefault;
                }

            }
        }

        /// <summary>
        /// 目标位置变更
        /// </summary>
        private void cmbEndPosition_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.boolIsLoaded)
            {
                //判断是否注册相应的事件
                if (this.U_EndPositionChanged != null)
                {
                    this.U_EndPositionChanged();
                }
            }
        }

        #endregion



        #region     ------选择起始位置、目标位置

        /// <summary>
        /// 按钮事件
        /// </summary>
        //private void WrapPanel_Click(object sender, RoutedEventArgs e)
        //{
        //    Button btn = e.OriginalSource as Button;
        //    if (btn != null)
        //    {
        //        switch (btn.Name)
        //        {
        //            case "btnStartPosition":
        //                this.SelectStartPosition();
        //                break;
        //            case "btnEndPosition":
        //                this.SelectEndPosition();
        //                break;
        //        }
        //    }
        //}

        private void CellSelect_Click(object sender, EventArgs e)
        {
            Button btn = sender as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnStartPosition":
                        this.SelectStartPosition();
                        break;
                    case "btnEndPosition":
                        this.SelectEndPosition();
                        break;
                }
            }
        }

        /// <summary>
        /// 选择起始位置
        /// </summary>
        private void SelectStartPosition()
        {
            //判断可选择货位
            if (cmbStartPosition.Items.Count > 0)
            {
                try
                {
                    WH_CELL mWH_CELL_Selected = null;

                    FrmCellSelect frmCellSelect = new FrmCellSelect();
                    if (frmCellSelect.ShowDialog() == DialogResult.OK)
                    {
                        WH_CELL mWH_CELL = frmCellSelect.mWH_CELL;
                        if (mWH_CELL != null)
                        {
                            cmbStartPosition.SelectedValue = mWH_CELL.CELL_ID;

                            //判断是否正确
                            if (cmbStartPosition.SelectedValue == null)
                            {                             
                                return;
                            }
                            else
                            {
                                //触发事件
                                if (this.U_StartPositionChanged != null)
                                {
                                    this.U_StartPositionChanged();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }

        ///// <summary>
        ///// 选择目标位置
        ///// </summary>
        private void SelectEndPosition()
        {
            try
            {

                FrmCellSelect frmCellSelect = new FrmCellSelect();
                if (frmCellSelect.ShowDialog() == DialogResult.OK)
                {
                    WH_CELL mWH_CELL = frmCellSelect.mWH_CELL;
                    if (mWH_CELL != null)
                    {

                        this.cmbEndPosition.DataSource = Program._I_PDAService.GetList(string.Format("select * from wh_cell where cell_id={0}", mWH_CELL.CELL_ID));
                                                              
                        cmbEndPosition.SelectedValue = mWH_CELL.CELL_ID;

                        //判断是否正确
                        if (cmbEndPosition.SelectedValue == null)
                        {
                            
                            return;
                        }
                        else
                        {
                            //触发事件
                            if (this.U_EndPositionChanged != null)
                            {
                                this.U_EndPositionChanged();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }

        #endregion


        #region     -------设置各个属性值

        /// <summary>
        /// 设置货位规格值
        /// </summary>
        public void U_SetCellModel(string CELL_MODEL)
        {
            this.cmbCellModel.SelectedValue = CELL_MODEL;
        }


        #endregion


        #region     ------校验填写的值是否合法

        //校验信息是否正确
        public bool U_CHECK_WAREHOUSE()
        {
            //货位规格
            if (this.boolAllowShowCellModel && this.cmbCellModel.SelectedValue == null)
            {
                MessageBox.Show("请选择规格");
                return false;
            }

            //起始位置
            if (boolShowStartPostion && !boolAllowAutoStartPostion && cmbStartPosition.SelectedValue == null)
            {

                MessageBox.Show("请选择起始位置");
                return false;
            }

            //终止位置
            if (boolShowEndPosition && !boolAllowAutoEndPostion && cmbEndPosition.SelectedValue == null)
            {
                MessageBox.Show("请选择终止位置");
                return false;
            }

            if (boolShowStockBarcode)
            {
                Match mCode = Regex.Match(this.txtStockBarcode.Text, "^[0-9]{6}$");

                if (!mCode.Success)

                    return false;
            }

            return true;
        }

        /// <summary>
        /// 检查起始位置是否合法,检查货位编码是否等于设备编码
        /// </summary>
        public bool U_CheckStartPosition(string START_POSITION_CODE)
        {
            bool boolResult = false;
            using (DataTable tableStartPosition = (this.cmbStartPosition.DataSource as DataView).Table)
            {
                if (tableStartPosition != null)
                {
                    boolResult = tableStartPosition.Select(string.Format("DEVICE_CODE='{0}'", START_POSITION_CODE)).Length > 0;
                }
            }
            return boolResult;
        }

        /// <summary>
        /// 检查目标位置是否合法,检查货位编码是否等于设备编码
        /// </summary>
        public bool U_CheckEndPosition(string END_POSITION_CODE)
        {
            bool boolResult = false;
            using (DataTable tableEndPosition = (this.cmbEndPosition.DataSource as DataView).Table)
            {
                if (tableEndPosition != null)
                {
                    boolResult = tableEndPosition.Select(string.Format("DEVICE_CODE='{0}'", END_POSITION_CODE)).Length > 0;
                }
            }
            return boolResult;
        }

        #endregion


        /// <summary>
        /// 刷新控件显示
        /// </summary>
        public void U_Update()
        {
            this.LoadDefaultValue();
        }

        /// <summary>
        /// 清除操作
        /// </summary>
        public void U_Clear()
        {
            this.boolIsLoaded = false;
            this.cmbCellModel.DataSource = null;
            this.DataBindClear();
            this.boolIsLoaded = true;
        }

        private void StockBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (this.U_StockBarcodeKeyDown != null && e.KeyCode == Keys.Enter)
            {
                this.U_StockBarcodeKeyDown();
            }
        }

        private void btSave_Click(object sender, EventArgs e)
        {
            if (this.U_SaveButtonClicked != null)
            {
                this.U_SaveButtonClicked();
            }
        }

        private void btRefresh_Click(object sender, EventArgs e)
        {
            this.U_Refresh();
        }





    }
}
