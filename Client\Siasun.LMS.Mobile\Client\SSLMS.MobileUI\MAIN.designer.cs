﻿namespace SSLMS.MobileUI
{
    partial class MAIN
    {
        /// <summary>
        /// 必需的设计器变量。
        /// </summary>
        private System.ComponentModel.IContainer components = null;
        private System.Windows.Forms.MainMenu mainMenu1;

        /// <summary>
        /// 清理所有正在使用的资源。
        /// </summary>
        /// <param name="disposing">如果应释放托管资源，为 true；否则为 false。</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows 窗体设计器生成的代码

        /// <summary>
        /// 设计器支持所需的方法 - 不要
        /// 使用代码编辑器修改此方法的内容。
        /// </summary>
        private void InitializeComponent()
        {
            this.mainMenu1 = new System.Windows.Forms.MainMenu();
            this.menuItem1 = new System.Windows.Forms.MenuItem();
            this.miExit = new System.Windows.Forms.MenuItem();
            this.menuItem8 = new System.Windows.Forms.MenuItem();
            this.miWorkList = new System.Windows.Forms.MenuItem();
            this.panel1 = new System.Windows.Forms.Panel();
            this.menuItem2 = new System.Windows.Forms.MenuItem();
            this.SuspendLayout();
            // 
            // mainMenu1
            // 
            this.mainMenu1.MenuItems.Add(this.menuItem1);
            this.mainMenu1.MenuItems.Add(this.menuItem8);
            // 
            // menuItem1
            // 
            this.menuItem1.MenuItems.Add(this.miExit);
            this.menuItem1.Text = "系统";
            // 
            // miExit
            // 
            this.miExit.Text = "退出";
            this.miExit.Click += new System.EventHandler(this.menuItem_Click);
            // 
            // menuItem8
            // 
            this.menuItem8.MenuItems.Add(this.miWorkList);
            this.menuItem8.MenuItems.Add(this.menuItem2);
            this.menuItem8.Text = "计划管理";
            // 
            // miWorkList
            // 
            this.miWorkList.Text = "待办工作";
            this.miWorkList.Click += new System.EventHandler(this.menuItem_Click);
            // 
            // panel1
            // 
            this.panel1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.panel1.Location = new System.Drawing.Point(0, 0);
            this.panel1.Name = "panel1";
            this.panel1.Size = new System.Drawing.Size(287, 275);
            // 
            // menuItem2
            // 
            this.menuItem2.Text = "空托盘上架";
            this.menuItem2.Click += new System.EventHandler(this.menuItem_Click);
            // 
            // MAIN
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(96F, 96F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.ClientSize = new System.Drawing.Size(287, 275);
            this.Controls.Add(this.panel1);
            this.Menu = this.mainMenu1;
            this.Name = "MAIN";
            this.Text = "新松仓库管理系统[手持终端]";
            this.Load += new System.EventHandler(this.FrmMain_Load);
            this.Closed += new System.EventHandler(this.MAIN_Closed);
            this.KeyDown += new System.Windows.Forms.KeyEventHandler(this.MAIN_KeyDown);
            this.ResumeLayout(false);

        }

        #endregion

        private System.Windows.Forms.Panel panel1;
        private System.Windows.Forms.MenuItem menuItem1;
        private System.Windows.Forms.MenuItem miExit;
        private System.Windows.Forms.MenuItem menuItem8;
        private System.Windows.Forms.MenuItem miWorkList;
        private System.Windows.Forms.MenuItem menuItem2;
    }
}