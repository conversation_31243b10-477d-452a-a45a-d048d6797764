﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;

    /// <summary>
    /// STORAGE_LIST 
    /// </summary>
    [Serializable]
    public class STORAGE_LOCK
    {
        public STORAGE_LOCK()
        {

        }

        private int _storage_lock_id;
        private int _storage_list_id;
        private int _plan_list_id;
        private decimal _storage_lock_quantity;
        private string _storage_lock_flag;
        private string _detail_flag;
        private string _storage_lock_remark;

        /// <summary>
        /// 锁定编号
        /// </summary>
        public int STORAGE_LOCK_ID
        {
            get
            {
                return _storage_lock_id;
            }

            set
            {
                _storage_lock_id = value;
            }
        }

        /// <summary>
        /// 库存列表编号
        /// </summary>
        public int STORAGE_LIST_ID
        {
            get
            {
                return _storage_list_id;
            }

            set
            {
                _storage_list_id = value;
            }
        }

        /// <summary>
        /// 计划列表编号
        /// </summary>
        public int PLAN_LIST_ID
        {
            get
            {
                return _plan_list_id;
            }

            set
            {
                _plan_list_id = value;
            }
        }

        /// <summary>
        /// 库存锁定数量
        /// </summary>
        public decimal STORAGE_LOCK_QUANTITY
        {
            get
            {
                return _storage_lock_quantity;
            }

            set
            {
                _storage_lock_quantity = value;
            }
        }

        /// <summary>
        /// 标志
        /// </summary>
        public string STORAGE_LOCK_FLAG
        {
            get
            {
                return _storage_lock_flag;
            }

            set
            {
                _storage_lock_flag = value;
            }
        }

        /// <summary>
        /// 明细标志
        /// </summary>
        public string DETAIL_FLAG
        {
            get
            {
                return _detail_flag;
            }

            set
            {
                _detail_flag = value;
            }
        }

        /// <summary>
        /// 备注
        /// </summary>
        public string STORAGE_LOCK_REMARK
        {
            get
            {
                return _storage_lock_remark;
            }

            set
            {
                _storage_lock_remark = value;
            }
        }
    }
}