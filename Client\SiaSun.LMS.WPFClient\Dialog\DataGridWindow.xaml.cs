﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// DataGridWindow.xaml 的交互逻辑
    /// </summary>
    public partial class DataGridWindow : AvalonDock.DocumentContent
    {
        bool boolAllowEdit = true;
        string strTableName = string.Empty;
        string strOrderField = string.Empty;
        string strTotalColumn = string.Empty;
        string strWhere = string.Empty;

        string strSplitPropertyType = string.Empty;

        string strSplitPropertyKey = string.Empty;

        string strSplitPropertyColumn = string.Empty;

        bool bSplitColumnCombine= false;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="TabText">窗体显示名</param>
        /// <param name="TableName">数据库表名</param>
        /// <param name="QueryWhere">查询条件</param>
        /// <param name="AllowChecked">是否显示选择列</param>
        /// <param name="AllowEdit">是否允许编辑数据</param>
        /// <param name="TotalColumnName">计算数量总和的列名</param>
        public DataGridWindow(string TabText, 
                              string TableName, 
                              string QueryWhere, 
                              string OrderField, 
                              bool AllowEdit, 
                              string TotalColumnName)
        {
            InitializeComponent();

            //设置空间属性
            this.Title = TabText;
            this.boolAllowEdit = AllowEdit;
            this.strTableName = TableName;
            this.strOrderField = OrderField;
            this.strTotalColumn = TotalColumnName;
            this.strWhere = QueryWhere;

            this.ucQKQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQKQuery_U_Query);

        }


         public DataGridWindow(string TabText, 
                              string TableName, 
                              string QueryWhere, 
                              string OrderField, 
                              bool AllowEdit, 
                              string TotalColumnName,
                              string SplitPropertyType,
                              string SplitPropertyKey,
                              string SplitPropertyColumn,
                              bool SplitColumnCombine)
        {
            InitializeComponent();

            //设置空间属性
            this.Title = TabText;
            this.boolAllowEdit = AllowEdit;
            this.strTableName = TableName;
            this.strOrderField = OrderField;
            this.strTotalColumn = TotalColumnName;
            this.strWhere = QueryWhere;

            this.strSplitPropertyType = SplitPropertyType;

             this.strSplitPropertyKey = SplitPropertyKey;
             this.strSplitPropertyColumn = SplitPropertyColumn;
             this.bSplitColumnCombine = SplitColumnCombine;

            this.ucQKQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQKQuery_U_Query);

        }


         //XUTAO

                
                //XUTAO


        /// <summary>
        /// 初始化窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //初始化查询控件
            InitQueryControl();
            //加载数据
            LoadDataGrid();
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            try
            {
                this.ucQKQuery.U_WindowName = this.GetType().Name;
                this.ucQKQuery.U_XmlTableName = this.strTableName;
                this.ucQKQuery.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadDataGrid()
        {
            try
            {
                this.gridQuery.U_WindowName = this.GetType().Name;
                this.gridQuery.U_TableName = this.strTableName;
                this.gridQuery.U_Where = this.strWhere;
                this.gridQuery.U_OrderField = this.strOrderField;
                this.gridQuery.U_TotalColumnName = this.strTotalColumn;
                


                this.gridQuery.U_AllowOperatData = this.boolAllowEdit;
                this.gridQuery.U_AllowPage = true;
                this.gridQuery.U_AllowChecked= this.boolAllowEdit;


                gridQuery.U_SplitPropertyType = strSplitPropertyType;

                gridQuery.U_SplitPropertyKey = strSplitPropertyKey;

                gridQuery.U_SplitPropertyColumn = strSplitPropertyColumn;

                gridQuery.U_SplitColumnCombine = bSplitColumnCombine;

                this.gridQuery.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //查询数据
        void ucQKQuery_U_Query(string QueryWhere)
        {
            try
            {
                //查询
                this.strWhere = string.IsNullOrEmpty(strWhere) ? "1=1" : strWhere;
                this.gridQuery.U_Where = string.IsNullOrEmpty(QueryWhere) ? strWhere : (QueryWhere.Length == 0 ? string.Empty : QueryWhere + " AND " + strWhere);
                this.gridQuery.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
    }
}
