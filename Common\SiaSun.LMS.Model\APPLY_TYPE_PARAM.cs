﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     siasun
 *       日期：     2014/11/3
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// APPLY_TYPE_PARAM 
    /// </summary>
    [Serializable]
    [DataContract]
    public class APPLY_TYPE_PARAM
    {
        public APPLY_TYPE_PARAM()
        {
        }

        private bool bSendLedMessage;
        
        [DataMember]
        public bool U_SendLedMessage
        {
            get { return bSendLedMessage; }
            set { bSendLedMessage = value; }
        }

        private bool bCreateExceptionTask;

        [DataMember]
        public bool U_CreateExceptionTask
        {
            get { return bCreateExceptionTask; }
            set { bCreateExceptionTask = value; }
        }

        private bool bWriteHisData = true;
        [DataMember]
        public bool U_WriteHisData
        {
            get { return bWriteHisData; }
            set { bWriteHisData = value; }
        }

        private string strExceptionStation;
        [DataMember]
        public string U_ExceptionStation
        {
            get { return strExceptionStation; }
            set { strExceptionStation = value; } 
        }

        private string strNextStation;
        [DataMember]
        public string U_NextStation
        {
            get { return strNextStation; }
            set { strNextStation = value; }
        }
    }
}
