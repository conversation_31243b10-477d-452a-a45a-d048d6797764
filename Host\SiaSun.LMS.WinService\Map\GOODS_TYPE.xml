﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="GOODS_TYPE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="GOODS_TYPE" type="SiaSun.LMS.Model.GOODS_TYPE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="GOODS_TYPE">
			<result property="GOODS_TYPE_ID" column="goods_type_id" />
			<result property="GOODS_TYPE_CODE" column="goods_type_code" />
			<result property="GOODS_TYPE_NAME" column="goods_type_name" />
			<result property="GOODS_TYPE_REMARK" column="goods_type_remark" />
			<result property="GOODS_TYPE_ORDER" column="goods_type_order" />
			<result property="GOODS_TYPE_FLAG" column="goods_type_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="GOODS_TYPE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  goods_type_id,
				  goods_type_code,
				  goods_type_name,
				  goods_type_remark,
				  goods_type_order,
				  goods_type_flag
			From GOODS_TYPE
		</select>
		
		<select id="GOODS_TYPE_SELECT_BY_ID" parameterClass="int" extends = "GOODS_TYPE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_type_id=#GOODS_TYPE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="GOODS_TYPE_SELECT_BY_GOODS_TYPE_REMARK" parameterClass="string" extends = "GOODS_TYPE_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_type_remark=#GOODS_TYPE_REMARK#
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="GOODS_TYPE_INSERT" parameterClass="GOODS_TYPE">
      Insert Into GOODS_TYPE (
      goods_type_id,
      goods_type_code,
      goods_type_name,
      goods_type_remark,
      goods_type_order,
      goods_type_flag
      )Values(
      #GOODS_TYPE_ID#,
      #GOODS_TYPE_CODE#,
      #GOODS_TYPE_NAME#,
      #GOODS_TYPE_REMARK#,
      #GOODS_TYPE_ORDER#,
      #GOODS_TYPE_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="GOODS_TYPE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="GOODS_TYPE_UPDATE" parameterClass="GOODS_TYPE">
      Update GOODS_TYPE Set
      goods_type_id=#GOODS_TYPE_ID#,
      goods_type_code=#GOODS_TYPE_CODE#,
      goods_type_name=#GOODS_TYPE_NAME#,
      goods_type_remark=#GOODS_TYPE_REMARK#,
      goods_type_order=#GOODS_TYPE_ORDER#,
      goods_type_flag=#GOODS_TYPE_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					goods_type_id=#GOODS_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="GOODS_TYPE_DELETE" parameterClass="int">
			Delete From GOODS_TYPE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_type_id=#GOODS_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>