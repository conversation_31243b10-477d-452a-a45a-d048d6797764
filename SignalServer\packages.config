﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="log4net" version="2.0.12" targetFramework="net46" />
  <package id="MaterialDesignColors" version="1.2.7" targetFramework="net46" />
  <package id="MaterialDesignThemes" version="3.2.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.Cors" version="5.0.0" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR.Core" version="2.4.1" targetFramework="net46" />
  <package id="Microsoft.AspNet.SignalR.SelfHost" version="2.4.1" targetFramework="net46" />
  <package id="Microsoft.Owin" version="4.1.1" targetFramework="net46" />
  <package id="Microsoft.Owin.Cors" version="4.1.1" targetFramework="net46" />
  <package id="Microsoft.Owin.Diagnostics" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Owin.Host.HttpListener" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Owin.Hosting" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Owin.Security" version="2.1.0" targetFramework="net46" />
  <package id="Microsoft.Owin.SelfHost" version="2.1.0" targetFramework="net46" />
  <package id="Newtonsoft.Json" version="6.0.4" targetFramework="net46" />
  <package id="Owin" version="1.0" targetFramework="net46" />
</packages>