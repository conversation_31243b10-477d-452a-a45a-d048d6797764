﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.PICK.View.PickStationView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
        xmlns:cmd="SiaSun.LMS.WPFClient.MVVM"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:gridhelper="clr-namespace:SiaSun.LMS.WPFClient.PICK.Model"
        mc:Ignorable="d"
        Title="PickStatioView">

    <Grid>
        <Grid.Resources>
            <local:InformationBackgroundConverter x:Key="InformationHighlightBackgroundConverter"
                                                   DefaultBrush="AliceBlue" HighlightBrush="Yellow">
            </local:InformationBackgroundConverter>

            <local:VisiabilityConverter x:Key="VisibilityConverter"></local:VisiabilityConverter>
            <DataTemplate x:Key="BoxTypeADataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition MinHeight="100"></RowDefinition>
                        <RowDefinition MaxHeight="60"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                             gridhelper:GridHelper.ShowBorder="True"
                             gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Label Style="{x:Null}" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeBDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                            gridhelper:GridHelper.ShowBorder="True"
                            gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label Style="{x:Null}" Grid.Column="0" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Column="1" Background="{Binding PartBackGround[1]}" FontSize="20" 
                               Foreground="Red" Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeCDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                             gridhelper:GridHelper.ShowBorder="True"
                             gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label Style="{x:Null}" Grid.Column="0" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Column="1" Background="{Binding PartBackGround[1]}" FontSize="20" 
                               Foreground="Red"  Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Column="2" Background="{Binding PartBackGround[2]}" FontSize="20"
                               Foreground="Red" Content="3" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
            </DataTemplate>
            
            <DataTemplate x:Key="BoxTypeDDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                            gridhelper:GridHelper.ShowBorder="True"
                            gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle Grid.Row="0" Grid.Column="0" Fill="LightCyan"/>
                        <Rectangle Grid.Row="0" Grid.Column="1" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="0" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="1" Fill="LightCyan"/>-->
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="0" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="1" Background="{Binding PartBackGround[1]}" FontSize="20" 
                               Foreground="Red" Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="0" Background="{Binding PartBackGround[2]}" FontSize="20"
                               Foreground="Red" Content="3" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="1" Background="{Binding PartBackGround[3]}" FontSize="20"
                               Foreground="Red" Content="4" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeEDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                             gridhelper:GridHelper.ShowBorder="True"
                             gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="0" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="1" Background="{Binding PartBackGround[1]}" FontSize="20" 
                               Foreground="Red" Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="2" Background="{Binding PartBackGround[2]}" FontSize="20"
                               Foreground="Red" Content="3" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="0" Background="{Binding PartBackGround[3]}" FontSize="20"
                               Foreground="Red" Content="4" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="1" Background="{Binding PartBackGround[4]}" FontSize="20"
                               Foreground="Red" Content="5" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="2" Background="{Binding PartBackGround[5]}" FontSize="20"
                               Foreground="Red" Content="6" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
            </DataTemplate>
            
            <!--二期相反布局-->
            <DataTemplate x:Key="BoxTypeARDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition MinHeight="100"></RowDefinition>
                        <RowDefinition MaxHeight="60"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                             gridhelper:GridHelper.ShowBorder="True"
                             gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Label Style="{x:Null}" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeBRDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                            gridhelper:GridHelper.ShowBorder="True"
                            gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label Style="{x:Null}" Grid.Column="0" Background="{Binding PartBackGround[1]}" FontSize="20" 
                               Foreground="Red" Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Column="1" Background="{Binding PartBackGround[0]}" FontSize="20" 
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeCRDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                             gridhelper:GridHelper.ShowBorder="True"
                             gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Label Style="{x:Null}" Grid.Column="0" Background="{Binding PartBackGround[2]}" FontSize="20" 
                               Foreground="Red" Content="3" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Column="1" Background="{Binding PartBackGround[1]}" FontSize="20" 
                               Foreground="Red"  Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Column="2" Background="{Binding PartBackGround[0]}" FontSize="20"
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeDRDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                            gridhelper:GridHelper.ShowBorder="True"
                            gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <!--<Rectangle Grid.Row="0" Grid.Column="0" Fill="LightCyan"/>
                        <Rectangle Grid.Row="0" Grid.Column="1" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="0" Fill="LightBlue"/>
                        <Rectangle Grid.Row="1" Grid.Column="1" Fill="LightCyan"/>-->
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="0" Background="{Binding PartBackGround[3]}" FontSize="20" 
                               Foreground="Red" Content="4" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="1" Background="{Binding PartBackGround[2]}" FontSize="20" 
                               Foreground="Red" Content="3" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="0" Background="{Binding PartBackGround[1]}" FontSize="20"
                               Foreground="Red" Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="1" Background="{Binding PartBackGround[0]}" FontSize="20"
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
                <!--<box:Box_B></box:Box_B>-->
            </DataTemplate>

            <DataTemplate x:Key="BoxTypeERDataTemplate">
                <Grid Width="400" Height="200">
                    <Grid.RowDefinitions>
                        <RowDefinition Height="*" MinHeight="100"></RowDefinition>
                        <RowDefinition Height="Auto" MaxHeight="30"></RowDefinition>
                    </Grid.RowDefinitions>
                    <Grid gridhelper:GridHelper.GridLineBrush="Black"
                             gridhelper:GridHelper.ShowBorder="True"
                             gridhelper:GridHelper.GridLineThickness="5" Grid.Row="0">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="*" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>

                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="0" Background="{Binding PartBackGround[5]}" FontSize="20" 
                               Foreground="Red" Content="6" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="1" Background="{Binding PartBackGround[4]}" FontSize="20" 
                               Foreground="Red" Content="5" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="0" Grid.Column="2" Background="{Binding PartBackGround[3]}" FontSize="20"
                               Foreground="Red" Content="4" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="0" Background="{Binding PartBackGround[2]}" FontSize="20"
                               Foreground="Red" Content="3" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="1" Background="{Binding PartBackGround[1]}" FontSize="20"
                               Foreground="Red" Content="2" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                        <Label Style="{x:Null}" Grid.Row="1" Grid.Column="2" Background="{Binding PartBackGround[0]}" FontSize="20"
                               Foreground="Red" Content="1" VerticalAlignment="Stretch"
                               HorizontalAlignment="Stretch"></Label>
                    </Grid>
                    <StackPanel Orientation="Horizontal" VerticalAlignment="Center" HorizontalAlignment="Center" Grid.Row="1">
                        <TextBlock  VerticalAlignment="Center" HorizontalAlignment="Center"
                       FontSize="20" FontWeight="Bold" Text="{Binding Stock_barcode}"></TextBlock>
                    </StackPanel>
                </Grid>
            </DataTemplate>
            <!---->
            <DataTemplate  x:Key="PickPositionTemplate">
                <local:Pick_Position Margin="2" Padding="5" HorizontalAlignment="Stretch"></local:Pick_Position>
            </DataTemplate>

            <DataTemplate x:Key="InformationTemplate">
                <Border Margin="2" BorderThickness="2" Background="Ivory" BorderBrush="DarkMagenta" CornerRadius="5,5,5,5" Padding="2">
                    <StackPanel  Orientation="Horizontal">
                        <Label Margin="5,2,5,2" Content="{Binding Msg}"  Style="{x:Null}"
                           Background="{Binding Path=IsHighLight,Converter={StaticResource InformationHighlightBackgroundConverter}}"
                           Height="30" FontSize="18" Width="150" FontWeight="Bold"></Label>
                        <TextBlock Margin="5,2,5,2" Text="拣选位置:" Height="30" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="{Binding Title}" Height="30" MinWidth="40" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="目标位置:" Height="30" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="{Binding Remark}" Height="30" MinWidth="100" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="物料编码:" Height="30" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="{Binding SubTitle}" Height="30" MinWidth="180" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="物料名称:" Height="30" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="{Binding Header}" Height="30" MinWidth="200" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="数量:" Height="30" FontSize="18" FontWeight="Bold" Foreground="Crimson"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="{Binding Content}" Height="30" FontSize="18" Foreground="Crimson"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="抬头:"  Height="30" FontSize="18"></TextBlock>
                        <TextBlock Margin="5,2,5,2" Text="{Binding HeaderText}" Height="30" FontSize="18"></TextBlock>

                    </StackPanel>
                </Border>
            </DataTemplate>
        </Grid.Resources>
        <Grid.RowDefinitions>
            <RowDefinition Height="100" />
            <RowDefinition Height="*" MaxHeight="300" />
            <RowDefinition Height="100" />
            <RowDefinition Height="2*" MaxHeight="350"  />
        </Grid.RowDefinitions>
        <StackPanel Grid.Row="0" Orientation="Vertical">
            <ToolBar  ToolBarTray.IsLocked="True" Height="50" VerticalContentAlignment="Center" >
                <Label FontSize="16" FontWeight="Bold" Height="30" Content="{Binding StationName}" Margin="5"></Label>
                <!--<uc:ComboBoxQuery Margin="10,2,2,2"  FontSize="16" FontWeight="Bold" ComboBoxHeight="25" ComboBoxWidth="180" 
                                  ButtonHeight="27" ButtonWidth="90" ButtonContent="查询计划"  ComboBoxIsEditable="True" ComboBoxIsTextSearchCaseSensitive="True"
                                  ComboBoxDataTable="{Binding OrderDataTable}"  HorizontalAlignment="Center"
                                  SelectItem="{Binding PlanGroup,Mode=TwoWay}"
                                  ComboBoxDisplayMemberPath="NAME"
                                  ></uc:ComboBoxQuery>-->
                <Label FontSize="16" FontWeight="Bold" Height="30" Content="已绑定唯一码:" Margin="5"></Label>
                <Label FontSize="16" FontWeight="Bold" Height="30" Content="{Binding PlanGroup,Mode=OneWay}" Margin="5"></Label>
                <ComboBox Margin="15,5,5,5" FontSize="16" FontWeight="Bold" MinWidth="90" Height="35" HorizontalAlignment="Center"
                          ItemsSource="{Binding OrderDataTable}" SelectedValue="{Binding PlanGroupToBind,Mode=TwoWay}" 
                      SelectedValuePath="VALUE" DisplayMemberPath="NAME"></ComboBox>
                <Button Content="订单绑定" MinWidth="90" ToolTip="将唯一码对应的一组WBS绑定在拣选工作站上" Height="35" Margin="5" Command="{Binding CmdBindOrder}"/>
                <Button Content="拣选锁定(完全)" MinWidth="90" ToolTip="完全锁定绑定该拣选工作站的订单并生成锁定库存，当存在库存短缺情况时，提示锁定失败" Height="35" Margin="5" Command="{Binding CmdLockAndOutOrder}"/>
                <Button Content="拣选出库" MinWidth="90" ToolTip="根据锁定库存下达下架出库任务" Height="35" Margin="5" Command="{Binding CmdManageDownOutOrder}"/>
                <!--<Button Content="拣选任务取消" Height="35" Margin="5" Command="{Binding CmdA}" Visibility="Collapsed"/>
            <Button Content="拣选箱离开" Height="35"  Margin="5" Command="{Binding CmdB}" Visibility="Collapsed"/>
            <Button Content="删除" Height="35" Margin="5" Command="{Binding CmdDelete}" Visibility="Collapsed"/>-->
                <Button Content="订单绑定信息" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdShowBindInformation}"></Button>
                <Button Content="拣选箱库存" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdShowStorageInformation}"></Button>
                <Button Content="拣选任务列表" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdManagePickList}"></Button>
                <Button Content="拣选锁定(缺料)" MinWidth="90" ToolTip="部分锁定绑定该拣选工作站的订单并生成锁定库存，当存在库存短缺情况时，依旧可以锁定成功" Height="35" Margin="5" Command="{Binding CmdLockAndOutOrderPartly}"/>
                <Button Content="订单解绑" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdUnBindOrder}"/>
                <Button Content="打印物料标签" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdPrintManagePickContent}"></Button>
                <Button Content="关闭" MinWidth="60" Height="35" Margin="5" Command="{Binding CmdClose}"></Button>
                <!--<Button Content="异常处理" MinWidth="60" Height="35" Margin="5" Command="{Binding CmdErrorOperation}"></Button>
            <Button Content="强制脱开并解锁" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdErrorOperation}"></Button>
            <TextBox Height="35" Width="60"></TextBox>-->
                <Menu Height="35" FontSize="15" FontWeight="Bold" Margin="5">
                    <MenuItem Header="异常处理" Height="35" Background="OrangeRed">
                        <StackPanel Orientation="Horizontal">
                            <TextBlock FontSize="14" Margin="2">验证码:</TextBlock>
                            <TextBox FontSize="14" Height="35" Margin="2" MinWidth="50" Text="{Binding CheckStringForOperation,Mode=TwoWay }"></TextBox>
                            <Button Width="70" Height="35" FontSize="14" Content="提交" MinWidth="30" Command="{Binding CmdErrorOperationSubmit}"></Button>
                        </StackPanel>
                        <MenuItem Header="强制脱开" Command="{Binding CmdErrorOperationUnBindOrder}" IsEnabled="{Binding ErrerOperationShow}"></MenuItem>
                        <MenuItem Header="解锁库存" Command="{Binding CmdErrorOperationUnLock}" IsEnabled="{Binding ErrerOperationShow}"></MenuItem>
                    </MenuItem>
                </Menu>
                <Button Content="预绑定" Style="{x:Null}" Background="SpringGreen" MinWidth="90" Height="35" Margin="5" 
                        Command="{Binding CmdShowPrevBinding}"></Button>

            </ToolBar>
            
            <ToolBar ToolBarTray.IsLocked="True" Height="50" VerticalContentAlignment="Center" 
                     Visibility="{Binding IsShowPrevToolBar, Converter={StaticResource VisibilityConverter}}">
                <Label FontSize="16" FontWeight="Bold" Height="30" Foreground="DeepPink" Content="预绑定唯一码:" Margin="5"></Label>
                <Label FontSize="16" FontWeight="Bold" Height="30" Foreground="Yellow" Content="{Binding PrevBindingPlanGroup,Mode=OneWay}" Margin="5"></Label>
                <ComboBox Margin="15,5,5,5" FontSize="16" FontWeight="Bold" MinWidth="90" Height="35" HorizontalAlignment="Center"
                          ItemsSource="{Binding PrevBindingOrderDataTable}" SelectedValue="{Binding PlanGroupToPrevBinding,Mode=TwoWay}" 
                      SelectedValuePath="VALUE" DisplayMemberPath="NAME"></ComboBox>
                
                <Button Content="锁定-出库" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdPrevBinding}"></Button>
                <!--<Button Content="待用" MinWidth="90" Height="35" Margin="5" Command="{Binding CmdManagePickList}"></Button>-->
                </ToolBar>
        </StackPanel>

        <Grid Grid.Row="1">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200" ></ColumnDefinition>
                <ColumnDefinition Width="2*"></ColumnDefinition>
                <ColumnDefinition Width="200"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <StackPanel Orientation="Horizontal" Grid.Column="0" HorizontalAlignment="Right">
                <Image Margin="10" Style="{x:Null}" VerticalAlignment="Center" FlowDirection="{Binding StationFlowDirection}"  HorizontalAlignment="Right" Stretch="None"  Source=".\Arrow.png"/>
                <!--<Image Margin="10" Style="{x:Null}" VerticalAlignment="Center"  Source=".\Arrow.png"/>
                <Image Margin="10" Style="{x:Null}" VerticalAlignment="Center"  Source=".\Arrow.png"/>-->
            </StackPanel>
            <ItemsControl Grid.Column="1" ItemsSource="{Binding Boxs}" Margin="10" HorizontalContentAlignment="Center" VerticalAlignment="Center">
                <ItemsControl.ItemTemplateSelector>
                    <local:BoxTypeDataTemplateSelector 
                    BoxTypeADataTemplate="{StaticResource BoxTypeADataTemplate}"
                    BoxTypeBDataTemplate="{StaticResource BoxTypeBDataTemplate}"
                    BoxTypeCDataTemplate="{StaticResource BoxTypeCDataTemplate}"
                    BoxTypeDDataTemplate="{StaticResource BoxTypeDDataTemplate}"
                    BoxTypeEDataTemplate="{StaticResource BoxTypeEDataTemplate}"
                    BoxTypeARDataTemplate="{StaticResource BoxTypeARDataTemplate}"
                    BoxTypeBRDataTemplate="{StaticResource BoxTypeBRDataTemplate}"
                    BoxTypeCRDataTemplate="{StaticResource BoxTypeCRDataTemplate}"
                    BoxTypeDRDataTemplate="{StaticResource BoxTypeDRDataTemplate}"
                    BoxTypeERDataTemplate="{StaticResource BoxTypeERDataTemplate}"
                    >
                    </local:BoxTypeDataTemplateSelector>
                </ItemsControl.ItemTemplateSelector>
            </ItemsControl>
            <StackPanel  Orientation="Horizontal" Grid.Column="2" HorizontalAlignment="Left">
                <Image Margin="10" Style="{x:Null}" VerticalAlignment="Center" FlowDirection="{Binding StationFlowDirection}" HorizontalAlignment="Right" Stretch="None" Source=".\Arrow.png"/>
                <!--<Image Margin="10" Style="{x:Null}" VerticalAlignment="Center" Source=".\Arrow.png"/>
                <Image Margin="10" Style="{x:Null}" VerticalAlignment="Center" Source=".\Arrow.png"/>-->
            </StackPanel>
            <StackPanel  Orientation="Vertical" Grid.Column="3" HorizontalAlignment="Left" Visibility="Collapsed">
                <StackPanel  Orientation="Horizontal">
                    <TextBlock Margin="5" FontWeight="Bold" FontSize="16">SN:</TextBlock>
                    <TextBox Text="{Binding ScanSN,Mode=TwoWay}" Margin="5" Width="120" FontSize="16"></TextBox>
                </StackPanel>
                <StackPanel Orientation="Horizontal">
                    <Button Content="添加" MinWidth="50" Height="35" Margin="5" Command="{Binding CmdAddSNToList}"></Button>
                    <Button Content="清空" MinWidth="50" Height="35" Margin="5" Command="{Binding CmdClearSNList}"></Button>
                    <Button Content="提交" MinWidth="50" Height="35" Margin="5" Command="{Binding CmdConfirmSNList}"></Button>
                </StackPanel>
                <ListView Margin="5" FontWeight="Bold" FontSize="16" Width="170" MinHeight="50" MaxHeight="100" ItemsSource="{Binding SNList}"></ListView>
            </StackPanel>
        </Grid>
        
        <!--<ScrollViewer Grid.Row="2" MaxHeight="100" Margin="5,10,5,10">-->
        <ItemsControl Grid.Row="2" MaxHeight="100" ItemsSource="{Binding Informations}" Margin="30,5,30,5" 
                      HorizontalContentAlignment="Center" VerticalAlignment="Center"
                      ItemTemplate="{StaticResource InformationTemplate}" >
            <!--ScrollViewer.VerticalScrollBarVisibility="Visible" ScrollViewer.CanContentScroll="True">-->
        </ItemsControl>
        <!--</ScrollViewer>-->

        <Grid Grid.Row="3" Margin="5,10,5,10">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <local:Pick_Position Grid.Column="0" Margin="2" Padding="5" DataContext="{Binding PickPositionViewModels[4]}"></local:Pick_Position>
            <local:Pick_Position Grid.Column="1" Margin="2" Padding="5" DataContext="{Binding PickPositionViewModels[3]}"></local:Pick_Position>
            <local:Pick_Position Grid.Column="2" Margin="2" Padding="5" DataContext="{Binding PickPositionViewModels[2]}"></local:Pick_Position>
            <local:Pick_Position Grid.Column="3" Margin="2" Padding="5" DataContext="{Binding PickPositionViewModels[1]}"></local:Pick_Position>
            <local:Pick_Position Grid.Column="4" Margin="2" Padding="5" DataContext="{Binding PickPositionViewModels[0]}"></local:Pick_Position>
        </Grid>

        <!--<ItemsControl Grid.Row="3" ItemsSource="{Binding PickPositionViewModels[0]}" ItemTemplate="{StaticResource PickPositionTemplate}" >
            <ItemsControl.ItemsPanel>
                <ItemsPanelTemplate>
                    <StackPanel Orientation="Horizontal" ></StackPanel>
                </ItemsPanelTemplate>
            </ItemsControl.ItemsPanel>
        </ItemsControl>-->
    </Grid>
</ad:DocumentContent>
