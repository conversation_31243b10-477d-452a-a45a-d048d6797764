﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// STORAGE_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class STORAGE_MAIN
	{
		public STORAGE_MAIN()
		{
			
		}
		
		private int _storage_id;
        private int _goods_template_id;
		private int _cell_id;
		private string _stock_barcode;
		private string _cell_model;
		private string _full_flag;
		private string _storage_remark;

        private string _is_exception;
        private string _kitbox_up_complete;
        private string _storage_pick_operator;

        ///<sumary>
        /// 库存编号
        ///</sumary>
        [DataMember]
		public int STORAGE_ID
		{
			get{return _storage_id;}
			set{_storage_id = value;}
		}


        [DataMember]
        public int GOODS_TEMPLATE_ID
        {
            get { return _goods_template_id; }
            set { _goods_template_id = value; }

        }

		///<sumary>
		/// 货位编号
        ///</sumary>
        [DataMember]
		public int CELL_ID
		{
			get{return _cell_id;}
			set{_cell_id = value;}
		}
		///<sumary>
		/// 托盘条码
        ///</sumary>
        [DataMember]
		public string STOCK_BARCODE
		{
			get{return _stock_barcode;}
			set{_stock_barcode = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CELL_MODEL
		{
			get{return _cell_model;}
			set{_cell_model = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string FULL_FLAG
		{
			get{return _full_flag;}
			set{_full_flag = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string STORAGE_REMARK
		{
			get{return _storage_remark;}
			set{_storage_remark = value;}
		}

        ///<sumary>
        /// 库存是否异常，拣选工作站处可设置为异常，之后申请时剔除
        ///</sumary>
        [DataMember]
        public string IS_EXCEPTION
        {
            get { return _is_exception; }
            set { _is_exception = value; }
        }

        ///<sumary>
        /// 标记齐套箱是否上架完成，防止齐套箱上架完成后被人工下架又上架更新计划完成数量
        ///</sumary>
        [DataMember]
        public string KITBOX_UP_COMPLETE
        {
            get { return _kitbox_up_complete; }
            set { _kitbox_up_complete = value; }
        }
        ///<sumary>
        /// 拣选操作人
        ///</sumary>
        [DataMember]
        public string STORAGE_PICK_OPERATOR
        {
            get { return _storage_pick_operator; }
            set { _storage_pick_operator = value; }
        }
    }
}
