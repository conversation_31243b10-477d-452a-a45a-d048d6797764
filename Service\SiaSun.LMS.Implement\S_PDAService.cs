﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;
using System.Reflection;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, 
     InstanceContextMode = InstanceContextMode.Single, 
     ConcurrencyMode = ConcurrencyMode.Multiple, 
     MaxItemsInObjectGraph = int.MaxValue)]
    public partial class S_PDAService : S_BaseService, SiaSun.LMS.Interface.I_PDAService
    {
        public S_PDAService()
        {
        }

        public DataTable GetList(string strSQL)
        {
            return base.GetList(strSQL);
        }
        public new ObjectT GetModel(string statementName, object parameterObject)
        {
            return base.GetModel(statementName, parameterObject);
        }

        public bool ManageCreate(string sClassName,string sMethodName,
                                 SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 bool bCheckStorage,
                                 bool bComplete,
                                 bool bAutoSendControl,
                                 out string sResult)
        {
            return base.Invoke(sClassName, sMethodName, new object[] { mMANAGE_MAIN, lsMANAGE_LIST, bTrans, bCheckStorage, bComplete, bAutoSendControl }, out sResult);

        }


        public SiaSun.LMS.Model.WH_CELL GetModelCellCode(string CELL_CODE)
        {
            return (WH_CELL)base.GetModel("WH_CELL_SELECT_BY_CELL_CODE", CELL_CODE).RequestObject;
        }


        public bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SiaSun.LMS.Model.SYS_USER USER)
        {
            return base._S_SystemService.USER_LOGIN(USER_CODE, USER_PASSWORD, out USER);
        }


        public IList<SiaSun.LMS.Model.MANAGE_TYPE_PARAM> ManageTypeParamGetList(int MANAGE_TYPE_ID)
        {
            return base._S_ManageService.ManageTypeParamGetList(MANAGE_TYPE_ID);
        }

    }
}
