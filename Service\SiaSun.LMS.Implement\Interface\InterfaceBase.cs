﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;

namespace SiaSun.LMS.Implement.Interface
{
    public class InterfaceBase : S_BaseService
    {
        private static string _sWmsSrvUrl;
        private static string _sWmsSrvUrlTest;

        public static readonly object interfaceLockObj = new object();

        public InterfaceBase()
        {
            if (string.IsNullOrEmpty(_sWmsSrvUrl))
            {
                _sWmsSrvUrl = Common.StringUtil.GetConfig("WMSSrvUrl");
            }
            if (string.IsNullOrEmpty(_sWmsSrvUrlTest))
            {
                _sWmsSrvUrlTest = Common.StringUtil.GetConfig("WMSSrvUrlTest");
            }
        }

        /// <summary>
        /// 公共出参
        /// </summary>
        public class OutputPara
        {
            private string _responseCode;
            private string _responseMessage;

            ///<sumary>
            /// 响应代码
            ///</sumary>
            public string responseCode
            {
                get { return this._responseCode; }
                set { this._responseCode = value; }
            }
            ///<sumary>
            /// 响应消息
            ///</sumary>
            public string responseMessage
            {
                get { return "新松返回 " + this._responseMessage; }
                set { this._responseMessage = value; }
            }
        }

        /// <summary>
        /// 调用立库
        /// </summary>
        public bool InvokeHouse(string sMethodName, string inParm, out string outParm)
        {
            bool bResult = true;
            string sResult = string.Empty;
            outParm = string.Empty;

            DateTime dtBegin = new DateTime();
            DateTime dtEnd = new DateTime();

            lock (interfaceLockObj)
            {
                try
                {
                    dtBegin = System.DateTime.Now;

                    string sClassFullName = string.Format("SiaSun.LMS.Implement.Interface.{0}", sMethodName);
                    bResult = this.Invoke(sClassFullName, "NotifyMethod", inParm, out outParm);
                }
                catch (Exception ex)
                {
                    bResult = false;
                    outParm = string.Format("InvokeHouse发生异常 {0}", ex.Message);
                }
                finally
                {
                    dtEnd = System.DateTime.Now;
                    this.CreateSysLog(Enum.LogThread.Interface, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("InterfaceBase.InvokeHouse():接口方法[{2}]_入参[{0}]_出参[{1}]", inParm, outParm, sMethodName));
                    this._log.Debug(string.Format("InvokeHouse：接口方法_{0} 入参_{1} 出参_{2} 开始时间_{3} 结束时间_{4}", sMethodName, inParm, outParm, dtBegin.ToString("yyyy-MM-dd HH:mm:ss,fff"), dtEnd.ToString("yyyy-MM-dd HH:mm:ss,fff")));
                }
            }

            return bResult;
        }

        /// <summary>
        /// 调用外部(WebService)
        /// </summary>
        public bool InvokeExternal(String sMethodName, String inParm, out string outParm)
        {
            bool bResult = true;
            outParm = string.Empty;

            DateTime dtBegin = new DateTime();
            DateTime dtEnd = new DateTime();

            try
            {
                dtBegin = System.DateTime.Now;

                bResult = SiaSun.LMS.Common.WebServiceHelper.Invoke(_sWmsSrvUrl, string.Empty, sMethodName, new object[] { inParm }, out outParm);
            }
            catch (Exception ex)
            {
                bResult = false;
                outParm = string.Format("InvokeExternal发生异常 {0}", ex.Message);
            }
            finally
            {
                dtEnd = System.DateTime.Now;
                this.CreateSysLog(Enum.LogThread.Interface, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("InterfaceBase.InvokeExternal():接口方法[{2}]_入参[{0}]_出参[{1}] ", inParm, outParm,sMethodName));
                this._log.Debug(string.Format("InvokeExternal：接口方法_{0} 入参_{1} 出参_{2} 开始时间_{3} 结束时间_{4}", sMethodName, inParm, outParm, dtBegin.ToString("yyyy-MM-dd HH:mm:ss,fff"), dtEnd.ToString("yyyy-MM-dd HH:mm:ss,fff")));
            }

            return bResult;
        }

        /// <summary>
        /// 调用外部测试接口(WebService)
        /// </summary>
        public bool InvokeExternalTest(String sMethodName, String inParm, out string outParm)
        {
            bool bResult = true;
            outParm = string.Empty;

            DateTime dtBegin = new DateTime();
            DateTime dtEnd = new DateTime();

            try
            {
                dtBegin = System.DateTime.Now;

                bResult = SiaSun.LMS.Common.WebServiceHelper.Invoke(_sWmsSrvUrlTest, string.Empty, sMethodName, new object[] { inParm }, out outParm);
            }
            catch (Exception ex)
            {
                bResult = false;
                outParm = string.Format("InvokeExternalTest发生异常 {0}", ex.Message);
            }
            finally
            {
                dtEnd = System.DateTime.Now;
                this.CreateSysLog(Enum.LogThread.Interface, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("InterfaceBase.InvokeExternalTest():接口方法[{2}]_入参[{0}]_出参[{1}] ", inParm, outParm, sMethodName));
                this._log.Debug(string.Format("InvokeExternalTest：接口方法_{0} 入参_{1} 出参_{2} 开始时间_{3} 结束时间_{4}", sMethodName, inParm, outParm, dtBegin.ToString("yyyy-MM-dd HH:mm:ss,fff"), dtEnd.ToString("yyyy-MM-dd HH:mm:ss,fff")));
            }

            return bResult;
        }

        ///// <summary>
        ///// 调用WCS(WebService)
        ///// </summary>
        //public bool InvokeWcs(String sMethodName, int inParm, out string outParm)
        //{
        //    bool bResult = true;
        //    outParm = string.Empty;

        //    DateTime dtBegin = new DateTime();
        //    DateTime dtEnd = new DateTime();

        //    try
        //    {
        //        dtBegin = System.DateTime.Now;

        //        bResult = SiaSun.LMS.Common.WebServiceHelper.Invoke(_sWcsSrvUrl, string.Empty, sMethodName, new object[] { inParm }, out outParm);
        //    }
        //    catch (Exception ex)
        //    {
        //        bResult = false;
        //        outParm = string.Format("程序发生异常_异常信息{0}", ex.Message);
        //    }
        //    finally
        //    {
        //        dtEnd = System.DateTime.Now;
        //        this.CreateSysLog(Enum.LogThread.Interface, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("InterfaceBase.InvokeWcs():接口方法[{2}]_入参[{0}]_出参[{1}] ", inParm, outParm, sMethodName));
        //        this._log.Debug(string.Format("InvokeWcs：接口方法_{0} 入参_{1} 出参_{2} 开始时间_{3} 结束时间_{4}", sMethodName, inParm, outParm, dtBegin.ToString("yyyy-MM-dd HH:mm:ss,fff"), dtEnd.ToString("yyyy-MM-dd HH:mm:ss,fff")));
        //    }

        //    return bResult;
        //}

        /// <summary>
        /// 反射调用
        /// </summary>
        private bool Invoke(string sClassFullName, string sMethodName, object sXmlIn, out string sXmlOut)
        {
            bool bResult = true;
            sXmlOut = string.Empty;

            Type t = this.GetType();
            Assembly complierAssembly = t.Assembly;
            object complierInstance = complierAssembly.CreateInstance(sClassFullName);
            Type type = complierInstance.GetType();
            MethodInfo Method = type.GetMethod(sMethodName);
            object[] obj = new object[] { sXmlIn, sXmlOut };
            object oResult = Method.Invoke(complierInstance, obj);
            bResult = Convert.ToBoolean(oResult);
            sXmlOut = obj[1].ToString();

            return bResult;
        }



    }
}
