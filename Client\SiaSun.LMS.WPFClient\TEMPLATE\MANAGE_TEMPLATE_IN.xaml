﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.TEMPLATE.MANAGE_TEMPLATE_IN"
       xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_IN" Height="353" Width="620" Loaded="DocumentContent_Loaded">


        <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>

        <WrapPanel Grid.Row="0"  HorizontalAlignment="Left" VerticalAlignment="Center">
            <StackPanel  Name="panelGoods" Orientation="Horizontal" Margin="5,5,5,5" >
                <TextBlock Text="产品:" Margin="2" VerticalAlignment="Center" />
                <ComboBox Name="cmbGoods" Margin="2,0,2,0" MinWidth="120" MinHeight="23" />
            </StackPanel>

            <StackPanel Name="panelTemplate"  Orientation="Horizontal" Margin="{Binding ElementName=panelGoods,Path=Margin}">
                <TextBlock Text="配备方案:" Margin="2" VerticalAlignment="Center"></TextBlock>
                <ComboBox Name="cmbTemplate" Margin="2,0,0,0" MinWidth="120" IsEditable="True"  AllowDrop="True" IsTextSearchEnabled="True"  StaysOpenOnEdit="True"></ComboBox>
            </StackPanel>
        </WrapPanel>

        <uc:ucSplitPropertyGridTab x:Name="gridManageList" Grid.Row="1" ></uc:ucSplitPropertyGridTab>
            
          <WrapPanel Grid.Row="2" HorizontalAlignment="Left" VerticalAlignment="Center" ButtonBase.Click="Button_Click">
            <uc:ucManagePosition Grid.Row="3" x:Name="ucManagePosition"  Margin="0,0,2,0" ></uc:ucManagePosition>
            <CheckBox Name="rbRefresh" Width="100" VerticalAlignment="Center" Content="保留组盘信息"/>
            <Button Name="btnSave"  Width="70" Margin="5">保存</Button>
            <Button Name="btnRefresh"  Width="70" Margin="5">刷新</Button>
          </WrapPanel>
        </Grid>

</ad:DocumentContent >
