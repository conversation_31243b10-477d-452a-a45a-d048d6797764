﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Windows;
using System.Windows.Controls;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_MOVE_OUT.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_DOWN : AvalonDock.DocumentContent
    {
        private string _STOCK_BARCODE = string.Empty;
        private bool _MUTI_BOX = false;

        private Model.MANAGE_TYPE mMANAGE_TYPE = null;

        private List<Model.MANAGE_LIST> listMANAGE_LIST = null;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_DOWN(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                ((QueryWhere) =>
                {
                    QueryWhere = string.Format("{0} AND {1}", this.ucSplitPanel.U_GetSplitPropertyWhere(), string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere);
                    this.StorageListBind(QueryWhere);
                }
                );
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_DOWN(string MANAGE_TYPE_CODE, string STOCK_BARCODE)
            : this(MANAGE_TYPE_CODE)
        {
            this._STOCK_BARCODE = STOCK_BARCODE;
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_DOWN(string MANAGE_TYPE_CODE, bool MUTI_BOX)
            : this(MANAGE_TYPE_CODE)
        {
            this._MUTI_BOX = MUTI_BOX;
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //输送位置
            this.InitManagePosotion();
            //属性控件
            this.InitSplitPropertyPanel();
            //查询控件
            this.InitQueryControl();

            if (!string.IsNullOrEmpty(this._STOCK_BARCODE))
            {
                this.ucQuery.U_ButtonClick(this._STOCK_BARCODE);
            }
        }

        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = "V_STORAGE_LIST";
            this.ucQuery.U_InitControl();
        }

        /// <summary>
        /// 初始化属性面板
        /// </summary>
        private void InitSplitPropertyPanel()
        {
            this.ucSplitPanel.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucSplitPanel.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.ucSplitPanel.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.ucSplitPanel.U_SplitPropertyType = "GOODS_TYPE";
            this.ucSplitPanel.U_InitControl();
        }

        private void StorageListBind(string QueryWhere)
        {
            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";

            //wdz add 2019-07-07
            if (MainApp._USER.USER_CODE == "super")
            {
                this.ucStorageGroup.U_Where = string.Format(" AREA_TYPE= 'LiKu' and cell_id not in ( select start_cell_id from manage_main) and {0} ", QueryWhere);
            }
            else
            {
                this.ucStorageGroup.U_Where = string.Format(" cell_status <> 'Pallet'  AND AREA_TYPE= 'LiKu' and cell_id not in ( select start_cell_id from manage_main) and {0} ", QueryWhere);
            }

            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = true;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;

                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = false;
            bool boolSingleResult = true;
            string strResult = "\n";
            string sSingleReslut = string.Empty;

            try
            {
                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                //获得选中记录
                List<DataRowView> listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();

                //校验是否选中记录
                if (listDataRowView.Count == 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                    return;
                }

                //xcjt add 2017-04-12
                //下架页面选中一条数据后将同托盘条码的条目都自动选中
                var checkedBarcode = (from r in listDataRowView select r["STOCK_BARCODE"]).ToList();
                foreach (DataRowView drv in listDataRowView[0].DataView)
                {
                    if (checkedBarcode.Contains(drv["STOCK_BARCODE"].ToString()))
                    {
                        this.ucStorageGroup.U_CheckRow(drv);
                    }
                }
                //重新获得选中记录
                listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();
                if (listDataRowView.Any(r => r["AREA_ID"].ToString() == "4") && listDataRowView.GroupBy(r => r["STOCK_BARCODE"]).Count() != 1)
                {
                    MainApp._MessageDialog.ShowResult(false, "3楼双深立体库只允许选择1个托盘信息");
                    return;
                }

                foreach (DataRowView drv in listDataRowView)
                {
                    drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                }

                var cell_id_group = from v in listDataRowView
                                    group v by v["CELL_ID"].ToString() into a
                                    select a;

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //wdz add 2019-07-07
                    if (mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageArrangeGoodsDown.ToString())
                    {
                        DataTable dtEndCellCode = MainApp._I_BaseService.GetList(string.Format("select CELL_CODE from WH_CELL where CELL_ID={0}", this.ucManagePosition.U_END_POSITION_ID));
                        if (dtEndCellCode == null || dtEndCellCode.Rows.Count < 1)
                        {
                            MainApp._MessageDialog.ShowResult(false, "未找到下架终点位置");
                            return;
                        }
                        string endCellCode = dtEndCellCode.Rows[0]["CELL_CODE"].ToString();

                        DataTable dtWorkMode = MainApp._I_BaseService.GetList(string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY='WorkingMode-{0}'", endCellCode));
                        if (dtWorkMode == null || dtWorkMode.Rows.Count < 1)
                        {
                            MainApp._MessageDialog.ShowResult(false, string.Format("未找到指定的整理工位[{0}]的工作模式配置信息", endCellCode));
                            return;
                        }
                        string workMode = dtWorkMode.Rows[0]["PARAMETER_VALUE"].ToString();

                        if (workMode != Enum.ArrangeWorkMode.ArrangeGoods.ToString())
                        {
                            MainApp._MessageDialog.ShowResult(false, string.Format("指定整理工位[{0}]的工作模式[{1}]不是整理未满箱", endCellCode, workMode));
                            return;
                        }

                        //wdz add 2019-03-05 如果整理虚拟箱有货则提示失败
                        DataTable dtStorageMainCheck = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_MAIN where STOCK_BARCODE ='{0}'", "V" + endCellCode));
                        if (dtStorageMainCheck == null || dtStorageMainCheck.Rows.Count < 1)
                        {
                            MainApp._MessageDialog.ShowResult(false, string.Format("未找到虚拟箱库存信息_虚拟箱[{0}]", "V" + endCellCode));
                            return;
                        }
                        DataTable dtStorageListCheck = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_LIST where STORAGE_ID ={0}", dtStorageMainCheck.Rows[0]["STORAGE_ID"].ToString()));
                        if (dtStorageListCheck != null && dtStorageListCheck.Rows.Count > 0)
                        {
                            MainApp._MessageDialog.ShowResult(false, string.Format("整理下架任务时发现虚拟箱存在库存_虚拟箱[{0}]_库存条数[{1}]", "V" + endCellCode, dtStorageListCheck.Rows.Count));
                            return;
                        }

                        List<DateTime> lsStorageListInTime = new List<DateTime>();

                        foreach (var cell_id in cell_id_group)
                        {
                            int startCellId = Convert.ToInt32(cell_id.Key);

                            //验证所选库存是否包含已锁定箱、齐套箱、异常库存箱
                            DataTable dtStorageMainVerify = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_MAIN where CELL_ID ={0}", startCellId));
                            if (dtStorageMainVerify == null || dtStorageMainVerify.Rows.Count != 1)
                            {
                                MainApp._MessageDialog.ShowResult(false, string.Format("未找到待下架箱的库存信息"));
                                return;
                            }
                            if (!new string[] { "1", "2", "3", "4", "6" }.Contains(dtStorageMainVerify.Rows[0]["CELL_MODEL"]))
                            {
                                MainApp._MessageDialog.ShowResult(false, string.Format("待下架箱必须为原料箱"));
                                return;
                            }

                            DataTable dtStorageListVerify1 = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_LIST where STORAGE_ID ={0}", dtStorageMainVerify.Rows[0]["STORAGE_ID"].ToString()));
                            if (dtStorageListVerify1 == null || dtStorageListVerify1.Rows.Count < 1)
                            {
                                MainApp._MessageDialog.ShowResult(false, string.Format("未找到待下架箱的库存列表信息"));
                                return;
                            }

                            string querySql = string.Format(@"select * from V_STORAGE_LIST t
                                                      where STORAGE_ID = {0}
                                                            and CELL_TYPE = 'Cell' and RUN_STATUS = 'Enable' and CELL_STATUS in ('Full', 'Pallet')
                                                            and (STORAGE_LOCK_QUANTITY is null or STORAGE_LOCK_QUANTITY = 0)
                                                            and (GOODS_PROPERTY2 is null or GOODS_PROPERTY2 != '1')
                                                            and (IS_EXCEPTION is null or IS_EXCEPTION != '1')
                                                            and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null
                                                            and CELL_MODEL in ('1','2','3','4','6')",
                                     dtStorageMainVerify.Rows[0]["STORAGE_ID"].ToString());

                            DataTable dtStorageListVerify2 = MainApp._I_BaseService.GetList(querySql);
                            if (dtStorageListVerify2 == null || dtStorageListVerify2.Rows.Count != dtStorageListVerify1.Rows.Count)
                            {
                                MainApp._MessageDialog.ShowResult(false, string.Format("选定的下架箱库存存在锁定库存、异常库存或者货位状态不正确_箱条码[{0}]", dtStorageMainVerify.Rows[0]["STOCK_BARCODE"].ToString()));
                                return;
                            }

                            //保存入库时间 校验入库时间是否相差大于一个月
                            foreach (DataRow item in dtStorageListVerify2.Rows)
                            {
                                lsStorageListInTime.Add(DateTime.Parse(item["ENTRY_TIME"].ToString()));
                            }
                        }

                        //2021-08-17 08:27:20 取消限制
                        ////校验入库时间防止打破先进先出原则
                        //if ((lsStorageListInTime.Max() - lsStorageListInTime.Min()).TotalDays > 30)
                        //{
                        //    MainApp._MessageDialog.ShowResult(false, string.Format("选定的下架箱的库存入库时间间隔大于30天_不允许未满箱整理"));
                        //    return;
                        //}
                    }

                    foreach (var cell_id in cell_id_group)
                    {
                        int intStartPositionID = Convert.ToInt32(cell_id.Key);

                        var value_ceLl_id = from v in listDataRowView
                                            where v["CELL_ID"].ToString() == cell_id.Key
                                            select v;

                        DataRowView[] drvGroup = value_ceLl_id.Cast<DataRowView>().ToArray();

                        listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(drvGroup, null);

                        Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.CELL_MODEL = drvGroup[0]["CELL_MODEL"].ToString();
                        mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                        mMANAGE_MAIN.MANAGE_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];
                        mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                        mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                        mMANAGE_MAIN.PLAN_ID = 0;
                        mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                        mMANAGE_MAIN.START_CELL_ID = intStartPositionID;
                        mMANAGE_MAIN.STOCK_BARCODE = drvGroup[0]["STOCK_BARCODE"].ToString();

                        boolSingleResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                             "ManageCreate",
                                              new object[] { mMANAGE_MAIN,
                                                             listMANAGE_LIST,
                                                             true,
                                                             this.ucManagePosition.U_AutoCompleteTask ,
                                                             this.ucManagePosition.U_AutoDownloadControlTask,
                                                            },
                                              out sSingleReslut);

                        if (!boolSingleResult)
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达失败 " + sSingleReslut + "\n";

                            break;
                        }
                        else
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达成功 " + "\n";

                            boolResult = true;

                            //wdz add 2019-07-07 ywz 去掉用super账户控制，改为用变量 2022-05-20 13:20:38.717
                            if (!_MUTI_BOX && mMANAGE_TYPE.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManageArrangeGoodsDown.ToString())
                            {
                                break;
                            }
                        }
                    }

                    this.Refresh();
                }

                MainApp._MessageDialog.ShowResult(boolResult, strResult);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                this.Refresh();
            }
        }

        /// <summary>
        /// 获得托盘集合列表
        /// </summary>
        private IDictionary<string, Model.WH_CELL> GetPalletKeyValuePair(List<DataRowView> listDataRowView)
        {
            IDictionary<string, Model.WH_CELL> dicStack = new Dictionary<string, Model.WH_CELL>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack = rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (mWH_CELL != null && !dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, mWH_CELL);
                    }
                }
            }
            return dicStack;
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            //刷新
            this.ucManagePosition.U_Update();

            this.ucStorageGroup.U_InitControl();
        }
    }
}