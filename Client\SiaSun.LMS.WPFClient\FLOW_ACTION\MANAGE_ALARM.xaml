﻿<Window x:Class="SiaSun.LMS.WPFClient.FLOW_ACTION.MANAGE_ALARM"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.FLOW_ACTION"
        mc:Ignorable="d"
        Title="MANAGE_ALARM" Height="200" Width="350" WindowStyle="None" Topmost="True">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="1*"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
            <RowDefinition Height="1*"></RowDefinition>
        </Grid.RowDefinitions>
        <Label x:Name="alarmtime" FontSize="24"  Margin="5" Foreground="Red" Tag="2">
            <Label.Style>
                <Style TargetType="Label">
                    <Style.Triggers>
                        <DataTrigger Binding="{Binding RelativeSource={RelativeSource Self},Path=Tag}" Value="2">
                            <DataTrigger.EnterActions>
                                <BeginStoryboard Name="Shine">
                                    <Storyboard RepeatBehavior="Forever">
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetProperty="Foreground.Color">
                                            <EasingColorKeyFrame KeyTime="0" Value="Red"></EasingColorKeyFrame>
                                            <EasingColorKeyFrame KeyTime="0:0:0.5" Value="Green"></EasingColorKeyFrame>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </BeginStoryboard>
                            </DataTrigger.EnterActions>
                            <DataTrigger.ExitActions>
                                <StopStoryboard BeginStoryboardName="Shine" />
                            </DataTrigger.ExitActions>
                        </DataTrigger>
                    </Style.Triggers>
                </Style>
            </Label.Style>
        </Label>
        <TextBox x:Name="txt" Grid.Row="1" VerticalAlignment="Stretch" FontSize="20"  Margin="10" HorizontalAlignment="Stretch" TextWrapping="Wrap" IsReadOnly="True"/>
        <Button Grid.Row="2" Width="60" Content="我知道了" VerticalAlignment="Stretch" Height="40" Click="Comform"></Button>
    </Grid>
</Window>
