﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.Data;
using System.Xml;
using System.IO;

namespace SiaSun.LMS.Common
{
    public class CloneObjectValues
    {
        #region     ------T-T,T-DataRow,DataRow-T,DataRow-DataRow复制


        /// <summary>
        /// 将字典中的数据赋值给T的实例
        /// xcjt add 2017-01-13
        /// </summary>
        public static T CloneModelValue<T>(T model_T, IDictionary<string, string> dicPairValue)
        {
            if(model_T!=null)
            {
                PropertyInfo[] arPropertyInfo_T = model_T.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach(var item in arPropertyInfo_T)
                {
                    if(dicPairValue!=null&&dicPairValue.ContainsKey(item.Name))
                    {
                        item.SetValue(model_T, Convert.ChangeType(dicPairValue[item.Name], item.PropertyType), null);
                    }
                }
            }
            return model_T;
        }

        /// <summary>
        /// 将T1实例的值复制到T2实例
        /// </summary>
        public T2 CloneModelValue<T1, T2>(T1 model_T1, T2 model_T2,IDictionary<string,object> dicPairValue)
        {
            if (model_T1 != null && model_T2 != null)
            {
                PropertyInfo[] arPropertyInfo_T1 = model_T1.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
                PropertyInfo[] arPropertyInfo_T2 = model_T2.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);

                foreach (PropertyInfo pInfo_T2 in arPropertyInfo_T2)
                {
                    //判断是否存在值对
                    if (dicPairValue != null && (dicPairValue.ContainsKey(pInfo_T2.Name)))
                    {
                        pInfo_T2.SetValue(model_T2, Convert.ChangeType(dicPairValue[pInfo_T2.Name], pInfo_T2.PropertyType), null);
                    }
                    else
                    {
                        //判断是否存在于T1中
                        if (arPropertyInfo_T1.Count(r => r.Name == pInfo_T2.Name && r.PropertyType.ToString() == pInfo_T2.PropertyType.ToString()) > 0)
                        {
                            //获得T1的属性
                            PropertyInfo pInfo_T1 = arPropertyInfo_T1.Single(r => r.Name == pInfo_T2.Name && r.PropertyType.ToString() == pInfo_T2.PropertyType.ToString());
                            //获得属性值
                            object objValue_T1 = pInfo_T1.GetValue(model_T1, null);
                            //设置属性值
                            pInfo_T2.SetValue(model_T2, Convert.ChangeType(objValue_T1, pInfo_T2.PropertyType), null);
                        }
                    }
                }
            }
            return model_T2;
        }

        /// <summary>
        /// 从DataRow中读取值付给实例
        /// </summary>
        public T CloneModelValue<T>(DataRow row, T model_T, IDictionary<string, object> dicPairValue)
        {
            if (model_T != null)
            {
                //获得所有列集合
                DataColumn[] arColumn = row.Table.Columns.Cast<DataColumn>().ToArray();
                PropertyInfo[] arPropertyInfo_T = model_T.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach (PropertyInfo pInfo_T in arPropertyInfo_T)
                {
                    //判断是否存在值对
                    if (dicPairValue != null && (dicPairValue.ContainsKey(pInfo_T.Name)))
                    {
                        pInfo_T.SetValue(model_T, Convert.ChangeType(dicPairValue[pInfo_T.Name], pInfo_T.PropertyType), null);
                    }
                    else
                    {
                        //判断是否存在该属性
                        if (arColumn.Count(r => r.ColumnName.ToLower() == pInfo_T.Name.ToLower()) > 0)
                        {
                            DataColumn col = arColumn.Single(r => r.ColumnName == pInfo_T.Name);
                            if (!row.IsNull(col))
                            {
                                //设置属性值
                                pInfo_T.SetValue(model_T, Convert.ChangeType(row[col], pInfo_T.PropertyType), null);
                            }
                        }
                    }
                }
            }
            return model_T;
        }



        /// <summary>
        /// 从实例T中复制到DataRow中
        /// </summary>
        public DataRow CloneDataRowValue<T>(T model_T, DataRow row, IDictionary<string, object> dicPairValue)
        {
            if (model_T != null)
            {
                //获得所有列集合
                DataColumn[] arColumn = row.Table.Columns.Cast<DataColumn>().ToArray();
                PropertyInfo[] arPropertyInfo_T = model_T.GetType().GetProperties(BindingFlags.Public | BindingFlags.Instance);
                foreach (DataColumn col in arColumn)
                {
                    //设置默认值
                    row[col] = col.DefaultValue;

                    //判断是否存在值对
                    if (dicPairValue != null && (dicPairValue.ContainsKey(col.ColumnName)))
                    {
                        row[col] = dicPairValue[col.ColumnName];
                    }
                    else
                    {
                        //判断属性是否存在
                        if (arPropertyInfo_T.Count(r => r.Name.ToLower() == col.ColumnName.ToLower()) > 0)
                        {
                            PropertyInfo pInfo = arPropertyInfo_T.Single(r => r.Name == col.ColumnName);
                            //设置行值
                            row[col] = pInfo.GetValue(model_T, null);
                        }
                    }
                }
            }
            return row;
        }

        /// <summary>
        /// 从实例T中复制到DataRow中
        /// </summary>
        /// <returns></returns>
        public DataRow CloneDataRowValue(DataRow row_T1, DataRow row_T2, IDictionary<string, object> dicPairValue)
        {
            if (row_T1 != null && row_T2 != null)
            {
                //获得所有列集合
                DataColumn[] arColumn_T1 = row_T1.Table.Columns.Cast<DataColumn>().ToArray();
                DataColumn[] arColumn_T2 = row_T2.Table.Columns.Cast<DataColumn>().ToArray();

                foreach (DataColumn col_T2 in arColumn_T2)
                {
                    //设置默认值
                    row_T2[col_T2] = col_T2.DefaultValue;

                    //判断是否存在值对
                    if (dicPairValue != null && (dicPairValue.ContainsKey(col_T2.ColumnName)))
                    {
                        row_T2[col_T2] = dicPairValue[col_T2.ColumnName];
                    }
                    else
                    {
                        //判断属性是否存在
                        if (arColumn_T1.Count(r => r.ColumnName.ToLower() == col_T2.ColumnName.ToLower()) > 0)
                        {
                            //设置行值
                            row_T2[col_T2] = row_T1[col_T2.ColumnName];
                        }
                    }
                }
            }
            return row_T2;
        }

        #endregion 

        /// <summary>
        /// 根据数据源获得列表
        /// </summary>
        /// <returns></returns>
        public List<T> GetListFromDataTable<T>(DataTable tableSource,Dictionary<string ,object> dicDefaultValue)
        { 
            List<T> list = new List<T>();
            foreach (DataRow row in tableSource.Rows)
            {
                T t = Activator.CreateInstance<T>();
                t = this.CloneModelValue<T>(row, t, dicDefaultValue);
                list.Add(t);
            }
            return list;
        }

        /// <summary>
        /// 根据数据源获得列表
        /// </summary>
        /// <returns></returns>
        public List<T> GetListFromDataTable<T>(DataRowView[] drvArray, Dictionary<string, object> dicDefaultValue)
        {
            List<T> list = new List<T>();
            foreach (DataRowView row in drvArray)
            {
                T t = Activator.CreateInstance<T>();
                t = this.CloneModelValue<T>(row.Row, t, dicDefaultValue);
                list.Add(t);
            }
            return list;
        }

   

        #region    ------XML与DataTable转换

        /// <summary>
        /// 根据XML获得DataSet
        /// </summary>
        /// <returns></returns>
        public DataTable GetDataTableFromXml(string XmlContent)
        {
            StringReader StrStream = null;
            XmlTextReader Xmlrdr = null;
            try
            {
                DataSet ds = new DataSet();
                //读取字符串中的信息  
                StrStream = new StringReader(XmlContent);
                //获取StrStream中的数据  
                Xmlrdr = new XmlTextReader(StrStream);
                //ds获取Xmlrdr中的数据                  
                ds.ReadXml(Xmlrdr);
                return (ds.Tables.Count == 0 ? null : ds.Tables[0]);
            }
            catch (Exception e)
            {
                throw e;
            }
            finally
            {
                //释放资源  
                if (Xmlrdr != null)
                {
                    Xmlrdr.Close();
                    StrStream.Close();
                    StrStream.Dispose();
                }
            }
        }

        /// <summary>
        /// 根据DataTable获得Xml
        /// </summary>
        public string GetXmlFromDataTable(DataTable tableXml)
        {
            if (tableXml != null)
            {
                MemoryStream ms = null;
                XmlTextWriter XmlWt = null;
                try
                {
                    ms = new MemoryStream();
                    //根据ms实例化XmlWt  
                    XmlWt = new XmlTextWriter(ms, Encoding.Unicode);
                    //获取ds中的数据  
                    tableXml.WriteXml(XmlWt);
                    int count = (int)ms.Length;
                    byte[] temp = new byte[count];
                    ms.Seek(0, SeekOrigin.Begin);
                    ms.Read(temp, 0, count);
                    //返回Unicode编码的文本  
                    UnicodeEncoding ucode = new UnicodeEncoding();
                    string returnValue = ucode.GetString(temp).Trim();
                    return returnValue;
                }
                catch (System.Exception ex)
                {
                    throw ex;
                }
                finally
                {
                    //释放资源  
                    if (XmlWt != null)
                    {
                        XmlWt.Close();
                        ms.Close();
                        ms.Dispose();
                    }
                }
            }
            else
            {
                return "";
            }
        }

        #endregion
    }
}
