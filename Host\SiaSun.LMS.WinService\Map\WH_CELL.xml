﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="WH_CELL" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="WH_CELL" type="SiaSun.LMS.Model.WH_CELL, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="WH_CELL">
      <result property="WAREHOUSE_ID" column="warehouse_id" />
      <result property="CELL_ID" column="cell_id" />
      <result property="AREA_ID" column="area_id" />
      <result property="LOGIC_ID" column="logic_id" />
      <result property="CELL_NAME" column="cell_name" />
      <result property="CELL_CODE" column="cell_code" />
      <result property="CELL_TYPE" column="cell_type" />
      <result property="DEVICE_CODE" column="device_code" />
      <result property="CELL_Z" column="cell_z" />
      <result property="CELL_X" column="cell_x" />
      <result property="CELL_Y" column="cell_y" />
      <result property="CELL_INOUT" column="cell_inout" />
      <result property="CELL_MODEL" column="cell_model" />
      <result property="CELL_STATUS" column="cell_status" />
      <result property="RUN_STATUS" column="run_status" />
      <result property="CELL_FORK_TYPE" column="cell_fork_type" />
      <result property="CELL_LOGICAL_NAME" column="cell_logical_name" />
      <result property="LANE_WAY" column="lane_way" />
      <result property="CELL_GROUP" column="cell_group" />
      <result property="CELL_FLAG" column="cell_flag" />
      <result property="SHELF_TYPE" column="shelf_type" />
      <result property="SHELF_NEIGHBOUR" column="shelf_neighbour" />
      <result property="CELL_STORAGE_TYPE" column="cell_storage_type" />
      <result property="CELL_WIDTH" column="cell_width" />
      <result property="CELL_HEIGHT" column="cell_height" />
      <result property="LOCK_DEVICE_CODE" column="lock_device_code" />
      <result property="CELL_PROPERTY" column="cell_property" />

      <result property="CELL_SCORE" column="cell_score" />
      <result property="SCORE_UPDATE_TIME" column="score_update_time" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="WH_CELL_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      cell_id,
      warehouse_id,
      area_id,
      logic_id,
      cell_name,
      cell_code,
      cell_type,
      device_code,
      cell_z,
      cell_x,
      cell_y,
      cell_inout,
      cell_model,
      cell_status,
      run_status,
      cell_fork_type,
      cell_logical_name,
      lane_way,
      cell_group,
      shelf_type,
      shelf_neighbour,
      cell_storage_type,
      lock_device_code,
      cell_width,
      cell_height,
      cell_property,
      cell_flag,
      cell_score,
      score_update_time
      From WH_CELL
    </select>

    <select id="WH_CELL_SELECT_BY_ID" parameterClass="int" extends = "WH_CELL_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          cell_id=#CELL_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_LOCK_DEVICE_CODE" parameterClass="string" extends = "WH_CELL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          LOCK_DEVICE_CODE=#LOCK_DEVICE_CODE# ORDER BY CELL_X DESC
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_AREA_ID_CELL_CODE" parameterClass="Hashtable" extends = "WH_CELL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          area_id = #AREA_ID# AND cell_code=#CELL_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_DEVICE_CODE_AND_PLC_NO" parameterClass="Hashtable" extends = "WH_CELL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          cell_id in (select wh_cell_id from t_pick_position where dzbq_property2=#DEVICE_CODE# and dzbq_property3=#PLC_NO#)
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_CELL_CODE" parameterClass="String" extends = "WH_CELL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          cell_code=#CELL_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_DEVICE_CODE" parameterClass="string" extends = "WH_CELL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          DEVICE_CODE=#DEVICE_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_WAREHOUSE_ID_CELL_Z" parameterClass="Hashtable" extends = "WH_CELL_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          warehouse_id=#WAREHOUSE_ID# and cell_z=#CELL_Z# order by CELL_X,CELL_Y ASC
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="WH_CELL_SELECT_BY_AREA_ID_CELL_Z" parameterClass="Hashtable" extends = "WH_CELL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          area_id = #AREA_ID# AND cell_z=#CELL_Z# and cell_flag='1'
        </isParameterPresent>
      </dynamic>
    </select>


    <select id="WH_CELL_SELECT_BY_ALL" parameterClass="Hashtable" extends = "WH_CELL_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          area_id=#AREA_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <insert id="WH_CELL_INSERT" parameterClass="WH_CELL">
      Insert Into WH_CELL (
      cell_id,
      warehouse_id,
      area_id,
      logic_id,
      cell_name,
      cell_code,
      cell_type,
      device_code,
      cell_z,
      cell_x,
      cell_y,
      cell_inout,
      cell_model,
      cell_status,
      run_status,
      cell_fork_type,
      cell_logical_name,
      lane_way,
      cell_group,
      shelf_type,
      shelf_neighbour,
      cell_storage_type,
      lock_device_code,
      cell_width,
      cell_height,
      cell_property,
      cell_flag,
      cell_score,
      score_update_time
      )Values(
      #CELL_ID#,
      #WAREHOUSE_ID#,
      #AREA_ID#,
      #LOGIC_ID#,
      #CELL_NAME#,
      #CELL_CODE#,
      #CELL_TYPE#,
      #DEVICE_CODE#,
      #CELL_Z#,
      #CELL_X#,
      #CELL_Y#,
      #CELL_INOUT#,
      #CELL_MODEL#,
      #CELL_STATUS#,
      #RUN_STATUS#,
      #CELL_FORK_TYPE#,
      #CELL_LOGICAL_NAME#,
      #LANE_WAY#,
      #CELL_GROUP#,
      #SHELF_TYPE#,
      #SHELF_NEIGHBOUR#,
      #CELL_STORAGE_TYPE#,
      #LOCK_DEVICE_CODE#,
      #CELL_WIDTH#,
      #CELL_HEIGHT#,
      #CELL_PROPERTY#,
      #CELL_FLAG#,
      #CELL_SCORE#,
      #SCORE_UPDATE_TIME#
      )
      <!--<selectKey  resultClass="int" type="post" property="CELL_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="WH_CELL_UPDATE" parameterClass="WH_CELL">
      Update WH_CELL Set
      cell_id=#CELL_ID#,
      warehouse_id=#WAREHOUSE_ID#,
      area_id=#AREA_ID#,
      logic_id=#LOGIC_ID#,
      cell_name=#CELL_NAME#,
      cell_code=#CELL_CODE#,
      cell_type=#CELL_TYPE#,
      device_code=#DEVICE_CODE#,
      cell_z=#CELL_Z#,
      cell_x=#CELL_X#,
      cell_y=#CELL_Y#,
      cell_inout=#CELL_INOUT#,
      cell_model=#CELL_MODEL#,
      cell_status=#CELL_STATUS#,
      run_status=#RUN_STATUS#,
      cell_fork_type=#CELL_FORK_TYPE#,
      cell_logical_name=#CELL_LOGICAL_NAME#,
      lane_way=#LANE_WAY#,
      cell_group=#CELL_GROUP#,
      shelf_type=#SHELF_TYPE#,
      shelf_neighbour=#SHELF_NEIGHBOUR#,
      cell_storage_type=#CELL_STORAGE_TYPE#,
      lock_device_code=#LOCK_DEVICE_CODE#,
      cell_width=#CELL_WIDTH#,
      cell_height=#CELL_HEIGHT#,
      cell_property=#CELL_PROPERTY#,
      cell_flag=#CELL_FLAG#,      
      cell_score=#CELL_SCORE#,
      score_update_time=#SCORE_UPDATE_TIME#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          cell_id=#CELL_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="WH_CELL_DELETE" parameterClass="int">
      Delete From WH_CELL
      <dynamic prepend="WHERE">
        <isParameterPresent>
          cell_id=#CELL_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>