﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class PlanImport:S_BaseService
    {
        public PlanImport()
        {
        }

        public bool PlanCreate(Model.PLAN_MAIN mPLAN_MAIN, IList<Model.PLAN_LIST> lsPLAN_LIST, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int iReturn = 0;

            try
            {
                this._P_Base_House.BeginTransaction();

                if (null != this._P_PLAN_MAIN.GetModelPlanCode(mPLAN_MAIN.PLAN_CODE))
                {
                    bResult = false;

                    sResult = string.Format("{0}单号已经存在!", mPLAN_MAIN.PLAN_CODE);

                    return bResult;
                }

                this._P_PLAN_MAIN.Add(mPLAN_MAIN);

                foreach (Model.PLAN_LIST mPLAN_LIST in lsPLAN_LIST)
                {
                    if (mPLAN_LIST.PLAN_ID.Equals(0))
                    {
                        mPLAN_LIST.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                        this._P_PLAN_LIST.Add(mPLAN_LIST);
                    }
                    else
                    {
                        this._P_PLAN_LIST.Update(mPLAN_LIST);
                    }
                }
                iReturn = this.ExecuteNonQuery_ReturnInt(string.Format(" update u5wmcsinterface set wmi_status = '3' where wmi_taskno = '{0}'", mPLAN_MAIN.PLAN_CODE), "ERPMap");
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (iReturn > 0)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        //public bool GoodsCreate(SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN, out string sResult)
        //{

        //    bool bResult = true;

        //    sResult = string.Empty;

        //    int iReturn=  this._P_GOODS_MAIN.Add(mGOODS_MAIN);


        //    if (iReturn == 0)
        //    {
        //        sResult = "导入ERP物料失败!";

        //        bResult = false;
        //    }

        //    return bResult;

        //}

        public bool GoodsCreate(DataTable dt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int rowaffect = 0;

            foreach (DataRow dr in dt.Rows)
            {
                if (string.IsNullOrEmpty(dr["物资编码"].ToString()) || string.IsNullOrEmpty(dr["域"].ToString()) || string.IsNullOrEmpty(dr["会计组"].ToString()))
                    continue;

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(dr["物资编码"].ToString(), dr["域"].ToString());

                if (mGOODS_MAIN == null)
                {
                    Model.GOODS_CLASS mGOODS_CLASS = this._P_GOODS_CLASS.GetModel(dr["会计组"].ToString());

                    if (mGOODS_CLASS != null)
                    {
                        mGOODS_MAIN = new Model.GOODS_MAIN();

                        mGOODS_MAIN.GOODS_CLASS_ID = mGOODS_CLASS.GOODS_CLASS_ID;

                        mGOODS_MAIN.GOODS_CODE = dr["物资编码"].ToString();

                        mGOODS_MAIN.GOODS_NAME = dr["物资名称"].ToString();

                        mGOODS_MAIN.GOODS_UNITS = dr["单位"].ToString();

                        mGOODS_MAIN.GOODS_CONST_PROPERTY1 = dr["规格型号"].ToString();

                        mGOODS_MAIN.GOODS_CONST_PROPERTY3 = dr["域"].ToString();

                        mGOODS_MAIN.GOODS_FLAG = "1";

                        rowaffect += this._P_GOODS_MAIN.Add(mGOODS_MAIN);
                    }
                }
            }
            
            if (rowaffect == 0)
            {
                sResult = "导入ERP物料失败!";

                bResult = false;
            }
            else
            {
                sResult = string.Format("导入{0}条物料成功!",rowaffect);
            }

            return bResult;
        }       
    }
}
