﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SiaSun.LMS.WPFClient.MVVM;
using SiaSun.LMS.Model;

using System.Collections.ObjectModel;
using System.Windows.Input;
//using SiaSun.LMS.WPFClient.PICK.ViewModel.Box;
using SiaSun.LMS.WPFClient.PICK.Model;
using System.Windows;
using System.Data;
using System.Threading;
using System.Threading.Tasks;

using System.Drawing.Printing;
using System.Drawing;
using Microsoft.AspNet.SignalR.Client.Hubs;

namespace SiaSun.LMS.WPFClient.PICK.ViewModel
{
    public class PickStationAutoBindNewViewModel : ObservableObject
    {
        private T_PICK_STATION mT_PICK_STATION;
        private string _stationName;
        private STORAGE_MAIN mSTORAGE_MAIN_STATION = new STORAGE_MAIN();
        private List<STORAGE_LIST> mSTORAGE_LISTS = new List<STORAGE_LIST>();
        private MANAGE_MAIN pickMANAGE_MAIN = new MANAGE_MAIN();
        private List<MANAGE_LIST> pickMANAGE_LISTS = new List<MANAGE_LIST>();
        private List<T_PICK_POSITION> T_PICK_POSITIONs = new List<T_PICK_POSITION>();
        private ObservableCollection<PickPositionNewViewModel> _PickPositionViewModels = new ObservableCollection<PickPositionNewViewModel>();
        private ObservableCollection<Information> _informations = new ObservableCollection<Information>();
        private DataTable _OrderDataTable = new DataTable();

        private string _OrderCode;//订单号
        private string _PlanGroup;//唯一码
        private string _PlanGroupToBind;//需要绑定的订单
        public string STATION_IP = Common.StringUtil.GetIpAddress();
        private CancellationTokenSource tokenSource = new CancellationTokenSource();
        private AvalonDock.DocumentContent windowView;
        #region print define
        public PrintDocument printDocumentForPick = new PrintDocument();
        public PrinterHelper printHelperForPick = new PrinterHelper();
        private int width_p = 100;//单位是mm
        private int height_p = 50;//单位是mm
        private int margin_lr = 2;//左右边距
        private int margin_tb = 2;//上下边距

        private string goods_code;
        private string goods_name;
        private string goods_quantity;
        private string goods_units;
        private string wbs_code;

        private string plan_to_user;//2020-10-27 增加打印集货号功能

        private string plan_relatedbill;// PLAN_RELATEDBILL;//2020-12-27 增加派工单
        private string plan_shopno;// PLAN_SHOPNO 2020-12-27 增加车间
        private string plan_workingseatl;// PLAN_WORKINGSEATL 2020-12-27 增加工位
        private string plan_projectcode; //PLAN_PROJECT_CODE 2020-12-27 增加项目号
        //private string plan_from_dept; //PLAN_FROM_DEPT 2020-12-27 增加需求日期
        private string backup_field1; //BACKUP_FIELD 2021-01-16 10:08:56 MES需求日期

        private string plan_list_workseat;//工位 PLAN_LIST.GOODS_PROPERTY3
        private string plan_list_workorder;//工序 PLAN_LIST.GOODS_PROPERTY4

        private string _invFollow = string.Empty;           //质量追溯码  2025-06-26新增

        private string wbs_description;
        private string wbs_headtext;

        private string usercode;
        private string username;
        #endregion

        private string _PlanHanderText = "----";

        #region sn define
        private string _ScanSN;
        private List<string> _SNList = new List<string>();
        #endregion

        #region error_operation_define
        private string _CheckStringForOperation;
        private bool _ErrerOperationShow;
        #endregion

        #region AutoBindDefine
        private Message.MessageSrv messageSrv;
        private System.Windows.Threading.Dispatcher dispather4AutoBind;
        public event EventHandler PlayDingDanRequested;
        private bool _ConnectLoginBtnIsEnabled = true;
        private string _LoginStatus = "已离线";

        public bool ConnectLoginBtnIsEnabled
        {
            get
            {
                return _ConnectLoginBtnIsEnabled;
            }

            set
            {
                if (_ConnectLoginBtnIsEnabled != value)
                {
                    _ConnectLoginBtnIsEnabled = value;
                    RaisePropertyChanged("ConnectLoginBtnIsEnabled");
                }
            }
        }

        public string LoginStatus
        {
            get
            {
                return _LoginStatus;
            }

            set
            {
                if (_LoginStatus != value)
                {
                    _LoginStatus = value;
                    RaisePropertyChanged("LoginStatus");
                }
            }
        }

        #endregion
        public ObservableCollection<PickPositionNewViewModel> PickPositionViewModels
        {
            get
            {
                return _PickPositionViewModels;
            }

            set
            {
                if (_PickPositionViewModels != value)
                {
                    _PickPositionViewModels = value;
                    RaisePropertyChanged("PickPositionViewModels");
                }
            }
        }

        private ObservableCollection<Model.Box> _Boxs = new ObservableCollection<Model.Box>();

        public ObservableCollection<Model.Box> Boxs
        {
            get
            {
                return _Boxs;
            }

            set
            {
                if (_Boxs != value)
                {
                    _Boxs = value;
                    RaisePropertyChanged("Boxs");
                }
            }
        }

        public ObservableCollection<Information> Informations
        {
            get
            {
                return _informations;
            }

            set
            {
                if (_informations != value)
                {
                    _informations = value;
                    RaisePropertyChanged("Informations");
                }
            }
        }

        public string StationName
        {
            get
            {
                return _stationName;
            }

            set
            {
                if (_stationName != value)
                {
                    _stationName = value;
                    RaisePropertyChanged("StationName");
                }
            }
        }

        public List<STORAGE_LIST> STORAGE_LISTS
        {
            get
            {
                return mSTORAGE_LISTS;
            }

            set
            {
                if (mSTORAGE_LISTS != value)
                {
                    mSTORAGE_LISTS = value;
                    RaisePropertyChanged("STORAGE_LISTS");
                }
            }
        }

        public List<MANAGE_LIST> MANAGE_LISTS
        {
            get
            {
                return pickMANAGE_LISTS;
            }

            set
            {
                if (pickMANAGE_LISTS != value)
                {
                    pickMANAGE_LISTS = value;
                    RaisePropertyChanged("MANAGE_LISTS");
                }
            }
        }

        public DataTable OrderDataTable
        {
            get
            {
                return _OrderDataTable;
            }

            set
            {
                if (_OrderDataTable != value)
                {
                    _OrderDataTable = value;
                    RaisePropertyChanged("OrderDataTable");
                }
            }
        }

        /// <summary>
        /// 拣选工作站绑定的订单号
        /// </summary>
        public string OrderCode
        {
            get
            {
                return _OrderCode;
            }

            set
            {
                if (_OrderCode != value)
                {
                    _OrderCode = value;
                    RaisePropertyChanged("OrderCode");
                }
            }
        }

        /// <summary>
        /// 拣选工作站绑定的订单的唯一码
        /// </summary>
        public string PlanGroup
        {
            get
            {
                return _PlanGroup;
            }

            set
            {
                if (_PlanGroup != value)
                {
                    _PlanGroup = value;
                    RaisePropertyChanged("PlanGroup");
                }
            }
        }

        /// <summary>
        /// 拣选工作站绑定的订单的唯一码
        /// </summary>
        public string PlanGroupToBind
        {
            get
            {
                return _PlanGroupToBind;
            }

            set
            {
                if (_PlanGroupToBind != value)
                {
                    _PlanGroupToBind = value;
                    RaisePropertyChanged("PlanGroupToBind");
                }
            }
        }

        public string ScanSN
        {
            get
            {
                return _ScanSN;
            }

            set
            {
                if (_ScanSN != value)
                {
                    _ScanSN = value;
                    RaisePropertyChanged("ScanSN");
                }
            }
        }

        public List<string> SNList
        {
            get
            {
                return _SNList;
            }

            set
            {
                if (_SNList != value)
                {
                    _SNList = value;
                    RaisePropertyChanged("SNList");
                }
            }
        }

        /// <summary>
        /// 唯一码 下某一个拣选计划的抬头文本
        /// 相同唯一码下拣选计划抬头文本相同
        /// </summary>
        public string PlanHanderText
        {
            get
            {
                return _PlanHanderText;
            }

            set
            {
                if (_PlanHanderText != value)
                {
                    _PlanHanderText = value;
                    RaisePropertyChanged("PlanHanderText");
                }
            }
        }

        public string CheckStringForOperation
        {
            get
            {
                return _CheckStringForOperation;
            }

            set
            {
                if (_CheckStringForOperation != value)
                {
                    _CheckStringForOperation = value;
                    RaisePropertyChanged("CheckStringForOperation");
                }
            }
        }

        public bool ErrerOperationShow
        {
            get
            {
                return _ErrerOperationShow;
            }

            set
            {
                if (_ErrerOperationShow != value)
                {
                    _ErrerOperationShow = value;
                    RaisePropertyChanged("ErrerOperationShow");
                }
            }
        }

        #region PrevBinding
        private bool showPrevBinding = false;
        private string prevBindingPlanGroup = string.Empty;
        private DataTable prevBindingOrderDataTable = new DataTable();
        private string planGroupToPrevBinding = string.Empty;

        /// <summary>
        /// 会否显示预绑定菜单
        /// </summary>
        public bool IsShowPrevToolBar
        {
            get
            {
                return showPrevBinding;
            }
            set
            {
                if (showPrevBinding != value)
                {
                    showPrevBinding = value;
                    RaisePropertyChanged("IsShowPrevToolBar");
                }
            }
        }

        /// <summary>
        /// 工作站绑定的预绑定订单唯一码
        /// </summary>
        public string PrevBindingPlanGroup
        {
            get
            {
                return prevBindingPlanGroup;
            }
            set
            {
                if (prevBindingPlanGroup != value)
                {
                    prevBindingPlanGroup = value;
                    RaisePropertyChanged("PrevBindingPlanGroup");
                }
            }
        }

        /// <summary>
        /// 预绑定订单唯一码池
        /// </summary>
        public DataTable PrevBindingOrderDataTable
        {
            get
            {
                return prevBindingOrderDataTable;
            }
            set
            {
                if (prevBindingOrderDataTable != value)
                {
                    prevBindingOrderDataTable = value;
                    RaisePropertyChanged("PrevBindingOrderDataTable");
                }
            }
        }

        /// <summary>
        /// 预绑定唯一码
        /// </summary>
        public string PlanGroupToPrevBinding
        {
            get
            {
                return planGroupToPrevBinding;
            }

            set
            {
                if (planGroupToPrevBinding != value)
                {
                    planGroupToPrevBinding = value;
                    RaisePropertyChanged("PlanGroupToPrevBinding");
                }
            }
        }
        #endregion


        #region 二期反向
        private FlowDirection _StationFlowDirection;
        private string _Direction = "normal";
        public FlowDirection StationFlowDirection
        {
            get
            {
                return _StationFlowDirection;
            }

            set
            {
                if (_StationFlowDirection != value)
                {
                    _StationFlowDirection = value;
                    RaisePropertyChanged("StationFlowDirection");
                }
            }
        }
        #endregion
        public PickStationAutoBindNewViewModel(FrameworkElement view)
        {
            //this.STATION_IP = "************";

            string sResult = string.Empty;
            ErrerOperationShow = false;

            #region registe msg
            Message.MainRegister mPickStationMsgRegister = new Message.MainRegister();
            mPickStationMsgRegister.RegInstance = view;
            var win = view as AvalonDock.DocumentContent;
            this.windowView = win;
            if (win == null)
            {
                throw new Exception("only can set a Window's message as global!");
            }
            this.MsgManager = MVVM.Message.MessageManager.Default;
            win.Closed += MVVM.Message.MessageManager.Default.WindowClose;
            win.Closing += Win_Closing;
            mPickStationMsgRegister.MsgManager = this.MsgManager;
            mPickStationMsgRegister.Register();
            #endregion

            var dispatcher = view.Dispatcher;

            if (!string.IsNullOrEmpty(STATION_IP))
            {
                mT_PICK_STATION = MainApp._I_StorageService.Get_T_PICK_STATION_BY_IP(STATION_IP, out sResult);

                if (mT_PICK_STATION != null && mT_PICK_STATION.PROPERTY9 == "1")
                {
                    StationName = mT_PICK_STATION.STATION_NAME;

                    //一期
                    this._StationFlowDirection = FlowDirection.LeftToRight;

                    int PlcNo;
                    if (int.TryParse(mT_PICK_STATION.PROPERTY1, out PlcNo))
                    {
                        if (PlcNo > 3)
                        {
                            //二期
                            this._Direction = "reverse";
                            this._StationFlowDirection = FlowDirection.RightToLeft;
                        }
                    }

                    #region auto_bind
                    messageSrv = new Message.MessageSrv();
                    dispather4AutoBind = view.Dispatcher;
                    messageSrv.ConnectionClosed += MessageSrv_ConnectionClosed;
                    messageSrv.NewTextMessage += MessageSrv_NewTextMessage;
                    #endregion

                    this._OrderCode = mT_PICK_STATION.PLAN_GROUP_NAME;//订单号
                    //this._PlanGroup = mT_PICK_STATION.PLAN_GROUP_CODE;//唯一码
                    this._PlanGroup = mT_PICK_STATION.PLAN_GROUP_CODE;

                    //this._OrderDataTable =
                    //    MainApp._I_BaseService.GetList(@"select DISTINCT PLAN_GROUP AS NAME,PLAN_GROUP AS VALUE from PLAN_MAIN t
                    //                                                     WHERE PLAN_TYPE_CODE ='PlanPick' and PLAN_GROUP IS NOT NULL and PLAN_STATUS='Waiting'");
                    this._OrderDataTable =
                        MainApp._I_BaseService.GetList(@"select DISTINCT PLAN_GROUP AS NAME,PLAN_GROUP AS VALUE from PLAN_MAIN t
                                                                         WHERE PLAN_TYPE_CODE ='PlanPick' and PLAN_GROUP IS NOT NULL and PLAN_STATUS='Waiting'
                                                                and PLAN_GROUP not in (select plan_group_code from t_pick_station where length(plan_group_code)>0)");


                    #region print test
                    this.InitPrintSetting();
                    #endregion

                    var token = tokenSource.Token;
                    Task.Factory.StartNew(() =>
                    {

                        while (mT_PICK_STATION != null)
                        {
                            dispatcher.BeginInvoke(new Action(UpdateForDispather));
                            System.Threading.Thread.Sleep(1500);

                            if (token.IsCancellationRequested)
                            {
                                return;
                            }
                        }
                    }, token
                    );

                    //Task.Factory.StartNew(() =>
                    //{
                    //    while (mT_PICK_STATION != null)
                    //    {
                    //        dispatcher.BeginInvoke(new Action(UpdateForOrderDataTable));
                    //        System.Threading.Thread.Sleep(15000);

                    //        if (token.IsCancellationRequested)
                    //        {
                    //            return;
                    //        }
                    //    }
                    //}, token
                    //);

                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "本工作站已设置为旧拣选模式，不可进入新拣选工作站页面!");
                }
            }
        }



        private void Win_Closing(object sender, System.ComponentModel.CancelEventArgs e)
        {
            tokenSource.Cancel();

            //关闭连接，退出登录
            this.DisconnectNdLogout();
        }

        public void UpdateForOrderDataTable()
        {
            try
            {


                //this.OrderDataTable =
                //            MainApp._I_BaseService.GetList(@"select DISTINCT PLAN_GROUP AS NAME,PLAN_GROUP AS VALUE from PLAN_MAIN t
                //                                                             WHERE PLAN_TYPE_CODE ='PlanPick' and PLAN_GROUP IS NOT NULL and PLAN_STATUS='Waiting'");
                //this.OrderDataTable =
                //           MainApp._I_BaseService.GetList(@"select DISTINCT PLAN_GROUP AS NAME,PLAN_GROUP AS VALUE from PLAN_MAIN t
                //                                                             WHERE PLAN_TYPE_CODE ='PlanPick' and PLAN_GROUP IS NOT NULL and PLAN_STATUS='Waiting'
                //                                                    and PLAN_GROUP not in (select plan_group_code from t_pick_station where length(plan_group_code)>0)");
                this.OrderDataTable =
                           MainApp._I_BaseService.GetList(@"select DISTINCT PLAN_GROUP AS NAME,PLAN_GROUP AS VALUE,PlanLevel FROM (select PLAN_GROUP,max(PLAN_LEVEL) AS PlanLevel from PLAN_MAIN t
WHERE PLAN_TYPE_CODE ='PlanPick' and PLAN_GROUP IS NOT NULL and PLAN_STATUS='Waiting'
and PLAN_GROUP not in (select plan_group_code from t_pick_station where length(plan_group_code)>0)
group by plan_group) 
ORDER BY PlanLevel DESC,PLAN_GROUP ASC");

                this.PrevBindingOrderDataTable = this.OrderDataTable;

            }
            catch (Exception ex)
            {
                return;
            }
        }
        public void UpdateForDispather()
        {
            bool bResult = true;
            string sResult = string.Empty;

            #region update
            try
            {
                mT_PICK_STATION = MainApp._I_StorageService.Get_T_PICK_STATION_BY_IP(STATION_IP, out sResult);

                if (mT_PICK_STATION != null)
                {
                    this.PlanGroup = mT_PICK_STATION.PLAN_GROUP_CODE;

                    DataTable wDataT = MainApp._I_BaseService.GetList(string.Format(@"select DISTINCT PLAN_HEADTEXT from PLAN_MAIN t
                                                                         WHERE PLAN_TYPE_CODE ='PlanPick' and PLAN_GROUP IS NOT NULL and PLAN_GROUP='{0}'", mT_PICK_STATION.PLAN_GROUP_CODE));
                    if (wDataT != null && wDataT.Rows.Count > 0)
                    {
                        this.PlanHanderText = wDataT.Rows[0]["PLAN_HEADTEXT"].ToString();
                    }
                    else
                    {
                        this.PlanHanderText = "抬头文本为空";
                    }



                    //预绑定
                    this.PrevBindingPlanGroup = mT_PICK_STATION.STATION_MAC;

                    T_PICK_POSITIONs = MainApp._I_StorageService.
                    Get_T_PICK_POSITION_BY_STATION_ID(mT_PICK_STATION.STATION_ID, out sResult).OrderBy((s) => s.POSITION_ID).ToList();

                    this.InitPickPostionViewModels(T_PICK_POSITIONs);
                    SiaSun.LMS.Model.MANAGE_MAIN alreadyPrintPickManageMain = pickMANAGE_MAIN;
                    bResult = MainApp._I_StorageService.Get_PICK_STATION_STORAGE_BY_STATION_ID(
                        mT_PICK_STATION.STATION_ID, out mSTORAGE_MAIN_STATION, out mSTORAGE_LISTS, out pickMANAGE_MAIN, out pickMANAGE_LISTS, out sResult);

                    if (bResult)
                    {
                        AddBox(mSTORAGE_MAIN_STATION);
                        BindInformation();
                        if (pickMANAGE_LISTS != null)
                        {
                            SwitchSelectLight(pickMANAGE_LISTS[0].BOX_BARCODE);
                        }
                        try
                        {
                            if (pickMANAGE_MAIN != null & alreadyPrintPickManageMain != null)
                            {
                                if (!pickMANAGE_MAIN.MANAGE_ID.Equals(alreadyPrintPickManageMain.MANAGE_ID))
                                {
                                    if (this.pickMANAGE_LISTS == null)
                                    {
                                        return;
                                    }
                                    if (this.pickMANAGE_LISTS.Count == 0)
                                    {
                                        return;
                                    }
                                    printDocumentForPick.Print();

                                }
                            }
                            else if (pickMANAGE_MAIN != null & alreadyPrintPickManageMain == null)
                            {
                                if (this.pickMANAGE_LISTS == null)
                                {
                                    return;
                                }
                                if (this.pickMANAGE_LISTS.Count == 0)
                                {
                                    return;
                                }
                                printDocumentForPick.Print();
                            }

                        }
                        catch (Exception ex)
                        {
                            return;
                        }

                    }
                    else
                    {
                        if (mSTORAGE_MAIN_STATION == null)
                        {
                            this._Boxs.Clear();
                        }
                        if (pickMANAGE_LISTS == null)
                        {
                            this._informations.Clear();
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                return;
            }

            #endregion
        }

        public void AddBox(SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN)
        {
            Model.Box aBox = new Model.Box();
            if (!string.IsNullOrEmpty(mSTORAGE_MAIN.CELL_MODEL))
            {
                switch (mSTORAGE_MAIN.CELL_MODEL)
                {
                    case "1":
                        aBox = new Model.BoxTypeA(mSTORAGE_MAIN.STOCK_BARCODE);
                        if (this._Direction == "reverse")
                        {
                            aBox = new Model.BoxTypeAR(mSTORAGE_MAIN.STOCK_BARCODE);
                        }
                        break;
                    case "2":
                        aBox = new Model.BoxTypeB(mSTORAGE_MAIN.STOCK_BARCODE);
                        if (this._Direction == "reverse")
                        {
                            aBox = new Model.BoxTypeBR(mSTORAGE_MAIN.STOCK_BARCODE);
                        }
                        break;
                    case "3":
                        aBox = new Model.BoxTypeC(mSTORAGE_MAIN.STOCK_BARCODE);
                        if (this._Direction == "reverse")
                        {
                            aBox = new Model.BoxTypeCR(mSTORAGE_MAIN.STOCK_BARCODE);
                        }
                        break;
                    case "4":
                        aBox = new Model.BoxTypeD(mSTORAGE_MAIN.STOCK_BARCODE);
                        if (this._Direction == "reverse")
                        {
                            aBox = new Model.BoxTypeDR(mSTORAGE_MAIN.STOCK_BARCODE);
                        }
                        break;
                    case "6":
                        aBox = new Model.BoxTypeE(mSTORAGE_MAIN.STOCK_BARCODE);
                        if (this._Direction == "reverse")
                        {
                            aBox = new Model.BoxTypeER(mSTORAGE_MAIN.STOCK_BARCODE);
                        }
                        break;

                }

                if (aBox != null)
                {
                    this._Boxs.Clear();
                    this._Boxs.Add(aBox);
                }

            }
        }


        #region Command
        void ABOXExecute()
        {
            //AddBox("A666888");

            SwitchSelectLight("4");
            //MsgManager.SendMsg("ShowBox", "这是提示");

        }

        bool CanABOXExecute()
        {
            return true;
        }

        public ICommand CmdA { get { return new RelayCommand(ABOXExecute, CanABOXExecute); } }


        void BBOXExecute()
        {
            //AddBox("B999555");

            SwitchSelectLight("2");
            //var msg = new MVVM.Message.ConfirmMsgArgs(
            //                "请确认!",
            //                "是否要把当前按钮颜色变成蓝色？");
            //MsgManager.SendMsg("ShowConfirmBox", msg);
            //if (msg.Result)
            //{ MsgManager.SendMsg("ShowBox", "yes"); }
            //else
            //{ MsgManager.SendMsg("ShowBox", "no"); }
        }

        bool CanBBOXExecute()
        {
            return true;
        }

        public ICommand CmdB { get { return new RelayCommand(BBOXExecute, CanBBOXExecute); } }

        /// <summary>
        /// 显示订单绑定信息
        /// </summary>
        void ShowBindInformationExecute()
        {
            string sResult = string.Empty;
            //MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.Dialog.DataGridWindow", "title", new object[] { "", "GOODS_WERKS_LGORT_PROPERTY", "GOODS_WERKS_LGORT_PROPERTY_ID>0", "GOODS_WERKS_LGORT_PROPERTY_ORDER", true, "" }, out sResult);
            Dialog.DataGridDialog dialog = new Dialog.DataGridDialog(mT_PICK_STATION.STATION_NAME + "订单位置绑定情况", "V_PICK_POSITION_BIND", string.Format("PICK_STATION_ID={0}", mT_PICK_STATION.STATION_ID), "PICK_POSITION_PLAN_BIND_ID", true, "");

            dialog.MinWidth = 800;
            dialog.MinHeight = 400;
            dialog.MaxHeight = 700;
            dialog.ShowDialog();
        }
        bool CanShowBindInformationExecute()
        {
            return true;
        }
        public ICommand CmdShowBindInformation { get { return new RelayCommand(ShowBindInformationExecute, CanShowBindInformationExecute); } }

        /// <summary>
        /// 显示被拣选箱库存信息
        /// </summary>
        void ShowStorageInformationExecute()
        {
            string sResult = string.Empty;
            try
            {
                if (mSTORAGE_MAIN_STATION != null)
                {
                    //MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.Dialog.DataGridWindow", "title", new object[] { "", "GOODS_WERKS_LGORT_PROPERTY", "GOODS_WERKS_LGORT_PROPERTY_ID>0", "GOODS_WERKS_LGORT_PROPERTY_ORDER", true, "" }, out sResult);
                    Dialog.DataGridDialog dialog = new Dialog.DataGridDialog(mSTORAGE_MAIN_STATION.STOCK_BARCODE + "库存信息", "V_STORAGE_LIST_4PS", string.Format("STOCK_BARCODE='{0}'", mSTORAGE_MAIN_STATION.STOCK_BARCODE), "BOX_BARCODE", true, "");
                    dialog.MinWidth = 800;
                    dialog.MinHeight = 300;
                    dialog.ShowDialog();
                }

            }
            catch
            {
                return;
            }

        }
        bool CanShowStorageInformationExecute()
        {
            return true;
        }
        public ICommand CmdShowStorageInformation { get { return new RelayCommand(ShowStorageInformationExecute, CanShowStorageInformationExecute); } }

        /// <summary>
        /// 显示被拣选箱库存信息
        /// </summary>
        void ManagePickListExecute()
        {
            string sResult = string.Empty;
            try
            {
                if (mSTORAGE_MAIN_STATION != null)
                {
                    //MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.Dialog.DataGridWindow", "title", new object[] { "", "GOODS_WERKS_LGORT_PROPERTY", "GOODS_WERKS_LGORT_PROPERTY_ID>0", "GOODS_WERKS_LGORT_PROPERTY_ORDER", true, "" }, out sResult);
                    Dialog.DataGridDialog dialog = new Dialog.DataGridDialog(mSTORAGE_MAIN_STATION.STOCK_BARCODE + "拣选任务列表", "V_MANAGE_PICK", string.Format("STOCK_BARCODE='{0}'", mSTORAGE_MAIN_STATION.STOCK_BARCODE), "BOX_BARCODE", true, "");
                    dialog.MinWidth = 800;
                    dialog.MinHeight = 300;
                    dialog.ShowDialog();
                }

            }
            catch
            {
                return;
            }

        }
        bool CanManagePickListExecute()
        {
            return true;
        }
        public ICommand CmdManagePickList { get { return new RelayCommand(ManagePickListExecute, CanManagePickListExecute); } }

        /// <summary>
        /// 将订单绑定到拣选工作站上
        /// done
        /// </summary>
        void BindOrderExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {


                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码对应的订单:{0}绑定至{1}?", this._PlanGroupToBind, mT_PICK_STATION.STATION_NAME));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    if (string.IsNullOrEmpty(this._PlanGroupToBind))
                    {
                        MsgManager.SendMsg("ShowBox", "操作失败:选中的唯一码为空");
                        return;
                    }
                    bResult = MainApp._I_StorageService.BindOrderCodeToPickStationNew2024(MainApp._USER, mT_PICK_STATION.STATION_ID, this._PlanGroupToBind, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }
            this.Update();

        }
        bool CanBindOrderExecute()
        {
            return true;
        }
        public ICommand CmdBindOrder { get { return new RelayCommand(BindOrderExecute, CanBindOrderExecute); } }

        /// <summary>
        /// 拣选工作站上解绑拣选计划
        /// done
        /// </summary>
        void UnBindOrderExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {

                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否解除{0}所绑定的拣选订单,唯一码:{1}?", mT_PICK_STATION.STATION_NAME, this._PlanGroup));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    string group = string.Empty;
                    bResult = MainApp._I_StorageService.UnBindOrderPickStation(MainApp._USER, mT_PICK_STATION.STATION_ID, out group, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }
            this.Update();

        }
        bool CanUnBindOrderExecute()
        {
            return true;
        }
        public ICommand CmdUnBindOrder { get { return new RelayCommand(UnBindOrderExecute, CanUnBindOrderExecute); } }


        /// <summary>
        /// 锁定订单并出库
        /// 只锁定已绑定拣选点的订单
        /// doing
        /// </summary>
        void LockAndOutOrderExecute()
        {
            string sResult = string.Empty;
            bool bResult = false;
            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码:{0}对应的订单锁定至{1}?", this._PlanGroup, mT_PICK_STATION.STATION_NAME));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    bResult = MainApp._I_StorageService.LockAndOutOrderByPickStation(MainApp._USER, mT_PICK_STATION.STATION_ID, this._PlanGroup, out sResult);

                    this.ShowResult(bResult, sResult);
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }
            this.Update();
        }
        bool CanLockAndOutOrderExecute()
        {
            return true;
        }
        public ICommand CmdLockAndOutOrder { get { return new RelayCommand(LockAndOutOrderExecute, CanLockAndOutOrderExecute); } }


        /// <summary>
        /// 锁定订单并出库(允许缺料)
        /// 只锁定已绑定拣选点的订单
        /// doing
        /// </summary>
        void LockAndOutOrderPartlyExecute()
        {
            string sResult = string.Empty;
            bool bResult = false;
            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码:{0}对应的订单锁定至{1}?本次锁定为部分锁定，如果物料短缺，也可以锁定成功!", this._PlanGroup, mT_PICK_STATION.STATION_NAME));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    bResult = MainApp._I_StorageService.LockAndOutOrderPartlyByPickStation(MainApp._USER, mT_PICK_STATION.STATION_ID, this._PlanGroup, out sResult);

                    //this.ShowResult(bResult, sResult);
                    if (bResult)
                    {
                        if (string.IsNullOrEmpty(sResult))
                        {
                            MsgManager.SendMsg("ShowBox", string.Format("锁定库存成功!"));
                        }
                        else
                        {
                            MsgManager.SendMsg("ShowBox", string.Format("锁定库存成功!以下物料缺少库存{0}", sResult));
                        }
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }
            this.Update();
        }
        bool CanLockAndOutOrderPartlyExecute()
        {
            return true;
        }
        public ICommand CmdLockAndOutOrderPartly { get { return new RelayCommand(LockAndOutOrderPartlyExecute, CanLockAndOutOrderPartlyExecute); } }


        /// <summary>
        /// 锁定订单并出库
        /// 只锁定已绑定拣选点的订单
        /// doing
        /// </summary>
        void ManageDownOutOrderExecute()
        {
            string sResult = string.Empty;
            bool bResult = false;
            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码:{0}对应的订单锁定下架至{1}?", this._PlanGroup, mT_PICK_STATION.STATION_NAME));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    bResult = MainApp._I_StorageService.ManageDownOutByPickStation(MainApp._USER, mT_PICK_STATION.STATION_ID, this._PlanGroup, out sResult);

                    this.ShowResult(bResult, sResult);
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }
            this.Update();
        }
        bool CanManageDownOutOrderExecute()
        {
            return true;
        }
        public ICommand CmdManageDownOutOrder { get { return new RelayCommand(ManageDownOutOrderExecute, CanManageDownOutOrderExecute); } }


        void PrintManagePickContentExecute()
        {
            if (this.pickMANAGE_LISTS == null)
            {
                return;
            }
            if (this.pickMANAGE_LISTS.Count == 0)
            {
                return;
            }
            printDocumentForPick.Print();
        }

        bool CanPrintManagePickContentExecute()
        {
            return true;
        }

        public ICommand CmdPrintManagePickContent { get { return new RelayCommand(PrintManagePickContentExecute, CanPrintManagePickContentExecute); } }


        void CloseExecute()
        {
            this.windowView.Close();
        }

        bool CanCloseExecute()
        {
            return true;
        }

        public ICommand CmdClose { get { return new RelayCommand(CloseExecute, CanCloseExecute); } }

        void AddSNToListExecute()
        {
            if (this._SNList.Contains(this._ScanSN))
            {
                return;
            }
            else
            {
                List<string> newSNList = new List<string>();
                if (this._SNList.Count > 0)
                {
                    foreach (string sn in this._SNList)
                    {
                        newSNList.Add(sn);
                    }
                }
                newSNList.Add(this._ScanSN);
                this.SNList = newSNList;
                this.ScanSN = string.Empty;
            }
        }

        bool CanAddSNToListExecute()
        {
            return true;
        }

        public ICommand CmdAddSNToList { get { return new RelayCommand(AddSNToListExecute, CanAddSNToListExecute); } }

        void ClearSNListExecute()
        {
            this.SNList = new List<string>();
            this.ScanSN = string.Empty;
        }

        bool CanClearSNListExecute()
        {
            return true;
        }

        public ICommand CmdClearSNList { get { return new RelayCommand(ClearSNListExecute, CanClearSNListExecute); } }

        void ConfirmSNListExecute()
        {
            if (this.pickMANAGE_LISTS == null)
            {
                this.ShowResult(false, "拣选任务明细为空 EX6001");
                return;
            }
            if (this.pickMANAGE_LISTS.Count == 0)
            {
                this.ShowResult(false, "拣选任务明细为空 EX6002");

                return;
            }

            if (this._SNList.Count == 0)
            {
                this.ShowResult(false, "添加的序列号数量为0 EX6003");

                return;
            }

            string sResult = string.Empty;
            bool bResult = false;
            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否进行SN码绑定?"));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    if (this.pickMANAGE_LISTS[0] == null)
                    {
                        bResult = false;
                        MsgManager.SendMsg("ShowBox", "未找到拣选任务!");
                        return;
                    }
                    List<SiaSun.LMS.Model.MANAGE_DETAIL> ManageDetails = new List<MANAGE_DETAIL>();
                    foreach (string sn in this._SNList)
                    {
                        SiaSun.LMS.Model.MANAGE_DETAIL mMANAGE_DETAIL = new MANAGE_DETAIL();
                        mMANAGE_DETAIL.MANAGE_LIST_ID = this.pickMANAGE_LISTS[0].MANAGE_LIST_ID;
                        mMANAGE_DETAIL.GOODS_BARCODE = sn;
                        mMANAGE_DETAIL.MANAGE_DETAIL_REMARK =
                            string.Format("由拣选工作站:{0},唯一码:{1},被WBS:{2}拣选",
                            this.mT_PICK_STATION.STATION_NAME, this._PlanGroup,
                            this.pickMANAGE_LISTS[0].GOODS_PROPERTY8);

                        ManageDetails.Add(mMANAGE_DETAIL);
                    }

                    bResult = MainApp._I_StorageService.CreateManageDetailsForManagePick(MainApp._USER, ManageDetails, out sResult);


                    if (bResult)
                    {
                        ClearSNListExecute();
                    }
                    this.ShowResult(bResult, sResult);
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }
        }

        bool CanConfirmSNListExecute()
        {
            return true;
        }

        public ICommand CmdConfirmSNList { get { return new RelayCommand(ConfirmSNListExecute, CanConfirmSNListExecute); } }



        void ErrorOperationSubmitExecute()
        {

            var msg = new MVVM.Message.ConfirmMsgArgs(
                            "请确认!",
                            "是否开启异常处理模式？");
            MsgManager.SendMsg("ShowConfirmBox", msg);
            if (msg.Result)
            {
                if (this._CheckStringForOperation.Equals("`123qwe"))
                {
                    ErrerOperationShow = true;
                    MsgManager.SendMsg("ShowBox", "yes");
                }
                else
                {
                    ErrerOperationShow = false;
                    MsgManager.SendMsg("ShowBox", "验证码错误");
                }

            }
            else
            {
                MsgManager.SendMsg("ShowBox", "已取消");
            }
        }

        bool CanErrorOperationSubmitExecute()
        {
            return true;
        }

        public ICommand CmdErrorOperationSubmit { get { return new RelayCommand(ErrorOperationSubmitExecute, CanErrorOperationSubmitExecute); } }

        /// <summary>
        /// 强制将唯一码与拣选工作站断开
        /// </summary>
        void ErrorOperationUnBindOrderExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {

                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否强制解除{0}所绑定的拣选订单,唯一码:{1}?", mT_PICK_STATION.STATION_NAME, this._PlanGroup));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    string group = string.Empty;
                    bResult = MainApp._I_StorageService.ErrorOperationUnBindOrder(MainApp._USER, mT_PICK_STATION.STATION_ID, out group, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }
            this.Update();

        }
        bool CanErrorOperationUnBindOrderExecute()
        {
            return true;
        }
        public ICommand CmdErrorOperationUnBindOrder { get { return new RelayCommand(ErrorOperationUnBindOrderExecute, CanErrorOperationUnBindOrderExecute); } }


        void ErrorOperationUnLockExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {

                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否强制解锁{0}所绑定的拣选订单的锁定库存,唯一码:{1}?", mT_PICK_STATION.STATION_NAME, this._PlanGroup));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    string group = string.Empty;
                    bResult = MainApp._I_StorageService.ErrorOperationUnLock(MainApp._USER, mT_PICK_STATION.STATION_ID, out group, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }
            this.Update();

        }
        bool CanErrorOperationUnLockExecute()
        {
            return true;
        }
        public ICommand CmdErrorOperationUnLock { get { return new RelayCommand(ErrorOperationUnLockExecute, CanErrorOperationUnLockExecute); } }

        #endregion

        #region method
        public void InitPickPostionViewModels(List<T_PICK_POSITION> T_PICK_POSITIONs)
        {
            try
            {
                _PickPositionViewModels.Clear();

                if (this._Direction == "reverse")
                {
                    T_PICK_POSITIONs.Reverse();
                }
                foreach (T_PICK_POSITION pick_position in T_PICK_POSITIONs)
                {
                    _PickPositionViewModels.Add(new PickPositionNewViewModel(pick_position, mT_PICK_STATION, this.MsgManager));
                }
            }
            catch (Exception ex)
            {
                string msg = ex.Message;
            }
        }

        public void BindInformation()
        {
            int index = 1;
            this._informations.Clear();

            //foreach (SiaSun.LMS.Model.STORAGE_LIST list in mSTORAGE_LISTS.Where(t=>t.BOX_BARCODE==(this.Boxs[0].Select_part_index+1).ToString()).OrderBy(s => s.BOX_BARCODE))
            //{

            //    this._informations.Add(new Information(index, list.BOX_BARCODE, MainApp._I_GoodsService.GoodsGetModelGoodsID(list.GOODS_ID).GOODS_NAME, list.STORAGE_LIST_QUANTITY.ToString(), (list.GOODS_PROPERTY3 == "1") ? "扫描序列号拣选" : "电子标签拣选", string.Empty, (list.GOODS_PROPERTY3 == "1") ? true : false));
            //    index++;
            //}

            if (pickMANAGE_LISTS != null)
            {
                foreach (SiaSun.LMS.Model.MANAGE_LIST list in pickMANAGE_LISTS.Where(t => t.BOX_BARCODE == (this.Boxs[0].Select_part_index + 1).ToString()).OrderBy(s => s.BOX_BARCODE))
                {
                    this._invFollow = list.GOODS_PROPERTY5;    //质量追数码 2025-06-26


                    T_PICK_POSITION findT_PICK_POSITION = T_PICK_POSITIONs.Where(t => t.WH_CELL_ID == pickMANAGE_MAIN.END_CELL_ID).ToList()[0];
                    if (findT_PICK_POSITION != null)
                    {
                        SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = MainApp._I_GoodsService.GoodsGetModelGoodsID(list.GOODS_ID);
                        if (mGOODS_MAIN != null)
                        {
                            this.goods_code = mGOODS_MAIN.GOODS_CODE;
                            this.goods_name = mGOODS_MAIN.GOODS_NAME;
                            this.goods_quantity = list.MANAGE_LIST_QUANTITY.ToString();
                            this.goods_units = mGOODS_MAIN.GOODS_UNITS;
                            SiaSun.LMS.Model.T_PICK_POSITION_PLAN_BIND mT_PICK_POSITION_PLAN_BIND
                = MainApp._I_StorageService.Get_T_PICK_POSITION_PLAN_BIND_BY_PICK_POSITION_ID(findT_PICK_POSITION.POSITION_ID);

                            if (mT_PICK_POSITION_PLAN_BIND != null)
                            {
                                int wbsplan_id = mT_PICK_POSITION_PLAN_BIND.PLAN_ID;
                                SiaSun.LMS.Model.PLAN_MAIN wbsPLAN_MAIN = MainApp._I_PlanService.PlanGetModel(wbsplan_id);

                                if (wbsPLAN_MAIN != null)
                                {
                                    this.wbs_code = wbsPLAN_MAIN.PLAN_CODE;
                                    this.wbs_description = wbsPLAN_MAIN.PLAN_DESCRIPTION;
                                    this.wbs_headtext = wbsPLAN_MAIN.PLAN_HEADTEXT;

                                    this.plan_to_user = wbsPLAN_MAIN.PLAN_TO_USER == null ? "" : wbsPLAN_MAIN.PLAN_TO_USER;//2020-10-27集货号

                                    this.plan_relatedbill = wbsPLAN_MAIN.PLAN_RELATEDBILL == null ? "" : wbsPLAN_MAIN.PLAN_RELATEDBILL;//2020-12-27派工单
                                    this.plan_shopno = wbsPLAN_MAIN.PLAN_SHOPNO == null ? "" : wbsPLAN_MAIN.PLAN_SHOPNO;//2020-12-27车间
                                    this.plan_workingseatl = wbsPLAN_MAIN.PLAN_WORKINGSEATL == null ? "" : wbsPLAN_MAIN.PLAN_WORKINGSEATL;//2020-12-27工位
                                    this.plan_projectcode = wbsPLAN_MAIN.PLAN_PROJECT_CODE == null ? "" : wbsPLAN_MAIN.PLAN_PROJECT_CODE;//2020-12-27项目号
                                    //this.plan_from_dept = wbsPLAN_MAIN.PLAN_FROM_DEPT == null ? "" : wbsPLAN_MAIN.PLAN_FROM_DEPT;//2020-12-27需求日期
                                    this.backup_field1 = wbsPLAN_MAIN.BACKUP_FILED1 == null ? "" : wbsPLAN_MAIN.BACKUP_FILED1;//2021-01-16 10:09:42 MES需求日期

                                }

                                //增加PLAN_LIST中的工序工位信息
                                int wbsplan_list_id = list.PLAN_LIST_ID;
                                SiaSun.LMS.Model.PLAN_LIST wbsPLAN_LIST = MainApp._I_PlanService.PlanListGetModel(wbsplan_list_id);
                                if (wbsPLAN_LIST != null)
                                {
                                    this.plan_list_workseat = wbsPLAN_LIST.GOODS_PROPERTY3;//工位 PLAN_LIST.GOODS_PROPERTY3
                                    this.plan_list_workorder = wbsPLAN_LIST.GOODS_PROPERTY4;//工序 PLAN_LIST.GOODS_PROPERTY4
                                }
                            }

                            this.usercode = MainApp._USER.USER_CODE;
                            this.username = MainApp._USER.USER_NAME;
                            this._informations.Add(new Information(index, list.BOX_BARCODE, mGOODS_MAIN.GOODS_CODE, mGOODS_MAIN.GOODS_NAME, list.MANAGE_LIST_QUANTITY.ToString() + " " + mGOODS_MAIN.GOODS_UNITS, (list.DETAIL_FLAG == "1") ? "扫描序列号拣选" : "电子标签拣选", findT_PICK_POSITION.POSITON_NAME, (list.DETAIL_FLAG == "1") ? true : false, this.wbs_headtext));
                            index++;
                            CreateInfoForPick();
                        }
                    }

                }
            }
        }

        public void BindInformation(string boxbarcode)
        {
            int index = 1;
            this._informations.Clear();

            //foreach (SiaSun.LMS.Model.STORAGE_LIST list in mSTORAGE_LISTS.Where(t => t.BOX_BARCODE== boxbarcode).OrderBy(s => s.BOX_BARCODE))
            //{

            //    this._informations.Add(new Information(index, list.BOX_BARCODE, MainApp._I_GoodsService.GoodsGetModelGoodsID(list.GOODS_ID).GOODS_NAME, list.STORAGE_LIST_QUANTITY.ToString(), (list.GOODS_PROPERTY3 == "1") ? "扫描序列号拣选" : "电子标签拣选", string.Empty, (list.GOODS_PROPERTY3 == "1") ? true : false));
            //    index++;
            //}
            if (pickMANAGE_LISTS != null)
            {
                foreach (SiaSun.LMS.Model.MANAGE_LIST list in pickMANAGE_LISTS.Where(t => t.BOX_BARCODE == boxbarcode).OrderBy(s => s.BOX_BARCODE))
                {
                    T_PICK_POSITION findT_PICK_POSITION = T_PICK_POSITIONs.Where(t => t.WH_CELL_ID == pickMANAGE_MAIN.END_CELL_ID).ToList()[0];
                    if (findT_PICK_POSITION != null)
                    {
                        SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = MainApp._I_GoodsService.GoodsGetModelGoodsID(list.GOODS_ID);

                        this._invFollow = list.GOODS_PROPERTY5;    //质量追数码 2025-06-26

                        //this._informations.Add(new Information(index, list.BOX_BARCODE, mGOODS_MAIN.GOODS_CODE, mGOODS_MAIN.GOODS_NAME, list.MANAGE_LIST_QUANTITY.ToString() + " " + mGOODS_MAIN.GOODS_UNITS, (list.DETAIL_FLAG == "1") ? "扫描序列号拣选" : "电子标签拣选", findT_PICK_POSITION.POSITON_NAME, (list.DETAIL_FLAG == "1") ? true : false));
                        //index++;
                        if (mGOODS_MAIN != null)
                        {
                            this.goods_code = mGOODS_MAIN.GOODS_CODE;
                            this.goods_name = mGOODS_MAIN.GOODS_NAME;
                            this.goods_quantity = list.MANAGE_LIST_QUANTITY.ToString();
                            this.goods_units = mGOODS_MAIN.GOODS_UNITS;
                            SiaSun.LMS.Model.T_PICK_POSITION_PLAN_BIND mT_PICK_POSITION_PLAN_BIND
                = MainApp._I_StorageService.Get_T_PICK_POSITION_PLAN_BIND_BY_PICK_POSITION_ID(findT_PICK_POSITION.POSITION_ID);

                            if (mT_PICK_POSITION_PLAN_BIND != null)
                            {
                                int wbsplan_id = mT_PICK_POSITION_PLAN_BIND.PLAN_ID;
                                SiaSun.LMS.Model.PLAN_MAIN wbsPLAN_MAIN = MainApp._I_PlanService.PlanGetModel(wbsplan_id);

                                if (wbsPLAN_MAIN != null)
                                {
                                    this.wbs_code = wbsPLAN_MAIN.PLAN_CODE;
                                    this.wbs_description = wbsPLAN_MAIN.PLAN_DESCRIPTION;
                                    this.wbs_headtext = wbsPLAN_MAIN.PLAN_HEADTEXT;

                                    this.plan_to_user = wbsPLAN_MAIN.PLAN_TO_USER == null ? "" : wbsPLAN_MAIN.PLAN_TO_USER;//2020-10-27集货号

                                    this.plan_relatedbill = wbsPLAN_MAIN.PLAN_RELATEDBILL == null ? "" : wbsPLAN_MAIN.PLAN_RELATEDBILL;//2020-12-27派工单
                                    this.plan_shopno = wbsPLAN_MAIN.PLAN_SHOPNO == null ? "" : wbsPLAN_MAIN.PLAN_SHOPNO;//2020-12-27车间
                                    this.plan_workingseatl = wbsPLAN_MAIN.PLAN_WORKINGSEATL == null ? "" : wbsPLAN_MAIN.PLAN_WORKINGSEATL;//2020-12-27工位
                                    this.plan_projectcode = wbsPLAN_MAIN.PLAN_PROJECT_CODE == null ? "" : wbsPLAN_MAIN.PLAN_PROJECT_CODE;//2020-12-27项目号
                                    //this.plan_from_dept = wbsPLAN_MAIN.PLAN_FROM_DEPT == null ? "" : wbsPLAN_MAIN.PLAN_FROM_DEPT;//2020-12-27需求日期
                                    this.backup_field1 = wbsPLAN_MAIN.BACKUP_FILED1 == null ? "" : wbsPLAN_MAIN.BACKUP_FILED1;//2021-01-16 10:10:14 MES需求日期

                                }

                                //增加PLAN_LIST中的工序工位信息
                                int wbsplan_list_id = list.PLAN_LIST_ID;
                                SiaSun.LMS.Model.PLAN_LIST wbsPLAN_LIST = MainApp._I_PlanService.PlanListGetModel(wbsplan_list_id);
                                if (wbsPLAN_LIST != null)
                                {
                                    this.plan_list_workseat = wbsPLAN_LIST.GOODS_PROPERTY3;//工位 PLAN_LIST.GOODS_PROPERTY3
                                    this.plan_list_workorder = wbsPLAN_LIST.GOODS_PROPERTY4;//工序 PLAN_LIST.GOODS_PROPERTY4
                                }
                            }
                            this.usercode = MainApp._USER.USER_CODE;
                            this.username = MainApp._USER.USER_NAME;
                            this._informations.Add(new Information(index, list.BOX_BARCODE, mGOODS_MAIN.GOODS_CODE, mGOODS_MAIN.GOODS_NAME, list.MANAGE_LIST_QUANTITY.ToString() + " " + mGOODS_MAIN.GOODS_UNITS, (list.DETAIL_FLAG == "1") ? "扫描序列号拣选" : "电子标签拣选", findT_PICK_POSITION.POSITON_NAME, (list.DETAIL_FLAG == "1") ? true : false, this.wbs_headtext));
                            index++;
                            CreateInfoForPick();
                        }
                    }
                }
            }
        }

        public void SwitchSelectLight(string boxbarcode)
        {
            this._Boxs[0].Select_part_index = Convert.ToInt32(boxbarcode) - 1;
            BindInformation(boxbarcode);
        }

        public void Update()
        {
            string sResult = string.Empty;
            T_PICK_POSITIONs = MainApp._I_StorageService.
                        Get_T_PICK_POSITION_BY_STATION_ID(mT_PICK_STATION.STATION_ID, out sResult).OrderBy((s) => s.POSITION_ID).ToList();

            this.InitPickPostionViewModels(T_PICK_POSITIONs);
        }

        public void ShowResult(bool bResult, string sResult)
        {
            if (bResult)
            {
                MsgManager.SendMsg("ShowBox", "操作成功!");
            }
            else
            {
                MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
            }
        }

        private void InitInfo()
        {
            PrinterSettings printSetting = new PrinterSettings();
            printSetting.PrintRange = PrintRange.AllPages;
            int width_in = MM2Inch(width_p);
            int height_in = MM2Inch(height_p);
            PageSettings pageSetting = new PageSettings(printSetting);
            pageSetting.PaperSize = new PaperSize("customer", width_in, height_in);

            int margin_lr_in = MM2Inch(margin_lr);
            int margin_tb_in = MM2Inch(margin_tb);
            pageSetting.Margins = new Margins(margin_lr_in, margin_lr_in, margin_tb_in, margin_tb_in);
            this.printDocumentForPick.DefaultPageSettings = pageSetting;
        }

        /// <summary>
        /// 转换毫米到百分之一英寸
        /// </summary>
        /// <param name="mm"></param>
        /// <returns></returns>
        private int MM2Inch(int mm)
        {
            return (int)(mm * 100.0f / 25.4f);
        }

        private void CreateInfoForPick_forSave()
        {
            if (this._informations.Count == 0)
            {
                return;
            }
            Information info = this._informations[0];
            List<PrintInfo> lstPrintInfos = new List<PrintInfo>();

            //标题配置
            PrintInfo p1 = new PrintInfo();
            p1.PrtType = PrintType.Text;
            p1.PrtColor = Color.Black;
            p1.Content = "物料编码：" + this.goods_code + "    数量：" + this.goods_quantity + "    单位：" + this.goods_units;
            p1.Size = 8;
            p1.FontStyle = System.Drawing.FontStyle.Bold;
            p1.Start = new System.Drawing.Point(2, 4);
            lstPrintInfos.Add(p1);

            PrintInfo p2 = new PrintInfo();
            p2.PrtType = PrintType.Text;
            p2.PrtColor = Color.Black;
            p2.Content = "物料描述：" + this.goods_name;
            p2.Size = 8;
            p2.FontStyle = System.Drawing.FontStyle.Bold;
            p2.Start = new System.Drawing.Point(2, 8);
            lstPrintInfos.Add(p2);

            //标题配置
            PrintInfo p3 = new PrintInfo();
            p3.PrtType = PrintType.Text;
            p3.PrtColor = Color.Black;
            p3.Content = "WBS:" + this.wbs_code;
            p3.Size = 8;
            p3.FontStyle = System.Drawing.FontStyle.Bold;
            p3.Start = new System.Drawing.Point(2, 12);
            lstPrintInfos.Add(p3);

            //打印时间
            PrintInfo p5 = new PrintInfo();
            p5.PrtType = PrintType.Text;
            p5.PrtColor = Color.Black;
            p5.Content = "由" + mT_PICK_STATION.STATION_NAME + "打印于" + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"); ;
            p5.Size = 7;
            p5.FontStyle = System.Drawing.FontStyle.Italic;
            p5.Start = new System.Drawing.Point(40, 26);

            lstPrintInfos.Add(p5);

            PrintInfo p6 = new PrintInfo();
            p6.PrtType = PrintType.BarcodeImage;
            p6.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = this.goods_code,
                BarcodeHeight = 30,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p6.Start = new System.Drawing.Point(5, 16);

            lstPrintInfos.Add(p6);
            printHelperForPick.PrintInfos = lstPrintInfos;

        }
        private void CreateInfoForPick_forSave30_150()
        {
            if (this._informations.Count == 0)
            {
                return;
            }
            Information info = this._informations[0];
            List<PrintInfo> lstPrintInfos = new List<PrintInfo>();

            //标题配置
            PrintInfo p1 = new PrintInfo();
            p1.PrtType = PrintType.Text;
            p1.PrtColor = Color.Black;
            p1.Content = "物料编码：" + this.goods_code + "  数量：" + this.goods_quantity + "  单位：" + this.goods_units + "  集货号：" + this.plan_to_user;
            p1.Size = 8;
            p1.FontStyle = System.Drawing.FontStyle.Bold;
            p1.Start = new System.Drawing.Point(6, 1);
            lstPrintInfos.Add(p1);

            PrintInfo p2 = new PrintInfo();
            p2.PrtType = PrintType.Text;
            p2.PrtColor = Color.Black;
            p2.Content = "物料描述：" + this.goods_name;
            p2.Size = 8;
            p2.FontStyle = System.Drawing.FontStyle.Bold;
            p2.Start = new System.Drawing.Point(6, 4);
            lstPrintInfos.Add(p2);

            //标题配置
            PrintInfo p3 = new PrintInfo();
            p3.PrtType = PrintType.Text;
            p3.PrtColor = Color.Black;
            p3.Content = "WBS:" + this.wbs_code;
            p3.Size = 8;
            p3.FontStyle = System.Drawing.FontStyle.Bold;
            p3.Start = new System.Drawing.Point(6, 12);
            lstPrintInfos.Add(p3);

            //标题配置
            PrintInfo p4 = new PrintInfo();
            p4.PrtType = PrintType.Text;
            p4.PrtColor = Color.Black;
            p4.Content = "装配体名称:" + this.wbs_description;
            p4.Size = 8;
            p4.FontStyle = System.Drawing.FontStyle.Bold;
            p4.Start = new System.Drawing.Point(6, 15);
            lstPrintInfos.Add(p4);

            //打印时间
            PrintInfo p5 = new PrintInfo();
            p5.PrtType = PrintType.Text;
            p5.PrtColor = Color.Black;
            p5.Content = "由" + this.username + "|" + this.usercode + "于" + mT_PICK_STATION.STATION_NAME + "拣选 " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            p5.Size = 7;
            p5.FontStyle = System.Drawing.FontStyle.Bold;
            p5.Start = new System.Drawing.Point(6, 26);

            lstPrintInfos.Add(p5);

            PrintInfo p6 = new PrintInfo();
            p6.PrtType = PrintType.BarcodeImage;
            p6.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = this.wbs_code,
                BarcodeHeight = 15,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p6.Start = new System.Drawing.Point(12, 19);
            lstPrintInfos.Add(p6);

            PrintInfo p7 = new PrintInfo();
            p7.PrtType = PrintType.BarcodeImage;
            p7.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = this.goods_code,
                BarcodeHeight = 15,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p7.Start = new System.Drawing.Point(24, 8);
            lstPrintInfos.Add(p7);

            PrintInfo p8 = new PrintInfo();
            p8.PrtType = PrintType.Text;
            p8.PrtColor = Color.Black;
            p8.Content = this.wbs_headtext;
            p8.Size = 8;
            p8.FontStyle = System.Drawing.FontStyle.Bold;
            p8.Start = new System.Drawing.Point(6, 23);

            lstPrintInfos.Add(p8);

            printHelperForPick.PrintInfos = lstPrintInfos;

        }

        /// <summary>
        /// 2020-12-27
        /// 二维码打印
        /// </summary>
        private void CreateInfoForPick_forSave1227()
        {
            if (this._informations.Count == 0)
            {
                return;
            }
            Information info = this._informations[0];
            List<PrintInfo> lstPrintInfos = new List<PrintInfo>();

            //项目号 第1行
            PrintInfo p1 = new PrintInfo();
            p1.PrtType = PrintType.Text;
            p1.PrtColor = Color.Black;
            p1.Content = string.Format("项目号:{0}", this.plan_projectcode);// "物料编码：" + this.goods_code + "  数量：" + this.goods_quantity + "  单位：" + this.goods_units + "  集货号：" + this.plan_to_user;
            p1.Size = 8;
            p1.FontStyle = System.Drawing.FontStyle.Bold;
            p1.Start = new System.Drawing.Point(5, 1);
            lstPrintInfos.Add(p1);

            //集货号 第1行
            PrintInfo p2 = new PrintInfo();
            p2.PrtType = PrintType.Text;
            p2.PrtColor = Color.Black;
            p2.Content = string.Format("集货号:{0}", this.plan_to_user);
            p2.Size = 8;
            p2.FontStyle = System.Drawing.FontStyle.Bold;
            p2.Start = new System.Drawing.Point(70, 1);
            lstPrintInfos.Add(p2);

            //派工单 第2行
            PrintInfo p3 = new PrintInfo();
            p3.PrtType = PrintType.Text;
            p3.PrtColor = Color.Black;
            p3.Content = string.Format("派工单:{0}", this.plan_relatedbill);
            p3.Size = 8;
            p3.FontStyle = System.Drawing.FontStyle.Bold;
            p3.Start = new System.Drawing.Point(5, 5);
            lstPrintInfos.Add(p3);

            //物料号 第3行
            PrintInfo p4 = new PrintInfo();
            p4.PrtType = PrintType.Text;
            p4.PrtColor = Color.Black;
            p4.Content = string.Format("物料号:{0}", this.goods_code);
            p4.Size = 8;
            p4.FontStyle = System.Drawing.FontStyle.Bold;
            p4.Start = new System.Drawing.Point(5, 9);
            lstPrintInfos.Add(p4);

            //物料号 一维码
            PrintInfo p16 = new PrintInfo();
            p16.PrtType = PrintType.BarcodeImage;
            p16.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = this.goods_code,
                BarcodeHeight = 15,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p16.Start = new System.Drawing.Point(10, 13);
            lstPrintInfos.Add(p16);

            //描述 第4行
            PrintInfo p5 = new PrintInfo();
            p5.PrtType = PrintType.Text;
            p5.PrtColor = Color.Black;
            p5.Content = string.Format("描述:{0}", this.goods_name);
            p5.Size = 7;
            p5.FontStyle = System.Drawing.FontStyle.Bold;
            p5.Start = new System.Drawing.Point(5, 17);
            lstPrintInfos.Add(p5);

            //车间 第5行
            PrintInfo p6 = new PrintInfo();
            p6.PrtType = PrintType.Text;
            p6.PrtColor = Color.Black;
            p6.Content = string.Format("车间:{0}", this.plan_shopno);
            p6.Size = 8;
            p6.FontStyle = System.Drawing.FontStyle.Bold;
            p6.Start = new System.Drawing.Point(5, 21);
            lstPrintInfos.Add(p6);

            //工位 第5行
            PrintInfo p7 = new PrintInfo();
            p7.PrtType = PrintType.Text;
            p7.PrtColor = Color.Black;
            p7.Content = string.Format("工位:{0}", this.plan_workingseatl);
            p7.Size = 8;
            p7.FontStyle = System.Drawing.FontStyle.Bold;
            p7.Start = new System.Drawing.Point(30, 21);
            lstPrintInfos.Add(p7);

            //需求日期 第6行
            PrintInfo p8 = new PrintInfo();
            p8.PrtType = PrintType.Text;
            p8.PrtColor = Color.Black;
            p8.Content = string.Format("需求日期:{0}", this.backup_field1);
            p8.Size = 8;
            p8.FontStyle = System.Drawing.FontStyle.Bold;
            p8.Start = new System.Drawing.Point(5, 25);
            lstPrintInfos.Add(p8);

            //批次 第7行
            PrintInfo p9 = new PrintInfo();
            p9.PrtType = PrintType.Text;
            p9.PrtColor = Color.Black;
            p9.Content = string.Format("批次:{0}", "");
            p9.Size = 8;
            p9.FontStyle = System.Drawing.FontStyle.Bold;
            p9.Start = new System.Drawing.Point(5, 29);
            lstPrintInfos.Add(p9);

            //数量 第7行
            PrintInfo p10 = new PrintInfo();
            p10.PrtType = PrintType.Text;
            p10.PrtColor = Color.Black;
            p10.Content = string.Format("数量:{0}", this.goods_quantity);
            p10.Size = 8;
            p10.FontStyle = System.Drawing.FontStyle.Bold;
            p10.Start = new System.Drawing.Point(45, 29);
            lstPrintInfos.Add(p10);

            //wbs号 第8行
            PrintInfo p11 = new PrintInfo();
            p11.PrtType = PrintType.Text;
            p11.PrtColor = Color.Black;
            p11.Content = string.Format("WBS号:{0}", this.wbs_code);
            p11.Size = 8;
            p11.FontStyle = System.Drawing.FontStyle.Bold;
            p11.Start = new System.Drawing.Point(5, 33);
            lstPrintInfos.Add(p11);

            //装配体名称 第9行
            PrintInfo p12 = new PrintInfo();
            p12.PrtType = PrintType.Text;
            p12.PrtColor = Color.Black;
            p12.Content = string.Format("装配体名称:{0}", this.wbs_description);
            p12.Size = 8;
            p12.FontStyle = System.Drawing.FontStyle.Bold;
            p12.Start = new System.Drawing.Point(5, 37);
            lstPrintInfos.Add(p12);

            //抬头文本 第10行
            PrintInfo p13 = new PrintInfo();
            p13.PrtType = PrintType.Text;
            p13.PrtColor = Color.Black;
            p13.Content = string.Format("{0}", this.wbs_headtext);
            p13.Size = 7;
            p13.FontStyle = System.Drawing.FontStyle.Bold;
            p13.Start = new System.Drawing.Point(5, 41);
            lstPrintInfos.Add(p13);

            //打印信息 第11行
            PrintInfo p14 = new PrintInfo();
            p14.PrtType = PrintType.Text;
            p14.PrtColor = Color.Black;
            p14.Content = "由" + this.username + "|" + this.usercode + "于" + mT_PICK_STATION.STATION_NAME + "拣选 " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            p14.Size = 7;
            p14.FontStyle = System.Drawing.FontStyle.Bold;
            p14.Start = new System.Drawing.Point(10, 44);
            lstPrintInfos.Add(p14);

            PrintInfo p15 = new PrintInfo();
            p15.PrtType = PrintType.QRCode;
            p15.QrCodeSettings = new QrCodeSetting
            {
                Data = string.IsNullOrEmpty(this.plan_relatedbill) ?
                string.Format("{0}", this.goods_code) :
                string.Format("{0}*{1}", this.plan_relatedbill, this.goods_code),
                x = 65f,
                y = 9f,
                width = 30f,
                height = 30f,
                PixelsPerModuleToUse = 5,
            };

            lstPrintInfos.Add(p15);

            printHelperForPick.PrintInfos = lstPrintInfos;
        }

        /// <summary>
        /// 2021-09-03
        /// 打印内容调整
        /// </summary>
        private void CreateInfoForPick_forSave250526()
        {
            if (this._informations.Count == 0)
            {
                return;
            }
            Information info = this._informations[0];
            List<PrintInfo> lstPrintInfos = new List<PrintInfo>();

            //项目号 第1行
            PrintInfo p1 = new PrintInfo();
            p1.PrtType = PrintType.Text;
            p1.PrtColor = Color.Black;
            p1.Content = string.Format("项目号:{0}", this.plan_projectcode);// "物料编码：" + this.goods_code + "  数量：" + this.goods_quantity + "  单位：" + this.goods_units + "  集货号：" + this.plan_to_user;
            p1.Size = 8;
            p1.FontStyle = System.Drawing.FontStyle.Bold;
            p1.Start = new System.Drawing.Point(5, 1);
            lstPrintInfos.Add(p1);

            //集货号 第1行
            PrintInfo p2 = new PrintInfo();
            p2.PrtType = PrintType.Text;
            p2.PrtColor = Color.Black;
            p2.Content = string.Format("集货号:{0}", this.plan_to_user);
            p2.Size = 10;
            p2.FontStyle = System.Drawing.FontStyle.Bold;
            p2.Start = new System.Drawing.Point(70, 1);
            lstPrintInfos.Add(p2);

            //派工单 第2行
            PrintInfo p3 = new PrintInfo();
            p3.PrtType = PrintType.Text;
            p3.PrtColor = Color.Black;
            p3.Content = string.Format("派工单:{0}", this.plan_relatedbill);
            p3.Size = 8;
            p3.FontStyle = System.Drawing.FontStyle.Bold;
            p3.Start = new System.Drawing.Point(5, 5);
            lstPrintInfos.Add(p3);

            //物料号 第3行
            PrintInfo p4 = new PrintInfo();
            p4.PrtType = PrintType.Text;
            p4.PrtColor = Color.Black;
            p4.Content = string.Format("物料号:{0}", this.goods_code);
            p4.Size = 8;
            p4.FontStyle = System.Drawing.FontStyle.Bold;
            p4.Start = new System.Drawing.Point(5, 9);
            lstPrintInfos.Add(p4);

            //数量 第3行
            PrintInfo p10 = new PrintInfo();
            p10.PrtType = PrintType.Text;
            p10.PrtColor = Color.Black;
            p10.Content = string.Format("数量:{0}", this.goods_quantity);
            p10.Size = 10;
            p10.FontStyle = System.Drawing.FontStyle.Bold;
            p10.Start = new System.Drawing.Point(45, 9);
            lstPrintInfos.Add(p10);

            //物料号 一维码
            PrintInfo p16 = new PrintInfo();
            p16.PrtType = PrintType.BarcodeImage;
            p16.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = this.goods_code,
                BarcodeHeight = 15,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p16.Start = new System.Drawing.Point(10, 13);
            lstPrintInfos.Add(p16);

            //描述 第4行
            PrintInfo p5 = new PrintInfo();
            p5.PrtType = PrintType.Text;
            p5.PrtColor = Color.Black;
            p5.Content = string.Format("物料描述:{0}", this.goods_name);
            p5.Size = 7;
            p5.FontStyle = System.Drawing.FontStyle.Bold;
            p5.Start = new System.Drawing.Point(5, 17);
            lstPrintInfos.Add(p5);

            //车间 第5行
            PrintInfo p6 = new PrintInfo();
            p6.PrtType = PrintType.Text;
            p6.PrtColor = Color.Black;
            p6.Content = string.Format("车间:{0}", this.plan_shopno);
            p6.Size = 8;
            p6.FontStyle = System.Drawing.FontStyle.Bold;
            p6.Start = new System.Drawing.Point(5, 21);
            lstPrintInfos.Add(p6);

            //工位 第5行
            PrintInfo p7 = new PrintInfo();
            p7.PrtType = PrintType.Text;
            p7.PrtColor = Color.Black;
            p7.Content = string.Format("工位:{0}", this.plan_list_workseat);
            p7.Size = 8;
            p7.FontStyle = System.Drawing.FontStyle.Bold;
            p7.Start = new System.Drawing.Point(30, 21);
            lstPrintInfos.Add(p7);

            //工序序号 第6行
            PrintInfo p9 = new PrintInfo();
            p9.PrtType = PrintType.Text;
            p9.PrtColor = Color.Black;
            p9.Content = string.Format("工序序号:{0}", this.plan_list_workorder);
            p9.Size = 8;
            p9.FontStyle = System.Drawing.FontStyle.Bold;
            p9.Start = new System.Drawing.Point(5, 25);
            lstPrintInfos.Add(p9);

            //需求日期 第7行
            PrintInfo p8 = new PrintInfo();
            p8.PrtType = PrintType.Text;
            p8.PrtColor = Color.Black;
            p8.Content = string.Format("需求日期:{0}", this.backup_field1);
            p8.Size = 8;
            p8.FontStyle = System.Drawing.FontStyle.Bold;
            p8.Start = new System.Drawing.Point(5, 29);
            lstPrintInfos.Add(p8);

            //批次 第7行
            //PrintInfo p9 = new PrintInfo();
            //p9.PrtType = PrintType.Text;
            //p9.PrtColor = Color.Black;
            //p9.Content = string.Format("批次:{0}", "");
            //p9.Size = 8;
            //p9.FontStyle = System.Drawing.FontStyle.Bold;
            //p9.Start = new System.Drawing.Point(5, 29);
            //lstPrintInfos.Add(p9);

            //数量 第7行
            //PrintInfo p10 = new PrintInfo();
            //p10.PrtType = PrintType.Text;
            //p10.PrtColor = Color.Black;
            //p10.Content = string.Format("数量:{0}", this.goods_quantity);
            //p10.Size = 8;
            //p10.FontStyle = System.Drawing.FontStyle.Bold;
            //p10.Start = new System.Drawing.Point(45, 29);
            //lstPrintInfos.Add(p10);

            //wbs号 第8行
            PrintInfo p11 = new PrintInfo();
            p11.PrtType = PrintType.Text;
            p11.PrtColor = Color.Black;
            p11.Content = string.Format("WBS号/分组:{0}", this.wbs_code);
            p11.Size = 8;
            p11.FontStyle = System.Drawing.FontStyle.Bold;
            p11.Start = new System.Drawing.Point(5, 33);
            lstPrintInfos.Add(p11);

            //装配体名称 第9行
            PrintInfo p12 = new PrintInfo();
            p12.PrtType = PrintType.Text;
            p12.PrtColor = Color.Black;
            p12.Content = string.Format("装配体名称/描述:{0}", this.wbs_description);
            p12.Size = 8;
            p12.FontStyle = System.Drawing.FontStyle.Bold;
            p12.Start = new System.Drawing.Point(5, 37);
            lstPrintInfos.Add(p12);

            //抬头文本 第10行
            PrintInfo p13 = new PrintInfo();
            p13.PrtType = PrintType.Text;
            p13.PrtColor = Color.Black;
            p13.Content = string.Format("{0}", this.wbs_headtext);
            p13.Size = 7;
            p13.FontStyle = System.Drawing.FontStyle.Bold;
            p13.Start = new System.Drawing.Point(5, 41);
            lstPrintInfos.Add(p13);

            //打印信息 第11行
            PrintInfo p14 = new PrintInfo();
            p14.PrtType = PrintType.Text;
            p14.PrtColor = Color.Black;
            p14.Content = "由" + this.username + "|" + this.usercode + "于" + mT_PICK_STATION.STATION_NAME + "拣选 " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            p14.Size = 7;
            p14.FontStyle = System.Drawing.FontStyle.Bold;
            p14.Start = new System.Drawing.Point(10, 44);
            lstPrintInfos.Add(p14);

            PrintInfo p15 = new PrintInfo();
            p15.PrtType = PrintType.QRCode;
            p15.QrCodeSettings = new QrCodeSetting
            {
                Data = string.IsNullOrEmpty(this.plan_relatedbill) ?
                string.Format("{0}", this.goods_code) :
                string.Format("{0}*{1}*{2}", this.plan_relatedbill, this.wbs_code, this.goods_code),
                x = 65f,
                y = 9f,
                width = 30f,
                height = 30f,
                PixelsPerModuleToUse = 5,
            };

            lstPrintInfos.Add(p15);

            printHelperForPick.PrintInfos = lstPrintInfos;
        }

        /// <summary>
        /// 2025-06-26
        /// 打印内容调整
        /// </summary>
        private void CreateInfoForPick()
        {
            if (this._informations.Count == 0)
            {
                return;
            }
            Information info = this._informations[0];
            List<PrintInfo> lstPrintInfos = new List<PrintInfo>();

            //项目号 第1行
            PrintInfo p1 = new PrintInfo();
            p1.PrtType = PrintType.Text;
            p1.PrtColor = Color.Black;
            p1.Content = string.Format("项目号:{0}", this.plan_projectcode);// "物料编码：" + this.goods_code + "  数量：" + this.goods_quantity + "  单位：" + this.goods_units + "  集货号：" + this.plan_to_user;
            p1.Size = 8;
            p1.FontStyle = System.Drawing.FontStyle.Bold;
            p1.Start = new System.Drawing.Point(5, 1);
            lstPrintInfos.Add(p1);

            //集货号 第1行
            PrintInfo p2 = new PrintInfo();
            p2.PrtType = PrintType.Text;
            p2.PrtColor = Color.Black;
            p2.Content = string.Format("集货号:{0}", this.plan_to_user);
            p2.Size = 10;
            p2.FontStyle = System.Drawing.FontStyle.Bold;
            p2.Start = new System.Drawing.Point(70, 1);
            lstPrintInfos.Add(p2);

            //派工单 第2行
            PrintInfo p3 = new PrintInfo();
            p3.PrtType = PrintType.Text;
            p3.PrtColor = Color.Black;
            p3.Content = string.Format("派工单:{0}", this.plan_relatedbill);
            p3.Size = 8;
            p3.FontStyle = System.Drawing.FontStyle.Bold;
            p3.Start = new System.Drawing.Point(5, 5);
            lstPrintInfos.Add(p3);

            //物料号 第3行
            PrintInfo p4 = new PrintInfo();
            p4.PrtType = PrintType.Text;
            p4.PrtColor = Color.Black;
            p4.Content = string.Format("物料号:{0}", this.goods_code);
            p4.Size = 8;
            p4.FontStyle = System.Drawing.FontStyle.Bold;
            p4.Start = new System.Drawing.Point(5, 9);
            lstPrintInfos.Add(p4);

            //数量 第3行
            PrintInfo p10 = new PrintInfo();
            p10.PrtType = PrintType.Text;
            p10.PrtColor = Color.Black;
            p10.Content = string.Format("数量:{0}", this.goods_quantity);
            p10.Size = 10;
            p10.FontStyle = System.Drawing.FontStyle.Bold;
            p10.Start = new System.Drawing.Point(45, 9);
            lstPrintInfos.Add(p10);

            //物料号 一维码
            PrintInfo p16 = new PrintInfo();
            p16.PrtType = PrintType.BarcodeImage;
            p16.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = this.goods_code,
                BarcodeHeight = 15,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p16.Start = new System.Drawing.Point(10, 13);
            lstPrintInfos.Add(p16);

            //描述 第4行
            PrintInfo p5 = new PrintInfo();
            p5.PrtType = PrintType.Text;
            p5.PrtColor = Color.Black;
            p5.Content = string.Format("物料描述:{0}", this.goods_name);
            p5.Size = 7;
            p5.FontStyle = System.Drawing.FontStyle.Bold;
            p5.Start = new System.Drawing.Point(5, 17);
            lstPrintInfos.Add(p5);

            //车间 第5行
            PrintInfo p6 = new PrintInfo();
            p6.PrtType = PrintType.Text;
            p6.PrtColor = Color.Black;
            p6.Content = string.Format("车间:{0}", this.plan_shopno);
            p6.Size = 8;
            p6.FontStyle = System.Drawing.FontStyle.Bold;
            p6.Start = new System.Drawing.Point(5, 21);
            lstPrintInfos.Add(p6);

            //工位 第5行
            PrintInfo p7 = new PrintInfo();
            p7.PrtType = PrintType.Text;
            p7.PrtColor = Color.Black;
            p7.Content = string.Format("工位:{0}", this.plan_list_workseat);
            p7.Size = 8;
            p7.FontStyle = System.Drawing.FontStyle.Bold;
            p7.Start = new System.Drawing.Point(30, 21);
            lstPrintInfos.Add(p7);

            //工序序号 第6行
            PrintInfo p9 = new PrintInfo();
            p9.PrtType = PrintType.Text;
            p9.PrtColor = Color.Black;
            p9.Content = string.Format("工序序号:{0}", this.plan_list_workorder);
            p9.Size = 8;
            p9.FontStyle = System.Drawing.FontStyle.Bold;
            p9.Start = new System.Drawing.Point(5, 25);
            lstPrintInfos.Add(p9);

            //需求日期 第7行
            PrintInfo p8 = new PrintInfo();
            p8.PrtType = PrintType.Text;
            p8.PrtColor = Color.Black;
            p8.Content = string.Format("需求日期:{0}", this.backup_field1);
            p8.Size = 8;
            p8.FontStyle = System.Drawing.FontStyle.Bold;
            p8.Start = new System.Drawing.Point(5, 29);
            lstPrintInfos.Add(p8);

            //批次 第7行
            //PrintInfo p9 = new PrintInfo();
            //p9.PrtType = PrintType.Text;
            //p9.PrtColor = Color.Black;
            //p9.Content = string.Format("批次:{0}", "");
            //p9.Size = 8;
            //p9.FontStyle = System.Drawing.FontStyle.Bold;
            //p9.Start = new System.Drawing.Point(5, 29);
            //lstPrintInfos.Add(p9);

            //数量 第7行
            //PrintInfo p10 = new PrintInfo();
            //p10.PrtType = PrintType.Text;
            //p10.PrtColor = Color.Black;
            //p10.Content = string.Format("数量:{0}", this.goods_quantity);
            //p10.Size = 8;
            //p10.FontStyle = System.Drawing.FontStyle.Bold;
            //p10.Start = new System.Drawing.Point(45, 29);
            //lstPrintInfos.Add(p10);

            //wbs号 第8行
            PrintInfo p11 = new PrintInfo();
            p11.PrtType = PrintType.Text;
            p11.PrtColor = Color.Black;
            p11.Content = string.Format("WBS号/分组:{0}", this.wbs_code);
            p11.Size = 8;
            p11.FontStyle = System.Drawing.FontStyle.Bold;
            p11.Start = new System.Drawing.Point(5, 33);
            lstPrintInfos.Add(p11);

            //装配体名称 第9行
            PrintInfo p12 = new PrintInfo();
            p12.PrtType = PrintType.Text;
            p12.PrtColor = Color.Black;
            p12.Content = string.Format("装配体名称/描述:{0}", this.wbs_description);
            p12.Size = 8;
            p12.FontStyle = System.Drawing.FontStyle.Bold;
            p12.Start = new System.Drawing.Point(5, 37);
            lstPrintInfos.Add(p12);

            //抬头文本 第10行
            PrintInfo p13 = new PrintInfo();
            p13.PrtType = PrintType.Text;
            p13.PrtColor = Color.Black;
            p13.Content = string.Format("{0}", this.wbs_headtext);
            p13.Size = 7;
            p13.FontStyle = System.Drawing.FontStyle.Bold;
            p13.Start = new System.Drawing.Point(5, 41);
            lstPrintInfos.Add(p13);

            //打印信息 第11行
            PrintInfo p14 = new PrintInfo();
            p14.PrtType = PrintType.Text;
            p14.PrtColor = Color.Black;
            p14.Content = "由" + this.username + "|" + this.usercode + "于" + mT_PICK_STATION.STATION_NAME + "拣选 " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            p14.Size = 7;
            p14.FontStyle = System.Drawing.FontStyle.Bold;
            p14.Start = new System.Drawing.Point(10, 44);
            lstPrintInfos.Add(p14);

            PrintInfo p15 = new PrintInfo();
            p15.PrtType = PrintType.QRCode;
            string factoryCode = "0000";
            string zuPanDate = System.DateTime.Today.ToString("YYMMDD");
            string sfcSn = string.Format("{0}", string.Empty);

            //2025-06-26
            //标签中二维码内容为“追溯码 @派工单号_分组*物料号 * SFC / SN”,追溯码为空时写0000

            //原格式
            //p15.QrCodeSettings = new QrCodeSetting
            //{
            //    Data = string.IsNullOrEmpty(this.plan_relatedbill) ?
            //    string.Format("{0}+{1}",
            //        factoryCode,//0
            //        zuPanDate//1
            //                 //this.plan_relatedbill,//2
            //                 //this.wbs_code, //3  
            //                 //this.goods_code,//4
            //                 //sfcSn //5
            //    ) :
            //    string.Format("{0}+{1}@{2}_{3}*{4}*{5}",
            //        factoryCode,//0
            //        zuPanDate,//1
            //        this.plan_relatedbill,//2
            //        this.wbs_code, //3  
            //        this.goods_code,//4
            //        sfcSn //5
            //    ),
            //    x = 65f,
            //    y = 9f,
            //    width = 30f,
            //    height = 30f,
            //    PixelsPerModuleToUse = 5,
            //};

            //新格式
            p15.QrCodeSettings = new QrCodeSetting
            {
                Data = string.Format("{0}@{2}_{1}*{3}*{4}",
                    string.IsNullOrEmpty(this._invFollow) ? "0000" : this._invFollow,
                    this.wbs_code,
                    this.plan_relatedbill,
                    this.goods_code,
                    sfcSn),

                x = 65f,
                y = 9f,
                width = 30f,
                height = 30f,
                PixelsPerModuleToUse = 5,
            };

            lstPrintInfos.Add(p15);

            printHelperForPick.PrintInfos = lstPrintInfos;
        }

        private void PrintDocumentForPick_PrintPage(object sender, PrintPageEventArgs e)
        {
            //Font font = new Font("Arial", 14f);
            Graphics g = e.Graphics;
            g.PageScale = 1;
            g.PageUnit = GraphicsUnit.Millimeter;//单位
            //先画一个矩形
            System.Drawing.Pen lineColor = new System.Drawing.Pen(System.Drawing.Color.Black, 0.2f);
            //g.FillRectangle(System.Drawing.Brushes.White, 0, 0, width_p, height_p);
            //g.DrawRectangle(lineColor, margin_lr, margin_tb, width_p-2* margin_lr, height_p-2* margin_tb);
            //for(int r = 0; r < 10; r++)
            //{
            //    g.DrawLine(lineColor, new System.Drawing.Point(2, 2+r), new System.Drawing.Point(2+10*r, 2+r));
            //}

            printHelperForPick.Print(g);
        }


        private void InitPrintSetting()
        {
            InitInfo();
            //CreateInfoForPick();
            printDocumentForPick.PrintPage += PrintDocumentForPick_PrintPage;
        }
        #endregion

        //201902
        //预绑定
        #region PrevBinding
        void ShowPrevBindingExecute()
        {
            IsShowPrevToolBar = !IsShowPrevToolBar;
        }

        bool CanShowPrevBindingExecute()
        {
            return true;
        }

        public ICommand CmdShowPrevBinding { get { return new RelayCommand(ShowPrevBindingExecute, CanShowPrevBindingExecute); } }

        /// <summary>
        /// 拣选工作站预绑定
        /// testing
        /// </summary>
        void PrevBindingExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {


                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码对应的订单:{0}预绑定至{1}?", this.planGroupToPrevBinding, mT_PICK_STATION.STATION_NAME));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    if (string.IsNullOrEmpty(this.planGroupToPrevBinding))
                    {
                        MsgManager.SendMsg("ShowBox", "操作失败:选中的唯一码为空");
                        return;
                    }
                    bResult = MainApp._I_StorageService.PrevBindOrderCodeToPickStation(MainApp._USER, mT_PICK_STATION.STATION_ID, this.planGroupToPrevBinding, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }
            this.Update();

        }
        bool CanPrevBindingExecute()
        {
            return true;
        }
        public ICommand CmdPrevBinding { get { return new RelayCommand(PrevBindingExecute, CanPrevBindingExecute); } }
        #endregion

        #region AutoBind


        /// <summary>
        /// 执行连接与登录操作
        /// 成功操作后，可以接收后台推送订单操作
        /// </summary>
        void ConnectNdLoginExecute()
        {
            try
            {
                this.ConnectLoginBtnIsEnabled = false;

                Task connectTask = messageSrv.ConnectAsync();

                connectTask.ContinueWith(new Action<Task>(
                    (s) =>
                    {
                        if (connectTask.Exception == null)
                        {
                            this.dispather4AutoBind.BeginInvoke(new Action(() =>
                            {
                                this.ConnectLoginBtnIsEnabled = false;
                                this.LoginStatus = "已连接";
                                //MsgManager.SendMsg("ShowBox", "连接成功");
                            }));

                            Task<bool> loginTask = messageSrv.LoginAsync(this.mT_PICK_STATION.STATION_CODE,
                                                                    MainApp._USER.USER_ID,
                                                                    MainApp._USER.USER_CODE,
                                                                    MainApp._USER.USER_NAME,
                                                                    "OP");
                            loginTask.Wait();

                            if (loginTask.Result)
                            {
                                this.dispather4AutoBind.BeginInvoke(new Action(() =>
                                {
                                    this.ConnectLoginBtnIsEnabled = false;
                                    this.LoginStatus = "已登录,申请接单";
                                    MsgManager.SendMsg("ShowBox", "登录成功");
                                }));
                            }
                            else
                            {
                                this.dispather4AutoBind.BeginInvoke(new Action(() =>
                                {
                                    this.ConnectLoginBtnIsEnabled = true;
                                    this.LoginStatus = "已连接,未登录";
                                    MsgManager.SendMsg("ShowBox", "登录失败");
                                }));
                            }
                        }
                        else
                        {
                            this.dispather4AutoBind.BeginInvoke(new Action(() =>
                            {
                                this.ConnectLoginBtnIsEnabled = true;
                                this.LoginStatus = "已离线";
                                MsgManager.SendMsg("ShowBox", "连接失败");
                            }));
                        }
                    }
                    ));



            }
            catch (AggregateException aex)
            {
                MsgManager.SendMsg("ShowBox", string.Format("出现重大错误:{0}，请退出重进！", aex.Message));
            }
            catch (Exception ex)
            {
                MsgManager.SendMsg("ShowBox", string.Format("出现重大未预见错误:{0}，请退出重进！", ex.Message));
            }

        }
        bool CanConnectNdLoginExecute()
        {
            return true;
        }
        public ICommand CmdConnectNdLogin { get { return new RelayCommand(ConnectNdLoginExecute, CanConnectNdLoginExecute); } }


        /// <summary>
        /// 订单推送到客户端事件
        /// </summary>
        /// <param name="arg1">用户</param>
        /// <param name="arg2">订单信息</param>
        /// <param name="arg3">消息类型</param>
        private void MessageSrv_NewTextMessage(string arg1, string arg2, Message.MessageType arg3)
        {
            string name = arg1;
            string planGroupToBind = arg2;
            Message.MessageType mType = arg3;

            if (this.PlayDingDanRequested != null)
            {
                this.PlayDingDanRequested(this, EventArgs.Empty);
            }
            /*
            var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请及时确认绑定推送订单!",
                                string.Format("是否将推送的唯一码对应的订单:{0}绑定至{1}?", planGroupToBind, mT_PICK_STATION.STATION_NAME));
            MsgManager.SendMsg("ShowConfirmBox", msg);
            bool bResult = true;
            string sResult = string.Empty;

            if (msg.Result)
            {
                //todo 绑定操作
                //MsgManager.SendMsg("ShowBox", "操作成功!");
                

                bResult = MainApp._I_StorageService.BindOrderCodeToPickStationNew2024(MainApp._USER, mT_PICK_STATION.STATION_ID, planGroupToBind, out sResult);
                if (bResult)
                {
                    MsgManager.SendMsg("ShowBox", "绑定成功!");
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                }
            }
            else
            {
                //取消
                bResult = MainApp._I_StorageService.UnAtuoBindOrderPickStationNew2024(MainApp._USER, mT_PICK_STATION.STATION_ID, planGroupToBind, out sResult);

                if (bResult)
                {
                    MsgManager.SendMsg("ShowBox", "拒绝成功!");
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                }
            }
           */
            bool bResult = true;
            string sResult = string.Empty;

            bResult = MainApp._I_StorageService.BindOrderCodeToPickStationNew2024(MainApp._USER, mT_PICK_STATION.STATION_ID, planGroupToBind, out sResult);
            if (!bResult)
            {
                MsgManager.SendMsg("ShowBox", string.Format("{1}绑定失败，原因:{0}", sResult, planGroupToBind));
            }


            this.Update();
        }

        private void MessageSrv_ConnectionClosed()
        {
            this.dispather4AutoBind.BeginInvoke(new Action(() =>
            {
                this.ConnectLoginBtnIsEnabled = true;
                MsgManager.SendMsg("ShowBox", "连接已断，请退出!");
            }));
        }


        private void DisconnectNdLogout()
        {
            messageSrv.Disconnect();
        }

        #endregion
    }
}
