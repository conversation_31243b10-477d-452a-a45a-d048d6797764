﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// RECORD_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class RECORD_LIST
	{
		public RECORD_LIST()
		{
			
		}
		
		private int _record_list_id;
		private int _record_id;
		private int _plan_list_id;
		private System.Decimal _record_list_quantity;
		private int _goods_id;
		private string _record_list_remark;
		private string _goods_property1;
		private string _goods_property2;
		private string _goods_property3;
		private string _goods_property4;
		private string _goods_property5;
		private string _goods_property6;
		private string _goods_property7;
		private string _goods_property8;
		private string _box_barcode;
		
		///<sumary>
		/// 记录列表编号
        ///</sumary>
        [DataMember]
		public int RECORD_LIST_ID
		{
			get{return _record_list_id;}
			set{_record_list_id = value;}
		}
		///<sumary>
		/// 记录编号
        ///</sumary>
        [DataMember]
		public int RECORD_ID
		{
			get{return _record_id;}
			set{_record_id = value;}
		}
		///<sumary>
		/// 计划列表编号
        ///</sumary>
        [DataMember]
		public int PLAN_LIST_ID
		{
			get{return _plan_list_id;}
			set{_plan_list_id = value;}
		}
		///<sumary>
		/// 数量
        ///</sumary>
        [DataMember]
		public System.Decimal RECORD_LIST_QUANTITY
		{
			get{return _record_list_quantity;}
			set{_record_list_quantity = value;}
		}
		///<sumary>
		/// 物料编号
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string RECORD_LIST_REMARK
		{
			get{return _record_list_remark;}
			set{_record_list_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY1
		{
			get{return _goods_property1;}
			set{_goods_property1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY2
		{
			get{return _goods_property2;}
			set{_goods_property2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY3
		{
			get{return _goods_property3;}
			set{_goods_property3 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY4
		{
			get{return _goods_property4;}
			set{_goods_property4 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY5
		{
			get{return _goods_property5;}
			set{_goods_property5 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY6
		{
			get{return _goods_property6;}
			set{_goods_property6 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY7
		{
			get{return _goods_property7;}
			set{_goods_property7 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY8
		{
			get{return _goods_property8;}
			set{_goods_property8 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
	}
}
