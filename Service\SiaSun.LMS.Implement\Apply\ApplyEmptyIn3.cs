﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Apply
{
    /// <summary>
    /// 三期3楼线边仓空箱入库申请
    /// PS.空箱库存不进入线边仓，经线边仓堆垛机、3楼提升机传输至5楼
    /// </summary>
    public class ApplyEmptyIn3 : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyEmptyIn3.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));
                
                //开事务
                this._P_Base_House.BeginTransaction(true);

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);

                
                //空箱不应有库存&任务
                if (mMANAGE_MAIN != null || mSTORAGE_MAIN != null)
                {
                    bResult = false;
                    sResult = string.Format("申请条码存在库存或任务，请根据箱子实际情况将多余的任务删除或将异常的库存出库");
                    return bResult;
                }
                else
                {
                    //生成入库任务
                    List<Model.MANAGE_LIST> listMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                    mMANAGE_LIST.GOODS_ID = 1;  //空料箱
                    mMANAGE_LIST.GOODS_PROPERTY1 = Enum.CellModel.EmptyBox.ToString("d");//箱型
                    mMANAGE_LIST.GOODS_PROPERTY2 = "green";//颜色
                    mMANAGE_LIST.MANAGE_LIST_QUANTITY = Convert.ToDecimal(1.00);

                    listMANAGE_LIST.Add(mMANAGE_LIST);

                    mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_OPERATOR = string.Format("{0}申请", mIO_CONTROL_APPLY.DEVICE_CODE);
                    //mMANAGE_MAIN.MANAGE_OPERATOR = mSTORAGE_MAIN.STORAGE_PICK_OPERATOR;
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.START_CELL_ID = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE).CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                    mMANAGE_MAIN.END_CELL_ID = 0;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    string manageLevel = string.Empty;
                    mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("ApplyTaskLevel", out manageLevel) ? manageLevel : "0";


                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();   //上架不需要使用TASK_SOURCE判断接口
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageStockIn.ToString();

                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";


                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;

                    

                    bResult = this._S_ManageService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, listMANAGE_LIST, false, true, false, false, true }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    string nextStation = string.Empty;
                    if(this._S_SystemService.GetSysParameter("MoveToFloor5EndStation", out nextStation))
                    {
                        bResult = this._S_ManageService.ControlCreate(3, mIO_CONTROL_APPLY.STOCK_BARCODE, "1", mIO_CONTROL_APPLY.DEVICE_CODE, "1", nextStation, "5", out sResult, true, false);
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("ApplyEmptyIn3.ApplyHandle:三期空箱入库查找MoveToFloor5EndStation失败");
                    }
                }
            }
            catch(Exception ex)
            {
                this._P_Base_House.RollBackTransaction(true);

                bResult = false;

                sResult = string.Format("处理申请时发生异常 {0}_ApplyEmptyIn3", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(true);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(true);
                }

                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyEmptyTrans.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
