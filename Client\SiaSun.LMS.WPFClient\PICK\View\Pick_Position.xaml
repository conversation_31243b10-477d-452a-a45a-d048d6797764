﻿<UserControl x:Class="SiaSun.LMS.WPFClient.PICK.View.Pick_Position"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             xmlns:i="http://schemas.microsoft.com/expression/2010/interactivity"
             mc:Ignorable="d" 
             MinWidth="240" MinHeight="220">
    <Grid>
        <Grid.Resources>
            <local:InformationBackgroundConverter x:Key="InformationHighlightBackgroundConverter"
                                                   DefaultBrush="RoyalBlue" HighlightBrush="DeepPink">
            </local:InformationBackgroundConverter>
            
            <Style x:Key="GroupBoxStyle1" TargetType="{x:Type GroupBox}">
                <Setter Property="BorderBrush" Value="#D5DFE5"/>
                <Setter Property="BorderThickness" Value="1"/>
                <Setter Property="Template">
                    <Setter.Value>
                        <ControlTemplate TargetType="{x:Type GroupBox}">
                            <Grid SnapsToDevicePixels="true">
                                <Grid.ColumnDefinitions>
                                    <ColumnDefinition Width="6"/>
                                    <ColumnDefinition Width="Auto"/>
                                    <ColumnDefinition Width="*"/>
                                    <ColumnDefinition Width="6"/>
                                </Grid.ColumnDefinitions>
                                <Grid.RowDefinitions>
                                    <RowDefinition Height="Auto"/>
                                    <RowDefinition Height="Auto" MinHeight="59"/>
                                    <RowDefinition Height="*"/>
                                    <RowDefinition Height="6"/>
                                </Grid.RowDefinitions>
                                <Border BorderBrush="Transparent" BorderThickness="{TemplateBinding BorderThickness}" Grid.ColumnSpan="4" Grid.Column="0" CornerRadius="4" Grid.Row="0" Grid.RowSpan="4" Background="#FFE5E5E5" Margin="0,-0.25,0,0.25">
                                    <Border.Effect>
                                        <DropShadowEffect Color="#FFAAAAAA" Direction="350"/>
                                    </Border.Effect>
                                </Border>
                                <Border x:Name="Header" Grid.Column="1" Padding="3,1,3,0" Grid.Row="1" Grid.RowSpan="1" HorizontalAlignment="Right" Background="{x:Null}" Margin="0" Height="16.96" VerticalAlignment="Top"/>
                                <ContentPresenter Grid.ColumnSpan="2" Grid.Column="1" Margin="{TemplateBinding Padding}" Grid.Row="2" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" Grid.RowSpan="1"/>
                                <Border BorderBrush="White" BorderThickness="{TemplateBinding BorderThickness}" Grid.ColumnSpan="4" CornerRadius="4" Grid.Row="1" Grid.RowSpan="3">
                                    <Border BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3">
                                        <Border BorderBrush="White" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2"/>
                                    </Border>
                                </Border>
                                <Grid x:Name="HeaderGrid" Height="47.2" VerticalAlignment="Stretch" Grid.Column="2" Grid.ColumnSpan="2" Grid.RowSpan="1" Margin="0,7.982,-16,3.818" Grid.Row="1" HorizontalAlignment="Right">
                                    <Path Data="M12.19,0 L12.290733,14.847 -1.3000648E-08,14.847 z" Height="16.1" Margin="0,0,8.067,0" RenderTransformOrigin="0.499999978361064,0.499999995889058" Stretch="Fill" Stroke="Black" StrokeThickness="0" VerticalAlignment="Top" HorizontalAlignment="Right" Width="12.29" >
                                        <Path.Fill>
                                            <LinearGradientBrush EndPoint="0.466,2.201" StartPoint="0.5,0">
                                                <GradientStop Color="#C66A6A6A" Offset="1"/>
                                                <GradientStop Color="#E1434343" Offset="0.855"/>
                                                <GradientStop Color="#FFC6C6C6" Offset="0.11"/>
                                            </LinearGradientBrush>
                                        </Path.Fill>
                                        <Path.RenderTransform>
                                            <TransformGroup>
                                                <ScaleTransform/>
                                                <SkewTransform/>
                                                <RotateTransform Angle="90.087"/>
                                                <TranslateTransform Y="-6.04399277075815" X="6.0531771644038841"/>
                                            </TransformGroup>
                                        </Path.RenderTransform>
                                    </Path>
                                    <Border BorderBrush="Black" BorderThickness="0" Margin="0,8.061,0,0" CornerRadius="16,0,0,16" Background="White">
                                        <Border.Effect>
                                            <DropShadowEffect Direction="195" BlurRadius="10" Opacity="0.305" ShadowDepth="6"/>
                                        </Border.Effect>
                                        <Border x:Name="contentBorder" BorderBrush="Black" Margin="6,6,0,6" CornerRadius="16,0,0,16">
                                            <Border.Background>
                                                <LinearGradientBrush EndPoint="1.002,0.498" StartPoint="-0.024,0.502">
                                                    <!--<GradientStop Color="#FF678B03" Offset="0.027"/>
                                                    <GradientStop Color="#FFA4C43D" Offset="0.948"/>
                                                    <GradientStop Color="#FFADCA54" Offset="0.969"/>
                                                    <GradientStop Color="#FFA7C646" Offset="0.975"/>
                                                    <GradientStop Color="#FFC9EF4C" Offset="0.994"/>-->
                                                    <GradientStop Color="#FF87CEEB" Offset="0.027"/>
                                                    <GradientStop Color="#FF87CEFF" Offset="0.948"/>
                                                    <GradientStop Color="#FF87CEFA" Offset="0.969"/>
                                                    <GradientStop Color="#FF7EC0EE" Offset="0.975"/>
                                                    <GradientStop Color="#FF97FFFF" Offset="0.994"/>
                                                </LinearGradientBrush>
                                            </Border.Background>
                                            <Grid>
                                                <ContentControl HorizontalAlignment="Left" Margin="20,0,23,0" d:LayoutOverrides="Height" VerticalAlignment="Center" Foreground="White">
                                                    <ContentPresenter ContentSource="Header" RecognizesAccessKey="True" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" HorizontalAlignment="Stretch" VerticalAlignment="Center" Margin="0" Width="212.323"/>
                                                </ContentControl>
                                            </Grid>
                                        </Border>
                                    </Border>
                                </Grid>
                            </Grid>
                        </ControlTemplate>
                    </Setter.Value>
                </Setter>
            </Style>
        </Grid.Resources>
        <GroupBox Style="{StaticResource GroupBoxStyle1}" Header="{Binding PositionName}">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>
                <Border Grid.Row="0" BorderThickness="3" BorderBrush="Black" Height="100" Width="162" Margin="5">
                    <Rectangle  Fill="{Binding Path=bPick,Converter={StaticResource InformationHighlightBackgroundConverter}}" Stretch="Fill">
                        <i:Interaction.Triggers>
                            <i:EventTrigger EventName="MouseUp">
                                <i:InvokeCommandAction Command="{Binding MouseUpCommand}"/>
                            </i:EventTrigger>
                        </i:Interaction.Triggers>
                    </Rectangle>
                </Border>
                <TextBlock Margin="5" Grid.Row="1" Height="30" FontSize="20" FontWeight="Bold" HorizontalAlignment="Center"
                           Text="{Binding PositionStockBarcode}"></TextBlock>
                <!--<uc:ComboBoxQuery Margin="5" Grid.Row="2" FontSize="20" FontWeight="Bold" ComboBoxHeight="35" ComboBoxWidth="100" 
                                  ButtonHeight="35" ButtonWidth="60" ButtonContent="查询计划" 
                                  ComboBoxDataTable="{Binding PlanDataTable}"  HorizontalAlignment="Center"
                                  SelectItem="{Binding Plan_ID,Mode=TwoWay}"
                                  ComboBoxDisplayMemberPath="PLAN_CODE"
                                  ></uc:ComboBoxQuery>-->
                <ComboBox Margin="5" Grid.Row="2" FontSize="16" FontWeight="Bold" Width="200" Height="35" HorizontalAlignment="Center"
                          ItemsSource="{Binding PlanDataTable}" SelectedValue="{Binding Plan_ID,Mode=TwoWay}" SelectedValuePath="PLAN_ID" DisplayMemberPath="PLAN_CODE"></ComboBox>
                <StackPanel Margin="2" Grid.Row="3" Orientation="Horizontal" HorizontalAlignment="Center">
                    <Button Margin="5" Height="35" Width="60" FontSize="16"
                            FontWeight="Bold" BorderBrush="Ivory" BorderThickness="1" 
                            Background="Pink" Style="{x:Null}" Command="{Binding CommandPick}">清单</Button>
                    <Button Margin="5" Height="35" Width="60" 
                            FontSize="16" FontWeight="Bold" BorderBrush="Ivory" BorderThickness="1" 
                            Background="GreenYellow" Style="{x:Null}" Command="{Binding CommandLock}">未拣</Button>
                    <Button Margin="5" Height="35" Width="60" 
                            FontSize="16" FontWeight="Bold" BorderBrush="Ivory" BorderThickness="1" 
                            Background="WhiteSmoke" Style="{x:Null}" Command="{Binding CmdPrintWBSContent}">打印</Button>
                </StackPanel>
            </Grid>
        </GroupBox>
    </Grid>
</UserControl>
