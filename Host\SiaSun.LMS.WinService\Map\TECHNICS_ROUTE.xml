﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="TECHNICS_ROUTE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="TECHNICS_ROUTE" type="SiaSun.LMS.Model.TECHNICS_ROUTE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="TECHNICS_ROUTE">
			<result property="ROUTE_ID" column="route_id" />
			<result property="TECHNICS_ID" column="technics_id" />
			<result property="EXCEPTIONAL_ROUTE_ID" column="exceptional_route_id" />
			<result property="ROUTE_DESCRIPTION" column="route_description" />
			<result property="ROUTE_ORDER" column="route_order" />
			<result property="TASK_TYPE" column="task_type" />
			<result property="START_AREA_ID" column="start_area_id" />
			<result property="END_AREA_ID" column="end_area_id" />
			<result property="START_POSITION" column="start_position" />
			<result property="END_POSITION" column="end_position" />
			<result property="START_NODE_FLAG" column="start_node_flag" />
			<result property="END_NODE_FLAG" column="end_node_flag" />
			<result property="DEVICE_TYPE" column="device_type" />
			<result property="TASK_LEVEL" column="task_level" />
			<result property="CHECK_START_POSITION_FLAG" column="check_start_position_flag" />
			<result property="CHECK_END_POSITION_FLAG" column="check_end_position_flag" />
			<result property="START_STATUS" column="start_status" />
			<result property="FINISH_STATUS" column="finish_status" />
			<result property="EP_START_CELL_STATUS" column="ep_start_cell_status" />
			<result property="EP_START_RUN_STATUS" column="ep_start_run_status" />
			<result property="SP_END_CELL_STATUS" column="sp_end_cell_status" />
			<result property="SP_END_RUN_STATUS" column="sp_end_run_status" />
			<result property="EP_END_CELL_STATUS" column="ep_end_cell_status" />
			<result property="EP_END_RUN_STATUS" column="ep_end_run_status" />
			<result property="ROUTE_ACTIONS" column="route_actions" />
			<result property="ROUTE_REMARK" column="route_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="TECHNICS_ROUTE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  route_id,
				  technics_id,
				  exceptional_route_id,
				  route_description,
				  route_order,
				  task_type,
				  start_area_id,
				  end_area_id,
				  start_position,
				  end_position,
				  start_node_flag,
				  end_node_flag,
				  device_type,
				  task_level,
				  check_start_position_flag,
				  check_end_position_flag,
				  start_status,
				  finish_status,
				  ep_start_cell_status,
				  ep_start_run_status,
				  sp_end_cell_status,
				  sp_end_run_status,
				  ep_end_cell_status,
				  ep_end_run_status,
				  route_actions,
				  route_remark
			From TECHNICS_ROUTE
		</select>
		
		<select id="TECHNICS_ROUTE_SELECT_BY_ID" parameterClass="int" extends = "TECHNICS_ROUTE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					route_id=#ROUTE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="TECHNICS_ROUTE_INSERT" parameterClass="TECHNICS_ROUTE">
      Insert Into TECHNICS_ROUTE (
      route_id,
      technics_id,
      exceptional_route_id,
      route_description,
      route_order,
      task_type,
      start_area_id,
      end_area_id,
      start_position,
      end_position,
      start_node_flag,
      end_node_flag,
      device_type,
      task_level,
      check_start_position_flag,
      check_end_position_flag,
      start_status,
      finish_status,
      ep_start_cell_status,
      ep_start_run_status,
      sp_end_cell_status,
      sp_end_run_status,
      ep_end_cell_status,
      ep_end_run_status,
      route_actions,
      route_remark
      )Values(
      #ROUTE_ID#,
      #TECHNICS_ID#,
      #EXCEPTIONAL_ROUTE_ID#,
      #ROUTE_DESCRIPTION#,
      #ROUTE_ORDER#,
      #TASK_TYPE#,
      #START_AREA_ID#,
      #END_AREA_ID#,
      #START_POSITION#,
      #END_POSITION#,
      #START_NODE_FLAG#,
      #END_NODE_FLAG#,
      #DEVICE_TYPE#,
      #TASK_LEVEL#,
      #CHECK_START_POSITION_FLAG#,
      #CHECK_END_POSITION_FLAG#,
      #START_STATUS#,
      #FINISH_STATUS#,
      #EP_START_CELL_STATUS#,
      #EP_START_RUN_STATUS#,
      #SP_END_CELL_STATUS#,
      #SP_END_RUN_STATUS#,
      #EP_END_CELL_STATUS#,
      #EP_END_RUN_STATUS#,
      #ROUTE_ACTIONS#,
      #ROUTE_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="ROUTE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="TECHNICS_ROUTE_UPDATE" parameterClass="TECHNICS_ROUTE">
      Update TECHNICS_ROUTE Set
      route_id=#ROUTE_ID#,
      technics_id=#TECHNICS_ID#,
      exceptional_route_id=#EXCEPTIONAL_ROUTE_ID#,
      route_description=#ROUTE_DESCRIPTION#,
      route_order=#ROUTE_ORDER#,
      task_type=#TASK_TYPE#,
      start_area_id=#START_AREA_ID#,
      end_area_id=#END_AREA_ID#,
      start_position=#START_POSITION#,
      end_position=#END_POSITION#,
      start_node_flag=#START_NODE_FLAG#,
      end_node_flag=#END_NODE_FLAG#,
      device_type=#DEVICE_TYPE#,
      task_level=#TASK_LEVEL#,
      check_start_position_flag=#CHECK_START_POSITION_FLAG#,
      check_end_position_flag=#CHECK_END_POSITION_FLAG#,
      start_status=#START_STATUS#,
      finish_status=#FINISH_STATUS#,
      ep_start_cell_status=#EP_START_CELL_STATUS#,
      ep_start_run_status=#EP_START_RUN_STATUS#,
      sp_end_cell_status=#SP_END_CELL_STATUS#,
      sp_end_run_status=#SP_END_RUN_STATUS#,
      ep_end_cell_status=#EP_END_CELL_STATUS#,
      ep_end_run_status=#EP_END_RUN_STATUS#,
      route_actions=#ROUTE_ACTIONS#,
      route_remark=#ROUTE_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					route_id=#ROUTE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="TECHNICS_ROUTE_DELETE" parameterClass="int">
			Delete From TECHNICS_ROUTE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					route_id=#ROUTE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>