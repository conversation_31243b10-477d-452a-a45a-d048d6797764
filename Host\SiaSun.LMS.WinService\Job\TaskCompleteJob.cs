﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class TaskCompleteJob : IJob
    {  
        public void Execute(IJobExecutionContext context)
        {
            //bool bResult = true;
            string sResult = string.Empty;
            DateTime dateTimeGC = DateTime.Now;

            try
            {
                //MainApp.BaseService._S_ManageService.ControlTranslate(3, out sResult);
                MainApp.BaseService._S_ManageService.ControlTranslate("", out sResult);
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("TaskCompleteJob.Execute:异常", ex);
                throw;
            }
            finally
            {
                if (sResult != string.Empty)
                {
                    Program.sysLog.WarnFormat("TaskCompleteJob.Execute:处理Control任务失败 {0}", sResult);
                }

                if (dateTimeGC.AddMinutes(20) < DateTime.Now)
                {
                    dateTimeGC = DateTime.Now;
                    System.Diagnostics.Process.GetCurrentProcess().MinWorkingSet = new IntPtr(20);
                }
            }
        }
    }
}
