﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="Report" targetNamespace="http://tempuri.org/Report.xsd" xmlns:mstns="http://tempuri.org/Report.xsd" xmlns="http://tempuri.org/Report.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="Report" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_DataSetName="Report" msprop:Generator_UserDSName="Report">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="V_GOODS_OUT" msprop:Generator_TableClassName="V_GOODS_OUTDataTable" msprop:Generator_TableVarName="tableV_GOODS_OUT" msprop:Generator_RowChangedName="V_GOODS_OUTRowChanged" msprop:Generator_TablePropName="V_GOODS_OUT" msprop:Generator_RowDeletingName="V_GOODS_OUTRowDeleting" msprop:Generator_RowChangingName="V_GOODS_OUTRowChanging" msprop:Generator_RowEvHandlerName="V_GOODS_OUTRowChangeEventHandler" msprop:Generator_RowDeletedName="V_GOODS_OUTRowDeleted" msprop:Generator_RowClassName="V_GOODS_OUTRow" msprop:Generator_UserTableName="V_GOODS_OUT" msprop:Generator_RowEvArgName="V_GOODS_OUTRowChangeEvent">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GOODS_NAME" msprop:Generator_ColumnVarNameInTable="columnGOODS_NAME" msprop:Generator_ColumnPropNameInRow="GOODS_NAME" msprop:Generator_ColumnPropNameInTable="GOODS_NAMEColumn" msprop:Generator_UserColumnName="GOODS_NAME" type="xs:string" minOccurs="0" />
              <xs:element name="RECORD_LIST_QUANTITY" msprop:Generator_ColumnVarNameInTable="columnRECORD_LIST_QUANTITY" msprop:Generator_ColumnPropNameInRow="RECORD_LIST_QUANTITY" msprop:Generator_ColumnPropNameInTable="RECORD_LIST_QUANTITYColumn" msprop:Generator_UserColumnName="RECORD_LIST_QUANTITY" type="xs:string" minOccurs="0" />
              <xs:element name="QUANTITY" msprop:Generator_ColumnVarNameInTable="columnQUANTITY" msprop:Generator_ColumnPropNameInRow="QUANTITY" msprop:Generator_ColumnPropNameInTable="QUANTITYColumn" msprop:Generator_UserColumnName="QUANTITY" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_CODE" msprop:Generator_ColumnVarNameInTable="columnGOODS_CODE" msprop:Generator_ColumnPropNameInRow="GOODS_CODE" msprop:Generator_ColumnPropNameInTable="GOODS_CODEColumn" msprop:Generator_UserColumnName="GOODS_CODE" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY1" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY1" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY1" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY1Column" msprop:Generator_UserColumnName="GOODS_PROPERTY1" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY2" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY2" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY2" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY2Column" msprop:Generator_UserColumnName="GOODS_PROPERTY2" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY3" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY3" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY3" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY3Column" msprop:Generator_UserColumnName="GOODS_PROPERTY3" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY4" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY4" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY4" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY4Column" msprop:Generator_UserColumnName="GOODS_PROPERTY4" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY5" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY5" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY5" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY5Column" msprop:Generator_UserColumnName="GOODS_PROPERTY5" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY6" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY6" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY6" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY6Column" msprop:Generator_UserColumnName="GOODS_PROPERTY6" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY7" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY7" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY7" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY7Column" msprop:Generator_UserColumnName="GOODS_PROPERTY7" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY8" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY8" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY8" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY8Column" msprop:Generator_UserColumnName="GOODS_PROPERTY8" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
        <xs:element name="InOutQuery" msprop:Generator_TableClassName="InOutQueryDataTable" msprop:Generator_TableVarName="tableInOutQuery" msprop:Generator_TablePropName="InOutQuery" msprop:Generator_RowDeletingName="InOutQueryRowDeleting" msprop:Generator_RowChangingName="InOutQueryRowChanging" msprop:Generator_RowEvHandlerName="InOutQueryRowChangeEventHandler" msprop:Generator_RowDeletedName="InOutQueryRowDeleted" msprop:Generator_UserTableName="InOutQuery" msprop:Generator_RowChangedName="InOutQueryRowChanged" msprop:Generator_RowEvArgName="InOutQueryRowChangeEvent" msprop:Generator_RowClassName="InOutQueryRow">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="GOODS_ID" msprop:Generator_ColumnVarNameInTable="columnGOODS_ID" msprop:Generator_ColumnPropNameInRow="GOODS_ID" msprop:Generator_ColumnPropNameInTable="GOODS_IDColumn" msprop:Generator_UserColumnName="GOODS_ID" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_CODE" msprop:Generator_ColumnVarNameInTable="columnGOODS_CODE" msprop:Generator_ColumnPropNameInRow="GOODS_CODE" msprop:Generator_ColumnPropNameInTable="GOODS_CODEColumn" msprop:Generator_UserColumnName="GOODS_CODE" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_NAME" msprop:Generator_ColumnVarNameInTable="columnGOODS_NAME" msprop:Generator_ColumnPropNameInRow="GOODS_NAME" msprop:Generator_ColumnPropNameInTable="GOODS_NAMEColumn" msprop:Generator_UserColumnName="GOODS_NAME" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_UNIT" msprop:Generator_ColumnVarNameInTable="columnGOODS_UNIT" msprop:Generator_ColumnPropNameInRow="GOODS_UNIT" msprop:Generator_ColumnPropNameInTable="GOODS_UNITColumn" msprop:Generator_UserColumnName="GOODS_UNIT" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_CONST_PROPERTY1" msprop:Generator_ColumnVarNameInTable="columnGOODS_CONST_PROPERTY1" msprop:Generator_ColumnPropNameInRow="GOODS_CONST_PROPERTY1" msprop:Generator_ColumnPropNameInTable="GOODS_CONST_PROPERTY1Column" msprop:Generator_UserColumnName="GOODS_CONST_PROPERTY1" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_CONST_PROPERTY2" msprop:Generator_ColumnVarNameInTable="columnGOODS_CONST_PROPERTY2" msprop:Generator_ColumnPropNameInRow="GOODS_CONST_PROPERTY2" msprop:Generator_ColumnPropNameInTable="GOODS_CONST_PROPERTY2Column" msprop:Generator_UserColumnName="GOODS_CONST_PROPERTY2" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_CONST_PROPERTY3" msprop:Generator_ColumnVarNameInTable="columnGOODS_CONST_PROPERTY3" msprop:Generator_ColumnPropNameInRow="GOODS_CONST_PROPERTY3" msprop:Generator_ColumnPropNameInTable="GOODS_CONST_PROPERTY3Column" msprop:Generator_UserColumnName="GOODS_CONST_PROPERTY3" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY1" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY1" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY1" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY1Column" msprop:Generator_UserColumnName="GOODS_PROPERTY1" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY2" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY2" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY2" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY2Column" msprop:Generator_UserColumnName="GOODS_PROPERTY2" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY3" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY3" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY3" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY3Column" msprop:Generator_UserColumnName="GOODS_PROPERTY3" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY4" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY4" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY4" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY4Column" msprop:Generator_UserColumnName="GOODS_PROPERTY4" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY5" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY5" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY5" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY5Column" msprop:Generator_UserColumnName="GOODS_PROPERTY5" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY6" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY6" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY6" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY6Column" msprop:Generator_UserColumnName="GOODS_PROPERTY6" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY7" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY7" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY7" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY7Column" msprop:Generator_UserColumnName="GOODS_PROPERTY7" type="xs:string" minOccurs="0" />
              <xs:element name="GOODS_PROPERTY8" msprop:Generator_ColumnVarNameInTable="columnGOODS_PROPERTY8" msprop:Generator_ColumnPropNameInRow="GOODS_PROPERTY8" msprop:Generator_ColumnPropNameInTable="GOODS_PROPERTY8Column" msprop:Generator_UserColumnName="GOODS_PROPERTY8" type="xs:string" minOccurs="0" />
              <xs:element name="STORAGE_QUANTITY_START" msprop:Generator_ColumnVarNameInTable="columnSTORAGE_QUANTITY_START" msprop:Generator_ColumnPropNameInRow="STORAGE_QUANTITY_START" msprop:Generator_ColumnPropNameInTable="STORAGE_QUANTITY_STARTColumn" msprop:Generator_UserColumnName="STORAGE_QUANTITY_START" type="xs:decimal" minOccurs="0" />
              <xs:element name="IN_QUANTITY_StartToEnd" msprop:Generator_ColumnVarNameInTable="columnIN_QUANTITY_StartToEnd" msprop:Generator_ColumnPropNameInRow="IN_QUANTITY_StartToEnd" msprop:Generator_ColumnPropNameInTable="IN_QUANTITY_StartToEndColumn" msprop:Generator_UserColumnName="IN_QUANTITY_StartToEnd" type="xs:decimal" minOccurs="0" />
              <xs:element name="OUT_QUANTITY_StartToEnd" msprop:Generator_ColumnVarNameInTable="columnOUT_QUANTITY_StartToEnd" msprop:Generator_ColumnPropNameInRow="OUT_QUANTITY_StartToEnd" msprop:Generator_ColumnPropNameInTable="OUT_QUANTITY_StartToEndColumn" msprop:Generator_UserColumnName="OUT_QUANTITY_StartToEnd" type="xs:decimal" minOccurs="0" />
              <xs:element name="STORAGE_QUANTITY_END" msprop:Generator_ColumnVarNameInTable="columnSTORAGE_QUANTITY_END" msprop:Generator_ColumnPropNameInRow="STORAGE_QUANTITY_END" msprop:Generator_ColumnPropNameInTable="STORAGE_QUANTITY_ENDColumn" msprop:Generator_UserColumnName="STORAGE_QUANTITY_END" type="xs:decimal" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>