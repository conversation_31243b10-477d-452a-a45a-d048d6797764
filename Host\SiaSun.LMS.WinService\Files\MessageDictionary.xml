﻿<?xml version="1.0" encoding="utf-8" ?>
<MessageDictionary>
  
  <!--通用标准关键字-->
  <Message Key="Title" Default="系统提示" en-US=""></Message>
  <Message Key="Header" Default="执行消息" en-US=""></Message>
  <Message Key="OK" Default="执行完毕！" en-US=""></Message>
  <Message Key="Faile" Default="执行失败！" en-US=""></Message>

  <Message Key="Null" Default="请检查{0}输入值是否空值！" en-US=""></Message>
  <Message Key="Type" Default="请检查{0}输入值数据类型是否合法！" en-US=""></Message>
  <Message Key="Lenght" Default="请检查{0}输入值是否为空或是否重复或超出最大长度范围！" en-US=""></Message>
  <Message Key="Weight" Default="请检查{0}重量是否合法！" en-US=""></Message>
  <Message Key="Input" Default="请检查是否输入{0}数据！" en-US=""></Message>
  <Message Key="Data" Default="请检查输入{0}数据是否合法！" en-US=""></Message>
  <Message Key="Exists" Default="请检查{0}数据是否重复！" en-US=""></Message>
  
  <Message Key="SelectCount" Default="请检查是否选中{0}数据记录！" en-US=""></Message>
  <Message Key="AllowSelectOneOnly" Default="仅且只能选择一个选项，请重新选择！" en-US=""></Message>
  <Message Key="AffectCount" Default="操作失败，影响行数为0！" en-US=""></Message>
  <Message Key="FailRollBack" Default="操作失败，任务回滚！" en-US=""></Message>

  <Message Key="Save" Default="您确认执行{0}保存操作？" en-US=""></Message>
  <Message Key="Delete" Default="您确认执行{0}删除操作？" en-US=""></Message>
  <Message Key="Cancel" Default="您确认执行{0}取消操作？" en-US=""></Message>
  <Message Key="Print" Default="您确认执行{0}打印操作？" en-US=""></Message>
  <Message Key="Close" Default="您确认关闭{0}窗口？" en-US=""></Message>
  <Message Key="Exit" Default="您确认退出{0}系统？" en-US=""></Message>
  
  <Message Key="CheckStack" Default="无法获得组盘/组垛实例，请检查托盘库存是否存在！{0}." en-US=""></Message>
  <Message Key="CheckStorage" Default="请检查托盘/周转箱库存是否存在或是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStoragePosition" Default="请检查库存中的存储位置是否合法！{0}." en-US=""></Message>
  <Message Key="CheckTechnicsType" Default="请检查输送流程是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStockBarCode" Default="请检查托盘条码是否合法！{0}" en-US=""></Message>
  <Message Key="CheckStockBarCodeArea" Default="请检查托盘条码的使用区域是否合法！{0}" en-US=""></Message>
  <Message Key="CheckStackNo" Default="请检查垛号是否合法！{0}." en-US=""></Message>
  <Message Key="CheckWareHouse" Default="请检查仓库是否合法！{0}." en-US=""></Message>
  <Message Key="CheckLaneway" Default="请检查巷道是否合法！{0}." en-US=""></Message>
  <Message Key="CheckArea" Default="请检查起始库区或目标库区是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStartArea" Default="请检查起始库区是否合法！{0}." en-US=""></Message>
  <Message Key="CheckEndArea" Default="请检查目标库区是否合法！{0}." en-US=""></Message>
  <Message Key="CheckPosition" Default="请检查起始位置或目标位置是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStartPosition" Default="请检查起始位置是否合法！{0}." en-US=""></Message>
  <Message Key="CheckEndPosition" Default="请检查目标位置是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStationCode" Default="请检查站台条码是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStationDeviceCode" Default="站台设备条码不存在，请检查站台设备条码是否合法！{0}." en-US=""></Message>
  <Message Key="CheckStationLaneway" Default="请检查起始位置与目标位置是否属于相同巷道！{0}." en-US=""></Message>
  <Message Key="CheckCellModel" Default="请检查货位规格是否合法！{0}." en-US=""></Message>
  <Message Key="CheckOccupyPercent" Default="请检查托盘占用率是否合法！{0}." en-US=""></Message>
  <Message Key="CheckLoginUser" Default="请检查用户名或密码是否合法！{0}." en-US=""></Message>
  <Message Key="CheckProType" Default="品种不允许为空，请输入品种！{0}." en-US=""></Message>
  <Message Key="CheckAssisQuantity" Default="请检查千米数是否合法！{0}." en-US=""></Message>
  <Message Key="CheckBoxNo" Default="请检查箱/卷号是否空！{0}." en-US=""></Message>

  <Message Key="ConfirmExecute" Default="您确认执行{0}操作？" en-US=""></Message>
  <Message Key="ConfirmAssembly" Default="确定执行{0}配盘操作？" en-US=""></Message>
  <Message Key="ConfirmCreateTask" Default="您确认创建{0}任务？" en-US=""></Message>
  <Message Key="ConfirmCreatePlan" Default="您确认创建{0}计划工单？" en-US=""></Message>

  <!--App-->
  <Message Key="App_CheckKey" Default="Key名称不允许空！" en-US=""></Message>
  
  <!--Import-->
  <Message Key="Import_CheckExists" Default="请检查是否设置导入模板映射关系！" en-US=""></Message>
  
  <!--User-->
  <Message Key="User_CheckOldPassword" Default="旧密码输入错误！" en-US=""></Message>
  <Message Key="User_CheckNewWithOldPwd" Default="请检查两次输入的密码是否一致！" en-US=""></Message>
  <Message Key="User_CheckUserExists" Default="请检查用户是否已经存在！{0}." en-US=""></Message>

  <!--Role-->
  <Message Key="Role_CheckRoleExists" Default="请检查角色是否已经存在！{0}." en-US=""></Message>

  <!--Goods-->
  <Message Key="Goods_CheckGoodsExists" Default="请检查物料基础信息是否存在！{0}." en-US=""></Message>
  <Message Key="Goods_CheckGoodsCode" Default="请检查物料编码是否合法！{0}." en-US=""></Message>
  
  <!--Line-->
  <Message Key="Line_CheckLine" Default="请检查生产线是否存在或合法！{0}." en-US=""></Message>
  <Message Key="Line_CheckLineStaction" Default="请检查生产线对应的站台是否合法或是否设置！{0}." en-US=""></Message>

  
  <!--Technics-->
  <Message Key="Technics_CheckTechnicsExists" Default="请检查输送任务流程是否存在或参数是否合法！{0}." en-US=""></Message>
  <Message Key="Technics_CheckTechnicsMulti" Default="存在多个匹配流程，请检查流程参数是否足够或流程配置是否重复！{0}." en-US=""></Message>
  <Message Key="Technics_CheckRelativeTechnics" Default="请检查当前任务流程的关联输送流程是否存在或合法！{0}." en-US=""></Message>
  <Message Key="Technics_CheckNextTechnics" Default="请检查当前任务流程的下一输送流程是否存在或合法！{0}." en-US=""></Message>
  <Message Key="Technics_CheckStorageExists" Default="流程库存设置与实际库存不一致，请检查库存是否存在！{0}." en-US=""></Message>
  <Message Key="Technics_CheckRouteExists" Default="请检查输送任务流程路线是否存在！{0}." en-US=""></Message>
  <Message Key="Technics_CheckStartRouteExists" Default="请检查输送任务流程起始路线是否存在！{0}." en-US=""></Message>
  <Message Key="Technics_CheckEndRouteExists" Default="请检查输送任务流程结束路线是否存在！{0}." en-US=""></Message>
  <Message Key="Technics_CheckNextRoute" Default="请检查当前任务流程的下一流程路线是否存在或合法！{0}." en-US=""></Message>

  <!--Relation-->
  <Message Key="Relation_CheckRelationType" Default="请检查关系类型是否存在！关系类型编码：{0}." en-US=""></Message>
  <Message Key="Relation_CheckRelationExists" Default="请检查关系记录是否存在！关系编码：{0}." en-US=""></Message>
  
  <!--Storage-->
  <Message Key="Storage_CheckLogicStatus" Default="请检查输送任务物料与库存物料的工艺状态是否一致!{0}." en-US=""></Message>
  <Message Key="Storage_CheckStorageListID" Default="请检查输送任务中的库存清单编号与实际库存清单编号是否一致！{0}." en-US=""></Message>
  <Message Key="Storage_CheckStorageQuantity" Default="请检查库存数量是否合法或库存数量是否充足！{0}." en-US=""></Message>
  <Message Key="Storage_DeleteStorage" Default="删除库存失败！{0}." en-US=""></Message>
  <Message Key="Storage_CheckGoodsNoExists" Default="请检查物料条码是否存在！{0}." en-US=""></Message>

  <!--Plan-->
  <Message Key="Plan_CheckPlanExists" Default="请检查计划工单是否存在或参数是否合法！{0}." en-US=""></Message>
  <Message Key="Plan_CheckPlanCodeExists" Default="请检查单号是否已经存在！{0}." en-US=""></Message>
  <Message Key="Plan_CheckPlanStatus" Default="请检查计划单状态是否合法！{0}." en-US=""></Message>
  <Message Key="Plan_CheckSamePlanType" Default="发现存在相同类型的计划正在执行，请暂停或终止或完成该计划单后重试！{0}." en-US=""></Message>
  <Message Key="Plan_CheckPlanListExists" Default="请检查该计划是否存在计划清单！{0}." en-US=""></Message>

  <!--Manage-->
  <Message Key="Manage_CheckManageType" Default="请检查任务类型是否合法！{0}." en-US=""></Message>
  <Message Key="Manage_CheckManageExists" Default="请检查输送任务是否存在或任务状态是否合法！{0}." en-US=""></Message>
  <Message Key="Manage_CheckAddSameGoods" Default="请检查是否重复添加相同物料编码、相同物料属性的物料，如果重复请修改已存在物料的数量!{0}." en-US=""></Message>
  <Message Key="Manage_CheckAssemblyGoodsCount" Default="请检查是否选中配盘数据！{0}." en-US=""></Message>
  <Message Key="Manage_CheckManageStatus" Default="请检查输送任务状态是否合法！{0}." en-US=""></Message>
  <Message Key="Manage_CheckManageQuantity" Default="请检查物料是否存在或任务数量是否超出计划分配数量！{0}." en-US=""></Message>
  <Message Key="Manage_CheckStorageQuantity" Default="请检查物料是否存在或任务数量是否超出计划分配数量！{0}." en-US=""></Message>
  
  <Message Key="Manage_CheckStoragePosition" Default="请检查输送任务托盘库存位置与选择的起始货位是否一致！{0}." en-US=""></Message>
  <Message Key="Manage_CheckStartCellStatus" Default="请检查管理任务起始位置存储状态是否合法或是否启用该流程设置！{0}." en-US=""></Message>
  <Message Key="Manage_CheckStartRunStatus" Default="请检查管理任务起始位置运行状态是否合法或是否启用该流程设置！{0}." en-US=""></Message>
  <Message Key="Manage_CheckEndCellStatus" Default="请检查管理任务结束位置存储状态是否合法或是否启用该流程设置！{0}." en-US=""></Message>
  <Message Key="Manage_CheckEndRunStatus" Default="请检查管理任务结束位置运行状态是否合法或是否启用该流程设置！{0}." en-US=""></Message>
  <Message Key="Manage_CheckGoodsClassID" Default="请检查托盘内物料的类别与流程配置中的物料类别是否一致！{0}." en-US=""></Message>

  <Message Key="Manage_CheckSameCellModelWithStockType" Default="请检查输送任务托盘类型与货位规格是否一致！{0}." en-US=""></Message>
  <Message Key="Manage_CheckSameArea" Default="请检查输送任务起始位置与目标位置是否属于同一库区！{0}." en-US=""></Message>
  <Message Key="Manage_CheckSameLaneway" Default="请检查输送任务起始位置与目标位置是否属于同一巷道！{0}." en-US=""></Message>
  <Message Key="Manage_CheckSameCellZ" Default="极限排货位，起始位置与目标位置必须属于同侧货叉，即起始位置与目标位置的排值奇偶数一致！" en-US=""></Message>
  <Message Key="Manage_InPutStockBarcode" Default="请输入托盘条码" en-US=""></Message>   
  
  <!--Control-->
  <Message Key="Control_CheckControlExists" Default="请检查设备控制指令是否存在或指令状态是否合法！{0}." en-US=""></Message>
  <Message Key="Control_CheckControlStatus" Default="请检查设备控制指令状态是否合法！{0}." en-US=""></Message>
  <Message Key="Control_CheckChangeRouteCellType" Default="请检查改道前和改道后的输送位置类型是否一致！{0}." en-US=""></Message>
  <Message Key="ControlApply_CheckLanewayStockBarCode" Default="请检查托盘任务是否属于该巷道！{0}." en-US=""></Message>

  <!--项目特殊定义-->
  <Message Key="Pro_CheckGoodsNo" Default="请检查箔号是否合法！{0}." en-US=""></Message>
  <Message Key="TaskComplete" Default="任务下达完成！{0}" en-US="" />

  <Message Key="IsContinue" Default="{0}未输入数据，是否保持默认并继续" en-US="" />

  <Message Key="ConnectionString" Default="server=.;database=SSLMS_Metro;uid=sa;pwd=*****" en-US="" />
</MessageDictionary>