﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.ROLE_WINDOW_CONTROL"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="ROLE_WINDOW_CONTROL" Height="299" Width="385" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" MinHeight="200" MaxHeight="450" ></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>

        <GroupBox Grid.Row="0" Grid.ColumnSpan="3" Header="角色列表"  Margin="0,0,0,2">
            <uc:ucCommonDataGrid x:Name="gridRole"></uc:ucCommonDataGrid>             
        </GroupBox>
        
        <uc:ucTreeView x:Name="tvwWindow" Grid.Row="1" Grid.Column="0"  U_Header="窗体列表" Margin="1,2,2,1"></uc:ucTreeView>
        <GridSplitter Grid.Column="1" Grid.Row="1"  VerticalAlignment="Stretch" Width="2"></GridSplitter>
        <GroupBox Name="grpbControl" Grid.Column="2" Grid.Row="1" Header="窗体控件设置" Tag="窗体{0}-控件设置"  >
            <uc:DataGridTemplate x:Name="gridControl" CanUserAddRows="False" Margin="1,2,1,1" AutoGenerateColumns="False">
                <uc:DataGridTemplate.Columns>
                    <DataGridTextColumn Header="角色ID" IsReadOnly="True" Binding="{Binding Path=ROLE_ID, Mode=TwoWay}"></DataGridTextColumn>
                    <DataGridTextColumn Header="菜单ID" IsReadOnly="True" Binding="{Binding Path=MENU_ID, Mode=TwoWay}"></DataGridTextColumn>
                    <DataGridTextColumn Header="控件名称" IsReadOnly="True" Binding="{Binding Path=CONTROL_NAME, Mode=TwoWay}"></DataGridTextColumn>
                    <DataGridTextColumn Header="控件描述" IsReadOnly="True" Binding="{Binding Path=CONTROL_HEADER, Mode=TwoWay}"></DataGridTextColumn>
                    <DataGridTextColumn Header="[状态标识]" Binding="{Binding Path=FLAG, Mode=TwoWay}"></DataGridTextColumn>
                </uc:DataGridTemplate.Columns>
            </uc:DataGridTemplate>
        </GroupBox>        
        <GroupBox Grid.Row="2" Grid.ColumnSpan="3" Header="操作区"  >
            <Border  >
                <WrapPanel HorizontalAlignment="Center" Margin="5" ButtonBase.Click="WrapPanel_Click">
                    <StackPanel Orientation="Horizontal" >
                        <Button Name="btnSave"  Width="50">保存</Button>
                        <Button Name="btnRefresh"  Width="50">刷新</Button>
                    </StackPanel>
                </WrapPanel>
            </Border>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
