﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="GOODS_TEMPLATE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="GOODS_TEMPLATE" type="SiaSun.LMS.Model.GOODS_TEMPLATE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="GOODS_TEMPLATE">
			<result property="GOODS_TEMPLATE_ID" column="goods_template_id" />
			<result property="GOODS_TEMPLATE_CODE" column="goods_template_code" />
			<result property="GOODS_TEMPLATE_NAME" column="goods_template_name" />
      <result property="GOODS_ID" column="goods_id" />
			<result property="GOODS_TEMPLATE_REMARK" column="goods_template_remark" />
			<result property="GOODS_TEMPLATE_ORDER" column="goods_template_order" />
			<result property="GOODS_TEMPLATE_FLAG" column="goods_template_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="GOODS_TEMPLATE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  goods_template_id,
				  goods_template_code,
				  goods_template_name,
          goods_id,
				  goods_template_remark,
				  goods_template_order,
				  goods_template_flag
			From GOODS_TEMPLATE
		</select>
		
		<select id="GOODS_TEMPLATE_SELECT_BY_ID" parameterClass="int" extends = "GOODS_TEMPLATE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_template_id=#GOODS_TEMPLATE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>


    <select id="GOODS_TEMPLATE_SELECT_BY_TEMPLATE_CODE" parameterClass="int" extends = "GOODS_TEMPLATE_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_template_code=#GOODS_TEMPLATE_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="GOODS_TEMPLATE_SELECT_BY_GOODS_ID" parameterClass="int" extends = "GOODS_TEMPLATE_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_id=#GOODS_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    
				
		<insert id="GOODS_TEMPLATE_INSERT" parameterClass="GOODS_TEMPLATE">
      Insert Into GOODS_TEMPLATE (
      goods_template_id,
      goods_template_code,
      goods_template_name,
      goods_id,
      goods_template_remark,
      goods_template_order,
      goods_template_flag
      )Values(
      #GOODS_TEMPLATE_ID#,
      #GOODS_TEMPLATE_CODE#,
      #GOODS_TEMPLATE_NAME#,
      #GOODS_ID#,
      #GOODS_TEMPLATE_REMARK#,
      #GOODS_TEMPLATE_ORDER#,
      #GOODS_TEMPLATE_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="GOODS_TEMPLATE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="GOODS_TEMPLATE_UPDATE" parameterClass="GOODS_TEMPLATE">
      Update GOODS_TEMPLATE Set
      goods_template_id=#GOODS_TEMPLATE_ID#,
      goods_template_code=#GOODS_TEMPLATE_CODE#,
      goods_template_name=#GOODS_TEMPLATE_NAME#,
      goods_id=#GOODS_ID#,
      goods_template_remark=#GOODS_TEMPLATE_REMARK#,
      goods_template_order=#GOODS_TEMPLATE_ORDER#,
      goods_template_flag=#GOODS_TEMPLATE_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					goods_template_id=#GOODS_TEMPLATE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="GOODS_TEMPLATE_DELETE" parameterClass="int">
			Delete From GOODS_TEMPLATE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_template_id=#GOODS_TEMPLATE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>