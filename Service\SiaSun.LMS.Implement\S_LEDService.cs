﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.ServiceModel;
using System.Data;
using SiaSun.LMS.Model;
using SiaSun.LMS.Common;
using System.Collections;
using System.Xml;
using IBatisNet.Common.Logging;
using System.Reflection;
using log4net;
using System.IO;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true)]
    public class S_LEDService : S_BaseService, SiaSun.LMS.Interface.I_LEDService
    {
        private string strSQL = string.Empty;

        private int SHOW_STUNT = 4;     //显示特效，向左连移

        private int RUN_SPEED = 10;      //运行速度

        private int SHOW_TIME = 0;      //停留时间

        /// <summary>
        /// 根据LED_MAIN,生成LED_LIST，每一行基础高度为16，屏幕高度为16的整数倍
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool INIT_LED_LIST(out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(true);

                #region 清空LED_LIST

                strSQL = string.Format(@"DELETE FROM LED_LIST");

                this._P_Base_House.ExecuteNonQuery(strSQL, "dynamicSQL");

                #endregion

                #region 生成LED_LIST

                IList<LED_MAIN> lsLED_MAIN = this._P_LED_MAIN.GetList();

                bResult = lsLED_MAIN.Count != 0;

                if(!bResult)
                {
                    sResult = "请先添加LED_MAN信息";

                    return bResult;
                }

                foreach (LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    bResult = ((mLED_MAIN.SCREEN_HEIGHT / 16) >= mLED_MAIN.LINE_NUM) && ((mLED_MAIN.SCREEN_HEIGHT % mLED_MAIN.LINE_NUM) == 0);

                    if (!bResult)
                    {
                        sResult = string.Format("设备{0}对应的屏幕的行数设置错误", mLED_MAIN.DEVICE_CODE);

                        return bResult;
                    }

                    int LINE_NO = 0;            //行号，图文区号，每行改变

                    int AREA_X = 0;

                    int AREA_Y = 0;             //图文区横纵坐标，每行改变     

                    int AREA_WIDTH = mLED_MAIN.SCREEN_WIDTH;

                    int AREA_HEIGHT = mLED_MAIN.SCREEN_HEIGHT / mLED_MAIN.LINE_NUM;

                    int FONT_SIZE = 10 * (AREA_HEIGHT / 16);

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = new LED_LIST();

                        mLED_LIST.LED_ID = mLED_MAIN.LED_ID;

                        mLED_LIST.LINE_NO = LINE_NO;

                        mLED_LIST.AREA_X = AREA_X;

                        mLED_LIST.AREA_Y = AREA_Y;

                        mLED_LIST.AREA_WIDTH = AREA_WIDTH;

                        mLED_LIST.AREA_HEIGHT = AREA_HEIGHT;

                        mLED_LIST.FILE_NAME = string.Format("{0}.{1}.txt",mLED_MAIN.LED_IP,LINE_NO);

                        mLED_LIST.LINE_TEXT = string.Empty;

                        mLED_LIST.FONT_SIZE = FONT_SIZE;

                        //偶数行，行号从0开始,向左连移
                        if (LINE_NO % 2 != 0)
                        {
                            SHOW_STUNT = 4;

                            mLED_LIST.SHOW_STUNT = SHOW_STUNT;
                        }
                         //奇数行，静止
                        else
                        {
                            SHOW_STUNT = 1;

                            mLED_LIST.SHOW_STUNT = SHOW_STUNT;
                        }

                        mLED_LIST.RUN_SPEED = RUN_SPEED;

                        mLED_LIST.SHOW_TIME = SHOW_TIME;         

                        mLED_LIST.LED_LIST_REMARK = string.Empty;

                        mLED_LIST.LED_LIST_PARA1 = string.Empty;

                        mLED_LIST.LED_LIST_PARA2 = string.Empty;

                        mLED_LIST.LED_LIST_PARA3 = string.Empty;

                        mLED_LIST.LED_LIST_PARA4 = string.Empty;

                        mLED_LIST.LED_LIST_PARA5 = string.Empty;

                        this._P_LED_LIST.Add(mLED_LIST);

                        LINE_NO++;

                        AREA_Y += AREA_HEIGHT;
                    }
                }

                #endregion
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult += string.Format("INIT_LED_LIST catch Exception:{0}", ex.Message);
            }
            finally
            {
                if(bResult)
                {
                    this._P_Base_House.CommitTransaction(true);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(true);
                }
            }

            return bResult;
        }

        public SiaSun.LMS.Model.LED_MAIN LED_MAIN_GetModel(int LED_ID)
        {
            return _P_LED_MAIN.GetModel(LED_ID);
        }

        public SiaSun.LMS.Model.LED_LIST LED_LIST_GetModel(int LED_LIST_ID)
        {
            return _P_LED_LIST.GetModel(LED_LIST_ID);
        }

        public IList<SiaSun.LMS.Model.LED_MAIN> LED_MAIN_GetList_AUTO_FLAG_LED_STATUS(string AUTO_FLAG, string LED_STATUS)
        {
            return _P_LED_MAIN.GetList_AUTO_FLAG_LED_STATUS(AUTO_FLAG,LED_STATUS);
        }

        public IList<SiaSun.LMS.Model.LED_LIST> LED_LIST_GetList_LED_ID(int LED_ID)
        {
            return _P_LED_LIST.GetList(LED_ID);
        }

        public void LED_MAIN_Update(SiaSun.LMS.Model.LED_MAIN mLED_MAIN)
        {
            _P_LED_MAIN.Update(mLED_MAIN);
        }

        /// <summary>
        /// 服务端保存LED发送的内容-发送内容用|分隔
        /// </summary>
        /// <param name="DeviceCode">设备编码</param>
        /// <param name="LineCount">内容总行号</param>
        /// <param name="SendTxt">发送内容，每行用|分隔</param>
        public bool AddLedTxt(string DeviceCode, int LineCount, string SendTxt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                IList<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = this._P_LED_MAIN.GetList_DEVICE_CODE_AUTO_FLAG(DeviceCode,"1");

                if (lsLED_MAIN.Count == 0 || lsLED_MAIN == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到设备{0}所对应的大屏幕", DeviceCode);

                    return bResult;
                }

                string[] arrSendTxt = SendTxt.Split('|');

                if (arrSendTxt.Length != LineCount)
                {
                    bResult = false;

                    sResult = string.Format("发送到设备{0}的内容行数{1}与内容{2}格式不一致", DeviceCode, LineCount,SendTxt);

                    return bResult;
                }

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    //内容行数与大屏设置行数不一致时，重新生成LED_LIST
                    if (mLED_MAIN.LINE_NUM != LineCount)
                    {
                        mLED_MAIN.LINE_NUM=LineCount;

                        bResult = INIT_LED_LIST_LED_MAIN(mLED_MAIN,out sResult);

                        //保存大屏当前设置
                        if(bResult)
                        {
                            this._P_LED_MAIN.Update(mLED_MAIN);
                        }

                        else
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}重新生成LED_LIST失败", mLED_MAIN.DEVICE_CODE);

                            return bResult;
                        }
                    }

                    //逐行添加LED_LIST的内容
                    int LINE_NO = 0;

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = this._P_LED_LIST.GetModel(mLED_MAIN.LED_ID, LINE_NO);

                        if (mLED_LIST == null)
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", DeviceCode, LINE_NO);

                            return bResult;
                        }

                        //添加发送内容
                        mLED_LIST.LINE_TEXT = string.IsNullOrEmpty(arrSendTxt[LINE_NO]) ? mLED_LIST.LINE_TEXT : arrSendTxt[LINE_NO];

                        this._P_LED_LIST.Update(mLED_LIST);

                        LINE_NO++;
                    }

                    //更新发送标识,0-未发送
                    mLED_MAIN.LED_STATUS = "0";

                    this._P_LED_MAIN.Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedTxt catch Exception: {0}", ex));
            }

            return bResult;
        }


        /// <summary>
        /// 服务端保存LED 发送指定行的显示信息
        /// </summary>
        /// <param name="DeviceCode">设备编码</param>
        /// <param name="LineNum">行号</param>
        /// <param name="SendTxt">发送内容</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool AddLedLineTxt(string DeviceCode, int LineNum, string SendTxt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                IList<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = this._P_LED_MAIN.GetList_DEVICE_CODE_AUTO_FLAG(DeviceCode, "1");

                if (lsLED_MAIN.Count == 0 || lsLED_MAIN == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到设备{0}所对应的大屏幕", DeviceCode);

                    return bResult;
                }


                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    SiaSun.LMS.Model.LED_LIST mLED_LIST = this._P_LED_LIST.GetModel(mLED_MAIN.LED_ID, LineNum);

                    if (mLED_LIST == null)
                    {
                        bResult = false;

                        sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", DeviceCode, LineNum);

                        return bResult;
                    }

                    if (mLED_LIST.LINE_TEXT.Contains("空闲"))
                        mLED_LIST.LINE_TEXT = string.Empty;


                    //添加发送内容
                    if (mLED_LIST.LINE_TEXT.Contains(SendTxt)&&SendTxt!= string.Empty)
                        mLED_LIST.LINE_TEXT = mLED_LIST.LINE_TEXT.Replace(SendTxt, string.Empty);
                    else
                    {
                        if(SendTxt.Contains("托盘"))
                            mLED_LIST.LINE_TEXT = SendTxt +mLED_LIST.LINE_TEXT ;
                        else
                            mLED_LIST.LINE_TEXT = mLED_LIST.LINE_TEXT + SendTxt;

                    }

                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 0 && mLED_MAIN.DEVICE_CODE == "12010")
                        mLED_LIST.LINE_TEXT = "1#垛机空闲";
                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 1 && mLED_MAIN.DEVICE_CODE == "12010")
                        mLED_LIST.LINE_TEXT = "2#垛机空闲";
                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 0 && mLED_MAIN.DEVICE_CODE == "12011")
                        mLED_LIST.LINE_TEXT = "3#垛机空闲";
                    if (mLED_LIST.LINE_TEXT == string.Empty && mLED_LIST.LINE_NO == 1 && mLED_MAIN.DEVICE_CODE == "12011")
                        mLED_LIST.LINE_TEXT = "4#垛机空闲";

                    this._P_LED_LIST.Update(mLED_LIST);
                    //更新发送标识,0-未发送
                    mLED_MAIN.LED_STATUS = "0";

                    this._P_LED_MAIN.Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedTxt catch Exception: {0}", ex));
            }

            return bResult;
        }


        public bool ClearLedLineTxt(string DeviceCode, int LineNum, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                IList<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = this._P_LED_MAIN.GetList_DEVICE_CODE_AUTO_FLAG(DeviceCode, "1");

                if (lsLED_MAIN.Count == 0 || lsLED_MAIN == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到设备{0}所对应的大屏幕", DeviceCode);

                    return bResult;
                }


                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    SiaSun.LMS.Model.LED_LIST mLED_LIST = this._P_LED_LIST.GetModel(mLED_MAIN.LED_ID, LineNum);

                    if (mLED_LIST == null)
                    {
                        bResult = false;

                        sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", DeviceCode, LineNum);

                        return bResult;
                    }


                    if (mLED_LIST.LINE_TEXT.Contains("托盘"))
                    {
                        mLED_LIST.LINE_TEXT = string.Empty;

                        this._P_LED_LIST.Update(mLED_LIST);
                        //更新发送标识,0-未发送
                        mLED_MAIN.LED_STATUS = "0";

                        this._P_LED_MAIN.Update(mLED_MAIN);
                    }
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedTxt catch Exception: {0}", ex));
            }

            return bResult;
        }
        /// <summary>
        /// 根据LED_MAIN配置，生成对应的LED_LIST
        /// </summary>
        /// <param name="mLED_MAIN"></param>
        /// <param name="_Language"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool INIT_LED_LIST_LED_MAIN(SiaSun.LMS.Model.LED_MAIN mLED_MAIN,out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                #region 清空对应的LED_LIST

                strSQL = string.Format(@"DELETE FROM LED_LIST WHERE LED_ID='{0}'", mLED_MAIN.LED_ID);

                this._P_Base_House.ExecuteNonQuery(strSQL, "dynamicSQL");

                #endregion

                #region 根据配置生成LED_LIST

                if (mLED_MAIN!=null)
                {
                    bResult = ((mLED_MAIN.SCREEN_HEIGHT / 16) >= mLED_MAIN.LINE_NUM) && ((mLED_MAIN.SCREEN_HEIGHT % mLED_MAIN.LINE_NUM) == 0);

                    if (!bResult)
                    {
                        sResult = string.Format("设备{0}的显示屏行数不能设为{1}", mLED_MAIN.DEVICE_CODE, mLED_MAIN.LINE_NUM);

                        return bResult;
                    }

                    int LINE_NO = 0;            //行号，图文区号，每行改变

                    int AREA_X = 0;

                    int AREA_Y = 0;             //图文区横纵坐标，每行改变     

                    int AREA_WIDTH = mLED_MAIN.SCREEN_WIDTH;

                    int AREA_HEIGHT = mLED_MAIN.SCREEN_HEIGHT / mLED_MAIN.LINE_NUM;

                    int FONT_SIZE = 10 * (AREA_HEIGHT / 16);

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = new LED_LIST();

                        mLED_LIST.LED_ID = mLED_MAIN.LED_ID;

                        mLED_LIST.LINE_NO = LINE_NO;

                        mLED_LIST.AREA_X = AREA_X;

                        mLED_LIST.AREA_Y = AREA_Y;

                        mLED_LIST.AREA_WIDTH = AREA_WIDTH;

                        mLED_LIST.AREA_HEIGHT = AREA_HEIGHT;

                        mLED_LIST.FILE_NAME = string.Format("{0}.{1}.txt", mLED_MAIN.LED_IP, LINE_NO);

                        mLED_LIST.LINE_TEXT = string.Empty;

                        mLED_LIST.FONT_SIZE = FONT_SIZE;

                        //偶数行，行号从0开始,向左连移
                        if (LINE_NO % 2 != 0)
                        {
                            SHOW_STUNT = 4;

                            mLED_LIST.SHOW_STUNT = SHOW_STUNT;
                        }
                        //奇数行，静止
                        else
                        {
                            SHOW_STUNT = 1;

                            mLED_LIST.SHOW_STUNT = SHOW_STUNT;
                        }

                        mLED_LIST.RUN_SPEED = RUN_SPEED;

                        mLED_LIST.SHOW_TIME = SHOW_TIME;

                        mLED_LIST.LED_LIST_REMARK = string.Empty;

                        mLED_LIST.LED_LIST_PARA1 = string.Empty;

                        mLED_LIST.LED_LIST_PARA2 = string.Empty;

                        mLED_LIST.LED_LIST_PARA3 = string.Empty;

                        mLED_LIST.LED_LIST_PARA4 = string.Empty;

                        mLED_LIST.LED_LIST_PARA5 = string.Empty;

                        this._P_LED_LIST.Add(mLED_LIST);

                        LINE_NO++;

                        AREA_Y += AREA_HEIGHT;
                    }
                }

                #endregion
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult += string.Format("INIT_LED_LIST_LED_MAIN catch Exception:{0}", ex.Message);
            }

            return bResult;
        }

        /// <summary>
        /// 向LED发送固定内容-发送内容用|分隔
        /// </summary>
        /// <param name="mLED_MAIN">屏幕Model</param>
        /// <param name="LineCount">内容总行号</param>
        /// <param name="SendTxt">发送内容，每行用|分隔</param>
        public bool AddLedDefaltTxt(SiaSun.LMS.Model.LED_MAIN mLED_MAIN, int LineCount, string SendTxt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                if (mLED_MAIN!=null)
                {
                    string[] arrSendTxt = SendTxt.Split('|');

                    if (arrSendTxt.Length != LineCount)
                    {
                        bResult = false;

                        sResult = string.Format("发送到设备{0}的内容行数{1}与内容{2}格式不一致", mLED_MAIN.DEVICE_CODE, LineCount, SendTxt);

                        return bResult;
                    }

                    //内容行数与大屏设置行数不一致时，重新生成LED_LIST
                    if (mLED_MAIN.LINE_NUM != LineCount)
                    {
                        mLED_MAIN.LINE_NUM = LineCount;

                        bResult = INIT_LED_LIST_LED_MAIN(mLED_MAIN, out sResult);

                        //保存大屏当前设置
                        if (bResult)
                        {
                            this._P_LED_MAIN.Update(mLED_MAIN);
                        }

                        else
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}重新生成LED_LIST失败", mLED_MAIN.DEVICE_CODE);

                            return bResult;
                        }
                    }

                    //逐行添加LED_LIST的内容
                    int LINE_NO = 0;

                    while (mLED_MAIN.LINE_NUM != LINE_NO)
                    {
                        SiaSun.LMS.Model.LED_LIST mLED_LIST = this._P_LED_LIST.GetModel(mLED_MAIN.LED_ID, LINE_NO);

                        if (mLED_LIST == null)
                        {
                            bResult = false;

                            sResult = string.Format("设备{0}的显示屏不存在第{1}行，重新生成LED_LIST", mLED_MAIN.DEVICE_CODE, LINE_NO);

                            return bResult;
                        }

                        //添加发送内容,空值则保留原值
                        mLED_LIST.LINE_TEXT = string.IsNullOrEmpty(arrSendTxt[LINE_NO]) ? mLED_LIST.LINE_TEXT : arrSendTxt[LINE_NO];

                        this._P_LED_LIST.Update(mLED_LIST);

                        LINE_NO++;
                    }

                    //1已发送,0未发送
                    mLED_MAIN.LED_STATUS = "0";

                    this._P_LED_MAIN.Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                //this._log.Error(string.Format("AddLedDefaltTxt catch Exception: {0}", ex));
            }

            return bResult;
        }



        public bool ledMessageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN)
        {
            bool bResult = true;

            string sResult = string.Empty;

            string ledMes = string.Empty;

            try
            {
                SiaSun.LMS.Model.WH_CELL mSTART_WM_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mEND_WH_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

                if (mMANAGE_TYPE.MANAGE_TYPE_GROUP.TrimEnd() == "1")
                {
                    switch (mEND_WH_CELL.DEVICE_CODE.TrimEnd())
                    {
                        case "18001":
                            ledMes = string.Format("1#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 0, ledMes, out sResult);
                            break;
                        case "18002":
                            ledMes = string.Format("2#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 1, ledMes, out sResult);
                            break;

                        case "18003":
                            ledMes = string.Format("3#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 0, ledMes, out sResult);
                            break;
                        case "18004":
                            ledMes = string.Format("4#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 1, ledMes, out sResult);
                            break;
                    }
                }

                if (mMANAGE_TYPE.MANAGE_TYPE_GROUP.TrimEnd() == "2")
                {
                    switch (mSTART_WM_CELL.DEVICE_CODE.TrimEnd())
                    {
                        case "18001":
                            ledMes = string.Format("1#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 0, ledMes, out sResult);
                            break;
                        case "18002":
                            ledMes = string.Format("2#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12010", 1, ledMes, out sResult);
                            break;
                        case "18003":
                            ledMes = string.Format("3#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 0, ledMes, out sResult);
                            break;
                        case "18004":
                            ledMes = string.Format("4#垛机出库{0}", mSTART_WM_CELL.CELL_CODE);
                            this.AddLedLineTxt("12011", 1, ledMes, out sResult);
                            break;

                    }
                }
            }
            catch
            {
            }

            return bResult;


        }

        //public bool ledMessageClear(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN, out string sResult)
        //{
        //    bool bResult = true;

        //    sResult = string.Empty;

        //    string ledMes = string.Empty;

        //    try
        //    {
        //        SiaSun.LMS.Model.WH_CELL mSTART_WM_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

        //        SiaSun.LMS.Model.WH_CELL mEND_WH_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

        //        SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

        //        if (mMANAGE_TYPE.MANAGE_TYPE_GROUP.TrimEnd() == "1")
        //        {
        //            switch (mEND_WH_CELL.DEVICE_CODE.TrimEnd())
        //            {
        //                case "18001":
        //                    ledMes = string.Format("1#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12010", 0, string.Empty, out sResult);
        //                    break;
        //                case "18002":
        //                    ledMes = string.Format("2#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12010", 1, string.Empty, out sResult);
        //                    break;

        //                case "18003":
        //                    ledMes = string.Format("3#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12011", 0, string.Empty, out sResult);
        //                    break;
        //                case "18004":
        //                    ledMes = string.Format("4#垛机入库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12011", 1, string.Empty, out sResult);
        //                    break;
        //            }
        //        }

        //        if (mMANAGE_TYPE.MANAGE_TYPE_GROUP.TrimEnd() == "2")
        //        {
        //            switch (mSTART_WM_CELL.DEVICE_CODE.TrimEnd())
        //            {
        //                case "18001":
        //                    ledMes = string.Format("1#垛机出库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12010", 0, string.Empty, out sResult);
        //                    break;
        //                case "18002":
        //                    ledMes = string.Format("2#垛机出库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12010", 1, string.Empty, out sResult);
        //                    break;
        //                case "18003":
        //                    ledMes = string.Format("3#垛机出库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12011", 0, string.Empty, out sResult);
        //                    break;
        //                case "18004":
        //                    ledMes = string.Format("4#垛机出库{0}", mEND_WH_CELL.CELL_CODE);
        //                    this.AddLedLineTxt("12011", 1, string.Empty, out sResult);
        //                    break;

        //            }
        //        }
        //    }
        //    catch
        //    {
        //    }

        //    return bResult;


        //}
    }
}
