﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 取消Miniload拣选任务
    /// </summary>
    public class MiniloadSortTaskCancel : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            string _pickType = string.Empty;          //拣选类型

            string _taskNo = string.Empty;          //任务编码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源


            /// <summary>
            /// 拣选类型
            /// </summary>
            public string pickType
            {
                get { return _pickType; }
                set { _pickType = value; }
            }


            /// <summary>
            /// 任务编码
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }            
        }
   
        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();

            try
            {
                this._P_Base_House.BeginTransaction();

                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("MiniloadSortTaskCancel.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.taskNo))
                {
                    bResult = false;
                    outJson = string.Format("MiniloadSortTaskCancel.NotifyMethod:入参存在空值");
                    return bResult;
                }

                IList<Model.PLAN_MAIN> lsPLAN_MAIN = this._P_PLAN_MAIN.GetList_PLAN_RELATIVE_CODE(taskInfo.taskNo).Where(r=>r.PLAN_TYPE_CODE==Enum.PLAN_TYPE_CODE.PlanPick.ToString()).ToList();
                if (lsPLAN_MAIN == null || lsPLAN_MAIN.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("MiniloadSortTaskCancel.NotifyMethod:根据任务号未找到拣选计划_任务号[{0}]", taskInfo.taskNo);
                    return bResult;
                }

                foreach (Model.PLAN_MAIN itemPLAN_MAIN in lsPLAN_MAIN)
                {
                    if (itemPLAN_MAIN.PLAN_STATUS != Enum.PLAN_STATUS.Waiting.ToString())
                    {
                        bResult = false;
                        outJson = string.Format("MiniloadSortTaskCancel.NotifyMethod:当前任务号对应的拣选计划已执行_无法删除_任务号[{0}]", taskInfo.taskNo);
                        return bResult;
                    }

                    //string strTemp = string.Empty;
                    bResult = this.Invoke("PlanBase", "PlanCancel", new object[] { itemPLAN_MAIN.PLAN_ID,"No", true, false }, out outJson);
                    if (!bResult)
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", Enum.LOG_LEVEL.Error, string.Format("MiniloadSortTaskCancel.NotifyMethod():WMS删除拣选计划失败_未能取消计划_原因[{0}]_计划ID[{1}]_任务号[{2}]", outJson, itemPLAN_MAIN.PLAN_ID, taskInfo.taskNo));
                        return bResult;
                    }
                    else
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", Enum.LOG_LEVEL.Critical, string.Format("MiniloadSortTaskCancel.NotifyMethod():WMS删除拣选计划成功_计划ID[{0}]_任务号[{1}]", itemPLAN_MAIN.PLAN_ID, taskInfo.taskNo));
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("MiniloadSortTaskCancel.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();

                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();

                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
