﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// Miniload拣选任务下发
    /// </summary>
    public class MiniloadSortTask : InterfaceBase
    {
        string[] numberToAlphabet = { "A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M", "N", "O", "P", "Q", "R", "S", "T", "U", "V", "W", "X", "Y", "Z" };

        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            string _pickType = string.Empty;            //拣选方式
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源

            string _UseTime = string.Empty;       //用料时间
            string _UserArea = string.Empty;     //出库位置

            public List<FirstDetails> firstDetails { get; set; }//一级明细
            

            /// <summary>
            /// 拣选方式
            /// </summary>
            public string pickType
            {
                get { return _pickType; }
                set { _pickType = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 用料时间  2020-09-22 15:05:33
            /// </summary>
            public string UseTime
            {
                get { return _UseTime; }
                set { _UseTime = value; }
            }
            /// <summary>
            /// 出库位置  2020-09-22 15:05:44
            /// </summary>
            public string UserArea
            {
                get { return _UserArea; }
                set { _UserArea = value; }

            }

        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号
            private string _description = string.Empty;     //WBS描述
            private string _headText;     //抬头文本
            private string _priority;    //优先级
            private string _collectionNo;   //集货号
            private string _relatedBill2;     //派工单
            private string _shopNo;    //车间
            private string _demandDate;   //mes需求时间
            private string _demandTime;     //时

            //private string _workingSeatl;   //工位
            //private string _productNo;      //工序序号


            public List<SecondDetails> secondDetails { get; set; }//二级明细

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// WBS描述
            /// </summary>
            public string description
            {
                get { return _description; }
                set { _description = value; }
            }


            /// <summary>
            /// 抬头文本
            /// </summary>
            public string headText
            {
                get { return _headText; }
                set { _headText = value; }
            }

            /// <summary>
            /// 优先级
            /// </summary>
            public string priority
            {
                get { return _priority; }
                set { _priority = value; }
            }

            /// <summary>
            /// 集货号 2020-10-15 18:45:59
            /// </summary>
            public string collectionNo
            {
                get { return _collectionNo; }
                set { _collectionNo = value; }
            }

            /// <summary>
            /// 派工单
            /// </summary>
            public string relatedBill2
            {
                get { return _relatedBill2; }
                set { _relatedBill2 = value; }
            }

            /// <summary>
            /// 车间
            /// </summary>
            public string shopNo
            {
                get { return _shopNo; }
                set { _shopNo = value; }
            }
            
            /// <summary>
            /// mes需求时间
            /// </summary>
            public string demandDate
            {
                get { return _demandDate; }
                set { _demandDate = value; }
            }

            /// <summary>
            /// mes需求时间
            /// </summary>
            public string demandTime
            {
                get { return _demandTime; }
                set { _demandTime = value; }
            }

            ///// <summary>
            ///// 工位
            ///// </summary>
            //public string workingSeatl
            //{
            //    get { return _workingSeatl; }
            //    set { _workingSeatl = value; }
            //}

            ///// <summary>
            ///// 工序序号
            ///// </summary>
            //public string productNo
            //{
            //    get { return _productNo; }
            //    set { _productNo = value; }
            //}

        }
        /// <summary>
        /// 入参_二级明细
        /// </summary>
        public class SecondDetails
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty;     //数量

            private string _workingSeatl;   //工位
            private string _productNo;      //工序序号

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }

            /// <summary>
            /// 工位
            /// </summary>
            public string workingSeatl
            {
                get { return _workingSeatl; }
                set { _workingSeatl = value; }
            }

            /// <summary>
            /// 工序序号
            /// </summary>
            public string productNo
            {
                get { return _productNo; }
                set { _productNo = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            int iPlanId = 0;
            OutputPara outputPara = new OutputPara();

            try
            {
                this._P_Base_House.BeginTransaction();

                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("MiniloadSortTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.pickType) || string.IsNullOrEmpty(taskInfo.uniqueCode) || taskInfo.firstDetails.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("MiniloadSortTask.NotifyMethod:入参存在空值");
                    return bResult;
                }
               
                //2020-09-22 15:25:14 尝试转换"使用时间",如果失败则写空,后续就不会自动移库到三楼
                string useTimeString = string.Empty;
                DateTime useTime;
                if (DateTime.TryParseExact(taskInfo.UseTime, "yyyyMMddHHmmss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal, out useTime))
                {
                    useTimeString = useTime.ToString("yyyy-MM-dd HH:mm:ss");
                }

                if(string.IsNullOrEmpty(useTimeString))
                {
                    if (DateTime.TryParseExact(taskInfo.UseTime, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal, out useTime))
                    {
                        useTimeString = useTime.ToString("yyyy-MM-dd HH:mm:ss");
                    }
                }

                if (taskInfo.pickType != "01" && taskInfo.pickType != "02")
                {
                    bResult = false;
                    outJson = string.Format("MiniloadSortTask.NotifyMethod:任务类别pickType[{0}]字段不在预期范围内", taskInfo.pickType);
                    return bResult;
                }

                //类型01-常规；02-只有一个一级明细，WES将其折叠为5个一级明细下发
                if (taskInfo.pickType == "01" || this._S_SystemService.GetSysParameter("PickType02Status","") !="Enable")
                {
                    foreach (FirstDetails itemFirst in taskInfo.firstDetails)
                    {
                        if (string.IsNullOrEmpty(itemFirst.wbsNo) || string.IsNullOrEmpty(itemFirst.projectNo) || string.IsNullOrEmpty(itemFirst.taskNo) || itemFirst.secondDetails.Count == 0)
                        {
                            bResult = false;
                            outJson = string.Format("MiniloadSortTask.NotifyMethod:入参存在空值");
                            return bResult;
                        }

                        Model.PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN();
                        mPLAN_MAIN.PLAN_CODE = itemFirst.wbsNo;             //WBS号
                        mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                        mPLAN_MAIN.PLAN_CREATE_TIME = Common.StringUtil.GetDateTime();
                        mPLAN_MAIN.PLAN_DESCRIPTION = itemFirst.description;
                        mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                        mPLAN_MAIN.PLAN_GROUP = taskInfo.uniqueCode;
                        mPLAN_MAIN.PLAN_PICK_TYPE = "01";
                        mPLAN_MAIN.PLAN_PROJECT_CODE = itemFirst.projectNo;     //项目号
                        mPLAN_MAIN.PLAN_RELATIVE_CODE = itemFirst.taskNo;   //任务号
                        mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                        mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanPick.ToString();
                        //string planLevel = string.Empty;
                        //mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("MiniloadSortPlanLevel", out planLevel) ? planLevel : "0";
                        mPLAN_MAIN.PLAN_HEADTEXT = itemFirst.headText;
                        mPLAN_MAIN.PLAN_LEVEL = itemFirst.priority;

                        //2021-03-12 22:47:42
                        DateTime demandDate;
                        double demandTime = 0.0;
                        if (DateTime.TryParseExact(itemFirst.demandDate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal, out demandDate))
                        {
                            useTimeString = demandDate.ToString("yyyy-MM-dd HH:mm:ss");
                        }
                        else if (!string.IsNullOrEmpty(useTimeString) && double.TryParse(itemFirst.demandTime, out demandTime))
                        {
                            useTimeString = useTime.AddHours(demandTime).ToString("yyyy-MM-dd HH:mm:ss");
                        }

                        //2020-09-22 15:21:04
                        mPLAN_MAIN.PLAN_FROM_DEPT = useTimeString;      //时间
                        mPLAN_MAIN.PLAN_TO_DEPT = string.IsNullOrEmpty(taskInfo.UserArea) ? "0" : taskInfo.UserArea;   //3-三楼使用；0-其他
                        mPLAN_MAIN.PLAN_FROM_USER = "0";                //下架处理标记，0-未处理；1-成功；2-失败

                        //2020-10-15 18:49:57
                        mPLAN_MAIN.PLAN_TO_USER = itemFirst.collectionNo;  //集货号

                        //2020-12-23 08:38:09
                        mPLAN_MAIN.PLAN_RELATEDBILL = itemFirst.relatedBill2;   //派工单
                        //mPLAN_MAIN.PLAN_SHOPNO = itemFirst.shopNo;              //车间

                        //改造需求，车间为空则标为其他
                        //PLAN_SHOPNO SYS_ITEM未找到，则标记为其他
                        if (string.IsNullOrEmpty(itemFirst.shopNo))
                        {
                            mPLAN_MAIN.PLAN_SHOPNO = "其他";
                        }
                        else
                        {
                            //不为空则查询 SYS_ITEM_CODE
                            List<Model.SYS_ITEM_LIST> shopNos = this._P_SYS_ITEM_LIST.GetListItemCode("PLAN_SHOPNO").ToList();
                            if (shopNos.Count > 0 && shopNos.FindIndex(t => t.ITEM_LIST_CODE.Equals(taskInfo.firstDetails[0].shopNo)) > 0) {
                                Model.SYS_ITEM_LIST targetShopNo = shopNos.Find(t => t.ITEM_LIST_CODE.Equals(itemFirst.shopNo));

                                if (targetShopNo != null && !string.IsNullOrEmpty(targetShopNo.ITEM_LIST_REMARK))
                                {
                                    mPLAN_MAIN.PLAN_SHOPNO = itemFirst.shopNo;
                                }
                                else
                                {
                                    mPLAN_MAIN.PLAN_SHOPNO = "其他";
                                }
                            }
                            else
                            {
                                mPLAN_MAIN.PLAN_SHOPNO = "其他";
                            }
                        }
                        
                        //mPLAN_MAIN.PLAN_SHOPNO = string.IsNullOrEmpty(itemFirst.shopNo)?"其他": itemFirst.shopNo;              //车间

                        //2021-01-16 09:07:01
                        mPLAN_MAIN.BACKUP_FILED1 = itemFirst.demandDate;        //mes需求时间（仅打印标签使用）

                        List<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();

                        var secondDetailGroup = itemFirst.secondDetails.GroupBy(r => r.itemCode);

                        foreach (var itemSecond in secondDetailGroup)
                        {
                            Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(itemSecond.Key);
                            if (mGOODS_MAIN == null)
                            {
                                bResult = false;
                                outJson = string.Format("MiniloadSortTask.NotifyMethod:传入物料信息有误 传入物料编码_{0}", itemSecond.Key);
                                return bResult;
                            }
                            //double quantity = 0;
                            //if(!double.TryParse(itemSecond.quantity, out quantity))
                            //{
                            //    bResult = false;
                            //    outJson = string.Format("MiniloadSortTask.NotifyMethod:传入数量有误 传入值_{0}", itemSecond.quantity);
                            //    return bResult;
                            //}

                            Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                            mPLAN_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;
                            mPLAN_LIST.PLAN_LIST_QUANTITY = Convert.ToDecimal(itemSecond.Sum(r => double.Parse(r.quantity)));

                            //工位和工序序号 2021-08-31 23:47:23
                            mPLAN_LIST.GOODS_PROPERTY3 = string.Join("-", itemSecond.Select(t => t.workingSeatl).Distinct());
                            mPLAN_LIST.GOODS_PROPERTY4 = string.Join("-", itemSecond.Select(t => t.productNo).Distinct());

                            lsPLAN_LIST.Add(mPLAN_LIST);
                        }

                        object[] invokeOutParams = new object[] { };
                        bResult = this.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST, false, iPlanId, outJson }, out invokeOutParams);
                        if (!bResult)
                        {
                            outJson = invokeOutParams[4].ToString();
                            return bResult;
                        }
                    }
                }
                else
                {
                    if(taskInfo.firstDetails.Count !=1)
                    {
                        bResult = false;
                        outJson = string.Format("MiniloadSortTask.NotifyMethod:任务类别pickType[{0}]对应多个任务行", taskInfo.pickType);
                        return bResult;
                    }

                    List<Tuple<int, decimal>> goodsAndQuantity = new List<Tuple<int, decimal>>();
                    //var goodsAndQuantity = new Dictionary<int, decimal>();
                    var secondDetailGroup = taskInfo.firstDetails[0].secondDetails.GroupBy(r => r.itemCode);
                    foreach (var itemSecond in secondDetailGroup)
                    {
                        Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(itemSecond.Key);
                        if (mGOODS_MAIN == null)
                        {
                            bResult = false;
                            outJson = string.Format("MiniloadSortTask.NotifyMethod:传入物料信息有误 传入物料编码_{0}", itemSecond.Key);
                            return bResult;
                        }

                        goodsAndQuantity.Add(new Tuple<int, decimal>(mGOODS_MAIN.GOODS_ID, Convert.ToDecimal(itemSecond.Sum(r => double.Parse(r.quantity)))));
                    }

                    var handledGoodsCount = 0;
                    var totalGoodsCount = goodsAndQuantity.Count;
                    var planCount = totalGoodsCount > 5 ? 5 : totalGoodsCount;

                    for (int i = 0; i < planCount; i++)
                    {
                        var planListCount = (totalGoodsCount - handledGoodsCount) / (planCount - i);

                        Model.PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN();
                        mPLAN_MAIN.PLAN_CODE = $"{taskInfo.firstDetails[0].wbsNo}|{numberToAlphabet[i]}";             //WBS号
                        mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                        mPLAN_MAIN.PLAN_CREATE_TIME = Common.StringUtil.GetDateTime();
                        mPLAN_MAIN.PLAN_DESCRIPTION = taskInfo.firstDetails[0].description;
                        mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                        mPLAN_MAIN.PLAN_GROUP = taskInfo.uniqueCode;
                        mPLAN_MAIN.PLAN_PICK_TYPE = "02";
                        mPLAN_MAIN.PLAN_PROJECT_CODE = taskInfo.firstDetails[0].projectNo;     //项目号
                        mPLAN_MAIN.PLAN_RELATIVE_CODE = taskInfo.firstDetails[0].taskNo;   //任务号
                        mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                        mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanPick.ToString();
                        //string planLevel = string.Empty;
                        //mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("MiniloadSortPlanLevel", out planLevel) ? planLevel : "0";
                        mPLAN_MAIN.PLAN_HEADTEXT = taskInfo.firstDetails[0].headText;
                        mPLAN_MAIN.PLAN_LEVEL = taskInfo.firstDetails[0].priority;

                        //2021-03-12 22:47:42
                        DateTime demandDate;
                        double demandTime = 0.0;
                        if (DateTime.TryParseExact(taskInfo.firstDetails[0].demandDate, "yyyy-MM-dd HH:mm:ss", System.Globalization.CultureInfo.InvariantCulture, System.Globalization.DateTimeStyles.AdjustToUniversal, out demandDate))
                        {
                            useTimeString = demandDate.ToString("yyyy-MM-dd HH:mm:ss");
                        }
                        else if (!string.IsNullOrEmpty(useTimeString) && double.TryParse(taskInfo.firstDetails[0].demandTime, out demandTime))
                        {
                            useTimeString = useTime.AddHours(demandTime).ToString("yyyy-MM-dd HH:mm:ss");
                        }

                        //2020-09-22 15:21:04
                        mPLAN_MAIN.PLAN_FROM_DEPT = useTimeString;      //时间
                        mPLAN_MAIN.PLAN_TO_DEPT = string.IsNullOrEmpty(taskInfo.UserArea) ? "0" : taskInfo.UserArea;   //3-三楼使用；0-其他
                        mPLAN_MAIN.PLAN_FROM_USER = "0";                //下架处理标记，0-未处理；1-成功；2-失败

                        //2020-10-15 18:49:57
                        mPLAN_MAIN.PLAN_TO_USER = taskInfo.firstDetails[0].collectionNo;  //集货号

                        //2020-12-23 08:38:09
                        mPLAN_MAIN.PLAN_RELATEDBILL = taskInfo.firstDetails[0].relatedBill2;   //派工单
                        //mPLAN_MAIN.PLAN_SHOPNO = taskInfo.firstDetails[0].shopNo;              //车间

                        //改造需求，车间为空则标为其他

                        if (string.IsNullOrEmpty(taskInfo.firstDetails[0].shopNo))
                        {
                            mPLAN_MAIN.PLAN_SHOPNO = "其他";
                        }
                        else
                        {
                            //不为空则查询 SYS_ITEM_CODE
                            List<Model.SYS_ITEM_LIST> shopNos = this._P_SYS_ITEM_LIST.GetListItemCode("PLAN_SHOPNO").ToList();
                            if(shopNos.Count>0 && shopNos.FindIndex(t => t.ITEM_LIST_CODE.Equals(taskInfo.firstDetails[0].shopNo)) > 0)
                            {
                                Model.SYS_ITEM_LIST targetShopNo = shopNos.Find(t => t.ITEM_LIST_CODE.Equals(taskInfo.firstDetails[0].shopNo));

                                if (targetShopNo != null && !string.IsNullOrEmpty(targetShopNo.ITEM_LIST_REMARK))
                                {
                                    mPLAN_MAIN.PLAN_SHOPNO = taskInfo.firstDetails[0].shopNo;
                                }
                                else
                                {
                                    mPLAN_MAIN.PLAN_SHOPNO = "其他";
                                }
                            }
                            else
                            {
                                mPLAN_MAIN.PLAN_SHOPNO = "其他";
                            }
                        }

                        //mPLAN_MAIN.PLAN_SHOPNO = string.IsNullOrEmpty(taskInfo.firstDetails[0].shopNo)? "其他" :taskInfo.firstDetails[0].shopNo;              //车间


                        //2021-01-16 09:07:01
                        mPLAN_MAIN.BACKUP_FILED1 = taskInfo.firstDetails[0].demandDate;        //mes需求时间（仅打印标签使用）

                        List<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();

                        for (int j = 0; j < planListCount; j++)
                        {
                            Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                            mPLAN_LIST.GOODS_ID = goodsAndQuantity[handledGoodsCount + j].Item1;
                            mPLAN_LIST.PLAN_LIST_QUANTITY = goodsAndQuantity[handledGoodsCount + j].Item2;

                            //工位和工序序号 2021-08-31 23:49:28
                            mPLAN_LIST.GOODS_PROPERTY3 = string.Join("-", taskInfo.firstDetails[0].secondDetails.Where(t => t.itemCode == this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID).GOODS_CODE).Select(r => r.workingSeatl).Distinct());
                            mPLAN_LIST.GOODS_PROPERTY4 = string.Join("-", taskInfo.firstDetails[0].secondDetails.Where(t => t.itemCode == this._P_GOODS_MAIN.GetModel(mPLAN_LIST.GOODS_ID).GOODS_CODE).Select(r => r.productNo).Distinct());

                            lsPLAN_LIST.Add(mPLAN_LIST);
                        }

                        handledGoodsCount += planListCount;

                        object[] invokeOutParams = new object[] { };
                        bResult = this.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST, false, iPlanId, outJson }, out invokeOutParams);
                        if (!bResult)
                        {
                            outJson = invokeOutParams[4].ToString();
                            return bResult;
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("MiniloadSortTask.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();

                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();

                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
