<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="ToolTip">
        <Setter Property="Background" Value="#33000000" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="3,0,3,0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="ToolTip">
                    <Border x:Name="Root" Background="{TemplateBinding Background}" BorderBrush="#19000000" CornerRadius="3">
                        <Border Margin="-3,-3,3,3" BorderBrush="#FF767676" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3" Padding="4">
                            <Border.Resources>
                                <Storyboard x:Key="Visible State" />
                                <Storyboard x:Key="Normal State" />
                            </Border.Resources>
                            <Border.Background>
                                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                    <GradientStop Color="#FFFFFFFF" Offset="0.004" />
                                    <GradientStop Color="#FFCDDCF0" Offset="1" />
                                </LinearGradientBrush>
                            </Border.Background>
                            <ContentPresenter Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" Cursor="{TemplateBinding Cursor}" Margin="{TemplateBinding Padding}" />
                        </Border>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>