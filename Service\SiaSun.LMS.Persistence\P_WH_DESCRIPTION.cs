﻿/***************************************************************************
 * 
 *       功能：     货位规划持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// WH_DESCRIPTION
	/// </summary>
	public class P_WH_DESCRIPTION : P_Base_House
	{
		public P_WH_DESCRIPTION ()
		{
			//
			// TODO: 此处添加WH_DESCRIPTION的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<WH_DESCRIPTION> GetList()
		{
            return ExecuteQueryForList<WH_DESCRIPTION>("WH_DESCRIPTION_SELECT_ORDER_BY_ID", null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(WH_DESCRIPTION wh_description)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("WH_DESCRIPTION");
                wh_description.DESCRIPTION_ID = id;
            }

            return ExecuteInsert("WH_DESCRIPTION_INSERT",wh_description);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(WH_DESCRIPTION wh_description)
		{
			return ExecuteUpdate("WH_DESCRIPTION_UPDATE",wh_description);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public WH_DESCRIPTION GetModel(System.Int32 DESCRIPTION_ID)
		{
			return ExecuteQueryForObject<WH_DESCRIPTION>("WH_DESCRIPTION_SELECT_BY_ID",DESCRIPTION_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 DESCRIPTION_ID)
		{
			return ExecuteDelete("WH_DESCRIPTION_DELETE",DESCRIPTION_ID);
		}
		

	}
}
