﻿using System;
using System.Data;
using System.Collections.Generic;
using System.Reflection;

namespace SiaSun.LMS.Common
{
    /// <summary>
    /// 基础类
    /// </summary>
    public class DataSetOperate
    {
        /// <summary>
        /// 对DataSet进行处理，建立DataTable表间的关系，向DataTable中增加列
        /// </summary>
        public DataSetOperate()
        {
        }

        /// <summary>
        /// 复制数据集合结构
        /// </summary>
        public DataTable CopyDataTableColumns(DataTable tableSource)
        {
            DataTable tableCopy = new DataTable();
            foreach (DataColumn col in tableSource.Columns)
            {
                DataColumn colCopy = new DataColumn();
                colCopy.ColumnName = col.ColumnName;
                colCopy.DataType = col.DataType;
                colCopy.DefaultValue = col.DefaultValue;
                colCopy.AllowDBNull = col.AllowDBNull;
                colCopy.Unique = col.Unique;
                tableCopy.Columns.Add(colCopy);
            }
            return tableCopy;
        }

        #region     ----设置DataTable的约束

        /// <summary>
        /// 设置列主键
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="table">数据源表</param>
        public void SetKeys(System.Data.DataTable table, params string[] columnName)
        {
            List<DataColumn> listColumn = new List<DataColumn>();

            //查找匹配的列，添加到主键集合内
            foreach (string s in columnName)
            {
                if (table.Columns.Contains(s))
                {
                    listColumn.Add(table.Columns[s]);
                    if (listColumn.Count == columnName.Length)
                        break;
                }
            }
            //设置主键集合
            table.PrimaryKey = listColumn.ToArray();
        }

        /// <summary>
        /// 设置列是否是唯一
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="table">数据源表</param>
        /// <param name="isUnique">是否唯一</param>
        public void SetUnique(string columnName, System.Data.DataTable table, bool isUnique)
        {
            foreach (DataColumn col in table.Columns)
            {
                if (col.ColumnName == columnName)
                {
                    col.Unique = isUnique;
                    break;
                }
            }
        }

        /// <summary>
        /// 设置列是否允许空
        /// </summary>
        /// <param name="columnName">列名</param>
        /// <param name="table">数据源表</param>
        /// <param name="isNull">是否唯一</param>
        public void SetNull(string columnName, System.Data.DataTable table, bool isNull)
        {
            foreach (DataColumn col in table.Columns)
            {
                if (col.ColumnName == columnName)
                {
                    col.AllowDBNull = isNull;
                    break;
                }
            }
        }

        #endregion 

        #region     ------表间关系操作

        /// <summary>
        /// 建立DataTable表间的关系
        /// </summary>
        /// <param name="ds">需要建立关系的DataSet</param>
        /// <param name="primaryTableName">主键表名称</param>
        /// <param name="foreignTableName">外键表名称</param>
        /// <param name="primaryColumnName">主键列名称</param>
        /// <param name="foreignColumnName">外键列名称</param>
        public void newRelation(DataSet ds, string primaryTableName, string foreignTableName, string primaryColumnName, string foreignColumnName)
        {
            DataTable primaryTable = ds.Tables[primaryTableName];
            DataTable foreignTable = ds.Tables[foreignTableName];
            ds.Relations.Add(primaryTableName + foreignTableName, primaryTable.Columns[primaryColumnName], foreignTable.Columns[foreignColumnName]);
        }

        /// <summary>
        /// 先建立DataSet间关系，在向外建表DataTabe中增加列
        /// </summary>
        /// <param name="ds">需要建立关系的DataSet</param>
        /// /// <param name="primaryTableName">主键表名称</param>
        /// <param name="foreignTableName">外键表名称</param>
        /// <param name="primaryColumnName">主键列名称</param>
        /// <param name="foreignColumnName">外键列名称</param>
        /// <param name="addColumnName">主键表中列名称</param>
        /// <param name="newColumnName">外键表中新增列名称</param>
        public void addColumnWithRelation(DataSet ds, string primaryTableName, string foreignTableName, string primaryColumnName, string foreignColumnName, string addColumnName, string newColumnName)
        {
            DataTable primaryTable = ds.Tables[primaryTableName];
            DataTable foreignTable = ds.Tables[foreignTableName];
            ds.Relations.Add(primaryTableName + foreignTableName, primaryTable.Columns[primaryColumnName], foreignTable.Columns[foreignColumnName]);
            DataColumn newColumn = new DataColumn(newColumnName, ds.Tables[primaryTableName].Columns[addColumnName].DataType);
            ds.Tables[foreignTableName].Columns.Add(newColumn);
            int rowCount = ds.Tables[foreignTableName].Rows.Count;
            for (int i = 0; i < rowCount; i++)
            {
                DataRow parentCustomerDR = foreignTable.Rows[i].GetParentRow(primaryTableName + foreignTableName);
                foreignTable.Rows[i][newColumnName] = parentCustomerDR[addColumnName];
            }
        }

        /// </summary>
        /// <param name="ds">需要建立关系的DataSet</param>
        /// <param name="primaryTableName">主键表名称</param>
        /// <param name="foreignTableName">外键表名称</param>
        /// <param name="addColumnName">主键表中列名称</param>
        /// <param name="newColumnName">外键表中新增列名称</param>
        public void addColumn(DataSet ds, string primaryTableName, string foreignTableName, string addColumnName, string newColumnName)
        {
            DataTable primaryTable = ds.Tables[primaryTableName];
            DataTable foreignTable = ds.Tables[foreignTableName];
            DataColumn newColumn = new DataColumn(newColumnName, ds.Tables[primaryTableName].Columns[addColumnName].DataType);
            ds.Tables[foreignTableName].Columns.Add(newColumn);
            int rowCount = ds.Tables[foreignTableName].Rows.Count;
            for (int i = 0; i < rowCount; i++)
            {
                DataRow parentCustomerDR = foreignTable.Rows[i].GetParentRow(primaryTableName + foreignTableName);
                foreignTable.Rows[i][newColumnName] = parentCustomerDR[addColumnName];
            }
        }

        #endregion
    }
}