﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     siasun
 *       日期：     2013/5/21
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    /// <summary>
    /// MANAGE_TYPE_PARAM 
    /// </summary>
    [Serializable]
    [DataContract]
    public class MANAGE_TYPE_PARAM
    {
        public MANAGE_TYPE_PARAM()
        {

        }
        private string strStartCellType;

        [DataMember]
        public string U_StartCellType
        {
            get { return strStartCellType; }
            set { strStartCellType = value; }
        }


        private string strStartCellInOut;
        [DataMember]
        public string U_StartCellInOut
        {
            get { return strStartCellInOut; }
            set { strStartCellInOut = value; }
        }


        private string strAssembleResource;
        [DataMember]
        public string U_AssembleResource
        {
            get { return strAssembleResource; }
            set { strAssembleResource = value; }
        }


        private bool bCheckStockExistStorage;
        [DataMember]
        public bool U_CheckStockExistStorage
        {
            get { return bCheckStockExistStorage; }
            set { bCheckStockExistStorage = value; }
        }


        private bool bAutoDownloadControlTask;
        [DataMember]
        public bool U_AutoDownloadControlTask
        {
            get { return bAutoDownloadControlTask; }
            set { bAutoDownloadControlTask = value; }
        }


        private string strEndCellType;
        [DataMember]
        public string U_EndCellType
        {
            get { return strEndCellType; }
            set { strEndCellType = value; }
        }

        private string strEndCellInOut;
        [DataMember]
        public string U_EndCellInOut
        {
            get { return strEndCellInOut; }
            set { strEndCellInOut = value; }
        }


        private bool bAllowShowStockBarcode;
        [DataMember]
        public bool U_AllowShowStockBarcode
        {
            get { return bAllowShowStockBarcode; }
            set { bAllowShowStockBarcode = value; }
        }


        private bool bAllowShowCellModel;
        [DataMember]
        public bool U_AllowShowCellModel
        {
            get { return bAllowShowCellModel; }
            set { bAllowShowCellModel = value; }
        }


        private bool bAllowShowStartPosition;
        [DataMember]
        public bool U_AllowShowStartPosition
        {
            get { return bAllowShowStartPosition; }
            set { bAllowShowStartPosition = value; }
        }

        private bool bAllowShowEndPosition;
        [DataMember]
        public bool U_AllowShowEndPosition
        {
            get { return bAllowShowEndPosition; }
            set { bAllowShowEndPosition = value; }
        }


        private bool bAutoCompleteTask;
        [DataMember]
        public bool U_AutoCompleteTask
        {
            get { return bAutoCompleteTask; }
            set { bAutoCompleteTask = value; }
        }


        private bool bAllowAutoStartPostion;
        [DataMember]
        public bool U_AllowAutoStartPostion
        {
            get { return bAllowAutoStartPostion; }
            set { bAllowAutoStartPostion = value; }
        }


        private bool bAllowAutoEndPostion;
        [DataMember]
        public bool U_AllowAutoEndPostion
        {
            get { return bAllowAutoEndPostion; }
            set { bAllowAutoEndPostion = value; }
        }

        private string strStartPositionDefault;
        [DataMember]
        public string U_StartPositionDefault
        {
            get { return strStartPositionDefault; }
            set { strStartPositionDefault = value; }
        }

        private string strEndPositionDefault;
        [DataMember]
        public string U_EndPositionDefault
        {
            get { return strEndPositionDefault; }
            set { strEndPositionDefault = value; }
        }

        private string strCellModelDefault;

        [DataMember]
        public string U_CellModelDefault
        {
            get { return strCellModelDefault; }
            set { strCellModelDefault = value; }
        }
        private string strStartPositionCellStatus;

        [DataMember]
        public string U_StartPositionCellStatus
        {
            get { return strStartPositionCellStatus; }
            set { strStartPositionCellStatus = value; }
        }
        private string strEndPositionCellStatus;

        [DataMember]
        public string U_EndPositionCellStatus
        {
            get { return strEndPositionCellStatus; }
            set { strEndPositionCellStatus = value; }
        }

        private bool bAllowShowOccupyPercent;
        [DataMember]
        public bool U_AllowShowOccupyPercent
        {
            get { return bAllowShowOccupyPercent; }
            set { bAllowShowOccupyPercent = value; }
        }


        private string strWarehouseType;
        [DataMember]
        public string U_WarehouseType
        {
            get { return strWarehouseType; }
            set { strWarehouseType = value; }
        }


    }
}
