﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.ServiceModel;
using System.Data;

using SiaSun.LMS.Model;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_FlowService
    {

        [OperationContract]
        IList<SiaSun.LMS.Model.FLOW_PARA> FlowGetParameters(string FLOW_TYPE_CODE);

        [OperationContract]
        DataTable PlanGetAction(string PLAN_ID);

        [OperationContract(Name="PlanGetAction1")]
        DataTable PlanGetAction(string PLAN_ID, string FLOW_ACTION_DEFAULT);

        [OperationContract]
        DataTable ManageGetAction(string MANAGE_ID);

        [OperationContract(Name="ManageGetAction1")]
        DataTable ManageGetAction(string MANAGE_ID, string ACTION_CODE);

        [OperationContract]
        DataTable ControlGetAction(string MANAGE_ID);

        [OperationContract]
        bool PlanEventExecute(SiaSun.LMS.Model.PLAN_ACTION_EXCUTE mPLAN_ACTION_EXCUTE, out string sResult);

        [OperationContract]
        bool ManageEventExecute(SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE mt, out string sResult);

    }
}
