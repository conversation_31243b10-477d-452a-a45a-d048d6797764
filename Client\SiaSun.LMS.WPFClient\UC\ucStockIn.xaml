﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucStockIn"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon" d:DesignHeight="250" d:DesignWidth="300">
    <Border BorderThickness="3">
        <Grid >
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>               
            </Grid.RowDefinitions>
            
            
            <WrapPanel  Grid.Row="0" Keyboard.KeyDown="WrapPanel_KeyDown"  Margin="0,0,0,0" >
                <StackPanel Name="panelSingleStockType" Orientation="Horizontal" Margin="5,2,5,2"  Visibility="Collapsed">
                    <TextBlock MinWidth="70" VerticalAlignment="Center">托盘类型:</TextBlock>
                    <ComboBox Name="cmbStockType" Margin="2,0,2,0" MinWidth="120" MinHeight="23"></ComboBox>
                </StackPanel>

                <StackPanel Name="panelStockInType" Orientation="Horizontal" Margin="5,2,5,2">                 
                    <TextBlock MinWidth="70" VerticalAlignment="Center">入库方式:</TextBlock>
                    <WrapPanel>
                    <RadioButton Name="rbSingle" Margin="2,0,2,0"  Content="单箱" Checked="rbSingle_Checked"></RadioButton>
                    <RadioButton Name="rbSingleContinue" Margin="2,0,2,0"  Content="连续箱" Checked="rbSingle_Checked"  ></RadioButton>
                    <RadioButton Name="rbMultiple" Margin="2,0,2,0" Content="托盘组" Checked="rbSingle_Checked" Visibility="Collapsed"></RadioButton>
                    </WrapPanel>
                </StackPanel>

                <StackPanel Name="panelBeingStockBarCode" Orientation="Horizontal" Margin="5,2,5,2">
                    <TextBlock Name="tbkStockBarcode" MinWidth="70" VerticalAlignment="Center">箱条码:</TextBlock>
                    <TextBox Name="txtBeginStockBarCode" Margin="2,0,2,0" MinWidth="120" MinHeight="23" ></TextBox>
                </StackPanel>
                <StackPanel Name="panelEndStockBarCode" Orientation="Horizontal" Margin="5,2,5,2">
                    <TextBlock MinWidth="70" VerticalAlignment="Center">终止条码:</TextBlock>
                    <TextBox Name="txtEndStockBarCode" Margin="2,0,2,0" MinWidth="120" MinHeight="23" ></TextBox>
                </StackPanel>
                
            </WrapPanel>
            <Grid Grid.Row="1">
                <ListView   Name="lvStockBarcode" Margin="80,0,100,0" MinHeight="100"  ></ListView>
            </Grid>
            
    </Grid>
    </Border>
</UserControl>
