﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="WH_CELL_GROUP" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="WH_CELL_GROUP" type="SiaSun.LMS.Model.WH_CELL_GROUP, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="WH_CELL_GROUP">
			<result property="GROUP_ID" column="group_id" />
			<result property="PICK_NO" column="pick_no" />
			<result property="AVAIL_LOC_NUM" column="avail_loc_num" />
			<result property="GROUP_STATUS" column="group_status" />
			<result property="GROUP_CODE" column="group_code" />
			<result property="GROUP_TYPE" column="group_type" />
		</resultMap>
	</resultMaps>

	<statements>

		<select id="WH_CELL_GROUP_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			group_id,
			pick_no,
			avail_loc_num,
			group_status,
			group_code,
			group_type
			From WH_CELL_GROUP
		</select>

		<select id="WH_CELL_GROUP_SELECT_BY_ID" parameterClass="int" extends = "WH_CELL_GROUP_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					group_id=#GROUP_ID#
				</isParameterPresent>
			</dynamic>
		</select>
		<select id="WH_CELL_GROUP_SELECT_BY_GROUP_CODE" parameterClass="string" extends = "WH_CELL_GROUP_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					group_code=#GROUP_CODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="WH_CELL_GROUP_SELECT_BY_PICK_NO" parameterClass="string" extends = "WH_CELL_GROUP_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					pick_no=#PICK_NO# AND avail_loc_num > 0
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="WH_CELL_GROUP_SELECT_BY_AVAIL_LOC_NUM" parameterClass="string" extends = "WH_CELL_GROUP_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					avail_loc_num = 4 and group_type = 'Open'
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="WH_CELL_GROUP_INSERT" parameterClass="WH_CELL_GROUP">
			Insert Into WH_CELL_GROUP (
			group_id,
			pick_no,
			avail_loc_num,
			group_status,
			group_code,
			group_type
			)Values(
			#GROUP_ID#,
			#PICK_NO#,
			#AVAIL_LOC_NUM#,
			#GROUP_STATUS#,
			#GROUP_CODE#,
			#GROUP_TYPE#
			)
			<!--<selectKey  resultClass="int" type="post" property="GROUP_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>

		<update id="WH_CELL_GROUP_UPDATE" parameterClass="WH_CELL_GROUP">
			Update WH_CELL_GROUP Set
			group_id=#GROUP_ID#,
			pick_no=#PICK_NO#,
			avail_loc_num=#AVAIL_LOC_NUM#,
			group_status=#GROUP_STATUS#,
			group_code=#GROUP_CODE#,
			group_type=#GROUP_TYPE#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					group_id=#GROUP_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="WH_CELL_GROUP_DELETE" parameterClass="int">
			Delete From WH_CELL_GROUP
			<dynamic prepend="WHERE">
				<isParameterPresent>
					group_id=#GROUP_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

	</statements>
</sqlMap>