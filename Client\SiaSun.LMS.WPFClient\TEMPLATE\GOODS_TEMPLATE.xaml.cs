﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.TEMPLATE
{

    public partial class GOODS_TEMPLATE : AvalonDock.DocumentContent
    {

        int intTemplateID = 0;

        public GOODS_TEMPLATE()
        {
            InitializeComponent();

            this.ucQueryTemplate.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQueryTemplate_U_Query);

            this.gridTemplate.gridApp.SelectionChanged += new SelectionChangedEventHandler(gridApp_SelectionChanged);
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.TEMPLATE_Query_Bind();

            this.TEMPLATE_Bind();

            this.TEMPLATE_LIST_Bind(0);
        }

        private void TEMPLATE_Query_Bind()
        {
            try
            {
                this.ucQueryTemplate.U_XmlTableName = "V_GOODS_TEMPLATE";

                this.ucQueryTemplate.U_WindowName = this.GetType().Name;

                this.ucQueryTemplate.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void ucQueryTemplate_U_Query(string QueryWhere)
        {
            try
            {
                this.gridTemplate.U_AppendWhere = QueryWhere;

                this.gridTemplate.U_InitControl();
                
                this.SelectTemplateDefault();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void TEMPLATE_Bind()
        {
            try
            {
                this.gridTemplate.U_WindowName = this.GetType().Name;
                this.gridTemplate.U_TableName = "V_GOODS_TEMPLATE";
                this.gridTemplate.U_OrderField = "GOODS_TEMPLATE_ID";
                //this.gridTemplate.U_Where = strExceptPlanStatus.Length > 0 ? "PLAN_STATUS NOT IN('" + strExceptPlanStatus.Replace("|", "','") + "')" : string.Empty;
                this.gridTemplate.U_AllowChecked = false;
                this.gridTemplate.U_AllowOperatData = false;
                this.gridTemplate.U_AllowPage = false;

                //初始化控件
                this.gridTemplate.U_InitControl();

                this.SelectTemplateDefault();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void SelectTemplateDefault()
        {
            if (!this.gridTemplate.gridApp.HasItems)
            {          
                this.TEMPLATE_LIST_Bind(0);
            }
        }

        void gridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.gridTemplate.gridApp.SelectedItem == null)
            {
                this.gridTemplateList.U_DataSource = null;
                return;
            }

            DataRowView drvPlan = this.gridTemplate.gridApp.SelectedItem as DataRowView;

             this.intTemplateID = drvPlan == null ? 0 : Convert.ToInt32(drvPlan["GOODS_TEMPLATE_ID"].ToString());

             this.TEMPLATE_LIST_Bind(intTemplateID);

        }

        private void TEMPLATE_LIST_Bind(int PLAN_ID)
        {
            this.gridTemplateList.U_Clear();
            this.gridTemplateList.U_WindowName = this.GetType().Name;
            this.gridTemplateList.U_TableName = "V_GOODS_TEMPLATE_LIST";
            this.gridTemplateList.U_XmlTableName = "V_GOODS_TEMPLATE_LIST";
            this.gridTemplateList.U_Fields = "*";
            this.gridTemplateList.U_Where = string.Format("GOODS_TEMPLATE_ID ={0}", PLAN_ID);
            this.gridTemplateList.U_OrderField = "GOODS_TEMPLATE_LIST_ID";

            this.gridTemplateList.U_AllowChecked = false;
            this.gridTemplateList.U_AllowOperatData = false;
            this.gridTemplateList.U_AllowPage =false;
            

            try
            {
                //初始化控件
                this.gridTemplateList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void tbarButton_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.Source as Button;

            if (btn == null) return;

            string sResult = string.Empty;

            try
            {
                switch (btn.Name)
                {
                    case "btnAdd":
                        MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.TEMPLATE.GOODS_TEMPLATE_EDIT", "新建方案", new object[] { }, out sResult);
                        break;
                    case "btnEdit":

                        if(this.intTemplateID != 0)
                            MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.TEMPLATE.GOODS_TEMPLATE_EDIT", "编辑方案", new object[] { intTemplateID }, out sResult);

                        break;
                    case "btnSave":
                        break;
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void TemplateAdd()
        {

        }

        private void TemplateEdit()
        {

        }


        private void TemplateSave()
        {

        }

    }
}
