﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// TECHNICS_ROUTE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class TECHNICS_ROUTE
	{
		public TECHNICS_ROUTE()
		{
			
		}
		
		private int _route_id;
		private int _technics_id;
		private int _exceptional_route_id;
		private string _route_description;
		private int _route_order;
		private string _task_type;
		private int _start_area_id;
		private int _end_area_id;
		private int _start_position;
		private int _end_position;
		private int _start_node_flag;
		private int _end_node_flag;
		private string _device_type;
		private int _task_level;
		private int _check_start_position_flag;
		private int _check_end_position_flag;
		private string _start_status;
		private string _finish_status;
		private string _ep_start_cell_status;
		private string _ep_start_run_status;
		private string _sp_end_cell_status;
		private string _sp_end_run_status;
		private string _ep_end_cell_status;
		private string _ep_end_run_status;
		private string _route_actions;
		private string _route_remark;
		
		///<sumary>
		/// 路线编号
        ///</sumary>
        [DataMember]
		public int ROUTE_ID
		{
			get{return _route_id;}
			set{_route_id = value;}
		}
		///<sumary>
		/// 流程编号
        ///</sumary>
        [DataMember]
		public int TECHNICS_ID
		{
			get{return _technics_id;}
			set{_technics_id = value;}
		}
		///<sumary>
		/// 异常流线编号
        ///</sumary>
        [DataMember]
		public int EXCEPTIONAL_ROUTE_ID
		{
			get{return _exceptional_route_id;}
			set{_exceptional_route_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string ROUTE_DESCRIPTION
		{
			get{return _route_description;}
			set{_route_description = value;}
		}
		///<sumary>
		/// 路线序号
        ///</sumary>
        [DataMember]
		public int ROUTE_ORDER
		{
			get{return _route_order;}
			set{_route_order = value;}
		}
		///<sumary>
		/// 任务类型
        ///</sumary>
        [DataMember]
		public string TASK_TYPE
		{
			get{return _task_type;}
			set{_task_type = value;}
		}
		///<sumary>
		/// 起始库区
        ///</sumary>
        [DataMember]
		public int START_AREA_ID
		{
			get{return _start_area_id;}
			set{_start_area_id = value;}
		}
		///<sumary>
		/// 终止库区
        ///</sumary>
        [DataMember]
		public int END_AREA_ID
		{
			get{return _end_area_id;}
			set{_end_area_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int START_POSITION
		{
			get{return _start_position;}
			set{_start_position = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int END_POSITION
		{
			get{return _end_position;}
			set{_end_position = value;}
		}
		///<sumary>
		/// 开始标识
        ///</sumary>
        [DataMember]
		public int START_NODE_FLAG
		{
			get{return _start_node_flag;}
			set{_start_node_flag = value;}
		}
		///<sumary>
		/// 结束标识
        ///</sumary>
        [DataMember]
		public int END_NODE_FLAG
		{
			get{return _end_node_flag;}
			set{_end_node_flag = value;}
		}
		///<sumary>
		/// 设备类型
        ///</sumary>
        [DataMember]
		public string DEVICE_TYPE
		{
			get{return _device_type;}
			set{_device_type = value;}
		}
		///<sumary>
		/// 任务优先级
        ///</sumary>
        [DataMember]
		public int TASK_LEVEL
		{
			get{return _task_level;}
			set{_task_level = value;}
		}
		///<sumary>
		/// 检查起始位置
        ///</sumary>
        [DataMember]
		public int CHECK_START_POSITION_FLAG
		{
			get{return _check_start_position_flag;}
			set{_check_start_position_flag = value;}
		}
		///<sumary>
		/// 检查结束位置
        ///</sumary>
        [DataMember]
		public int CHECK_END_POSITION_FLAG
		{
			get{return _check_end_position_flag;}
			set{_check_end_position_flag = value;}
		}
		///<sumary>
		/// 任务起始状态
        ///</sumary>
        [DataMember]
		public string START_STATUS
		{
			get{return _start_status;}
			set{_start_status = value;}
		}
		///<sumary>
		/// 任务完成状态
        ///</sumary>
        [DataMember]
		public string FINISH_STATUS
		{
			get{return _finish_status;}
			set{_finish_status = value;}
		}
		///<sumary>
		/// 结束位置开始时存储状态
        ///</sumary>
        [DataMember]
		public string EP_START_CELL_STATUS
		{
			get{return _ep_start_cell_status;}
			set{_ep_start_cell_status = value;}
		}
		///<sumary>
		/// 结束位置开始时运行状态
        ///</sumary>
        [DataMember]
		public string EP_START_RUN_STATUS
		{
			get{return _ep_start_run_status;}
			set{_ep_start_run_status = value;}
		}
		///<sumary>
		/// 起始位置完成时存储状态
        ///</sumary>
        [DataMember]
		public string SP_END_CELL_STATUS
		{
			get{return _sp_end_cell_status;}
			set{_sp_end_cell_status = value;}
		}
		///<sumary>
		/// 起始位置完成时运行状态
        ///</sumary>
        [DataMember]
		public string SP_END_RUN_STATUS
		{
			get{return _sp_end_run_status;}
			set{_sp_end_run_status = value;}
		}
		///<sumary>
		/// 结束位置完成时存储状态
        ///</sumary>
        [DataMember]
		public string EP_END_CELL_STATUS
		{
			get{return _ep_end_cell_status;}
			set{_ep_end_cell_status = value;}
		}
		///<sumary>
		/// 结束位置完成时运行状态
        ///</sumary>
        [DataMember]
		public string EP_END_RUN_STATUS
		{
			get{return _ep_end_run_status;}
			set{_ep_end_run_status = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string ROUTE_ACTIONS
		{
			get{return _route_actions;}
			set{_route_actions = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string ROUTE_REMARK
		{
			get{return _route_remark;}
			set{_route_remark = value;}
		}
	}
}
