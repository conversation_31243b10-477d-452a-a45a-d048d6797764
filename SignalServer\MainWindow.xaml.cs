﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;


using Microsoft.Owin.Hosting;
using SignalServer.Models;
using Microsoft.AspNet.SignalR;
using System.ComponentModel;
using System.Collections.ObjectModel;
using System.Configuration;
using System.IO;
using System.Runtime.CompilerServices;

namespace SignalServer
{
    /// <summary>
    /// MainWindow.xaml 的交互逻辑
    /// </summary>
    public partial class MainWindow : Window
    {

        public string url = ConfigurationManager.AppSettings["SignalRServerUrl"];
        public bool bAllowClose = false;

        public IDisposable server;
        BindModel bindModel = new BindModel();

        public MainWindow()
        {
            InitializeComponent();

            this.DataContext = bindModel;

            App.host.Open();
        }


        private void OpenSever_Click(object sender, RoutedEventArgs e)
        {
            try
            {

                server = WebApp.Start<Startup>(this.url);
                this.bindModel.IsServerAlive = true;
                //GlobalHost.ConnectionManager.GetHubContext<MessageHub>().Clients;
                //GlobalHost.ConnectionManager.GetHubContext<MessageHub>();

                this.WriteLog("开启服务");

            }
            catch (Exception ex)
            {
                this.WriteLog("开启服务异常: " + ex.Message);
            }
        }

        private void Close_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (server != null)
                {
                    server.Dispose();
                    this.bindModel.IsServerAlive = false;
                    this.WriteLog("停止服务");
                }
            }
            catch (Exception ex)
            {
                this.WriteLog("关闭服务异常：" + ex.Message);

            }
        }

        private void Boardcast_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                IHubContext context = GlobalHost.ConnectionManager.GetHubContext<WorkHub>();
                string targetPKCode = this.TargetPKCodeTextBox.Text.Trim();

                if (string.IsNullOrEmpty(targetPKCode))
                {
                    //广播
                    context.Clients.All.BroadcastTextMessage("admin", "msg to all..");
                }
                else
                {
                    context.Clients.Client(WorkHub.MessageClients.SingleOrDefault(m => m.Value.PickStationCode == targetPKCode).Value.ConnectID)?.BroadcastTextMessage("Singal", "msg to person");
                }
            }
            catch (Exception ex)
            {
                this.WriteLog("手动信息发送出现故障," + ex.Message);
            }
        }

        private void ColorZone_MouseDown(object sender, MouseButtonEventArgs e)
        {
            DragMove();
        }


        private void Sample1_DialogHost_OnDialogClosing(object sender, MaterialDesignThemes.Wpf.DialogClosingEventArgs eventArgs)
        {
            var arg = eventArgs;

            if (((bool)arg.Parameter).ToString() == "True")
            {
                //确定
                if (this.CloseWindowPasswordTextBox.Text.Trim() == "Pa$Sw0rd!`/*^")
                {
                    if (server != null)
                    {
                        server.Dispose();
                    }

                    this.bAllowClose = true;
                    this.Close();
                }
                else
                {
                    //this.CloseWindowPasswordTextBox.Text = string.Format("111");
                }
            }
            else
            {
                //取消
                this.CloseWindowPasswordTextBox.Text = string.Empty;
            }
        }


        public void WriteLog(string msg, [CallerMemberName]string methodName = "")
        {
            Dispatcher.BeginInvoke(new Action(() =>
            {
                this.InfoToShow.AppendText(string.Format("{0}:{1}, \r",
                    System.DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), msg));
            }));


            Log.WriteInfo(msg, methodName);
        }

        

        public Client UpdatePickStationUserInfo(
            string pickStationCode, int userId,
            string userCode,
            string userName,
            string userRole,
            string status,
            bool isLogin)
        {
            Client client = null;
            IEnumerable<Client> result = this.bindModel.Clients.Where(c => c.PickStationCode == pickStationCode);
            if (result.Count() > 0)
            {
                int index = this.bindModel.Clients.IndexOf(result.First());


                this.bindModel.Clients[index].LoginUserID = userId;
                this.bindModel.Clients[index].LoginUserName = userName;
                this.bindModel.Clients[index].LoginUserCode = userCode;
                this.bindModel.Clients[index].LoginUserRole = userRole;
                this.bindModel.Clients[index].Status = status;
                this.bindModel.Clients[index].IsLogin = isLogin;

                client = this.bindModel.Clients[index];
            }

            return client;
        }

        /// <summary>
        /// 关闭中响应时间
        /// 判断是否可以关闭程序
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void Window_Closing(object sender, CancelEventArgs e)
        {
            //通过自定义的关闭按钮，符合关闭条件的才可以关闭，否则应该取消关闭动作
            if (!this.bAllowClose)
            {
                e.Cancel = true;
            }
        }
    }



    public class BindModel : INotifyPropertyChanged
    {
        public event PropertyChangedEventHandler PropertyChanged;

        private bool isServerAlive = false;
        private ObservableCollection<Models.Client> _Clients = new ObservableCollection<Client>();

        public bool IsServerAlive
        {
            get
            {
                return this.isServerAlive;
            }
            set
            {
                if (this.isServerAlive != value)
                {
                    this.isServerAlive = value;
                    if (PropertyChanged != null)
                    {
                        PropertyChanged(this, new PropertyChangedEventArgs("IsServerAlive"));
                    }
                }
            }
        }

        public ObservableCollection<Models.Client> Clients
        {
            get
            {
                return this._Clients;
            }
            set
            {
                this._Clients = value;

                PropertyChanged(this, new PropertyChangedEventArgs("Clients"));
            }
        }


        public BindModel()
        {
            string strJson = GetFileJson(AppDomain.CurrentDomain.BaseDirectory + "Clients.json");

            List<Client> clientList = Newtonsoft.Json.JsonConvert.DeserializeObject<List<Client>>(strJson);
            
            if(clientList!=null)
            {
                foreach(Client c in clientList)
                {
                    this.Clients.Add(c);
                }
            }
        }


        public string GetFileJson(string filepath)
        {
            string json = string.Empty;
            try
            {
              
                using (FileStream fs = new FileStream(filepath, FileMode.Open, System.IO.FileAccess.Read, FileShare.ReadWrite))
                {
                    using (StreamReader sr = new StreamReader(fs, Encoding.GetEncoding("gb2312")))
                    {
                        json = sr.ReadToEnd().ToString();
                    }
                }
            }
            catch (Exception ex)
            {
                
            }
           
            return json;
        }
    }
}
