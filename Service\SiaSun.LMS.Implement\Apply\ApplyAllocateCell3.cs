﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 三楼线边库入库分配货位申请
    /// </summary>
    public class ApplyAllocateCell3 : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {

            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCell.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);
                
                /*存在管理任务&库存：由5楼直接出库至3楼出库站台*/
                if (mMANAGE_MAIN != null && mSTORAGE_MAIN != null)
                {
                    if(mMANAGE_MAIN.MANAGE_TYPE_CODE.Equals(Enum.MANAGE_TYPE.ManageOut.ToString()) && 
                            mMANAGE_MAIN.PLAN_TYPE_CODE.Equals(Enum.PLAN_TYPE_CODE.PlanKitOut.ToString()))
                    {
                        //经81050扫码器申请后，更新管理任务的起点位置为81050，并调用ManageDownload方法下达调度任务
                        Model.WH_CELL mSTART_CELL = _P_WH_CELL.GetModel(5,"81050");
                        if (null == mSTART_CELL)
                        {
                            bResult = false;
                            sResult = string.Format("ApplyAlllocateCell3-ApplyHandle:未能找到起始位置{0}", "81050");
                            return bResult;
                        }
                        
                        bResult = new ManageBase().ManageDownLoad(mMANAGE_MAIN.MANAGE_ID, mSTART_CELL.CELL_CODE, true, out sResult);
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format("申请条码同时存在库存和任务，请根据箱子实际情况将多余的任务删除或将异常的库存出库");
                        return bResult;
                    }
                }
                /*存在管理任务&不存在库存：1楼越库*/
                else if (mMANAGE_MAIN != null)
                {
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManageIn.ToString()
                    || mMANAGE_MAIN.MANAGE_SOURCE != Enum.TASK_SOURCE.WMS.ToString()
                    || mMANAGE_MAIN.CROSS_FLAG != "3")
                    {
                        bResult = false;
                        sResult = string.Format("申请条码存在除[越库入库到3楼的任务]外的其他任务，无法执行入库到三楼线边库");
                        return bResult;
                    }

                    //出库的异常任务有可能被调度送到条码读头
                    if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.Error.ToString()
                        || mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.ExceptionComplete.ToString())
                    {
                        bResult = false;
                        sResult = string.Format("申请条码指向的任务状态异常");
                        return bResult;
                    }

                    //验证入库的物料是否存在盘点计划
                    IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);
                    if (lsMANAGE_LIST != null && lsMANAGE_LIST.Count > 0)
                    {
                        StringBuilder goodsIdCombine = new StringBuilder();
                        foreach (var mMANAGE_LIST in lsMANAGE_LIST)
                        {
                            goodsIdCombine.Append(mMANAGE_LIST.GOODS_ID);
                            goodsIdCombine.Append(",");
                        }
                        string goodsIdSql = (string.IsNullOrEmpty(goodsIdCombine.ToString().TrimEnd(','))) ? "" : string.Format("and GOODS_ID in ({0})", goodsIdCombine.ToString().TrimEnd(','));

                        DataTable dtPlanListCheck = this.GetList(string.Format("select 0 from V_PLAN_LIST where PLAN_TYPE_CODE = 'PlanCheck' and PLAN_STATUS!='Finish' {0}", goodsIdSql));
                        if (dtPlanListCheck != null && dtPlanListCheck.Rows.Count > 0)
                        {
                            bResult = false;
                            sResult = string.Format("箱中至少存在一种物料正在进行盘点_不允许入库_箱条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                            return bResult;
                        }
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                    {
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoadTrans", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), false }, out sResult);
                    }
                    else
                    {
                        bool wsNoticeWcs = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE == "3";
                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoad", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), true, wsNoticeWcs }, out sResult);
                    }
                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                /*不存在管理任务&存在库存：5楼下架（移库）至3楼*/
                else if (mSTORAGE_MAIN != null)
                {
                    //wdz add 2018-04-18
                    if (mSTORAGE_MAIN.IS_EXCEPTION == "1")
                    {
                        bResult = false;
                        sResult = string.Format("箱条码[{0}]库存已被标记为异常", mSTORAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_OPERATOR = string.Format("{0}申请", mIO_CONTROL_APPLY.DEVICE_CODE);
                    //mMANAGE_MAIN.MANAGE_OPERATOR = mSTORAGE_MAIN.STORAGE_PICK_OPERATOR;
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.START_CELL_ID = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE).CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                    mMANAGE_MAIN.END_CELL_ID = 0;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    string manageLevel = string.Empty;
                    mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("ApplyTaskLevel", out manageLevel) ? manageLevel : "0";

                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();   //上架不需要使用TASK_SOURCE判断接口
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageUp.ToString();

                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";

                    bool wsNoticeWcs = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE == "3";
                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                    bResult = this._S_ManageService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, true, true, wsNoticeWcs, false }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                /*不存在任务&库存：异常料箱，停在扫描点*/
                else
                {
                    bResult = false;
                    sResult = string.Format("申请条码不存在库存或者入库上架任务_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_ApplyTaskIn", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult? Enum.LOG_LEVEL.Information: Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCell.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
