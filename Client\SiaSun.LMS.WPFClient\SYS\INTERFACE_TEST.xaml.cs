﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.SYS
{
    public partial class INTERFACE_TEST : AvalonDock.DocumentContent
    {
        public INTERFACE_TEST()
        {
            InitializeComponent();
        }
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.cbxInterfaceName.Items.Add("---请选择调用接口---");
            this.cbxInterfaceName.Items.Add("执行结果回传接口");
            this.cbxInterfaceName.Items.Add("盘点结果回传接口");
            this.cbxInterfaceName.Items.Add("Miniload拣货确认回传接口");
            this.cbxInterfaceName.Items.Add("齐套箱出库箱号信息回传接口");
            this.cbxInterfaceName.Items.Add("紧急配料出库箱号信息回传接口");
            this.cbxInterfaceName.Items.Add("非立库输送任务结果回传接口");
            this.cbxInterfaceName.SelectedIndex = 0;
        }

        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;

            Button btn = e.OriginalSource as Button;
            switch (btn.Name)
            {
                case "countResultReturnFromWCS":
                    bResult = MainApp._I_WESJsonService.countResultReturnFromWCS("test", out sResult);
                    break;

                case "handleResultReturnFromWCS":
                    bResult = MainApp._I_WESJsonService.handleResultReturnFromWCS("test", out sResult);
                    break;

                case "miniloadPickConfirmFromWCS":
                    bResult = MainApp._I_WESJsonService.miniloadPickConfirmFromWCS("test", out sResult);
                    break;

                case "neatBoxInfoReceiveFromWCS":
                    bResult = MainApp._I_WESJsonService.neatBoxInfoReceiveFromWCS("test", out sResult);
                    break;

                case "urgentBoxReceiveFromWCS":
                    bResult = MainApp._I_WESJsonService.urgentBoxReceiveFromWCS("test", out sResult);
                    break;

                default:
                    break;
            }

            MainApp._MessageDialog.ShowResult(bResult, sResult);
        }

        private void btnInvoke_Click(object sender, RoutedEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;

            if (string.IsNullOrEmpty(this.tbxJson.Text))
            {
                bResult = false;
                sResult = string.Format("入参Json字符串不能为空");
            }
            else
            {
                switch (this.cbxInterfaceName.SelectedValue.ToString())
                {
                    case "盘点结果回传接口":
                        bResult = MainApp._I_WESJsonService.countResultReturnFromWCS(this.tbxJson.Text, out sResult);
                        break;

                    case "执行结果回传接口":
                        bResult = MainApp._I_WESJsonService.handleResultReturnFromWCS(this.tbxJson.Text, out sResult);
                        break;

                    case "Miniload拣货确认回传接口":
                        bResult = MainApp._I_WESJsonService.miniloadPickConfirmFromWCS(this.tbxJson.Text, out sResult);
                        break;

                    case "齐套箱出库箱号信息回传接口":
                        bResult = MainApp._I_WESJsonService.neatBoxInfoReceiveFromWCS(this.tbxJson.Text, out sResult);
                        break;

                    case "紧急配料出库箱号信息回传接口":
                        bResult = MainApp._I_WESJsonService.urgentBoxReceiveFromWCS(this.tbxJson.Text, out sResult);
                        break;

                    case "非立库输送任务结果回传接口":
                        bResult = false;
                        sResult = string.Format("WMS尚未开放此接口");
                        break;

                    default:
                        bResult = false;
                        sResult = string.Format("接口名选择有误");
                        break;
                }
            }
            MainApp._MessageDialog.ShowResult(bResult, sResult);
        }
    }
}
