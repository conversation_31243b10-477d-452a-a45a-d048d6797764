﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Jacky He
 *       日期：     2017/9/19
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;


    /// <summary>
    /// T_PICK_POSITION 
    /// </summary>
    [Serializable]
    [DataContract]
    public class T_PICK_POSITION
    {
        public T_PICK_POSITION()
        {

        }

        private int _position_id;
        private string _position_code;
        private string _positon_name;
        private int _station_id;
        private int _wh_cell_id;
        private string _remark;
        private string _dzbq_mac;
        private string _dzbq_status;
        private System.Decimal _dzbq_show;
        private System.Decimal _dzbq_store;
        private int _dzbq_flag;
        private string _dzbq_property1;
        private string _dzbq_property2;
        private string _dzbq_property3;
        private string _dzbq_property4;
        private string _dzbq_property5;
        private int _position_flag;
        private string _postion_status;

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int POSITION_ID
        {
            get { return _position_id; }
            set { _position_id = value; }
        }
        ///<sumary>
        /// 2024 改造唯一码
        ///</sumary>
        [DataMember]
        public string POSITION_CODE
        {
            get { return _position_code; }
            set { _position_code = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string POSITON_NAME
        {
            get { return _positon_name; }
            set { _positon_name = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int STATION_ID
        {
            get { return _station_id; }
            set { _station_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int WH_CELL_ID
        {
            get { return _wh_cell_id; }
            set { _wh_cell_id = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string REMARK
        {
            get { return _remark; }
            set { _remark = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string DZBQ_MAC
        {
            get { return _dzbq_mac; }
            set { _dzbq_mac = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string DZBQ_STATUS
        {
            get { return _dzbq_status; }
            set { _dzbq_status = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public System.Decimal DZBQ_SHOW
        {
            get { return _dzbq_show; }
            set { _dzbq_show = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public System.Decimal DZBQ_STORE
        {
            get { return _dzbq_store; }
            set { _dzbq_store = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int DZBQ_FLAG
        {
            get { return _dzbq_flag; }
            set { _dzbq_flag = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string DZBQ_PROPERTY1
        {
            get { return _dzbq_property1; }
            set { _dzbq_property1 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string DZBQ_PROPERTY2
        {
            get { return _dzbq_property2; }
            set { _dzbq_property2 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string DZBQ_PROPERTY3
        {
            get { return _dzbq_property3; }
            set { _dzbq_property3 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string DZBQ_PROPERTY4
        {
            get { return _dzbq_property4; }
            set { _dzbq_property4 = value; }
        }
        ///<sumary>
        /// 二期新增
        /// 二期拣选工作站拣选工作点对应的
        /// 使箱子离开的电子标签地址
        ///</sumary>
        [DataMember]
        public string DZBQ_PROPERTY5
        {
            get { return _dzbq_property5; }
            set { _dzbq_property5 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int POSITION_FLAG
        {
            get { return _position_flag; }
            set { _position_flag = value; }
        }
        ///<sumary>
        /// 二期 拣选工作点顺序 趋中效果
        /// 从来料方向
        /// 1号拣选点  顺序4
        /// 2号拣选点  顺序2
        /// 3号拣选点  顺序1
        /// 4号拣选点  顺序3
        /// 5号拣选点  顺序5
        ///</sumary>
        [DataMember]
        public string POSTION_STATUS
        {
            get { return _postion_status; }
            set { _postion_status = value; }
        }
    }
}
