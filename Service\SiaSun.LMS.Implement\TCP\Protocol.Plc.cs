﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.TCP
{
    public partial class Protocol
    {
        /// <summary>
        /// 01
        /// 料箱到位通知
        /// done
        /// </summary>
        public event ProtocolEventHandler BoxArriveRequest;

        /// <summary>
        /// 02
        /// 料箱离开应答
        /// done
        /// </summary>
        public event ProtocolEventHandler BoxLeaveResponse;

        /// <summary>
        /// 03
        /// 指示灯状态应答
        /// done
        /// </summary>
        public event ProtocolEventHandler LightsStatusResponse;

        /// <summary>
        /// 检验协议头，并返回协议长度
        /// done
        /// </summary>
        public int Check(byte byte0, byte byte1)
        {
            if (byte0 == 0xfc && byte1 == 0xfc)
            {
                return 30;
            }
            else
            {
                return -1;
            }
        }

        /// <summary>
        /// 校验协议并作处理
        /// done
        /// </summary>
        public bool Deal(byte[] bytes)
        {
            if (!Check(bytes))
            {
                return false;
            }
            else
            {
                ProtocolEventHandler eventHandler = null;
                ProtocolEventArgs eventArgs = null;
                switch (bytes[2])//报文名称 1料箱到位 2料箱离开 3灯光状态相互
                {
                    case 1:
                        eventHandler = BoxArriveRequest;
                        eventArgs = new ProtocolEventArgs(new object[]
                        {
                            1,
                            BytesToShort(bytes[4], bytes[5]),//PLC编号
                            BytesToShort(bytes[6], bytes[7]),//任务号
                            BytesToShort(bytes[8], bytes[9]),//状态
                            BytesToInt(bytes[10], bytes[11],bytes[12], bytes[13]),//设备号
                            BytesToString(bytes[14], bytes[15], bytes[16], bytes[17], bytes[18], bytes[19], bytes[20], bytes[21], bytes[22], bytes[23])//条码
                        });
                        break;
                    case 2:
                        eventHandler = BoxLeaveResponse;
                        eventArgs = new ProtocolEventArgs(new object[]
                        {
                            2,
                            BytesToShort(bytes[4], bytes[5]),//PLC编号
                            BytesToShort(bytes[6], bytes[7]),//任务号
                            BytesToShort(bytes[8], bytes[9]),//命令
                            BytesToInt(bytes[10], bytes[11],bytes[12], bytes[13]),//设备号
                            BytesToString(bytes[14], bytes[15], bytes[16], bytes[17], bytes[18], bytes[19], bytes[20], bytes[21], bytes[22], bytes[23])//条码
                        });
                        break;
                    case 3:
                        eventHandler = LightsStatusResponse;
                        eventArgs = new ProtocolEventArgs(new object[]
                        {
                            3,
                            BytesToShort(bytes[4], bytes[5]),//PLC编号
                            BytesToBooleanArray(bytes[8], bytes[9]),//开关灯状态
                            BytesToInt(bytes[10], bytes[11],bytes[12], bytes[13]),//设备号
                            BytesToString(bytes[14], bytes[15], bytes[16], bytes[17], bytes[18], bytes[19], bytes[20], bytes[21], bytes[22], bytes[23])//条码
                        });
                        break;
                    default: break;
                }
                if (eventHandler == null || eventArgs == null)
                {
                    return false;
                }
                else
                {
                    foreach (ProtocolEventHandler item in eventHandler.GetInvocationList())
                    {
                        item.BeginInvoke(eventArgs, null, null);
                    }
                    return true;
                }
            }
        }
    }
}
