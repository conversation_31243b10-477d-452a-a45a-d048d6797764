﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using System.Windows.Media;

namespace SiaSun.LMS.WPFClient.PICK.Model
{
    public class BoxTypeDR : Box
    {
        private static readonly List<Brush> defaultPartBackGround = new List<Brush>() {
            new SolidColorBrush(Colors.LightCyan),
            new SolidColorBrush(Colors.LightBlue),
            new SolidColorBrush(Colors.LightBlue),
            new SolidColorBrush(Colors.LightCyan)
        };
        public BoxTypeDR(string stock_barcode) : base(2, 2, Box_Type.DR,stock_barcode)
        {
            this.PartBackGround.Clear();

            this.PartBackGround = defaultPartBackGround;

            this.OnChangeBackGround += BoxTypeD_OnChangeBackGround;
        }

        private void BoxTypeD_OnChangeBackGround()
        {
            if (this.Select_part_index == -1)
            {
                this.PartBackGround = defaultPartBackGround;
                
            }
            else if (this.Select_part_index > (this.Part_count - 1))
            {
                this.PartBackGround = defaultPartBackGround;
            }
            else
            {
                List<Brush> brushes = new List<Brush>();
                brushes.AddRange(defaultPartBackGround);
                brushes[Select_part_index]= new SolidColorBrush(Colors.Yellow);
                this.PartBackGround = brushes;
            }
        }
    }
}
