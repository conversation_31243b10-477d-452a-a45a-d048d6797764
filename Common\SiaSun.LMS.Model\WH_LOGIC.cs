﻿/***************************************************************************
 * 
 *       功能：     存储区实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// WH_LOGIC 
	/// </summary>
    [Serializable]
    [DataContract]
	public class WH_LOGIC
	{
		public WH_LOGIC()
		{
			
		}
		
		private int _logic_id;
		private int _warehouse_id;
		private string _logic_type;
		private string _logic_code;
		private string _logic_name;
		private int _logic_order;
		private string _logic_flag;
		private string _logic_remark;
		private int _logic_group;
		
		///<sumary>
		/// 存储区编号
        ///</sumary>
        [DataMember]
		public int LOGIC_ID
		{
			get{return _logic_id;}
			set{_logic_id = value;}
		}
		///<sumary>
		/// 仓库编号
        ///</sumary>
        [DataMember]
		public int WAREHOUSE_ID
		{
			get{return _warehouse_id;}
			set{_warehouse_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string LOGIC_TYPE
		{
			get{return _logic_type;}
			set{_logic_type = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string LOGIC_CODE
		{
			get{return _logic_code;}
			set{_logic_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string LOGIC_NAME
		{
			get{return _logic_name;}
			set{_logic_name = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int LOGIC_ORDER
		{
			get{return _logic_order;}
			set{_logic_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string LOGIC_FLAG
		{
			get{return _logic_flag;}
			set{_logic_flag = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string LOGIC_REMARK
		{
			get{return _logic_remark;}
			set{_logic_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int LOGIC_GROUP
		{
			get{return _logic_group;}
			set{_logic_group = value;}
		}
	}
}
