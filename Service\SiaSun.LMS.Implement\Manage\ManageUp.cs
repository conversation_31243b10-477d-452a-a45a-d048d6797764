﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ManageUp:ManageBase
    {

        /// <summary>
        /// 根据库存生成移库任务
        /// </summary>
        public bool ManageCreate(Model.MANAGE_MAIN mMANAGE_MAIN,
                                   bool bTrans,
                                   bool bAutoSendControl,
                                   bool wsNoticeControl,
                                   bool bComplete, 
                                   out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
            Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

            if (mMANAGE_MAIN.STOCK_BARCODE != string.Empty && this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.LiKu.ToString(), out sResult))
            {
                sResult = string.Format("托盘{0}已经在立库区", mMANAGE_MAIN.STOCK_BARCODE);
                return false;
            }

            IList<Model.STORAGE_MAIN> lsSTORAGE_MAIN = null;

            if (mMANAGE_MAIN.STOCK_BARCODE == string.Empty)
            {
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListCellID(mMANAGE_MAIN.START_CELL_ID);
            }
            else
            {
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
            }

            bResult = lsSTORAGE_MAIN.Count > 0;
            if (!bResult)
            {
                sResult = string.Format("未找到库存");
                return bResult;
            }
            if (!string.IsNullOrEmpty(mMANAGE_MAIN.STOCK_BARCODE) && lsSTORAGE_MAIN.Count != 1)
            {
                bResult = false;
                sResult = string.Format("根据条码找到了多条库存信息 条码_{0}", mMANAGE_MAIN.STOCK_BARCODE);
                return bResult;
            }

            if (!string.IsNullOrEmpty(lsSTORAGE_MAIN[0].STOCK_BARCODE) && this._P_MANAGE_MAIN.GetModelStockBarcode(lsSTORAGE_MAIN[0].STOCK_BARCODE) != null)
            {
                bResult = false;
                sResult = string.Format("托盘条码{0}已经存在任务", lsSTORAGE_MAIN[0].STOCK_BARCODE);
                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST = null;
                SiaSun.LMS.Model.MANAGE_DETAIL mMANAGE_DETAIL = null;

                mMANAGE_MAIN.STOCK_BARCODE = lsSTORAGE_MAIN[0].STOCK_BARCODE;
                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN in lsSTORAGE_MAIN)
                {
                    IList<SiaSun.LMS.Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);

                    foreach (SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        mMANAGE_LIST = new SiaSun.LMS.Model.MANAGE_LIST();

                        mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                        mMANAGE_LIST.STORAGE_LIST_ID = mSTORAGE_LIST.STORAGE_LIST_ID;
                        mMANAGE_LIST.PLAN_LIST_ID = mSTORAGE_LIST.PLAN_LIST_ID;
                        mMANAGE_LIST.GOODS_ID = mSTORAGE_LIST.GOODS_ID;

                        bResult = this._S_GoodsService.GoodsPropertySetValue(mSTORAGE_LIST.GOODS_ID, mMANAGE_LIST, mSTORAGE_LIST, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }
                        mMANAGE_LIST.GOODS_PROPERTY1 = mSTORAGE_LIST.GOODS_PROPERTY1;
                        mMANAGE_LIST.GOODS_PROPERTY2 = mSTORAGE_LIST.GOODS_PROPERTY2;
                        mMANAGE_LIST.GOODS_PROPERTY3 = mSTORAGE_LIST.GOODS_PROPERTY3;
                        mMANAGE_LIST.GOODS_PROPERTY4 = mSTORAGE_LIST.GOODS_PROPERTY4;
                        mMANAGE_LIST.GOODS_PROPERTY5 = mSTORAGE_LIST.GOODS_PROPERTY5;
                        mMANAGE_LIST.GOODS_PROPERTY6 = mSTORAGE_LIST.GOODS_PROPERTY6;
                        mMANAGE_LIST.GOODS_PROPERTY7 = mSTORAGE_LIST.GOODS_PROPERTY7;
                        mMANAGE_LIST.GOODS_PROPERTY8 = mSTORAGE_LIST.GOODS_PROPERTY8;

                        mMANAGE_LIST.BOX_BARCODE = mSTORAGE_LIST.BOX_BARCODE;
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.MANAGE_LIST_REMARK = mSTORAGE_LIST.STORAGE_LIST_REMARK;

                        //wdz add 2018-04-18
                        if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageUp.ToString())
                        {
                            mMANAGE_LIST.PLAN_LIST_ID = 0;
                        }

                        this._P_MANAGE_LIST.Add(mMANAGE_LIST);

                        IList<Model.STORAGE_DETAIL> lsSTORAGE_DETAIL = this._P_STORAGE_DETAIL.GetList(mSTORAGE_LIST.STORAGE_LIST_ID);
                        foreach (Model.STORAGE_DETAIL mSTORAGE_DETAIL in lsSTORAGE_DETAIL)
                        {
                            //记录SN码
                            mMANAGE_DETAIL = new Model.MANAGE_DETAIL();
                            mMANAGE_DETAIL.MANAGE_LIST_ID = mMANAGE_LIST.MANAGE_LIST_ID;
                            mMANAGE_DETAIL.GOODS_BARCODE = mSTORAGE_DETAIL.GOODS_BARCODE;
                            this._P_MANAGE_DETAIL.Add(mMANAGE_DETAIL);
                        }
                    }
                    //wdz add 2017-12-07
                    Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModelPlanListId(lsSTORAGE_LIST[0].PLAN_LIST_ID);
                    if (mPLAN_MAIN != null)
                    {
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = mPLAN_MAIN.PLAN_GROUP;
                    }
                }

                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);

                if (mWH_CELL_START != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }

                if (mWH_CELL_END != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }

                int controlId = 0;
                if (bAutoSendControl)
                {
                    bResult = this.ManageDownLoad(mMANAGE_MAIN.MANAGE_ID, string.Empty, false, false, out controlId, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (bComplete)
                {
                    bResult = this.ManageComplete(mMANAGE_MAIN.MANAGE_ID, false, out sResult);
                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }
                sResult = string.Format("托盘{0}生成任务成功", mMANAGE_MAIN.STOCK_BARCODE);

                this._P_Base_House.CommitTransaction(bTrans);

                //wdz add 2018-03-03  webservice通知调度
                if (wsNoticeControl)
                {
                    this._S_ManageService.ManageDownLoadWebService(controlId);
                }
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);
                bResult = false;
                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 根据库存生成移库任务
        /// </summary>
        public bool ManageCreate(Model.MANAGE_MAIN mMANAGE_MAIN,
                                   bool bTrans,
                                   bool bAutoSendControl,
                                   bool bComplete, out string sResult)
        {
            return this.ManageCreate(mMANAGE_MAIN, bTrans, bAutoSendControl, false, bComplete, out sResult);
        }


        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans">是否独立事务</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            bResult = null != mMANAGE_MAIN;

            if (!bResult)
            {
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());

                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

            if (mMANAGE_TYPE == null)
            {
                bResult = false;

                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                           || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }

                if (!bResult)
                {
                    sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());

                    return bResult;
                }
                if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);

                    if (lsMANAGE_LIST.Count == 1 && lsMANAGE_LIST[0].GOODS_ID == this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID)
                    {
                        //wdz add 2018-02-06 如果是空箱上架则更新终点状态为托盘
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                               SiaSun.LMS.Enum.CELL_STATUS.Pallet.ToString(),
                                               SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                               out sResult);
                    }
                    else
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                       SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(),
                                                                       SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                       out sResult);
                    }
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());

                    return bResult;
                }

                bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID }, out sResult);

                if (!bResult)
                {
                    sResult = string.Format("库存处理错误-{0}", sResult);

                    return bResult;
                }

                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("上架任务完成时出错_未找到库存信息_箱条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }

                //如果是拣选后上架的任务则更新对应的计划单信息，已经上架成功的齐套箱不再更新
                if (mMANAGE_MAIN.CELL_MODEL == Enum.CellModel.KitBox.ToString("d") 
                    && mMANAGE_MAIN.MANAGE_TYPE_CODE==Enum.MANAGE_TYPE.ManageKitUp.ToString()
                    && mSTORAGE_MAIN.KITBOX_UP_COMPLETE!="1")
                {
                    IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);
                    foreach(Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                    {
                        Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);
                        if (mPLAN_LIST == null)
                        {
                            bResult = false;
                            sResult = string.Format("拣选上架任务完成时未能找到对应的计划单 条码-{0} 任务ID-{1} 任务单ID-{2} 任务单中记录的计划单-{3}",
                                mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.MANAGE_ID, mMANAGE_LIST.MANAGE_LIST_ID, mMANAGE_LIST.PLAN_LIST_ID);
                            return bResult;
                        }

                        //mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        //mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        //this._P_PLAN_LIST.Update(mPLAN_LIST);

                        //wdz add 2018-04-27  保存SN码
                        IList<Model.MANAGE_DETAIL> lsMANAGE_DETAIL = this._P_MANAGE_DETAIL.GetListManageListID(mMANAGE_LIST.MANAGE_LIST_ID);
                        if (lsMANAGE_DETAIL != null && lsMANAGE_DETAIL.Count != 0)
                        {
                            foreach (Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                            {
                                Model.PLAN_DETAIL mPLAN_DETAIL = new Model.PLAN_DETAIL();
                                mPLAN_DETAIL.PLAN_LIST_ID = mPLAN_LIST.PLAN_LIST_ID;
                                mPLAN_DETAIL.GOODS_BARCODE = mMANAGE_DETAIL.GOODS_BARCODE;
                                this._P_PLAN_DETAIL.Add(mPLAN_DETAIL);
                            }
                        }
                    }

                    //wdz add 齐套箱首次上架完成后做标记
                    if(mMANAGE_MAIN!=null)
                    {
                        mSTORAGE_MAIN.KITBOX_UP_COMPLETE = "1";
                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                    }
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

    }
}
