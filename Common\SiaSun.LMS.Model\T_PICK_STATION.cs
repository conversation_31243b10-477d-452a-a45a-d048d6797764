﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Jacky He
 *       日期：     2017/9/19
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;


    /// <summary>
    /// T_PICK_STATION 
    /// </summary>
    [Serializable]
    [DataContract]
    public class T_PICK_STATION
    {
        public T_PICK_STATION()
        {

        }

        private int _station_id;
        private string _station_code;
        private string _station_name;
        private string _station_ip;
        private string _remark;
        private int _position_count;
        private string _plan_group_code;
        private string _plan_group_name;
        private int _wh_cell_id;
        private string _station_mac;
        private int _flag;
        private int _flag_os;
        private string _property1;
        private string _property2;
        private string _property3;
        private string _property4;
        private string _property5;
        private string _property6;
        private string _property7;
        private string _property8;
        private string _property9;

        ///<sumary>
        /// ID
        ///</sumary>
        [DataMember]
        public int STATION_ID
        {
            get { return _station_id; }
            set { _station_id = value; }
        }
        ///<sumary>
        /// 拣选工作站编码
        ///</sumary>
        [DataMember]
        public string STATION_CODE
        {
            get { return _station_code; }
            set { _station_code = value; }
        }
        ///<sumary>
        /// 拣选工作站名称
        ///</sumary>
        [DataMember]
        public string STATION_NAME
        {
            get { return _station_name; }
            set { _station_name = value; }
        }
        ///<sumary>
        /// 拣选工作站操作一体机IP地址
        ///</sumary>
        [DataMember]
        public string STATION_IP
        {
            get { return _station_ip; }
            set { _station_ip = value; }
        }
        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string REMARK
        {
            get { return _remark; }
            set { _remark = value; }
        }
        ///<sumary>
        /// 拣选点数量
        ///</sumary>
        [DataMember]
        public int POSITION_COUNT
        {
            get { return _position_count; }
            set { _position_count = value; }
        }
        ///<sumary>
        /// 订单唯一码
        ///</sumary>
        [DataMember]
        public string PLAN_GROUP_CODE
        {
            get { return _plan_group_code; }
            set { _plan_group_code = value; }
        }
        ///<sumary>
        /// 订单号
        /// OrderCode
        /// Done
        ///</sumary>
        [DataMember]
        public string PLAN_GROUP_NAME
        {
            get { return _plan_group_name; }
            set { _plan_group_name = value; }
        }
        ///<sumary>
        /// 拣选工作站站台（出库）
        ///</sumary>
        [DataMember]
        public int WH_CELL_ID
        {
            get { return _wh_cell_id; }
            set { _wh_cell_id = value; }
        }
        ///<sumary>
        /// 拣选工作站预绑定唯一码
        ///</sumary>
        [DataMember]
        public string STATION_MAC
        {
            get { return _station_mac; }
            set { _station_mac = value; }
        }
        ///<sumary>
        /// 启用拣选标志
        ///</sumary>
        [DataMember]
        public int FLAG
        {
            get { return _flag; }
            set { _flag = value; }
        }
        ///<sumary>
        /// 未用
        ///</sumary>
        [DataMember]
        public int FLAG_OS
        {
            get { return _flag_os; }
            set { _flag_os = value; }
        }
        ///<sumary>
        /// PLC No
        ///</sumary>
        [DataMember]
        public string PROPERTY1
        {
            get { return _property1; }
            set { _property1 = value; }
        }
        ///<sumary>
        /// 拣选工作站立库箱暂存货位
        /// 60016
        ///</sumary>
        [DataMember]
        public string PROPERTY2
        {
            get { return _property2; }
            set { _property2 = value; }
        }
        ///<sumary>
        /// 拣选工作站是否空闲（1是空闲）
        ///</sumary>
        [DataMember]
        public string PROPERTY3
        {
            get { return _property3; }
            set { _property3 = value; }
        }
        ///<sumary>
        /// 拣选工作站拣选用料箱号
        ///</sumary>
        [DataMember]
        public string PROPERTY4
        {
            get { return _property4; }
            set { _property4 = value; }
        }
        ///<sumary>
        /// 当前执行的拣选任务MAIN_ID
        ///</sumary>
        [DataMember]
        public string PROPERTY5
        {
            get { return _property5; }
            set { _property5 = value; }
        }


        ///<sumary>
        /// 车间
        ///</sumary>
        [DataMember]
        public string PROPERTY6
        {
            get { return _property6; }
            set { _property6 = value; }
        }
        ///<sumary>
        ///
        ///</sumary>
        [DataMember]
        public string PROPERTY7
        {
            get { return _property7; }
            set { _property7 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string PROPERTY8
        {
            get { return _property8; }
            set { _property8 = value; }
        }
        ///<sumary>
        /// 拣选模式，1为新模式，0为老模式
        ///</sumary>
        [DataMember]
        public string PROPERTY9
        {
            get { return _property9; }
            set { _property9 = value; }
        }
    }
}
