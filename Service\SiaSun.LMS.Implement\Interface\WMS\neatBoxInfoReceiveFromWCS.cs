﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 齐套箱出库箱号信息回传接口
    /// </summary>
    public class neatBoxInfoReceiveFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            string _uniqueCode = string.Empty;          //唯一码
            string _boxNo = string.Empty;                //箱号
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public List<FirstDetails> firstDetails { get; set; }//一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号
            public List<SecondDetails> secondDetails { get; set; }//二级明细

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }
        }
        /// <summary>
        /// 入参_二级明细
        /// </summary>
        public class SecondDetails
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty;     //数量

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(int paraManageId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();

            try
            {
                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(paraManageId);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("neatBoxInfoReceiveFromWCS.NotifyMethod:未能获取任务信息 任务ID_{0}", paraManageId);
                    return bResult;
                }
                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(paraManageId);
                if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("neatBoxInfoReceiveFromWCS.NotifyMethod:未能获取任务列表 任务ID_{0}", paraManageId);
                    return bResult;
                }

                List<FirstDetails> lsFirstDetails = new List<FirstDetails>();
                //var manageListGroup = lsMANAGE_LIST.GroupBy(r => r.GOODS_PROPERTY8);

                //以WBS号分组
                var manageListGroupByWbsno = lsMANAGE_LIST.GroupBy(r => r.GOODS_PROPERTY8);

                foreach (var manageListGroupByWbsnoItem in manageListGroupByWbsno)
                {
                    //以任务号分组
                    var manageListGroupByWbsnoGroupByTaskno = manageListGroupByWbsnoItem.GroupBy(r => r.GOODS_PROPERTY6);
                    foreach (var manageListGroupByWbsnoGroupByTasknoItem in manageListGroupByWbsnoGroupByTaskno)
                    {
                        List<SecondDetails> lsSecondDetails = new List<SecondDetails>();
                        foreach (var manageListItem in manageListGroupByWbsnoGroupByTasknoItem)
                        {
                            SecondDetails secondDetails = new SecondDetails();
                            secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(manageListItem.GOODS_ID).GOODS_CODE;
                            secondDetails.quantity = manageListItem.MANAGE_LIST_QUANTITY.ToString();
                            lsSecondDetails.Add(secondDetails);
                        }
                        FirstDetails firstDetails = new FirstDetails();
                        firstDetails.taskNo = manageListGroupByWbsnoGroupByTasknoItem.ToList()[0].GOODS_PROPERTY6;
                        firstDetails.projectNo = manageListGroupByWbsnoGroupByTasknoItem.ToList()[0].GOODS_PROPERTY7;
                        firstDetails.wbsNo = manageListGroupByWbsnoItem.Key;
                        firstDetails.secondDetails = lsSecondDetails;
                        lsFirstDetails.Add(firstDetails);
                    }
                }

                //foreach (var manageListGroupItem in manageListGroup)
                //{
                //    List<SecondDetails> lsSecondDetails = new List<SecondDetails>();
                //    foreach (var manageListItem in manageListGroupItem)
                //    {
                //        SecondDetails secondDetails = new SecondDetails();
                //        secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(manageListItem.GOODS_ID).GOODS_CODE;
                //        secondDetails.quantity = manageListItem.MANAGE_LIST_QUANTITY.ToString();
                //        lsSecondDetails.Add(secondDetails);
                //    }

                //    FirstDetails firstDetails = new FirstDetails();
                //    firstDetails.taskNo = manageListGroupItem.ToList()[0].GOODS_PROPERTY6;
                //    firstDetails.projectNo = manageListGroupItem.ToList()[0].GOODS_PROPERTY7;
                //    firstDetails.wbsNo = manageListGroupItem.Key;
                //    firstDetails.secondDetails = lsSecondDetails;
                //    lsFirstDetails.Add(firstDetails);
                //}

                inputPara.uniqueCode = mMANAGE_MAIN.MANAGE_RELATE_CODE;
                inputPara.boxNo = mMANAGE_MAIN.STOCK_BARCODE;
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";
                inputPara.firstDetails = lsFirstDetails;

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);
                bResult = this._S_WESJsonService.neatBoxInfoReceiveFromWCS(strInputParaJson, out sResult);
                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if(outputPara.responseCode!="1")
                    {
                        bResult = false;
                        sResult = string.Format(" neatBoxInfoReceiveFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("neatBoxInfoReceiveFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }

    }
}
