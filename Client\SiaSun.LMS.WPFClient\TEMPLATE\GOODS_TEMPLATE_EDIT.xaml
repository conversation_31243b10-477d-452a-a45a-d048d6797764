﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.TEMPLATE.GOODS_TEMPLATE_EDIT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="GOODS_TEMPLATE_EDIT" Height="340" Width="599" Loaded="DocumentContent_Loaded" xmlns:dxr="http://schemas.devexpress.com/winfx/2008/xaml/ribbon" xmlns:dxb="http://schemas.devexpress.com/winfx/2008/xaml/bars">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition MinWidth="200" MaxWidth="300"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <GroupBox Name="grpbPlanMain" Grid.Column="0"  Header="编辑计划信息" >
            <Grid>
                <uc:ucEditPanel x:Name="ucEditTemplate" DockPanel.Dock="Top" MinHeight="100"></uc:ucEditPanel>
            </Grid>
        </GroupBox>

        <GridSplitter Grid.Column="1" Width="2" VerticalAlignment="Stretch" ></GridSplitter>

        <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="1.5*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
            </Grid.RowDefinitions>

            <GroupBox Grid.Row="0" Header="双击选择物料清单" Margin="1">
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="*"></RowDefinition>
                    </Grid.RowDefinitions>
                    <uc:ucQuickQuery x:Name="ucQueryGoods" Grid.Row="0" ></uc:ucQuickQuery>
                    <uc:ucCommonDataGrid x:Name="gridGoods" Grid.Row="1"></uc:ucCommonDataGrid>
                </Grid>
            </GroupBox>
            
            <GridSplitter Grid.Row="1" Height="2" HorizontalAlignment="Stretch"></GridSplitter>

            <GroupBox Name="grpbPlanList" Grid.Row="2" Margin="1"  Header="编辑清单物料数量、属性">
                <uc:ucSplitPropertyGridTab x:Name="gridTemplateList"></uc:ucSplitPropertyGridTab>
            </GroupBox>
            
            <Border Grid.Row="3"  Margin="1" ButtonBase.Click="Border_Click">
                <WrapPanel HorizontalAlignment="Center">
                    <Button Name="btnSave" Width="70"  Margin="5" >保存</Button>
                    <Button Name="btnReset" Width="70"  Margin="5">重置</Button>
                </WrapPanel>
            </Border>
        </Grid>
    </Grid>
</ad:DocumentContent>
