﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun-XCJT
 *       日期：     2016/12/26
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
    /// MID_GOODS
	/// </summary>
    public class P_MID_GOODS : P_Base_ERP
	{
        public P_MID_GOODS()
		{
			//
            // TODO: 此处添加MID_TASK的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
        public IList<MID_GOODS> GetList()
		{
            return ExecuteQueryForList<MID_GOODS>("MID_GOODS_SELECT", null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MID_GOODS> GetList(int GOODS_ID)
        {
            return this.ExecuteQueryForList<MID_GOODS>("MID_GOODS_SELECT_BY_GOODS_ID", GOODS_ID);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MID_GOODS> GetList(string GOODS_CODE)
        {
            return this.ExecuteQueryForList<MID_GOODS>("MID_GOODS_SELECT_BY_GOODS_CODE", GOODS_CODE);
        }

		/// <summary>
		/// 新建
		/// </summary>
        public int Add(MID_GOODS MID_GOODS)
		{
            return ExecuteInsert("MID_TASK_INSERT", MID_GOODS);
		}

		/// <summary>
		/// 修改
		/// </summary>
        public int Update(MID_GOODS MID_GOODS)
		{
            return ExecuteUpdate("MID_GOODS_UPDATE", MID_GOODS);
		}		

		/// <summary>
		/// 删除
		/// </summary>
        public int Delete(int goods_id)
		{
            return ExecuteDelete("MID_GOODS_DELETE", goods_id);
		}
	}
}
