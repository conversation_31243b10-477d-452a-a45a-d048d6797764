﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun-XCJT
 *       日期：     2016/12/26
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
    /// MID_TASK 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MID_TASK
	{
        public MID_TASK()
		{			
		}

        private int _fid;
        private string _task_no;
        private string _io_flag;
        //private string _goods_id;
        private string _goods_code;
        //private string _goods_name;
        private string _goods_property;
        private int _plan_qty;
        private int _finish_qty;
        private string _finish_flag;
        private string _error_info;

		///<sumary>
		/// 物料表ID
		///</sumary>
        [DataMember]
        public int FID
		{
            get { return _fid; }
            set { _fid = value; }
		}
		///<sumary>
		/// 任务编号
        ///</sumary>
        [DataMember]
        public string TASK_NO
		{
            get { return _task_no; }
            set { _task_no = value; }
		}
		///<sumary>
		/// 出入库类型
        ///</sumary>
        [DataMember]
        public string IO_FLAG
		{
            get { return _io_flag; }
            set { _io_flag = value; }
		}
        /////<sumary>
        ///// 物料ID
        /////</sumary>
        //[DataMember]
        //public string GOODS_ID
        //{
        //    get { return _goods_id; }
        //    set { _goods_id = value; }
        //}
		///<sumary>
		/// 物料编码
        ///</sumary>
        [DataMember]
        public string GOODS_CODE
		{
            get { return _goods_code; }
            set { _goods_code = value; }
		}
        /////<sumary>
        ///// 物料名称
        /////</sumary>
        //[DataMember]
        //public string GOODS_NAME
        //{
        //    get { return _goods_name; }
        //    set { _goods_name = value; }
        //}
		///<sumary>
		/// 物料属性
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY
		{
            get { return _goods_property; }
            set { _goods_property = value; }
		}
		///<sumary>
		/// 计划数量
        ///</sumary>
        [DataMember]
        public int PLAN_QTY
		{
            get { return _plan_qty; }
            set { _plan_qty = value; }
		}
		///<sumary>
		/// 完成数量
        ///</sumary>
        [DataMember]
        public int FINISH_QTY
		{
            get { return _finish_qty; }
            set { _finish_qty = value; }
		}
		///<sumary>
		/// 计划完成标志
        ///</sumary>
        [DataMember]
        public string FINISH_FLAG
		{
            get { return _finish_flag; }
            set { _finish_flag = value; }
		}
        ///<sumary>
        /// 错误信息
        ///</sumary>
        [DataMember]
        public string ERROR_INFO
        {
            get { return _error_info; }
            set { _error_info = value; }
        }
    }
}
