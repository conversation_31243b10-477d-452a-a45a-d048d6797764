﻿using Microsoft.AspNet.SignalR;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;
using System.Windows;

namespace SignalServer.Services
{
    // 注意: 使用“重构”菜单上的“重命名”命令，可以同时更改代码和配置文件中的类名“SignalServerSrv”。
    public class SignalServerSrv : ISignalServerSrv
    {
        /// <summary>
        /// 获得所有已登录的拣选工作站列表
        /// </summary>
        /// <param name="group">组，暂不用</param>
        /// <param name="PickStationCodes">已登录的拣选工作站编码列表</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool ConnnectNdLoginPickStations(string group, out List<string> PickStationCodes, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            PickStationCodes = null;

            try
            {
                PickStationCodes = Models.WorkHub.MessageClients.Keys.Cast<string>().ToList();
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }


        /// <summary>
        /// 通知拣选工作站绑定新订单
        /// </summary>
        /// <param name="PickStationCode">拣选工作站编码</param>
        /// <param name="PlanGroupToBind">新订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool InformPickStationBindNewOrder(string PickStationCode, string PlanGroupToBind, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {

            }
            catch(Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }

        /// <summary>
        /// 通知拣选工作站绑定新订单
        /// </summary>
        /// <param name="informDict">key:拣选工作站编码；value:订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool InformPickStationBindNewOrderMultiple(Dictionary<string, string> informDict, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                if (informDict == null)
                {
                    sResult = string.Format("通讯字典为空");
                    bResult = false;
                    return bResult;
                }

                if (informDict.Keys.Count == 0)
                {
                    sResult = string.Format("通讯字典不含任何内容");
                    bResult = false;
                    return bResult;
                }
                
                foreach(KeyValuePair<string,string> kpv in informDict)
                {
                    string connectId = Models.WorkHub.MessageClients.SingleOrDefault(c => c.Key == kpv.Key).Value.ConnectID;
                    IHubContext context= GlobalHost.ConnectionManager.GetHubContext<Models.WorkHub>();

                    context.Clients.Client(connectId).BroadcastTextMessage(kpv.Key, kpv.Value);


                    Application.Current.Dispatcher.Invoke(new Action(
                                    () =>
                                    {
                                        MainWindow mainWindow = ((MainWindow)Application.Current.MainWindow);
                                        mainWindow.WriteLog(string.Format("通知：工作站:{0} 绑定:{1}",kpv.Key,kpv.Value));
                                    }
                                    ));

                    Models.Log.WriteInfo(
                        string.Format("通知：工作站:{0} 绑定:{1}", kpv.Key, kpv.Value),
                        "InformPickStationBindNewOrderMultiple");
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                
            }
            if (bResult)
            {
                Models.Log.WriteInfo(string.Format("入参:{0},成功",
                    Newtonsoft.Json.JsonConvert.SerializeObject(informDict)), 
                    "InformPickStationBindNewOrderMultiple");
            }
            else
            {
                Models.Log.WriteError(string.Format("入参:{0},失败，{1}",
                    Newtonsoft.Json.JsonConvert.SerializeObject(informDict),sResult),
                    "InformPickStationBindNewOrderMultiple");
            }
            return bResult;
        }
    }
}
