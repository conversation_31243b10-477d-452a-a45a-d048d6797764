﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ManageDown : ManageBase
    {


        /// <summary>
        /// 移库任务 任务完成生成库存
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PLAN_LIST_ID"></param>
        /// <param name="STOCK_BARCODE"></param>
        /// <param name="START_CELL_ID"></param>
        /// <param name="END_CELL_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="bAutoSendControl"></param>
        /// <param name="bComplete"></param>
        /// <param name="sResult"></param>
        /// <param name="MANAGE_ID"></param>
        /// <returns></returns>
        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 bool bComplete,
                                 bool bAutoSendControl,
                                 out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int MANAGE_ID = 0;

            try
            {
                //wdz add 2019-08-07 11:22:30
                string validRegexString = string.Empty;
                if (this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex", out validRegexString) &&
                    !Common.RegexValid.IsValidate(mMANAGE_MAIN.STOCK_BARCODE, validRegexString))
                {
                    bResult = false;
                    sResult = string.Format("申请条码不符合规范_申请值[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }

                SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

                if (mMANAGE_TYPE == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                    return bResult;
                }


                if (mMANAGE_MAIN.STOCK_BARCODE != string.Empty && this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    bResult = false;

                    sResult = string.Format("托盘条码{0}已经存在任务", mMANAGE_MAIN.STOCK_BARCODE);

                    return bResult;
                }

                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                if (mWH_CELL_START != null
                     && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                     && (mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Have.ToString() || mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Pallet.ToString())
                     && mWH_CELL_START.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    bResult = false;

                    sResult = string.Format("起始位置{0}不可用", mWH_CELL_START.CELL_CODE);

                    return bResult;
                }

                if (mWH_CELL_END != null
                    && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                    && mWH_CELL_END.CELL_STATUS != Enum.CELL_STATUS.Nohave.ToString()
                    && mWH_CELL_END.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    bResult = false;

                    sResult = string.Format("终止位置{0}不可用", mWH_CELL_END.CELL_CODE);

                    return bResult;
                }

                

                this._P_Base_House.BeginTransaction(bTrans);

                #region 判断是否需要倒库
                bResult = new ManageBase().ManageCheck(mWH_CELL_START, mMANAGE_MAIN, out sResult);

                if (!bResult)
                {
                    sResult = string.Format(@"{0}下架任务下达失败：{1}", mWH_CELL_START.CELL_CODE, sResult);
                    this._P_Base_House.RollBackTransaction(bTrans);
                    return bResult;
                }
                #endregion

                //校验库存位置和任务位置是否相符 2022-04-18
                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                if(mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format(@"未找到条码[{0}]的库存信息", mMANAGE_MAIN.STOCK_BARCODE);
                    this._P_Base_House.RollBackTransaction(bTrans);
                    return bResult;
                }
                if(mSTORAGE_MAIN.CELL_ID != mWH_CELL_START.CELL_ID)
                {
                    bResult = false;
                    sResult = string.Format(@"条码[{0}]的库存位置ID[{1}]与任务起点ID[{2}]不相符", mMANAGE_MAIN.STOCK_BARCODE, mSTORAGE_MAIN.CELL_ID , mWH_CELL_START.CELL_ID);
                    this._P_Base_House.RollBackTransaction(bTrans);
                    return bResult;
                }

                if (mMANAGE_MAIN.END_CELL_ID == 0)
                {
                    if (mSTORAGE_MAIN != null)
                        mMANAGE_MAIN.END_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                }

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;


                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }


                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                if (bComplete)
                {
                    bResult = _S_ManageService.Invoke(
                                                     mMANAGE_TYPE.MANAGE_TYPE_CLASS,
                                                     "ManageComplete",
                                                     new object[] { MANAGE_ID, false },
                                                     out sResult
                                                     );


                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }


                }
                else
                {



                    if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                                   || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }

                    if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                                 || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }
                }

                if (bAutoSendControl)
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }


                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans">是否独立事务</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            bResult = null != mMANAGE_MAIN;

            if (!bResult)
            {
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());

                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

            if (mMANAGE_TYPE == null)
            {
                bResult = false;

                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                if (mMANAGE_TYPE.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManageCheckDown.ToString() ||
                    mMANAGE_MAIN.MANAGE_STATUS != Enum.MANAGE_STATUS.WaitingConfirm.ToString())
                {

                    SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                    SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                    if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                               || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                       SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                       SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                       out sResult);
                    }

                    if (!bResult)
                    {
                        sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());

                        return bResult;
                    }
                    if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                        || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                       SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(),
                                                                       SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                       out sResult);
                    }
                    if (!bResult)
                    {
                        sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());

                        return bResult;
                    }

                    //wdz add 2019-02-25  整理下架任务完成后将库存加到特殊的箱中，托盘号"V+设备编码"
                    if (mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageArrangeDown.ToString() ||
                        mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageArrangeGoodsDown.ToString())
                    {
                        bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "VirtualStorageMoveIn", new object[] {
                            mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageArrangeDown.ToString()?Enum.ArrangeWorkMode.Arrange:Enum.ArrangeWorkMode.ArrangeGoods,
                            string.Format("V{0}",  mWH_CELL_END.CELL_CODE),
                            mMANAGE_MAIN.STOCK_BARCODE }, out sResult);
                    }
                    else
                    {
                        bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID }, out sResult);
                    }

                    if (!bResult)
                    {
                        sResult = string.Format("库存处理错误-{0}", sResult);

                        return bResult;
                    }
                }

                //wdz add 2017-12-26  盘点下架任务完成后将任务状态修改为待确认
                if (mMANAGE_TYPE.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageCheckDown.ToString() &&
                    mMANAGE_MAIN.MANAGE_STATUS != Enum.MANAGE_STATUS.WaitingConfirm.ToString())
                {
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingConfirm.ToString();
                    this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                    this._P_IO_CONTROL.DeleteManageID(MANAGE_ID);
                    return bResult;
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }


        /// <summary>
        /// 下架任务“等待确认”状态时库存确认操作
        /// </summary>
        public bool ManageConfirm(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("ManageDown.ManageConfirm:未能找到任务信息 任务ID-{0}", MANAGE_ID);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);
                if (mPLAN_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("ManageDown.ManageConfirm:未找到计划 计划ID-{0}", mMANAGE_MAIN.PLAN_ID);
                    return bResult;
                }
                if (mPLAN_MAIN.PLAN_TYPE_CODE != Enum.PLAN_TYPE_CODE.PlanCheck.ToString())
                {
                    bResult = false;
                    sResult = string.Format("ManageDown.ManageConfirm:计划类型不是预期的盘点计划 计划ID-{0}", mMANAGE_MAIN.PLAN_ID);
                    return bResult;
                }
                Model.T_PICK_STATION mT_PICK_STATION = this._P_T_PICK_STATION.GetModel_BY_CELL_ID(mMANAGE_MAIN.END_CELL_ID);
                if (mT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("ManageDown.ManageConfirm:未找到与任务关联的拣选工作站 计划ID-{0}", mMANAGE_MAIN.PLAN_ID);
                    return bResult;
                }

                //wdz add 2017-12-27 将调整库存后的库存量累加到计划列表中
                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageCheckDown.ToString() &&
                    mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.WaitingConfirm.ToString())
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                    if (mSTORAGE_MAIN == null)
                    {
                        bResult = false;
                        sResult = string.Format("ManageDown.ManageConfirm:盘点确认库存时未能找到库存信息 容器条码-{0}", mMANAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("ManageDown.ManageConfirm:盘点确认库存时未能找到库存列表 容器条码-{0} 库存ID-{1}", mMANAGE_MAIN.STOCK_BARCODE, mSTORAGE_MAIN.STORAGE_ID);
                        return bResult;
                    }
                    IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(mMANAGE_MAIN.PLAN_ID);
                    if (lsPLAN_LIST == null || lsPLAN_LIST.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("ManageDown.ManageConfirm:盘点确认库存时未能找到盘点计划列表 容器条码-{0} 计划ID-{1}", mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.PLAN_ID);
                        return bResult;
                    }
                    foreach (Model.PLAN_LIST itemPlanList in lsPLAN_LIST)
                    {
                        var storageListFilted = lsSTORAGE_LIST.Where(r => r.GOODS_ID == itemPlanList.GOODS_ID);
                        if (storageListFilted.Count() > 0)
                        {
                            itemPlanList.PLAN_LIST_QUANTITY_APPEND += storageListFilted.First().STORAGE_LIST_QUANTITY;
                            this._P_PLAN_LIST.Update(itemPlanList);
                        }
                    }

                    ////wdz add 2018-09-12 如果盘点出现库存为空时，将库存物料改为空箱，如果不是一格箱库存为空，将List删除           
                    //if (mSTORAGE_MAIN != null)
                    //{
                    //    //if (mSTORAGE_MAIN.CELL_MODEL == Enum.CellModel.GoodsBox1.ToString("d"))
                    //    //{
                    //    if (lsSTORAGE_LIST != null && lsSTORAGE_LIST.Count == 1 && lsSTORAGE_LIST[0].STORAGE_LIST_QUANTITY == 0)
                    //    {
                    //        Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel("emptyBox");
                    //        if (mGOODS_MAIN != null)
                    //        {
                    //            lsSTORAGE_LIST[0].STORAGE_LIST_REMARK = string.Format("盘点时将原数量[{0}]调整为[0]，生成空箱库存", lsSTORAGE_LIST[0].STORAGE_LIST_QUANTITY);
                    //            lsSTORAGE_LIST[0].GOODS_ID = mGOODS_MAIN.GOODS_ID;
                    //            lsSTORAGE_LIST[0].STORAGE_LIST_QUANTITY = 1;

                    //            // 2019-06-17 14:28:41 by ywz  去除异常标记
                    //            lsSTORAGE_LIST[0].GOODS_PROPERTY2 = "0";

                    //            this._P_STORAGE_LIST.Update(lsSTORAGE_LIST[0]);


                    //            mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");

                    //            this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                    //        }
                    //    }
                    //    //}
                    //    else
                    //    {
                    //        foreach (var item in lsSTORAGE_LIST)
                    //        {
                    //            if (item.STORAGE_LIST_QUANTITY == 0 && lsPLAN_LIST.Where(r => r.GOODS_ID == item.GOODS_ID).Count() > 0)
                    //            {
                    //                this._P_STORAGE_LIST.Delete(item.STORAGE_LIST_ID);
                    //            }
                    //        }
                    //    }
                    //}


                    //wdz add 2019-11-03 09:54 及时将盘空箱子改为空箱库存
                    if (mSTORAGE_MAIN != null)
                    {
                        foreach (var item in lsSTORAGE_LIST)
                        {
                            if (item.STORAGE_LIST_QUANTITY == 0 && lsPLAN_LIST.Where(r => r.GOODS_ID == item.GOODS_ID).Count() > 0)
                            {
                                this._P_STORAGE_LIST.Delete(item.STORAGE_LIST_ID);
                            }
                        }

                        if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).Count < 1)
                        {
                            Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel("emptyBox");
                            if (mGOODS_MAIN != null)
                            {
                                Model.STORAGE_LIST newSTORAGE_LIST = new Model.STORAGE_LIST();
                                newSTORAGE_LIST.STORAGE_LIST_REMARK = string.Format("盘点箱[{0}]已盘空，生成空箱库存", mSTORAGE_MAIN.STOCK_BARCODE);
                                newSTORAGE_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;
                                newSTORAGE_LIST.STORAGE_LIST_QUANTITY = 1;
                                // 2019-06-17 14:28:41 by ywz  去除异常标记
                                newSTORAGE_LIST.GOODS_PROPERTY2 = "0";
                                newSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;
                                this._P_STORAGE_LIST.Add(newSTORAGE_LIST);
                                mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d");
                                this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                            }
                        }
                    }

                }


                bResult = this.ManageComplete(MANAGE_ID, false, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("ManageDown.ManageConfirm:任务完成时错误_{0}", sResult);
                    return bResult;
                }

                ///////////生成回库调度任务
                Model.SYS_USER mSYS_USER = new Model.SYS_USER() { USER_CODE = "SYS" };
                bResult = new Implement.WDZ.PickStationOperation().LetBoxLeave(mT_PICK_STATION, mSYS_USER, mMANAGE_MAIN.STOCK_BARCODE, out sResult);
                //log
                this.CreateSysLog(Enum.LogThread.Task, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ManageDown.ManageConfirm():库存确认时通知拣选工作站箱子离开{0} {1}", bResult ? "成功" : "失败 ", sResult));
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManageDown.ManageConfirm:程序异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

    }
}
