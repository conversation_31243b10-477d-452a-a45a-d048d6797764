<?xml version="*.0" encoding="utf-8"?>
<configuration>
<configSections>
  <section name="log4net" type="log4net.Config.Log4NetConfigurationSectionHandler, log4net" />
</configSections>
 <log4net>
   <!--<root>
     <level value="ALL" />
     --><!--文件形式记录日志--><!--
     <appender-ref ref="LogFileAppender" />
     --><!--控制台控制显示日志--><!--
     --><!--<appender-ref ref="ConsoleAppender" />--><!--
     --><!--Windows事件日志--><!--
     --><!--<appender-ref ref="EventLogAppender" />--><!--
     --><!-- 数据库 --><!--
     --><!--<appender-ref ref="AdoNetAppender_Access" />--><!--
   </root>-->

   <!--SiaSun.LMS.WinService项目的日志-->
   <logger name="WinServiceLog">
     <!--FATAL-毁灭级别,ERROR-错误级别,WARN-警告级别,INFO-消息级别,DEBUG-调试级别-->
     <level value="DEBUG"/>
     <appender-ref ref="WinServiceAppender" />
   </logger>

   <!--SiaSun.LMS.Implement项目的日志-->
   <logger name="ImplementLog">
     <level value="DEBUG"/>
     <appender-ref ref="LogFileAppender" />
   </logger>

   <!--系统运行日志-->
   <logger name="SysRunningLog">
     <level value="DEBUG"/>
     <appender-ref ref="AdoNetAppender_Oracle" />
   </logger>

   <!--定义输出到文件中-->
   <appender name="LogFileAppender" type="log4net.Appender.RollingFileAppender">
     <!--日志的路径-->
     <file value="log\" />
     <appendToFile value="true" />

     <rollingStyle value="Date" />

     <datePattern value="yyyy-MM-dd&quot;.html&quot;" />

     <staticLogFileName value="false" />

     <layout type="log4net.Layout.PatternLayout">
       <!--每条日志末尾的文字说明-->
       <header value="&lt;title&gt;运行日志&lt;/title&gt; &lt;table style='table-layout:auto|fixed' sort='true' resize='true' width='*00%' border='*' bordercolorlight='#808080' bordercolordark='#f8f8f8'  cellpadding='*' cellspacing='0' &gt;
              &lt;tr&gt;
            &lt;td&gt;记录时间&lt;/td&gt;
            &lt;td&gt;线程&lt;/td&gt;
            &lt;td&gt;模块&lt;/td&gt;
            &lt;td&gt;信息&lt;/td&gt;
            &lt;/tr&gt;" />

       <conversionPattern value="&lt;tr&gt;
             &lt;td&gt;%date&lt;/td&gt;
            &lt;td&gt;%thread&lt;/td&gt;
            &lt;td&gt;%logger&lt;/td&gt;
            &lt;td&gt;%message&lt;/td&gt;
            &lt;/tr&gt;" />
       <footer value="&lt;/table&gt;" />
     </layout>

   </appender>

   
    <appender name="WinServiceAppender" type="log4net.Appender.RollingFileAppender">

     <File value="Log\WinServiceLog.log" />
     <AppendToFile value="true" />
     <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />
     <Encoding value="UTF-8" />
     <MaxSizeRollBackups value="2" />
     <StaticLogFileName value="true" />
     <RollingStyle value="Size" />
     <DatePattern value="yyyy-MM-dd/&quot;ReflectionLayout.log&quot;"  />
     <maximumFileSize value="*MB" />

     <filter type="log4net.Filter.LevelRangeFilter">
       <param name="LevelMin" value="ALL" />
       <param name="LevelMax" value="FATAL" />
     </filter>

     <!--记录的格式。一般用log4net.Layout.PatternLayout布局-->
     <layout type="log4net.Layout.PatternLayout">
       <footer value="" />
       <!--<conversionPattern value="记录时间：%date 线程ID:[%thread] 日志级别：%-5level 出错类：%logger property:[%property{NDC}] - 错误描述：%message%newline" />-->
       <conversionPattern value="[%date] [%class] [%-5level] [%thread] [%message]%newline%exception" />

     </layout>-->

   </appender>
   
    <appender name="ImplementAppender" type="log4net.Appender.RollingFileAppender">
     
      <!--日志文件路径，“/”与“/”作用相同，到达的目录相同，文件夹不存在则新建 -->
      <!--按文件大小方式输出时在这里指定文件名，并且当天的日志在下一天时在文件名后自动追加当天日期形成新文件。-->
      <!--按照日期形式输出时，直接连接元素DatePattern的value形成文件路径。此处使用这种方式-->
      <!--param的名称,可以直接查对应的appender类的属性名即可,这里要查的就是RollingFileAppender类的属性 -->
      <File value="Log\Implementlog.log" />

      <!--是否追加到文件-->
      <AppendToFile value="true" />

      <!--记录日志写入文件时，不锁定文本文件，防止多线程时不能写Log,官方说线程非安全-->
      <lockingModel type="log4net.Appender.FileAppender+MinimalLock" />

      <!--使用Unicode编码-->
      <Encoding value="UTF-8" />

      <!--最多产生的日志文件数，超过则只保留最新的n个。设定值value="－*"为不限文件数-->
      <MaxSizeRollBackups value="2" />

      <!--是否只写到一个文件中-->
      <StaticLogFileName value="true" />

      <!--按照何种方式产生多个日志文件(日期[Date],文件大小[Size],混合[Composite])-->
      <RollingStyle value="Size" />
      <!--按日期产生文件夹和文件名［在日期方式与混合方式下使用］-->
      
      <!--此处按日期产生文件夹，文件名固定。-->
      <!--<datePattern value="yyyy-MM-dd&quot;.log&quot;" />-->
      <DatePattern value="yyyy-MM-dd/&quot;ReflectionLayout.log&quot;"  />

      <!--这是按日期产生文件夹，并在文件名前也加上日期-->
      <!--<param name="DatePattern" value="yyyyMMdd/yyyyMMdd&quot;-TimerServer.log&quot;"  />-->

      <!--这是先按日期产生文件夹，再形成下一级固定的文件夹-->
      <!--<param name="DatePattern" value="yyyyMMdd/&quot;TimerServer/TimerServer.log&quot;"  />-->



      <!--每个文件的大小。只在混合方式与文件大小方式下使用。

          超出大小后在所有文件名后自动增加正整数重新命名，数字最大的最早写入。

          可用的单位:KB|MB|GB。不要使用小数,否则会一直写入当前日志-->
      <maximumFileSize value="*MB" />

      <!--计数类型为*，2，3…-->
      <!--<CountDirection value="*"/>-->
      
      <!--过滤设置，LevelRangeFilter为使用的过滤器。-->
      <!--日志的等级，它们由高到底分别为：OFF > FATAL > ERROR > WARN > INFO > DEBUG  > ALL-->

      <filter type="log4net.Filter.LevelRangeFilter">
        <param name="LevelMin" value="ALL" />
        <param name="LevelMax" value="FATAL" />
      </filter>

      <!--记录的格式。一般用log4net.Layout.PatternLayout布局-->
      <layout type="log4net.Layout.PatternLayout">
        <footer value="" />
        <!--<conversionPattern value="记录时间：%date 线程ID:[%thread] 日志级别：%-5level 出错类：%logger property:[%property{NDC}] - 错误描述：%message%newline" />-->
        <conversionPattern value="[%date] [%class] [%-5level] [%thread] [%message]%newline%exception" />

      </layout>-->

    </appender>

   <appender name="AdoNetAppender_Oracle" type="log4net.Appender.ADONetAppender">

     <bufferSize value="0" />
     <connectionType value="Oracle.ManagedDataAccess.Client.OracleConnection, Oracle.ManagedDataAccess, Version=4.*22.*8.3, Culture=neutral, PublicKeyToken=89b483f429c47342"/>
     <!--<connectionString value="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*72.*0.*0.3)(PORT=*52*))(CONNECT_DATA=(SERVICE_NAME=orcl)));User Id=c##***;Password=***;" />-->
	   <connectionString value="Data Source=(DESCRIPTION=(ADDRESS=(PROTOCOL=TCP)(HOST=*92.*68.204.200)(PORT=*52*))(CONNECT_DATA=(SERVICE_NAME=ORCL)));User Id=C##***;Password=***;" />

     <commandText value="insert into SYS_LOG (LOG_ID,LOG_THREAD,LOG_LEVEL,LOG_LOGGER,LOG_DATE,LOG_MESSAGE) values (SYS_LOG_SEQ.nextval, :customThread, :customLevel, :customLogger, :log_date, :message)" />

     <parameter>
       <parameterName value=":customThread" />
       <dbType value="String" />
       <size value="50" />
       <layout type="log4net.Layout.PatternLayout">
         <conversionPattern value="%property{CustomThread}" />
       </layout>
     </parameter>
     <parameter>
       <parameterName value=":customLevel" />
       <dbType value="String" />
       <size value="50" />
       <layout type="log4net.Layout.PatternLayout">
         <conversionPattern value="%property{CustomLevel}" />
       </layout>
     </parameter>
     <parameter>
       <parameterName value=":customLogger" />
       <dbType value="String" />
       <size value="50" />
       <layout type="log4net.Layout.PatternLayout">
         <conversionPattern value="%property{CustomLogger}" />
       </layout>
     </parameter>
     <parameter>
       <parameterName value=":log_date" />
       <dbType value="String" />
       <size value="50" />
       <layout type="log4net.Layout.PatternLayout" >
         <conversionPattern value="%date{yyyy-MM-dd HH:mm:ss.ffff}" />
       </layout>
     </parameter>
     <parameter>
       <parameterName value=":message" />
       <dbType value="String" />
       <size value="*000" />
       <layout type="log4net.Layout.PatternLayout">
         <conversionPattern value="%message" />
       </layout>
     </parameter>
   </appender>


   <!--定义输出到控制台命令行中-->
    <appender name="ConsoleAppender" type="log4net.Appender.ConsoleAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
      </layout>
    </appender>
    
    <!--定义输出到windows事件中-->
    <appender name="EventLogAppender" type="log4net.Appender.EventLogAppender">
      <layout type="log4net.Layout.PatternLayout">
        <conversionPattern value="%date [%thread] %-5level %logger [%property{NDC}] - %message%newline" />
      </layout>
    </appender>
    
    <!--定义输出到数据库中，这里举例输出到oracle数据库中，数据库为ORCL  modify by 20**-09-07-->
    <appender  xmlns=""  name="AdoNetAppender_Oracle"     type="log4net.Appender.AdoNetAppender">

      <connectionType value="System.Data.OracleClient, Version=*.0.5000.0, Culture=neutral, PublicKeyToken=b77a5c56*934e089"/>

      <connectionString value="data source=(DESCRIPTION =(ADDRESS_LIST =(ADDRESS = (PROTOCOL = TCP)(HOST = weiyu)(PORT = *52*))(ADDRESS =(PROTOCOL = TCP)(HOST = weiyu)(PORT = *52*))(FAILOVER = ON) (LOAD_BALANCE = OFF))(CONNECT_DATA =(SERVER = weiyu)(SERVICE_NAME = ORCL)));User ID=SA;Password=*;Unicode=true;Max Pool Size=*0;Min Pool Size=5;Pooling=true;"/>
      
      <commandText value="INSERT INTO SYS_Log ([log_Date],[log_Thread],[log_Level],[log_Logger],[log_Message]) VALUES (@log_date, @thread, @log_level, @logger, @message)" />
      
      <!--定义各个参数-->
      <parameter>
        <parameterName value="@log_date" />
        
        <dbType value="Date" />
        
        <layout type="log4net.Layout.PatternLayout">
          
          <!--<conversionPattern value="%date" />-->
          
        </layout>
        
      </parameter>
      
      <parameter>
        
        <parameterName value="@thread" />
        
        <dbType value="String" />
        
        <size value="255" />
        
        <layout type="log4net.Layout.PatternLayout">
          
          <conversionPattern value="%thread" />
          
        </layout>
        
      </parameter>
      
      <parameter>
        
        <parameterName value="@log_level" />
        
        <dbType value="String" />
        
        <size value="50" />
        
        <layout type="log4net.Layout.PatternLayout">
          
          <conversionPattern value="%level" />
          
        </layout>
        
      </parameter>
      
      <parameter>
        
        <parameterName value="@logger" />
        
        <dbType value="String" />
        
        <size value="255" />
        
        <layout type="log4net.Layout.PatternLayout">
          
          <conversionPattern value="%logger" />
          
        </layout>
        
      </parameter>
      
      <parameter>
        
        <parameterName value="@message" />
        
        <dbType value="String" />
        
        <size value="4000" />
        
        <layout type="log4net.Layout.PatternLayout">
          
          <conversionPattern value="%message" />
          
        </layout>
        
      </parameter>
      
    </appender>
    
    <!--定义日志的输出媒介，下面定义日志以四种方式输出。也可以下面的按照一种类型或其他类型输出。-->
  </log4net>
  
</configuration>