﻿using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class CaluGoodsUsagerateJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            string sResult = string.Empty;

            try
            {
                sResult = MainApp.BaseService._S_GoodsService.CalculateGoodsUsageRate();
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("CaluGoodsUsagerateJob.Execute:异常", ex);
                throw;
            }
            finally
            {
                if (!string.IsNullOrEmpty(sResult))
                {
                    Program.sysLog.WarnFormat("CaluGoodsUsagerateJob.Execute:处理申请失败_{0}", sResult);
                }
            }
        }
    }
}
