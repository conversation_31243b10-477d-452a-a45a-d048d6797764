﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;

namespace SiaSun.LMS.WPFClient.UC.Validate
{
    public class ValidateNullEmptyRule:ValidationRule
    {
        public override ValidationResult Validate(object value, System.Globalization.CultureInfo cultureInfo)
        {
            if (string.IsNullOrEmpty((string)value))
            { 
                return new ValidationResult(false,string.Format("不允许空值"));
            }
            return ValidationResult.ValidResult;
        }
    }
}
