﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// RECORD_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class RECORD_MAIN
	{
		public RECORD_MAIN()
		{
			
		}

        private int _record_id;
        private int _manage_id;
        private string _goods_template_code;
		private string _plan_code;
		private string _plan_type_code;
		private string _manage_type_code;
		private string _stock_barcode;
		private string _start_position;
		private string _end_position;
		private string _record_operator;
		private string _manage_begin_time;
		private string _manage_end_time;
		private string _manage_confirm_time;
		private string _record_remark;

        private string _cell_model;
        private string _manage_source;
        private string _manage_relate_code;
        private int _plan_id;

        ///<sumary>
        /// 记录id
        ///</sumary>
        [DataMember]
		public int RECORD_ID
		{
			get{return _record_id;}
			set{_record_id = value;}
		}
        ///<sumary>
        /// 原任务id
        ///</sumary>
        [DataMember]
        public int MANAGE_ID
        {
            get { return _manage_id; }
            set { _manage_id = value; }
        }	
        ///<sumary>
        /// 配盘编码
        ///</sumary>
        [DataMember]
        public string GOODS_TEMPLATE_CODE
        {
            get { return _goods_template_code; }
            set { _goods_template_code = value; }
        }
		///<sumary>
		/// 计划单号
        ///</sumary>
        [DataMember]
		public string PLAN_CODE
		{
			get{return _plan_code;}
			set{_plan_code = value;}
		}
		///<sumary>
		/// 计划类型
        ///</sumary>
        [DataMember]
		public string PLAN_TYPE_CODE
		{
			get{return _plan_type_code;}
			set{_plan_type_code = value;}
		}
		///<sumary>
		/// 管理任务类型
        ///</sumary>
        [DataMember]
		public string MANAGE_TYPE_CODE
		{
			get{return _manage_type_code;}
			set{_manage_type_code = value;}
		}
		///<sumary>
		/// 容器条码
        ///</sumary>
        [DataMember]
		public string STOCK_BARCODE
		{
			get{return _stock_barcode;}
			set{_stock_barcode = value;}
		}
		///<sumary>
		/// 起始货位索引
		///</sumary>
        [DataMember]
		public string START_POSITION
		{
			get{return _start_position;}
			set{_start_position = value;}
		}
		///<sumary>
		/// 终止货位索引
        ///</sumary>
        [DataMember]
        public string END_POSITION
		{
            get { return _end_position; }
            set { _end_position = value; }
		}
		///<sumary>
		/// 操作者
        ///</sumary>
        [DataMember]
        public string RECORD_OPERATOR
		{
            get { return _record_operator; }
            set { _record_operator = value; }
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MANAGE_BEGIN_TIME
		{
			get{return _manage_begin_time;}
			set{_manage_begin_time = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MANAGE_END_TIME
		{
			get{return _manage_end_time;}
			set{_manage_end_time = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string MANAGE_CONFIRM_TIME
		{
			get{return _manage_confirm_time;}
			set{_manage_confirm_time = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string RECORD_REMARK
		{
			get{return _record_remark;}
			set{_record_remark = value;}
		}

        ///<sumary>
		/// 箱类型
		///</sumary>
		public string CELL_MODEL
        {
            get { return _cell_model; }
            set { _cell_model = value; }
        }
        ///<sumary>
        /// 任务来源
        ///</sumary>
        public string MANAGE_SOURCE
        {
            get { return _manage_source; }
            set { _manage_source = value; }
        }
        ///<sumary>
        /// 关联单号
        ///</sumary>
        public string MANAGE_RELATE_CODE
        {
            get { return _manage_relate_code; }
            set { _manage_relate_code = value; }
        }
        ///<sumary>
		/// wdz取代PLAN_CODE
		///</sumary>
		public int PLAN_ID
        {
            get { return _plan_id; }
            set { _plan_id = value; }
        }
    }
}
