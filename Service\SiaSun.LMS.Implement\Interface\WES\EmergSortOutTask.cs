﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 紧急配料出库
    /// </summary>
    public class EmergSortOutTask : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _taskNo = string.Empty;          //任务单号
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public List<FirstDetails> firstDetails { get; set; }//一级明细

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }

        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty; //数量

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }


        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            int iPlanId = 0;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("EmergSortOutTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.taskNo) || string.IsNullOrEmpty(taskInfo.uniqueCode) || taskInfo.firstDetails.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("EmergSortOutTask.NotifyMethod:入参存在空值");
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModelPlanCode(taskInfo.uniqueCode, string.Empty, string.Empty);
                if (mPLAN_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("EmergSortOutTask.NotifyMethod:传入唯一码已存在计划 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                if(taskInfo.firstDetails.Count != taskInfo.firstDetails.GroupBy(r=>r.itemCode).Count())
                {
                    bResult = false;
                    outJson = string.Format("EmergSortOutTask.NotifyMethod:入参中一级明细条目数与物料种类不等}");
                    return bResult;
                }

                //wdz add 2019-07-07
                string planInoutStation = string.Empty;
                System.Data.DataTable dtWorkMode = this.GetList("select PARAMETER_KEY from SYS_PARAMETER where PARAMETER_KEY in ('WorkingMode-21098','WorkingMode-22141') and PARAMETER_VALUE ='Emerg' order by PARAMETER_ID");
                if (dtWorkMode != null && dtWorkMode.Rows.Count > 0)
                {
                    planInoutStation = dtWorkMode.Rows[0]["PARAMETER_KEY"].ToString().Split('-')[1];
                }
                else
                {
                    if (!this._S_SystemService.GetSysParameter("DefaultEmergOutStation", out planInoutStation))
                    {
                        planInoutStation = string.Empty;
                    }
                }
                if (planInoutStation == string.Empty)
                {
                    bResult = false;
                    outJson = string.Format("EmergSortOutTask.NotifyMethod:未能获取出库站台_请检查整理工位工作模式和默认紧急出库站台设置}");
                    return bResult;
                }

                mPLAN_MAIN = new Model.PLAN_MAIN();
                mPLAN_MAIN.PLAN_CODE = taskInfo.uniqueCode;
                mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mPLAN_MAIN.PLAN_CREATE_TIME= Common.StringUtil.GetDateTime();
                mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                //wdz add 2019-07-07
                mPLAN_MAIN.PLAN_INOUT_STATION = planInoutStation;
                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanOutEmerg.ToString();
                string planLevel = string.Empty;
                mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("EmergOutPlanLevel", out planLevel) ? planLevel : "0";

                List<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();

                var firstDetailGroup = taskInfo.firstDetails.GroupBy(r => r.itemCode);
                foreach(var firstDetail in firstDetailGroup)
                {
                    if (string.IsNullOrEmpty(firstDetail.Key))
                    {
                        bResult = false;
                        outJson = string.Format("EmergSortOutTask.NotifyMethod:入参存在空值");
                        return bResult;
                    }
                    double quantity = firstDetail.Sum(r => double.Parse(r.quantity));
                    if (quantity < 0)
                    {
                        bResult = false;
                        outJson = string.Format("EmergSortOutTask.NotifyMethod:传入数量有误");
                        return bResult;
                    }               

                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(firstDetail.Key);
                    if (mGOODS_MAIN == null)
                    {
                        bResult = false;
                        outJson = string.Format("EmergSortOutTask.NotifyMethod:传入物料编码有误 物料编码_{0}", firstDetail.Key);
                        return bResult;
                    }

                    Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                    mPLAN_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;
                    mPLAN_LIST.PLAN_LIST_QUANTITY = Convert.ToDecimal(quantity);
                    lsPLAN_LIST.Add(mPLAN_LIST);
                }

                object[] invokeOutParams = new object[] { };
                bResult = this.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST, iPlanId, outJson }, out invokeOutParams);
                int.TryParse(invokeOutParams[2].ToString(), out iPlanId);
                if (!bResult)
                {
                    outJson = invokeOutParams[3].ToString();
                    return bResult;
                }

                bResult = this.Invoke("PlanBase", "PlanOutDownLoad", new object[] { iPlanId }, out outJson);
                if (!bResult)
                {
                    string strTemp = string.Empty;
                    if(!this.Invoke("PlanBase", "PlanCancel", new object[] { iPlanId ,false}, out strTemp))
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("EmergSortOutTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划失败_{1}_计划ID[{2}]", outJson, strTemp, iPlanId));
                    }
                    else
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("EmergSortOutTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划成功_计划ID[{1}]", outJson, iPlanId));
                    }

                    outJson = string.Format("EmergSortOutTask.NotifyMethod:执行计划失败 {0}", outJson);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("EmergSortOutTask.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
