﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.CURRENT_APPLY"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="CURRENT_APPLY" Height="300" Width="300"  Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

        <uc:ucQuickQuery x:Name="ucQueryControl" Margin="1,1,1,3" Grid.Row="0" BorderBrush="Black" Grid.ColumnSpan="2"></uc:ucQuickQuery>

        <GroupBox Header="申请历史列表"  Margin="1,5,1,1" Grid.Row="1" Grid.ColumnSpan="2">
            <uc:ucCommonDataGrid x:Name="ucApplyDataGrid" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
