﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// IO_CONTROL_ROUTE
	/// </summary>
	public class P_IO_CONTROL_ROUTE : P_Base_House
	{
		public P_IO_CONTROL_ROUTE ()
		{
			//
			// TODO: 此处添加IO_CONTROL_ROUTE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<IO_CONTROL_ROUTE> GetList(string START_DEVICE_CODE,string END_DEVICE_CODE)
		{
            Hashtable ht = new Hashtable();

            ht.Add("START_DEVICE", START_DEVICE_CODE);
            ht.Add("END_DEVICE", END_DEVICE_CODE);
            return ExecuteQueryForList<IO_CONTROL_ROUTE>("IO_CONTROL_ROUTE_SELECT_BY_START_DEVICE_END_DEVICE", ht);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(IO_CONTROL_ROUTE io_control_route)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("IO_CONTROL_ROUTE");
                io_control_route.CONTROL_ROUTE_ID = id;
            }        

            return ExecuteInsert("IO_CONTROL_ROUTE_INSERT",io_control_route);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(IO_CONTROL_ROUTE io_control_route)
		{
			return ExecuteUpdate("IO_CONTROL_ROUTE_UPDATE",io_control_route);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IO_CONTROL_ROUTE GetModel(System.Int32 CONTROL_ROUTE_ID)
		{
			return ExecuteQueryForObject<IO_CONTROL_ROUTE>("IO_CONTROL_ROUTE_SELECT_BY_ID",CONTROL_ROUTE_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 CONTROL_ROUTE_ID)
		{
			return ExecuteDelete("IO_CONTROL_ROUTE_DELETE",CONTROL_ROUTE_ID);
		}
		

	}
}
