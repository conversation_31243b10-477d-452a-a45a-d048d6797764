﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="T_PICK_POSITION_PLAN_BIND" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="T_PICK_POSITION_PLAN_BIND" type="SiaSun.LMS.Model.T_PICK_POSITION_PLAN_BIND, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="T_PICK_POSITION_PLAN_BIND">
      <result property="PICK_POSITION_PLAN_BIND_ID" column="pick_position_plan_bind_id" />
      <result property="PICK_POSITION_ID" column="pick_position_id" />
      <result property="PICK_STATION_ID" column="pick_station_id" />
      <result property="PLAN_ID" column="plan_id" />
      <result property="USER_ID" column="user_id" />
      <result property="CELL_ID" column="cell_id" />
      <result property="BIND_TIME" column="bind_time" />
      <result property="BIND_REMARK" column="bind_remark" />
      <result property="BIND_PROPERTY1" column="bind_property1" />
      <result property="BIND_PROPERTY2" column="bind_property2" />
      <result property="BIND_PROPERTY3" column="bind_property3" />
      <result property="FLAG" column="flag" />
      <result property="REMARK" column="remark" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      pick_position_plan_bind_id,
      pick_position_id,
      pick_station_id,
      plan_id,
      user_id,
      cell_id,
      bind_time,
      bind_remark,
      bind_property1,
      bind_property2,
      bind_property3,
      flag,
      remark
      From T_PICK_POSITION_PLAN_BIND
    </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_ID" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_plan_bind_id=#PICK_POSITION_PLAN_BIND_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_id=#PICK_POSITION_ID#
          order by bind_time desc
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_FORLIST" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_id=#PICK_POSITION_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_FLAG" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_id=#PICK_POSITION_ID#
          and flag=#FLAG#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_FLAG" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_station_id=#PICK_STATION_ID#
          and flag=#FLAG#
        </isParameterPresent>
      </dynamic>
    </select>


	  <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_FLAG_NEW" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

		  <dynamic prepend="WHERE">
			  <isParameterPresent>
				  pick_position_id=#PICK_POSITION_ID#
				  and flag=#FLAG#
			  </isParameterPresent>
		  </dynamic>
	  </select>
    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_PARTLY" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_station_id=#PICK_STATION_ID#
        </isParameterPresent>
      </dynamic>
    </select>

	  <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_PARTLY_NEW" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

		  <dynamic prepend="WHERE">
			  <isParameterPresent>
				  pick_position_id=#PICK_POSITION_ID#
			  </isParameterPresent>
		  </dynamic>
	  </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_PLAN_GROUP_PARTLY" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_station_id=#PICK_STATION_ID#
          and plan_id in (select plan_id from plan_main where plan_group=#PLAN_GROUP#)
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_STATION_ID_PLAN_LIST_ID" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_station_id=#PICK_STATION_ID# and plan_id in (select plan_id from plan_list where plan_list_id=#PLAN_LIST_ID#)
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_PLAN_BIND_SELECT_BY_PICK_POSITION_ID_PLAN_LIST_ID_FLAG" parameterClass="int" extends = "T_PICK_POSITION_PLAN_BIND_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_id=#PICK_POSITION_ID# and plan_id in (select plan_id from plan_list where plan_list_id=#PLAN_LIST_ID#)
          and flag=#FLAG#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <insert id="T_PICK_POSITION_PLAN_BIND_INSERT" parameterClass="T_PICK_POSITION_PLAN_BIND">
      Insert Into T_PICK_POSITION_PLAN_BIND (
      pick_position_plan_bind_id,
      pick_position_id,
      pick_station_id,
      plan_id,
      user_id,
      cell_id,
      bind_time,
      bind_remark,
      bind_property1,
      bind_property2,
      bind_property3,
      flag,
      remark
      )Values(
      #PICK_POSITION_PLAN_BIND_ID#,
      #PICK_POSITION_ID#,
      #PICK_STATION_ID#,
      #PLAN_ID#,
      #USER_ID#,
      #CELL_ID#,
      #BIND_TIME#,
      #BIND_REMARK#,
      #BIND_PROPERTY1#,
      #BIND_PROPERTY2#,
      #BIND_PROPERTY3#,
      #FLAG#,
      #REMARK#
      )
    </insert>

    <update id="T_PICK_POSITION_PLAN_BIND_UPDATE" parameterClass="T_PICK_POSITION_PLAN_BIND">
      Update T_PICK_POSITION_PLAN_BIND Set
      pick_position_plan_bind_id=#PICK_POSITION_PLAN_BIND_ID#,
      pick_position_id=#PICK_POSITION_ID#,
      pick_station_id=#PICK_STATION_ID#,
      plan_id=#PLAN_ID#,
      user_id=#USER_ID#,
      cell_id=#CELL_ID#,
      bind_time=#BIND_TIME#,
      bind_remark=#BIND_REMARK#,
      bind_property1=#BIND_PROPERTY1#,
      bind_property2=#BIND_PROPERTY2#,
      bind_property3=#BIND_PROPERTY3#,
      flag=#FLAG#,
      remark=#REMARK#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_plan_bind_id=#PICK_POSITION_PLAN_BIND_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="T_PICK_POSITION_PLAN_BIND_DELETE" parameterClass="int">
      Delete From T_PICK_POSITION_PLAN_BIND
      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_position_plan_bind_id=#PICK_POSITION_PLAN_BIND_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

    <delete id="T_PICK_POSITION_PLAN_BIND_DELETE_BY_PICK_STATION_ID" parameterClass="int">
      Delete From T_PICK_POSITION_PLAN_BIND
      <dynamic prepend="WHERE">
        <isParameterPresent>
          pick_station_id=#PICK_STATION_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

	  <delete id="T_PICK_POSITION_PLAN_BIND_DELETE_BY_PICK_POSITION_ID" parameterClass="int">
		  Delete From T_PICK_POSITION_PLAN_BIND
		  <dynamic prepend="WHERE">
			  <isParameterPresent>
				  pick_position_id=#PICK_POSITION_ID#
			  </isParameterPresent>
		  </dynamic>
	  </delete>
	  
  </statements>
</sqlMap>