﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.TEMPLATE
{
    public partial class GOODS_TEMPLATE_EDIT : AvalonDock.DocumentContent
    {
        private int _TEMPLATE_ID;

        public GOODS_TEMPLATE_EDIT()
        {
            InitializeComponent();

            this.ucQueryGoods.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQueryGoods_U_Query);
            
            this.gridGoods.gridApp.MouseDoubleClick += new MouseButtonEventHandler(gridApp_MouseDoubleClick);
        }

        public GOODS_TEMPLATE_EDIT(int TEMPLATE_ID)
            : this()           
        {
            this._TEMPLATE_ID = TEMPLATE_ID;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitQueryControl();

            this.GoodsBind(string.Empty);

            this.TemplateBind();

            this.TemplateListBind();

            Register_DataTable_Event();
        }

        private void InitQueryControl()
        {
            this.ucQueryGoods.U_WindowName = this.GetType().Name;
            this.ucQueryGoods.U_XmlTableName = "GOODS_MAIN";
            this.ucQueryGoods.U_InitControl();
        }

        /// <summary>
        /// 计划单加载
        /// </summary>
        private void TemplateBind()
        {
            try
            {
                //获得计划实体
                SiaSun.LMS.Model.GOODS_TEMPLATE mGOODS_TEMPLATE = MainApp._I_GoodsService.GoodsTemplateGetModel(this._TEMPLATE_ID);
                //判断是否存在计划实例
                if (null == mGOODS_TEMPLATE)
                {
                    mGOODS_TEMPLATE = new SiaSun.LMS.Model.GOODS_TEMPLATE();

                }
                //初始化编辑控件
                this.ucEditTemplate.U_InitControl<Model.GOODS_TEMPLATE>(mGOODS_TEMPLATE == null, this.GetType().Name, "GOODS_TEMPLATE", mGOODS_TEMPLATE);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 加载物料信息
        /// </summary>
        private void GoodsBind(string QueryWhere)
        {
            try
            {
                this.gridGoods.U_WindowName = this.GetType().Name;
                this.gridGoods.U_TableName = "V_GOODS";
                this.gridGoods.U_XmlTableName = "GOODS_MAIN";
                this.gridGoods.U_OrderField = "GOODS_CODE";
                this.gridGoods.U_Where = string.Format("GOODS_CLASS_CODE!='zt' and {0}", string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere);
                this.gridGoods.U_AllowOperatData = false;
                this.gridGoods.U_AllowChecked = false;
                this.gridGoods.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void ucQueryGoods_U_Query(string QueryWhere)
        {
            this.GoodsBind(QueryWhere);
        }

        /// <summary>
        /// 加载计划单物料清单
        /// </summary>
        private void TemplateListBind()
        {
            try
            {
                this.gridTemplateList.U_Clear();

                this.gridTemplateList.U_WindowName = this.GetType().Name;
                this.gridTemplateList.U_XmlTableName = "V_GOODS_TEMPLATE_LIST";
                this.gridTemplateList.U_TableName = "V_GOODS_TEMPLATE_LIST";
                this.gridTemplateList.U_Where = string.Format("GOODS_TEMPLATE_ID ={0}", this._TEMPLATE_ID);

                this.gridTemplateList.U_SplitGroupColumn = "GOODS_TYPE_ID";
                this.gridTemplateList.U_SplitGroupHeader = "GOODS_TYPE_NAME";
                this.gridTemplateList.U_SplitPropertyType = "GOODS_TYPE";
                this.gridTemplateList.U_SplitPropertyColumn = "GOODS_PROPERTY";

                //this.gridTemplateList.U_TotalColumnName = "PLAN_LIST_QUANTITY";
                this.gridTemplateList.U_DefaultRowValues.Add("GOODS_TEMPLATE_ID", _TEMPLATE_ID.ToString());

                this.gridTemplateList.U_AllowShowPage = false;
                this.gridTemplateList.U_AllowChecked = false;
                this.gridTemplateList.U_AllowAdd = System.Windows.Visibility.Collapsed;
                this.gridTemplateList.U_AllowSave = System.Windows.Visibility.Collapsed;

                this.gridTemplateList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (gridGoods.gridApp.SelectedItem == null)
                return;

            //获得物料记录
            DataRowView rowViewGoods = this.gridGoods.gridApp.SelectedItem as DataRowView;

            //获得添加行
            DataTable tableTemplateList = this.TEMPLATE_LIST_Add_By_GOODS(_TEMPLATE_ID, new string[] { rowViewGoods["GOODS_ID"].ToString() });

            //添加行数据
            this.gridTemplateList.U_AddTabPageRows(tableTemplateList.Rows.Cast<DataRow>().ToArray());

            //注册事件
            Register_DataTable_Event();
        }


        /// <summary>
        /// 添加计划信息返回表单集合
        /// </summary>
        public DataTable TEMPLATE_LIST_Add_By_GOODS(int TEMPLATE_ID, IList<string> listGOODS_ID)
        {
            //创建集合
            string strSql = string.Format("SELECT * FROM V_GOODS_TEMPLATE_LIST WHERE GOODS_TEMPLATE_ID={0}", 0);
            using (DataTable tableTemplateList = MainApp._I_BaseService.GetList(strSql))
            {
                foreach (string strGoodsID in listGOODS_ID)
                {
                    //获得物料实体
                    SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN =MainApp._I_GoodsService.GoodsGetModelGoodsID(Convert.ToInt32(strGoodsID));

                    SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = MainApp._I_GoodsService.GoodsClassGetModelGoodsClassID(mGOODS_MAIN.GOODS_CLASS_ID);
                    
                    if (mGOODS_MAIN != null && mGOODS_CLASS != null)
                    {
                        DataRow rowTemplateList = tableTemplateList.NewRow();
                        rowTemplateList["GOODS_TEMPLATE_ID"] = TEMPLATE_ID;
                        rowTemplateList["GOODS_ID"] = mGOODS_MAIN.GOODS_ID;
                        rowTemplateList["GOODS_CODE"] = mGOODS_MAIN.GOODS_CODE;
                        rowTemplateList["GOODS_NAME"] = mGOODS_MAIN.GOODS_NAME;
                        rowTemplateList["GOODS_TYPE_ID"] = mGOODS_CLASS.GOODS_TYPE_ID;
                        rowTemplateList["GOODS_TEMPLATE_QUANTITY"] = 0;
                        tableTemplateList.Rows.Add(rowTemplateList);
                    }
                }
                return tableTemplateList;
            }
        }

        /// <summary>
        /// 注册表单事件
        /// </summary>
        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.gridTemplateList.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.RowChanged -= new DataRowChangeEventHandler(tableSource_RowChanged);
                    tableSource.RowChanged += new DataRowChangeEventHandler(tableSource_RowChanged);

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }

        /// <summary>
        /// 表单数据校验
        /// </summary>
        void tableSource_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            if (e.Row.RowState == DataRowState.Added || e.Row.RowState == DataRowState.Modified)
            {
                //判断数量
                if (e.Row.IsNull("GOODS_TEMPLATE_QUANTITY") || Convert.ToDecimal(e.Row["GOODS_TEMPLATE_QUANTITY"]) <= 0 || string.IsNullOrEmpty(e.Row["GOODS_TEMPLATE_QUANTITY"].ToString()))
                {
                    e.Row.RowError = string.Format("请检查数量是否合法!");
                }
      
            }
        }

        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "GOODS_TEMPLATE_QUANTITY":
                    bResult = (string.Empty != e.ProposedValue.ToString() && !string.IsNullOrEmpty(e.ProposedValue.ToString()) && Convert.ToInt32(e.ProposedValue) > 0);

                    sResult = string.Format("{0}{1}不能小于等于0!", e.Column.ColumnName, e.ProposedValue.ToString());
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                    }
                    break;
            }

           
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Border_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnSave":
                        this.SavePlanOrder();
                        break;
                    case "btnReset":
                        this.Refresh();
                        break;                        
                }
            }
        }

        /// <summary>
        /// 保存计划
        /// </summary>
        private void SavePlanOrder()
        {
            bool bResult = true;
            string sResult = string.Empty;

            //检查计划工单填写错误
            if (!this.ucEditTemplate.U_IsValidate(out sResult))
            {
                MainApp._MessageDialog.ShowException(sResult);
                return;
            }

            //结束编辑
            this.gridTemplateList.U_EndCurrentEdit();
            //检查计划清单
            using (DataTable tableSource = this.gridTemplateList.U_DataSource)
            {
                if (tableSource.HasErrors)
                {
                    MainApp._MessageDialog.ShowException(tableSource.GetErrors()[0].RowError);
                    return;
                }

                //获得计划实例
                SiaSun.LMS.Model.GOODS_TEMPLATE mGOODS_TEMPLATE = this.ucEditTemplate.DataContext as SiaSun.LMS.Model.GOODS_TEMPLATE;
                if (mGOODS_TEMPLATE != null)
                {
                    //判断拆分属性是否填写正确
                    //if (!this.gridTemplateList.U_Check_Split_Property("GOODS_PROPERTY",out sResult))
                    //{
                    //    MainApp._MessageDialog.ShowException(sResult);
                    //    return;
                    //}

                    //提示
                    if (MainApp._MessageDialog.ShowDialog("ConfirmCreatePlan",null) == Sid.Windows.Controls.TaskDialogResult.Ok)
                    {
                        try
                        {
                            bResult = MainApp._I_BaseService.Invoke("TemplateBase", "TemplateCreate", new object[] { mGOODS_TEMPLATE, tableSource, _TEMPLATE_ID }, out sResult);
                        }
                        catch (Exception ex)
                        {
                            bResult = false;
                            sResult = ex.Message;
                        }

                        if (bResult)
                        {
                            this.Title = string.Format("作业单-[{0}]", mGOODS_TEMPLATE.GOODS_TEMPLATE_CODE);
                        }
                        MainApp._MessageDialog.ShowResult(bResult, sResult);
                    }
                }
            }
        }

        /// <summary>
        /// 重置
        /// </summary>
        private void Refresh()
        {
            //加载计划信息
            this.TemplateBind();

            //加载计划清单
            this.TemplateListBind();
        }

    }
}
