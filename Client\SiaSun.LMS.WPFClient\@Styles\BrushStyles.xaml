﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--Control colors.-->
    <Color x:Key="ContentAreaColorLight">#FFC5CBF9</Color>
    <Color x:Key="ContentAreaColorDark">#FF7381F9</Color>

    <Color x:Key="DisabledControlDarkColor">#FFC5CBF9</Color>
    <Color x:Key="ControlLightColor">White</Color>
    <Color x:Key="ControlMediumColor">#FF7381F9</Color>

    <Color x:Key="ControlPressedColor">#FF211AA9</Color>

    <!--Border colors-->
    <Color x:Key="BorderLightColor">#FFCCCCCC</Color>
    <Color x:Key="BorderMediumColor">#FF888888</Color>
    <Color x:Key="BorderDarkColor">#FF444444</Color>
    <Color x:Key="DisabledBorderLightColor">#FFAAAAAA</Color>

    <!--ControlBackBrush-->
    <LinearGradientBrush x:Key="ControlBackBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="#FFCDDBF5" Offset="0.0"/>
                <GradientStop Color="White" Offset="0.5"/>
                <GradientStop Color="White" Offset="0.6"/>
                <GradientStop Color="#FFCDDBF5" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>
    
<!--PanelBackBrush-->
    <LinearGradientBrush x:Key="PanelBackBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="LightBlue" Offset="0.0"/>
                <GradientStop Color="White" Offset="0.6"/>
                <GradientStop Color="LightBlue" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <!--NormalBorderBrush-->
    <LinearGradientBrush x:Key="NormalBorderBrush" StartPoint="0,0" EndPoint="0,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="#CCC" Offset="0.0"/>
                <GradientStop Color="#444" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>    
    
    <!--默认鼠标滑过画刷-->
    <LinearGradientBrush x:Key="mouseOverDefaultBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFEDE47A" Offset="0.03"/>
        <GradientStop Color="White" Offset="1"/>
    </LinearGradientBrush>
    
    <!--默认Window背景画刷-->
    <LinearGradientBrush x:Key="windowBackgroundDefaultBrush" EndPoint="1.117,1.196" StartPoint="0.032,0.132">
        <GradientStop Color="#FF082E78"/>
        <GradientStop Color="White" Offset="1"/>
    </LinearGradientBrush>  
    
    <!--默认Menu画刷-->
    <LinearGradientBrush x:Key="menuBackgroundDefaultBrush" EndPoint="0,0" StartPoint="0,1">
        <GradientStop Color="#FF6993EB" Offset="0"/>
        <GradientStop Color="#FFCDDBF5" Offset="1"/>
    </LinearGradientBrush>

    <!--Menu Pup-->
    <LinearGradientBrush x:Key="menuItemPopupBrush"  EndPoint="0.5,1"  StartPoint="0.5,0">
        <GradientStop Color="{DynamicResource ControlLightColor}" Offset="0" />
        <GradientStop Color="{DynamicResource ControlMediumColor}" Offset="0.5" />
        <GradientStop Color="{DynamicResource ControlLightColor}"  Offset="1" />
    </LinearGradientBrush>
    
    <!--默认MenuItem画刷-->
    <LinearGradientBrush x:Key="menuItemBackgroundDefaultBrush" EndPoint="0,0" StartPoint="1,0">
        <GradientStop Color="CornflowerBlue" Offset="1"/>
        <GradientStop Color="White" Offset="0"/>
    </LinearGradientBrush>

    <!--默认StatusBar画刷-->
    <LinearGradientBrush x:Key="statusBarBackgroundDefaultBrush" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FF3A60AD" Offset="0.528"/>
        <GradientStop Color="#FFAFC2FB" Offset="0.01"/>
        <GradientStop Color="#FF202E7E" Offset="1"/>
    </LinearGradientBrush>

    <!--默认Button背景画刷-->
    <LinearGradientBrush x:Key="buttonBackgroundDefaultBrush" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="WhiteSmoke" Offset="0"/>
        <GradientStop Color="#FFCDDBF5" Offset="0.5"/>
        <GradientStop Color="#FF3A60AD" Offset="1"/>
    </LinearGradientBrush>
    
</ResourceDictionary>