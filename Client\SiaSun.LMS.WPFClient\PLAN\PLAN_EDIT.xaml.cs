﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.PLAN
{
    public partial class PLAN_EDIT : AvalonDock.DocumentContent
    {
        private int _PLAN_ID;

        public PLAN_EDIT()
        {
            InitializeComponent();

            this.ucQueryGoods.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQueryGoods_U_Query);
            
            this.gridGoods.gridApp.MouseDoubleClick += new MouseButtonEventHandler(gridApp_MouseDoubleClick);
        }

        public PLAN_EDIT(int PLAN_ID) : this()           
        {
            this._PLAN_ID = PLAN_ID;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = MainApp._I_PlanService.PlanGetModel(this._PLAN_ID);
            if (mPLAN_MAIN != null && mPLAN_MAIN.PLAN_FLAG=="1")
            {
                this.Close();
                return;
            }
            this.InitQueryControl();

            this.GoodsBind(string.Empty);


            this.PlanBind();

            this.PlanListBind();

            Register_DataTable_Event();
        }

        private void InitQueryControl()
        {
            this.ucQueryGoods.U_WindowName = this.GetType().Name;
            this.ucQueryGoods.U_XmlTableName = "GOODS_MAIN";
            this.ucQueryGoods.U_InitControl();
        }

        /// <summary>
        /// 计划单加载
        /// </summary>
        private void PlanBind()
        {
            try
            {
                //获得计划实体
                SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = MainApp._I_PlanService.PlanGetModel(this._PLAN_ID);
                //判断是否存在计划实例

                if (null == mPLAN_MAIN)
                {
                    mPLAN_MAIN = new SiaSun.LMS.Model.PLAN_MAIN();
                    mPLAN_MAIN.PLAN_CREATER = MainApp._USER.USER_NAME;
                    mPLAN_MAIN.PLAN_CREATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mPLAN_MAIN.PLAN_STATUS = SiaSun.LMS.Enum.PLAN_STATUS.Waiting.ToString();
                    mPLAN_MAIN.PLAN_FLAG = "0";
                    mPLAN_MAIN.PLAN_INOUT_STATION = "";
                }
                //初始化编辑控件
                this.ucEditPlan.U_InitControl<Model.PLAN_MAIN>(mPLAN_MAIN==null, this.GetType().Name, "PLAN_MAIN", mPLAN_MAIN);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 加载物料信息
        /// </summary>
        private void GoodsBind(string QueryWhere)
        {
            try
            {
                this.gridGoods.U_WindowName = this.GetType().Name;
                this.gridGoods.U_TableName = "V_GOODS";
                this.gridGoods.U_XmlTableName = "GOODS_MAIN";
                this.gridGoods.U_OrderField = "GOODS_ORDER";
                this.gridGoods.U_Where = string.Format("GOODS_CLASS_CODE!='zt' and {0}", string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere);
                this.gridGoods.U_AllowOperatData = false;
                this.gridGoods.U_AllowChecked = false;
                this.gridGoods.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void ucQueryGoods_U_Query(string QueryWhere)
        {
            this.GoodsBind(QueryWhere);
        }

        /// <summary>
        /// 加载计划单物料清单
        /// </summary>
        private void PlanListBind()
        {
            try
            {
                this.gridPlanList.U_Clear();

                this.gridPlanList.U_WindowName = this.GetType().Name;
                this.gridPlanList.U_XmlTableName = "V_PLAN_LIST";
                this.gridPlanList.U_TableName = "V_PLAN_LIST";
                this.gridPlanList.U_Where = string.Format("PLAN_ID ={0}", this._PLAN_ID);

                this.gridPlanList.U_SplitGroupColumn = "GOODS_TYPE_ID";
                this.gridPlanList.U_SplitGroupHeader = "GOODS_TYPE_NAME";
                this.gridPlanList.U_SplitPropertyType = "GOODS_TYPE";
                this.gridPlanList.U_SplitPropertyColumn = "GOODS_PROPERTY";

                this.gridPlanList.U_TotalColumnName = "PLAN_LIST_QUANTITY";
                this.gridPlanList.U_DefaultRowValues.Add("PLAN_ID", _PLAN_ID.ToString());

                this.gridPlanList.U_AllowShowPage = false;
                this.gridPlanList.U_AllowChecked = false;
                this.gridPlanList.U_AllowAdd = System.Windows.Visibility.Collapsed;
                this.gridPlanList.U_AllowSave = System.Windows.Visibility.Collapsed; 

                this.gridPlanList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (gridGoods.gridApp.SelectedItem == null)
                return;

            //获得物料记录
            DataRowView rowViewGoods = this.gridGoods.gridApp.SelectedItem as DataRowView;

            //获得添加行
            DataTable tablePlanList = this.PLAN_LIST_Add_By_GOODS(_PLAN_ID, new string[] { rowViewGoods["GOODS_ID"].ToString() });

            //添加行数据
            this.gridPlanList.U_AddTabPageRows(tablePlanList.Rows.Cast<DataRow>().ToArray());

            //注册事件
            Register_DataTable_Event();
        }


        /// <summary>
        /// 添加计划信息返回表单集合
        /// </summary>
        public DataTable PLAN_LIST_Add_By_GOODS(int PLAN_ID, IList<string> listGOODS_ID)
        {
            //创建集合
            string strSql = string.Format("SELECT * FROM V_PLAN_LIST WHERE PLAN_ID={0}", 0);
            using (DataTable tablePlanList = MainApp._I_BaseService.GetList(strSql))
            {
                foreach (string strGoodsID in listGOODS_ID)
                {
                    //获得物料实体
                    SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN =MainApp._I_GoodsService.GoodsGetModelGoodsID(Convert.ToInt32(strGoodsID));

                    SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = MainApp._I_GoodsService.GoodsClassGetModelGoodsClassID(mGOODS_MAIN.GOODS_CLASS_ID);
                    
                    if (mGOODS_MAIN != null && mGOODS_CLASS != null)
                    {
                        DataRow rowPlanList = tablePlanList.NewRow();
                        rowPlanList["PLAN_ID"] = PLAN_ID;
                        rowPlanList["GOODS_ID"] = mGOODS_MAIN.GOODS_ID;
                        rowPlanList["GOODS_CODE"] = mGOODS_MAIN.GOODS_CODE;
                        rowPlanList["GOODS_NAME"] = mGOODS_MAIN.GOODS_NAME;
                        rowPlanList["GOODS_TYPE_ID"] = mGOODS_CLASS.GOODS_TYPE_ID;

                        rowPlanList["GOODS_PROPERTY1"] = mGOODS_MAIN.GOODS_CONST_PROPERTY1;
                        rowPlanList["GOODS_PROPERTY2"] = mGOODS_MAIN.GOODS_CONST_PROPERTY2;
                        rowPlanList["GOODS_PROPERTY3"] = mGOODS_MAIN.GOODS_CONST_PROPERTY3;
                        rowPlanList["GOODS_PROPERTY4"] = mGOODS_MAIN.GOODS_CONST_PROPERTY4;
                        rowPlanList["GOODS_PROPERTY5"] = mGOODS_MAIN.GOODS_CONST_PROPERTY5;
                        rowPlanList["GOODS_PROPERTY6"] = mGOODS_MAIN.GOODS_CONST_PROPERTY6;
                        rowPlanList["GOODS_PROPERTY7"] = mGOODS_MAIN.GOODS_CONST_PROPERTY7;
                        rowPlanList["GOODS_PROPERTY8"] = mGOODS_MAIN.GOODS_CONST_PROPERTY8;

                        rowPlanList["GOODS_UNITS"] = mGOODS_MAIN.GOODS_UNITS;
                        rowPlanList["GOODS_TYPE_ID"] = mGOODS_CLASS.GOODS_TYPE_ID;

                        rowPlanList["PLAN_LIST_QUANTITY"] = 0;
                        rowPlanList["PLAN_LIST_ORDERED_QUANTITY"] = 0;
                        rowPlanList["PLAN_LIST_FINISHED_QUANTITY"] = 0;
                        tablePlanList.Rows.Add(rowPlanList);
                    }
                }
                return tablePlanList;
            }
        }

        /// <summary>
        /// 注册表单事件
        /// </summary>
        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.gridPlanList.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.RowChanged -= new DataRowChangeEventHandler(tableSource_RowChanged);
                    tableSource.RowChanged += new DataRowChangeEventHandler(tableSource_RowChanged);

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }

        /// <summary>
        /// 表单数据校验
        /// </summary>
        void tableSource_RowChanged(object sender, DataRowChangeEventArgs e)
        {
            if (e.Row.RowState == DataRowState.Added || e.Row.RowState == DataRowState.Modified)
            {
                //判断数量
                if (e.Row.IsNull("PLAN_LIST_QUANTITY") || Convert.ToDecimal(e.Row["PLAN_LIST_QUANTITY"]) <= 0 || string.IsNullOrEmpty(e.Row["PLAN_LIST_QUANTITY"].ToString()))
                {
                    e.Row.RowError = string.Format("请检查数量是否合法!");
                }
      
            }
        }

        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "PLAN_LIST_QUANTITY":
                    bResult = (string.Empty != e.ProposedValue.ToString() && !string.IsNullOrEmpty(e.ProposedValue.ToString()) && Convert.ToInt32(e.ProposedValue) > 0);

                    sResult = string.Format("{0}{1}不能小于等于0!", e.Column.ColumnName, e.ProposedValue.ToString());
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                    }
                    break;
            }

           
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Border_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnSave":
                        this.SavePlanOrder();
                        break;
                    case "btnReset":
                        this.Refresh();
                        break;                        
                }
            }
        }

        /// <summary>
        /// 保存计划
        /// </summary>
        private void SavePlanOrder()
        {
            bool bResult = true;
            string sResult = string.Empty;
            int PlAN_ID = 0;

            //检查计划工单填写错误
            if (!this.ucEditPlan.U_IsValidate(out sResult))
            {
                MainApp._MessageDialog.ShowException(sResult);
                return;
            }

            //结束编辑
            this.gridPlanList.U_EndCurrentEdit();
            //检查计划清单
            using (DataTable tableSource = this.gridPlanList.U_DataSource)
            {
                if (tableSource.HasErrors)
                {
                    MainApp._MessageDialog.ShowException(tableSource.GetErrors()[0].RowError);
                    return;
                }

                //获得计划实例
                SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this.ucEditPlan.DataContext as SiaSun.LMS.Model.PLAN_MAIN;
                if (mPLAN_MAIN != null)
                {
                    if(mPLAN_MAIN.PLAN_CODE==null || string.IsNullOrEmpty(mPLAN_MAIN.PLAN_CODE))
                    {
                        mPLAN_MAIN.PLAN_CODE = string.Format("Local{0}", DateTime.Now.ToString("yyyyMMddHHmmss"));
                    }

                    //判断拆分属性是否填写正确
                    if (!this.gridPlanList.U_Check_Split_Property("GOODS_PROPERTY",out sResult))
                    {
                        MainApp._MessageDialog.ShowException(sResult);
                        return;
                    }

                    object[] outParams = new object[] { };
                    //提示
                    if (MainApp._MessageDialog.ShowDialog("ConfirmCreatePlan",null) == Sid.Windows.Controls.TaskDialogResult.Ok)
                    {
                        try
                        {
                            //创建单据
                            mPLAN_MAIN.PLAN_FLAG = Enum.TASK_SOURCE.WES.ToString();
                            mPLAN_MAIN.PLAN_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];

                            bResult = MainApp._I_BaseService.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, tableSource, _PLAN_ID, sResult }, out outParams);
                        }
                        catch (Exception ex)
                        {
                            bResult = false;
                            sResult = ex.Message;
                        }

                        if (bResult)
                        {
                            this._PLAN_ID = Convert.ToInt32( outParams[2]);
                            this.PlanBind();
                            this.PlanListBind();
                            this.Title = string.Format("计划单-[{0}]", _PLAN_ID.ToString());
                        }
                        MainApp._MessageDialog.ShowResult(bResult, sResult);
                    }
                }
            }
        }

        /// <summary>
        /// 重置
        /// </summary>
        private void Refresh()
        {
            //加载计划信息
            this.PlanBind();

            //加载计划清单
            this.PlanListBind();
        }

    }
}
