﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Reflection;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    public class ManageBase:S_BaseService
    {
        private static readonly object lockObj = new object();
        
        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans">是否独立事务</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            bResult = null != mMANAGE_MAIN;

            if (!bResult)
            {
                bResult = false;

                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());

                return bResult;
            }

            bResult =  this.ManageComplete(mMANAGE_MAIN, bTrans, out sResult);

            return bResult;
        }

        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans">是否独立事务</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageComplete(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
               
                this._P_Base_House.BeginTransaction(bTrans);


                mMANAGE_MAIN.MANAGE_END_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);

                bResult = this._S_ManageService.RecordCreate(mMANAGE_MAIN.MANAGE_ID, out sResult);

                if (!bResult)
                {
                    sResult = string.Format("生成出入库记录错误_{0}", sResult);

                    return bResult;
                }

                

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    //this._S_LEDService.ledMessageCreate(mMANAGE_MAIN);

                    this._P_Base_House.CommitTransaction(bTrans);

                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        /// <summary>管理-异常
        /// 管理-异常
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageException(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;

                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());

                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);


                this._P_Base_House.BeginTransaction();

                

                if (mWH_CELL_START != null && mWH_CELL_START.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                {
                    bResult = this._S_CellService.CellUpdateStatus(mWH_CELL_START.CELL_ID, Enum.CELL_STATUS.Exception.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新起始位置{0} 状态错误\n", mWH_CELL_START.CELL_CODE.ToString());

                        this._P_Base_House.RollBackTransaction();


                        return bResult;
                    }
                }

                if (mWH_CELL_END != null && mWH_CELL_END.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                {
                    bResult = this._S_CellService.CellUpdateStatus(mWH_CELL_END.CELL_ID, Enum.CELL_STATUS.Exception.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新目标位置{0} 状态错误\n", mWH_CELL_START.CELL_CODE.ToString());

                        this._P_Base_House.RollBackTransaction();


                        return bResult;
                    }
                }


                SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(mMANAGE_MAIN.MANAGE_ID);

                if (null == mIO_CONTROL)
                {
                    sResult = string.Format("未能找到管理任务索引{0}的控制任务", MANAGE_ID.ToString());

                    this._P_Base_House.RollBackTransaction();

                    return bResult;
                }

                mMANAGE_MAIN.MANAGE_REMARK = mIO_CONTROL.ERROR_TEXT;

                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.ExceptionComplete.ToString();

                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);

                this._P_IO_CONTROL.DeleteManageID(mMANAGE_MAIN.MANAGE_ID);

                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>
        /// 管理-故障
        /// </summary>
        /// <param name="dt"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageError(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());
                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction();

                SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(mMANAGE_MAIN.MANAGE_ID);

                if (null == mIO_CONTROL)
                {
                    bResult = false;
                    sResult = string.Format("未能找到管理任务索引{0}的控制任务", MANAGE_ID.ToString());
                    this._P_Base_House.RollBackTransaction();
                    return bResult;
                }

                mMANAGE_MAIN.MANAGE_REMARK = mIO_CONTROL.ERROR_TEXT;
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString() ;

                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this._P_Base_House.RollBackTransaction();
            }

            return bResult;
        }

        /// <summary>管理-取消
        /// 管理-取消
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageCancel(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            bool updateFlag = true;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());
                return bResult;
            }
            IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);
            if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count==0)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务列表 任务ID{0}", MANAGE_ID.ToString());
                return bResult;
            }

            ////wdz add 2018-01-13
            //if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString())
            //{
            //    bResult = false;
            //    sResult = string.Format("WMS直接下达的任务目前不可以取消", MANAGE_ID.ToString());
            //    return bResult;
            //}

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
            if (null == mMANAGE_TYPE)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                return bResult;
            }

            SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(MANAGE_ID);
            if (null != mIO_CONTROL && !mIO_CONTROL.CONTROL_STATUS.Equals(900))
            {
                bResult = false;
                sResult = string.Format("存在控制任务{0}，先处理控制任务", mIO_CONTROL.CONTROL_ID);
                return bResult;
            }

            DataTable dtRelateIoControl = this.GetList(string.Format("select 0 from IO_CONTROL where STOCK_BARCODE ='{0}' and CONTROL_STATUS != 900 ",mMANAGE_MAIN.STOCK_BARCODE));
            if(dtRelateIoControl.Rows.Count!=0)
            {
                bResult = false;
                sResult = string.Format("条码{0}存在控制任务，先处理控制任务", mMANAGE_MAIN.STOCK_BARCODE);
                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction();

                bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(), out sResult);
                if (!bResult)
                {
                    bResult = false;
                    sResult = string.Format("更新起始货位{0}状态错误\n{1}", mMANAGE_MAIN.START_CELL_ID.ToString(), sResult);
                    this._P_Base_House.RollBackTransaction();
                    return bResult;
                }

                bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(), out sResult);

                if (!bResult)
                {
                    bResult = false;
                    sResult = string.Format("更新终止货位{0}状态错误\n{1}", mMANAGE_MAIN.END_CELL_ID.ToString(), sResult);
                    this._P_Base_House.RollBackTransaction();
                    return bResult;
                }

                if (mMANAGE_TYPE.MANAGE_TYPE_GROUP != "3" && mMANAGE_TYPE.MANAGE_TYPE_CODE!=Enum.MANAGE_TYPE.ManageKitUp.ToString())
                {
                    //wdz add 2017-12-25
                    Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

                    foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                    {
                        SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mPLAN_MAIN != null && mPLAN_LIST != null && mPLAN_MAIN.PLAN_ID == mPLAN_LIST.PLAN_ID && (updateFlag) &&
                            (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString() ||
                             //mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString()||
                             mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString()))
                        {
                            //盘点、整理计划取消任务时计划列表-1
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY--;
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                            updateFlag = false;
                        }
                        else if (mPLAN_MAIN != null && mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString())
                        {
                            //盘点
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY--;
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                        }
                        else if (null != mPLAN_LIST && mPLAN_LIST.GOODS_ID == mMANAGE_LIST.GOODS_ID)
                        {
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                        }
                    }
                }

                //wdz add 2018-01-13 WMS任务取消后通知WMS任务失败
                string isNoticeWms = string.Empty;
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                    {
                        //非立库输送任务
                        bResult = new Interface.noneMiniloadTranResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, lsMANAGE_LIST[0].GOODS_PROPERTY1, lsMANAGE_LIST[0].GOODS_PROPERTY2, lsMANAGE_LIST[0].GOODS_PROPERTY3, lsMANAGE_LIST[0].GOODS_PROPERTY4, false, " 任务已删除",string.Empty, out sResult);
                    }
                    else if(mMANAGE_MAIN.PLAN_ID==0)
                    {
                        //除 非立库输送任务 外的其他没有计划的任务
                        bResult = new Interface.handleResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, false, " 任务已删除", out sResult);
                    }

                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                this._P_IO_CONTROL.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_DETAIL.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_LIST.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_MAIN.Delete(MANAGE_ID);

                if (bResult)
                {
                    //this._S_LEDService.ledMessageCreate(mMANAGE_MAIN);
                }              

                this._P_Base_House.CommitTransaction(); 
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this._P_Base_House.RollBackTransaction();
            }
            finally
            {
                this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("ManageBase.Cancel():任务取消_计划ID[{0}]_任务ID[{1}]_任务类型[{2}]_起始位置ID[{3}]_终点位置ID[{4}]_托盘条码[{5}]", mMANAGE_MAIN.PLAN_ID, mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE));
            }

            return bResult;
        }

        /// <summary>管理-取消
        /// 管理-取消
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageCancel_NoTrans(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            bool updateFlag = true;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());
                return bResult;
            }
            IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);
            if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务列表 任务ID{0}", MANAGE_ID.ToString());
                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
            if (null == mMANAGE_TYPE)
            {
                bResult = false;
                sResult = string.Format("未能找到管理任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                return bResult;
            }

            SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(MANAGE_ID);
            if (null != mIO_CONTROL && !mIO_CONTROL.CONTROL_STATUS.Equals(900))
            {
                bResult = false;
                sResult = string.Format("存在控制任务{0}，先处理控制任务", mIO_CONTROL.CONTROL_ID);
                return bResult;
            }

            DataTable dtRelateIoControl = this.GetList(string.Format("select 0 from IO_CONTROL where STOCK_BARCODE ='{0}' and CONTROL_STATUS != 900 ", mMANAGE_MAIN.STOCK_BARCODE));
            if (dtRelateIoControl.Rows.Count != 0)
            {
                bResult = false;
                sResult = string.Format("条码{0}存在控制任务，先处理控制任务", mMANAGE_MAIN.STOCK_BARCODE);
                return bResult;
            }

            try
            {
                bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(), out sResult);
                if (!bResult)
                {
                    bResult = false;
                    sResult = string.Format("更新起始货位{0}状态错误\n{1}", mMANAGE_MAIN.START_CELL_ID.ToString(), sResult);
                    return bResult;
                }

                bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(), out sResult);

                if (!bResult)
                {
                    bResult = false;
                    sResult = string.Format("更新终止货位{0}状态错误\n{1}", mMANAGE_MAIN.END_CELL_ID.ToString(), sResult);
                    return bResult;
                }

                if (mMANAGE_TYPE.MANAGE_TYPE_GROUP != "3" && mMANAGE_TYPE.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManageKitUp.ToString())
                {
                    //wdz add 2017-12-25
                    Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

                    foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                    {
                        SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mPLAN_MAIN != null && mPLAN_LIST != null && mPLAN_MAIN.PLAN_ID == mPLAN_LIST.PLAN_ID && (updateFlag) &&
                            (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString() ||
                             //mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString()||
                             mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString()))
                        {
                            //盘点、整理计划取消任务时计划列表-1
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY--;
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                            updateFlag = false;
                        }
                        else if (mPLAN_MAIN != null && mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString())
                        {
                            //盘点
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY--;
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                        }
                        else if (null != mPLAN_LIST && mPLAN_LIST.GOODS_ID == mMANAGE_LIST.GOODS_ID)
                        {
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                        }
                    }
                }

                //wdz add 2018-01-13 WMS任务取消后通知WMS任务失败
                string isNoticeWms = string.Empty;
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    if (mMANAGE_MAIN.MANAGE_TYPE_CODE == Enum.MANAGE_TYPE.ManageTrans.ToString())
                    {
                        //非立库输送任务
                        bResult = new Interface.noneMiniloadTranResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, lsMANAGE_LIST[0].GOODS_PROPERTY1, lsMANAGE_LIST[0].GOODS_PROPERTY2, lsMANAGE_LIST[0].GOODS_PROPERTY3, lsMANAGE_LIST[0].GOODS_PROPERTY4, false, " 任务已删除", string.Empty, out sResult);
                    }
                    else if (mMANAGE_MAIN.PLAN_ID == 0)
                    {
                        //除 非立库输送任务 外的其他没有计划的任务
                        bResult = new Interface.handleResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, false, " 任务已删除", out sResult);
                    }

                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                this._P_IO_CONTROL.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_DETAIL.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_LIST.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_MAIN.Delete(MANAGE_ID);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("ManageBase.Cancel():任务取消_计划ID[{0}]_任务ID[{1}]_任务类型[{2}]_起始位置ID[{3}]_终点位置ID[{4}]_托盘条码[{5}]", mMANAGE_MAIN.PLAN_ID, mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID, mMANAGE_MAIN.STOCK_BARCODE));
            }

            return bResult;
        }

        /// <summary>管理-执行
        /// 管理-执行
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageExecute(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;

                sResult = string.Format("未能找到管理任务索引{0}", MANAGE_ID.ToString());

                return bResult;
            }

            ////ywz add 2019-05-23
            //if (mMANAGE_MAIN.MANAGE_STATUS == Enum.MANAGE_STATUS.Executing.ToString())
            //{
            //    return bResult;
            //}

            try
            {
                this._P_Base_House.BeginTransaction();

                mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                //ywz add 2019-05-17
                string confirmTime = mMANAGE_MAIN.MANAGE_CONFIRM_TIME + ";" + Common.StringUtil.GetDateTime();
                mMANAGE_MAIN.MANAGE_CONFIRM_TIME = confirmTime.Length > 100 ? confirmTime.Substring(0, 100) : confirmTime;

                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Executing.ToString();

                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);

                if (!bResult)
                {
                    sResult = string.Format("任务状态更新错误\n{0}", sResult);

                    this._P_Base_House.RollBackTransaction();

                    return bResult;
                }

                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }
            finally
            {
                if (bResult)
                {
                    //this._S_LEDService.ledMessageCreate(mMANAGE_MAIN);
                    
                }
            }

            //this._log.Debug(string.Format("任务执行处理完毕{0} {1}", MethodBase.GetCurrentMethod().Name, MANAGE_ID));

            return bResult;
        }

        /// <summary>管理-执行
        /// 管理-执行
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageWaitConfirm(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            return bResult;
        }

        /// <summary>管理-执行
        /// 管理-执行
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageConfirm(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
            }
            catch(Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManageBase.ManageConfirm:程序异常 {0}", ex.Message);
            }
            finally
            {

            }
            
            return bResult;
        }


        /// <summary>管理-下达 控制是否独立事务
        /// 管理-下达 控制是否独立事务
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageDownLoad(int MANAGE_ID,string START_CELL_CODE, bool bTrans,bool wsNoticeControl ,out int controlId, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            controlId = 0;

            int END_CELL_ID =0;
            SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = null;
            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                return bResult;
            }

            SiaSun.LMS.Model.WH_CELL mSTART_CELL = null;

            if (START_CELL_CODE == string.Empty)
            {
                mSTART_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                if (null == mSTART_CELL)
                {
                    bResult = false;
                    sResult = string.Format("未能找到起始位置{0}", mSTART_CELL.CELL_CODE);
                    return bResult;
                }
            }
            else
            {
                mSTART_CELL = this._P_WH_CELL.GetModel(START_CELL_CODE);

                if (null == mSTART_CELL)
                {
                    bResult = false;
                    sResult = string.Format("未能找到起始位置{0}", mSTART_CELL.CELL_CODE);
                    return bResult;
                }
            }

            //wdz add 2018-05-20
            lock (lockObj)
            {
                try
                {
                    this._P_Base_House.BeginTransaction(bTrans);

                    #region 检查双深是否需要倒库，需要的话生成倒库任务
                    //起始货位是3期立库区
                    //if (4 == mSTART_CELL.AREA_ID && mSTART_CELL.CELL_TYPE.Equals(Enum.CELL_TYPE.Cell.ToString()))
                    //{
                    //    int normalCellID = 0;
                    //    string normalStockBarcode;
                    //    STORAGE_MAIN mNORMAL_STORAGE_MAIN = null;
                    //    IList<STORAGE_LIST> ls_NORMAL_STORAGE_LIST = null;
                    //    bResult = this._S_CellService.CheckNormalMove(mSTART_CELL.CELL_ID, out normalCellID, out normalStockBarcode, out mNORMAL_STORAGE_MAIN, out ls_NORMAL_STORAGE_LIST, out sResult);
                    //    var normalCell = this._P_WH_CELL.GetModel(normalCellID);
                    //    if (bResult)
                    //    {

                    //        if (normalCell == null)
                    //        {
                    //            bResult = false;
                    //            sResult = string.Format("ManageBase.ManageDownload:创建倒库任务时未能找到[{0}]相邻货位的货位信息_箱条码[{1}]", mSTART_CELL.CELL_CODE, normalStockBarcode);
                    //            return bResult;
                    //        }

                    //        if (mNORMAL_STORAGE_MAIN == null || ls_NORMAL_STORAGE_LIST == null)
                    //        {
                    //            bResult = false;
                    //            sResult = string.Format("ManageBase.ManageDownload:创建倒库任务时未能找到[{0}]相邻货位的库存信息_箱条码[{1}]", mSTART_CELL.CELL_CODE, normalStockBarcode);
                    //            return bResult;
                    //        }

                    //        //需要倒库，构建倒库任务
                    //        var manageMainMove = new Model.MANAGE_MAIN()
                    //        {
                    //            CROSS_FLAG = "0",
                    //            FULL_FLAG = "0",
                    //            GOODS_TEMPLATE_ID = 0,
                    //            MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                    //            MANAGE_CONFIRM_TIME = "",
                    //            MANAGE_END_TIME = "",
                    //            MANAGE_ID = 0,
                    //            MANAGE_LEVEL = "9",
                    //            MANAGE_OPERATOR = "自动倒库",
                    //            MANAGE_RELATE_CODE = "",
                    //            MANAGE_REMARK = $"{mMANAGE_MAIN.STOCK_BARCODE}出库产生的倒库任务",
                    //            MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString(),
                    //            MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString(),
                    //            MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageMove.ToString(),
                    //            PLAN_ID = 0,
                    //            PLAN_TYPE_CODE = "",
                    //            START_CELL_ID = normalCellID,
                    //            STOCK_BARCODE = normalStockBarcode,
                    //            CELL_MODEL = mNORMAL_STORAGE_MAIN.CELL_MODEL,
                    //            END_CELL_ID = 0
                    //        };

                    //        var manageListsMove = new List<Model.MANAGE_LIST>();

                    //        foreach (var item in ls_NORMAL_STORAGE_LIST)
                    //        {
                    //            manageListsMove.Add(new Model.MANAGE_LIST()
                    //            {
                    //                BOX_BARCODE = item.BOX_BARCODE,
                    //                DETAIL_FLAG = "0",
                    //                MANAGE_ID = 0,
                    //                MANAGE_LIST_ID = 0,
                    //                MANAGE_LIST_QUANTITY = item.STORAGE_LIST_QUANTITY,
                    //                MANAGE_LIST_REMARK = "",
                    //                PLAN_LIST_ID = 0,
                    //                STORAGE_LIST_ID = item.STORAGE_LIST_ID,
                    //                STORAGE_LOCK_ID = 0,
                    //                GOODS_ID = item.GOODS_ID,
                    //                GOODS_PROPERTY1 = item.GOODS_PROPERTY1,
                    //                GOODS_PROPERTY2 = item.GOODS_PROPERTY2,
                    //                GOODS_PROPERTY3 = item.GOODS_PROPERTY3,
                    //                GOODS_PROPERTY4 = item.GOODS_PROPERTY4,
                    //                GOODS_PROPERTY5 = item.GOODS_PROPERTY5,
                    //                GOODS_PROPERTY6 = item.GOODS_PROPERTY6,
                    //                GOODS_PROPERTY7 = item.GOODS_PROPERTY7,
                    //                GOODS_PROPERTY8 = item.GOODS_PROPERTY8
                    //            });
                    //        }
                    //        //使用双伸货位分配方法分配一个空闲货位
                    //        int newEndCellId = 0;
                    //        bResult = this._S_CellService._S_CellService.CellInAllocate(
                    //            normalCell.WAREHOUSE_ID,
                    //            normalCell.AREA_ID,
                    //            ls_NORMAL_STORAGE_LIST[0].GOODS_PROPERTY6,
                    //            ls_NORMAL_STORAGE_LIST[0].GOODS_PROPERTY7,
                    //            ls_NORMAL_STORAGE_LIST[0].GOODS_PROPERTY8,
                    //            out newEndCellId,
                    //            out sResult);

                    //        if (!bResult || newEndCellId <= 0)
                    //        {
                    //            sResult = string.Format("ManageBase.ManageDownload:创建倒库任务时双伸货位外侧库存[{0}]未能分配新货位_信息[{1}]", mNORMAL_STORAGE_MAIN.STOCK_BARCODE, sResult);
                    //            return bResult;
                    //        }
                    //        manageMainMove.END_CELL_ID = newEndCellId;

                    //        bResult = new ManageMove().ManageCreate(manageMainMove, manageListsMove, false, true, false, true, out sResult);
                    //        if (!bResult)
                    //        {
                    //            sResult = string.Format("ManageBase.ManageDownload:双伸货位外侧库存[{0}]倒库时下达任务失败_信息[{1}]", mNORMAL_STORAGE_MAIN.STOCK_BARCODE, sResult);
                    //            return bResult;
                    //        }

                    //    }
                    //    //外层无库存有任务，即有入库任务，内层无法出库
                    //    else if (mNORMAL_STORAGE_MAIN == null && normalCell != null && normalCell.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                    //    {
                    //        bResult = false;
                    //        sResult = string.Format("ManageBase.ManageDownload:无法出库_齐套箱[{0}]在双伸货位内侧_并且外侧货位[{1}]有入库任务", mNORMAL_STORAGE_MAIN.STOCK_BARCODE, normalCell.CELL_CODE);
                    //        return bResult;
                    //    }
                    //}
                    #endregion

                    if (mMANAGE_MAIN.END_CELL_ID.Equals(0))
                    {
                        //出库任务
                        if (mSTART_CELL.CELL_TYPE == Enum.CELL_TYPE.Cell.ToString())
                        {
                            
                            

                            bResult = this._S_CellService.CellOutAllocate(mSTART_CELL.CELL_CODE, out END_CELL_ID, out sResult);
                            if (!bResult)
                            {
                                sResult = string.Format("未找到出库站台\n {0}", sResult);
                                return bResult;
                            }
                        }
                        //入库任务
                        else
                        {
                            //@mxh 判断起始点所属仓库,由三楼立体库入库申请站台发起的manageUp任务，调用双深货位分配方法。
                            #region 获取三楼立体库Warehouse_ID
                            int floor3WarehouseID;
                            string paraKey = string.Format("Floor3WarehouseID");
                            DataTable dtPara = this.GetList(string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY='{0}' and PARAMETER_FLAG='1'", paraKey));
                            if (dtPara != null && dtPara.Rows.Count == 1)
                            {
                                floor3WarehouseID = Convert.ToInt32(dtPara.Rows[0]["PARAMETER_VALUE"].ToString());
                            }
                            else
                            {
                                bResult = false;
                                sResult = string.Format("Managebase.cs-入库货位分配算法区分:未能获取数据 key-{0}", paraKey);
                                return bResult;
                            }
                            #endregion

                            if (mSTART_CELL.WAREHOUSE_ID == floor3WarehouseID)
                            {
                                #region 获取Manage任务中物料的任务号、项目号、WBS号
                                

                                IList<MANAGE_LIST> lsMANAGE_LIST = _P_MANAGE_LIST.GetListManageID(MANAGE_ID);
                                MANAGE_LIST mMANAGE_LIST = lsMANAGE_LIST[0];
                                string GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                                string GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                                string GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                                //bool hasStorage = false;

                                //STORAGE_MAIN mSTORAGE_MAIN = _P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                                //if (mSTORAGE_MAIN != null)
                                //{
                                //    hasStorage = true;
                                //}
                                #endregion
                                
                                //选取双深入库货位
                                //bResult = this._S_CellService.CellInAllocate(mSTART_CELL.WAREHOUSE_ID,
                                //                                            mMANAGE_MAIN.START_CELL_ID,
                                //                                            GOODS_PROPERTY6,
                                //                                            GOODS_PROPERTY7,
                                //                                            GOODS_PROPERTY8,
                                //                                            out END_CELL_ID,
                                //                                            out sResult);
                                bResult = this._S_CellService.CellInAllocate_ThirdFloor(mSTART_CELL.WAREHOUSE_ID, mMANAGE_MAIN.START_CELL_ID, GOODS_PROPERTY6, GOODS_PROPERTY7, GOODS_PROPERTY8, out END_CELL_ID, out sResult);
                            }
                            else
                            {
                                #region 原调用货位分配代码块
                                //wdz add 2019-08-07 14:52:39
                                //根据配置使用新逻辑做货位分配
                                DataTable dtMaxGoodsScore = this.GetList(string.Format("select max(GOODS_MAIN.GOODS_CONST_PROPERTY3) as MAX_GOODS_SCORE from MANAGE_LIST left join GOODS_MAIN on MANAGE_LIST.GOODS_ID = GOODS_MAIN.GOODS_ID where MANAGE_LIST.MANAGE_ID = {0}", mMANAGE_MAIN.MANAGE_ID));
                                double maxGoodsScore = 0.0;
                                string intelligentStation = string.Empty;
                                if (this._S_SystemService.GetSysParameter("IntelligentAllcateCellStations", out intelligentStation) &&
                                    !string.IsNullOrEmpty(intelligentStation) && intelligentStation.Split(',').Contains(mSTART_CELL.CELL_CODE) &&
                                    dtMaxGoodsScore != null && dtMaxGoodsScore.Rows.Count > 0 &&
                                    double.TryParse(dtMaxGoodsScore.Rows[0]["MAX_GOODS_SCORE"].ToString(), out maxGoodsScore) &&
                                    maxGoodsScore > 0)
                                {
                                    bResult = this._S_CellService.CellInAllocate(mSTART_CELL.CELL_CODE, maxGoodsScore, string.Empty, out END_CELL_ID, out sResult);
                                }
                                else
                                {
                                    bResult = this._S_CellService.CellInAllocate(mSTART_CELL.CELL_CODE, mMANAGE_MAIN.CELL_MODEL, mMANAGE_MAIN.MANAGE_ID, 1, out END_CELL_ID, out sResult);

                                }
                                #endregion
                            }

                            if (!bResult)
                            {
                                sResult = string.Format("分配货位失败\n {0}", sResult);
                                return bResult;
                            }
                        }

                        mMANAGE_MAIN.START_CELL_ID = mSTART_CELL.CELL_ID;
                        mMANAGE_MAIN.END_CELL_ID = END_CELL_ID;
                    }
                    else
                    {
                        Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                        if (mWH_CELL_END == null)
                        {
                            bResult = false;
                            sResult = string.Format("未能获取用户选定的货位 ID{0}_ManageDownLoad", mMANAGE_MAIN.END_CELL_ID);
                            return bResult;
                        }
                        //if (mMANAGE_MAIN.CELL_MODEL == "Height" && mWH_CELL_END.CELL_MODEL == "Height%")
                        //{
                        //    bResult = false;
                        //    sResult = string.Format("用户选定货位时将高尺寸物料分配至标准货位_ManageDownLoad");
                        //    return bResult;
                        //}
                    }

                    SiaSun.LMS.Model.WH_CELL mEND_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                    if (null == mEND_CELL)
                    {
                        bResult = false;
                        sResult = string.Format("未能找到终止位置{0}", mEND_CELL.CELL_CODE);
                        return bResult;
                    }
                    //81050-3楼舵机18301不校验路径
                    string Floor3ApplyStartStation = string.Empty;
                    if (!this._S_SystemService.GetSysParameter("Floor3ApplyStartStation", out Floor3ApplyStartStation) ||
                        string.IsNullOrEmpty(Floor3ApplyStartStation))
                    {
                        bResult = false;
                        sResult = "未找到系统参数[Floor3ApplyStartStation]_未能获取三楼提升机入库申请站台";
                        return bResult;
                    }
                    if (!mSTART_CELL.DEVICE_CODE.Equals(Floor3ApplyStartStation))
                    {
                        //路径校验
                        IList<Model.IO_CONTROL_ROUTE> lsIO_CONTROL_ROUTE = this._P_IO_CONTROL_ROUTE.GetList(mSTART_CELL.DEVICE_CODE, mEND_CELL.DEVICE_CODE);
                        bResult = lsIO_CONTROL_ROUTE.Count > 0;
                        if (!bResult)
                        {
                            sResult = string.Format("{0}-{1}之间无可用路径，请在监控系统中查看设备状态！", mSTART_CELL.CELL_CODE, mEND_CELL.CELL_CODE);
                            return bResult;
                        }
                    }
                    

                    Model.WH_WAREHOUSE WH_WAREHOUSE_START = this._P_WH_WAREHOUSE.GetModel(mSTART_CELL.WAREHOUSE_ID);
                    Model.WH_WAREHOUSE WH_WAREHOUSE_END = this._P_WH_WAREHOUSE.GetModel(mEND_CELL.WAREHOUSE_ID);
                    if (WH_WAREHOUSE_START == null || WH_WAREHOUSE_END == null)
                    {
                        bResult = false;
                        sResult = string.Format("未能找到{0}仓库", WH_WAREHOUSE_START == null ? "起始" : "终止");
                        return bResult;
                    }

                    mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(MANAGE_ID);
                    if (mIO_CONTROL != null)
                    {
                        bResult = false;
                        sResult = string.Format("控制任务{0}已经存在!", MANAGE_ID.ToString());
                        return bResult;
                    }
                    // wdz add 2018-05-20
                    if (this._P_IO_CONTROL.GetList(mMANAGE_MAIN.STOCK_BARCODE).Count > 0)
                    {
                        bResult = false;
                        sResult = string.Format("控制任务已经存在_条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    int CONTROL_TASK_TYPE = 0;
                    mIO_CONTROL = new SiaSun.LMS.Model.IO_CONTROL();

                    string IN_OUT_TYPE = string.Format("{0}-{1}", mSTART_CELL.CELL_TYPE, mEND_CELL.CELL_TYPE).ToLower();

                    switch (IN_OUT_TYPE)
                    {
                        case "station-cell"://站台-货位-入

                            CONTROL_TASK_TYPE = Convert.ToInt32(SiaSun.LMS.Enum.CONTROL_TYPE.Up.ToString("d"));
                            mIO_CONTROL.CELL_GROUP = mEND_CELL.LANE_WAY;
                            break;

                        case "cell-station"://货位-站台-出

                            CONTROL_TASK_TYPE = Convert.ToInt32(SiaSun.LMS.Enum.CONTROL_TYPE.Down.ToString("d"));
                            mIO_CONTROL.CELL_GROUP = mSTART_CELL.LANE_WAY;
                            break;

                        case "cell-cell"://货位-货位-移

                            CONTROL_TASK_TYPE = Convert.ToInt32(SiaSun.LMS.Enum.CONTROL_TYPE.Move.ToString("d"));
                            break;

                        case "station-station"://站台-站台-移

                            CONTROL_TASK_TYPE = Convert.ToInt32(SiaSun.LMS.Enum.CONTROL_TYPE.MoveStation.ToString("d"));
                            break;
                    }

                    mIO_CONTROL.RELATIVE_CONTROL_ID = -1;
                    mIO_CONTROL.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                    mIO_CONTROL.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;

                    mIO_CONTROL.CONTROL_TASK_LEVEL = string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_LEVEL) ? "0" : mMANAGE_MAIN.MANAGE_LEVEL;
                    mIO_CONTROL.PRE_CONTROL_STATUS = string.Empty;
                    mIO_CONTROL.CONTROL_TASK_TYPE = CONTROL_TASK_TYPE;

                    #region 转换3期线边仓货位号
                    string startCellCode = string.Empty,endCellCode = string.Empty;
                    //TODO:将三期仓库ID配置进SYS_PARAMETER，消除硬编码
                    if(3 == mSTART_CELL.WAREHOUSE_ID && mSTART_CELL.CELL_Z > 50)
                    {
                        mSTART_CELL.CELL_CODE = string.Format("{0}"+ mSTART_CELL.CELL_CODE.Substring(1, mSTART_CELL.CELL_CODE.Length - 1),"0");
                         
                    }

                    if (3 == mEND_CELL.WAREHOUSE_ID && mEND_CELL.CELL_Z > 50)
                    {
                        mEND_CELL.CELL_CODE = string.Format("{0}" + mEND_CELL.CELL_CODE.Substring(1, mEND_CELL.CELL_CODE.Length - 1), "0");

                    }
                    #endregion

                    mIO_CONTROL.START_DEVICE_CODE = mSTART_CELL.CELL_CODE;
                    mIO_CONTROL.END_DEVICE_CODE = mEND_CELL.CELL_CODE;
                    mIO_CONTROL.PRE_CONTROL_STATUS = "0";
                    mIO_CONTROL.CONTROL_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    //单叉状态25 双叉状态0 string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_FORK) || !mMANAGE_MAIN.MANAGE_FORK.Equals("1") ? 0 : 25;
                    mIO_CONTROL.CONTROL_STATUS = 0;
                    mIO_CONTROL.CONTROL_REMARK = mMANAGE_MAIN.MANAGE_REMARK;
                    mIO_CONTROL.START_WAREHOUSE_CODE = WH_WAREHOUSE_START.WAREHOUSE_CODE;
                    mIO_CONTROL.END_WAREHOUSE_CODE = WH_WAREHOUSE_END.WAREHOUSE_CODE;

                    mIO_CONTROL.AGV_TASK = "0";

                    #region 齐套出库一楼模式判断
                    if (mEND_CELL.CELL_CODE == "710313")
                    {
                        string strSql = string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'PlanMode-KitOut1'");
                        DataTable dtWorkModeKitOut1 = this.GetList(strSql);
                        if (dtWorkModeKitOut1 != null && dtWorkModeKitOut1.Rows.Count > 0)
                        {
                            //单一模式需给调度传递TASK_GROUP 
                            if (dtWorkModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.One.ToString())
                            {
                                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);
                                if (mPLAN_MAIN != null)
                                {
                                    mIO_CONTROL.TASK_GROUP = mPLAN_MAIN.BACKUP_FILED3;//用集货单号表示一次下达的任务，用于调度排序
                                }
                            }
                        }    

                    }
                    #endregion

                    #region 齐套出库三楼模式判断
                    if (mEND_CELL.CELL_CODE == "81056" || mEND_CELL.CELL_CODE == "81070" || mEND_CELL.CELL_CODE == "81084" || mEND_CELL.CELL_CODE == "81098")
                    {
                        string strSql = string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'PlanMode-KitOut3'");
                        DataTable dtWorkModeKitOut3 = this.GetList(strSql);
                        if (dtWorkModeKitOut3 != null && dtWorkModeKitOut3.Rows.Count > 0)
                        {
                            //单一模式需给调度传递TASK_GROUP 
                            if (dtWorkModeKitOut3.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.One.ToString())
                            {
                                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);
                                if (mPLAN_MAIN != null)
                                {
                                    mIO_CONTROL.TASK_GROUP = mPLAN_MAIN.BACKUP_FILED3;//用集货单号表示一次下达的任务，用于调度排序
                                }
                            }
                        }
                    }
                    #endregion

                    this._P_IO_CONTROL.Add(mIO_CONTROL);
                    controlId = mIO_CONTROL.CONTROL_ID;

                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingExecute.ToString();

                    this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.Message;
                }
                finally
                {
                    if (bResult)
                    {
                        this._P_Base_House.CommitTransaction(bTrans);

                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("ManageBase.ManageDownLoad():起始仓库[{0}]_起始位置[{1}]_终止仓库[{2}]_终止位置[{3}]_条码[{4}]_CONTROL_ID[{5}]", mIO_CONTROL.START_WAREHOUSE_CODE, mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.END_WAREHOUSE_CODE, mIO_CONTROL.END_DEVICE_CODE, mIO_CONTROL.STOCK_BARCODE, mIO_CONTROL.CONTROL_ID));

                        //wdz add 2018-03-03  webservice通知调度
                        if (wsNoticeControl)
                        {
                            this._S_ManageService.ManageDownLoadWebService(controlId);
                        }
                    }
                    else
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);
                     }
                }
            }

            return bResult;
        }
        
        /// <summary>
        /// ManageDownLoad扩展 不输出controlid 不WebService通知
        /// wdz add 2018-03-03
        /// </summary>
        public bool ManageDownLoad(int MANAGE_ID, string START_CELL_CODE, bool bTrans, out string sResult)
        {
            int controlId = 0;
            return ManageDownLoad(MANAGE_ID, START_CELL_CODE, bTrans, false, out controlId, out sResult);
        }
        
        /// <summary>
        /// ManageDownLoad扩展 不输出controlid WebService通知
        /// wdz add 2018-03-03
        /// </summary>
        public bool ManageDownLoad(int MANAGE_ID, string START_CELL_CODE, bool bTrans, bool wsNoticeControl, out string sResult)
        {
            int controlId = 0;
            return ManageDownLoad(MANAGE_ID, START_CELL_CODE, bTrans, wsNoticeControl, out controlId, out sResult);
        }
        
        /// <summary>
        /// 管理下发非立库输送任务Control
        /// wdz add 2017-12
        /// </summary>
        public bool ManageDownLoadTrans(int paraManageId,string startDeviceCode, bool wsNoticeControl, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.IO_CONTROL mIO_CONTROL = null;
            
            try
            {
                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(paraManageId);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("ManageBase.ManageDownLoadTrans:未能找到任务 任务ID_{0}", paraManageId);
                    return bResult;
                }
                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(paraManageId);
                if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("ManageBase.ManageDownLoadTrans:未能找到任务列表 任务ID_{0}", paraManageId);
                    return bResult;
                }
                
                mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(paraManageId);
                if (mIO_CONTROL != null)
                {
                    bResult = false;
                    sResult = string.Format("ManageBase.ManageDownLoadTrans:控制任务已经存在 ManageId_{0}", paraManageId);
                    return bResult;
                }

                // wdz add 2018-05-20
                if (!string.IsNullOrEmpty(mMANAGE_MAIN.STOCK_BARCODE) && this._P_IO_CONTROL.GetList(mMANAGE_MAIN.STOCK_BARCODE).Count > 0)
                {
                    bResult = false;
                    sResult = string.Format("控制任务已经存在_条码[{0}]", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }


                foreach (Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mIO_CONTROL = new SiaSun.LMS.Model.IO_CONTROL();

                    mIO_CONTROL.RELATIVE_CONTROL_ID = -1;
                    mIO_CONTROL.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                    mIO_CONTROL.PRE_CONTROL_STATUS = "0";
                    mIO_CONTROL.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;
                    mIO_CONTROL.CONTROL_TASK_LEVEL = string.IsNullOrEmpty(mMANAGE_MAIN.MANAGE_LEVEL) ? "0" : mMANAGE_MAIN.MANAGE_LEVEL;
                    mIO_CONTROL.CONTROL_TASK_TYPE = 3;
                    mIO_CONTROL.CONTROL_STATUS = 0;
                    mIO_CONTROL.CONTROL_REMARK = mMANAGE_MAIN.MANAGE_REMARK;
                    mIO_CONTROL.CONTROL_BEGIN_TIME = Common.StringUtil.GetDateTime();

                    mIO_CONTROL.RELEVANT_CODE = mMANAGE_LIST.GOODS_PROPERTY1;
                    mIO_CONTROL.TASK_ID = mMANAGE_LIST.GOODS_PROPERTY2;
                    mIO_CONTROL.STEP_NO = mMANAGE_LIST.GOODS_PROPERTY3;
                    mIO_CONTROL.ACTION_TYPE = mMANAGE_LIST.GOODS_PROPERTY4;
                    mIO_CONTROL.FINAL_POSITION = mMANAGE_LIST.GOODS_PROPERTY5;
                    mIO_CONTROL.START_DEVICE_CODE = string.IsNullOrEmpty(startDeviceCode)? mMANAGE_LIST.GOODS_PROPERTY7: startDeviceCode;
                    mIO_CONTROL.END_DEVICE_CODE = mMANAGE_LIST.GOODS_PROPERTY8;
                    mIO_CONTROL.RCS_TASK_CODE = mMANAGE_MAIN.BACKUP_FIELD1;

                    //wdz add 2018-01-13  如果起止位置有空，则复制一下
                    if(string.IsNullOrEmpty(mIO_CONTROL.START_DEVICE_CODE) && !string.IsNullOrEmpty(mIO_CONTROL.END_DEVICE_CODE))
                    {
                        mIO_CONTROL.START_DEVICE_CODE = mIO_CONTROL.END_DEVICE_CODE;
                    }
                    if (!string.IsNullOrEmpty(mIO_CONTROL.START_DEVICE_CODE) && string.IsNullOrEmpty(mIO_CONTROL.END_DEVICE_CODE))
                    {
                        mIO_CONTROL.END_DEVICE_CODE = mIO_CONTROL.START_DEVICE_CODE;
                    }

                    //验证路径
                    if (!string.IsNullOrEmpty(mIO_CONTROL.START_DEVICE_CODE) && !string.IsNullOrEmpty(mIO_CONTROL.END_DEVICE_CODE))
                    {
                        DataTable dtIoControlRouteDisable = this.GetList(string.Format("select * from IO_CONTROL_ROUTE where START_DEVICE = '{0}' and END_DEVICE ='{1}' and CONTROL_ROUTE_STATUS ='0' ", mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.END_DEVICE_CODE));
                        if (dtIoControlRouteDisable != null && dtIoControlRouteDisable.Rows.Count > 0)
                        {
                            bResult = false;
                            sResult = string.Format("ManageBase.ManageDownLoadTrans:任务路径不可用_起点[{0}]_终点[{1}]", mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.END_DEVICE_CODE);
                            return bResult;
                        }
                    }

                    this._P_IO_CONTROL.Add(mIO_CONTROL);
                }

                mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.Executing.ToString();
                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManageBase.ManageDownLoadTrans:异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    //wdz add 2018-03-03  webservice通知调度
                    if (wsNoticeControl)
                    {
                        this._S_ManageService.ManageDownLoadWebService(mIO_CONTROL.CONTROL_ID);
                    }

                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("ManageBase.ManageDownLoadTrans():起始仓库[{0}]_起始位置[{1}]_终止仓库[{2}]_终止位置[{3}]_条码[{4}]_CONTROL_ID[{5}]", mIO_CONTROL.START_WAREHOUSE_CODE, mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.END_WAREHOUSE_CODE, mIO_CONTROL.END_DEVICE_CODE, mIO_CONTROL.STOCK_BARCODE, mIO_CONTROL.CONTROL_ID));
                }
            }
            return bResult;
        }
        
        /// <summary>
        /// wdz add 2018-02-28
        /// IO_CONTROL上报放货重
        /// </summary>
        public bool ManageReDownloadException(int manageId, out string message)
        {
            bool result = true;
            message = string.Empty;

            Model.MANAGE_MAIN manageMain = this._P_MANAGE_MAIN.GetModel(manageId);
            if (manageMain == null)
            {
                result = false;
                message = string.Format("ManageBase.ManageReDownloadException():未能找到任务_参数manageId[{0}]", manageId);
                return result;
            }
            Model.WH_CELL endCell = this._P_WH_CELL.GetModel(manageMain.END_CELL_ID);
            if(endCell == null)
            {
                result = false;
                message = string.Format("ManageBase.ManageReDownloadException():未能找到终点位置_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID,manageMain.STOCK_BARCODE);
                return result;
            }
            Model.MANAGE_TYPE manageType = this._P_MANAGE_TYPE.GetModelManageTypeCode(manageMain.MANAGE_TYPE_CODE);
            if (manageType == null)
            {
                result = false;
                message = string.Format("ManageBase.ManageReDownloadException():未能找到任务类型_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID, manageMain.STOCK_BARCODE);
                return result;
            }
            if (!new string[] { "1", "3" }.Contains(manageType.MANAGE_TYPE_INOUT))
            {
                result = false;
                message = string.Format("ManageBase.ManageReDownloadException():重置入库货位功能只针对入库或者上架任务有效_任务ID[{0}]_条码[{1}]", manageMain.MANAGE_ID, manageMain.STOCK_BARCODE);
                return result;
            }

            try
            {
                this._P_Base_House.BeginTransaction();

                //重置任务终点位置
                manageMain.END_CELL_ID = 0;
                manageMain.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();
                manageMain.MANAGE_REMARK = "调度上报放货重复";
                this._P_MANAGE_MAIN.Update(manageMain);
                this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("ManageBase.ManageReDownloadException():调度上报放货重复_重置管理任务终点位置位置成功_任务ID[{0}]_条码[{1}]", manageId, manageMain.STOCK_BARCODE));

                //删除IO_CONTROL
                this._P_IO_CONTROL.DeleteManageID(manageId);

                //将终点位置标记为不可用
                endCell.RUN_STATUS = Enum.RUN_STATUS.Disable.ToString();
                this._P_WH_CELL.Update(endCell);
                this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Critical, string.Format("ManageBase.ManageReDownloadException():调度上报放货重复_将异常货位标记为禁用_任务ID[{0}]_条码[{1}]_货位编码[{2}]", manageId, manageMain.STOCK_BARCODE, endCell.CELL_CODE));
            }
            catch (Exception ex)
            {
                result = false;
                this.CreateSysLog(Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Error, string.Format("ManageBase.ManageReDownloadException():重置货位时发生异常_任务ID[{0}]_异常信息[{1}]", manageId,ex.Message));
                throw;
            }
            finally
            {
                if (result)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }

            return result;
        }

        #region Invalid in wdz
        /// <summary>
        /// 更新ERP中间表中的完成数量
        /// xcjt 2016-12-28
        /// </summary>
        internal bool ManageCompleteUpdateErpQty(int MANAGE_ID)
        {
            bool bResult = true;
            string sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;

            try
            {
                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("ManageCompleteUpdateErpQty()未能获取任务 任务ID{0}", MANAGE_ID);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);
                if (mPLAN_MAIN == null || mPLAN_MAIN.PLAN_FLAG == "0")
                {
                    bResult = false;
                    sResult = string.Format("ManageCompleteUpdateErpQty()未能获取任务所属的导入计划 任务ID{0}", MANAGE_ID);
                    return bResult;
                }

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);

                foreach (var mMANAGE_LIST in lsMANAGE_LIST)
                {
                    Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);
                    Model.MID_TASK mMID_TASK = this._P_MID_TASK.GetModel(int.Parse(mPLAN_LIST.PLAN_LIST_CODE));
                    mMID_TASK.FINISH_QTY += Convert.ToInt32(mMANAGE_LIST.MANAGE_LIST_QUANTITY);
                    this._P_MID_TASK.Update(mMID_TASK);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("任务完成后更新ERP中间表中的完成数量时发生异常 {0}", ex.Message);
            }
            finally
            {
                if (mMANAGE_MAIN != null)
                {
                    this._S_SystemService.CreateSysLog(Enum.LogThread.Interface, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ManageBase.ManageCompleteUpdateErpQty():任务完成更新ERP中间表FINISH_QTY_结果{0} {1}_计划ID[{2}]_任务ID[{3}]_任务类型[{4}]_条码[{5}]",
                                                                                              bResult ? "成功" : "失败",
                                                                                              sResult,
                                                                                              mMANAGE_MAIN.PLAN_ID,
                                                                                              mMANAGE_MAIN.MANAGE_ID,
                                                                                              mMANAGE_MAIN.MANAGE_TYPE_CODE,
                                                                                              mMANAGE_MAIN.STOCK_BARCODE));
                }
                else
                {
                    this._S_SystemService.CreateSysLog(Enum.LogThread.Interface, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ManageBase.ManageCompleteUpdateErpQty():任务完成更新ERP中间表FINISH_QTY_结果{0} {1}", bResult ? "成功" : "失败 ", sResult));
                }
            }

            return bResult;
        }


        /// <summary>
        /// 出库任务完成后记录托盘匹配信息
        /// tzyg add 2017-03-02
        /// </summary>
        internal bool ManageOutLog(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (mMANAGE_MAIN == null)
            {
                bResult = false;
                sResult = string.Format("未能获取任务信息 ID:{0}", MANAGE_ID);
                return bResult;
            }
            IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);
            if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
            {
                bResult = false;
                sResult = string.Format("未能获取任务明细信息 ID:{0}", MANAGE_ID);
                return bResult;
            }

            try
            {
                StringBuilder sb = new StringBuilder();
                sb.Append("|");
                foreach (var MANAGE_LIST in lsMANAGE_LIST)
                {
                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(MANAGE_LIST.GOODS_ID);
                    if (mMANAGE_MAIN != null)
                    {
                        sb.Append(mGOODS_MAIN.GOODS_CODE);
                        sb.Append("|");
                    }
                }

                Model.MANAGE_OUT_LOG mMANAGE_OUT_LOG = this._P_MANAGE_OUT_LOG.GetModelManageId(mMANAGE_MAIN.MANAGE_ID);
                if (mMANAGE_OUT_LOG == null)
                {
                    mMANAGE_OUT_LOG = new Model.MANAGE_OUT_LOG();

                    mMANAGE_OUT_LOG.MANAGE_OUT_LOG_DATE = Common.StringUtil.GetDateTime();
                    mMANAGE_OUT_LOG.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                    mMANAGE_OUT_LOG.MANAGE_OUT_LOG_GOODS = sb.ToString();
                    mMANAGE_OUT_LOG.MANAGE_OUT_LOG_STOCK = mMANAGE_MAIN.STOCK_BARCODE;

                    this._P_MANAGE_OUT_LOG.Add(mMANAGE_OUT_LOG);
                }
                else
                {
                    mMANAGE_OUT_LOG.MANAGE_OUT_LOG_DATE = Common.StringUtil.GetDateTime();
                    mMANAGE_OUT_LOG.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                    mMANAGE_OUT_LOG.MANAGE_OUT_LOG_GOODS = sb.ToString();
                    mMANAGE_OUT_LOG.MANAGE_OUT_LOG_STOCK = mMANAGE_MAIN.STOCK_BARCODE;

                    this._P_MANAGE_OUT_LOG.Update(mMANAGE_OUT_LOG);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("出库完成后记录托盘匹配信息异常 {0}", ex.Message);
            }

            return bResult;
        }


        /// <summary>
        /// 任务完成后更新物料表中模具的状态字段
        /// tzyg add 2017-03-23
        /// </summary>
        internal bool ManageUpdateGoodsInfo(int MANAGE_ID)
        {
            bool bResult = true;
            string sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;
            string goodsStatus = string.Empty;

            try
            {
                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未能获取任务 任务ID{0}_ManageUpdateGoodsInfo", MANAGE_ID);
                    return bResult;
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                if (mMANAGE_TYPE == null)
                {
                    bResult = false;
                    sResult = string.Format("未能获取任务类型 任务ID{0}_ManageUpdateGoodsInfo", MANAGE_ID);
                    return bResult;
                }

                switch (mMANAGE_TYPE.MANAGE_TYPE_INOUT)
                {
                    case "1":
                        goodsStatus = "InStore";
                        break;
                    case "2":
                        goodsStatus = "NotInStore";
                        break;
                    default:
                        break;
                }

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);

                foreach (var mMANAGE_LIST in lsMANAGE_LIST)
                {
                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mMANAGE_LIST.GOODS_ID);
                    if (mGOODS_MAIN.GOODS_CLASS_ID == 2 && !string.IsNullOrEmpty(goodsStatus))
                    {
                        mGOODS_MAIN.GOODS_CONST_PROPERTY7 = goodsStatus;
                        this._P_GOODS_MAIN.Update(mGOODS_MAIN);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("任务完成后物料状态时发生异常 {0}", ex.Message);
            }
            finally
            {
                if (mMANAGE_MAIN != null)
                {
                    this._S_SystemService.CreateSysLog(Enum.LogThread.Task, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ManageBase.ManageUpdateGoodsInfo():任务完成更新物料状态_结果{0} {1}_计划ID[{2}]_任务ID[{3}]_任务类型[{4}]_条码[{5}]",
                                                                                              bResult ? "成功" : "失败 ",
                                                                                              sResult,
                                                                                              mMANAGE_MAIN.PLAN_ID,
                                                                                              mMANAGE_MAIN.MANAGE_ID,
                                                                                              mMANAGE_MAIN.MANAGE_TYPE_CODE,
                                                                                              mMANAGE_MAIN.STOCK_BARCODE));
                }
                else
                {
                    this._S_SystemService.CreateSysLog(Enum.LogThread.Task, "System", bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ManageBase.ManageUpdateGoodsInfo():任务完成更新物料状态_结果{0} {1}", bResult ? "成功" : "失败 ", sResult));
                }
            }

            return bResult;
        }
        #endregion

        public bool ManageCheck(WH_CELL mSTART_CELL, MANAGE_MAIN mMANAGE_MAIN, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                #region 检查双深是否需要倒库，需要的话生成倒库任务
                //起始货位是3期立库区
                if (4 == mSTART_CELL.AREA_ID && mSTART_CELL.CELL_TYPE.Equals(Enum.CELL_TYPE.Cell.ToString()))
                {
                    int normalCellID = 0;
                    string normalStockBarcode;
                    STORAGE_MAIN mNORMAL_STORAGE_MAIN = null;
                    IList<STORAGE_LIST> ls_NORMAL_STORAGE_LIST = null;
                    this._S_CellService.CheckNormalMove(mSTART_CELL.CELL_ID, out normalCellID, out normalStockBarcode, out mNORMAL_STORAGE_MAIN, out ls_NORMAL_STORAGE_LIST, out sResult);
                    var normalCell = this._P_WH_CELL.GetModel(normalCellID);
                    if (normalCellID != 0)
                    {

                        if (normalCell == null)
                        {
                            bResult = false;
                            sResult = string.Format("ManageBase.ManageDownload:创建倒库任务时未能找到[{0}]相邻货位的货位信息_箱条码[{1}]", mSTART_CELL.CELL_CODE, normalStockBarcode);
                            return bResult;
                        }

                        if (mNORMAL_STORAGE_MAIN == null || ls_NORMAL_STORAGE_LIST == null)
                        {
                            bResult = false;
                            sResult = string.Format("ManageBase.ManageDownload:创建倒库任务时未能找到[{0}]相邻货位的库存信息_箱条码[{1}]", mSTART_CELL.CELL_CODE, normalStockBarcode);
                            return bResult;
                        }

                        //需要倒库，构建倒库任务
                        var manageMainMove = new Model.MANAGE_MAIN()
                        {
                            CROSS_FLAG = "0",
                            FULL_FLAG = "0",
                            GOODS_TEMPLATE_ID = 0,
                            MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                            MANAGE_CONFIRM_TIME = "",
                            MANAGE_END_TIME = "",
                            MANAGE_ID = 0,
                            MANAGE_LEVEL = "9",
                            MANAGE_OPERATOR = "自动倒库",
                            MANAGE_RELATE_CODE = "",
                            MANAGE_REMARK = $"{mMANAGE_MAIN.STOCK_BARCODE}出库产生的倒库任务",
                            MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString(),
                            MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString(),
                            MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageMove.ToString(),
                            PLAN_ID = 0,
                            PLAN_TYPE_CODE = "",
                            START_CELL_ID = normalCellID,
                            STOCK_BARCODE = normalStockBarcode,
                            CELL_MODEL = mNORMAL_STORAGE_MAIN.CELL_MODEL,
                            END_CELL_ID = 0
                        };

                        var manageListsMove = new List<Model.MANAGE_LIST>();

                        foreach (var item in ls_NORMAL_STORAGE_LIST)
                        {
                            manageListsMove.Add(new Model.MANAGE_LIST()
                            {
                                BOX_BARCODE = item.BOX_BARCODE,
                                DETAIL_FLAG = "0",
                                MANAGE_ID = 0,
                                MANAGE_LIST_ID = 0,
                                MANAGE_LIST_QUANTITY = item.STORAGE_LIST_QUANTITY,
                                MANAGE_LIST_REMARK = "",
                                PLAN_LIST_ID = 0,
                                STORAGE_LIST_ID = item.STORAGE_LIST_ID,
                                STORAGE_LOCK_ID = 0,
                                GOODS_ID = item.GOODS_ID,
                                GOODS_PROPERTY1 = item.GOODS_PROPERTY1,
                                GOODS_PROPERTY2 = item.GOODS_PROPERTY2,
                                GOODS_PROPERTY3 = item.GOODS_PROPERTY3,
                                GOODS_PROPERTY4 = item.GOODS_PROPERTY4,
                                GOODS_PROPERTY5 = item.GOODS_PROPERTY5,
                                GOODS_PROPERTY6 = item.GOODS_PROPERTY6,
                                GOODS_PROPERTY7 = item.GOODS_PROPERTY7,
                                GOODS_PROPERTY8 = item.GOODS_PROPERTY8
                            });
                        }

                        

                        //使用双伸货位分配方法分配一个空闲货位
                        int newEndCellId = 0;
                        WH_CELL_GROUP mWH_CELL_GROUP = _P_WH_CELL_GROUP.GetModelByGroupCode(normalCell.CELL_CODE.Substring(3));
                        bResult = this._S_CellService.RelocateInGroup(normalCell, mWH_CELL_GROUP, out newEndCellId, out sResult);
                        //bResult = this._S_CellService._S_CellService.CellInAllocate(
                        //    normalCell.WAREHOUSE_ID,
                        //    normalCell.AREA_ID,
                        //    ls_NORMAL_STORAGE_LIST[0].GOODS_PROPERTY6,
                        //    ls_NORMAL_STORAGE_LIST[0].GOODS_PROPERTY7,
                        //    ls_NORMAL_STORAGE_LIST[0].GOODS_PROPERTY8,
                        //    out newEndCellId,
                        //    out sResult);

                        if (!bResult || newEndCellId <= 0)
                        {
                            sResult = string.Format("ManageBase.ManageDownload:创建倒库任务时双伸货位外侧库存[{0}]未能分配新货位_信息[{1}]", mNORMAL_STORAGE_MAIN.STOCK_BARCODE, sResult);
                            return bResult;
                        }
                        manageMainMove.END_CELL_ID = newEndCellId;

                        bResult = new ManageMove().ManageCreate(manageMainMove, manageListsMove, false, true, false, true, out sResult);
                        if (!bResult)
                        {
                            sResult = string.Format("ManageBase.ManageDownload:双伸货位外侧库存[{0}]倒库时下达任务失败_信息[{1}]", mNORMAL_STORAGE_MAIN.STOCK_BARCODE, sResult);
                            return bResult;
                        }

                    }
                    //外层无库存有任务，即有入库任务，内层无法出库
                    else if (mNORMAL_STORAGE_MAIN == null && normalCell != null && normalCell.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                    {
                        bResult = false;
                        sResult = string.Format("ManageBase.ManageDownload:无法出库_齐套箱[{0}]在双伸货位内侧_并且外侧货位[{1}]有入库任务", mNORMAL_STORAGE_MAIN.STOCK_BARCODE, normalCell.CELL_CODE);
                        return bResult;
                    }
                }
                #endregion

                
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }

            return bResult;
        }

    }
}
