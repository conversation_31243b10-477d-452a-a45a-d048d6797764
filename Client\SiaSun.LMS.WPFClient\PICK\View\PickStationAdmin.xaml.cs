﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.PICK.View
{
    /// <summary>
    /// PickStationAdmin.xaml 的交互逻辑
    /// </summary>
    public partial class PickStationAdmin : AvalonDock.DocumentContent
    {
        private string AgentOrder = string.Empty;
        public PickStationAdmin()
        {
            InitializeComponent();
            this.gridOrder.gridApp.SelectionChanged -= GridApp_SelectionChanged;
            this.gridOrder.gridApp.SelectionChanged += GridApp_SelectionChanged;
        }

        private void GridApp_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (gridOrder.gridApp.SelectedItem == null)
            {
                return;
            }

            DataRow rowManage = (gridOrder.gridApp.SelectedItem as DataRowView).Row;
            this.AgentOrder = rowManage["PLAN_GROUP"].ToString();

            
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //
            this.PickStationBind();

            this.OrderBind();
        }

        private void PickStationBind()
        {
            //gridStation;
            try
            {
                this.gridStation.U_WindowName = this.GetType().Name;
                this.gridStation.U_TableName = "T_PICK_STATION";
                this.gridStation.U_XmlTableName = "T_PICK_STATION";
                this.gridStation.U_OrderField = "STATION_ID";
                
                this.gridStation.U_AllowChecked = false;
                this.gridStation.U_AllowOperatData = true;
                this.gridStation.U_AllowPage = true;
                this.gridStation.U_AllowDelete = Visibility.Collapsed;
                this.gridStation.U_AllowEdit = Visibility.Visible;

                this.gridStation.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }

        }

        private void OrderBind()
        {
            try
            {
                this.gridOrder.U_WindowName = this.GetType().Name;
                this.gridOrder.U_TableName = "V_PLAN_PICK_GROUP_WAITING";
                this.gridOrder.U_XmlTableName = "V_PLAN_PICK_GROUP_WAITING";
                this.gridOrder.U_OrderField = "ORDER_TIME, HEAD_TEXT,PLAN_TO_USER";

                this.gridOrder.U_AllowChecked = false;
                this.gridOrder.U_AllowOperatData = false;
                this.gridOrder.U_AllowPage = true;
                this.gridOrder.U_AllowDelete = Visibility.Collapsed;

                this.gridOrder.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 工作站信息刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void stationReflesh_Click(object sender, RoutedEventArgs e)
        {
            this.gridStation.U_Update();
        }

        /// <summary>
        /// 订单信息刷新
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void orderReflesh_Click(object sender, RoutedEventArgs e)
        {
            this.gridOrder.U_Update();
        }

        /// <summary>
        /// 加急
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void orderUrgent_Click(object sender, RoutedEventArgs e)
        {
            if (MainApp._MessageDialog.ShowDialog(
                Enum.MessageConverter.ConfirmExecute,
                string.Format("是否加急{0}", AgentOrder)) == Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                bool bResult = true;
                string sResult = string.Empty;

                bResult = MainApp._I_StorageService.AgentAutoBindOrder(MainApp._USER, AgentOrder, out sResult);

                if (bResult)
                {
                    MessageBox.Show("操作成功");
                   
                }
                else
                {
                    MessageBox.Show(string.Format("操作失败,原因:{0}", sResult));
                }

                this.gridOrder.U_Update();
            }
        }
    }
}
