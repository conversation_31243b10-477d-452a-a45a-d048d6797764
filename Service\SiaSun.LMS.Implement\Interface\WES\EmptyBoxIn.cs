﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 空箱入立库
    /// </summary>
    public class EmptyBoxIn : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _boxNo = string.Empty;                     //箱号
            private string _boxType = string.Empty;                   //箱子类型
            private string _fromPosition = string.Empty;              //起点位置
            private string _uniqueCode = string.Empty;                //唯一码
            private string _interfaceType = string.Empty;             //接口类型
            private string _boxColor = string.Empty;                  //箱子颜色
            private string _interfaceSource = string.Empty;           //接口来源

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 箱子颜色
            /// </summary>
            public string boxColor
            {
                get { return _boxColor; }
                set { _boxColor = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxIn.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if(string.IsNullOrEmpty(taskInfo.boxNo)||string.IsNullOrEmpty(taskInfo.boxType)||string.IsNullOrEmpty(taskInfo.boxColor)
                    ||string.IsNullOrEmpty(taskInfo.fromPosition)||string.IsNullOrEmpty(taskInfo.uniqueCode))
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxIn.NotifyMethod:入参存在空值");
                    return bResult;
                }

                string validRegexString = string.Empty;
                if (this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex", out validRegexString) &&
                    !Common.RegexValid.IsValidate(taskInfo.boxNo, validRegexString))
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxIn.NotifyMethod:箱条码验证失败 传入值-{0}", taskInfo.boxNo);
                    return bResult;
                }

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(taskInfo.fromPosition);
                if(mWH_CELL_START==null)
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxIn.NotifyMethod:入参 起始位置有误 传入值_{0}", taskInfo.fromPosition);
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelRelateCode(taskInfo.uniqueCode);
                if (mMANAGE_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxIn.NotifyMethod:传入唯一码已经存在任务 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                //wdz add 2018-01-14 如果任务的起点没有固定条码扫描器 则指定终点到四楼货位申请站台
                bool isAutoSendControl = false;
                bool isSendControlIndependent = false;
                string endCellCode = string.Empty;
                string standardGoodsOutStation = string.Empty;

                if (mWH_CELL_START.CELL_PROPERTY == "NoScaner")
                {
                    System.Data.DataTable dtWaittingTask = this.GetList(string.Format("select 0 from IO_CONTROL where START_DEVICE_CODE ='{0}' and CONTROL_STATUS in ('0','7') ", mWH_CELL_START.CELL_CODE));
                    if (dtWaittingTask != null && dtWaittingTask.Rows.Count > 0)
                    {
                        bResult = false;
                        outJson = string.Format("EmptyBoxIn.NotifyMethod:当前输送起点位置存在等待执行任务，请稍后再试 起点位置-{0}", taskInfo.fromPosition);
                        return bResult;
                    }

                    if (!this._S_SystemService.GetSysParameter("StandardGoodsInStation", out standardGoodsOutStation))
                    {
                        bResult = false;
                        outJson = string.Format("EmptyBoxIn.NotifyMethod:未能获取标准件出库站台_Key[StandardGoodsInStation]");
                        return bResult;
                    }
                    if (mWH_CELL_START.CELL_CODE == standardGoodsOutStation)
                    {
                        //判断如果是标准件入库站台入库，则下达控制任务
                        isAutoSendControl = true; 
                    }
                    else
                    {
                        bResult = this._S_SystemService.GetSysParameter("MoveToFloor5EndStation", out endCellCode);
                        Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(endCellCode);
                        if (!bResult || mWH_CELL_END == null)
                        {
                            bResult = false;
                            outJson = string.Format("EmptyBoxIn.NotifyMethod:起点位置没有固定条码扫描器且系统未找到货位申请站台 起点位置-{0}", taskInfo.fromPosition);
                            return bResult;
                        }

                        isAutoSendControl = false;
                        isSendControlIndependent = true;
                    }
                }
                else if (mWH_CELL_START != null && mWH_CELL_START.CELL_PROPERTY == "Scaner")
                {
                    isAutoSendControl = false;
                    isSendControlIndependent = false;
                }


                mMANAGE_MAIN = new Model.MANAGE_MAIN();
                //空箱入库不写入WMS提供的箱型 写入0，防止WMS传错
                //mMANAGE_MAIN.CELL_MODEL = taskInfo.boxType;
                mMANAGE_MAIN.CELL_MODEL = "0";
                mMANAGE_MAIN.END_CELL_ID = 0;
                mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                string manageLevel = string.Empty;
                mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("DefaultTaskLevel", out manageLevel) ? manageLevel : "0";
                mMANAGE_MAIN.MANAGE_OPERATOR = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mMANAGE_MAIN.MANAGE_RELATE_CODE = taskInfo.uniqueCode;
                mMANAGE_MAIN.MANAGE_SOURCE = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageStockIn.ToString();
                mMANAGE_MAIN.PLAN_ID = 0;
                mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                mMANAGE_MAIN.START_CELL_ID = mWH_CELL_START.CELL_ID;
                mMANAGE_MAIN.STOCK_BARCODE = taskInfo.boxNo;

                Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                //mMANAGE_LIST.BOX_BARCODE = taskInfo.boxNo;
                mMANAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                //空箱入库不写入WMS提供的箱型 写入0，防止WMS传错
                //mMANAGE_LIST.GOODS_PROPERTY1 = taskInfo.boxType;//箱型
                mMANAGE_LIST.GOODS_PROPERTY1 = "0";//箱型
                mMANAGE_LIST.GOODS_PROPERTY2 = taskInfo.boxColor;//颜色
                mMANAGE_LIST.MANAGE_LIST_QUANTITY = 1;

                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, new List<Model.MANAGE_LIST> { mMANAGE_LIST }, true, true, false, isAutoSendControl }, out outJson);

                if (bResult && isSendControlIndependent)
                {
                    //下达到四楼货位分配扫描头的不关联管理任务的Control
                    bResult = this._S_ManageService.ControlCreate(3, taskInfo.boxNo, "1", taskInfo.fromPosition, "1", endCellCode, "5", out outJson);
                    if (!bResult)
                    {
                        bool bTemp = true;
                        string strTemp = string.Empty;
                        //下达Control失败后删除任务
                        bTemp = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCancel", new object[] { mMANAGE_MAIN.MANAGE_ID }, out strTemp);
                        //log
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("EmptyBoxIn.NotifyMethod():管理任务生成成功_下达非关联管理任务的Control任务失败_{0}_取消管理任务{1} {2}", outJson, bTemp ? "成功" : "失败 ", strTemp));
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("EmptyBoxIn.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
