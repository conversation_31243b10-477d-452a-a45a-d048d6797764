﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="GOODS_TEMPLATE_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="GOODS_TEMPLATE_LIST" type="SiaSun.LMS.Model.GOODS_TEMPLATE_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="GOODS_TEMPLATE_LIST">
			<result property="GOODS_TEMPLATE_LIST_ID" column="goods_template_list_id" />
			<result property="GOODS_TEMPLATE_ID" column="goods_template_id" />
			<result property="GOODS_ID" column="goods_id" />
			<result property="GOODS_TEMPLATE_QUANTITY" column="goods_template_quantity" />
			<result property="GOODS_TEMPLATE_REMARK" column="goods_template_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="GOODS_TEMPLATE_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  goods_template_list_id,
				  goods_template_id,
				  goods_id,
				  goods_template_quantity,
				  goods_template_remark
			From GOODS_TEMPLATE_LIST
		</select>
		
		<select id="GOODS_TEMPLATE_LIST_SELECT_BY_TEMPLATE_ID" parameterClass="int" extends = "GOODS_TEMPLATE_LIST_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_template_id=#GOODS_TEMPLATE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="GOODS_TEMPLATE_LIST_SELECT_BY_ID" parameterClass="int" extends = "GOODS_TEMPLATE_LIST_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_templete_list_id=#GOODS_TEMPLETE_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>

				
		<insert id="GOODS_TEMPLATE_LIST_INSERT" parameterClass="GOODS_TEMPLATE_LIST">
      Insert Into GOODS_TEMPLATE_LIST (
      goods_template_list_id,
      goods_template_id,
      goods_id,
      goods_template_quantity,
      goods_template_remark
      )Values(
      #GOODS_TEMPLATE_LIST_ID#,
      #GOODS_TEMPLATE_ID#,
      #GOODS_ID#,
      #GOODS_TEMPLATE_QUANTITY#,
      #GOODS_TEMPLATE_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="GOODS_TEMPLATE_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="GOODS_TEMPLATE_LIST_UPDATE" parameterClass="GOODS_TEMPLATE_LIST">
      Update GOODS_TEMPLATE_LIST Set
      goods_template_list_id=#GOODS_TEMPLATE_LIST_ID#,
      goods_template_id=#GOODS_TEMPLATE_ID#,
      goods_id=#GOODS_ID#,
      goods_template_quantity=#GOODS_TEMPLATE_QUANTITY#,
      goods_template_remark=#GOODS_TEMPLATE_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					goods_template_list_id=#GOODS_TEMPLATE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="GOODS_TEMPLATE_LIST_DELETE" parameterClass="int">
			Delete From GOODS_TEMPLATE_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					goods_template_list_id=#GOODS_TEMPLATE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>