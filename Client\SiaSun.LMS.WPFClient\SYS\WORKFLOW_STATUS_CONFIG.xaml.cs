﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// WORKFLOW_STATUS_CONFIG.xaml 的交互逻辑
    /// </summary>
    public partial class WORKFLOW_STATUS_CONFIG : AvalonDock.DocumentContent
    {
        public WORKFLOW_STATUS_CONFIG()
        {
            InitializeComponent();
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.NodeTypeBind();
        }

        /// <summary>
        /// 加载节点类型
        /// </summary>
        private void NodeTypeBind()
        {
            try
            {
                //wdz alter 2017-08-16
                //this.cmbNodeType.DisplayMemberPath = "FLOW_TYPE_NAME".ToLower();
                //this.cmbNodeType.SelectedValuePath = "FLOW_TYPE_ID".ToLower();
                this.cmbNodeType.DisplayMemberPath = MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? "FLOW_TYPE_NAME" : "flow_type_name";
                this.cmbNodeType.SelectedValuePath = MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? "FLOW_TYPE_ID" : "flow_type_id";

                this.cmbNodeType.ItemsSource = MainApp._I_BaseService.GetTableXmlSql("FLOW_TYPE_SELECT",null).DefaultView;
                if (this.cmbNodeType.HasItems)
                { 
                    this.cmbNodeType.SelectedItem = this.cmbNodeType.Items[0];
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //选择不同的节点，显示该节点内容
        private void cmbNodeType_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.cmbNodeType.SelectedValue == null)
                return;

            try
            {
                //设置控件属性
                this.ucMdiGridFlowActions.U_AllowEdit = true;
                this.ucMdiGridFlowActions.U_WindowName = this.GetType().Name;
                this.ucMdiGridFlowActions.U_ParentHeader = "流程节点";
                this.ucMdiGridFlowActions.U_ParentTableName = "FLOW_NODE";
                this.ucMdiGridFlowActions.U_ParentTag = "FLOW_NODE_ID";
                this.ucMdiGridFlowActions.U_ParentWhere = string.Format("FLOW_TYPE_ID={0}", cmbNodeType.SelectedValue.ToString());
                this.ucMdiGridFlowActions.U_ParentOrder = "FLOW_NODE_ORDER";
                this.ucMdiGridFlowActions.U_ChildHeader = "节点动作";
                this.ucMdiGridFlowActions.U_ChildTableName = "FLOW_ACTION";
                this.ucMdiGridFlowActions.U_ChildTag = "FLOW_NODE_ID";
                this.ucMdiGridFlowActions.U_ChildWhere = string.Empty;
                this.ucMdiGridFlowActions.U_ChildOrder = "FLOW_ACTION_ORDER";

                Dictionary<string, string> dicDefault = new Dictionary<string, string>();
                dicDefault.Add("FLOW_TYPE_ID",this.cmbNodeType.SelectedValue.ToString());

                this.ucMdiGridFlowActions.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }
    }
}
