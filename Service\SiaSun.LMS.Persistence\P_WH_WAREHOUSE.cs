﻿/***************************************************************************
 * 
 *       功能：     仓库持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// WH_WAREHOUSE
	/// </summary>
	public class P_WH_WAREHOUSE : P_Base_House
	{
		public P_WH_WAREHOUSE ()
		{
			//
			// TODO: 此处添加WH_WAREHOUSE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<WH_WAREHOUSE> GetList()
		{
			return ExecuteQueryForList<WH_WAREHOUSE>("WH_WAREHOUSE_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(WH_WAREHOUSE wh_warehouse)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("WH_WAREHOUSE");
                wh_warehouse.WAREHOUSE_ID = id;
            }

            return ExecuteInsert("WH_WAREHOUSE_INSERT",wh_warehouse);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(WH_WAREHOUSE wh_warehouse)
		{
			return ExecuteUpdate("WH_WAREHOUSE_UPDATE",wh_warehouse);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public WH_WAREHOUSE GetModel(System.Int32 WAREHOUSE_ID)
		{
			return ExecuteQueryForObject<WH_WAREHOUSE>("WH_WAREHOUSE_SELECT_BY_ID",WAREHOUSE_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public WH_WAREHOUSE GetModel_CELL_ID(System.Int32 CELL_ID)
        {
            return this.ExecuteQueryForObject<WH_WAREHOUSE>("WH_WAREHOUSE_SELECT_BY_CELL_ID", CELL_ID);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 WAREHOUSE_ID)
		{
			return ExecuteDelete("WH_WAREHOUSE_DELETE",WAREHOUSE_ID);
		}
		

	}
}
