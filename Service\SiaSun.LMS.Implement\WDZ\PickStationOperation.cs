﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SiaSun.LMS.Implement.TCP;

namespace SiaSun.LMS.Implement.WDZ
{
    /// <summary>
    /// 拣选工作站操作方法类
    /// </summary>
    public class PickStationOperation : S_BaseService
    {
        /// <summary>
        /// 拣选工作站实例
        /// </summary>
        private Model.T_PICK_STATION mT_PICK_STATION;

        /// <summary>
        /// 拣选工作站下辖拣选点集合（5个）
        /// </summary>
        private List<Model.T_PICK_POSITION> T_PICK_POSITIONs;

        /// <summary>
        /// 与拣选工作站TCP交互的信息报文中
        /// 拣选工作站电气PLC序号
        /// </summary>
        public int PlcNo { get; set; }

        /// <summary>
        /// 与拣选工作站TCP交互的信息报文中
        /// 任务序号
        /// </summary>
        public short TaskNo { get; set; }

        /// <summary>
        /// 与拣选工作站TCP交互的信息报文中
        /// 报文类型
        /// </summary>
        public short Status { get; set; }

        /// <summary>
        /// 与拣选工作站TCP交互的信息报文中
        /// 拣选工作站设备号 例：7103
        /// </summary>
        public int DeviceNo { get; set; }

        /// <summary>
        /// 与拣选工作站TCP交互的信息报文中
        /// 电气报料箱条码 A00001 6位长度
        /// </summary>
        public string StockBarcode { get; set; }

        /// <summary>
        /// 方法类构造方法1
        /// 通过电气报文数据组装方法类各参数
        /// done
        /// </summary>
        /// <param name="args">拣选工作站电控系统报文参数</param>
        public PickStationOperation(TCP.PlcEventArgs args)
        {
            /*
             * 以报文内容组装方法实例属性
             */
            PlcNo = args.PlcNo;
            TaskNo = args.TaskNo;
            Status = args.Status;
            DeviceNo = args.DeviceNo;

            /*
             * 实际测试中发现当报文长度为6时,拣选工作站报上来的长度为10,类似A0001\0\0\0\0
             * 对报文中的箱条码进行截取，解决此问题
             */
            StockBarcode = args.StockBarcode.Substring(0, 6);


            /*
             * 获得拣选工作站实例与下辖拣选工作点实例
             */
            mT_PICK_STATION = this._P_T_PICK_STATION.GetModelByPROPERTY1(PlcNo.ToString());

            if (mT_PICK_STATION != null)
            {
                T_PICK_POSITIONs = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(mT_PICK_STATION.STATION_ID).ToList();
            }
        }

        /// <summary>
        /// 方法类构造方法2
        /// 通过拣选工作站ID组装方法类各参数
        /// done
        /// </summary>
        /// <param name="PICK_STATION_ID">拣选工作站ID</param>
        public PickStationOperation(int PICK_STATION_ID)
        {
            /*
             * 进行拣选任务切换时,是电子标签或者客户端按钮触发，没有电气的报文
             * 无法用构造方法1构造
             * 添加此方法解决此问题
             */
            mT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PICK_STATION_ID);

            if (mT_PICK_STATION != null)
            {
                T_PICK_POSITIONs = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(mT_PICK_STATION.STATION_ID).ToList();
                /*
                 * 将拣选工作站实例的PlcNo和箱条码 组装方法类各属性 
                 */
                PlcNo = Convert.ToInt32(mT_PICK_STATION.PROPERTY1);
                StockBarcode = mT_PICK_STATION.PROPERTY4;
            }
        }

        /// <summary>
        /// 方法类构造方法3
        /// done
        /// </summary>
        public PickStationOperation()
        {

        }
        /// <summary>
        /// 空箱到位
        /// 将箱号与PickPosition绑定
        /// done
        /// 
        /// 
        /// 二期改造
        /// 修改
        /// testing
        /// </summary>
        public void EmptyBoxHasArrivedPickPosition()
        {
            bool bResult = false;
            this._log.Debug(string.Format("[拣选工作站] 空箱到达:PLC:{0} TaskNo:{1} Status:{2} DeviceNo:{3} StockBarcode:{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode));

            try
            {

                this._P_Base_House.BeginTransaction();
                /*
                 * 通过PLC报送的设备号和PLC号,可以找到该拣选点对应的货位
                 * 将空箱的库存放在该货位上
                 */
                Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModelByDeviceCodeAndPlcNo(DeviceNo.ToString(), PlcNo.ToString());//三台拣选工作站地址号相同，需要区分  dzbq_property2 dzbq_property3

                if (mWH_CELL != null)
                {

                    /*
                     * 通过箱条码查找到位箱库存
                     * 如果找不到库存，则生成空箱（emptyBox），并生成在对应的storage_list挂在库存下
                     * 如果找到库存，则将该库存移动到该货位
                     */
                    #region if
                    string validRegexString = string.Empty;
                    if (this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex", out validRegexString) &&
                        !Common.RegexValid.IsValidate(StockBarcode.Trim(), validRegexString))
                    {
                        //传入的条码不符合标准
                        bResult = false;
                        this._log.ErrorFormat("传入的条码：{0}不符合规范", StockBarcode);
                        return;
                    }
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(StockBarcode.Trim());

                    if (mSTORAGE_MAIN == null)
                    {
                        //未找到
                        mSTORAGE_MAIN = new Model.STORAGE_MAIN();
                        mSTORAGE_MAIN.STOCK_BARCODE = StockBarcode.Trim();
                        mSTORAGE_MAIN.CELL_ID = mWH_CELL.CELL_ID;
                        mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.EmptyBox.ToString("d"); //空箱都是1格
                        Model.STORAGE_LIST mSTORAGE_LIST = new Model.STORAGE_LIST();
                        mSTORAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY = 1;
                        mSTORAGE_LIST.ENTRY_TIME = Common.StringUtil.GetDateTime();

                        this._P_STORAGE_MAIN.Add(mSTORAGE_MAIN);

                        mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;
                        this._P_STORAGE_LIST.Add(mSTORAGE_LIST);
                    }
                    else
                    {
                        //通过条码找到库存
                        mSTORAGE_MAIN.CELL_ID = mWH_CELL.CELL_ID;
                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                    }
                    #endregion


                    #region 二期改造
                    if (PlcNo > 3)
                    {
                        Model.T_PICK_POSITION forDisplayPickPosition = this._P_T_PICK_POSITION.GetModelByWH_CELL_ID(mWH_CELL.CELL_ID);
                        if (forDisplayPickPosition != null)
                        {
                            if (!string.IsNullOrEmpty(forDisplayPickPosition.DZBQ_PROPERTY5))
                            {
                                YiChuanPtlDevice.PtlDeviceCommOperation.BoxLeaveDisplay(forDisplayPickPosition.DZBQ_PROPERTY5);
                            }
                        }
                    }
                    #endregion
                    bResult = true;
                    this._log.Debug(string.Format("[拣选工作站] 空箱到达处理程序成功！PLC:{0} TaskNo:{1} Status:{2} DeviceNo:{3} StockBarcode:{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode));

                }

            }
            catch (Exception ex)
            {
                bResult = false;
                this._log.Debug(string.Format("[拣选工作站] 空箱到达处理程序失败！EX009 PLC:{0} TaskNo:{1} Status:{2} DeviceNo:{3} StockBarcode:{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode));

            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult);
            }
        }

        /// <summary>
        /// 满箱到达拣选工作站前的条码扫描
        /// 生成箱对应的拣选任务
        /// done
        /// </summary>
        public void FullBoxHasArrivedBarcodeScaner()
        {
            bool bResult = true;
            string sResult = string.Empty;
            string str = string.Format("[拣选工作站] 满箱到达拣选工作站入口条码扫描:{0}-{1}-{2}-{3}-{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode);
            this._log.Debug(str);
            try
            {

                this._P_Base_House.BeginTransaction();

                /*
                 * 通过电气报箱条码查询库存 
                 */
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(StockBarcode);

                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    return;
                }

                List<Model.STORAGE_LIST> STORAGE_LISTS = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).ToList();

                if (STORAGE_LISTS.Count == 0)
                {
                    bResult = false;
                    return;
                }

                /*
                 * 根据库存查询锁定库存
                 * 通过锁定库存生成拣选任务
                 * 在生成拣选任务前，要判断该箱库存的锁定库存是否是本拣选工作站绑定 
                 */
                foreach (Model.STORAGE_LIST mSTORAGE_LIST in STORAGE_LISTS)
                {
                    List<Model.STORAGE_LOCK> STORAGE_LOCKS = this._P_STORAGE_LOCK.GetList_STORAGE_LIST_ID(mSTORAGE_LIST.STORAGE_LIST_ID).ToList();

                    if (STORAGE_LOCKS.Count == 0)
                    {
                        continue;
                    }

                    foreach (Model.STORAGE_LOCK mSTORAGE_LOCK in STORAGE_LOCKS)
                    {
                        //根据storage_lock生成manage_pick
                        #region foreach
                        /*
                         * 判断锁定库存是否是绑定在此拣选工作站上 
                         */
                        Model.T_PICK_POSITION_PLAN_BIND checkT_PICK_POSITION_PLAN_BIND
                            = this._P_T_PICK_POSITION_PLAN_BIND.GetModelByPICK_STATION_ID_PLAN_LIST_ID(mT_PICK_STATION.STATION_ID, mSTORAGE_LOCK.PLAN_LIST_ID);

                        if (checkT_PICK_POSITION_PLAN_BIND == null)
                        {
                            bResult = false;
                            sResult = string.Format("not found bind information STATION_ID:{0}STORAGE_LOCK_ID:{1}", mT_PICK_STATION.STATION_ID,mSTORAGE_LOCK.STORAGE_LOCK_ID);
                            break;
                        }
                        /*
                         *生成拣选任务 
                         */
                        bResult &= new ManagePick().ManageCreate(mT_PICK_STATION, mSTORAGE_LOCK, false, false, out sResult);
                        if (!bResult)
                        {
                            break;
                        }
                        #endregion
                    }
                    if (!bResult)
                    {
                        break;
                    }
                }

            }
            catch (Exception ex)
            {
                sResult = string.Format("[拣选工作站] 满箱进入拣选工作站条码扫描处理程序异常:{0},EX006", ex.Message);
                bResult = false;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult);

                if (bResult)
                {
                    this._log.Debug(string.Format("[拣选工作站] 生成拣选任务成功！DataIn:{0},信息:{1}", str, sResult));
                }
                else
                {
                    this._log.Debug(string.Format("[拣选工作站] 生成拣选任务失败！DataIn:{0},信息: {1}", str, sResult));
                }
            }
        }

        /// <summary>
        /// 满箱已经到达拣选站台
        /// 呈现拣选任务
        /// 呈现电子标签
        /// done
        /// </summary>
        public void FullBoxHasArrivedPickStation()
        {
            /*
             * 早期拣选工作站测试程序
             * 已经注释
             */

            #region test
            /*
             switch (PlcNo)
             {
                 case 1:
                     DZBQ.DZBQOpration.Send("0011", 5);
                     CommunicationOperation.comm1.LightsStatus(1, new bool[6] { true, false, false, false, false, false }, 0, "1234567890");

                     break;
                 case 2:
                     DZBQ.DZBQOpration.Send("0006", 5);
                     CommunicationOperation.comm2.LightsStatus(2, new bool[6] { true, false, false, false, false, false }, 0, "1234567890");

                     break;
                 case 3:
                     DZBQ.DZBQOpration.Send("0001", 5);
                     CommunicationOperation.comm3.LightsStatus(3, new bool[6] { true, false, false, false, false, false }, 0, "1234567890");

                     break;
             }

             this._log.Debug(string.Format("满箱到达:{0}-{1}-{2}-{3}-{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode));
              */
            #endregion


            /*
             * 正式程序
             * 通过功能测试 
             */
            bool bResult = false;
            string sResult = string.Empty;
            string str = string.Format("[拣选工作站] 满箱到达拣选位置:{0}-{1}-{2}-{3}-{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode);
            this._log.Debug(str);
            try
            {
                this._P_Base_House.BeginTransaction();
                /*
                 * 查询该箱号已经生成的拣选任务,
                 */
                List<Model.MANAGE_MAIN> MANAGE_MAINS = this._P_MANAGE_MAIN.GetListByStockBarcodeAndManageType(StockBarcode, "ManagePick").ToList();

                if (MANAGE_MAINS.Count == 0)
                {
                    /*
                     * 没发生拣选任务，暂时报错
                     * 正式上线时，考虑直接让该箱离开
                     */
                    bResult = false;
                    sResult = string.Format("未找到箱条码{0}对应的拣选任务", StockBarcode);
                    return;
                }
                /*
                 * 对拣选任务进行排序，按照拣选任务目标货位进行排序，拣选点从右向左，设备号从小到大
                 * 取第一个拣选任务进行操作
                 * 控制电子标签显示
                 * 控制灯光显示
                 */

                Model.MANAGE_MAIN sMANAGE_MAIN = MANAGE_MAINS.OrderBy(t => t.END_CELL_ID).ToList()[0];
                bResult = this.OperatingManagePick(sMANAGE_MAIN, out sResult);

                /*
                 * 将已经到达拣选工位的箱号存入拣选工作站信息表中
                 */
                //mT_PICK_STATION.PROPERTY4 = StockBarcode;
                //mT_PICK_STATION.PROPERTY5 = sMANAGE_MAIN.MANAGE_ID.ToString();
                //this._P_T_PICK_STATION.Update(mT_PICK_STATION);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("[拣选工作站] 满箱到达拣选位置处理程序异常:{0},EX005", ex.Message);
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult);

                if (bResult)
                {
                    this._log.Debug(string.Format("[拣选工作站] 满箱到达拣选位置处理程序成功,DataIn:{0},信息:{1}", str, sResult));
                }
                else
                {
                    this._log.Debug(string.Format("[拣选工作站] 满箱到达拣选位置处理程序失败,DataIn:{0},信息: {1}", str, sResult));
                }
            }

        }

        /// <summary>
        /// 12按钮申请离开
        /// 准许离开
        /// done
        /// </summary>
        public void BoxAllowToLeave()
        {
            List<Model.T_PICK_POSITION> result = T_PICK_POSITIONs.Where(t => t.DZBQ_PROPERTY2 == DeviceNo.ToString()).ToList();
            if (result.Count > 0)
            {
                Model.T_PICK_POSITION position = result[0];

                Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(position.WH_CELL_ID);

                if (mWH_CELL != null)
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(StockBarcode);

                    if (mSTORAGE_MAIN != null)
                    {
                        List<Model.STORAGE_LIST> STORAGE_LISTS = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).ToList();

                        if (STORAGE_LISTS.Where(t => t.GOODS_ID == this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID).ToList().Count == 0)
                        {
                            Model.WH_CELL tWH_CELL = this._P_WH_CELL.GetModel(mT_PICK_STATION.PROPERTY2);
                            if (tWH_CELL != null)
                            {
                                mSTORAGE_MAIN.CELL_ID = tWH_CELL.CELL_ID;

                            }
                            mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.KitBox.ToString("d");
                            this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                        }
                        else
                        {
                            /*
                             * 根据现场反映，拣选工作站拣选工作点上的空箱被人工放走，需要将库存立即移走
                             * 以防后面的空箱补位后，页面刷新不出新到空箱
                             */
                            Model.WH_CELL tWH_CELL = this._P_WH_CELL.GetModel(mT_PICK_STATION.PROPERTY2);
                            if (tWH_CELL != null)
                            {
                                mSTORAGE_MAIN.CELL_ID = tWH_CELL.CELL_ID;
                            }
                            //mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.KitBox.ToString("d");
                            this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                        }
                    }
                }

                switch (PlcNo)
                {
                    case 1:
                        CommunicationOperation.comm1.BoxLeave((short)PlcNo, TaskNo, 1, DeviceNo, StockBarcode);// 1 离开
                        break;
                    case 2:
                        CommunicationOperation.comm2.BoxLeave((short)PlcNo, TaskNo, 1, DeviceNo, StockBarcode);// 1 离开
                        break;
                    case 3:
                        CommunicationOperation.comm3.BoxLeave((short)PlcNo, TaskNo, 1, DeviceNo, StockBarcode);// 1 离开
                        break;
                }

            }

            this._log.Debug(string.Format("[拣选工作站] 下箱准许离开:{0}-{1}-{2}-{3}-{4}", PlcNo, TaskNo, Status, DeviceNo, StockBarcode));
        }

        /// <summary>
        /// 操作拣选任务
        /// 通过拣选任务控制灯光与电子标签
        /// done
        /// </summary>
        /// <param name="mMANAGE_MAIN">当前执行的拣选任务</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool OperatingManagePick(Model.MANAGE_MAIN mMANAGE_MAIN, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                this._log.Debug(string.Format("[拣选工作站] 开始根据拣选任务控制灯光和电子标签显示，任务信息:MANAGE_ID:{0},箱码:{1}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE));
                Model.T_PICK_POSITION mT_PICK_POSITION = this._P_T_PICK_POSITION.GetModelByWH_CELL_ID(mMANAGE_MAIN.END_CELL_ID);

                if (mT_PICK_POSITION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作点!");
                    return bResult;
                }

                Model.T_PICK_STATION ssT_PICK_STATION = this._P_T_PICK_STATION.GetModel(mT_PICK_POSITION.STATION_ID);

                if (ssT_PICK_STATION == null)
                {
                    bResult = false;
                    return bResult;
                }

                //预绑定20190218
                this._log.Debug(string.Format("[拣选工作站] begin 预绑定切换{0}-->{1}", ssT_PICK_STATION.PLAN_GROUP_CODE, ssT_PICK_STATION.STATION_MAC));
                if(string.IsNullOrEmpty(ssT_PICK_STATION.PLAN_GROUP_CODE) 
                    && !string.IsNullOrEmpty(ssT_PICK_STATION.STATION_MAC))
                {
                    //预绑定订单原料箱第一次到达拣选工作站拣选工位
                    //切换拣选工作站绑定的唯一码
                    ssT_PICK_STATION.PLAN_GROUP_NAME = ssT_PICK_STATION.STATION_MAC;
                    ssT_PICK_STATION.PLAN_GROUP_CODE = ssT_PICK_STATION.STATION_MAC;
                    ssT_PICK_STATION.FLAG = 1;//拣选状态
                    ssT_PICK_STATION.PROPERTY3 = Enum.FLAG.Disable.ToString("d");
                    ssT_PICK_STATION.STATION_MAC = string.Empty;
                    this._log.Debug(string.Format("[拣选工作站] end 预绑定切换成功{0}", ssT_PICK_STATION.PLAN_GROUP_CODE));

                }
                //预绑定
                ssT_PICK_STATION.PROPERTY5 = mMANAGE_MAIN.MANAGE_ID.ToString();
                ssT_PICK_STATION.PROPERTY4 = mMANAGE_MAIN.STOCK_BARCODE;
                this._P_T_PICK_STATION.Update(ssT_PICK_STATION);
                List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

                /*
                 * 本现场拣选工作站拣选任务明细只能有1条
                 * 将拣选任务明细的主要信息存入拣选工作点中
                 * 以确保电子标签显示，按钮返回
                 */
                if (MANAGE_LISTS.Count != 1)
                {
                    bResult = false;
                    sResult = string.Format("拣选任务明细有错!");
                    return bResult;
                }

                Model.MANAGE_LIST mMANAGE_LIST = MANAGE_LISTS[0];

                mT_PICK_POSITION.DZBQ_SHOW = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                mT_PICK_POSITION.DZBQ_STORE = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                mT_PICK_POSITION.DZBQ_PROPERTY1 = mMANAGE_MAIN.MANAGE_ID.ToString();
                mT_PICK_POSITION.DZBQ_PROPERTY4 = mMANAGE_MAIN.MANAGE_TYPE_CODE;
                mT_PICK_POSITION.DZBQ_FLAG = 1;

                this._P_T_PICK_POSITION.Update(mT_PICK_POSITION);
                int showint = Convert.ToInt32(mMANAGE_LIST.MANAGE_LIST_QUANTITY);
                
                //指挥电子标签显示拣选任务数量

                if (PlcNo <= 3 && PlcNo>=1)
                {
                    // 一期
                    if (mMANAGE_LIST.DETAIL_FLAG != null && mMANAGE_LIST.DETAIL_FLAG.Equals(Enum.FLAG.Enable.ToString("d")))
                    {
                        //序列号拣选
                        DZBQ.DZBQOpration.Send(mT_PICK_POSITION.DZBQ_MAC, showint, 7, 1);
                        this._log.Debug(string.Format("[拣选工作站] 1-3序列号拣选，发送至电子标签显示，任务信息:MANAGE_ID:{0},箱码:{1},DZBQ_MAC:{2},显示数:{3}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE, mT_PICK_POSITION.DZBQ_MAC, showint));
                    }
                    else
                    {
                        DZBQ.DZBQOpration.Send(mT_PICK_POSITION.DZBQ_MAC, showint);
                        this._log.Debug(string.Format("[拣选工作站] 1-3正常拣选，发送至电子标签显示，任务信息:MANAGE_ID:{0},箱码:{1},DZBQ_MAC:{2},显示数:{3}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE, mT_PICK_POSITION.DZBQ_MAC, showint));
                    }
                }

                if (PlcNo >= 4 && PlcNo<=6)
                {
                    // 二期

                    #region 准备二期拣选标签显示内容
                    string goodsCode4Display = string.Empty;
                    string goodsName4Display = string.Empty;
                    string goodsUnit4Display = string.Empty;
                    string description4Display = string.Empty;
                    string wbsInformation4Display = string.Empty;
                    string positionStockBarcode4Display = string.Empty;

                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mMANAGE_LIST.GOODS_ID);
                    if (mGOODS_MAIN != null)
                    {
                        goodsCode4Display = mGOODS_MAIN.GOODS_CODE;
                        goodsName4Display = mGOODS_MAIN.GOODS_NAME;
                        goodsUnit4Display = mGOODS_MAIN.GOODS_UNITS;
                    }

                    Model.T_PICK_POSITION_PLAN_BIND sPickPositionPlanBind =
                        this._P_T_PICK_POSITION_PLAN_BIND.GetModel_By_PICK_POSITION_ID(mT_PICK_POSITION.POSITION_ID);

                    if (sPickPositionPlanBind != null)
                    {
                        Model.PLAN_MAIN sPlanMain = this._P_PLAN_MAIN.GetModel(sPickPositionPlanBind.PLAN_ID);

                        if (sPlanMain != null)
                        {
                            //wbsInformation4Display = string.Format("{0} {1}", sPlanMain.PLAN_CODE, sPlanMain.PLAN_HEADTEXT);
                            wbsInformation4Display = string.Format("{0}", sPlanMain.PLAN_CODE);

                        }
                    }
                    Model.WH_CELL sWH_CELL = this._P_WH_CELL.GetModel(mT_PICK_POSITION.WH_CELL_ID);
                    if (sWH_CELL != null)
                    {
                        Model.STORAGE_MAIN sStorageMain = 
                            this._P_STORAGE_MAIN.GetModelCellID(sWH_CELL.CELL_ID);

                        if (sStorageMain != null)
                        {
                            positionStockBarcode4Display = sStorageMain.STOCK_BARCODE;
                        }
                    }

                    description4Display = string.Format("{0}\r\n{1}\r\n{2}",goodsName4Display,wbsInformation4Display,positionStockBarcode4Display);
                    #endregion

                    if (mMANAGE_LIST.DETAIL_FLAG != null && mMANAGE_LIST.DETAIL_FLAG.Equals(Enum.FLAG.Enable.ToString("d")))
                    {
                        //序列号拣选
                        YiChuanPtlDevice.PtlDeviceCommOperation.PickDisplay(
                            mT_PICK_POSITION.DZBQ_MAC,
                            showint,
                            "SCAN",
                            goodsCode4Display,
                            description4Display,
                            goodsUnit4Display);
                        //DZBQ.DZBQOpration.Send(mT_PICK_POSITION.DZBQ_MAC, showint, 7, 1);
                        this._log.Debug(string.Format("[拣选工作站] 4-6序列号拣选，发送至电子标签显示，任务信息:MANAGE_ID:{0},箱码:{1},DZBQ_MAC:{2},显示数:{3}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE, mT_PICK_POSITION.DZBQ_MAC, showint));
                    }
                    else
                    {
                        YiChuanPtlDevice.PtlDeviceCommOperation.PickDisplay(
                            mT_PICK_POSITION.DZBQ_MAC, 
                            showint, 
                            "PICK",
                            goodsCode4Display,
                            description4Display,
                            goodsUnit4Display);
                        //DZBQ.DZBQOpration.Send(mT_PICK_POSITION.DZBQ_MAC, showint);
                        this._log.Debug(string.Format("[拣选工作站] 4-6正常拣选，发送至电子标签显示，任务信息:MANAGE_ID:{0},箱码:{1},DZBQ_MAC:{2},显示数:{3}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE, mT_PICK_POSITION.DZBQ_MAC, showint));
                    }
                }
               
                //指挥灯光指示
                bResult = LightOperateMethod(mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.CELL_MODEL, mMANAGE_LIST.BOX_BARCODE, out sResult);
                if (bResult)
                {
                    this._log.Debug(string.Format("[拣选工作站] 灯光指示成功，任务信息:MANAGE_ID:{0},箱码:{1},箱型:{2},指示格:{3}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.CELL_MODEL, mMANAGE_LIST.BOX_BARCODE));
                }
                else
                {
                    this._log.Debug(string.Format("[拣选工作站] 灯光指示失败，任务信息:MANAGE_ID:{0},箱码:{1},箱型:{2},指示格:{3}，异常信息:{4}", mMANAGE_MAIN.MANAGE_ID, mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.CELL_MODEL, mMANAGE_LIST.BOX_BARCODE, sResult));
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("[拣选工作站] 拣选任务处理异常! EX001");
                return bResult;
            }
            return bResult;
        }

        /// <summary>
        /// 灯光指示
        /// 通过箱型号与manage_list的box_barcode（所在位置）
        /// 指示灯光显示
        /// 不同型号的箱 与 不同位置 有不同灯光报文
        /// done
        /// </summary>
        /// <param name="STOCK_BARCODE">灯光照射的拣选箱箱号</param>
        /// <param name="CELL_MODEL">箱类型（1，2，3，4，6）格</param>
        /// <param name="BOX_BARCODE">希望照射在第几格（1，2，3，4，5，6）</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool LightOperateMethod(string STOCK_BARCODE, string CELL_MODEL, string BOX_BARCODE, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;

            try
            {
                //获得灯光照射矩阵
                bool[] result;
                if (PlcNo < 4)
                {
                    //一期
                    result = this.GetLightStatus(CELL_MODEL, BOX_BARCODE);
                }
                else
                {
                    //二期
                    result = this.GetLightStatusReverse(CELL_MODEL, BOX_BARCODE);
                }
                //通过PlcNo 下达灯光照射命令
                switch (PlcNo)
                {
                    case 1:
                        bResult = CommunicationOperation.comm1.LightsStatus((short)PlcNo, result, 0, STOCK_BARCODE);
                        break;
                    case 2:
                        bResult = CommunicationOperation.comm2.LightsStatus((short)PlcNo, result, 0, STOCK_BARCODE);
                        break;
                    case 3:
                        bResult = CommunicationOperation.comm3.LightsStatus((short)PlcNo, result, 0, STOCK_BARCODE);
                        break;
                    case 4:
                        bResult = CommunicationOperation.comm4.LightsStatus((short)PlcNo, result, 0, STOCK_BARCODE);
                        break;
                    case 5:
                        bResult = CommunicationOperation.comm5.LightsStatus((short)PlcNo, result, 0, STOCK_BARCODE);
                        break;
                    case 6:
                        bResult = CommunicationOperation.comm6.LightsStatus((short)PlcNo, result, 0, STOCK_BARCODE);
                        break;
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("[拣选工作站] 拣选任务灯光处理异常! EX002");
                return bResult;
            }
            return bResult;
        }

        /// <summary>
        /// 根据箱型和所指示格数
        /// 获得灯光照射报文
        /// done
        /// </summary>
        /// <param name="CELL_MODEL">箱类型（1，2，3，4，6格箱）</param>
        /// <param name="BOX_BARCODE">指示位置（第1，2，3，4，5，6）格</param>
        /// <returns>返回灯光指示报文 例bool[6]{true, true, true, true, true, true} </returns>
        public bool[] GetLightStatus(string CELL_MODEL, string BOX_BARCODE)
        {
            //默认全灭

            /*
             * 0 0 0
             * 0 0 0
             */
            bool[] result = new bool[6] { false, false, false, false, false, false };
            switch (CELL_MODEL)
            {
                case "1":
                    //1格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 1 1 1
                         * 1 1 1
                         */
                        result = new bool[6] { true, true, true, true, true, true };
                    }
                    break;
                case "2":
                    //2格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 1 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { true, false, false, true, false, false };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 0 1
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, true, false, false, true };
                    }
                    break;
                case "3":
                    //3格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 1 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { true, false, false, true, false, false };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 1 0
                         * 0 1 0
                         */
                        result = new bool[6] { false, true, false, false, true, false };
                    }
                    if (BOX_BARCODE == "3")
                    {
                        /*
                         * 0 0 1
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, true, false, false, true };
                    }
                    break;
                case "4":
                    //4格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 1 0 0
                         * 0 0 0
                         */
                        result = new bool[6] { true, false, false, false, false, false };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 0 1
                         * 0 0 0
                         */
                        result = new bool[6] { false, false, true, false, false, false };
                    }
                    if (BOX_BARCODE == "3")
                    {
                        /*
                         * 0 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { false, false, false, true, false, false };
                    }
                    if (BOX_BARCODE == "4")
                    {
                        /*
                         * 0 0 0
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, false, false, false, true };
                    }
                    break;
                case "6":
                    //6格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 1 0 0
                         * 0 0 0
                         */
                        result = new bool[6] { true, false, false, false, false, false };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 1 0
                         * 0 0 0
                         */
                        result = new bool[6] { false, true, false, false, false, false };
                    }
                    if (BOX_BARCODE == "3")
                    {
                        /*
                         * 0 0 1
                         * 0 0 0
                         */
                        result = new bool[6] { false, false, true, false, false, false };
                    }
                    if (BOX_BARCODE == "4")
                    {
                        /*
                         * 0 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { false, false, false, true, false, false };
                    }
                    if (BOX_BARCODE == "5")
                    {
                        /*
                         * 0 0 0
                         * 0 1 0
                         */
                        result = new bool[6] { false, false, false, false, true, false };
                    }
                    if (BOX_BARCODE == "6")
                    {
                        /*
                         * 0 0 0
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, false, false, false, true };
                    }
                    break;
            }
            return result;
        }

        /// <summary>
        /// 根据箱型和所指示格数
        /// 获得灯光照射报文
        /// 用于二期拣选灯光指示
        /// 二期拣选工作站进箱方向与一期相反
        /// </summary>
        /// <param name="CELL_MODEL">箱类型（1，2，3，4，6格箱）</param>
        /// <param name="BOX_BARCODE">指示位置（第1，2，3，4，5，6）格</param>
        /// <returns>返回灯光指示报文 例bool[6]{true, true, true, true, true, true} </returns>
        public bool[] GetLightStatusReverse(string CELL_MODEL, string BOX_BARCODE)
        {
            //默认全灭

            /*
             * 0 0 0
             * 0 0 0
             */
            bool[] result = new bool[6] { false, false, false, false, false, false };
            switch (CELL_MODEL)
            {
                case "1":
                    //1格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 1 1 1
                         * 1 1 1
                         */
                        result = new bool[6] { true, true, true, true, true, true };
                    }
                    break;
                case "2":
                    //2格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 0 0 1
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, true, false, false, true };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 1 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { true, false, false, true, false, false };
                    }
                    break;
                case "3":
                    //3格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 0 0 1
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, true, false, false, true };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 1 0
                         * 0 1 0
                         */
                        result = new bool[6] { false, true, false, false, true, false };
                    }
                    if (BOX_BARCODE == "3")
                    {
                        /*
                         * 1 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { true, false, false, true, false, false };
                    }
                    break;
                case "4":
                    //4格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 0 0 0
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, false, false, false, true };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { false, false, false, true, false, false };
                    }
                    if (BOX_BARCODE == "3")
                    {
                        /*
                         * 0 0 1
                         * 0 0 0
                         */
                        result = new bool[6] { false, false, true, false, false, false };
                    }
                    if (BOX_BARCODE == "4")
                    {
                        /*
                         * 1 0 0
                         * 0 0 0
                         */
                        result = new bool[6] { true, false, false, false, false, false };
                    }
                    break;
                case "6":
                    //6格箱
                    if (BOX_BARCODE == "1")
                    {
                        /*
                         * 0 0 0
                         * 0 0 1
                         */
                        result = new bool[6] { false, false, false, false, false, true };
                    }
                    if (BOX_BARCODE == "2")
                    {
                        /*
                         * 0 0 0
                         * 0 1 0
                         */
                        result = new bool[6] { false, false, false, false, true, false };
                    }
                    if (BOX_BARCODE == "3")
                    {
                        /*
                         * 0 0 0
                         * 1 0 0
                         */
                        result = new bool[6] { false, false, false, true, false, false };
                    }
                    if (BOX_BARCODE == "4")
                    {
                        /*
                         * 0 0 1
                         * 0 0 0
                         */
                        result = new bool[6] { false, false, true, false, false, false };
                    }
                    if (BOX_BARCODE == "5")
                    {
                        /*
                         * 0 1 0
                         * 0 0 0
                         */
                        result = new bool[6] { false, true, false, false, false, false };
                    }
                    if (BOX_BARCODE == "6")
                    {
                        /*
                         * 1 0 0
                         * 0 0 0
                         */
                        result = new bool[6] { true, false, false, false, false, false };
                    }
                    break;
            }
            return result;
        }
        /// <summary>
        /// 拣选工作站相关
        /// 一个箱中的前一条拣选任务完成后，切换到新的拣选任务
        /// done
        /// </summary>
        public void MoveNewManage()
        {
            bool bResult = false;
            string sResult = string.Empty;
            try
            {


                this._P_Base_House.BeginTransaction();
                /*
                * 从拣选工作站中取当前正在拣选的箱号
                * 并以此获得拣选任务
                */
                StockBarcode = mT_PICK_STATION.PROPERTY4;
                List<Model.MANAGE_MAIN> MANAGE_MAINS = this._P_MANAGE_MAIN.GetListByStockBarcodeAndManageType(StockBarcode, "ManagePick").ToList();

                if (MANAGE_MAINS.Count == 0)
                {
                    /*
                     * 如果没有拣选任务，通过拣选工作站PLC，下达箱子离开的指令，并指示灯光熄灭
                     */
                    switch (mT_PICK_STATION.PROPERTY1)
                    {
                        case "1":
                            bResult = TCP.CommunicationOperation.comm1.BoxLeave(1, 1, 1, 7103, StockBarcode);
                            bResult = TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                            break;
                        case "2":
                            bResult = TCP.CommunicationOperation.comm2.BoxLeave(2, 1, 1, 7103, StockBarcode);
                            bResult = TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                            break;
                        case "3":
                            bResult = TCP.CommunicationOperation.comm3.BoxLeave(3, 1, 1, 7103, StockBarcode);
                            bResult = TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                            break;
                        case "4":
                            bResult = TCP.CommunicationOperation.comm4.BoxLeave(4, 1, 1, 7103, StockBarcode);
                            bResult = TCP.CommunicationOperation.comm4.LightsStatus(4, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                            break;
                        case "5":
                            bResult = TCP.CommunicationOperation.comm5.BoxLeave(5, 1, 1, 7103, StockBarcode);
                            bResult = TCP.CommunicationOperation.comm5.LightsStatus(5, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                            break;
                        case "6":
                            bResult = TCP.CommunicationOperation.comm6.BoxLeave(6, 1, 1, 7103, StockBarcode);
                            bResult = TCP.CommunicationOperation.comm6.LightsStatus(6, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                            break;
                    }

                    /*
                     * 如果料箱成功离开，需要将拣选工作站上的箱号清楚
                     * 并且将箱子的货位改成暂存货位，例如60016
                     */
                    if (bResult)
                    {
                        Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(StockBarcode);
                        if (mSTORAGE_MAIN != null)
                        {
                            mT_PICK_STATION.PROPERTY4 = string.Empty;
                            Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(mT_PICK_STATION.PROPERTY2);
                            if (mWH_CELL != null)
                            {
                                mSTORAGE_MAIN.CELL_ID = mWH_CELL.CELL_ID;
                                this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                            }
                            System.Data.DataTable unPickedPlandt
                                = this.GetList(string.Format(@"select * from plan_main where plan_group='{0}' and plan_type_code='PlanPick' 
                            and  plan_id in (select plan_id from plan_list where plan_list_quantity<>plan_list_picked_quantity)", mT_PICK_STATION.PLAN_GROUP_CODE));

                            if (unPickedPlandt != null)
                            {
                                if (unPickedPlandt.Rows.Count == 0)
                                {
                                    //订单完成
                                    mT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                                    mT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                                    mT_PICK_STATION.PROPERTY5 = string.Empty;
                                    mT_PICK_STATION.REMARK = string.Empty;
                                    mT_PICK_STATION.FLAG = 0;//非拣选状态
                                    mT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲

                                }
                            }

                            this._P_T_PICK_STATION.Update(mT_PICK_STATION);

                            //2024改造
                            if (mT_PICK_STATION.PROPERTY9 == "1")
                            {
                                bool onceUpdate = false;
                                foreach (Model.T_PICK_POSITION position in T_PICK_POSITIONs.Where(s => !string.IsNullOrEmpty(s.POSITION_CODE)))
                                {
                                    System.Data.DataTable unPickedPlandtFor
                                   = this.GetList(string.Format(@"select * from plan_main where plan_group='{0}' and plan_type_code='PlanPick' 
                            and  plan_id in (select plan_id from plan_list where plan_list_quantity<>plan_list_picked_quantity)", position.POSITION_CODE));
                                    if (unPickedPlandtFor != null)
                                    {
                                        if (unPickedPlandtFor.Rows.Count == 0)
                                        {
                                            position.POSITION_CODE = string.Empty;
                                            position.REMARK = string.Empty;
                                            this._P_T_PICK_POSITION.Update(position);
                                            onceUpdate = true;
                                        }
                                    }
                                }

                                if (onceUpdate)
                                {
                                    mT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                                    mT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                                    mT_PICK_STATION.PROPERTY5 = string.Empty;
                                    mT_PICK_STATION.REMARK = string.Empty;
                                    mT_PICK_STATION.FLAG = 0;//非拣选状态
                                    mT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲
                                    this._P_T_PICK_STATION.Update(mT_PICK_STATION);
                                }
                            }
                            
                        }

                    }


                }
                else
                {
                    /*
                     * 如果还存在拣选任务，需要继续进行拣选
                     * 根据排序，确定需要执行的拣选任务，并指挥灯光、电子标签
                     */
                    bResult = this.OperatingManagePick(MANAGE_MAINS.OrderBy(t => t.END_CELL_ID).ToList()[0], out sResult);

                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                sResult = string.Format("[拣选工作站] 拣选任务切换处理程序异常:{0},EX004", sResult);
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult);

                if (bResult)
                {
                    this._log.Debug(string.Format("[拣选工作站] 拣选任务切换处理程序成功"));
                }
                else
                {
                    this._log.Debug(string.Format("[拣选工作站] 拣选任务切换处理程序失败,{0}", sResult));
                }
            }
        }


        /// <summary>
        /// 拣选工作站相关
        /// 用户希望将拣选工作站上箱子离开时，使用此方法
        /// 20180127
        /// doing
        /// </summary>
        /// <param name="mT_PICK_STATION">对应拣选工作站实例</param>
        /// <param name="mSYS_USER">下达指令用户</param>
        /// <param name="STOCK_BARCODE">箱码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool LetBoxLeave(Model.T_PICK_STATION mT_PICK_STATION, Model.SYS_USER mSYS_USER, string STOCK_BARCODE, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                if (mT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("传入的拣选工作站实例为空");

                    return bResult;
                }

                switch (mT_PICK_STATION.PROPERTY1)
                {
                    case "1":
                        bResult = TCP.CommunicationOperation.comm1.BoxLeave(1, 1, 1, 7103, STOCK_BARCODE);
                        //bResult = TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                        break;
                    case "2":
                        bResult = TCP.CommunicationOperation.comm2.BoxLeave(2, 1, 1, 7103, STOCK_BARCODE);
                        //bResult = TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                        break;
                    case "3":
                        bResult = TCP.CommunicationOperation.comm3.BoxLeave(3, 1, 1, 7103, STOCK_BARCODE);
                        //bResult = TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                        break;
                    case "4":
                        bResult = TCP.CommunicationOperation.comm4.BoxLeave(4, 1, 1, 7103, STOCK_BARCODE);
                        //bResult = TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                        break;
                    case "5":
                        bResult = TCP.CommunicationOperation.comm5.BoxLeave(5, 1, 1, 7103, STOCK_BARCODE);
                        //bResult = TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                        break;
                    case "6":
                        bResult = TCP.CommunicationOperation.comm6.BoxLeave(6, 1, 1, 7103, STOCK_BARCODE);
                        //bResult = TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, false, false, false, false, false }, 1, StockBarcode);
                        break;
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("方法调用错误，error:{0}", ex.Message);
            }
            finally
            {

            }
            return bResult;
        }
    }

}
