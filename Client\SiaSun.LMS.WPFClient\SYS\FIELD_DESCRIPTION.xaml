﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.FIELD_DESCRIPTION"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="FIELD_DESCRIPTION" Height="300" Width="500" >
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="30"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <GroupBox Name="grpboxModel" Margin="1,2,1,1" Grid.Column="0" Grid.RowSpan="2"   Header="Model对象列表" >
            <Grid>
                <ListBox Name="listBoxModel" Margin="2,8,2,2" MinWidth="150"></ListBox>
            </Grid>           
        </GroupBox>
        <GridSplitter Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Stretch"  FlowDirection="LeftToRight"></GridSplitter>
        <GroupBox Name="grpboxConfig"  Margin="1,2,1,1" Grid.Column="2" Grid.Row="0" Header="Model配置项">
            <uc:DataGridTemplate x:Name="gridModel" Margin="1,5,1,1"></uc:DataGridTemplate>
        </GroupBox>
        <ToolBar Grid.Row="1" Grid.Column="2" ButtonBase.Click="ToolBar_Click">
            <Button Name="btnLoad" Template="{StaticResource templateImageButtonLoad}">加载</Button>
            <Button Name="btnAdd" Template="{StaticResource templateImageButtonAdd}">添加</Button>
            <Button Name="btnDelete" Template="{StaticResource templateImageButtonDelete}">删除</Button>
            <Button Name="btnSave" Template="{StaticResource templateImageButtonSave}">保存</Button>
            <Button Name="btnUpdate" Template="{StaticResource templateImageButtonUpdate}">刷新</Button>
        </ToolBar>
    </Grid>
</ad:DocumentContent>
