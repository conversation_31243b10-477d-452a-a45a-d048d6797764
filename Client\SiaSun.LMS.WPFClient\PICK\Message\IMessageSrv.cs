﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SiaSun.LMS.WPFClient.PICK.Message
{
    public interface IMessageSrv
    {
        
        event Action ConnectionReconnecting;
        event Action ConnectionReconnected;
        event Action ConnectionClosed;
        event Action<string, string, MessageType> NewTextMessage;

        Task ConnectAsync();

        /// <summary>
        /// 已应用
        /// 重构完成
        /// </summary>
        /// <param name="pickStationCode">拣选工作站编码唯一，主键</param>
        /// <param name="userId">登录用户ID</param>
        /// <param name="userCode">用户编码</param>
        /// <param name="userName">用户名</param>
        /// <param name="userRole">用户角色</param>
        /// <returns></returns>
        Task<bool> LoginAsync(string pickStationCode,
            int userId,
            string userCode,
            string userName,
            string userRole);
        Task LogoutAsync();

        Task SendBroadcastMessageAsync(string msg);
    }
}
