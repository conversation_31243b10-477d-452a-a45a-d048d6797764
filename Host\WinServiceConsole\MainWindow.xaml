﻿<Window x:Class="WinServiceConsole.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="MainWindow" ShowInTaskbar="False"  AllowsTransparency="True" WindowStyle="None" Background="Transparent"
        WindowStartupLocation="CenterScreen"  Loaded="Window_Loaded"  mc:Ignorable="d" xmlns:d="http://schemas.microsoft.com/expression/blend/2008" xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" Height="265" Width="284">
    <Window.Resources>
        <Style x:Key="CloseButton" TargetType="{x:Type Button}">
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Margin" Value="0,8,12,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid x:Name="Root" Background="Transparent">
                            <Path x:Name="Ico" HorizontalAlignment="Center" VerticalAlignment="Center" Width="16" Height="16" Fill="#FF4C535C" Data="F1 M 3,4L 5.5,4L 8,6.66667L 10.5,4L 13,4L 9.25,8L 13,12L 10.5,12L 8,9.33333L 5.5,12L 3,12L 6.75,8L 3,4 Z"></Path>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Fill" TargetName="Ico" Value="#FFFF0000"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Fill" TargetName="Ico" Value="#FFFF0000" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
        <Style x:Key="MinButton" TargetType="{x:Type Button}">
            <Setter Property="HorizontalAlignment" Value="Right"/>
            <Setter Property="VerticalAlignment" Value="Top"/>
            <Setter Property="Width" Value="16"/>
            <Setter Property="Height" Value="16"/>
            <Setter Property="Margin" Value="0,8,12,0"/>
            <Setter Property="Template">
                <Setter.Value>
                    <ControlTemplate TargetType="Button">
                        <Grid x:Name="Root" Background="Transparent">
                            <Path x:Name="Ico" HorizontalAlignment="Center" VerticalAlignment="Center" Width="16" Height="16" Fill="#FF4C535C" Data="M 3,8L 3,11L 13,11L 13,8L 3,8 Z"></Path>
                        </Grid>
                        <ControlTemplate.Triggers>
                            <Trigger Property="IsMouseOver" Value="True">
                                <Setter Property="Fill" TargetName="Ico" Value="#FF1BA1E2"/>
                            </Trigger>
                            <Trigger Property="IsPressed" Value="True">
                                <Setter Property="Fill" TargetName="Ico" Value="#FF1BA1E2" />
                            </Trigger>
                        </ControlTemplate.Triggers>
                    </ControlTemplate>
                </Setter.Value>
            </Setter>
        </Style>
    </Window.Resources>
    
    <Border  Width="260" Height="200" >
        <Border.Background>
            <ImageBrush ImageSource="/WinServiceConsole;component/Images/backgroud.png" />
        </Border.Background>
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="10*"/>
                <RowDefinition Height="30*"/>
                <RowDefinition Height="30*" />
                <RowDefinition Height="30*" />
            </Grid.RowDefinitions>

            <Button Style="{DynamicResource MinButton }" Click="btnMinsize_Click"></Button>
            <TextBlock FontStyle="Oblique" FontSize="12" Margin="5,6,55,53" Grid.RowSpan="2">沈阳新松机器人自动化股份有限公司</TextBlock>
            <WrapPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center"  >
                <Button Content="安装服务"  Name="btnInstall"  Click="btnInstall_Click"  HorizontalAlignment="Left" Width="89"  />
                <Button Content="卸载服务"  Name="btnUninstall"  Click="btnUninstall_Click"  HorizontalAlignment="Right" Width="86" />
            </WrapPanel>
            <WrapPanel Grid.Row="2" HorizontalAlignment="Center" VerticalAlignment="Center" >
                <Button Content="启动服务" Height="23" HorizontalAlignment="Left"  Name="btnStart" VerticalAlignment="Top" Width="90" Click="btnStart_Click" />
                <Button Content="停止服务" Height="23" HorizontalAlignment="Left" Name="btnStop" VerticalAlignment="Top" Width="90" Click="btnStop_Click" />
            </WrapPanel>
            <WrapPanel Grid.Row="3" HorizontalAlignment="Center" VerticalAlignment="Center" >
                <Ellipse Height="22" HorizontalAlignment="Left"  Name="serviceStatus" Stroke="#FF27CB27" VerticalAlignment="Top" Width="25" Fill="Black" />
                <Label Content="" Height="28" HorizontalAlignment="Left"  Name="lblText" VerticalAlignment="Top" Width="142" />
            </WrapPanel>
        </Grid>

    </Border>
</Window>
