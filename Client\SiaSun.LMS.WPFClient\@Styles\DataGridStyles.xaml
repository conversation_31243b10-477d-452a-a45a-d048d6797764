﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
        
    <!--默认DataGrid样式-->
    <Style x:Key="styleDefaultDataGrid"  TargetType="DataGrid">
        <Setter Property="Background" Value="{StaticResource ControlBackBrush}"></Setter>
        <Setter Property="RowBackground" Value="#FFFFFF" />
        <Setter Property="AlternatingRowBackground" Value="#EBEBED" />
        <Setter Property="HorizontalGridLinesBrush" Value="#A0A0A0" />
        <Setter Property="HorizontalScrollBarVisibility" Value="Auto" />
        <Setter Property="VerticalScrollBarVisibility" Value="Auto" />
        <Setter Property="CanUserReorderColumns" Value="True" />
        <Setter Property="CanUserResizeColumns" Value="True" />
        <Setter Property="CanUserSortColumns" Value="True" />
        <Setter Property="AutoGenerateColumns" Value="False" />
        <Setter Property="MinRowHeight" Value="23" />
        <Setter Property="SelectionMode" Value="Single" />
        <Setter Property="RowDetailsVisibilityMode" Value="VisibleWhenSelected" />
        <Setter Property="GridLinesVisibility" Value="All"/>
        <Setter Property="HeadersVisibility" Value="All" />
    </Style >

    <!--表头样式-->
    <Style TargetType="DataGridColumnHeader" x:Key="styleDefaultDataGridColoumnHeader" >
        <Setter Property="FontSize" Value="14"/>
        <Setter Property="Foreground" Value="Black"/>
        <Setter Property="MinWidth" Value="0" />
        <Setter Property="MinHeight" Value="28" />
        <Setter Property="Cursor" Value="Hand" />
        <Setter Property="HorizontalContentAlignment" Value="Center"></Setter>
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Setter Property="Padding" Value="5,0,5,0"></Setter>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="0.1,1" StartPoint="0.1,0">
                    <GradientStop Color="WhiteSmoke" Offset="0"/>
                    <GradientStop Color="LightBlue" Offset=".2"/>
                    <GradientStop Color="CornflowerBlue" Offset=".9"/>
                    <GradientStop Color="DarkGray" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="DataGridColumnHeader">
                    <Border BorderThickness="1" BorderBrush="DarkBlue" Background="{TemplateBinding DataGridColumnHeader.Background}">
                        <TextBlock Text="{TemplateBinding Content}" Margin="5,0,5,0" Foreground="{TemplateBinding Foreground}" FontSize="{TemplateBinding FontSize}" VerticalAlignment="Center" HorizontalAlignment="Center"></TextBlock>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--行头-->
    <Style x:Key="styleDefaultDataGridRowHeader" TargetType="DataGridRowHeader">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="0.2,0.8" StartPoint="0.2,0.2">
                    <GradientStop Color="LightBlue" Offset="0"/>
                    <GradientStop Color="White" Offset="1"/>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="BorderThickness" Value="1"></Setter>
        <Setter Property="BorderBrush" Value="LightGray"></Setter>
        <Setter Property="MinWidth" Value="25"></Setter>
    </Style>

    <!--行样式触发-->
    <!--背景色改变必须先设置cellStyle 因为cellStyle会覆盖rowStyle样式-->
    <Style x:Key="styleDefaultDataGridRow"  TargetType="DataGridRow">
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="BorderBrush" Value="LightGreen" />
                <Setter Property="BorderThickness" Value="1" />
            </Trigger>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="PaleGoldenrod"/>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--单元格样式触发-->
    <Style x:Key="styleDefaultDataGridCell" TargetType="DataGridCell">
        <Setter Property="VerticalContentAlignment" Value="Center"></Setter>
        <Style.Triggers>
            <Trigger Property="IsSelected" Value="True">
                <Setter Property="Background" Value="LightGreen"/>
                <Setter Property="BorderThickness" Value="0"/>
                <Setter Property="Foreground" Value="DarkBlue"/>
            </Trigger>
        </Style.Triggers>
    </Style>

</ResourceDictionary>