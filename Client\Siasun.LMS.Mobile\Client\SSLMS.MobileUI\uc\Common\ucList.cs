﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace SSLMS.MobileUI
{
    public partial class ucList : UserControl
    {
        private string _GOODS_TYPE_SQL = @"SELECT GOODS_TYPE.GOODS_TYPE_ID,GOODS_TYPE.GOODS_TYPE_NAME FROM (SELECT DISTINCT {0} FROM {1} WHERE 1=1 {2}) a INNER JOIN GOODS_TYPE ON (a.{0} = GOODS_TYPE.GOODS_TYPE_ID) {3}";

        public string GOODS_TYPE_SQL
        {
            get { return this._GOODS_TYPE_SQL; }
            set { this._GOODS_TYPE_SQL = value; }
        }

        private string _colSplit = string.Empty;

        public string colSplit
        {
            get { return _colSplit; }
            set { _colSplit = value; }
        }

        private string _listXml;

        public string listXml
        {
            get { return _listXml; }
            set { _listXml = value; }
        }

        private bool _bCheck = false;

        public bool bCheck
        {
            get { return _bCheck; }
            set { _bCheck = value; }
        }

        private string _colGroup = string.Empty;

        public string colGroup
        {
            get { return _colGroup; }
            set { _colGroup = value; }
        }

        private string _listTable;

        public string listTable
        {
            get { return _listTable; }
            set { _listTable = value; }
        }

        private string _listWhere;

        public string listWhere
        {
            get { return _listWhere; }
            set { _listWhere = value; }
        }

        private string _listColumn;

        public string listColumn
        {
            get { return _listColumn; }
            set { _listColumn = value; }
        }

        public ucList()
        {
            InitializeComponent();
        }

        public void Init()
        {
            this.tabGoodsType.TabPages.Clear();
            
            this.InitType();
        }

        public void InitType()
        {
            DataTable dtGoodsType = Program._I_PDAService.GetList(string.Format(this._GOODS_TYPE_SQL, this._colGroup, this._listTable, this._listWhere, "ORDER BY a.GOODS_TYPE_ID DESC"));

            for (int i = 0; i < dtGoodsType.Rows.Count; i++)
            {
                string strGoodsType = dtGoodsType.Rows[i]["GOODS_TYPE_ID"].ToString();

                TabPage tp = new TabPage();

                tp.Name = "tbp_" + strGoodsType;

                tp.TabIndex = i;

                tp.Text = dtGoodsType.Rows[i]["GOODS_TYPE_NAME"].ToString();

                this.tabGoodsType.Controls.Add(tp);

                this.InitList(tp, strGoodsType);
            }
        }

        private void InitList(TabPage tbp, string strGoodsType)
        {
            ucGrid ucGrid = new ucGrid();

            tbp.Tag = ucGrid;

            ucGrid.Name = "ucGrid_" + strGoodsType;

            ucGrid.Dock = DockStyle.Fill;

            ucGrid.sTable = this._listTable;

            ucGrid.sWhere = this._listWhere + string.Format(" AND {0}='{1}'", this._colGroup, strGoodsType);

            ucGrid.sXml = this._listXml;

            ucGrid.colSplit = this._colSplit;

            ucGrid.Visible = true;

            ucGrid.TabIndex = 0;

            ucGrid.colGroup = this._colGroup;

            ucGrid.colSplit = this._colSplit;

            ucGrid.bCheck = this._bCheck;

            ucGrid.listColumn = this._listColumn;
            
            tbp.Controls.Add(ucGrid);

            ucGrid.Init();
        }

        //获得选择的列
        public string GetCheckColumns(string sResultColumn, char[] Split)
        {
            string sResult = string.Empty;

            foreach (TabPage tp in this.tabGoodsType.TabPages)
            {
                ucGrid ug = tp.Tag as ucGrid;

                sResult += ug.GetCheckColumns(sResultColumn, Split);
            }

            return sResult.Trim(Split);
        }



        public void SetEditColumns(string sColName, Color c)
        {
            foreach (TabPage tp in this.tabGoodsType.TabPages)
            {
                (tp.Tag as ucGrid).SetEditColumns(sColName, c);
            }
        }
    }
}
