﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// InfoWindow.xaml 的交互逻辑
    /// </summary>
    public partial class InfoWindow : Window
    {
        /// <summary>
        /// 原始数据
        /// </summary>
        public DataTable SourceDataTable = new DataTable();

        /// <summary>
        /// 查询结果
        /// </summary>
        public DataTable ResultDataTable = new DataTable();

        /// <summary>
        /// 返回的选择结果
        /// </summary>
        public string outstring = null;

        public InfoWindow()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化窗口
        /// </summary>
        public void U_Init()
        {
            DataView dv = (DataView)dataGrid1.ItemsSource as DataView;

            SourceDataTable = dv.Table;
            //拷贝架构
            ResultDataTable = SourceDataTable.Clone();

            foreach (DataColumn dataColumn in SourceDataTable.Columns)
            {
                #region Add TextBlock & TextBox
                TextBlock tb = new TextBlock();
                tb.Text = dataColumn.ColumnName + ":";
                tb.Name = dataColumn.ColumnName;
                tb.Margin = new Thickness(2, 5, 3, 0);
                tb.MinWidth = 50;
                tb.Height = 20;
                MyWrapPanel.Children.Add(tb);

                TextBox textBox = new TextBox();
                textBox.Name = dataColumn.ColumnName;
                textBox.Margin = new Thickness(2, 5, 3, 0);
                textBox.Width = 100;
                textBox.Height = 20;
                MyWrapPanel.Children.Add(textBox);
                #endregion
            }

            #region AddButton
            Button button = new Button();
            button.Name = "Query";
            button.Width = 70;
            button.Height = 20;
            button.Margin = new Thickness(5, 5, 0, 0);
            button.Content = "检索";
            button.Click += new RoutedEventHandler(QueryButtonClick);
            MyWrapPanel.Children.Add(button);

            Button buttonOther = new Button();
            buttonOther.Name = "Reset";
            buttonOther.Width = 70;
            buttonOther.Height = 20;
            buttonOther.Margin = new Thickness(5, 5, 0, 0);
            buttonOther.Content = "重置";
            buttonOther.Click += new RoutedEventHandler(ResetButtonClick);
            MyWrapPanel.Children.Add(buttonOther);
            #endregion

        }

        /// <summary>
        /// 生成搜索语句
        /// </summary>
        private void QueryStringBuilder(out string resultstring)
        {
            StringBuilder stringBuilder = new StringBuilder();

            for (int v = 0; v < VisualTreeHelper.GetChildrenCount(MyWrapPanel); v++)
            {
                DependencyObject element = VisualTreeHelper.GetChild(MyWrapPanel, v);

                if (element.GetType().Name == "TextBox")
                {
                    TextBox textBox = (TextBox)element as TextBox;

                    stringBuilder.AppendFormat("{0} like '%{1}%'", textBox.Name, textBox.Text);
                    stringBuilder.Append(" and ");
                }

            }
            stringBuilder.Append("1=1");
            resultstring = stringBuilder.ToString();
        }

        /// <summary>
        /// 查询按钮事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void QueryButtonClick(object sender, RoutedEventArgs e)
        {
            string str = null;
            QueryStringBuilder(out str);

            //DataRow[] drlist = SourceDataTable.Select("Value like '%1%'");
            DataRow[] drlist = SourceDataTable.Select(str);

            ResultDataTable.Rows.Clear();

            foreach (DataRow dr in drlist)
            {
                ResultDataTable.ImportRow(dr);
            }
            dataGrid1.ItemsSource = ResultDataTable.DefaultView;
        }

        /// <summary>
        /// 重置按钮事件
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void ResetButtonClick(object sender, RoutedEventArgs e)
        {

            for (int v = 0; v < VisualTreeHelper.GetChildrenCount(MyWrapPanel); v++)
            {
                DependencyObject element = VisualTreeHelper.GetChild(MyWrapPanel, v);

                if (element.GetType().Name == "TextBox")
                {
                    TextBox textBox = (TextBox)element as TextBox;
                    textBox.Text = "";
                    element = textBox;
                }
            }
            dataGrid1.ItemsSource = SourceDataTable.DefaultView;
        }

        private void dataGrid1_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            StringBuilder stringBuilder = new StringBuilder();

            DataRowView drv = (DataRowView)dataGrid1.SelectedItem as DataRowView;

            for (int f = 0; f < drv.DataView.Table.Columns.Count; f++)
            {
                stringBuilder.AppendFormat("{0} = '{1}'", drv.DataView.Table.Columns[f].ColumnName, drv.Row[f].ToString());
                if (f < (drv.DataView.Table.Columns.Count - 1))
                {
                    stringBuilder.Append(" and ");
                }
            }

            string querystr = stringBuilder.ToString();
            DataRow[] drlist = SourceDataTable.Select(querystr);
            if (drlist.Length > 0)
            {
                outstring = SourceDataTable.Rows.IndexOf(drlist[0]).ToString();
                this.Close();
            }
        }
    }
}
