﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_TABLE_CONVERTER 
	/// </summary>
    [Serializable]
    [DataContract]
	public class SYS_TABLE_CONVERTER
	{
		public SYS_TABLE_CONVERTER()
		{
			
		}
		
		private int _table_converter_id;
		private string _table_converter_code;
		private string _table_converter_name;
		private string _parent_table;
		private string _parent_key;
		private string _child_table;
		private string _child_foreign_key;
		private int _goods_type_id;
		private string _remark;
		
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int TABLE_CONVERTER_ID
		{
			get{return _table_converter_id;}
			set{_table_converter_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string TABLE_CONVERTER_CODE
		{
			get{return _table_converter_code;}
			set{_table_converter_code = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string TABLE_CONVERTER_NAME
		{
			get{return _table_converter_name;}
			set{_table_converter_name = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string PARENT_TABLE
		{
			get{return _parent_table;}
			set{_parent_table = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string PARENT_KEY
		{
			get{return _parent_key;}
			set{_parent_key = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CHILD_TABLE
		{
			get{return _child_table;}
			set{_child_table = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CHILD_FOREIGN_KEY
		{
			get{return _child_foreign_key;}
			set{_child_foreign_key = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public int GOODS_TYPE_ID
		{
            get { return _goods_type_id; }
            set { _goods_type_id = value; }
		}
		///<sumary>
		/// 


		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string REMARK
		{
			get{return _remark;}
			set{_remark = value;}
		}
	}
}
