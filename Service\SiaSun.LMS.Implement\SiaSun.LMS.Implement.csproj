﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.Implement</RootNamespace>
    <AssemblyName>SiaSun.LMS.Implement</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="IBatisNet.Common">
      <HintPath>..\..\Lib\IBatisNet.Common.dll</HintPath>
    </Reference>
    <Reference Include="IBatisNet.DataMapper">
      <HintPath>..\..\Lib\IBatisNet.DataMapper.dll</HintPath>
    </Reference>
    <Reference Include="log4net">
      <HintPath>..\..\Lib\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Oracle.ManagedDataAccess">
      <HintPath>..\..\Lib\Oracle.ManagedDataAccess.dll</HintPath>
    </Reference>
    <Reference Include="Ptl.Device">
      <HintPath>..\..\Lib\Ptl.Device.dll</HintPath>
    </Reference>
    <Reference Include="RYB_PTL_API">
      <HintPath>..\..\Lib\RYB_PTL_API.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.EnterpriseServices" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Apply\ApplyAllocateArea.cs" />
    <Compile Include="Apply\ApplyAllocateCell3.cs" />
    <Compile Include="Apply\ApplyBase.cs" />
    <Compile Include="Apply\ApplyAllocateFloor.cs" />
    <Compile Include="Apply\ApplyAllocateCellThirdFloor.cs" />
    <Compile Include="Apply\ApplyEmptyIn3.cs" />
    <Compile Include="Apply\ApplyRcs.cs" />
    <Compile Include="Apply\ApplyVerify.cs" />
    <Compile Include="Apply\ApplyAllocateCell.cs" />
    <Compile Include="Control\ControlBase%28弃用%29.cs" />
    <Compile Include="DZBQ\DZBQ.cs" />
    <Compile Include="DZBQ\DZBQOpration.cs" />
    <Compile Include="Interface\WCS\KitOutComplete.cs" />
    <Compile Include="Interface\WCS\z_SiasunWCSService.cs" />
    <Compile Include="Interface\WES\MiniloadSortTaskCancel.cs" />
    <Compile Include="Interface\WES\CancelMiniloadSortTask.cs" />
    <Compile Include="Interface\WMS\noneMiniloadTranResultReturnFromWCS.cs" />
    <Compile Include="Manage\ManageManualPick.cs" />
    <Compile Include="Manage\ManagePick.cs" />
    <Compile Include="Plan\PlanPick.cs" />
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
      <DependentUpon>Settings.settings</DependentUpon>
    </Compile>
    <Compile Include="WDZ\MessageProxy.cs" />
    <Compile Include="YiChuanPtlDevice\PtlDeviceComm.cs" />
    <Compile Include="Storage\List.cs" />
    <Compile Include="S_LCDService.cs" />
    <Compile Include="S_GoodsService.cs" />
    <Compile Include="Interface\WMS\miniloadPickConfirmFromWCS.cs" />
    <Compile Include="Interface\WMS\neatBoxInfoReceiveFromWCS.cs" />
    <Compile Include="Interface\WMS\urgentBoxReceiveFromWCS.cs" />
    <Compile Include="Interface\WMS\countResultReturnFromWCS.cs" />
    <Compile Include="Interface\WMS\handleResultReturnFromWCS.cs" />
    <Compile Include="Interface\WES\ArrangeEmptyBoxTask.cs" />
    <Compile Include="Interface\WES\BoxReturnInTask.cs" />
    <Compile Include="Interface\WES\MaterialSyn.cs" />
    <Compile Include="Interface\WES\KitBoxReturnInTask.cs" />
    <Compile Include="Interface\WES\KitBoxOutTask.cs" />
    <Compile Include="Interface\WES\EmergSortOutTask.cs" />
    <Compile Include="Interface\WES\MiniloadSortTask.cs" />
    <Compile Include="Interface\WES\StockCheckTask.cs" />
    <Compile Include="Interface\WES\EmptyBoxIn.cs" />
    <Compile Include="Interface\WES\EmptyBoxOut.cs" />
    <Compile Include="Interface\WES\CreateBoxIn.cs" />
    <Compile Include="Interface\InterfaceBase.cs" />
    <Compile Include="Interface\WES\NoneMiniloadTrans.cs" />
    <Compile Include="Manage\ManageBase.cs" />
    <Compile Include="Manage\ManageDown.cs" />
    <Compile Include="Manage\ManageTrans.cs" />
    <Compile Include="Manage\ManageIn.cs" />
    <Compile Include="Manage\ManageMove.cs" />
    <Compile Include="Manage\ManageOut.cs" />
    <Compile Include="Manage\ManageStockIn.cs" />
    <Compile Include="Manage\ManageUp.cs" />
    <Compile Include="Manage\ManageProductOut.cs" />
    <Compile Include="Plan\PlanBase.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Storage\StorageBase.cs" />
    <Compile Include="Storage\Storage_LFS.cs" />
    <Compile Include="S_BaseService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_LEDService.cs" />
    <Compile Include="S_StorageService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_ManageService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_PlanService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_SystemService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_FlowService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_CellService.cs">
      <SubType>Code</SubType>
    </Compile>
    <Compile Include="S_WESJsonService.cs" />
    <Compile Include="TCP\Communication.cs" />
    <Compile Include="TCP\CommunicationOperation.cs" />
    <Compile Include="TCP\Protocol.cs" />
    <Compile Include="TCP\Protocol.Plc.cs" />
    <Compile Include="TCP\Protocol.Wms.cs" />
    <Compile Include="TCP\TcpSocket.cs" />
    <Compile Include="Template\TemplateBase.cs" />
    <Compile Include="S_PDAService.cs" />
    <Compile Include="WDZ\PickPositionOperation.cs" />
    <Compile Include="WDZ\PickStationOperation.cs" />
    <Compile Include="YiChuanPtlDevice\PtlDeviceCommOperation.cs" />
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj">
      <Project>{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}</Project>
      <Name>SiaSun.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Interface\SiaSun.LMS.Interface.csproj">
      <Project>{612D8C56-ECEC-48B9-87F9-AF80BF81E070}</Project>
      <Name>SiaSun.LMS.Interface</Name>
    </ProjectReference>
    <ProjectReference Include="..\SiaSun.LMS.Persistence\SiaSun.LMS.Persistence.csproj">
      <Project>{B820FA47-3514-49DB-8D33-96367A80234E}</Project>
      <Name>SiaSun.LMS.Persistence</Name>
    </ProjectReference>
  </ItemGroup>
  <ItemGroup>
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>