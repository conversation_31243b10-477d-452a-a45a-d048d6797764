﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucWareHouseQuery.xaml 的交互逻辑
    /// </summary>
    public partial class ucWareHouseQuery : UserControl
    {
        #region     ------定义变量属性

        public delegate void U_WareHouseChangedHandler();
        public event U_WareHouseChangedHandler U_WareHouseChanged;

        string strCellType = string.Empty;
        string strCellInOut = string.Empty;
        string strAreaGroup = string.Empty;
        string strLogicGroup = string.Empty;

        bool boolAllowAreaNull = false;
        bool boolAllowCellZNull = true;

        /// <summary>
        /// 是否显示库区
        /// </summary>
        public bool U_AllowShowArea
        {
            get { return (this.panelArea.Visibility == System.Windows.Visibility.Visible); }
            set { this.panelArea.Visibility = value ? Visibility.Visible : Visibility.Collapsed; }
        }
        
        /// <summary>
        /// 是否显示排信息
        /// </summary>
        public bool U_AllowShowCellZ
        {
            get { return (this.panelLine.Visibility == System.Windows.Visibility.Visible); }
            set { this.panelLine.Visibility = value ? Visibility.Visible : Visibility.Collapsed; }
        }

        /// <summary>
        /// 是否允许库区空值
        /// </summary>
        public bool U_AllowAreaNull
        {
            get { return boolAllowAreaNull; }
            set { boolAllowAreaNull = value; }
        }

        /// <summary>
        /// 是否允许CELL_Z空值
        /// </summary>
        public bool U_AllowCellZNull
        {
            get { return boolAllowCellZNull; }
            set { boolAllowCellZNull = value; }
        }

        /// <summary>
        /// 货位类型
        /// </summary>
        public string U_CELL_TYPE
        {
            set { strCellType = value; }
        }

        /// <summary>
        /// 出/入库类型
        /// </summary>
        public string U_CELL_INOUT
        {
            set { strCellInOut = value; }
        }

        /// <summary>
        /// 库区分组
        /// </summary>
        public string U_AREA_GROUP
        {
            set { strAreaGroup = value; }
        }

        /// <summary>
        /// 仓库编号
        /// </summary>
        public int U_WAREHOUSE_ID
        {
            get { return Convert.ToInt32(cmbWare.SelectedValue); }
            set { this.cmbWare.SelectedValue = value; }
        }

        /// <summary>
        /// 库区编号
        /// </summary>
        public int U_AREA_ID
        {
            get { return this.cmbArea.SelectedValue == null ? 0 : Convert.ToInt32(this.cmbArea.SelectedValue); }
            set { this.cmbArea.SelectedValue = value; }
        }

        /// <summary>
        /// CELL_Z-所属排
        /// </summary>
        public int U_CELL_Z
        {
            get { return Convert.ToInt32(this.cmbLine.SelectedValue); }
            set { this.cmbLine.SelectedValue = value; }
        }

        #endregion

        /// <summary>
        /// 构造函数
        /// </summary>
        public ucWareHouseQuery()
        {
            InitializeComponent();
        }

        #region     ------仓库

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl(string WAREHOUSE_TYPE)
        {
            WareHouse_Bind(WAREHOUSE_TYPE);
        }

        /// <summary>
        /// 加载仓库信息
        /// </summary>
        private void WareHouse_Bind(string WAREHOUSE_TYPE)
        {
            this.cmbWare.DisplayMemberPath = "WAREHOUSE_NAME";
            this.cmbWare.SelectedValuePath = "WAREHOUSE_ID";
            try
            {

                 this.cmbWare.ItemsSource = MainApp._I_CellService.WAREHOUSE_GetList(MainApp._USER.USER_ID, WAREHOUSE_TYPE).DefaultView;
                

                if (this.cmbWare.Items.Count > 0)
                {
                    this.cmbWare.SelectedIndex = this.cmbWare.SelectedIndex < 0 ? 0 : this.cmbWare.SelectedIndex;
                    
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //选择仓库
        void cmbWare_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.Source == null)
                return;

            //加载库区
            int intWarehouseID = Convert.ToInt32(this.cmbWare.SelectedValue);

            this.Area_Bind(intWarehouseID);

            int intAreaID = Convert.ToInt32(this.cmbArea.SelectedValue);

            //this.CellZ_Bind(intWarehouseID);
            this.CellZ_Bind(intWarehouseID,intAreaID);

            //激活事件
            if (this.cmbWare.SelectedValue != null)
            {
                if (this.U_WareHouseChanged != null)
                {
                    this.U_WareHouseChanged();
                }
            }
        }

        #endregion

        #region     ------库区
        /// <summary>
        /// 加载库区信息
        /// </summary>
        private void Area_Bind(int WAREHOUSE_ID)
        {
            if (this.U_AllowShowArea)
            {
                this.cmbArea.DisplayMemberPath = "AREA_NAME";
                this.cmbArea.SelectedValuePath = "AREA_ID";
                try
                {
                    //if (strAreaGroup.Length > 0)
                    //{
                    //    this.cmbArea.ItemsSource = MainApp._I_CellService.AREA_GetList_GROUP(WAREHOUSE_ID, Convert.ToInt32(strAreaGroup));
                    //}
                    //else
                    //{
                    this.cmbArea.ItemsSource = MainApp._I_CellService.AREA_GetList(WAREHOUSE_ID,"LiKu").DefaultView;
                    //}

                    //是否允许空
                    if (boolAllowAreaNull)
                    {
                        this.cmbArea.SelectedIndex = -1;
                    }
                    else
                    {
                        if (this.cmbArea.Items.Count > 0)
                        {
                            this.cmbArea.SelectedIndex = 0;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        //选择库区
        void cmbArea_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (e.Source == null)
                return;
            int intWarehouseID = Convert.ToInt32(this.cmbWare.SelectedValue);
            int intAreaID = Convert.ToInt32(this.cmbArea.SelectedValue);
            //this.CellZ_Bind(intAreaID);
            this.CellZ_Bind(intWarehouseID,intAreaID);

            if (this.U_AllowShowArea)
            {
                if (this.cmbArea.SelectedItem != null)
                {
                    if (this.U_WareHouseChanged != null)
                    {
                        this.U_WareHouseChanged();
                    }
                }
            }
        }

        #endregion

        #region     ------排

        /// <summary>
        /// 加载排信息
        /// </summary>
        private void CellZ_Bind(int WAREHOUSE_ID)
        {
            if (this.U_AllowShowCellZ)
            {
                this.cmbLine.DisplayMemberPath = "CELL_Z_NAME";
                this.cmbLine.SelectedValuePath = "CELL_Z";
                try
                {
                    this.cmbLine.ItemsSource = MainApp._I_CellService.CELL_Z_GetList(WAREHOUSE_ID).DefaultView;

                    //是否允许空
                    if (boolAllowCellZNull)
                    {
                        this.cmbLine.SelectedIndex = -1;
                    }
                    else
                    {
                        if (this.cmbLine.Items.Count > 0)
                        {
                            this.cmbLine.SelectedIndex = 0;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        /// <summary>
        /// 加载排信息
        /// </summary>
        private void CellZ_Bind(int WAREHOUSE_ID,int AREA_ID)
        {
            if (this.U_AllowShowCellZ)
            {
                this.cmbLine.DisplayMemberPath = "CELL_Z_NAME";
                this.cmbLine.SelectedValuePath = "CELL_Z";
                try
                {
                    //this.cmbLine.ItemsSource = MainApp._I_CellService.CELL_Z_GetList(WAREHOUSE_ID).DefaultView;
                    this.cmbLine.ItemsSource = MainApp._I_CellService.CELL_Z_GetList_AREA(WAREHOUSE_ID, AREA_ID).DefaultView;

                    //是否允许空
                    if (boolAllowCellZNull)
                    {
                        this.cmbLine.SelectedIndex = -1;
                    }
                    else
                    {
                        if (this.cmbLine.Items.Count > 0)
                        {
                            this.cmbLine.SelectedIndex = 0;
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }
        //选择排
        void cmbLine_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.U_AllowShowCellZ)
            {
                if (this.cmbLine.SelectedItem != null)
                {
                    if (this.U_WareHouseChanged != null)
                    {
                        this.U_WareHouseChanged();
                    }
                }
            }
        }

        #endregion
    }
}
