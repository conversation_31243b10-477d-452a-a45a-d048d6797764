﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="MID_GOODS" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="MID_GOODS" type="SiaSun.LMS.Model.MID_GOODS, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="MID_GOODS">
      <result property="GOODS_ID" column="goods_id" />
      <result property="GOODS_CODE" column="goods_code" />
      <result property="GOODS_NAME" column="goods_name" />
      <result property="GOODS_PROPERTY" column="goods_property" />
      <result property="FMODIFYTIME" column="fmodifytime" />
    </resultMap>
  </resultMaps>

  <statements>
    <select id="MID_GOODS_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      goods_id,
      goods_code,
      goods_name,
      goods_property,
      fmodifytime
      From MID_GOODS
    </select>
    
    <select id="MID_GOODS_SELECT_BY_GOODS_ID" parameterClass="int" extends = "MID_GOODS_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_id=#GOODS_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MID_GOODS_SELECT_BY_GOODS_CODE" parameterClass="string" extends = "MID_GOODS_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_code=#GOODS_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <insert id="MID_GOODS_INSERT" parameterClass="MID_GOODS">
      Insert Into MID_GOODS (
      goods_id,
      goods_code,
      goods_name,
      goods_property,
      fmodifytime
      )Values(
      #GOODS_ID#,
      #GOODS_CODE#,
      #GOODS_NAME#,
      #GOODS_PROPERTY#,
      #FMODIFYTIME#
      )
      <selectKey  resultClass="int" type="post" property="GOODS_ID">
        select @@IDENTITY as value
      </selectKey>
    </insert>
    
    <update id="MID_GOODS_UPDATE" parameterClass="MID_GOODS">
      Update MID_GOODS Set
      goods_id=#GOODS_ID#,
      goods_code=#GOODS_CODE#,
      goods_name=#GOODS_NAME#,
      goods_property=#GOODS_PROPERTY#,
      fmodifytime=#FMODIFYTIME#,
      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_id=#GOODS_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="MID_GOODS_DELETE" parameterClass="int">
      Delete From MID_GOODS
      <dynamic prepend="WHERE">
        <isParameterPresent>
          goods_id=#GOODS_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>