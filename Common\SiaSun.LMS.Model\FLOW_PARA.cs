﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// FLOW_PARA 
	/// </summary>
    [Serializable]
    [DataContract]
	public class FLOW_PARA
	{
		public FLOW_PARA()
		{
			
		}
		
		private int _flow_para_id;
		private int _flow_type_id;
		private string _flow_para_code;
		private string _flow_para_name;
		private string _flow_para_remark;
		private int _flow_para_order;
		private string _flow_para_flag;
		
		///<sumary>
		/// 流程参数编号
        ///</sumary>
        [DataMember]
		public int FLOW_PARA_ID
		{
			get{return _flow_para_id;}
			set{_flow_para_id = value;}
		}
		///<sumary>
		/// 流程类型编号
        ///</sumary>
        [DataMember]
		public int FLOW_TYPE_ID
		{
			get{return _flow_type_id;}
			set{_flow_type_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string FLOW_PARA_CODE
		{
			get{return _flow_para_code;}
			set{_flow_para_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string FLOW_PARA_NAME
		{
			get{return _flow_para_name;}
			set{_flow_para_name = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string FLOW_PARA_REMARK
		{
			get{return _flow_para_remark;}
			set{_flow_para_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int FLOW_PARA_ORDER
		{
			get{return _flow_para_order;}
			set{_flow_para_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string FLOW_PARA_FLAG
		{
			get{return _flow_para_flag;}
			set{_flow_para_flag = value;}
		}
	}
}
