﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using System.Windows.Media;

namespace SiaSun.LMS.WPFClient.PICK.Model
{
    public class Box : MVVM.ObservableObject
    {
        private string _stock_barcode;
        private Box_Type _type;
        private int _part_count;
        private int _part_row_num;
        private int _part_column_num;
        private List<Part> _parts = new List<Part>();
        private int _select_part_index;
        private List<Brush> _partBackGround = new List<Brush>();

        public delegate void ChangeBackGrounddelegate();

        public event ChangeBackGrounddelegate OnChangeBackGround;

        /// <summary>
        /// Stock_barcode
        /// </summary>
        public string Stock_barcode
        {
            get
            {
                return _stock_barcode;
            }

            set
            {
                
                if (_stock_barcode != value)
                {
                    _stock_barcode = value;
                    RaisePropertyChanged("Stock_barcode");
                }
            }
        }

        /// <summary>
        /// Type
        /// </summary>
        public Box_Type Type
        {
            get
            {
                return _type;
            }

            set
            {
                _type = value;
            }
        }

        /// <summary>
        /// Part_count
        /// </summary>
        public int Part_count
        {
            get
            {
                return _parts.Count();
            }

            set
            {
                _part_count = value;
            }
        }

        /// <summary>
        /// Part_row_num
        /// </summary>
        public int Part_row_num
        {
            get
            {
                return _part_row_num;
            }

            set
            {
                _part_row_num = value;
            }
        }

        /// <summary>
        /// Part_column_num
        /// </summary>
        public int Part_column_num
        {
            get
            {
                return _part_column_num;
            }

            set
            {
                _part_column_num = value;
            }
        }

        /// <summary>
        /// Parts
        /// </summary>
        public List<Part> Parts
        {
            get
            {
                return _parts;
            }

            set
            {
                _parts = value;
            }
        }

        /// <summary>
        /// Select_part_index
        /// </summary>
        public int Select_part_index
        {
            get
            {
                return _select_part_index;
            }

            set
            {
                if (_select_part_index != value)
                {
                    if (OnChangeBackGround != null)
                    {
                        _select_part_index = value;
                        OnChangeBackGround();
                    }
                }
                else
                {
                    _select_part_index = value;
                    OnChangeBackGround();
                }
            }
        }

        /// <summary>
        /// Select_part
        /// </summary>
        public Part Select_part
        {
            get
            {
                return this.Parts[this._select_part_index];
            }
        }

        public List<Brush> PartBackGround
        {
            get
            {
                return _partBackGround;
            }

            set
            {
                if (_partBackGround != value)
                {
                    _partBackGround = value;
                    RaisePropertyChanged("PartBackGround");
                }
            }
        }

        public Box()
        {

        }

        public Box(int part_row_num, int part_column_num)
        {
            this._part_row_num = part_row_num;
            this._part_column_num = part_column_num;
            Init_Parts();
        }

        public Box(int part_row_num, int part_column_num, Box_Type type):this(part_row_num, part_column_num)
        {
            this._type = type;
        }

        public Box(int part_row_num, int part_column_num, Box_Type type,string stockbarcode) : this(part_row_num, part_column_num,type)
        {
            this._stock_barcode = stockbarcode;
        }

        public void Init_Parts()
        {
            int index = 0;
            for(int r = 1; r <= this._part_row_num; r++)
            {
                for(int c = 1; c <= this._part_column_num; c++)
                {
                    Part part = new Part(r, c);
                    part.Index = index;
                    index++;
                    this._parts.Add(part);
                }
            }
        }

        public Part Select()
        {
            Part sPart;
            sPart = this.Parts[this.Select_part_index];
            sPart.IsSelected = true;
            return sPart;
        }
        
    }

    public enum Box_Type
    {
        A = 1,
        B = 2,
        C = 3,
        D = 4,
        E = 6,
        AR=-1,
        BR=-2,
        CR=-3,
        DR=-4,
        ER=-6
    }
}
