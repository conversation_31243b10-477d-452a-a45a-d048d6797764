﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.TEMPLATE
{
    public partial class MANAGE_PRODUCT_OUT : AvalonDock.DocumentContent
    {
        int intPlanID = 0;

        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        string STORAGE_AREA_TYPE = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_PRODUCT_OUT( string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitManagePosotion();

            this.GOODS_BIND();

            this.cmbGoods.SelectionChanged += new SelectionChangedEventHandler
                                   ((cmbGoodsSender, cmbGoodsE) =>
                                      {
                                          if (this.cmbGoods.SelectedValue != null)
                                          {
                                              IList<SiaSun.LMS.Model.GOODS_TEMPLATE> listGOODS_TEMPLATE = MainApp._I_GoodsService.GoodsTemplateGetList(Convert.ToInt32(this.cmbGoods.SelectedValue));

                                              this.cmbTemplate.DisplayMemberPath = "GOODS_TEMPLATE_NAME";
                                              this.cmbTemplate.SelectedValuePath = "GOODS_TEMPLATE_ID";
                                              this.cmbTemplate.ItemsSource = listGOODS_TEMPLATE;
                                          }
                                      }
                                     );


            this.cmbTemplate.SelectionChanged += new SelectionChangedEventHandler
                                    ((cmbTemplateSender, cmbTemplateE) =>
                                      {
                                          if (this.cmbTemplate.SelectedValue != null)
                                          {

                                              this.TemplateList_Bind(Convert.ToInt32(this.cmbTemplate.SelectedValue));
                                          }
                                      }
                                    );
        }



        private void GOODS_BIND()
        {
            try
            {

                IList<SiaSun.LMS.Model.GOODS_MAIN> listGOODS_MAIN = MainApp._I_GoodsService.GoodsGetListGoodsClassID(3);

                this.cmbGoods.DisplayMemberPath = "GOODS_NAME";
                this.cmbGoods.SelectedValuePath = "GOODS_ID";
                this.cmbGoods.ItemsSource = listGOODS_MAIN;
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }



        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }

        private void TemplateList_Bind(int TEMPLATE_ID)
        {
            //数据源属性
            this.gridTemplateList.U_Clear();
            this.gridTemplateList.U_WindowName = this.GetType().Name;
            this.gridTemplateList.U_TableName = "V_GOODS_TEMPLATE_LIST";
            this.gridTemplateList.U_XmlTableName = "V_GOODS_TEMPLATE_LIST";
            this.gridTemplateList.U_Fields = "*";
            this.gridTemplateList.U_Where = string.Format("GOODS_TEMPLATE_ID  ={0}", TEMPLATE_ID);
            this.gridTemplateList.U_OrderField = "GOODS_TEMPLATE_LIST_ID";

            this.gridTemplateList.U_AllowChecked = true;
            this.gridTemplateList.U_AllowOperatData = false;
            this.gridTemplateList.U_AllowShowPage = false;

            //拆分列属性
            this.gridTemplateList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridTemplateList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridTemplateList.U_SplitPropertyColumn = "GOODS_PROPERTY";
            this.gridTemplateList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";

            //明细属性
            //this.gridPlanList.U_DetailTableName = "PLAN_DETAIL";
            //this.gridPlanList.U_DetailRelatvieColumn = "PLAN_LIST_ID";

            try
            {
                this.gridTemplateList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }



        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnCallTemplate":
                        this.CreateTask();
                        break;
                    case "btnPalletBack":

                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = false;

            string strResult = string.Empty;

            try
            {
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                    mMANAGE_MAIN.STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE;

                    mMANAGE_MAIN.CELL_MODEL = this.ucManagePosition.U_CELL_MODEL;

                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;

                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;

                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;

                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();

                    mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                    //调用
                    boolResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                             "ManageCreate",
                                                              new object[] { mMANAGE_MAIN,
                                                                               Convert.ToInt32(this.cmbTemplate.SelectedValue), 
                                                                               true,                                         
                                                                               this.ucManagePosition.U_AutoDownloadControlTask, 
                                                                               this.ucManagePosition.U_AutoCompleteTask,
                                                                               },
                                                               out strResult);

                        //判断结果
                        if (boolResult)
                        {
                            this.ucManagePosition.U_Refresh();
                        }

                        MainApp._MessageDialog.ShowResult(boolResult, strResult);
                    }

                   

                   

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }



    }
}
