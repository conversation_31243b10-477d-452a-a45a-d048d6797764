﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_MOVE"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_DOWN" Height="561" Width="500" Loaded="DocumentContent_Loaded">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                </Grid.RowDefinitions>
                 <uc:ucSplitPropertyGridTab x:Name="ucStorageGroup" Grid.Row="2" ></uc:ucSplitPropertyGridTab>

        <WrapPanel Margin="3" HorizontalAlignment="Left" VerticalAlignment="Center" Grid.Row="3" ButtonBase.Click="WrapPanel_Click">
                    <uc:ucManagePosition x:Name="ucManagePosition"  Grid.Row="3"></uc:ucManagePosition>
                    <Button Name="btnConfirm"  Width="60">下达任务</Button>
                    <Button Name="btnRefresh"  Width="60" Margin="10,0,0,0">刷新</Button>
                </WrapPanel>
            </Grid>          

</ad:DocumentContent>
