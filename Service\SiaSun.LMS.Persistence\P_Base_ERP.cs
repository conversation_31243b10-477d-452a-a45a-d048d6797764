﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using IBatisNet.DataMapper;
using System.Collections;
using System.Data;
using IBatisNet.DataMapper.Configuration;

namespace SiaSun.LMS.Persistence
{
    public class P_Base_ERP : P_Base
    {
        public static volatile ISqlMapper _sqlMap;


        public P_Base_ERP()
        {
            if (_sqlMap == null)
            {
                if (_sqlMap == null)
                {
                    DomSqlMapBuilder builder = new DomSqlMapBuilder();
                    _sqlMap = builder.Configure("ERPMap.config");
                }
            }

            base._sqlMap = _sqlMap;
        }



    
    }
}