﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.PLAN
{
    /// <summary>
    /// PLAN_IMPORT.xaml 的交互逻辑
    /// </summary>
    public partial class GOODS_IMPORT : AvalonDock.DocumentContent
    {
        string strPlanTypeCode = null;
        int intGoodsID = 0;
        string strImportType = null;

        public GOODS_IMPORT()
        {
            InitializeComponent();
        }

        

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {

        }


        /// <summary>
        /// 按钮点击事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnImport":
                        this.ImportExcel();
                        break;
                    case "btnSave":
                        this.SavePlanOrder();
                        break;
                    case "btnRefresh":
                        //this.gridImport.ItemsSource = null;
                        break;
                    case "btnPrint":
                        this.PrintOrder();
                        break;
                    case "btnQuery":

                        break;
                }
            }
        }

        /// <summary>
        /// 打印入库单
        /// </summary>
        private void PrintOrder()
        {
            //if (this.gridImport.ItemsSource == null)
            //    return;
            //using (DataTable table = (this.gridImport.ItemsSource as DataView).Table)
            //{
            //    new Dialog.ReportWindow(string.Format("制单人员：{0}", MainApp._USER.USER_CODE), this.GetType().Name,"PLAN_IMPORT_PRINT", table).ShowDialog();
            //}
        }

        /// <summary>
        /// 导入EXCEL数据
        /// </summary>
        private void ImportExcel()
        {
            Dialog.ImportExcelDialog winImport = new Dialog.ImportExcelDialog(false);
            if (winImport.ShowDialog() == true)
            {
                this.gridImport.ItemsSource = winImport.dt.DefaultView;
                winImport.Close();
            }
        }

        /// <summary>
        /// 保存
        /// </summary>
        private void SavePlanOrder()
        {
            string mesResult = string.Empty;

            string sResult = string.Empty;

            bool bResult = true;

            DataTable dtImport = (this.gridImport.ItemsSource as DataView).Table;

            if (dtImport.Rows.Count == 0)
            {
                MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                return;
            }

            bResult = MainApp._I_BaseService.Invoke("PlanImport", "GoodsCreate", new object[] { dtImport }, out sResult);

            //if (!bResult)
            //{
            //    MainApp._MessageDialog.ShowException(string.Format("导入接口表U5PARTS中物料{0}失败!", import["WMI_PART"].ToString()));

            //    return;
            //}

            MainApp._MessageDialog.ShowResult(bResult, sResult);

        }



    }
}
