﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:2.0.50727.5477
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

using System.Data;



[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public interface I_PDAService
{
    
    System.Data.DataTable GetList(string strSQL);
    
    ObjectT GetModel(string statementName, object parameterObject);
    
    bool Manage<PERSON>reate(string sClassName, string sMethodName, MANAGE_MAIN mMANAGE_MAIN, MANAGE_LIST[] lsMANAGE_LIST, bool bTrans, bool bCheckStorage, bool bComplete, bool bAutoSendControl, out string sResult);
    
    WH_CELL GetModelCellCode(string CELL_CODE);
    
    bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SYS_USER USER);
    
    MANAGE_TYPE_PARAM[] ManageTypeParamGetList(int MANAGE_TYPE_ID);
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="GetList", Namespace="http://tempuri.org/")]
public partial class GetListRequest
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public string strSQL;
    
    public GetListRequest()
    {
    }
    
    public GetListRequest(string strSQL)
    {
        this.strSQL = strSQL;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="GetListResponse", Namespace="http://tempuri.org/")]
public partial class GetListResponse
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public System.Data.DataTable GetListResult;
    
    public GetListResponse()
    {
    }
    
    public GetListResponse(System.Data.DataTable GetListResult)
    {
        this.GetListResult = GetListResult;
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class GOODS_TYPE
{
    
    private string gOODS_TYPE_CODEField;
    
    private string gOODS_TYPE_FLAGField;
    
    private int gOODS_TYPE_IDField;
    
    private bool gOODS_TYPE_IDFieldSpecified;
    
    private string gOODS_TYPE_NAMEField;
    
    private int gOODS_TYPE_ORDERField;
    
    private bool gOODS_TYPE_ORDERFieldSpecified;
    
    private string gOODS_TYPE_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string GOODS_TYPE_CODE
    {
        get
        {
            return this.gOODS_TYPE_CODEField;
        }
        set
        {
            this.gOODS_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_TYPE_FLAG
    {
        get
        {
            return this.gOODS_TYPE_FLAGField;
        }
        set
        {
            this.gOODS_TYPE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int GOODS_TYPE_ID
    {
        get
        {
            return this.gOODS_TYPE_IDField;
        }
        set
        {
            this.gOODS_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TYPE_IDSpecified
    {
        get
        {
            return this.gOODS_TYPE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TYPE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_TYPE_NAME
    {
        get
        {
            return this.gOODS_TYPE_NAMEField;
        }
        set
        {
            this.gOODS_TYPE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int GOODS_TYPE_ORDER
    {
        get
        {
            return this.gOODS_TYPE_ORDERField;
        }
        set
        {
            this.gOODS_TYPE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TYPE_ORDERSpecified
    {
        get
        {
            return this.gOODS_TYPE_ORDERFieldSpecified;
        }
        set
        {
            this.gOODS_TYPE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string GOODS_TYPE_REMARK
    {
        get
        {
            return this.gOODS_TYPE_REMARKField;
        }
        set
        {
            this.gOODS_TYPE_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class LED_LIST
{
    
    private int aREA_HEIGHTField;
    
    private bool aREA_HEIGHTFieldSpecified;
    
    private int aREA_WIDTHField;
    
    private bool aREA_WIDTHFieldSpecified;
    
    private int aREA_XField;
    
    private bool aREA_XFieldSpecified;
    
    private int aREA_YField;
    
    private bool aREA_YFieldSpecified;
    
    private string fILE_NAMEField;
    
    private int fONT_SIZEField;
    
    private bool fONT_SIZEFieldSpecified;
    
    private int lED_IDField;
    
    private bool lED_IDFieldSpecified;
    
    private int lED_LIST_IDField;
    
    private bool lED_LIST_IDFieldSpecified;
    
    private string lED_LIST_PARA1Field;
    
    private string lED_LIST_PARA2Field;
    
    private string lED_LIST_PARA3Field;
    
    private string lED_LIST_PARA4Field;
    
    private string lED_LIST_PARA5Field;
    
    private string lED_LIST_REMARKField;
    
    private int lINE_NOField;
    
    private bool lINE_NOFieldSpecified;
    
    private string lINE_TEXTField;
    
    private int rUN_SPEEDField;
    
    private bool rUN_SPEEDFieldSpecified;
    
    private int sHOW_STUNTField;
    
    private bool sHOW_STUNTFieldSpecified;
    
    private int sHOW_TIMEField;
    
    private bool sHOW_TIMEFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int AREA_HEIGHT
    {
        get
        {
            return this.aREA_HEIGHTField;
        }
        set
        {
            this.aREA_HEIGHTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_HEIGHTSpecified
    {
        get
        {
            return this.aREA_HEIGHTFieldSpecified;
        }
        set
        {
            this.aREA_HEIGHTFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int AREA_WIDTH
    {
        get
        {
            return this.aREA_WIDTHField;
        }
        set
        {
            this.aREA_WIDTHField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_WIDTHSpecified
    {
        get
        {
            return this.aREA_WIDTHFieldSpecified;
        }
        set
        {
            this.aREA_WIDTHFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int AREA_X
    {
        get
        {
            return this.aREA_XField;
        }
        set
        {
            this.aREA_XField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_XSpecified
    {
        get
        {
            return this.aREA_XFieldSpecified;
        }
        set
        {
            this.aREA_XFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int AREA_Y
    {
        get
        {
            return this.aREA_YField;
        }
        set
        {
            this.aREA_YField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_YSpecified
    {
        get
        {
            return this.aREA_YFieldSpecified;
        }
        set
        {
            this.aREA_YFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string FILE_NAME
    {
        get
        {
            return this.fILE_NAMEField;
        }
        set
        {
            this.fILE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int FONT_SIZE
    {
        get
        {
            return this.fONT_SIZEField;
        }
        set
        {
            this.fONT_SIZEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FONT_SIZESpecified
    {
        get
        {
            return this.fONT_SIZEFieldSpecified;
        }
        set
        {
            this.fONT_SIZEFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int LED_ID
    {
        get
        {
            return this.lED_IDField;
        }
        set
        {
            this.lED_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LED_IDSpecified
    {
        get
        {
            return this.lED_IDFieldSpecified;
        }
        set
        {
            this.lED_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int LED_LIST_ID
    {
        get
        {
            return this.lED_LIST_IDField;
        }
        set
        {
            this.lED_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LED_LIST_IDSpecified
    {
        get
        {
            return this.lED_LIST_IDFieldSpecified;
        }
        set
        {
            this.lED_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string LED_LIST_PARA1
    {
        get
        {
            return this.lED_LIST_PARA1Field;
        }
        set
        {
            this.lED_LIST_PARA1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string LED_LIST_PARA2
    {
        get
        {
            return this.lED_LIST_PARA2Field;
        }
        set
        {
            this.lED_LIST_PARA2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string LED_LIST_PARA3
    {
        get
        {
            return this.lED_LIST_PARA3Field;
        }
        set
        {
            this.lED_LIST_PARA3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string LED_LIST_PARA4
    {
        get
        {
            return this.lED_LIST_PARA4Field;
        }
        set
        {
            this.lED_LIST_PARA4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string LED_LIST_PARA5
    {
        get
        {
            return this.lED_LIST_PARA5Field;
        }
        set
        {
            this.lED_LIST_PARA5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string LED_LIST_REMARK
    {
        get
        {
            return this.lED_LIST_REMARKField;
        }
        set
        {
            this.lED_LIST_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public int LINE_NO
    {
        get
        {
            return this.lINE_NOField;
        }
        set
        {
            this.lINE_NOField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LINE_NOSpecified
    {
        get
        {
            return this.lINE_NOFieldSpecified;
        }
        set
        {
            this.lINE_NOFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string LINE_TEXT
    {
        get
        {
            return this.lINE_TEXTField;
        }
        set
        {
            this.lINE_TEXTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=16)]
    public int RUN_SPEED
    {
        get
        {
            return this.rUN_SPEEDField;
        }
        set
        {
            this.rUN_SPEEDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RUN_SPEEDSpecified
    {
        get
        {
            return this.rUN_SPEEDFieldSpecified;
        }
        set
        {
            this.rUN_SPEEDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=17)]
    public int SHOW_STUNT
    {
        get
        {
            return this.sHOW_STUNTField;
        }
        set
        {
            this.sHOW_STUNTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SHOW_STUNTSpecified
    {
        get
        {
            return this.sHOW_STUNTFieldSpecified;
        }
        set
        {
            this.sHOW_STUNTFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=18)]
    public int SHOW_TIME
    {
        get
        {
            return this.sHOW_TIMEField;
        }
        set
        {
            this.sHOW_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SHOW_TIMESpecified
    {
        get
        {
            return this.sHOW_TIMEFieldSpecified;
        }
        set
        {
            this.sHOW_TIMEFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class MANAGE_LIST
{
    
    private string bOX_BARCODEField;
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private string gOODS_PROPERTY1Field;
    
    private string gOODS_PROPERTY2Field;
    
    private string gOODS_PROPERTY3Field;
    
    private string gOODS_PROPERTY4Field;
    
    private string gOODS_PROPERTY5Field;
    
    private string gOODS_PROPERTY6Field;
    
    private string gOODS_PROPERTY7Field;
    
    private string gOODS_PROPERTY8Field;
    
    private int mANAGE_IDField;
    
    private bool mANAGE_IDFieldSpecified;
    
    private int mANAGE_LIST_IDField;
    
    private bool mANAGE_LIST_IDFieldSpecified;
    
    private decimal mANAGE_LIST_QUANTITYField;
    
    private bool mANAGE_LIST_QUANTITYFieldSpecified;
    
    private string mANAGE_LIST_REMARKField;
    
    private int pLAN_LIST_IDField;
    
    private bool pLAN_LIST_IDFieldSpecified;
    
    private int sTORAGE_LIST_IDField;
    
    private bool sTORAGE_LIST_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string GOODS_PROPERTY1
    {
        get
        {
            return this.gOODS_PROPERTY1Field;
        }
        set
        {
            this.gOODS_PROPERTY1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_PROPERTY2
    {
        get
        {
            return this.gOODS_PROPERTY2Field;
        }
        set
        {
            this.gOODS_PROPERTY2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_PROPERTY3
    {
        get
        {
            return this.gOODS_PROPERTY3Field;
        }
        set
        {
            this.gOODS_PROPERTY3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string GOODS_PROPERTY4
    {
        get
        {
            return this.gOODS_PROPERTY4Field;
        }
        set
        {
            this.gOODS_PROPERTY4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_PROPERTY5
    {
        get
        {
            return this.gOODS_PROPERTY5Field;
        }
        set
        {
            this.gOODS_PROPERTY5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string GOODS_PROPERTY6
    {
        get
        {
            return this.gOODS_PROPERTY6Field;
        }
        set
        {
            this.gOODS_PROPERTY6Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string GOODS_PROPERTY7
    {
        get
        {
            return this.gOODS_PROPERTY7Field;
        }
        set
        {
            this.gOODS_PROPERTY7Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string GOODS_PROPERTY8
    {
        get
        {
            return this.gOODS_PROPERTY8Field;
        }
        set
        {
            this.gOODS_PROPERTY8Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=10)]
    public int MANAGE_ID
    {
        get
        {
            return this.mANAGE_IDField;
        }
        set
        {
            this.mANAGE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_IDSpecified
    {
        get
        {
            return this.mANAGE_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int MANAGE_LIST_ID
    {
        get
        {
            return this.mANAGE_LIST_IDField;
        }
        set
        {
            this.mANAGE_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_LIST_IDSpecified
    {
        get
        {
            return this.mANAGE_LIST_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public decimal MANAGE_LIST_QUANTITY
    {
        get
        {
            return this.mANAGE_LIST_QUANTITYField;
        }
        set
        {
            this.mANAGE_LIST_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_LIST_QUANTITYSpecified
    {
        get
        {
            return this.mANAGE_LIST_QUANTITYFieldSpecified;
        }
        set
        {
            this.mANAGE_LIST_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string MANAGE_LIST_REMARK
    {
        get
        {
            return this.mANAGE_LIST_REMARKField;
        }
        set
        {
            this.mANAGE_LIST_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public int PLAN_LIST_ID
    {
        get
        {
            return this.pLAN_LIST_IDField;
        }
        set
        {
            this.pLAN_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_IDSpecified
    {
        get
        {
            return this.pLAN_LIST_IDFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public int STORAGE_LIST_ID
    {
        get
        {
            return this.sTORAGE_LIST_IDField;
        }
        set
        {
            this.sTORAGE_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_LIST_IDSpecified
    {
        get
        {
            return this.sTORAGE_LIST_IDFieldSpecified;
        }
        set
        {
            this.sTORAGE_LIST_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class STORAGE_DETAIL
{
    
    private string bOX_BARCODEField;
    
    private string gOODS_BARCODEField;
    
    private int sTORAGE_DETAIL_IDField;
    
    private bool sTORAGE_DETAIL_IDFieldSpecified;
    
    private string sTORAGE_DETAIL_REMARKField;
    
    private int sTORAGE_LIST_IDField;
    
    private bool sTORAGE_LIST_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_BARCODE
    {
        get
        {
            return this.gOODS_BARCODEField;
        }
        set
        {
            this.gOODS_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int STORAGE_DETAIL_ID
    {
        get
        {
            return this.sTORAGE_DETAIL_IDField;
        }
        set
        {
            this.sTORAGE_DETAIL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_DETAIL_IDSpecified
    {
        get
        {
            return this.sTORAGE_DETAIL_IDFieldSpecified;
        }
        set
        {
            this.sTORAGE_DETAIL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string STORAGE_DETAIL_REMARK
    {
        get
        {
            return this.sTORAGE_DETAIL_REMARKField;
        }
        set
        {
            this.sTORAGE_DETAIL_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int STORAGE_LIST_ID
    {
        get
        {
            return this.sTORAGE_LIST_IDField;
        }
        set
        {
            this.sTORAGE_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_LIST_IDSpecified
    {
        get
        {
            return this.sTORAGE_LIST_IDFieldSpecified;
        }
        set
        {
            this.sTORAGE_LIST_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class TECHNICS_MAIN
{
    
    private int aUTO_CREATE_CONTROLField;
    
    private bool aUTO_CREATE_CONTROLFieldSpecified;
    
    private int cANCEL_BIND_BARCODEField;
    
    private bool cANCEL_BIND_BARCODEFieldSpecified;
    
    private int cANCEL_STORAGE_FLAGField;
    
    private bool cANCEL_STORAGE_FLAGFieldSpecified;
    
    private string cELL_CLASSField;
    
    private string cELL_INOUTField;
    
    private string cELL_MODELField;
    
    private int cHECK_END_POSITION_FLAGField;
    
    private bool cHECK_END_POSITION_FLAGFieldSpecified;
    
    private int cHECK_START_POSITION_FLAGField;
    
    private bool cHECK_START_POSITION_FLAGFieldSpecified;
    
    private int cREATE_STORAGE_FLAGField;
    
    private bool cREATE_STORAGE_FLAGFieldSpecified;
    
    private string dATA_ORDERField;
    
    private int eND_AREA_IDField;
    
    private bool eND_AREA_IDFieldSpecified;
    
    private string eND_GOODS_STATUSField;
    
    private string eND_LOGIC_STATUSField;
    
    private int eND_POSITIONField;
    
    private bool eND_POSITIONFieldSpecified;
    
    private string eP_END_CELL_STATUSField;
    
    private string eP_END_RUN_STATUSField;
    
    private string eP_START_CELL_STATUSField;
    
    private string eP_START_RUN_STATUSField;
    
    private string fINISH_STATUSField;
    
    private int gOODS_CLASS_IDField;
    
    private bool gOODS_CLASS_IDFieldSpecified;
    
    private int hAS_STORAGE_FLAGField;
    
    private bool hAS_STORAGE_FLAGFieldSpecified;
    
    private int iNIT_STORAGE_FLAGField;
    
    private bool iNIT_STORAGE_FLAGFieldSpecified;
    
    private string mANAGE_TYPE_CODEField;
    
    private int mOVE_STROAGE_FLAGField;
    
    private bool mOVE_STROAGE_FLAGFieldSpecified;
    
    private int nEXT_GOODS_TECHNICS_IDField;
    
    private bool nEXT_GOODS_TECHNICS_IDFieldSpecified;
    
    private int nEXT_STOCK_TECHNICS_IDField;
    
    private bool nEXT_STOCK_TECHNICS_IDFieldSpecified;
    
    private int pLAN_CHANGE_FLAGField;
    
    private bool pLAN_CHANGE_FLAGFieldSpecified;
    
    private string pLAN_TYPE_CODEField;
    
    private int rELAVTIVE_TECHNICS_IDField;
    
    private bool rELAVTIVE_TECHNICS_IDFieldSpecified;
    
    private int sAME_LANEWAYField;
    
    private bool sAME_LANEWAYFieldSpecified;
    
    private string sP_END_CELL_STATUSField;
    
    private string sP_END_RUNS_STATUSField;
    
    private string sP_START_CELL_STATUSField;
    
    private string sP_START_RUNS_STATUSField;
    
    private int sTART_AREA_IDField;
    
    private bool sTART_AREA_IDFieldSpecified;
    
    private int sTART_POSITIONField;
    
    private bool sTART_POSITIONFieldSpecified;
    
    private string sTART_STATUSField;
    
    private int sTATION_PROJECT_FLAGField;
    
    private bool sTATION_PROJECT_FLAGFieldSpecified;
    
    private int sTATION_SORT_FLAGField;
    
    private bool sTATION_SORT_FLAGFieldSpecified;
    
    private string tECHNICS_ACTIONSField;
    
    private string tECHNICS_DESCRIPTIONField;
    
    private int tECHNICS_IDField;
    
    private bool tECHNICS_IDFieldSpecified;
    
    private string tECHNICS_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int AUTO_CREATE_CONTROL
    {
        get
        {
            return this.aUTO_CREATE_CONTROLField;
        }
        set
        {
            this.aUTO_CREATE_CONTROLField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AUTO_CREATE_CONTROLSpecified
    {
        get
        {
            return this.aUTO_CREATE_CONTROLFieldSpecified;
        }
        set
        {
            this.aUTO_CREATE_CONTROLFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int CANCEL_BIND_BARCODE
    {
        get
        {
            return this.cANCEL_BIND_BARCODEField;
        }
        set
        {
            this.cANCEL_BIND_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CANCEL_BIND_BARCODESpecified
    {
        get
        {
            return this.cANCEL_BIND_BARCODEFieldSpecified;
        }
        set
        {
            this.cANCEL_BIND_BARCODEFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int CANCEL_STORAGE_FLAG
    {
        get
        {
            return this.cANCEL_STORAGE_FLAGField;
        }
        set
        {
            this.cANCEL_STORAGE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CANCEL_STORAGE_FLAGSpecified
    {
        get
        {
            return this.cANCEL_STORAGE_FLAGFieldSpecified;
        }
        set
        {
            this.cANCEL_STORAGE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string CELL_CLASS
    {
        get
        {
            return this.cELL_CLASSField;
        }
        set
        {
            this.cELL_CLASSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string CELL_INOUT
    {
        get
        {
            return this.cELL_INOUTField;
        }
        set
        {
            this.cELL_INOUTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string CELL_MODEL
    {
        get
        {
            return this.cELL_MODELField;
        }
        set
        {
            this.cELL_MODELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int CHECK_END_POSITION_FLAG
    {
        get
        {
            return this.cHECK_END_POSITION_FLAGField;
        }
        set
        {
            this.cHECK_END_POSITION_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CHECK_END_POSITION_FLAGSpecified
    {
        get
        {
            return this.cHECK_END_POSITION_FLAGFieldSpecified;
        }
        set
        {
            this.cHECK_END_POSITION_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int CHECK_START_POSITION_FLAG
    {
        get
        {
            return this.cHECK_START_POSITION_FLAGField;
        }
        set
        {
            this.cHECK_START_POSITION_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CHECK_START_POSITION_FLAGSpecified
    {
        get
        {
            return this.cHECK_START_POSITION_FLAGFieldSpecified;
        }
        set
        {
            this.cHECK_START_POSITION_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int CREATE_STORAGE_FLAG
    {
        get
        {
            return this.cREATE_STORAGE_FLAGField;
        }
        set
        {
            this.cREATE_STORAGE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CREATE_STORAGE_FLAGSpecified
    {
        get
        {
            return this.cREATE_STORAGE_FLAGFieldSpecified;
        }
        set
        {
            this.cREATE_STORAGE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string DATA_ORDER
    {
        get
        {
            return this.dATA_ORDERField;
        }
        set
        {
            this.dATA_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=10)]
    public int END_AREA_ID
    {
        get
        {
            return this.eND_AREA_IDField;
        }
        set
        {
            this.eND_AREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_AREA_IDSpecified
    {
        get
        {
            return this.eND_AREA_IDFieldSpecified;
        }
        set
        {
            this.eND_AREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string END_GOODS_STATUS
    {
        get
        {
            return this.eND_GOODS_STATUSField;
        }
        set
        {
            this.eND_GOODS_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string END_LOGIC_STATUS
    {
        get
        {
            return this.eND_LOGIC_STATUSField;
        }
        set
        {
            this.eND_LOGIC_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public int END_POSITION
    {
        get
        {
            return this.eND_POSITIONField;
        }
        set
        {
            this.eND_POSITIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_POSITIONSpecified
    {
        get
        {
            return this.eND_POSITIONFieldSpecified;
        }
        set
        {
            this.eND_POSITIONFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
    public string EP_END_CELL_STATUS
    {
        get
        {
            return this.eP_END_CELL_STATUSField;
        }
        set
        {
            this.eP_END_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string EP_END_RUN_STATUS
    {
        get
        {
            return this.eP_END_RUN_STATUSField;
        }
        set
        {
            this.eP_END_RUN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
    public string EP_START_CELL_STATUS
    {
        get
        {
            return this.eP_START_CELL_STATUSField;
        }
        set
        {
            this.eP_START_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=17)]
    public string EP_START_RUN_STATUS
    {
        get
        {
            return this.eP_START_RUN_STATUSField;
        }
        set
        {
            this.eP_START_RUN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=18)]
    public string FINISH_STATUS
    {
        get
        {
            return this.fINISH_STATUSField;
        }
        set
        {
            this.fINISH_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=19)]
    public int GOODS_CLASS_ID
    {
        get
        {
            return this.gOODS_CLASS_IDField;
        }
        set
        {
            this.gOODS_CLASS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_CLASS_IDSpecified
    {
        get
        {
            return this.gOODS_CLASS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_CLASS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=20)]
    public int HAS_STORAGE_FLAG
    {
        get
        {
            return this.hAS_STORAGE_FLAGField;
        }
        set
        {
            this.hAS_STORAGE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool HAS_STORAGE_FLAGSpecified
    {
        get
        {
            return this.hAS_STORAGE_FLAGFieldSpecified;
        }
        set
        {
            this.hAS_STORAGE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=21)]
    public int INIT_STORAGE_FLAG
    {
        get
        {
            return this.iNIT_STORAGE_FLAGField;
        }
        set
        {
            this.iNIT_STORAGE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool INIT_STORAGE_FLAGSpecified
    {
        get
        {
            return this.iNIT_STORAGE_FLAGFieldSpecified;
        }
        set
        {
            this.iNIT_STORAGE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=22)]
    public string MANAGE_TYPE_CODE
    {
        get
        {
            return this.mANAGE_TYPE_CODEField;
        }
        set
        {
            this.mANAGE_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=23)]
    public int MOVE_STROAGE_FLAG
    {
        get
        {
            return this.mOVE_STROAGE_FLAGField;
        }
        set
        {
            this.mOVE_STROAGE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MOVE_STROAGE_FLAGSpecified
    {
        get
        {
            return this.mOVE_STROAGE_FLAGFieldSpecified;
        }
        set
        {
            this.mOVE_STROAGE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=24)]
    public int NEXT_GOODS_TECHNICS_ID
    {
        get
        {
            return this.nEXT_GOODS_TECHNICS_IDField;
        }
        set
        {
            this.nEXT_GOODS_TECHNICS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NEXT_GOODS_TECHNICS_IDSpecified
    {
        get
        {
            return this.nEXT_GOODS_TECHNICS_IDFieldSpecified;
        }
        set
        {
            this.nEXT_GOODS_TECHNICS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=25)]
    public int NEXT_STOCK_TECHNICS_ID
    {
        get
        {
            return this.nEXT_STOCK_TECHNICS_IDField;
        }
        set
        {
            this.nEXT_STOCK_TECHNICS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool NEXT_STOCK_TECHNICS_IDSpecified
    {
        get
        {
            return this.nEXT_STOCK_TECHNICS_IDFieldSpecified;
        }
        set
        {
            this.nEXT_STOCK_TECHNICS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=26)]
    public int PLAN_CHANGE_FLAG
    {
        get
        {
            return this.pLAN_CHANGE_FLAGField;
        }
        set
        {
            this.pLAN_CHANGE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_CHANGE_FLAGSpecified
    {
        get
        {
            return this.pLAN_CHANGE_FLAGFieldSpecified;
        }
        set
        {
            this.pLAN_CHANGE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=27)]
    public string PLAN_TYPE_CODE
    {
        get
        {
            return this.pLAN_TYPE_CODEField;
        }
        set
        {
            this.pLAN_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=28)]
    public int RELAVTIVE_TECHNICS_ID
    {
        get
        {
            return this.rELAVTIVE_TECHNICS_IDField;
        }
        set
        {
            this.rELAVTIVE_TECHNICS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELAVTIVE_TECHNICS_IDSpecified
    {
        get
        {
            return this.rELAVTIVE_TECHNICS_IDFieldSpecified;
        }
        set
        {
            this.rELAVTIVE_TECHNICS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=29)]
    public int SAME_LANEWAY
    {
        get
        {
            return this.sAME_LANEWAYField;
        }
        set
        {
            this.sAME_LANEWAYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SAME_LANEWAYSpecified
    {
        get
        {
            return this.sAME_LANEWAYFieldSpecified;
        }
        set
        {
            this.sAME_LANEWAYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=30)]
    public string SP_END_CELL_STATUS
    {
        get
        {
            return this.sP_END_CELL_STATUSField;
        }
        set
        {
            this.sP_END_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=31)]
    public string SP_END_RUNS_STATUS
    {
        get
        {
            return this.sP_END_RUNS_STATUSField;
        }
        set
        {
            this.sP_END_RUNS_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=32)]
    public string SP_START_CELL_STATUS
    {
        get
        {
            return this.sP_START_CELL_STATUSField;
        }
        set
        {
            this.sP_START_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=33)]
    public string SP_START_RUNS_STATUS
    {
        get
        {
            return this.sP_START_RUNS_STATUSField;
        }
        set
        {
            this.sP_START_RUNS_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=34)]
    public int START_AREA_ID
    {
        get
        {
            return this.sTART_AREA_IDField;
        }
        set
        {
            this.sTART_AREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_AREA_IDSpecified
    {
        get
        {
            return this.sTART_AREA_IDFieldSpecified;
        }
        set
        {
            this.sTART_AREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=35)]
    public int START_POSITION
    {
        get
        {
            return this.sTART_POSITIONField;
        }
        set
        {
            this.sTART_POSITIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_POSITIONSpecified
    {
        get
        {
            return this.sTART_POSITIONFieldSpecified;
        }
        set
        {
            this.sTART_POSITIONFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=36)]
    public string START_STATUS
    {
        get
        {
            return this.sTART_STATUSField;
        }
        set
        {
            this.sTART_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=37)]
    public int STATION_PROJECT_FLAG
    {
        get
        {
            return this.sTATION_PROJECT_FLAGField;
        }
        set
        {
            this.sTATION_PROJECT_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STATION_PROJECT_FLAGSpecified
    {
        get
        {
            return this.sTATION_PROJECT_FLAGFieldSpecified;
        }
        set
        {
            this.sTATION_PROJECT_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=38)]
    public int STATION_SORT_FLAG
    {
        get
        {
            return this.sTATION_SORT_FLAGField;
        }
        set
        {
            this.sTATION_SORT_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STATION_SORT_FLAGSpecified
    {
        get
        {
            return this.sTATION_SORT_FLAGFieldSpecified;
        }
        set
        {
            this.sTATION_SORT_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=39)]
    public string TECHNICS_ACTIONS
    {
        get
        {
            return this.tECHNICS_ACTIONSField;
        }
        set
        {
            this.tECHNICS_ACTIONSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=40)]
    public string TECHNICS_DESCRIPTION
    {
        get
        {
            return this.tECHNICS_DESCRIPTIONField;
        }
        set
        {
            this.tECHNICS_DESCRIPTIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=41)]
    public int TECHNICS_ID
    {
        get
        {
            return this.tECHNICS_IDField;
        }
        set
        {
            this.tECHNICS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TECHNICS_IDSpecified
    {
        get
        {
            return this.tECHNICS_IDFieldSpecified;
        }
        set
        {
            this.tECHNICS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=42)]
    public string TECHNICS_REMARK
    {
        get
        {
            return this.tECHNICS_REMARKField;
        }
        set
        {
            this.tECHNICS_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class MANAGE_TYPE_PARAM
{
    
    private int mANAGE_TYPE_IDField;
    
    private bool mANAGE_TYPE_IDFieldSpecified;
    
    private string pARAM_CODEField;
    
    private string pARAM_FLAGField;
    
    private int pARAM_IDField;
    
    private bool pARAM_IDFieldSpecified;
    
    private string pARAM_VALUEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int MANAGE_TYPE_ID
    {
        get
        {
            return this.mANAGE_TYPE_IDField;
        }
        set
        {
            this.mANAGE_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_TYPE_IDSpecified
    {
        get
        {
            return this.mANAGE_TYPE_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_TYPE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string PARAM_CODE
    {
        get
        {
            return this.pARAM_CODEField;
        }
        set
        {
            this.pARAM_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string PARAM_FLAG
    {
        get
        {
            return this.pARAM_FLAGField;
        }
        set
        {
            this.pARAM_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int PARAM_ID
    {
        get
        {
            return this.pARAM_IDField;
        }
        set
        {
            this.pARAM_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PARAM_IDSpecified
    {
        get
        {
            return this.pARAM_IDFieldSpecified;
        }
        set
        {
            this.pARAM_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string PARAM_VALUE
    {
        get
        {
            return this.pARAM_VALUEField;
        }
        set
        {
            this.pARAM_VALUEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class STORAGE_LIST
{
    
    private string bOX_BARCODEField;
    
    private string eNTRY_TIMEField;
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private string gOODS_PROPERTY1Field;
    
    private string gOODS_PROPERTY2Field;
    
    private string gOODS_PROPERTY3Field;
    
    private string gOODS_PROPERTY4Field;
    
    private string gOODS_PROPERTY5Field;
    
    private string gOODS_PROPERTY6Field;
    
    private string gOODS_PROPERTY7Field;
    
    private string gOODS_PROPERTY8Field;
    
    private int pLAN_LIST_IDField;
    
    private bool pLAN_LIST_IDFieldSpecified;
    
    private int sTORAGE_IDField;
    
    private bool sTORAGE_IDFieldSpecified;
    
    private int sTORAGE_LIST_IDField;
    
    private bool sTORAGE_LIST_IDFieldSpecified;
    
    private decimal sTORAGE_LIST_QUANTITYField;
    
    private bool sTORAGE_LIST_QUANTITYFieldSpecified;
    
    private string sTORAGE_LIST_REMARKField;
    
    private string uPDATE_TIMEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string ENTRY_TIME
    {
        get
        {
            return this.eNTRY_TIMEField;
        }
        set
        {
            this.eNTRY_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_PROPERTY1
    {
        get
        {
            return this.gOODS_PROPERTY1Field;
        }
        set
        {
            this.gOODS_PROPERTY1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_PROPERTY2
    {
        get
        {
            return this.gOODS_PROPERTY2Field;
        }
        set
        {
            this.gOODS_PROPERTY2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string GOODS_PROPERTY3
    {
        get
        {
            return this.gOODS_PROPERTY3Field;
        }
        set
        {
            this.gOODS_PROPERTY3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_PROPERTY4
    {
        get
        {
            return this.gOODS_PROPERTY4Field;
        }
        set
        {
            this.gOODS_PROPERTY4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string GOODS_PROPERTY5
    {
        get
        {
            return this.gOODS_PROPERTY5Field;
        }
        set
        {
            this.gOODS_PROPERTY5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string GOODS_PROPERTY6
    {
        get
        {
            return this.gOODS_PROPERTY6Field;
        }
        set
        {
            this.gOODS_PROPERTY6Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string GOODS_PROPERTY7
    {
        get
        {
            return this.gOODS_PROPERTY7Field;
        }
        set
        {
            this.gOODS_PROPERTY7Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string GOODS_PROPERTY8
    {
        get
        {
            return this.gOODS_PROPERTY8Field;
        }
        set
        {
            this.gOODS_PROPERTY8Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int PLAN_LIST_ID
    {
        get
        {
            return this.pLAN_LIST_IDField;
        }
        set
        {
            this.pLAN_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_IDSpecified
    {
        get
        {
            return this.pLAN_LIST_IDFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public int STORAGE_ID
    {
        get
        {
            return this.sTORAGE_IDField;
        }
        set
        {
            this.sTORAGE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_IDSpecified
    {
        get
        {
            return this.sTORAGE_IDFieldSpecified;
        }
        set
        {
            this.sTORAGE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public int STORAGE_LIST_ID
    {
        get
        {
            return this.sTORAGE_LIST_IDField;
        }
        set
        {
            this.sTORAGE_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_LIST_IDSpecified
    {
        get
        {
            return this.sTORAGE_LIST_IDFieldSpecified;
        }
        set
        {
            this.sTORAGE_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public decimal STORAGE_LIST_QUANTITY
    {
        get
        {
            return this.sTORAGE_LIST_QUANTITYField;
        }
        set
        {
            this.sTORAGE_LIST_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_LIST_QUANTITYSpecified
    {
        get
        {
            return this.sTORAGE_LIST_QUANTITYFieldSpecified;
        }
        set
        {
            this.sTORAGE_LIST_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string STORAGE_LIST_REMARK
    {
        get
        {
            return this.sTORAGE_LIST_REMARKField;
        }
        set
        {
            this.sTORAGE_LIST_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
    public string UPDATE_TIME
    {
        get
        {
            return this.uPDATE_TIMEField;
        }
        set
        {
            this.uPDATE_TIMEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class GOODS_PROPERTY
{
    
    private string gOODS_PROPERTY_CODEField;
    
    private string gOODS_PROPERTY_DATASOURCEField;
    
    private string gOODS_PROPERTY_FIELDField;
    
    private string gOODS_PROPERTY_FIELDTYPEField;
    
    private string gOODS_PROPERTY_FLAGField;
    
    private int gOODS_PROPERTY_IDField;
    
    private bool gOODS_PROPERTY_IDFieldSpecified;
    
    private string gOODS_PROPERTY_NAMEField;
    
    private int gOODS_PROPERTY_ORDERField;
    
    private bool gOODS_PROPERTY_ORDERFieldSpecified;
    
    private string gOODS_PROPERTY_VALIDField;
    
    private int gOODS_TYPE_IDField;
    
    private bool gOODS_TYPE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string GOODS_PROPERTY_CODE
    {
        get
        {
            return this.gOODS_PROPERTY_CODEField;
        }
        set
        {
            this.gOODS_PROPERTY_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_PROPERTY_DATASOURCE
    {
        get
        {
            return this.gOODS_PROPERTY_DATASOURCEField;
        }
        set
        {
            this.gOODS_PROPERTY_DATASOURCEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string GOODS_PROPERTY_FIELD
    {
        get
        {
            return this.gOODS_PROPERTY_FIELDField;
        }
        set
        {
            this.gOODS_PROPERTY_FIELDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_PROPERTY_FIELDTYPE
    {
        get
        {
            return this.gOODS_PROPERTY_FIELDTYPEField;
        }
        set
        {
            this.gOODS_PROPERTY_FIELDTYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_PROPERTY_FLAG
    {
        get
        {
            return this.gOODS_PROPERTY_FLAGField;
        }
        set
        {
            this.gOODS_PROPERTY_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int GOODS_PROPERTY_ID
    {
        get
        {
            return this.gOODS_PROPERTY_IDField;
        }
        set
        {
            this.gOODS_PROPERTY_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_PROPERTY_IDSpecified
    {
        get
        {
            return this.gOODS_PROPERTY_IDFieldSpecified;
        }
        set
        {
            this.gOODS_PROPERTY_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_PROPERTY_NAME
    {
        get
        {
            return this.gOODS_PROPERTY_NAMEField;
        }
        set
        {
            this.gOODS_PROPERTY_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int GOODS_PROPERTY_ORDER
    {
        get
        {
            return this.gOODS_PROPERTY_ORDERField;
        }
        set
        {
            this.gOODS_PROPERTY_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_PROPERTY_ORDERSpecified
    {
        get
        {
            return this.gOODS_PROPERTY_ORDERFieldSpecified;
        }
        set
        {
            this.gOODS_PROPERTY_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string GOODS_PROPERTY_VALID
    {
        get
        {
            return this.gOODS_PROPERTY_VALIDField;
        }
        set
        {
            this.gOODS_PROPERTY_VALIDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public int GOODS_TYPE_ID
    {
        get
        {
            return this.gOODS_TYPE_IDField;
        }
        set
        {
            this.gOODS_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TYPE_IDSpecified
    {
        get
        {
            return this.gOODS_TYPE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TYPE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class ServiceMessage
{
    
    private string keyField;
    
    private object[] paramsField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string Key
    {
        get
        {
            return this.keyField;
        }
        set
        {
            this.keyField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=1)]
    [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public object[] Params
    {
        get
        {
            return this.paramsField;
        }
        set
        {
            this.paramsField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class ServiceResponse
{
    
    private object[] paramsField;
    
    private object resultField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
    [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public object[] Params
    {
        get
        {
            return this.paramsField;
        }
        set
        {
            this.paramsField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public object Result
    {
        get
        {
            return this.resultField;
        }
        set
        {
            this.resultField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_ROLE
{
    
    private string rOLE_CODEField;
    
    private string rOLE_FLAGField;
    
    private int rOLE_IDField;
    
    private bool rOLE_IDFieldSpecified;
    
    private string rOLE_NAMEField;
    
    private int rOLE_ORDERField;
    
    private bool rOLE_ORDERFieldSpecified;
    
    private string rOLE_REMARKField;
    
    private int rOLE_START_MENU_IDField;
    
    private bool rOLE_START_MENU_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string ROLE_CODE
    {
        get
        {
            return this.rOLE_CODEField;
        }
        set
        {
            this.rOLE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string ROLE_FLAG
    {
        get
        {
            return this.rOLE_FLAGField;
        }
        set
        {
            this.rOLE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int ROLE_ID
    {
        get
        {
            return this.rOLE_IDField;
        }
        set
        {
            this.rOLE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROLE_IDSpecified
    {
        get
        {
            return this.rOLE_IDFieldSpecified;
        }
        set
        {
            this.rOLE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string ROLE_NAME
    {
        get
        {
            return this.rOLE_NAMEField;
        }
        set
        {
            this.rOLE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int ROLE_ORDER
    {
        get
        {
            return this.rOLE_ORDERField;
        }
        set
        {
            this.rOLE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROLE_ORDERSpecified
    {
        get
        {
            return this.rOLE_ORDERFieldSpecified;
        }
        set
        {
            this.rOLE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string ROLE_REMARK
    {
        get
        {
            return this.rOLE_REMARKField;
        }
        set
        {
            this.rOLE_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int ROLE_START_MENU_ID
    {
        get
        {
            return this.rOLE_START_MENU_IDField;
        }
        set
        {
            this.rOLE_START_MENU_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROLE_START_MENU_IDSpecified
    {
        get
        {
            return this.rOLE_START_MENU_IDFieldSpecified;
        }
        set
        {
            this.rOLE_START_MENU_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class FLOW_ACTION
{
    
    private string fLOW_ACTION_CODEField;
    
    private string fLOW_ACTION_DEFAULTField;
    
    private string fLOW_ACTION_EVENTField;
    
    private string fLOW_ACTION_FLAGField;
    
    private int fLOW_ACTION_IDField;
    
    private bool fLOW_ACTION_IDFieldSpecified;
    
    private string fLOW_ACTION_IMAGEField;
    
    private string fLOW_ACTION_NAMEField;
    
    private int fLOW_ACTION_ORDERField;
    
    private bool fLOW_ACTION_ORDERFieldSpecified;
    
    private string fLOW_ACTION_REMARKField;
    
    private int fLOW_NODE_IDField;
    
    private bool fLOW_NODE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string FLOW_ACTION_CODE
    {
        get
        {
            return this.fLOW_ACTION_CODEField;
        }
        set
        {
            this.fLOW_ACTION_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string FLOW_ACTION_DEFAULT
    {
        get
        {
            return this.fLOW_ACTION_DEFAULTField;
        }
        set
        {
            this.fLOW_ACTION_DEFAULTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string FLOW_ACTION_EVENT
    {
        get
        {
            return this.fLOW_ACTION_EVENTField;
        }
        set
        {
            this.fLOW_ACTION_EVENTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string FLOW_ACTION_FLAG
    {
        get
        {
            return this.fLOW_ACTION_FLAGField;
        }
        set
        {
            this.fLOW_ACTION_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int FLOW_ACTION_ID
    {
        get
        {
            return this.fLOW_ACTION_IDField;
        }
        set
        {
            this.fLOW_ACTION_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_ACTION_IDSpecified
    {
        get
        {
            return this.fLOW_ACTION_IDFieldSpecified;
        }
        set
        {
            this.fLOW_ACTION_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string FLOW_ACTION_IMAGE
    {
        get
        {
            return this.fLOW_ACTION_IMAGEField;
        }
        set
        {
            this.fLOW_ACTION_IMAGEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string FLOW_ACTION_NAME
    {
        get
        {
            return this.fLOW_ACTION_NAMEField;
        }
        set
        {
            this.fLOW_ACTION_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int FLOW_ACTION_ORDER
    {
        get
        {
            return this.fLOW_ACTION_ORDERField;
        }
        set
        {
            this.fLOW_ACTION_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_ACTION_ORDERSpecified
    {
        get
        {
            return this.fLOW_ACTION_ORDERFieldSpecified;
        }
        set
        {
            this.fLOW_ACTION_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string FLOW_ACTION_REMARK
    {
        get
        {
            return this.fLOW_ACTION_REMARKField;
        }
        set
        {
            this.fLOW_ACTION_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public int FLOW_NODE_ID
    {
        get
        {
            return this.fLOW_NODE_IDField;
        }
        set
        {
            this.fLOW_NODE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_NODE_IDSpecified
    {
        get
        {
            return this.fLOW_NODE_IDFieldSpecified;
        }
        set
        {
            this.fLOW_NODE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class PLAN_MAIN
{
    
    private string pLAN_BEGIN_TIMEField;
    
    private string pLAN_BILL_DATEField;
    
    private string pLAN_CODEField;
    
    private string pLAN_CONFIRM_TIMEField;
    
    private string pLAN_CONFIRM_USERField;
    
    private string pLAN_CREATERField;
    
    private string pLAN_CREATE_TIMEField;
    
    private string pLAN_END_TIMEField;
    
    private string pLAN_FLAGField;
    
    private string pLAN_FROM_DEPTField;
    
    private string pLAN_FROM_USERField;
    
    private int pLAN_IDField;
    
    private bool pLAN_IDFieldSpecified;
    
    private int pLAN_RELATIVE_IDField;
    
    private bool pLAN_RELATIVE_IDFieldSpecified;
    
    private string pLAN_REMARKField;
    
    private string pLAN_STATUSField;
    
    private string pLAN_TO_DEPTField;
    
    private string pLAN_TO_USERField;
    
    private string pLAN_TYPE_CODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string PLAN_BEGIN_TIME
    {
        get
        {
            return this.pLAN_BEGIN_TIMEField;
        }
        set
        {
            this.pLAN_BEGIN_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string PLAN_BILL_DATE
    {
        get
        {
            return this.pLAN_BILL_DATEField;
        }
        set
        {
            this.pLAN_BILL_DATEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string PLAN_CODE
    {
        get
        {
            return this.pLAN_CODEField;
        }
        set
        {
            this.pLAN_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string PLAN_CONFIRM_TIME
    {
        get
        {
            return this.pLAN_CONFIRM_TIMEField;
        }
        set
        {
            this.pLAN_CONFIRM_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string PLAN_CONFIRM_USER
    {
        get
        {
            return this.pLAN_CONFIRM_USERField;
        }
        set
        {
            this.pLAN_CONFIRM_USERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string PLAN_CREATER
    {
        get
        {
            return this.pLAN_CREATERField;
        }
        set
        {
            this.pLAN_CREATERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string PLAN_CREATE_TIME
    {
        get
        {
            return this.pLAN_CREATE_TIMEField;
        }
        set
        {
            this.pLAN_CREATE_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string PLAN_END_TIME
    {
        get
        {
            return this.pLAN_END_TIMEField;
        }
        set
        {
            this.pLAN_END_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string PLAN_FLAG
    {
        get
        {
            return this.pLAN_FLAGField;
        }
        set
        {
            this.pLAN_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string PLAN_FROM_DEPT
    {
        get
        {
            return this.pLAN_FROM_DEPTField;
        }
        set
        {
            this.pLAN_FROM_DEPTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string PLAN_FROM_USER
    {
        get
        {
            return this.pLAN_FROM_USERField;
        }
        set
        {
            this.pLAN_FROM_USERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int PLAN_ID
    {
        get
        {
            return this.pLAN_IDField;
        }
        set
        {
            this.pLAN_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_IDSpecified
    {
        get
        {
            return this.pLAN_IDFieldSpecified;
        }
        set
        {
            this.pLAN_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public int PLAN_RELATIVE_ID
    {
        get
        {
            return this.pLAN_RELATIVE_IDField;
        }
        set
        {
            this.pLAN_RELATIVE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_RELATIVE_IDSpecified
    {
        get
        {
            return this.pLAN_RELATIVE_IDFieldSpecified;
        }
        set
        {
            this.pLAN_RELATIVE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string PLAN_REMARK
    {
        get
        {
            return this.pLAN_REMARKField;
        }
        set
        {
            this.pLAN_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
    public string PLAN_STATUS
    {
        get
        {
            return this.pLAN_STATUSField;
        }
        set
        {
            this.pLAN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string PLAN_TO_DEPT
    {
        get
        {
            return this.pLAN_TO_DEPTField;
        }
        set
        {
            this.pLAN_TO_DEPTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
    public string PLAN_TO_USER
    {
        get
        {
            return this.pLAN_TO_USERField;
        }
        set
        {
            this.pLAN_TO_USERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=17)]
    public string PLAN_TYPE_CODE
    {
        get
        {
            return this.pLAN_TYPE_CODEField;
        }
        set
        {
            this.pLAN_TYPE_CODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_ITEM_LIST
{
    
    private int iTEM_IDField;
    
    private bool iTEM_IDFieldSpecified;
    
    private string iTEM_LIST_CODEField;
    
    private string iTEM_LIST_FLAGField;
    
    private int iTEM_LIST_IDField;
    
    private bool iTEM_LIST_IDFieldSpecified;
    
    private string iTEM_LIST_NAMEField;
    
    private int iTEM_LIST_ORDERField;
    
    private bool iTEM_LIST_ORDERFieldSpecified;
    
    private string iTEM_LIST_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int ITEM_ID
    {
        get
        {
            return this.iTEM_IDField;
        }
        set
        {
            this.iTEM_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ITEM_IDSpecified
    {
        get
        {
            return this.iTEM_IDFieldSpecified;
        }
        set
        {
            this.iTEM_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string ITEM_LIST_CODE
    {
        get
        {
            return this.iTEM_LIST_CODEField;
        }
        set
        {
            this.iTEM_LIST_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string ITEM_LIST_FLAG
    {
        get
        {
            return this.iTEM_LIST_FLAGField;
        }
        set
        {
            this.iTEM_LIST_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int ITEM_LIST_ID
    {
        get
        {
            return this.iTEM_LIST_IDField;
        }
        set
        {
            this.iTEM_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ITEM_LIST_IDSpecified
    {
        get
        {
            return this.iTEM_LIST_IDFieldSpecified;
        }
        set
        {
            this.iTEM_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string ITEM_LIST_NAME
    {
        get
        {
            return this.iTEM_LIST_NAMEField;
        }
        set
        {
            this.iTEM_LIST_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int ITEM_LIST_ORDER
    {
        get
        {
            return this.iTEM_LIST_ORDERField;
        }
        set
        {
            this.iTEM_LIST_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ITEM_LIST_ORDERSpecified
    {
        get
        {
            return this.iTEM_LIST_ORDERFieldSpecified;
        }
        set
        {
            this.iTEM_LIST_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string ITEM_LIST_REMARK
    {
        get
        {
            return this.iTEM_LIST_REMARKField;
        }
        set
        {
            this.iTEM_LIST_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_RELATION
{
    
    private string rELATION_CODEField;
    
    private int rELATION_IDField;
    
    private bool rELATION_IDFieldSpecified;
    
    private string rELATION_ID1Field;
    
    private string rELATION_ID2Field;
    
    private string rELATION_NAMEField;
    
    private string rELATION_REMARKField;
    
    private string rELATON_NAME1Field;
    
    private string rELATON_NAME2Field;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string RELATION_CODE
    {
        get
        {
            return this.rELATION_CODEField;
        }
        set
        {
            this.rELATION_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int RELATION_ID
    {
        get
        {
            return this.rELATION_IDField;
        }
        set
        {
            this.rELATION_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATION_IDSpecified
    {
        get
        {
            return this.rELATION_IDFieldSpecified;
        }
        set
        {
            this.rELATION_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string RELATION_ID1
    {
        get
        {
            return this.rELATION_ID1Field;
        }
        set
        {
            this.rELATION_ID1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string RELATION_ID2
    {
        get
        {
            return this.rELATION_ID2Field;
        }
        set
        {
            this.rELATION_ID2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string RELATION_NAME
    {
        get
        {
            return this.rELATION_NAMEField;
        }
        set
        {
            this.rELATION_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string RELATION_REMARK
    {
        get
        {
            return this.rELATION_REMARKField;
        }
        set
        {
            this.rELATION_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string RELATON_NAME1
    {
        get
        {
            return this.rELATON_NAME1Field;
        }
        set
        {
            this.rELATON_NAME1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string RELATON_NAME2
    {
        get
        {
            return this.rELATON_NAME2Field;
        }
        set
        {
            this.rELATON_NAME2Field = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class FIELD_DESCRIPTION
{
    
    private int allowQueryField;
    
    private bool allowQueryFieldSpecified;
    
    private string columnField;
    
    private string controlTypeField;
    
    private string dataBindField;
    
    private string dbTypeField;
    
    private string defaultValueField;
    
    private string headerField;
    
    private int orderField;
    
    private bool orderFieldSpecified;
    
    private string queryOperationField;
    
    private int readOnlyField;
    
    private bool readOnlyFieldSpecified;
    
    private string remarkField;
    
    private string validationField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int AllowQuery
    {
        get
        {
            return this.allowQueryField;
        }
        set
        {
            this.allowQueryField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AllowQuerySpecified
    {
        get
        {
            return this.allowQueryFieldSpecified;
        }
        set
        {
            this.allowQueryFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string Column
    {
        get
        {
            return this.columnField;
        }
        set
        {
            this.columnField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string ControlType
    {
        get
        {
            return this.controlTypeField;
        }
        set
        {
            this.controlTypeField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string DataBind
    {
        get
        {
            return this.dataBindField;
        }
        set
        {
            this.dataBindField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string DbType
    {
        get
        {
            return this.dbTypeField;
        }
        set
        {
            this.dbTypeField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string DefaultValue
    {
        get
        {
            return this.defaultValueField;
        }
        set
        {
            this.defaultValueField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string Header
    {
        get
        {
            return this.headerField;
        }
        set
        {
            this.headerField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int Order
    {
        get
        {
            return this.orderField;
        }
        set
        {
            this.orderField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool OrderSpecified
    {
        get
        {
            return this.orderFieldSpecified;
        }
        set
        {
            this.orderFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string QueryOperation
    {
        get
        {
            return this.queryOperationField;
        }
        set
        {
            this.queryOperationField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public int ReadOnly
    {
        get
        {
            return this.readOnlyField;
        }
        set
        {
            this.readOnlyField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ReadOnlySpecified
    {
        get
        {
            return this.readOnlyFieldSpecified;
        }
        set
        {
            this.readOnlyFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string Remark
    {
        get
        {
            return this.remarkField;
        }
        set
        {
            this.remarkField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string Validation
    {
        get
        {
            return this.validationField;
        }
        set
        {
            this.validationField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class FLOW_NODE
{
    
    private string fLOW_NODE_CODEField;
    
    private string fLOW_NODE_FLAGField;
    
    private int fLOW_NODE_IDField;
    
    private bool fLOW_NODE_IDFieldSpecified;
    
    private string fLOW_NODE_NAMEField;
    
    private int fLOW_NODE_ORDERField;
    
    private bool fLOW_NODE_ORDERFieldSpecified;
    
    private string fLOW_NODE_REMARKField;
    
    private int fLOW_TYPE_IDField;
    
    private bool fLOW_TYPE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string FLOW_NODE_CODE
    {
        get
        {
            return this.fLOW_NODE_CODEField;
        }
        set
        {
            this.fLOW_NODE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string FLOW_NODE_FLAG
    {
        get
        {
            return this.fLOW_NODE_FLAGField;
        }
        set
        {
            this.fLOW_NODE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int FLOW_NODE_ID
    {
        get
        {
            return this.fLOW_NODE_IDField;
        }
        set
        {
            this.fLOW_NODE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_NODE_IDSpecified
    {
        get
        {
            return this.fLOW_NODE_IDFieldSpecified;
        }
        set
        {
            this.fLOW_NODE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string FLOW_NODE_NAME
    {
        get
        {
            return this.fLOW_NODE_NAMEField;
        }
        set
        {
            this.fLOW_NODE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int FLOW_NODE_ORDER
    {
        get
        {
            return this.fLOW_NODE_ORDERField;
        }
        set
        {
            this.fLOW_NODE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_NODE_ORDERSpecified
    {
        get
        {
            return this.fLOW_NODE_ORDERFieldSpecified;
        }
        set
        {
            this.fLOW_NODE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string FLOW_NODE_REMARK
    {
        get
        {
            return this.fLOW_NODE_REMARKField;
        }
        set
        {
            this.fLOW_NODE_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int FLOW_TYPE_ID
    {
        get
        {
            return this.fLOW_TYPE_IDField;
        }
        set
        {
            this.fLOW_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_TYPE_IDSpecified
    {
        get
        {
            return this.fLOW_TYPE_IDFieldSpecified;
        }
        set
        {
            this.fLOW_TYPE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class FLOW_PARA
{
    
    private string fLOW_PARA_CODEField;
    
    private string fLOW_PARA_FLAGField;
    
    private int fLOW_PARA_IDField;
    
    private bool fLOW_PARA_IDFieldSpecified;
    
    private string fLOW_PARA_NAMEField;
    
    private int fLOW_PARA_ORDERField;
    
    private bool fLOW_PARA_ORDERFieldSpecified;
    
    private string fLOW_PARA_REMARKField;
    
    private int fLOW_TYPE_IDField;
    
    private bool fLOW_TYPE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string FLOW_PARA_CODE
    {
        get
        {
            return this.fLOW_PARA_CODEField;
        }
        set
        {
            this.fLOW_PARA_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string FLOW_PARA_FLAG
    {
        get
        {
            return this.fLOW_PARA_FLAGField;
        }
        set
        {
            this.fLOW_PARA_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int FLOW_PARA_ID
    {
        get
        {
            return this.fLOW_PARA_IDField;
        }
        set
        {
            this.fLOW_PARA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_PARA_IDSpecified
    {
        get
        {
            return this.fLOW_PARA_IDFieldSpecified;
        }
        set
        {
            this.fLOW_PARA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string FLOW_PARA_NAME
    {
        get
        {
            return this.fLOW_PARA_NAMEField;
        }
        set
        {
            this.fLOW_PARA_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int FLOW_PARA_ORDER
    {
        get
        {
            return this.fLOW_PARA_ORDERField;
        }
        set
        {
            this.fLOW_PARA_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_PARA_ORDERSpecified
    {
        get
        {
            return this.fLOW_PARA_ORDERFieldSpecified;
        }
        set
        {
            this.fLOW_PARA_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string FLOW_PARA_REMARK
    {
        get
        {
            return this.fLOW_PARA_REMARKField;
        }
        set
        {
            this.fLOW_PARA_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int FLOW_TYPE_ID
    {
        get
        {
            return this.fLOW_TYPE_IDField;
        }
        set
        {
            this.fLOW_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_TYPE_IDSpecified
    {
        get
        {
            return this.fLOW_TYPE_IDFieldSpecified;
        }
        set
        {
            this.fLOW_TYPE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class IO_CONTROL
{
    
    private string cELL_GROUPField;
    
    private string cONTROL_BEGIN_TIMEField;
    
    private string cONTROL_END_TIMEField;
    
    private int cONTROL_IDField;
    
    private bool cONTROL_IDFieldSpecified;
    
    private string cONTROL_REMARKField;
    
    private int cONTROL_STATUSField;
    
    private bool cONTROL_STATUSFieldSpecified;
    
    private string cONTROL_TASK_LEVELField;
    
    private int cONTROL_TASK_TYPEField;
    
    private bool cONTROL_TASK_TYPEFieldSpecified;
    
    private string eND_DEVICE_CODEField;
    
    private string eND_WAREHOUSE_CODEField;
    
    private string eRROR_TEXTField;
    
    private int mANAGE_IDField;
    
    private bool mANAGE_IDFieldSpecified;
    
    private string pRE_CONTROL_STATUSField;
    
    private int rELATIVE_CONTROL_IDField;
    
    private bool rELATIVE_CONTROL_IDFieldSpecified;
    
    private string sTART_DEVICE_CODEField;
    
    private string sTART_WAREHOUSE_CODEField;
    
    private string sTOCK_BARCODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string CELL_GROUP
    {
        get
        {
            return this.cELL_GROUPField;
        }
        set
        {
            this.cELL_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CONTROL_BEGIN_TIME
    {
        get
        {
            return this.cONTROL_BEGIN_TIMEField;
        }
        set
        {
            this.cONTROL_BEGIN_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string CONTROL_END_TIME
    {
        get
        {
            return this.cONTROL_END_TIMEField;
        }
        set
        {
            this.cONTROL_END_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int CONTROL_ID
    {
        get
        {
            return this.cONTROL_IDField;
        }
        set
        {
            this.cONTROL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_IDSpecified
    {
        get
        {
            return this.cONTROL_IDFieldSpecified;
        }
        set
        {
            this.cONTROL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string CONTROL_REMARK
    {
        get
        {
            return this.cONTROL_REMARKField;
        }
        set
        {
            this.cONTROL_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int CONTROL_STATUS
    {
        get
        {
            return this.cONTROL_STATUSField;
        }
        set
        {
            this.cONTROL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_STATUSSpecified
    {
        get
        {
            return this.cONTROL_STATUSFieldSpecified;
        }
        set
        {
            this.cONTROL_STATUSFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string CONTROL_TASK_LEVEL
    {
        get
        {
            return this.cONTROL_TASK_LEVELField;
        }
        set
        {
            this.cONTROL_TASK_LEVELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int CONTROL_TASK_TYPE
    {
        get
        {
            return this.cONTROL_TASK_TYPEField;
        }
        set
        {
            this.cONTROL_TASK_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_TASK_TYPESpecified
    {
        get
        {
            return this.cONTROL_TASK_TYPEFieldSpecified;
        }
        set
        {
            this.cONTROL_TASK_TYPEFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string END_DEVICE_CODE
    {
        get
        {
            return this.eND_DEVICE_CODEField;
        }
        set
        {
            this.eND_DEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string END_WAREHOUSE_CODE
    {
        get
        {
            return this.eND_WAREHOUSE_CODEField;
        }
        set
        {
            this.eND_WAREHOUSE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string ERROR_TEXT
    {
        get
        {
            return this.eRROR_TEXTField;
        }
        set
        {
            this.eRROR_TEXTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int MANAGE_ID
    {
        get
        {
            return this.mANAGE_IDField;
        }
        set
        {
            this.mANAGE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_IDSpecified
    {
        get
        {
            return this.mANAGE_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string PRE_CONTROL_STATUS
    {
        get
        {
            return this.pRE_CONTROL_STATUSField;
        }
        set
        {
            this.pRE_CONTROL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public int RELATIVE_CONTROL_ID
    {
        get
        {
            return this.rELATIVE_CONTROL_IDField;
        }
        set
        {
            this.rELATIVE_CONTROL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATIVE_CONTROL_IDSpecified
    {
        get
        {
            return this.rELATIVE_CONTROL_IDFieldSpecified;
        }
        set
        {
            this.rELATIVE_CONTROL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
    public string START_DEVICE_CODE
    {
        get
        {
            return this.sTART_DEVICE_CODEField;
        }
        set
        {
            this.sTART_DEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string START_WAREHOUSE_CODE
    {
        get
        {
            return this.sTART_WAREHOUSE_CODEField;
        }
        set
        {
            this.sTART_WAREHOUSE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
    public string STOCK_BARCODE
    {
        get
        {
            return this.sTOCK_BARCODEField;
        }
        set
        {
            this.sTOCK_BARCODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class MANAGE_ACTION_EXCUTE
{
    
    private string aCTION_EVENTField;
    
    private int mANAGE_IDField;
    
    private bool mANAGE_IDFieldSpecified;
    
    private string mANAGE_TYPE_CODEField;
    
    private string nEXT_NODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string ACTION_EVENT
    {
        get
        {
            return this.aCTION_EVENTField;
        }
        set
        {
            this.aCTION_EVENTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int MANAGE_ID
    {
        get
        {
            return this.mANAGE_IDField;
        }
        set
        {
            this.mANAGE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_IDSpecified
    {
        get
        {
            return this.mANAGE_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string MANAGE_TYPE_CODE
    {
        get
        {
            return this.mANAGE_TYPE_CODEField;
        }
        set
        {
            this.mANAGE_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string NEXT_NODE
    {
        get
        {
            return this.nEXT_NODEField;
        }
        set
        {
            this.nEXT_NODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class IO_CONTROL_ROUTE
{
    
    private string cONTROL_ROUTE_CODEField;
    
    private int cONTROL_ROUTE_IDField;
    
    private bool cONTROL_ROUTE_IDFieldSpecified;
    
    private string cONTROL_ROUTE_NAMEField;
    
    private string cONTROL_ROUTE_REMARKField;
    
    private int cONTROL_ROUTE_STATUSField;
    
    private bool cONTROL_ROUTE_STATUSFieldSpecified;
    
    private int cONTROL_ROUTE_TYPEField;
    
    private bool cONTROL_ROUTE_TYPEFieldSpecified;
    
    private string eND_DEVICEField;
    
    private string sTART_DEVICEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string CONTROL_ROUTE_CODE
    {
        get
        {
            return this.cONTROL_ROUTE_CODEField;
        }
        set
        {
            this.cONTROL_ROUTE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int CONTROL_ROUTE_ID
    {
        get
        {
            return this.cONTROL_ROUTE_IDField;
        }
        set
        {
            this.cONTROL_ROUTE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_ROUTE_IDSpecified
    {
        get
        {
            return this.cONTROL_ROUTE_IDFieldSpecified;
        }
        set
        {
            this.cONTROL_ROUTE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string CONTROL_ROUTE_NAME
    {
        get
        {
            return this.cONTROL_ROUTE_NAMEField;
        }
        set
        {
            this.cONTROL_ROUTE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string CONTROL_ROUTE_REMARK
    {
        get
        {
            return this.cONTROL_ROUTE_REMARKField;
        }
        set
        {
            this.cONTROL_ROUTE_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int CONTROL_ROUTE_STATUS
    {
        get
        {
            return this.cONTROL_ROUTE_STATUSField;
        }
        set
        {
            this.cONTROL_ROUTE_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_ROUTE_STATUSSpecified
    {
        get
        {
            return this.cONTROL_ROUTE_STATUSFieldSpecified;
        }
        set
        {
            this.cONTROL_ROUTE_STATUSFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int CONTROL_ROUTE_TYPE
    {
        get
        {
            return this.cONTROL_ROUTE_TYPEField;
        }
        set
        {
            this.cONTROL_ROUTE_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_ROUTE_TYPESpecified
    {
        get
        {
            return this.cONTROL_ROUTE_TYPEFieldSpecified;
        }
        set
        {
            this.cONTROL_ROUTE_TYPEFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string END_DEVICE
    {
        get
        {
            return this.eND_DEVICEField;
        }
        set
        {
            this.eND_DEVICEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string START_DEVICE
    {
        get
        {
            return this.sTART_DEVICEField;
        }
        set
        {
            this.sTART_DEVICEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class PLAN_TYPE
{
    
    private string mANAGE_TYPE_CODEField;
    
    private string pLAN_TYPE_CLASSField;
    
    private string pLAN_TYPE_CODEField;
    
    private string pLAN_TYPE_FLAGField;
    
    private string pLAN_TYPE_GROUPField;
    
    private int pLAN_TYPE_IDField;
    
    private bool pLAN_TYPE_IDFieldSpecified;
    
    private string pLAN_TYPE_INOUTField;
    
    private string pLAN_TYPE_NAMEField;
    
    private int pLAN_TYPE_ORDERField;
    
    private bool pLAN_TYPE_ORDERFieldSpecified;
    
    private string pLAN_TYPE_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string MANAGE_TYPE_CODE
    {
        get
        {
            return this.mANAGE_TYPE_CODEField;
        }
        set
        {
            this.mANAGE_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string PLAN_TYPE_CLASS
    {
        get
        {
            return this.pLAN_TYPE_CLASSField;
        }
        set
        {
            this.pLAN_TYPE_CLASSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string PLAN_TYPE_CODE
    {
        get
        {
            return this.pLAN_TYPE_CODEField;
        }
        set
        {
            this.pLAN_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string PLAN_TYPE_FLAG
    {
        get
        {
            return this.pLAN_TYPE_FLAGField;
        }
        set
        {
            this.pLAN_TYPE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string PLAN_TYPE_GROUP
    {
        get
        {
            return this.pLAN_TYPE_GROUPField;
        }
        set
        {
            this.pLAN_TYPE_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int PLAN_TYPE_ID
    {
        get
        {
            return this.pLAN_TYPE_IDField;
        }
        set
        {
            this.pLAN_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_TYPE_IDSpecified
    {
        get
        {
            return this.pLAN_TYPE_IDFieldSpecified;
        }
        set
        {
            this.pLAN_TYPE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string PLAN_TYPE_INOUT
    {
        get
        {
            return this.pLAN_TYPE_INOUTField;
        }
        set
        {
            this.pLAN_TYPE_INOUTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string PLAN_TYPE_NAME
    {
        get
        {
            return this.pLAN_TYPE_NAMEField;
        }
        set
        {
            this.pLAN_TYPE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int PLAN_TYPE_ORDER
    {
        get
        {
            return this.pLAN_TYPE_ORDERField;
        }
        set
        {
            this.pLAN_TYPE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_TYPE_ORDERSpecified
    {
        get
        {
            return this.pLAN_TYPE_ORDERFieldSpecified;
        }
        set
        {
            this.pLAN_TYPE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string PLAN_TYPE_REMARK
    {
        get
        {
            return this.pLAN_TYPE_REMARKField;
        }
        set
        {
            this.pLAN_TYPE_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class WH_LOGIC
{
    
    private string lOGIC_CODEField;
    
    private string lOGIC_FLAGField;
    
    private int lOGIC_GROUPField;
    
    private bool lOGIC_GROUPFieldSpecified;
    
    private int lOGIC_IDField;
    
    private bool lOGIC_IDFieldSpecified;
    
    private string lOGIC_NAMEField;
    
    private int lOGIC_ORDERField;
    
    private bool lOGIC_ORDERFieldSpecified;
    
    private string lOGIC_REMARKField;
    
    private string lOGIC_TYPEField;
    
    private int wAREHOUSE_IDField;
    
    private bool wAREHOUSE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string LOGIC_CODE
    {
        get
        {
            return this.lOGIC_CODEField;
        }
        set
        {
            this.lOGIC_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string LOGIC_FLAG
    {
        get
        {
            return this.lOGIC_FLAGField;
        }
        set
        {
            this.lOGIC_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int LOGIC_GROUP
    {
        get
        {
            return this.lOGIC_GROUPField;
        }
        set
        {
            this.lOGIC_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOGIC_GROUPSpecified
    {
        get
        {
            return this.lOGIC_GROUPFieldSpecified;
        }
        set
        {
            this.lOGIC_GROUPFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int LOGIC_ID
    {
        get
        {
            return this.lOGIC_IDField;
        }
        set
        {
            this.lOGIC_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOGIC_IDSpecified
    {
        get
        {
            return this.lOGIC_IDFieldSpecified;
        }
        set
        {
            this.lOGIC_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string LOGIC_NAME
    {
        get
        {
            return this.lOGIC_NAMEField;
        }
        set
        {
            this.lOGIC_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int LOGIC_ORDER
    {
        get
        {
            return this.lOGIC_ORDERField;
        }
        set
        {
            this.lOGIC_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOGIC_ORDERSpecified
    {
        get
        {
            return this.lOGIC_ORDERFieldSpecified;
        }
        set
        {
            this.lOGIC_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string LOGIC_REMARK
    {
        get
        {
            return this.lOGIC_REMARKField;
        }
        set
        {
            this.lOGIC_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string LOGIC_TYPE
    {
        get
        {
            return this.lOGIC_TYPEField;
        }
        set
        {
            this.lOGIC_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int WAREHOUSE_ID
    {
        get
        {
            return this.wAREHOUSE_IDField;
        }
        set
        {
            this.wAREHOUSE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool WAREHOUSE_IDSpecified
    {
        get
        {
            return this.wAREHOUSE_IDFieldSpecified;
        }
        set
        {
            this.wAREHOUSE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class IO_CONTROL_APPLY_HIS
{
    
    private int aPPLY_TASK_STATUSField;
    
    private bool aPPLY_TASK_STATUSFieldSpecified;
    
    private int cONTROL_APPLY_IDField;
    
    private bool cONTROL_APPLY_IDFieldSpecified;
    
    private string cONTROL_APPLY_PARA01Field;
    
    private string cONTROL_APPLY_PARA02Field;
    
    private string cONTROL_APPLY_REMARKField;
    
    private string cONTROL_APPLY_TYPEField;
    
    private string cONTROL_ERROR_TEXTField;
    
    private int cONTROL_IDField;
    
    private bool cONTROL_IDFieldSpecified;
    
    private string cREATE_TIMEField;
    
    private string dEVICE_CODEField;
    
    private string mANAGE_ERROR_TEXTField;
    
    private string sTOCK_BARCODEField;
    
    private string wAREHOUSE_CODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int APPLY_TASK_STATUS
    {
        get
        {
            return this.aPPLY_TASK_STATUSField;
        }
        set
        {
            this.aPPLY_TASK_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool APPLY_TASK_STATUSSpecified
    {
        get
        {
            return this.aPPLY_TASK_STATUSFieldSpecified;
        }
        set
        {
            this.aPPLY_TASK_STATUSFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int CONTROL_APPLY_ID
    {
        get
        {
            return this.cONTROL_APPLY_IDField;
        }
        set
        {
            this.cONTROL_APPLY_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_APPLY_IDSpecified
    {
        get
        {
            return this.cONTROL_APPLY_IDFieldSpecified;
        }
        set
        {
            this.cONTROL_APPLY_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string CONTROL_APPLY_PARA01
    {
        get
        {
            return this.cONTROL_APPLY_PARA01Field;
        }
        set
        {
            this.cONTROL_APPLY_PARA01Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string CONTROL_APPLY_PARA02
    {
        get
        {
            return this.cONTROL_APPLY_PARA02Field;
        }
        set
        {
            this.cONTROL_APPLY_PARA02Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string CONTROL_APPLY_REMARK
    {
        get
        {
            return this.cONTROL_APPLY_REMARKField;
        }
        set
        {
            this.cONTROL_APPLY_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string CONTROL_APPLY_TYPE
    {
        get
        {
            return this.cONTROL_APPLY_TYPEField;
        }
        set
        {
            this.cONTROL_APPLY_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string CONTROL_ERROR_TEXT
    {
        get
        {
            return this.cONTROL_ERROR_TEXTField;
        }
        set
        {
            this.cONTROL_ERROR_TEXTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int CONTROL_ID
    {
        get
        {
            return this.cONTROL_IDField;
        }
        set
        {
            this.cONTROL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_IDSpecified
    {
        get
        {
            return this.cONTROL_IDFieldSpecified;
        }
        set
        {
            this.cONTROL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string CREATE_TIME
    {
        get
        {
            return this.cREATE_TIMEField;
        }
        set
        {
            this.cREATE_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string DEVICE_CODE
    {
        get
        {
            return this.dEVICE_CODEField;
        }
        set
        {
            this.dEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string MANAGE_ERROR_TEXT
    {
        get
        {
            return this.mANAGE_ERROR_TEXTField;
        }
        set
        {
            this.mANAGE_ERROR_TEXTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string STOCK_BARCODE
    {
        get
        {
            return this.sTOCK_BARCODEField;
        }
        set
        {
            this.sTOCK_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string WAREHOUSE_CODE
    {
        get
        {
            return this.wAREHOUSE_CODEField;
        }
        set
        {
            this.wAREHOUSE_CODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class PLAN_DETAIL
{
    
    private string bOX_BARCODEField;
    
    private string gOODS_BARCODEField;
    
    private int pLAN_DETAIL_IDField;
    
    private bool pLAN_DETAIL_IDFieldSpecified;
    
    private string pLAN_DETAIL_REMARKField;
    
    private int pLAN_LIST_IDField;
    
    private bool pLAN_LIST_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_BARCODE
    {
        get
        {
            return this.gOODS_BARCODEField;
        }
        set
        {
            this.gOODS_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int PLAN_DETAIL_ID
    {
        get
        {
            return this.pLAN_DETAIL_IDField;
        }
        set
        {
            this.pLAN_DETAIL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_DETAIL_IDSpecified
    {
        get
        {
            return this.pLAN_DETAIL_IDFieldSpecified;
        }
        set
        {
            this.pLAN_DETAIL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string PLAN_DETAIL_REMARK
    {
        get
        {
            return this.pLAN_DETAIL_REMARKField;
        }
        set
        {
            this.pLAN_DETAIL_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int PLAN_LIST_ID
    {
        get
        {
            return this.pLAN_LIST_IDField;
        }
        set
        {
            this.pLAN_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_IDSpecified
    {
        get
        {
            return this.pLAN_LIST_IDFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class RECORD_MAIN
{
    
    private string eND_POSITIONField;
    
    private string gOODS_TEMPLATE_CODEField;
    
    private string mANAGE_BEGIN_TIMEField;
    
    private string mANAGE_CONFIRM_TIMEField;
    
    private string mANAGE_END_TIMEField;
    
    private string mANAGE_TYPE_CODEField;
    
    private string pLAN_CODEField;
    
    private string pLAN_TYPE_CODEField;
    
    private int rECORD_IDField;
    
    private bool rECORD_IDFieldSpecified;
    
    private string rECORD_OPERATORField;
    
    private string rECORD_REMARKField;
    
    private string sTART_POSITIONField;
    
    private string sTOCK_BARCODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string END_POSITION
    {
        get
        {
            return this.eND_POSITIONField;
        }
        set
        {
            this.eND_POSITIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_TEMPLATE_CODE
    {
        get
        {
            return this.gOODS_TEMPLATE_CODEField;
        }
        set
        {
            this.gOODS_TEMPLATE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string MANAGE_BEGIN_TIME
    {
        get
        {
            return this.mANAGE_BEGIN_TIMEField;
        }
        set
        {
            this.mANAGE_BEGIN_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string MANAGE_CONFIRM_TIME
    {
        get
        {
            return this.mANAGE_CONFIRM_TIMEField;
        }
        set
        {
            this.mANAGE_CONFIRM_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string MANAGE_END_TIME
    {
        get
        {
            return this.mANAGE_END_TIMEField;
        }
        set
        {
            this.mANAGE_END_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string MANAGE_TYPE_CODE
    {
        get
        {
            return this.mANAGE_TYPE_CODEField;
        }
        set
        {
            this.mANAGE_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string PLAN_CODE
    {
        get
        {
            return this.pLAN_CODEField;
        }
        set
        {
            this.pLAN_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string PLAN_TYPE_CODE
    {
        get
        {
            return this.pLAN_TYPE_CODEField;
        }
        set
        {
            this.pLAN_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int RECORD_ID
    {
        get
        {
            return this.rECORD_IDField;
        }
        set
        {
            this.rECORD_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RECORD_IDSpecified
    {
        get
        {
            return this.rECORD_IDFieldSpecified;
        }
        set
        {
            this.rECORD_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string RECORD_OPERATOR
    {
        get
        {
            return this.rECORD_OPERATORField;
        }
        set
        {
            this.rECORD_OPERATORField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string RECORD_REMARK
    {
        get
        {
            return this.rECORD_REMARKField;
        }
        set
        {
            this.rECORD_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string START_POSITION
    {
        get
        {
            return this.sTART_POSITIONField;
        }
        set
        {
            this.sTART_POSITIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string STOCK_BARCODE
    {
        get
        {
            return this.sTOCK_BARCODEField;
        }
        set
        {
            this.sTOCK_BARCODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_USER
{
    
    private string uSER_CODEField;
    
    private string uSER_FLAGField;
    
    private int uSER_IDField;
    
    private bool uSER_IDFieldSpecified;
    
    private string uSER_NAMEField;
    
    private int uSER_ORDERField;
    
    private bool uSER_ORDERFieldSpecified;
    
    private string uSER_PASSWORDField;
    
    private string uSER_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string USER_CODE
    {
        get
        {
            return this.uSER_CODEField;
        }
        set
        {
            this.uSER_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string USER_FLAG
    {
        get
        {
            return this.uSER_FLAGField;
        }
        set
        {
            this.uSER_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int USER_ID
    {
        get
        {
            return this.uSER_IDField;
        }
        set
        {
            this.uSER_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool USER_IDSpecified
    {
        get
        {
            return this.uSER_IDFieldSpecified;
        }
        set
        {
            this.uSER_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string USER_NAME
    {
        get
        {
            return this.uSER_NAMEField;
        }
        set
        {
            this.uSER_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int USER_ORDER
    {
        get
        {
            return this.uSER_ORDERField;
        }
        set
        {
            this.uSER_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool USER_ORDERSpecified
    {
        get
        {
            return this.uSER_ORDERFieldSpecified;
        }
        set
        {
            this.uSER_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string USER_PASSWORD
    {
        get
        {
            return this.uSER_PASSWORDField;
        }
        set
        {
            this.uSER_PASSWORDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string USER_REMARK
    {
        get
        {
            return this.uSER_REMARKField;
        }
        set
        {
            this.uSER_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class TECHNICS_ROUTE
{
    
    private int cHECK_END_POSITION_FLAGField;
    
    private bool cHECK_END_POSITION_FLAGFieldSpecified;
    
    private int cHECK_START_POSITION_FLAGField;
    
    private bool cHECK_START_POSITION_FLAGFieldSpecified;
    
    private string dEVICE_TYPEField;
    
    private int eND_AREA_IDField;
    
    private bool eND_AREA_IDFieldSpecified;
    
    private int eND_NODE_FLAGField;
    
    private bool eND_NODE_FLAGFieldSpecified;
    
    private int eND_POSITIONField;
    
    private bool eND_POSITIONFieldSpecified;
    
    private string eP_END_CELL_STATUSField;
    
    private string eP_END_RUN_STATUSField;
    
    private string eP_START_CELL_STATUSField;
    
    private string eP_START_RUN_STATUSField;
    
    private int eXCEPTIONAL_ROUTE_IDField;
    
    private bool eXCEPTIONAL_ROUTE_IDFieldSpecified;
    
    private string fINISH_STATUSField;
    
    private string rOUTE_ACTIONSField;
    
    private string rOUTE_DESCRIPTIONField;
    
    private int rOUTE_IDField;
    
    private bool rOUTE_IDFieldSpecified;
    
    private int rOUTE_ORDERField;
    
    private bool rOUTE_ORDERFieldSpecified;
    
    private string rOUTE_REMARKField;
    
    private string sP_END_CELL_STATUSField;
    
    private string sP_END_RUN_STATUSField;
    
    private int sTART_AREA_IDField;
    
    private bool sTART_AREA_IDFieldSpecified;
    
    private int sTART_NODE_FLAGField;
    
    private bool sTART_NODE_FLAGFieldSpecified;
    
    private int sTART_POSITIONField;
    
    private bool sTART_POSITIONFieldSpecified;
    
    private string sTART_STATUSField;
    
    private int tASK_LEVELField;
    
    private bool tASK_LEVELFieldSpecified;
    
    private string tASK_TYPEField;
    
    private int tECHNICS_IDField;
    
    private bool tECHNICS_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int CHECK_END_POSITION_FLAG
    {
        get
        {
            return this.cHECK_END_POSITION_FLAGField;
        }
        set
        {
            this.cHECK_END_POSITION_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CHECK_END_POSITION_FLAGSpecified
    {
        get
        {
            return this.cHECK_END_POSITION_FLAGFieldSpecified;
        }
        set
        {
            this.cHECK_END_POSITION_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int CHECK_START_POSITION_FLAG
    {
        get
        {
            return this.cHECK_START_POSITION_FLAGField;
        }
        set
        {
            this.cHECK_START_POSITION_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CHECK_START_POSITION_FLAGSpecified
    {
        get
        {
            return this.cHECK_START_POSITION_FLAGFieldSpecified;
        }
        set
        {
            this.cHECK_START_POSITION_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string DEVICE_TYPE
    {
        get
        {
            return this.dEVICE_TYPEField;
        }
        set
        {
            this.dEVICE_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int END_AREA_ID
    {
        get
        {
            return this.eND_AREA_IDField;
        }
        set
        {
            this.eND_AREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_AREA_IDSpecified
    {
        get
        {
            return this.eND_AREA_IDFieldSpecified;
        }
        set
        {
            this.eND_AREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int END_NODE_FLAG
    {
        get
        {
            return this.eND_NODE_FLAGField;
        }
        set
        {
            this.eND_NODE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_NODE_FLAGSpecified
    {
        get
        {
            return this.eND_NODE_FLAGFieldSpecified;
        }
        set
        {
            this.eND_NODE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int END_POSITION
    {
        get
        {
            return this.eND_POSITIONField;
        }
        set
        {
            this.eND_POSITIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_POSITIONSpecified
    {
        get
        {
            return this.eND_POSITIONFieldSpecified;
        }
        set
        {
            this.eND_POSITIONFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string EP_END_CELL_STATUS
    {
        get
        {
            return this.eP_END_CELL_STATUSField;
        }
        set
        {
            this.eP_END_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string EP_END_RUN_STATUS
    {
        get
        {
            return this.eP_END_RUN_STATUSField;
        }
        set
        {
            this.eP_END_RUN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string EP_START_CELL_STATUS
    {
        get
        {
            return this.eP_START_CELL_STATUSField;
        }
        set
        {
            this.eP_START_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string EP_START_RUN_STATUS
    {
        get
        {
            return this.eP_START_RUN_STATUSField;
        }
        set
        {
            this.eP_START_RUN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=10)]
    public int EXCEPTIONAL_ROUTE_ID
    {
        get
        {
            return this.eXCEPTIONAL_ROUTE_IDField;
        }
        set
        {
            this.eXCEPTIONAL_ROUTE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool EXCEPTIONAL_ROUTE_IDSpecified
    {
        get
        {
            return this.eXCEPTIONAL_ROUTE_IDFieldSpecified;
        }
        set
        {
            this.eXCEPTIONAL_ROUTE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string FINISH_STATUS
    {
        get
        {
            return this.fINISH_STATUSField;
        }
        set
        {
            this.fINISH_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string ROUTE_ACTIONS
    {
        get
        {
            return this.rOUTE_ACTIONSField;
        }
        set
        {
            this.rOUTE_ACTIONSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string ROUTE_DESCRIPTION
    {
        get
        {
            return this.rOUTE_DESCRIPTIONField;
        }
        set
        {
            this.rOUTE_DESCRIPTIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public int ROUTE_ID
    {
        get
        {
            return this.rOUTE_IDField;
        }
        set
        {
            this.rOUTE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROUTE_IDSpecified
    {
        get
        {
            return this.rOUTE_IDFieldSpecified;
        }
        set
        {
            this.rOUTE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public int ROUTE_ORDER
    {
        get
        {
            return this.rOUTE_ORDERField;
        }
        set
        {
            this.rOUTE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROUTE_ORDERSpecified
    {
        get
        {
            return this.rOUTE_ORDERFieldSpecified;
        }
        set
        {
            this.rOUTE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
    public string ROUTE_REMARK
    {
        get
        {
            return this.rOUTE_REMARKField;
        }
        set
        {
            this.rOUTE_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=17)]
    public string SP_END_CELL_STATUS
    {
        get
        {
            return this.sP_END_CELL_STATUSField;
        }
        set
        {
            this.sP_END_CELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=18)]
    public string SP_END_RUN_STATUS
    {
        get
        {
            return this.sP_END_RUN_STATUSField;
        }
        set
        {
            this.sP_END_RUN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=19)]
    public int START_AREA_ID
    {
        get
        {
            return this.sTART_AREA_IDField;
        }
        set
        {
            this.sTART_AREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_AREA_IDSpecified
    {
        get
        {
            return this.sTART_AREA_IDFieldSpecified;
        }
        set
        {
            this.sTART_AREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=20)]
    public int START_NODE_FLAG
    {
        get
        {
            return this.sTART_NODE_FLAGField;
        }
        set
        {
            this.sTART_NODE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_NODE_FLAGSpecified
    {
        get
        {
            return this.sTART_NODE_FLAGFieldSpecified;
        }
        set
        {
            this.sTART_NODE_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=21)]
    public int START_POSITION
    {
        get
        {
            return this.sTART_POSITIONField;
        }
        set
        {
            this.sTART_POSITIONField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_POSITIONSpecified
    {
        get
        {
            return this.sTART_POSITIONFieldSpecified;
        }
        set
        {
            this.sTART_POSITIONFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=22)]
    public string START_STATUS
    {
        get
        {
            return this.sTART_STATUSField;
        }
        set
        {
            this.sTART_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=23)]
    public int TASK_LEVEL
    {
        get
        {
            return this.tASK_LEVELField;
        }
        set
        {
            this.tASK_LEVELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TASK_LEVELSpecified
    {
        get
        {
            return this.tASK_LEVELFieldSpecified;
        }
        set
        {
            this.tASK_LEVELFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=24)]
    public string TASK_TYPE
    {
        get
        {
            return this.tASK_TYPEField;
        }
        set
        {
            this.tASK_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=25)]
    public int TECHNICS_ID
    {
        get
        {
            return this.tECHNICS_IDField;
        }
        set
        {
            this.tECHNICS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TECHNICS_IDSpecified
    {
        get
        {
            return this.tECHNICS_IDFieldSpecified;
        }
        set
        {
            this.tECHNICS_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class PLAN_LIST
{
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private string gOODS_PROPERTY1Field;
    
    private string gOODS_PROPERTY2Field;
    
    private string gOODS_PROPERTY3Field;
    
    private string gOODS_PROPERTY4Field;
    
    private string gOODS_PROPERTY5Field;
    
    private string gOODS_PROPERTY6Field;
    
    private string gOODS_PROPERTY7Field;
    
    private string gOODS_PROPERTY8Field;
    
    private int pLAN_IDField;
    
    private bool pLAN_IDFieldSpecified;
    
    private string pLAN_LIST_CODEField;
    
    private decimal pLAN_LIST_FINISHED_QUANTITYField;
    
    private bool pLAN_LIST_FINISHED_QUANTITYFieldSpecified;
    
    private int pLAN_LIST_IDField;
    
    private bool pLAN_LIST_IDFieldSpecified;
    
    private decimal pLAN_LIST_ORDERED_QUANTITYField;
    
    private bool pLAN_LIST_ORDERED_QUANTITYFieldSpecified;
    
    private decimal pLAN_LIST_QUANTITYField;
    
    private bool pLAN_LIST_QUANTITYFieldSpecified;
    
    private string pLAN_LIST_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_PROPERTY1
    {
        get
        {
            return this.gOODS_PROPERTY1Field;
        }
        set
        {
            this.gOODS_PROPERTY1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string GOODS_PROPERTY2
    {
        get
        {
            return this.gOODS_PROPERTY2Field;
        }
        set
        {
            this.gOODS_PROPERTY2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_PROPERTY3
    {
        get
        {
            return this.gOODS_PROPERTY3Field;
        }
        set
        {
            this.gOODS_PROPERTY3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_PROPERTY4
    {
        get
        {
            return this.gOODS_PROPERTY4Field;
        }
        set
        {
            this.gOODS_PROPERTY4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string GOODS_PROPERTY5
    {
        get
        {
            return this.gOODS_PROPERTY5Field;
        }
        set
        {
            this.gOODS_PROPERTY5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_PROPERTY6
    {
        get
        {
            return this.gOODS_PROPERTY6Field;
        }
        set
        {
            this.gOODS_PROPERTY6Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string GOODS_PROPERTY7
    {
        get
        {
            return this.gOODS_PROPERTY7Field;
        }
        set
        {
            this.gOODS_PROPERTY7Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string GOODS_PROPERTY8
    {
        get
        {
            return this.gOODS_PROPERTY8Field;
        }
        set
        {
            this.gOODS_PROPERTY8Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public int PLAN_ID
    {
        get
        {
            return this.pLAN_IDField;
        }
        set
        {
            this.pLAN_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_IDSpecified
    {
        get
        {
            return this.pLAN_IDFieldSpecified;
        }
        set
        {
            this.pLAN_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string PLAN_LIST_CODE
    {
        get
        {
            return this.pLAN_LIST_CODEField;
        }
        set
        {
            this.pLAN_LIST_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public decimal PLAN_LIST_FINISHED_QUANTITY
    {
        get
        {
            return this.pLAN_LIST_FINISHED_QUANTITYField;
        }
        set
        {
            this.pLAN_LIST_FINISHED_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_FINISHED_QUANTITYSpecified
    {
        get
        {
            return this.pLAN_LIST_FINISHED_QUANTITYFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_FINISHED_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public int PLAN_LIST_ID
    {
        get
        {
            return this.pLAN_LIST_IDField;
        }
        set
        {
            this.pLAN_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_IDSpecified
    {
        get
        {
            return this.pLAN_LIST_IDFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public decimal PLAN_LIST_ORDERED_QUANTITY
    {
        get
        {
            return this.pLAN_LIST_ORDERED_QUANTITYField;
        }
        set
        {
            this.pLAN_LIST_ORDERED_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_ORDERED_QUANTITYSpecified
    {
        get
        {
            return this.pLAN_LIST_ORDERED_QUANTITYFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_ORDERED_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public decimal PLAN_LIST_QUANTITY
    {
        get
        {
            return this.pLAN_LIST_QUANTITYField;
        }
        set
        {
            this.pLAN_LIST_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_QUANTITYSpecified
    {
        get
        {
            return this.pLAN_LIST_QUANTITYFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string PLAN_LIST_REMARK
    {
        get
        {
            return this.pLAN_LIST_REMARKField;
        }
        set
        {
            this.pLAN_LIST_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_RELATION_LIST
{
    
    private int rELATION_IDField;
    
    private bool rELATION_IDFieldSpecified;
    
    private int rELATION_ID1Field;
    
    private bool rELATION_ID1FieldSpecified;
    
    private int rELATION_ID2Field;
    
    private bool rELATION_ID2FieldSpecified;
    
    private int rELATION_LIST_FLAGField;
    
    private bool rELATION_LIST_FLAGFieldSpecified;
    
    private int rELATION_LIST_IDField;
    
    private bool rELATION_LIST_IDFieldSpecified;
    
    private string rELATION_LIST_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int RELATION_ID
    {
        get
        {
            return this.rELATION_IDField;
        }
        set
        {
            this.rELATION_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATION_IDSpecified
    {
        get
        {
            return this.rELATION_IDFieldSpecified;
        }
        set
        {
            this.rELATION_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int RELATION_ID1
    {
        get
        {
            return this.rELATION_ID1Field;
        }
        set
        {
            this.rELATION_ID1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATION_ID1Specified
    {
        get
        {
            return this.rELATION_ID1FieldSpecified;
        }
        set
        {
            this.rELATION_ID1FieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int RELATION_ID2
    {
        get
        {
            return this.rELATION_ID2Field;
        }
        set
        {
            this.rELATION_ID2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATION_ID2Specified
    {
        get
        {
            return this.rELATION_ID2FieldSpecified;
        }
        set
        {
            this.rELATION_ID2FieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int RELATION_LIST_FLAG
    {
        get
        {
            return this.rELATION_LIST_FLAGField;
        }
        set
        {
            this.rELATION_LIST_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATION_LIST_FLAGSpecified
    {
        get
        {
            return this.rELATION_LIST_FLAGFieldSpecified;
        }
        set
        {
            this.rELATION_LIST_FLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int RELATION_LIST_ID
    {
        get
        {
            return this.rELATION_LIST_IDField;
        }
        set
        {
            this.rELATION_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RELATION_LIST_IDSpecified
    {
        get
        {
            return this.rELATION_LIST_IDFieldSpecified;
        }
        set
        {
            this.rELATION_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string RELATION_LIST_REMARK
    {
        get
        {
            return this.rELATION_LIST_REMARKField;
        }
        set
        {
            this.rELATION_LIST_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class ObjectList
{
    
    private object[] requestObjectField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Order=0)]
    [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.microsoft.com/2003/10/Serialization/Arrays")]
    public object[] RequestObject
    {
        get
        {
            return this.requestObjectField;
        }
        set
        {
            this.requestObjectField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_LOG
{
    
    private string lOG_DATEField;
    
    private int lOG_IDField;
    
    private bool lOG_IDFieldSpecified;
    
    private string lOG_LEVELField;
    
    private string lOG_LOGGERField;
    
    private string lOG_MESSAGEField;
    
    private string lOG_THREADField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string LOG_DATE
    {
        get
        {
            return this.lOG_DATEField;
        }
        set
        {
            this.lOG_DATEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int LOG_ID
    {
        get
        {
            return this.lOG_IDField;
        }
        set
        {
            this.lOG_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOG_IDSpecified
    {
        get
        {
            return this.lOG_IDFieldSpecified;
        }
        set
        {
            this.lOG_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string LOG_LEVEL
    {
        get
        {
            return this.lOG_LEVELField;
        }
        set
        {
            this.lOG_LEVELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string LOG_LOGGER
    {
        get
        {
            return this.lOG_LOGGERField;
        }
        set
        {
            this.lOG_LOGGERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string LOG_MESSAGE
    {
        get
        {
            return this.lOG_MESSAGEField;
        }
        set
        {
            this.lOG_MESSAGEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string LOG_THREAD
    {
        get
        {
            return this.lOG_THREADField;
        }
        set
        {
            this.lOG_THREADField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_MENU
{
    
    private string mENU_CHILDNODEFLAGField;
    
    private string mENU_CLASSField;
    
    private string mENU_DEVELOPFLAGField;
    
    private int mENU_GROUPField;
    
    private bool mENU_GROUPFieldSpecified;
    
    private int mENU_IDField;
    
    private bool mENU_IDFieldSpecified;
    
    private string mENU_IMAGEField;
    
    private string mENU_IMAGE_SLField;
    
    private string mENU_NAMEField;
    
    private int mENU_ORDERField;
    
    private bool mENU_ORDERFieldSpecified;
    
    private string mENU_PARAMETERField;
    
    private string mENU_PARAMETER_SLField;
    
    private int mENU_PARENT_IDField;
    
    private bool mENU_PARENT_IDFieldSpecified;
    
    private string mENU_REMARKField;
    
    private string mENU_SELECTEDFLAGField;
    
    private string mENU_SYSFLAGField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string MENU_CHILDNODEFLAG
    {
        get
        {
            return this.mENU_CHILDNODEFLAGField;
        }
        set
        {
            this.mENU_CHILDNODEFLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string MENU_CLASS
    {
        get
        {
            return this.mENU_CLASSField;
        }
        set
        {
            this.mENU_CLASSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string MENU_DEVELOPFLAG
    {
        get
        {
            return this.mENU_DEVELOPFLAGField;
        }
        set
        {
            this.mENU_DEVELOPFLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int MENU_GROUP
    {
        get
        {
            return this.mENU_GROUPField;
        }
        set
        {
            this.mENU_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MENU_GROUPSpecified
    {
        get
        {
            return this.mENU_GROUPFieldSpecified;
        }
        set
        {
            this.mENU_GROUPFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int MENU_ID
    {
        get
        {
            return this.mENU_IDField;
        }
        set
        {
            this.mENU_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MENU_IDSpecified
    {
        get
        {
            return this.mENU_IDFieldSpecified;
        }
        set
        {
            this.mENU_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string MENU_IMAGE
    {
        get
        {
            return this.mENU_IMAGEField;
        }
        set
        {
            this.mENU_IMAGEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string MENU_IMAGE_SL
    {
        get
        {
            return this.mENU_IMAGE_SLField;
        }
        set
        {
            this.mENU_IMAGE_SLField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string MENU_NAME
    {
        get
        {
            return this.mENU_NAMEField;
        }
        set
        {
            this.mENU_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int MENU_ORDER
    {
        get
        {
            return this.mENU_ORDERField;
        }
        set
        {
            this.mENU_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MENU_ORDERSpecified
    {
        get
        {
            return this.mENU_ORDERFieldSpecified;
        }
        set
        {
            this.mENU_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string MENU_PARAMETER
    {
        get
        {
            return this.mENU_PARAMETERField;
        }
        set
        {
            this.mENU_PARAMETERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string MENU_PARAMETER_SL
    {
        get
        {
            return this.mENU_PARAMETER_SLField;
        }
        set
        {
            this.mENU_PARAMETER_SLField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int MENU_PARENT_ID
    {
        get
        {
            return this.mENU_PARENT_IDField;
        }
        set
        {
            this.mENU_PARENT_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MENU_PARENT_IDSpecified
    {
        get
        {
            return this.mENU_PARENT_IDFieldSpecified;
        }
        set
        {
            this.mENU_PARENT_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string MENU_REMARK
    {
        get
        {
            return this.mENU_REMARKField;
        }
        set
        {
            this.mENU_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string MENU_SELECTEDFLAG
    {
        get
        {
            return this.mENU_SELECTEDFLAGField;
        }
        set
        {
            this.mENU_SELECTEDFLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
    public string MENU_SYSFLAG
    {
        get
        {
            return this.mENU_SYSFLAGField;
        }
        set
        {
            this.mENU_SYSFLAGField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class WH_AREA
{
    
    private string aREA_CODEField;
    
    private string aREA_FLAGField;
    
    private int aREA_GROUPField;
    
    private bool aREA_GROUPFieldSpecified;
    
    private int aREA_IDField;
    
    private bool aREA_IDFieldSpecified;
    
    private string aREA_NAMEField;
    
    private int aREA_ORDERField;
    
    private bool aREA_ORDERFieldSpecified;
    
    private string aREA_REMARKField;
    
    private string aREA_TYPEField;
    
    private int wAREHOUSE_IDField;
    
    private bool wAREHOUSE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string AREA_CODE
    {
        get
        {
            return this.aREA_CODEField;
        }
        set
        {
            this.aREA_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string AREA_FLAG
    {
        get
        {
            return this.aREA_FLAGField;
        }
        set
        {
            this.aREA_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int AREA_GROUP
    {
        get
        {
            return this.aREA_GROUPField;
        }
        set
        {
            this.aREA_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_GROUPSpecified
    {
        get
        {
            return this.aREA_GROUPFieldSpecified;
        }
        set
        {
            this.aREA_GROUPFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int AREA_ID
    {
        get
        {
            return this.aREA_IDField;
        }
        set
        {
            this.aREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_IDSpecified
    {
        get
        {
            return this.aREA_IDFieldSpecified;
        }
        set
        {
            this.aREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string AREA_NAME
    {
        get
        {
            return this.aREA_NAMEField;
        }
        set
        {
            this.aREA_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int AREA_ORDER
    {
        get
        {
            return this.aREA_ORDERField;
        }
        set
        {
            this.aREA_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_ORDERSpecified
    {
        get
        {
            return this.aREA_ORDERFieldSpecified;
        }
        set
        {
            this.aREA_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string AREA_REMARK
    {
        get
        {
            return this.aREA_REMARKField;
        }
        set
        {
            this.aREA_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string AREA_TYPE
    {
        get
        {
            return this.aREA_TYPEField;
        }
        set
        {
            this.aREA_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=8)]
    public int WAREHOUSE_ID
    {
        get
        {
            return this.wAREHOUSE_IDField;
        }
        set
        {
            this.wAREHOUSE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool WAREHOUSE_IDSpecified
    {
        get
        {
            return this.wAREHOUSE_IDFieldSpecified;
        }
        set
        {
            this.wAREHOUSE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_TABLE_CONVERTER
{
    
    private string cHILD_FOREIGN_KEYField;
    
    private string cHILD_TABLEField;
    
    private string pARENT_KEYField;
    
    private string pARENT_TABLEField;
    
    private string rEMARKField;
    
    private string sPLIT_PROPERTY_COLUMNField;
    
    private string sPLIT_PROPERTY_KEYField;
    
    private string sPLIT_PROPERTY_TYPEField;
    
    private string tABLE_CONVERTER_CODEField;
    
    private int tABLE_CONVERTER_IDField;
    
    private bool tABLE_CONVERTER_IDFieldSpecified;
    
    private string tABLE_CONVERTER_NAMEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string CHILD_FOREIGN_KEY
    {
        get
        {
            return this.cHILD_FOREIGN_KEYField;
        }
        set
        {
            this.cHILD_FOREIGN_KEYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CHILD_TABLE
    {
        get
        {
            return this.cHILD_TABLEField;
        }
        set
        {
            this.cHILD_TABLEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string PARENT_KEY
    {
        get
        {
            return this.pARENT_KEYField;
        }
        set
        {
            this.pARENT_KEYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string PARENT_TABLE
    {
        get
        {
            return this.pARENT_TABLEField;
        }
        set
        {
            this.pARENT_TABLEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string REMARK
    {
        get
        {
            return this.rEMARKField;
        }
        set
        {
            this.rEMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string SPLIT_PROPERTY_COLUMN
    {
        get
        {
            return this.sPLIT_PROPERTY_COLUMNField;
        }
        set
        {
            this.sPLIT_PROPERTY_COLUMNField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string SPLIT_PROPERTY_KEY
    {
        get
        {
            return this.sPLIT_PROPERTY_KEYField;
        }
        set
        {
            this.sPLIT_PROPERTY_KEYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string SPLIT_PROPERTY_TYPE
    {
        get
        {
            return this.sPLIT_PROPERTY_TYPEField;
        }
        set
        {
            this.sPLIT_PROPERTY_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string TABLE_CONVERTER_CODE
    {
        get
        {
            return this.tABLE_CONVERTER_CODEField;
        }
        set
        {
            this.tABLE_CONVERTER_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public int TABLE_CONVERTER_ID
    {
        get
        {
            return this.tABLE_CONVERTER_IDField;
        }
        set
        {
            this.tABLE_CONVERTER_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TABLE_CONVERTER_IDSpecified
    {
        get
        {
            return this.tABLE_CONVERTER_IDFieldSpecified;
        }
        set
        {
            this.tABLE_CONVERTER_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string TABLE_CONVERTER_NAME
    {
        get
        {
            return this.tABLE_CONVERTER_NAMEField;
        }
        set
        {
            this.tABLE_CONVERTER_NAMEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class GOODS_CLASS
{
    
    private string gOODS_CLASS_CODEField;
    
    private string gOODS_CLASS_FLAGField;
    
    private int gOODS_CLASS_IDField;
    
    private bool gOODS_CLASS_IDFieldSpecified;
    
    private string gOODS_CLASS_NAMEField;
    
    private int gOODS_CLASS_ORDERField;
    
    private bool gOODS_CLASS_ORDERFieldSpecified;
    
    private int gOODS_CLASS_PARENT_IDField;
    
    private bool gOODS_CLASS_PARENT_IDFieldSpecified;
    
    private string gOODS_CLASS_REMARKField;
    
    private int gOODS_TYPE_IDField;
    
    private bool gOODS_TYPE_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string GOODS_CLASS_CODE
    {
        get
        {
            return this.gOODS_CLASS_CODEField;
        }
        set
        {
            this.gOODS_CLASS_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_CLASS_FLAG
    {
        get
        {
            return this.gOODS_CLASS_FLAGField;
        }
        set
        {
            this.gOODS_CLASS_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int GOODS_CLASS_ID
    {
        get
        {
            return this.gOODS_CLASS_IDField;
        }
        set
        {
            this.gOODS_CLASS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_CLASS_IDSpecified
    {
        get
        {
            return this.gOODS_CLASS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_CLASS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_CLASS_NAME
    {
        get
        {
            return this.gOODS_CLASS_NAMEField;
        }
        set
        {
            this.gOODS_CLASS_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int GOODS_CLASS_ORDER
    {
        get
        {
            return this.gOODS_CLASS_ORDERField;
        }
        set
        {
            this.gOODS_CLASS_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_CLASS_ORDERSpecified
    {
        get
        {
            return this.gOODS_CLASS_ORDERFieldSpecified;
        }
        set
        {
            this.gOODS_CLASS_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int GOODS_CLASS_PARENT_ID
    {
        get
        {
            return this.gOODS_CLASS_PARENT_IDField;
        }
        set
        {
            this.gOODS_CLASS_PARENT_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_CLASS_PARENT_IDSpecified
    {
        get
        {
            return this.gOODS_CLASS_PARENT_IDFieldSpecified;
        }
        set
        {
            this.gOODS_CLASS_PARENT_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_CLASS_REMARK
    {
        get
        {
            return this.gOODS_CLASS_REMARKField;
        }
        set
        {
            this.gOODS_CLASS_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int GOODS_TYPE_ID
    {
        get
        {
            return this.gOODS_TYPE_IDField;
        }
        set
        {
            this.gOODS_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TYPE_IDSpecified
    {
        get
        {
            return this.gOODS_TYPE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TYPE_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class GOODS_TEMPLATE
{
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private string gOODS_TEMPLATE_CODEField;
    
    private string gOODS_TEMPLATE_FLAGField;
    
    private int gOODS_TEMPLATE_IDField;
    
    private bool gOODS_TEMPLATE_IDFieldSpecified;
    
    private string gOODS_TEMPLATE_NAMEField;
    
    private int gOODS_TEMPLATE_ORDERField;
    
    private bool gOODS_TEMPLATE_ORDERFieldSpecified;
    
    private string gOODS_TEMPLATE_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_TEMPLATE_CODE
    {
        get
        {
            return this.gOODS_TEMPLATE_CODEField;
        }
        set
        {
            this.gOODS_TEMPLATE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string GOODS_TEMPLATE_FLAG
    {
        get
        {
            return this.gOODS_TEMPLATE_FLAGField;
        }
        set
        {
            this.gOODS_TEMPLATE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int GOODS_TEMPLATE_ID
    {
        get
        {
            return this.gOODS_TEMPLATE_IDField;
        }
        set
        {
            this.gOODS_TEMPLATE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_IDSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_TEMPLATE_NAME
    {
        get
        {
            return this.gOODS_TEMPLATE_NAMEField;
        }
        set
        {
            this.gOODS_TEMPLATE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int GOODS_TEMPLATE_ORDER
    {
        get
        {
            return this.gOODS_TEMPLATE_ORDERField;
        }
        set
        {
            this.gOODS_TEMPLATE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_ORDERSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_ORDERFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_TEMPLATE_REMARK
    {
        get
        {
            return this.gOODS_TEMPLATE_REMARKField;
        }
        set
        {
            this.gOODS_TEMPLATE_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_TABLE
{
    
    private string cOLUMN_NAMEField;
    
    private int tABLE_IDField;
    
    private bool tABLE_IDFieldSpecified;
    
    private string tABLE_NAMEField;
    
    private int tABLE_NEXT_IDField;
    
    private bool tABLE_NEXT_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string COLUMN_NAME
    {
        get
        {
            return this.cOLUMN_NAMEField;
        }
        set
        {
            this.cOLUMN_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int TABLE_ID
    {
        get
        {
            return this.tABLE_IDField;
        }
        set
        {
            this.tABLE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TABLE_IDSpecified
    {
        get
        {
            return this.tABLE_IDFieldSpecified;
        }
        set
        {
            this.tABLE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string TABLE_NAME
    {
        get
        {
            return this.tABLE_NAMEField;
        }
        set
        {
            this.tABLE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int TABLE_NEXT_ID
    {
        get
        {
            return this.tABLE_NEXT_IDField;
        }
        set
        {
            this.tABLE_NEXT_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TABLE_NEXT_IDSpecified
    {
        get
        {
            return this.tABLE_NEXT_IDFieldSpecified;
        }
        set
        {
            this.tABLE_NEXT_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class Log
{
    
    private string messageField;
    
    private string timeStampField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string Message
    {
        get
        {
            return this.messageField;
        }
        set
        {
            this.messageField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string TimeStamp
    {
        get
        {
            return this.timeStampField;
        }
        set
        {
            this.timeStampField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class PLAN_ACTION_EXCUTE
{
    
    private string aCTION_EVENTField;
    
    private int pLAN_IDField;
    
    private bool pLAN_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string ACTION_EVENT
    {
        get
        {
            return this.aCTION_EVENTField;
        }
        set
        {
            this.aCTION_EVENTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int PLAN_ID
    {
        get
        {
            return this.pLAN_IDField;
        }
        set
        {
            this.pLAN_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_IDSpecified
    {
        get
        {
            return this.pLAN_IDFieldSpecified;
        }
        set
        {
            this.pLAN_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_ITEM
{
    
    private string iTEM_CODEField;
    
    private string iTEM_FLAGField;
    
    private int iTEM_IDField;
    
    private bool iTEM_IDFieldSpecified;
    
    private string iTEM_NAMEField;
    
    private int iTEM_ORDERField;
    
    private bool iTEM_ORDERFieldSpecified;
    
    private int iTEM_PARENT_IDField;
    
    private bool iTEM_PARENT_IDFieldSpecified;
    
    private string iTEM_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string ITEM_CODE
    {
        get
        {
            return this.iTEM_CODEField;
        }
        set
        {
            this.iTEM_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string ITEM_FLAG
    {
        get
        {
            return this.iTEM_FLAGField;
        }
        set
        {
            this.iTEM_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int ITEM_ID
    {
        get
        {
            return this.iTEM_IDField;
        }
        set
        {
            this.iTEM_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ITEM_IDSpecified
    {
        get
        {
            return this.iTEM_IDFieldSpecified;
        }
        set
        {
            this.iTEM_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string ITEM_NAME
    {
        get
        {
            return this.iTEM_NAMEField;
        }
        set
        {
            this.iTEM_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int ITEM_ORDER
    {
        get
        {
            return this.iTEM_ORDERField;
        }
        set
        {
            this.iTEM_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ITEM_ORDERSpecified
    {
        get
        {
            return this.iTEM_ORDERFieldSpecified;
        }
        set
        {
            this.iTEM_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int ITEM_PARENT_ID
    {
        get
        {
            return this.iTEM_PARENT_IDField;
        }
        set
        {
            this.iTEM_PARENT_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ITEM_PARENT_IDSpecified
    {
        get
        {
            return this.iTEM_PARENT_IDFieldSpecified;
        }
        set
        {
            this.iTEM_PARENT_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string ITEM_REMARK
    {
        get
        {
            return this.iTEM_REMARKField;
        }
        set
        {
            this.iTEM_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class IO_CONTROL_APPLY
{
    
    private int aPPLY_TASK_STATUSField;
    
    private bool aPPLY_TASK_STATUSFieldSpecified;
    
    private int cONTROL_APPLY_IDField;
    
    private bool cONTROL_APPLY_IDFieldSpecified;
    
    private string cONTROL_APPLY_PARA01Field;
    
    private string cONTROL_APPLY_PARA02Field;
    
    private string cONTROL_APPLY_REMARKField;
    
    private string cONTROL_APPLY_TYPEField;
    
    private string cONTROL_ERROR_TEXTField;
    
    private int cONTROL_IDField;
    
    private bool cONTROL_IDFieldSpecified;
    
    private string cREATE_TIMEField;
    
    private string dEVICE_CODEField;
    
    private string mANAGE_ERROR_TEXTField;
    
    private string sTOCK_BARCODEField;
    
    private string wAREHOUSE_CODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int APPLY_TASK_STATUS
    {
        get
        {
            return this.aPPLY_TASK_STATUSField;
        }
        set
        {
            this.aPPLY_TASK_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool APPLY_TASK_STATUSSpecified
    {
        get
        {
            return this.aPPLY_TASK_STATUSFieldSpecified;
        }
        set
        {
            this.aPPLY_TASK_STATUSFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int CONTROL_APPLY_ID
    {
        get
        {
            return this.cONTROL_APPLY_IDField;
        }
        set
        {
            this.cONTROL_APPLY_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_APPLY_IDSpecified
    {
        get
        {
            return this.cONTROL_APPLY_IDFieldSpecified;
        }
        set
        {
            this.cONTROL_APPLY_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string CONTROL_APPLY_PARA01
    {
        get
        {
            return this.cONTROL_APPLY_PARA01Field;
        }
        set
        {
            this.cONTROL_APPLY_PARA01Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string CONTROL_APPLY_PARA02
    {
        get
        {
            return this.cONTROL_APPLY_PARA02Field;
        }
        set
        {
            this.cONTROL_APPLY_PARA02Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string CONTROL_APPLY_REMARK
    {
        get
        {
            return this.cONTROL_APPLY_REMARKField;
        }
        set
        {
            this.cONTROL_APPLY_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string CONTROL_APPLY_TYPE
    {
        get
        {
            return this.cONTROL_APPLY_TYPEField;
        }
        set
        {
            this.cONTROL_APPLY_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string CONTROL_ERROR_TEXT
    {
        get
        {
            return this.cONTROL_ERROR_TEXTField;
        }
        set
        {
            this.cONTROL_ERROR_TEXTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int CONTROL_ID
    {
        get
        {
            return this.cONTROL_IDField;
        }
        set
        {
            this.cONTROL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_IDSpecified
    {
        get
        {
            return this.cONTROL_IDFieldSpecified;
        }
        set
        {
            this.cONTROL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string CREATE_TIME
    {
        get
        {
            return this.cREATE_TIMEField;
        }
        set
        {
            this.cREATE_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string DEVICE_CODE
    {
        get
        {
            return this.dEVICE_CODEField;
        }
        set
        {
            this.dEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string MANAGE_ERROR_TEXT
    {
        get
        {
            return this.mANAGE_ERROR_TEXTField;
        }
        set
        {
            this.mANAGE_ERROR_TEXTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string STOCK_BARCODE
    {
        get
        {
            return this.sTOCK_BARCODEField;
        }
        set
        {
            this.sTOCK_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string WAREHOUSE_CODE
    {
        get
        {
            return this.wAREHOUSE_CODEField;
        }
        set
        {
            this.wAREHOUSE_CODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class LED_MAIN
{
    
    private string aUTO_FLAGField;
    
    private int cONTROL_TYPEField;
    
    private bool cONTROL_TYPEFieldSpecified;
    
    private string dEVICE_CODEField;
    
    private int lED_IDField;
    
    private bool lED_IDFieldSpecified;
    
    private string lED_IPField;
    
    private string lED_MAIN_PARA1Field;
    
    private string lED_MAIN_PARA2Field;
    
    private string lED_MAIN_PARA3Field;
    
    private string lED_MAIN_PARA4Field;
    
    private string lED_MAIN_PARA5Field;
    
    private string lED_MAIN_REMARKField;
    
    private string lED_STATUSField;
    
    private int lINE_NUMField;
    
    private bool lINE_NUMFieldSpecified;
    
    private int sCREEN_HEIGHTField;
    
    private bool sCREEN_HEIGHTFieldSpecified;
    
    private int sCREEN_WIDTHField;
    
    private bool sCREEN_WIDTHFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string AUTO_FLAG
    {
        get
        {
            return this.aUTO_FLAGField;
        }
        set
        {
            this.aUTO_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int CONTROL_TYPE
    {
        get
        {
            return this.cONTROL_TYPEField;
        }
        set
        {
            this.cONTROL_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CONTROL_TYPESpecified
    {
        get
        {
            return this.cONTROL_TYPEFieldSpecified;
        }
        set
        {
            this.cONTROL_TYPEFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string DEVICE_CODE
    {
        get
        {
            return this.dEVICE_CODEField;
        }
        set
        {
            this.dEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int LED_ID
    {
        get
        {
            return this.lED_IDField;
        }
        set
        {
            this.lED_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LED_IDSpecified
    {
        get
        {
            return this.lED_IDFieldSpecified;
        }
        set
        {
            this.lED_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string LED_IP
    {
        get
        {
            return this.lED_IPField;
        }
        set
        {
            this.lED_IPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string LED_MAIN_PARA1
    {
        get
        {
            return this.lED_MAIN_PARA1Field;
        }
        set
        {
            this.lED_MAIN_PARA1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string LED_MAIN_PARA2
    {
        get
        {
            return this.lED_MAIN_PARA2Field;
        }
        set
        {
            this.lED_MAIN_PARA2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string LED_MAIN_PARA3
    {
        get
        {
            return this.lED_MAIN_PARA3Field;
        }
        set
        {
            this.lED_MAIN_PARA3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string LED_MAIN_PARA4
    {
        get
        {
            return this.lED_MAIN_PARA4Field;
        }
        set
        {
            this.lED_MAIN_PARA4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string LED_MAIN_PARA5
    {
        get
        {
            return this.lED_MAIN_PARA5Field;
        }
        set
        {
            this.lED_MAIN_PARA5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string LED_MAIN_REMARK
    {
        get
        {
            return this.lED_MAIN_REMARKField;
        }
        set
        {
            this.lED_MAIN_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string LED_STATUS
    {
        get
        {
            return this.lED_STATUSField;
        }
        set
        {
            this.lED_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public int LINE_NUM
    {
        get
        {
            return this.lINE_NUMField;
        }
        set
        {
            this.lINE_NUMField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LINE_NUMSpecified
    {
        get
        {
            return this.lINE_NUMFieldSpecified;
        }
        set
        {
            this.lINE_NUMFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public int SCREEN_HEIGHT
    {
        get
        {
            return this.sCREEN_HEIGHTField;
        }
        set
        {
            this.sCREEN_HEIGHTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SCREEN_HEIGHTSpecified
    {
        get
        {
            return this.sCREEN_HEIGHTFieldSpecified;
        }
        set
        {
            this.sCREEN_HEIGHTFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public int SCREEN_WIDTH
    {
        get
        {
            return this.sCREEN_WIDTHField;
        }
        set
        {
            this.sCREEN_WIDTHField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool SCREEN_WIDTHSpecified
    {
        get
        {
            return this.sCREEN_WIDTHFieldSpecified;
        }
        set
        {
            this.sCREEN_WIDTHFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class MANAGE_DETAIL
{
    
    private string bOX_BARCODEField;
    
    private string gOODS_BARCODEField;
    
    private int mANAGE_DETAIL_IDField;
    
    private bool mANAGE_DETAIL_IDFieldSpecified;
    
    private string mANAGE_DETAIL_REMARKField;
    
    private int mANAGE_LIST_IDField;
    
    private bool mANAGE_LIST_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_BARCODE
    {
        get
        {
            return this.gOODS_BARCODEField;
        }
        set
        {
            this.gOODS_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int MANAGE_DETAIL_ID
    {
        get
        {
            return this.mANAGE_DETAIL_IDField;
        }
        set
        {
            this.mANAGE_DETAIL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_DETAIL_IDSpecified
    {
        get
        {
            return this.mANAGE_DETAIL_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_DETAIL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string MANAGE_DETAIL_REMARK
    {
        get
        {
            return this.mANAGE_DETAIL_REMARKField;
        }
        set
        {
            this.mANAGE_DETAIL_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int MANAGE_LIST_ID
    {
        get
        {
            return this.mANAGE_LIST_IDField;
        }
        set
        {
            this.mANAGE_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_LIST_IDSpecified
    {
        get
        {
            return this.mANAGE_LIST_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_LIST_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class QueryObject
{
    
    private string columnField;
    
    private string headerField;
    
    private string logicField;
    
    private string operationField;
    
    private string valueField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string Column
    {
        get
        {
            return this.columnField;
        }
        set
        {
            this.columnField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string Header
    {
        get
        {
            return this.headerField;
        }
        set
        {
            this.headerField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string Logic
    {
        get
        {
            return this.logicField;
        }
        set
        {
            this.logicField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string Operation
    {
        get
        {
            return this.operationField;
        }
        set
        {
            this.operationField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string Value
    {
        get
        {
            return this.valueField;
        }
        set
        {
            this.valueField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class RECORD_LIST
{
    
    private string bOX_BARCODEField;
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private string gOODS_PROPERTY1Field;
    
    private string gOODS_PROPERTY2Field;
    
    private string gOODS_PROPERTY3Field;
    
    private string gOODS_PROPERTY4Field;
    
    private string gOODS_PROPERTY5Field;
    
    private string gOODS_PROPERTY6Field;
    
    private string gOODS_PROPERTY7Field;
    
    private string gOODS_PROPERTY8Field;
    
    private int pLAN_LIST_IDField;
    
    private bool pLAN_LIST_IDFieldSpecified;
    
    private int rECORD_IDField;
    
    private bool rECORD_IDFieldSpecified;
    
    private int rECORD_LIST_IDField;
    
    private bool rECORD_LIST_IDFieldSpecified;
    
    private decimal rECORD_LIST_QUANTITYField;
    
    private bool rECORD_LIST_QUANTITYFieldSpecified;
    
    private string rECORD_LIST_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string GOODS_PROPERTY1
    {
        get
        {
            return this.gOODS_PROPERTY1Field;
        }
        set
        {
            this.gOODS_PROPERTY1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_PROPERTY2
    {
        get
        {
            return this.gOODS_PROPERTY2Field;
        }
        set
        {
            this.gOODS_PROPERTY2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_PROPERTY3
    {
        get
        {
            return this.gOODS_PROPERTY3Field;
        }
        set
        {
            this.gOODS_PROPERTY3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string GOODS_PROPERTY4
    {
        get
        {
            return this.gOODS_PROPERTY4Field;
        }
        set
        {
            this.gOODS_PROPERTY4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_PROPERTY5
    {
        get
        {
            return this.gOODS_PROPERTY5Field;
        }
        set
        {
            this.gOODS_PROPERTY5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string GOODS_PROPERTY6
    {
        get
        {
            return this.gOODS_PROPERTY6Field;
        }
        set
        {
            this.gOODS_PROPERTY6Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string GOODS_PROPERTY7
    {
        get
        {
            return this.gOODS_PROPERTY7Field;
        }
        set
        {
            this.gOODS_PROPERTY7Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string GOODS_PROPERTY8
    {
        get
        {
            return this.gOODS_PROPERTY8Field;
        }
        set
        {
            this.gOODS_PROPERTY8Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=10)]
    public int PLAN_LIST_ID
    {
        get
        {
            return this.pLAN_LIST_IDField;
        }
        set
        {
            this.pLAN_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_LIST_IDSpecified
    {
        get
        {
            return this.pLAN_LIST_IDFieldSpecified;
        }
        set
        {
            this.pLAN_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int RECORD_ID
    {
        get
        {
            return this.rECORD_IDField;
        }
        set
        {
            this.rECORD_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RECORD_IDSpecified
    {
        get
        {
            return this.rECORD_IDFieldSpecified;
        }
        set
        {
            this.rECORD_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public int RECORD_LIST_ID
    {
        get
        {
            return this.rECORD_LIST_IDField;
        }
        set
        {
            this.rECORD_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RECORD_LIST_IDSpecified
    {
        get
        {
            return this.rECORD_LIST_IDFieldSpecified;
        }
        set
        {
            this.rECORD_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public decimal RECORD_LIST_QUANTITY
    {
        get
        {
            return this.rECORD_LIST_QUANTITYField;
        }
        set
        {
            this.rECORD_LIST_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RECORD_LIST_QUANTITYSpecified
    {
        get
        {
            return this.rECORD_LIST_QUANTITYFieldSpecified;
        }
        set
        {
            this.rECORD_LIST_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
    public string RECORD_LIST_REMARK
    {
        get
        {
            return this.rECORD_LIST_REMARKField;
        }
        set
        {
            this.rECORD_LIST_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class GOODS_MAIN
{
    
    private int gOODS_CLASS_IDField;
    
    private bool gOODS_CLASS_IDFieldSpecified;
    
    private string gOODS_CODEField;
    
    private string gOODS_COLORField;
    
    private string gOODS_CONST_PROPERTY1Field;
    
    private string gOODS_CONST_PROPERTY2Field;
    
    private string gOODS_CONST_PROPERTY3Field;
    
    private string gOODS_CONST_PROPERTY4Field;
    
    private string gOODS_CONST_PROPERTY5Field;
    
    private string gOODS_CONST_PROPERTY6Field;
    
    private string gOODS_CONST_PROPERTY7Field;
    
    private string gOODS_CONST_PROPERTY8Field;
    
    private string gOODS_FLAGField;
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private decimal gOODS_LIMIT_LOWER_QUANTITYField;
    
    private bool gOODS_LIMIT_LOWER_QUANTITYFieldSpecified;
    
    private decimal gOODS_LIMIT_UPPER_QUANTITYField;
    
    private bool gOODS_LIMIT_UPPER_QUANTITYFieldSpecified;
    
    private string gOODS_NAMEField;
    
    private int gOODS_ORDERField;
    
    private bool gOODS_ORDERFieldSpecified;
    
    private string gOODS_REMARKField;
    
    private string gOODS_UNITSField;
    
    private int lOGIC_IDField;
    
    private bool lOGIC_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int GOODS_CLASS_ID
    {
        get
        {
            return this.gOODS_CLASS_IDField;
        }
        set
        {
            this.gOODS_CLASS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_CLASS_IDSpecified
    {
        get
        {
            return this.gOODS_CLASS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_CLASS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_CODE
    {
        get
        {
            return this.gOODS_CODEField;
        }
        set
        {
            this.gOODS_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string GOODS_COLOR
    {
        get
        {
            return this.gOODS_COLORField;
        }
        set
        {
            this.gOODS_COLORField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string GOODS_CONST_PROPERTY1
    {
        get
        {
            return this.gOODS_CONST_PROPERTY1Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY1Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_CONST_PROPERTY2
    {
        get
        {
            return this.gOODS_CONST_PROPERTY2Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY2Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string GOODS_CONST_PROPERTY3
    {
        get
        {
            return this.gOODS_CONST_PROPERTY3Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY3Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string GOODS_CONST_PROPERTY4
    {
        get
        {
            return this.gOODS_CONST_PROPERTY4Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY4Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string GOODS_CONST_PROPERTY5
    {
        get
        {
            return this.gOODS_CONST_PROPERTY5Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY5Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string GOODS_CONST_PROPERTY6
    {
        get
        {
            return this.gOODS_CONST_PROPERTY6Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY6Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string GOODS_CONST_PROPERTY7
    {
        get
        {
            return this.gOODS_CONST_PROPERTY7Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY7Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string GOODS_CONST_PROPERTY8
    {
        get
        {
            return this.gOODS_CONST_PROPERTY8Field;
        }
        set
        {
            this.gOODS_CONST_PROPERTY8Field = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string GOODS_FLAG
    {
        get
        {
            return this.gOODS_FLAGField;
        }
        set
        {
            this.gOODS_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=12)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public decimal GOODS_LIMIT_LOWER_QUANTITY
    {
        get
        {
            return this.gOODS_LIMIT_LOWER_QUANTITYField;
        }
        set
        {
            this.gOODS_LIMIT_LOWER_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_LIMIT_LOWER_QUANTITYSpecified
    {
        get
        {
            return this.gOODS_LIMIT_LOWER_QUANTITYFieldSpecified;
        }
        set
        {
            this.gOODS_LIMIT_LOWER_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public decimal GOODS_LIMIT_UPPER_QUANTITY
    {
        get
        {
            return this.gOODS_LIMIT_UPPER_QUANTITYField;
        }
        set
        {
            this.gOODS_LIMIT_UPPER_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_LIMIT_UPPER_QUANTITYSpecified
    {
        get
        {
            return this.gOODS_LIMIT_UPPER_QUANTITYFieldSpecified;
        }
        set
        {
            this.gOODS_LIMIT_UPPER_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=15)]
    public string GOODS_NAME
    {
        get
        {
            return this.gOODS_NAMEField;
        }
        set
        {
            this.gOODS_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=16)]
    public int GOODS_ORDER
    {
        get
        {
            return this.gOODS_ORDERField;
        }
        set
        {
            this.gOODS_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_ORDERSpecified
    {
        get
        {
            return this.gOODS_ORDERFieldSpecified;
        }
        set
        {
            this.gOODS_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=17)]
    public string GOODS_REMARK
    {
        get
        {
            return this.gOODS_REMARKField;
        }
        set
        {
            this.gOODS_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=18)]
    public string GOODS_UNITS
    {
        get
        {
            return this.gOODS_UNITSField;
        }
        set
        {
            this.gOODS_UNITSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=19)]
    public int LOGIC_ID
    {
        get
        {
            return this.lOGIC_IDField;
        }
        set
        {
            this.lOGIC_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOGIC_IDSpecified
    {
        get
        {
            return this.lOGIC_IDFieldSpecified;
        }
        set
        {
            this.lOGIC_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class GOODS_TEMPLATE_LIST
{
    
    private int gOODS_IDField;
    
    private bool gOODS_IDFieldSpecified;
    
    private int gOODS_TEMPLATE_IDField;
    
    private bool gOODS_TEMPLATE_IDFieldSpecified;
    
    private int gOODS_TEMPLATE_LIST_IDField;
    
    private bool gOODS_TEMPLATE_LIST_IDFieldSpecified;
    
    private decimal gOODS_TEMPLATE_QUANTITYField;
    
    private bool gOODS_TEMPLATE_QUANTITYFieldSpecified;
    
    private string gOODS_TEMPLATE_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int GOODS_ID
    {
        get
        {
            return this.gOODS_IDField;
        }
        set
        {
            this.gOODS_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_IDSpecified
    {
        get
        {
            return this.gOODS_IDFieldSpecified;
        }
        set
        {
            this.gOODS_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int GOODS_TEMPLATE_ID
    {
        get
        {
            return this.gOODS_TEMPLATE_IDField;
        }
        set
        {
            this.gOODS_TEMPLATE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_IDSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int GOODS_TEMPLATE_LIST_ID
    {
        get
        {
            return this.gOODS_TEMPLATE_LIST_IDField;
        }
        set
        {
            this.gOODS_TEMPLATE_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_LIST_IDSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_LIST_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public decimal GOODS_TEMPLATE_QUANTITY
    {
        get
        {
            return this.gOODS_TEMPLATE_QUANTITYField;
        }
        set
        {
            this.gOODS_TEMPLATE_QUANTITYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_QUANTITYSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_QUANTITYFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_QUANTITYFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string GOODS_TEMPLATE_REMARK
    {
        get
        {
            return this.gOODS_TEMPLATE_REMARKField;
        }
        set
        {
            this.gOODS_TEMPLATE_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class MANAGE_MAIN
{
    
    private string cELL_MODELField;
    
    private int eND_CELL_IDField;
    
    private bool eND_CELL_IDFieldSpecified;
    
    private string fULL_FLAGField;
    
    private int gOODS_TEMPLATE_IDField;
    
    private bool gOODS_TEMPLATE_IDFieldSpecified;
    
    private string mANAGE_BEGIN_TIMEField;
    
    private string mANAGE_CONFIRM_TIMEField;
    
    private string mANAGE_END_TIMEField;
    
    private int mANAGE_IDField;
    
    private bool mANAGE_IDFieldSpecified;
    
    private string mANAGE_LEVELField;
    
    private string mANAGE_OPERATORField;
    
    private string mANAGE_REMARKField;
    
    private string mANAGE_STATUSField;
    
    private string mANAGE_TYPE_CODEField;
    
    private int pLAN_IDField;
    
    private bool pLAN_IDFieldSpecified;
    
    private string pLAN_TYPE_CODEField;
    
    private int sTART_CELL_IDField;
    
    private bool sTART_CELL_IDFieldSpecified;
    
    private string sTOCK_BARCODEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string CELL_MODEL
    {
        get
        {
            return this.cELL_MODELField;
        }
        set
        {
            this.cELL_MODELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=1)]
    public int END_CELL_ID
    {
        get
        {
            return this.eND_CELL_IDField;
        }
        set
        {
            this.eND_CELL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_CELL_IDSpecified
    {
        get
        {
            return this.eND_CELL_IDFieldSpecified;
        }
        set
        {
            this.eND_CELL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string FULL_FLAG
    {
        get
        {
            return this.fULL_FLAGField;
        }
        set
        {
            this.fULL_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int GOODS_TEMPLATE_ID
    {
        get
        {
            return this.gOODS_TEMPLATE_IDField;
        }
        set
        {
            this.gOODS_TEMPLATE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_IDSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string MANAGE_BEGIN_TIME
    {
        get
        {
            return this.mANAGE_BEGIN_TIMEField;
        }
        set
        {
            this.mANAGE_BEGIN_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string MANAGE_CONFIRM_TIME
    {
        get
        {
            return this.mANAGE_CONFIRM_TIMEField;
        }
        set
        {
            this.mANAGE_CONFIRM_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string MANAGE_END_TIME
    {
        get
        {
            return this.mANAGE_END_TIMEField;
        }
        set
        {
            this.mANAGE_END_TIMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int MANAGE_ID
    {
        get
        {
            return this.mANAGE_IDField;
        }
        set
        {
            this.mANAGE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_IDSpecified
    {
        get
        {
            return this.mANAGE_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string MANAGE_LEVEL
    {
        get
        {
            return this.mANAGE_LEVELField;
        }
        set
        {
            this.mANAGE_LEVELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string MANAGE_OPERATOR
    {
        get
        {
            return this.mANAGE_OPERATORField;
        }
        set
        {
            this.mANAGE_OPERATORField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string MANAGE_REMARK
    {
        get
        {
            return this.mANAGE_REMARKField;
        }
        set
        {
            this.mANAGE_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string MANAGE_STATUS
    {
        get
        {
            return this.mANAGE_STATUSField;
        }
        set
        {
            this.mANAGE_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string MANAGE_TYPE_CODE
    {
        get
        {
            return this.mANAGE_TYPE_CODEField;
        }
        set
        {
            this.mANAGE_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=13)]
    public int PLAN_ID
    {
        get
        {
            return this.pLAN_IDField;
        }
        set
        {
            this.pLAN_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool PLAN_IDSpecified
    {
        get
        {
            return this.pLAN_IDFieldSpecified;
        }
        set
        {
            this.pLAN_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=14)]
    public string PLAN_TYPE_CODE
    {
        get
        {
            return this.pLAN_TYPE_CODEField;
        }
        set
        {
            this.pLAN_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public int START_CELL_ID
    {
        get
        {
            return this.sTART_CELL_IDField;
        }
        set
        {
            this.sTART_CELL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_CELL_IDSpecified
    {
        get
        {
            return this.sTART_CELL_IDFieldSpecified;
        }
        set
        {
            this.sTART_CELL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=16)]
    public string STOCK_BARCODE
    {
        get
        {
            return this.sTOCK_BARCODEField;
        }
        set
        {
            this.sTOCK_BARCODEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class STORAGE_MAIN
{
    
    private int cELL_IDField;
    
    private bool cELL_IDFieldSpecified;
    
    private string cELL_MODELField;
    
    private string fULL_FLAGField;
    
    private int gOODS_TEMPLATE_IDField;
    
    private bool gOODS_TEMPLATE_IDFieldSpecified;
    
    private string sTOCK_BARCODEField;
    
    private int sTORAGE_IDField;
    
    private bool sTORAGE_IDFieldSpecified;
    
    private string sTORAGE_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int CELL_ID
    {
        get
        {
            return this.cELL_IDField;
        }
        set
        {
            this.cELL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_IDSpecified
    {
        get
        {
            return this.cELL_IDFieldSpecified;
        }
        set
        {
            this.cELL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CELL_MODEL
    {
        get
        {
            return this.cELL_MODELField;
        }
        set
        {
            this.cELL_MODELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string FULL_FLAG
    {
        get
        {
            return this.fULL_FLAGField;
        }
        set
        {
            this.fULL_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int GOODS_TEMPLATE_ID
    {
        get
        {
            return this.gOODS_TEMPLATE_IDField;
        }
        set
        {
            this.gOODS_TEMPLATE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool GOODS_TEMPLATE_IDSpecified
    {
        get
        {
            return this.gOODS_TEMPLATE_IDFieldSpecified;
        }
        set
        {
            this.gOODS_TEMPLATE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string STOCK_BARCODE
    {
        get
        {
            return this.sTOCK_BARCODEField;
        }
        set
        {
            this.sTOCK_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int STORAGE_ID
    {
        get
        {
            return this.sTORAGE_IDField;
        }
        set
        {
            this.sTORAGE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool STORAGE_IDSpecified
    {
        get
        {
            return this.sTORAGE_IDFieldSpecified;
        }
        set
        {
            this.sTORAGE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string STORAGE_REMARK
    {
        get
        {
            return this.sTORAGE_REMARKField;
        }
        set
        {
            this.sTORAGE_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class WH_WAREHOUSE
{
    
    private string wAREHOUSE_CODEField;
    
    private string wAREHOUSE_FLAGField;
    
    private int wAREHOUSE_IDField;
    
    private bool wAREHOUSE_IDFieldSpecified;
    
    private string wAREHOUSE_NAMEField;
    
    private int wAREHOUSE_ORDERField;
    
    private bool wAREHOUSE_ORDERFieldSpecified;
    
    private string wAREHOUSE_REMARKField;
    
    private string wAREHOUSE_TYPEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string WAREHOUSE_CODE
    {
        get
        {
            return this.wAREHOUSE_CODEField;
        }
        set
        {
            this.wAREHOUSE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string WAREHOUSE_FLAG
    {
        get
        {
            return this.wAREHOUSE_FLAGField;
        }
        set
        {
            this.wAREHOUSE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int WAREHOUSE_ID
    {
        get
        {
            return this.wAREHOUSE_IDField;
        }
        set
        {
            this.wAREHOUSE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool WAREHOUSE_IDSpecified
    {
        get
        {
            return this.wAREHOUSE_IDFieldSpecified;
        }
        set
        {
            this.wAREHOUSE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string WAREHOUSE_NAME
    {
        get
        {
            return this.wAREHOUSE_NAMEField;
        }
        set
        {
            this.wAREHOUSE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int WAREHOUSE_ORDER
    {
        get
        {
            return this.wAREHOUSE_ORDERField;
        }
        set
        {
            this.wAREHOUSE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool WAREHOUSE_ORDERSpecified
    {
        get
        {
            return this.wAREHOUSE_ORDERFieldSpecified;
        }
        set
        {
            this.wAREHOUSE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string WAREHOUSE_REMARK
    {
        get
        {
            return this.wAREHOUSE_REMARKField;
        }
        set
        {
            this.wAREHOUSE_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string WAREHOUSE_TYPE
    {
        get
        {
            return this.wAREHOUSE_TYPEField;
        }
        set
        {
            this.wAREHOUSE_TYPEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class FLOW_TYPE
{
    
    private string fLOW_TYPE_CODEField;
    
    private string fLOW_TYPE_FLAGField;
    
    private int fLOW_TYPE_IDField;
    
    private bool fLOW_TYPE_IDFieldSpecified;
    
    private string fLOW_TYPE_NAMEField;
    
    private int fLOW_TYPE_ORDERField;
    
    private bool fLOW_TYPE_ORDERFieldSpecified;
    
    private string fLOW_TYPE_REMARKField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string FLOW_TYPE_CODE
    {
        get
        {
            return this.fLOW_TYPE_CODEField;
        }
        set
        {
            this.fLOW_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string FLOW_TYPE_FLAG
    {
        get
        {
            return this.fLOW_TYPE_FLAGField;
        }
        set
        {
            this.fLOW_TYPE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int FLOW_TYPE_ID
    {
        get
        {
            return this.fLOW_TYPE_IDField;
        }
        set
        {
            this.fLOW_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_TYPE_IDSpecified
    {
        get
        {
            return this.fLOW_TYPE_IDFieldSpecified;
        }
        set
        {
            this.fLOW_TYPE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string FLOW_TYPE_NAME
    {
        get
        {
            return this.fLOW_TYPE_NAMEField;
        }
        set
        {
            this.fLOW_TYPE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int FLOW_TYPE_ORDER
    {
        get
        {
            return this.fLOW_TYPE_ORDERField;
        }
        set
        {
            this.fLOW_TYPE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLOW_TYPE_ORDERSpecified
    {
        get
        {
            return this.fLOW_TYPE_ORDERFieldSpecified;
        }
        set
        {
            this.fLOW_TYPE_ORDERFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string FLOW_TYPE_REMARK
    {
        get
        {
            return this.fLOW_TYPE_REMARKField;
        }
        set
        {
            this.fLOW_TYPE_REMARKField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class MANAGE_TYPE
{
    
    private string mANAGE_TYPE_CLASSField;
    
    private string mANAGE_TYPE_CODEField;
    
    private string mANAGE_TYPE_FLAGField;
    
    private string mANAGE_TYPE_GROUPField;
    
    private int mANAGE_TYPE_IDField;
    
    private bool mANAGE_TYPE_IDFieldSpecified;
    
    private string mANAGE_TYPE_INOUTField;
    
    private string mANAGE_TYPE_NAMEField;
    
    private int mANAGE_TYPE_ORDERField;
    
    private bool mANAGE_TYPE_ORDERFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string MANAGE_TYPE_CLASS
    {
        get
        {
            return this.mANAGE_TYPE_CLASSField;
        }
        set
        {
            this.mANAGE_TYPE_CLASSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string MANAGE_TYPE_CODE
    {
        get
        {
            return this.mANAGE_TYPE_CODEField;
        }
        set
        {
            this.mANAGE_TYPE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string MANAGE_TYPE_FLAG
    {
        get
        {
            return this.mANAGE_TYPE_FLAGField;
        }
        set
        {
            this.mANAGE_TYPE_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string MANAGE_TYPE_GROUP
    {
        get
        {
            return this.mANAGE_TYPE_GROUPField;
        }
        set
        {
            this.mANAGE_TYPE_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int MANAGE_TYPE_ID
    {
        get
        {
            return this.mANAGE_TYPE_IDField;
        }
        set
        {
            this.mANAGE_TYPE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_TYPE_IDSpecified
    {
        get
        {
            return this.mANAGE_TYPE_IDFieldSpecified;
        }
        set
        {
            this.mANAGE_TYPE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string MANAGE_TYPE_INOUT
    {
        get
        {
            return this.mANAGE_TYPE_INOUTField;
        }
        set
        {
            this.mANAGE_TYPE_INOUTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string MANAGE_TYPE_NAME
    {
        get
        {
            return this.mANAGE_TYPE_NAMEField;
        }
        set
        {
            this.mANAGE_TYPE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=7)]
    public int MANAGE_TYPE_ORDER
    {
        get
        {
            return this.mANAGE_TYPE_ORDERField;
        }
        set
        {
            this.mANAGE_TYPE_ORDERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MANAGE_TYPE_ORDERSpecified
    {
        get
        {
            return this.mANAGE_TYPE_ORDERFieldSpecified;
        }
        set
        {
            this.mANAGE_TYPE_ORDERFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class RECORD_DETAIL
{
    
    private string bOX_BARCODEField;
    
    private string gOODS_BARCODEField;
    
    private int rECORD_DETAIL_IDField;
    
    private bool rECORD_DETAIL_IDFieldSpecified;
    
    private string rECORD_DETAIL_REMARKField;
    
    private int rECORD_LIST_IDField;
    
    private bool rECORD_LIST_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string BOX_BARCODE
    {
        get
        {
            return this.bOX_BARCODEField;
        }
        set
        {
            this.bOX_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string GOODS_BARCODE
    {
        get
        {
            return this.gOODS_BARCODEField;
        }
        set
        {
            this.gOODS_BARCODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int RECORD_DETAIL_ID
    {
        get
        {
            return this.rECORD_DETAIL_IDField;
        }
        set
        {
            this.rECORD_DETAIL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RECORD_DETAIL_IDSpecified
    {
        get
        {
            return this.rECORD_DETAIL_IDFieldSpecified;
        }
        set
        {
            this.rECORD_DETAIL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string RECORD_DETAIL_REMARK
    {
        get
        {
            return this.rECORD_DETAIL_REMARKField;
        }
        set
        {
            this.rECORD_DETAIL_REMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int RECORD_LIST_ID
    {
        get
        {
            return this.rECORD_LIST_IDField;
        }
        set
        {
            this.rECORD_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool RECORD_LIST_IDSpecified
    {
        get
        {
            return this.rECORD_LIST_IDFieldSpecified;
        }
        set
        {
            this.rECORD_LIST_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_ROLE_WINDOW
{
    
    private string cONTROL_HEADERField;
    
    private string cONTROL_NAMEField;
    
    private int fLAGField;
    
    private bool fLAGFieldSpecified;
    
    private int mENU_IDField;
    
    private bool mENU_IDFieldSpecified;
    
    private int rOLE_IDField;
    
    private bool rOLE_IDFieldSpecified;
    
    private int rOLE_WINDOW_IDField;
    
    private bool rOLE_WINDOW_IDFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string CONTROL_HEADER
    {
        get
        {
            return this.cONTROL_HEADERField;
        }
        set
        {
            this.cONTROL_HEADERField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CONTROL_NAME
    {
        get
        {
            return this.cONTROL_NAMEField;
        }
        set
        {
            this.cONTROL_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=2)]
    public int FLAG
    {
        get
        {
            return this.fLAGField;
        }
        set
        {
            this.fLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool FLAGSpecified
    {
        get
        {
            return this.fLAGFieldSpecified;
        }
        set
        {
            this.fLAGFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int MENU_ID
    {
        get
        {
            return this.mENU_IDField;
        }
        set
        {
            this.mENU_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool MENU_IDSpecified
    {
        get
        {
            return this.mENU_IDFieldSpecified;
        }
        set
        {
            this.mENU_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int ROLE_ID
    {
        get
        {
            return this.rOLE_IDField;
        }
        set
        {
            this.rOLE_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROLE_IDSpecified
    {
        get
        {
            return this.rOLE_IDFieldSpecified;
        }
        set
        {
            this.rOLE_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int ROLE_WINDOW_ID
    {
        get
        {
            return this.rOLE_WINDOW_IDField;
        }
        set
        {
            this.rOLE_WINDOW_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool ROLE_WINDOW_IDSpecified
    {
        get
        {
            return this.rOLE_WINDOW_IDFieldSpecified;
        }
        set
        {
            this.rOLE_WINDOW_IDFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class SYS_TABLE_CONVERTER_LIST
{
    
    private string cOLUMN_NAMEField;
    
    private string cONVERT_COLUMN_NAMEField;
    
    private string iSNULL_FLAGField;
    
    private string rEMARKField;
    
    private int tABLE_CONVERTER_IDField;
    
    private bool tABLE_CONVERTER_IDFieldSpecified;
    
    private int tABLE_CONVERTER_LIST_IDField;
    
    private bool tABLE_CONVERTER_LIST_IDFieldSpecified;
    
    private string tABLE_NAMEField;
    
    private string uNIQUE_FLAGField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public string COLUMN_NAME
    {
        get
        {
            return this.cOLUMN_NAMEField;
        }
        set
        {
            this.cOLUMN_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CONVERT_COLUMN_NAME
    {
        get
        {
            return this.cONVERT_COLUMN_NAMEField;
        }
        set
        {
            this.cONVERT_COLUMN_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string ISNULL_FLAG
    {
        get
        {
            return this.iSNULL_FLAGField;
        }
        set
        {
            this.iSNULL_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string REMARK
    {
        get
        {
            return this.rEMARKField;
        }
        set
        {
            this.rEMARKField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=4)]
    public int TABLE_CONVERTER_ID
    {
        get
        {
            return this.tABLE_CONVERTER_IDField;
        }
        set
        {
            this.tABLE_CONVERTER_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TABLE_CONVERTER_IDSpecified
    {
        get
        {
            return this.tABLE_CONVERTER_IDFieldSpecified;
        }
        set
        {
            this.tABLE_CONVERTER_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int TABLE_CONVERTER_LIST_ID
    {
        get
        {
            return this.tABLE_CONVERTER_LIST_IDField;
        }
        set
        {
            this.tABLE_CONVERTER_LIST_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool TABLE_CONVERTER_LIST_IDSpecified
    {
        get
        {
            return this.tABLE_CONVERTER_LIST_IDFieldSpecified;
        }
        set
        {
            this.tABLE_CONVERTER_LIST_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string TABLE_NAME
    {
        get
        {
            return this.tABLE_NAMEField;
        }
        set
        {
            this.tABLE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string UNIQUE_FLAG
    {
        get
        {
            return this.uNIQUE_FLAGField;
        }
        set
        {
            this.uNIQUE_FLAGField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class WH_CELL
{
    
    private int aREA_IDField;
    
    private bool aREA_IDFieldSpecified;
    
    private string cELL_CODEField;
    
    private string cELL_FLAGField;
    
    private string cELL_FORK_TYPEField;
    
    private string cELL_GROUPField;
    
    private int cELL_HEIGHTField;
    
    private bool cELL_HEIGHTFieldSpecified;
    
    private int cELL_IDField;
    
    private bool cELL_IDFieldSpecified;
    
    private string cELL_INOUTField;
    
    private string cELL_LOGICAL_NAMEField;
    
    private string cELL_MODELField;
    
    private string cELL_NAMEField;
    
    private string cELL_STATUSField;
    
    private string cELL_STORAGE_TYPEField;
    
    private string cELL_TYPEField;
    
    private int cELL_WIDTHField;
    
    private bool cELL_WIDTHFieldSpecified;
    
    private int cELL_XField;
    
    private bool cELL_XFieldSpecified;
    
    private int cELL_YField;
    
    private bool cELL_YFieldSpecified;
    
    private int cELL_ZField;
    
    private bool cELL_ZFieldSpecified;
    
    private string dEVICE_CODEField;
    
    private string lANE_WAYField;
    
    private string lOCK_CELL_IDField;
    
    private int lOGIC_IDField;
    
    private bool lOGIC_IDFieldSpecified;
    
    private string rUN_STATUSField;
    
    private string sHELF_NEIGHBOURField;
    
    private string sHELF_TYPEField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int AREA_ID
    {
        get
        {
            return this.aREA_IDField;
        }
        set
        {
            this.aREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_IDSpecified
    {
        get
        {
            return this.aREA_IDFieldSpecified;
        }
        set
        {
            this.aREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CELL_CODE
    {
        get
        {
            return this.cELL_CODEField;
        }
        set
        {
            this.cELL_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string CELL_FLAG
    {
        get
        {
            return this.cELL_FLAGField;
        }
        set
        {
            this.cELL_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=3)]
    public string CELL_FORK_TYPE
    {
        get
        {
            return this.cELL_FORK_TYPEField;
        }
        set
        {
            this.cELL_FORK_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string CELL_GROUP
    {
        get
        {
            return this.cELL_GROUPField;
        }
        set
        {
            this.cELL_GROUPField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=5)]
    public int CELL_HEIGHT
    {
        get
        {
            return this.cELL_HEIGHTField;
        }
        set
        {
            this.cELL_HEIGHTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_HEIGHTSpecified
    {
        get
        {
            return this.cELL_HEIGHTFieldSpecified;
        }
        set
        {
            this.cELL_HEIGHTFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=6)]
    public int CELL_ID
    {
        get
        {
            return this.cELL_IDField;
        }
        set
        {
            this.cELL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_IDSpecified
    {
        get
        {
            return this.cELL_IDFieldSpecified;
        }
        set
        {
            this.cELL_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string CELL_INOUT
    {
        get
        {
            return this.cELL_INOUTField;
        }
        set
        {
            this.cELL_INOUTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string CELL_LOGICAL_NAME
    {
        get
        {
            return this.cELL_LOGICAL_NAMEField;
        }
        set
        {
            this.cELL_LOGICAL_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=9)]
    public string CELL_MODEL
    {
        get
        {
            return this.cELL_MODELField;
        }
        set
        {
            this.cELL_MODELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string CELL_NAME
    {
        get
        {
            return this.cELL_NAMEField;
        }
        set
        {
            this.cELL_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=11)]
    public string CELL_STATUS
    {
        get
        {
            return this.cELL_STATUSField;
        }
        set
        {
            this.cELL_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string CELL_STORAGE_TYPE
    {
        get
        {
            return this.cELL_STORAGE_TYPEField;
        }
        set
        {
            this.cELL_STORAGE_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string CELL_TYPE
    {
        get
        {
            return this.cELL_TYPEField;
        }
        set
        {
            this.cELL_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public int CELL_WIDTH
    {
        get
        {
            return this.cELL_WIDTHField;
        }
        set
        {
            this.cELL_WIDTHField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_WIDTHSpecified
    {
        get
        {
            return this.cELL_WIDTHFieldSpecified;
        }
        set
        {
            this.cELL_WIDTHFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public int CELL_X
    {
        get
        {
            return this.cELL_XField;
        }
        set
        {
            this.cELL_XField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_XSpecified
    {
        get
        {
            return this.cELL_XFieldSpecified;
        }
        set
        {
            this.cELL_XFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=16)]
    public int CELL_Y
    {
        get
        {
            return this.cELL_YField;
        }
        set
        {
            this.cELL_YField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_YSpecified
    {
        get
        {
            return this.cELL_YFieldSpecified;
        }
        set
        {
            this.cELL_YFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=17)]
    public int CELL_Z
    {
        get
        {
            return this.cELL_ZField;
        }
        set
        {
            this.cELL_ZField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_ZSpecified
    {
        get
        {
            return this.cELL_ZFieldSpecified;
        }
        set
        {
            this.cELL_ZFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=18)]
    public string DEVICE_CODE
    {
        get
        {
            return this.dEVICE_CODEField;
        }
        set
        {
            this.dEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=19)]
    public string LANE_WAY
    {
        get
        {
            return this.lANE_WAYField;
        }
        set
        {
            this.lANE_WAYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=20)]
    public string LOCK_CELL_ID
    {
        get
        {
            return this.lOCK_CELL_IDField;
        }
        set
        {
            this.lOCK_CELL_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=21)]
    public int LOGIC_ID
    {
        get
        {
            return this.lOGIC_IDField;
        }
        set
        {
            this.lOGIC_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOGIC_IDSpecified
    {
        get
        {
            return this.lOGIC_IDFieldSpecified;
        }
        set
        {
            this.lOGIC_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=22)]
    public string RUN_STATUS
    {
        get
        {
            return this.rUN_STATUSField;
        }
        set
        {
            this.rUN_STATUSField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=23)]
    public string SHELF_NEIGHBOUR
    {
        get
        {
            return this.sHELF_NEIGHBOURField;
        }
        set
        {
            this.sHELF_NEIGHBOURField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=24)]
    public string SHELF_TYPE
    {
        get
        {
            return this.sHELF_TYPEField;
        }
        set
        {
            this.sHELF_TYPEField = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class WH_DESCRIPTION
{
    
    private int aREA_IDField;
    
    private bool aREA_IDFieldSpecified;
    
    private string cELL_FORK_COUNTField;
    
    private string cELL_FORK_TYPEField;
    
    private int cELL_HEIGHTField;
    
    private bool cELL_HEIGHTFieldSpecified;
    
    private string cELL_INOUTField;
    
    private string cELL_LOGICAL_NAMEField;
    
    private string cELL_MODELField;
    
    private string cELL_STORAGE_TYPEField;
    
    private string cELL_TYPEField;
    
    private int cELL_WIDTHField;
    
    private bool cELL_WIDTHFieldSpecified;
    
    private string dESCRIPTION_FLAGField;
    
    private int dESCRIPTION_IDField;
    
    private bool dESCRIPTION_IDFieldSpecified;
    
    private string dEVICE_CODEField;
    
    private string dEVICE_NAMEField;
    
    private int eND_XField;
    
    private bool eND_XFieldSpecified;
    
    private int eND_YField;
    
    private bool eND_YFieldSpecified;
    
    private int eND_ZField;
    
    private bool eND_ZFieldSpecified;
    
    private string lANE_WAYField;
    
    private int lOGIC_IDField;
    
    private bool lOGIC_IDFieldSpecified;
    
    private string sHELF_NEIGHBOURField;
    
    private string sHELF_TYPEField;
    
    private int sTART_XField;
    
    private bool sTART_XFieldSpecified;
    
    private int sTART_YField;
    
    private bool sTART_YFieldSpecified;
    
    private int sTART_ZField;
    
    private bool sTART_ZFieldSpecified;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=0)]
    public int AREA_ID
    {
        get
        {
            return this.aREA_IDField;
        }
        set
        {
            this.aREA_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool AREA_IDSpecified
    {
        get
        {
            return this.aREA_IDFieldSpecified;
        }
        set
        {
            this.aREA_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=1)]
    public string CELL_FORK_COUNT
    {
        get
        {
            return this.cELL_FORK_COUNTField;
        }
        set
        {
            this.cELL_FORK_COUNTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=2)]
    public string CELL_FORK_TYPE
    {
        get
        {
            return this.cELL_FORK_TYPEField;
        }
        set
        {
            this.cELL_FORK_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=3)]
    public int CELL_HEIGHT
    {
        get
        {
            return this.cELL_HEIGHTField;
        }
        set
        {
            this.cELL_HEIGHTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_HEIGHTSpecified
    {
        get
        {
            return this.cELL_HEIGHTFieldSpecified;
        }
        set
        {
            this.cELL_HEIGHTFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=4)]
    public string CELL_INOUT
    {
        get
        {
            return this.cELL_INOUTField;
        }
        set
        {
            this.cELL_INOUTField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=5)]
    public string CELL_LOGICAL_NAME
    {
        get
        {
            return this.cELL_LOGICAL_NAMEField;
        }
        set
        {
            this.cELL_LOGICAL_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=6)]
    public string CELL_MODEL
    {
        get
        {
            return this.cELL_MODELField;
        }
        set
        {
            this.cELL_MODELField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=7)]
    public string CELL_STORAGE_TYPE
    {
        get
        {
            return this.cELL_STORAGE_TYPEField;
        }
        set
        {
            this.cELL_STORAGE_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=8)]
    public string CELL_TYPE
    {
        get
        {
            return this.cELL_TYPEField;
        }
        set
        {
            this.cELL_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=9)]
    public int CELL_WIDTH
    {
        get
        {
            return this.cELL_WIDTHField;
        }
        set
        {
            this.cELL_WIDTHField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool CELL_WIDTHSpecified
    {
        get
        {
            return this.cELL_WIDTHFieldSpecified;
        }
        set
        {
            this.cELL_WIDTHFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=10)]
    public string DESCRIPTION_FLAG
    {
        get
        {
            return this.dESCRIPTION_FLAGField;
        }
        set
        {
            this.dESCRIPTION_FLAGField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=11)]
    public int DESCRIPTION_ID
    {
        get
        {
            return this.dESCRIPTION_IDField;
        }
        set
        {
            this.dESCRIPTION_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool DESCRIPTION_IDSpecified
    {
        get
        {
            return this.dESCRIPTION_IDFieldSpecified;
        }
        set
        {
            this.dESCRIPTION_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=12)]
    public string DEVICE_CODE
    {
        get
        {
            return this.dEVICE_CODEField;
        }
        set
        {
            this.dEVICE_CODEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=13)]
    public string DEVICE_NAME
    {
        get
        {
            return this.dEVICE_NAMEField;
        }
        set
        {
            this.dEVICE_NAMEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=14)]
    public int END_X
    {
        get
        {
            return this.eND_XField;
        }
        set
        {
            this.eND_XField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_XSpecified
    {
        get
        {
            return this.eND_XFieldSpecified;
        }
        set
        {
            this.eND_XFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=15)]
    public int END_Y
    {
        get
        {
            return this.eND_YField;
        }
        set
        {
            this.eND_YField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_YSpecified
    {
        get
        {
            return this.eND_YFieldSpecified;
        }
        set
        {
            this.eND_YFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=16)]
    public int END_Z
    {
        get
        {
            return this.eND_ZField;
        }
        set
        {
            this.eND_ZField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool END_ZSpecified
    {
        get
        {
            return this.eND_ZFieldSpecified;
        }
        set
        {
            this.eND_ZFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=17)]
    public string LANE_WAY
    {
        get
        {
            return this.lANE_WAYField;
        }
        set
        {
            this.lANE_WAYField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=18)]
    public int LOGIC_ID
    {
        get
        {
            return this.lOGIC_IDField;
        }
        set
        {
            this.lOGIC_IDField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool LOGIC_IDSpecified
    {
        get
        {
            return this.lOGIC_IDFieldSpecified;
        }
        set
        {
            this.lOGIC_IDFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=19)]
    public string SHELF_NEIGHBOUR
    {
        get
        {
            return this.sHELF_NEIGHBOURField;
        }
        set
        {
            this.sHELF_NEIGHBOURField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=20)]
    public string SHELF_TYPE
    {
        get
        {
            return this.sHELF_TYPEField;
        }
        set
        {
            this.sHELF_TYPEField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=21)]
    public int START_X
    {
        get
        {
            return this.sTART_XField;
        }
        set
        {
            this.sTART_XField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_XSpecified
    {
        get
        {
            return this.sTART_XFieldSpecified;
        }
        set
        {
            this.sTART_XFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=22)]
    public int START_Y
    {
        get
        {
            return this.sTART_YField;
        }
        set
        {
            this.sTART_YField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_YSpecified
    {
        get
        {
            return this.sTART_YFieldSpecified;
        }
        set
        {
            this.sTART_YFieldSpecified = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(Order=23)]
    public int START_Z
    {
        get
        {
            return this.sTART_ZField;
        }
        set
        {
            this.sTART_ZField = value;
        }
    }
    
    /// <remarks/>
    [System.Xml.Serialization.XmlIgnoreAttribute()]
    public bool START_ZSpecified
    {
        get
        {
            return this.sTART_ZFieldSpecified;
        }
        set
        {
            this.sTART_ZFieldSpecified = value;
        }
    }
}

/// <remarks/>
[System.CodeDom.Compiler.GeneratedCodeAttribute("NetCFSvcUtil", "*******")]
[System.SerializableAttribute()]
[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.ComponentModel.DesignerCategoryAttribute("code")]
[System.Xml.Serialization.XmlTypeAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
public partial class ObjectT
{
    
    private object requestObjectField;
    
    /// <remarks/>
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Order=0)]
    public object RequestObject
    {
        get
        {
            return this.requestObjectField;
        }
        set
        {
            this.requestObjectField = value;
        }
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="GetModel", Namespace="http://tempuri.org/")]
public partial class GetModelRequest
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public string statementName;
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=1)]
    public object parameterObject;
    
    public GetModelRequest()
    {
    }
    
    public GetModelRequest(string statementName, object parameterObject)
    {
        this.statementName = statementName;
        this.parameterObject = parameterObject;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="GetModelResponse", Namespace="http://tempuri.org/")]
public partial class GetModelResponse
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public ObjectT GetModelResult;
    
    public GetModelResponse()
    {
    }
    
    public GetModelResponse(ObjectT GetModelResult)
    {
        this.GetModelResult = GetModelResult;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="ManageCreate", Namespace="http://tempuri.org/")]
public partial class ManageCreateRequest
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public string sClassName;
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=1)]
    public string sMethodName;
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=2)]
    public MANAGE_MAIN mMANAGE_MAIN;
    
    [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=3)]
    [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
    public MANAGE_LIST[] lsMANAGE_LIST;
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=4)]
    public bool bTrans;
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=5)]
    public bool bCheckStorage;
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=6)]
    public bool bComplete;
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=7)]
    public bool bAutoSendControl;
    
    public ManageCreateRequest()
    {
    }
    
    public ManageCreateRequest(string sClassName, string sMethodName, MANAGE_MAIN mMANAGE_MAIN, MANAGE_LIST[] lsMANAGE_LIST, bool bTrans, bool bCheckStorage, bool bComplete, bool bAutoSendControl)
    {
        this.sClassName = sClassName;
        this.sMethodName = sMethodName;
        this.mMANAGE_MAIN = mMANAGE_MAIN;
        this.lsMANAGE_LIST = lsMANAGE_LIST;
        this.bTrans = bTrans;
        this.bCheckStorage = bCheckStorage;
        this.bComplete = bComplete;
        this.bAutoSendControl = bAutoSendControl;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="ManageCreateResponse", Namespace="http://tempuri.org/")]
public partial class ManageCreateResponse
{
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=0)]
    public bool ManageCreateResult;
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=1)]
    public string sResult;
    
    public ManageCreateResponse()
    {
    }
    
    public ManageCreateResponse(bool ManageCreateResult, string sResult)
    {
        this.ManageCreateResult = ManageCreateResult;
        this.sResult = sResult;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="GetModelCellCode", Namespace="http://tempuri.org/")]
public partial class GetModelCellCodeRequest
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public string CELL_CODE;
    
    public GetModelCellCodeRequest()
    {
    }
    
    public GetModelCellCodeRequest(string CELL_CODE)
    {
        this.CELL_CODE = CELL_CODE;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="GetModelCellCodeResponse", Namespace="http://tempuri.org/")]
public partial class GetModelCellCodeResponse
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public WH_CELL GetModelCellCodeResult;
    
    public GetModelCellCodeResponse()
    {
    }
    
    public GetModelCellCodeResponse(WH_CELL GetModelCellCodeResult)
    {
        this.GetModelCellCodeResult = GetModelCellCodeResult;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="USER_LOGIN", Namespace="http://tempuri.org/")]
public partial class USER_LOGINRequest
{
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    public string USER_CODE;
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=1)]
    public string USER_PASSWORD;
    
    public USER_LOGINRequest()
    {
    }
    
    public USER_LOGINRequest(string USER_CODE, string USER_PASSWORD)
    {
        this.USER_CODE = USER_CODE;
        this.USER_PASSWORD = USER_PASSWORD;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="USER_LOGINResponse", Namespace="http://tempuri.org/")]
public partial class USER_LOGINResponse
{
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=0)]
    public bool USER_LOGINResult;
    
    [System.Xml.Serialization.XmlElementAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=1)]
    public SYS_USER USER;
    
    public USER_LOGINResponse()
    {
    }
    
    public USER_LOGINResponse(bool USER_LOGINResult, SYS_USER USER)
    {
        this.USER_LOGINResult = USER_LOGINResult;
        this.USER = USER;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="ManageTypeParamGetList", Namespace="http://tempuri.org/")]
public partial class ManageTypeParamGetListRequest
{
    
    [System.Xml.Serialization.XmlElementAttribute(Namespace="http://tempuri.org/", Order=0)]
    public int MANAGE_TYPE_ID;
    
    public ManageTypeParamGetListRequest()
    {
    }
    
    public ManageTypeParamGetListRequest(int MANAGE_TYPE_ID)
    {
        this.MANAGE_TYPE_ID = MANAGE_TYPE_ID;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
[System.Xml.Serialization.XmlRootAttribute(ElementName="ManageTypeParamGetListResponse", Namespace="http://tempuri.org/")]
public partial class ManageTypeParamGetListResponse
{
    
    [System.Xml.Serialization.XmlArrayAttribute(IsNullable=true, Namespace="http://tempuri.org/", Order=0)]
    [System.Xml.Serialization.XmlArrayItemAttribute(Namespace="http://schemas.datacontract.org/2004/07/SiaSun.LMS.Model")]
    public MANAGE_TYPE_PARAM[] ManageTypeParamGetListResult;
    
    public ManageTypeParamGetListResponse()
    {
    }
    
    public ManageTypeParamGetListResponse(MANAGE_TYPE_PARAM[] ManageTypeParamGetListResult)
    {
        this.ManageTypeParamGetListResult = ManageTypeParamGetListResult;
    }
}

[System.Diagnostics.DebuggerStepThroughAttribute()]
[System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
public partial class I_PDAServiceClient : Microsoft.Tools.ServiceModel.CFClientBase<I_PDAService>, I_PDAService
{
    
    public static System.ServiceModel.EndpointAddress EndpointAddress = new System.ServiceModel.EndpointAddress("http://localhost:8001/Service/PDAService");
    
    public I_PDAServiceClient() : 
            this(CreateDefaultBinding(), EndpointAddress)
    {
    }
    
    public I_PDAServiceClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) : 
            base(binding, remoteAddress)
    {
        addProtectionRequirements(binding);
    }
    
    private GetListResponse GetList(GetListRequest request)
    {
        CFInvokeInfo info = new CFInvokeInfo();
        info.Action = "http://tempuri.org/I_PDAService/GetList";
        info.RequestIsWrapped = true;
        info.ReplyAction = "http://tempuri.org/I_PDAService/GetListResponse";
        info.ResponseIsWrapped = true;
        GetListResponse retVal = base.Invoke<GetListRequest, GetListResponse>(info, request);
        return retVal;
    }
    
    public System.Data.DataTable GetList(string strSQL)
    {
        GetListRequest request = new GetListRequest(strSQL);
        GetListResponse response = this.GetList(request);
        return response.GetListResult;
    }
    
    private GetModelResponse GetModel(GetModelRequest request)
    {
        CFInvokeInfo info = new CFInvokeInfo();
        info.Action = "http://tempuri.org/I_PDAService/GetModel";
        info.ExtraTypes = new System.Type[] {
                typeof(GOODS_TYPE),
                typeof(LED_LIST),
                typeof(STORAGE_DETAIL),
                typeof(TECHNICS_MAIN),
                typeof(STORAGE_LIST),
                typeof(GOODS_PROPERTY),
                typeof(ServiceMessage),
                typeof(ServiceResponse),
                typeof(SYS_ROLE),
                typeof(FLOW_ACTION),
                typeof(PLAN_MAIN),
                typeof(SYS_ITEM_LIST),
                typeof(SYS_RELATION),
                typeof(FIELD_DESCRIPTION),
                typeof(FLOW_NODE),
                typeof(FLOW_PARA),
                typeof(IO_CONTROL),
                typeof(MANAGE_ACTION_EXCUTE),
                typeof(IO_CONTROL_ROUTE),
                typeof(PLAN_TYPE),
                typeof(WH_LOGIC),
                typeof(IO_CONTROL_APPLY_HIS),
                typeof(PLAN_DETAIL),
                typeof(RECORD_MAIN),
                typeof(TECHNICS_ROUTE),
                typeof(PLAN_LIST),
                typeof(SYS_RELATION_LIST),
                typeof(ObjectList),
                typeof(SYS_LOG),
                typeof(SYS_MENU),
                typeof(WH_AREA),
                typeof(SYS_TABLE_CONVERTER),
                typeof(GOODS_CLASS),
                typeof(GOODS_TEMPLATE),
                typeof(SYS_TABLE),
                typeof(Log),
                typeof(PLAN_ACTION_EXCUTE),
                typeof(SYS_ITEM),
                typeof(IO_CONTROL_APPLY),
                typeof(LED_MAIN),
                typeof(MANAGE_DETAIL),
                typeof(QueryObject),
                typeof(RECORD_LIST),
                typeof(GOODS_MAIN),
                typeof(GOODS_TEMPLATE_LIST),
                typeof(STORAGE_MAIN),
                typeof(WH_WAREHOUSE),
                typeof(FLOW_TYPE),
                typeof(MANAGE_TYPE),
                typeof(RECORD_DETAIL),
                typeof(SYS_ROLE_WINDOW),
                typeof(SYS_TABLE_CONVERTER_LIST),
                typeof(WH_DESCRIPTION)};
        info.RequestIsWrapped = true;
        info.ReplyAction = "http://tempuri.org/I_PDAService/GetModelResponse";
        info.ResponseIsWrapped = true;
        GetModelResponse retVal = base.Invoke<GetModelRequest, GetModelResponse>(info, request);
        return retVal;
    }
    
    public ObjectT GetModel(string statementName, object parameterObject)
    {
        GetModelRequest request = new GetModelRequest(statementName, parameterObject);
        GetModelResponse response = this.GetModel(request);
        return response.GetModelResult;
    }
    
    private ManageCreateResponse ManageCreate(ManageCreateRequest request)
    {
        CFInvokeInfo info = new CFInvokeInfo();
        info.Action = "http://tempuri.org/I_PDAService/ManageCreate";
        info.ExtraTypes = new System.Type[] {
                typeof(GOODS_TYPE),
                typeof(LED_LIST),
                typeof(STORAGE_DETAIL),
                typeof(TECHNICS_MAIN),
                typeof(STORAGE_LIST),
                typeof(GOODS_PROPERTY),
                typeof(ServiceMessage),
                typeof(ServiceResponse),
                typeof(SYS_ROLE),
                typeof(FLOW_ACTION),
                typeof(PLAN_MAIN),
                typeof(SYS_ITEM_LIST),
                typeof(SYS_RELATION),
                typeof(FIELD_DESCRIPTION),
                typeof(FLOW_NODE),
                typeof(FLOW_PARA),
                typeof(IO_CONTROL),
                typeof(MANAGE_ACTION_EXCUTE),
                typeof(IO_CONTROL_ROUTE),
                typeof(PLAN_TYPE),
                typeof(WH_LOGIC),
                typeof(IO_CONTROL_APPLY_HIS),
                typeof(PLAN_DETAIL),
                typeof(RECORD_MAIN),
                typeof(TECHNICS_ROUTE),
                typeof(PLAN_LIST),
                typeof(SYS_RELATION_LIST),
                typeof(ObjectList),
                typeof(SYS_LOG),
                typeof(SYS_MENU),
                typeof(WH_AREA),
                typeof(SYS_TABLE_CONVERTER),
                typeof(GOODS_CLASS),
                typeof(GOODS_TEMPLATE),
                typeof(SYS_TABLE),
                typeof(Log),
                typeof(PLAN_ACTION_EXCUTE),
                typeof(SYS_ITEM),
                typeof(IO_CONTROL_APPLY),
                typeof(LED_MAIN),
                typeof(MANAGE_DETAIL),
                typeof(QueryObject),
                typeof(RECORD_LIST),
                typeof(GOODS_MAIN),
                typeof(GOODS_TEMPLATE_LIST),
                typeof(STORAGE_MAIN),
                typeof(WH_WAREHOUSE),
                typeof(FLOW_TYPE),
                typeof(MANAGE_TYPE),
                typeof(RECORD_DETAIL),
                typeof(SYS_ROLE_WINDOW),
                typeof(SYS_TABLE_CONVERTER_LIST),
                typeof(WH_DESCRIPTION)};
        info.RequestIsWrapped = true;
        info.ReplyAction = "http://tempuri.org/I_PDAService/ManageCreateResponse";
        info.ResponseIsWrapped = true;
        ManageCreateResponse retVal = base.Invoke<ManageCreateRequest, ManageCreateResponse>(info, request);
        return retVal;
    }
    
    public bool ManageCreate(string sClassName, string sMethodName, MANAGE_MAIN mMANAGE_MAIN, MANAGE_LIST[] lsMANAGE_LIST, bool bTrans, bool bCheckStorage, bool bComplete, bool bAutoSendControl, out string sResult)
    {
        ManageCreateRequest request = new ManageCreateRequest(sClassName, sMethodName, mMANAGE_MAIN, lsMANAGE_LIST, bTrans, bCheckStorage, bComplete, bAutoSendControl);
        ManageCreateResponse response = this.ManageCreate(request);
        sResult = response.sResult;
        return response.ManageCreateResult;
    }
    
    private GetModelCellCodeResponse GetModelCellCode(GetModelCellCodeRequest request)
    {
        CFInvokeInfo info = new CFInvokeInfo();
        info.Action = "http://tempuri.org/I_PDAService/GetModelCellCode";
        info.ExtraTypes = new System.Type[] {
                typeof(GOODS_TYPE),
                typeof(LED_LIST),
                typeof(STORAGE_DETAIL),
                typeof(TECHNICS_MAIN),
                typeof(STORAGE_LIST),
                typeof(GOODS_PROPERTY),
                typeof(ServiceMessage),
                typeof(ServiceResponse),
                typeof(SYS_ROLE),
                typeof(FLOW_ACTION),
                typeof(PLAN_MAIN),
                typeof(SYS_ITEM_LIST),
                typeof(SYS_RELATION),
                typeof(FIELD_DESCRIPTION),
                typeof(FLOW_NODE),
                typeof(FLOW_PARA),
                typeof(IO_CONTROL),
                typeof(MANAGE_ACTION_EXCUTE),
                typeof(IO_CONTROL_ROUTE),
                typeof(PLAN_TYPE),
                typeof(WH_LOGIC),
                typeof(IO_CONTROL_APPLY_HIS),
                typeof(PLAN_DETAIL),
                typeof(RECORD_MAIN),
                typeof(TECHNICS_ROUTE),
                typeof(PLAN_LIST),
                typeof(SYS_RELATION_LIST),
                typeof(ObjectList),
                typeof(SYS_LOG),
                typeof(SYS_MENU),
                typeof(WH_AREA),
                typeof(SYS_TABLE_CONVERTER),
                typeof(GOODS_CLASS),
                typeof(GOODS_TEMPLATE),
                typeof(SYS_TABLE),
                typeof(Log),
                typeof(PLAN_ACTION_EXCUTE),
                typeof(SYS_ITEM),
                typeof(IO_CONTROL_APPLY),
                typeof(LED_MAIN),
                typeof(MANAGE_DETAIL),
                typeof(QueryObject),
                typeof(RECORD_LIST),
                typeof(GOODS_MAIN),
                typeof(GOODS_TEMPLATE_LIST),
                typeof(STORAGE_MAIN),
                typeof(WH_WAREHOUSE),
                typeof(FLOW_TYPE),
                typeof(MANAGE_TYPE),
                typeof(RECORD_DETAIL),
                typeof(SYS_ROLE_WINDOW),
                typeof(SYS_TABLE_CONVERTER_LIST),
                typeof(WH_DESCRIPTION)};
        info.RequestIsWrapped = true;
        info.ReplyAction = "http://tempuri.org/I_PDAService/GetModelCellCodeResponse";
        info.ResponseIsWrapped = true;
        GetModelCellCodeResponse retVal = base.Invoke<GetModelCellCodeRequest, GetModelCellCodeResponse>(info, request);
        return retVal;
    }
    
    public WH_CELL GetModelCellCode(string CELL_CODE)
    {
        GetModelCellCodeRequest request = new GetModelCellCodeRequest(CELL_CODE);
        GetModelCellCodeResponse response = this.GetModelCellCode(request);
        return response.GetModelCellCodeResult;
    }
    
    private USER_LOGINResponse USER_LOGIN(USER_LOGINRequest request)
    {
        CFInvokeInfo info = new CFInvokeInfo();
        info.Action = "http://tempuri.org/I_PDAService/USER_LOGIN";
        info.ExtraTypes = new System.Type[] {
                typeof(GOODS_TYPE),
                typeof(LED_LIST),
                typeof(STORAGE_DETAIL),
                typeof(TECHNICS_MAIN),
                typeof(STORAGE_LIST),
                typeof(GOODS_PROPERTY),
                typeof(ServiceMessage),
                typeof(ServiceResponse),
                typeof(SYS_ROLE),
                typeof(FLOW_ACTION),
                typeof(PLAN_MAIN),
                typeof(SYS_ITEM_LIST),
                typeof(SYS_RELATION),
                typeof(FIELD_DESCRIPTION),
                typeof(FLOW_NODE),
                typeof(FLOW_PARA),
                typeof(IO_CONTROL),
                typeof(MANAGE_ACTION_EXCUTE),
                typeof(IO_CONTROL_ROUTE),
                typeof(PLAN_TYPE),
                typeof(WH_LOGIC),
                typeof(IO_CONTROL_APPLY_HIS),
                typeof(PLAN_DETAIL),
                typeof(RECORD_MAIN),
                typeof(TECHNICS_ROUTE),
                typeof(PLAN_LIST),
                typeof(SYS_RELATION_LIST),
                typeof(ObjectList),
                typeof(SYS_LOG),
                typeof(SYS_MENU),
                typeof(WH_AREA),
                typeof(SYS_TABLE_CONVERTER),
                typeof(GOODS_CLASS),
                typeof(GOODS_TEMPLATE),
                typeof(SYS_TABLE),
                typeof(Log),
                typeof(PLAN_ACTION_EXCUTE),
                typeof(SYS_ITEM),
                typeof(IO_CONTROL_APPLY),
                typeof(LED_MAIN),
                typeof(MANAGE_DETAIL),
                typeof(QueryObject),
                typeof(RECORD_LIST),
                typeof(GOODS_MAIN),
                typeof(GOODS_TEMPLATE_LIST),
                typeof(STORAGE_MAIN),
                typeof(WH_WAREHOUSE),
                typeof(FLOW_TYPE),
                typeof(MANAGE_TYPE),
                typeof(RECORD_DETAIL),
                typeof(SYS_ROLE_WINDOW),
                typeof(SYS_TABLE_CONVERTER_LIST),
                typeof(WH_DESCRIPTION)};
        info.RequestIsWrapped = true;
        info.ReplyAction = "http://tempuri.org/I_PDAService/USER_LOGINResponse";
        info.ResponseIsWrapped = true;
        USER_LOGINResponse retVal = base.Invoke<USER_LOGINRequest, USER_LOGINResponse>(info, request);
        return retVal;
    }
    
    public bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SYS_USER USER)
    {
        USER_LOGINRequest request = new USER_LOGINRequest(USER_CODE, USER_PASSWORD);
        USER_LOGINResponse response = this.USER_LOGIN(request);
        USER = response.USER;
        return response.USER_LOGINResult;
    }
    
    private ManageTypeParamGetListResponse ManageTypeParamGetList(ManageTypeParamGetListRequest request)
    {
        CFInvokeInfo info = new CFInvokeInfo();
        info.Action = "http://tempuri.org/I_PDAService/ManageTypeParamGetList";
        info.ExtraTypes = new System.Type[] {
                typeof(GOODS_TYPE),
                typeof(LED_LIST),
                typeof(STORAGE_DETAIL),
                typeof(TECHNICS_MAIN),
                typeof(STORAGE_LIST),
                typeof(GOODS_PROPERTY),
                typeof(ServiceMessage),
                typeof(ServiceResponse),
                typeof(SYS_ROLE),
                typeof(FLOW_ACTION),
                typeof(PLAN_MAIN),
                typeof(SYS_ITEM_LIST),
                typeof(SYS_RELATION),
                typeof(FIELD_DESCRIPTION),
                typeof(FLOW_NODE),
                typeof(FLOW_PARA),
                typeof(IO_CONTROL),
                typeof(MANAGE_ACTION_EXCUTE),
                typeof(IO_CONTROL_ROUTE),
                typeof(PLAN_TYPE),
                typeof(WH_LOGIC),
                typeof(IO_CONTROL_APPLY_HIS),
                typeof(PLAN_DETAIL),
                typeof(RECORD_MAIN),
                typeof(TECHNICS_ROUTE),
                typeof(PLAN_LIST),
                typeof(SYS_RELATION_LIST),
                typeof(ObjectList),
                typeof(SYS_LOG),
                typeof(SYS_MENU),
                typeof(WH_AREA),
                typeof(SYS_TABLE_CONVERTER),
                typeof(GOODS_CLASS),
                typeof(GOODS_TEMPLATE),
                typeof(SYS_TABLE),
                typeof(Log),
                typeof(PLAN_ACTION_EXCUTE),
                typeof(SYS_ITEM),
                typeof(IO_CONTROL_APPLY),
                typeof(LED_MAIN),
                typeof(MANAGE_DETAIL),
                typeof(QueryObject),
                typeof(RECORD_LIST),
                typeof(GOODS_MAIN),
                typeof(GOODS_TEMPLATE_LIST),
                typeof(STORAGE_MAIN),
                typeof(WH_WAREHOUSE),
                typeof(FLOW_TYPE),
                typeof(MANAGE_TYPE),
                typeof(RECORD_DETAIL),
                typeof(SYS_ROLE_WINDOW),
                typeof(SYS_TABLE_CONVERTER_LIST),
                typeof(WH_DESCRIPTION)};
        info.RequestIsWrapped = true;
        info.ReplyAction = "http://tempuri.org/I_PDAService/ManageTypeParamGetListResponse";
        info.ResponseIsWrapped = true;
        ManageTypeParamGetListResponse retVal = base.Invoke<ManageTypeParamGetListRequest, ManageTypeParamGetListResponse>(info, request);
        return retVal;
    }
    
    public MANAGE_TYPE_PARAM[] ManageTypeParamGetList(int MANAGE_TYPE_ID)
    {
        ManageTypeParamGetListRequest request = new ManageTypeParamGetListRequest(MANAGE_TYPE_ID);
        ManageTypeParamGetListResponse response = this.ManageTypeParamGetList(request);
        return response.ManageTypeParamGetListResult;
    }
    
    public static System.ServiceModel.Channels.Binding CreateDefaultBinding()
    {
        System.ServiceModel.Channels.CustomBinding binding = new System.ServiceModel.Channels.CustomBinding();
        binding.Elements.Add(new System.ServiceModel.Channels.TextMessageEncodingBindingElement(System.ServiceModel.Channels.MessageVersion.Soap11, System.Text.Encoding.UTF8));
        binding.Elements.Add(new System.ServiceModel.Channels.HttpTransportBindingElement());
        return binding;
    }
    
    private void addProtectionRequirements(System.ServiceModel.Channels.Binding binding)
    {
        if ((IsSecureMessageBinding(binding) == false))
        {
            return;
        }
        System.ServiceModel.Security.ChannelProtectionRequirements cpr = new System.ServiceModel.Security.ChannelProtectionRequirements();
        ApplyProtection("http://tempuri.org/I_PDAService/GetList", cpr.IncomingSignatureParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/GetList", cpr.IncomingEncryptionParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/GetModel", cpr.IncomingSignatureParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/GetModel", cpr.IncomingEncryptionParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/ManageCreate", cpr.IncomingSignatureParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/ManageCreate", cpr.IncomingEncryptionParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/GetModelCellCode", cpr.IncomingSignatureParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/GetModelCellCode", cpr.IncomingEncryptionParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/USER_LOGIN", cpr.IncomingSignatureParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/USER_LOGIN", cpr.IncomingEncryptionParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/ManageTypeParamGetList", cpr.IncomingSignatureParts, true);
        ApplyProtection("http://tempuri.org/I_PDAService/ManageTypeParamGetList", cpr.IncomingEncryptionParts, true);
        if ((binding.MessageVersion.Addressing == System.ServiceModel.Channels.AddressingVersion.None))
        {
            ApplyProtection("*", cpr.OutgoingSignatureParts, true);
            ApplyProtection("*", cpr.OutgoingEncryptionParts, true);
        }
        else
        {
            ApplyProtection("http://tempuri.org/I_PDAService/GetListResponse", cpr.OutgoingSignatureParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/GetListResponse", cpr.OutgoingEncryptionParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/GetModelResponse", cpr.OutgoingSignatureParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/GetModelResponse", cpr.OutgoingEncryptionParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/ManageCreateResponse", cpr.OutgoingSignatureParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/ManageCreateResponse", cpr.OutgoingEncryptionParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/GetModelCellCodeResponse", cpr.OutgoingSignatureParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/GetModelCellCodeResponse", cpr.OutgoingEncryptionParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/USER_LOGINResponse", cpr.OutgoingSignatureParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/USER_LOGINResponse", cpr.OutgoingEncryptionParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/ManageTypeParamGetListResponse", cpr.OutgoingSignatureParts, true);
            ApplyProtection("http://tempuri.org/I_PDAService/ManageTypeParamGetListResponse", cpr.OutgoingEncryptionParts, true);
        }
        this.Parameters.Add(cpr);
    }
}
