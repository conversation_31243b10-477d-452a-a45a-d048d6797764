﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace SSLMS.MobileUI.uc
{
    public partial class ucMANAGE_CONFIRM : UserControl
    {
        public ucMANAGE_CONFIRM()
        {
            InitializeComponent();
            this.ucdgvIO_MANAGE.dgv.CurrentCellChanged += new EventHandler(dgv_CurrentCellChanged);
            this.textBox1.Focus();

            this.textBox1.SelectAll();
            this.Init();
        }

        public DataRow drMANAGE
        {
            get
            {
                return this.ucdgvIO_MANAGE.dgv.GetCurDataRow();

            }
        }

        private void Init()
        {
            this.textBox1.Focus();

            this.textBox1.SelectAll();

            this.MANAGE_Bind();

            this.dgv_CurrentCellChanged(null, null);
        }

        void dgv_CurrentCellChanged(object sender, EventArgs e)
        {
              string MANAGE_ID  = "0";

            if (drMANAGE!= null)
            {
                MANAGE_ID = drMANAGE["MANAGE_ID"].ToString();
            }



            this.MANAGE_LIST_Bind(MANAGE_ID);

        }


        private void MANAGE_Bind()
        {
            this.ucdgvIO_MANAGE.sTable = "V_IO_MANAGE";

            this.ucdgvIO_MANAGE.sXml = "V_IO_MANAGE";

            this.ucdgvIO_MANAGE.sWhere = string.Format(" AND MANAGE_STATUS =  '{0}' ", SSLMS.Model.MANAGE_STATUS.WaitConfirm.ToString());

            this.ucdgvIO_MANAGE.Init();
        }

        private void MANAGE_LIST_Bind(string MANAGE_ID)
        {
            this.uclMANAGE_LIST.listXml = "V_IO_MANAGE_LIST";

            this.uclMANAGE_LIST.listTable = "V_IO_MANAGE_LIST";

            this.uclMANAGE_LIST.listWhere = string.Format("AND MANAGE_ID ={0}", MANAGE_ID);

            this.uclMANAGE_LIST.colGroup = "GOODS_TYPE_ID";

            this.uclMANAGE_LIST.colSplit = "GOODS_PROPERTY";

            this.uclMANAGE_LIST.Init();
        }

        private void textBox1_KeyDown(object sender, KeyEventArgs e)
        {
            if (Keys.Enter != e.KeyCode)
            {
                return;
            }


            MANAGE_MAIN mIO_MANAGE = Program._I_ManageService.ManageGetModelByStockBarcode(this.textBox1.Text.TrimEnd());

           string sResult = string.Empty;

           bool bResult = false;

            if (mIO_MANAGE != null && mIO_MANAGE._manage_status == SSLMS.Model.MANAGE_STATUS.WaitConfirm.ToString())
            {
                if (MessageBox.Show("Are you sure to confirm", "system info", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                {
                    bResult =  Program._I_ManageService.ManageFinishConfirm(mIO_MANAGE._manage_id, true, out sResult);

                    if (!bResult)
                        MessageBox.Show("task confirm failed\n" + sResult);
                    else
                        this.Init();
                }
            }

            this.textBox1.Focus();

            this.textBox1.SelectAll();
        }

        private void btnRefresh_Click(object sender, EventArgs e)
        {
            this.Init();
        }

        private void btnConfirm_Click(object sender, EventArgs e)
        {
            MANAGE_MAIN mIO_MANAGE = Program._I_ManageService.ManageGetModelByStockBarcode(this.textBox1.Text.TrimEnd());

            string sResult = string.Empty;

            bool bResult = false;

            if (mIO_MANAGE != null && mIO_MANAGE._manage_status == SSLMS.Model.MANAGE_STATUS.WaitConfirm.ToString())
            {
                if (MessageBox.Show("Are you sure to confirm", "system info", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                {
                    bResult = Program._I_ManageService.ManageFinishConfirm(mIO_MANAGE._manage_id, true, out sResult);

                    if (!bResult)
                        MessageBox.Show("task confirm failed\n" + sResult);
                    else
                        this.Init();
                }
            }

            this.textBox1.Focus();

            this.textBox1.SelectAll();
        }

    }
}
