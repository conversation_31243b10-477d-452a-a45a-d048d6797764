<ResourceDictionary  xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
  mc:Ignorable="d"
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type TreeView}">
        <Setter Property="Background" Value="{DynamicResource ControlBackgroundBrush}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="BorderBrush" Value="{DynamicResource ControlBorderBrush}" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TreeView}">
                    <Grid>
                        <Border x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2" Background="{DynamicResource ControlBackgroundBrush}">
                            <ScrollViewer Focusable="False" Background="{TemplateBinding Background}" Padding="4" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="False">
                                <ItemsPresenter />
                            </ScrollViewer>
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="NuclearTreeViewItemToggleButton" d:IsControlPart="True" TargetType="{x:Type ToggleButton}">
        <Setter Property="Focusable" Value="False" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <ColorAnimation Duration="0" Storyboard.TargetName="UncheckedVisual" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFFFCB66" />
                            <ColorAnimation Duration="0" Storyboard.TargetName="UncheckedVisual" Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)" To="#FFFFCB66" />
                            <ColorAnimation Duration="0" Storyboard.TargetName="CheckedVisual" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFFFCB66" />
                            <ColorAnimation Duration="0" Storyboard.TargetName="CheckedVisual" Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)" To="#FFFFCB66" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <ColorAnimation Duration="0" Storyboard.TargetName="UncheckedVisual" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FF9EC6FB" />
                            <ColorAnimation Duration="0" Storyboard.TargetName="UncheckedVisual" Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)" To="#FF9EC6FB" />
                            <ColorAnimation Duration="0" Storyboard.TargetName="CheckedVisual" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFE56E17" />
                            <ColorAnimation Duration="0" Storyboard.TargetName="CheckedVisual" Storyboard.TargetProperty="(Shape.Stroke).(SolidColorBrush.Color)" To="#FFE56E17" />
                        </Storyboard>
                        <Storyboard x:Key="CheckedOn">
                            <DoubleAnimation Duration="0" Storyboard.TargetName="UncheckedVisual" Storyboard.TargetProperty="Opacity" To="0" />
                            <DoubleAnimation Duration="0" Storyboard.TargetName="CheckedVisual" Storyboard.TargetProperty="Opacity" To="1" />
                        </Storyboard>
                        <Storyboard x:Key="CheckedOff">
                            <DoubleAnimation Duration="0" Storyboard.TargetName="UncheckedVisual" Storyboard.TargetProperty="Opacity" To="1" />
                            <DoubleAnimation Duration="0" Storyboard.TargetName="CheckedVisual" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid HorizontalAlignment="Right" Margin="2,2,5,2">
                        <Path x:Name="UncheckedVisual" Height="9" Width="6" HorizontalAlignment="Right" VerticalAlignment="Center" Fill="#FF9EC6FB" Stroke="#FF9EC6FB" StrokeLineJoin="Miter" Data="M 0,0 L 0,9 L 5,4.5 Z" />
                        <Path x:Name="CheckedVisual" Height="6" Width="6" HorizontalAlignment="Right" VerticalAlignment="Center" Fill="#FFE56E17" Stroke="#FFE56E17" StrokeLineJoin="Miter" Opacity="0" Data="M 6,0 L 6,6 L 0,6 Z" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsChecked" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource CheckedOff}" x:Name="CheckedOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource CheckedOn}" x:Name="CheckedOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style d:IsControlPart="True" TargetType="{x:Type TreeViewItem}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="5,3,15,3" />
        <Setter Property="Cursor" Value="Arrow" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TreeViewItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="SelectedOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="select_gradient" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="SelectedOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="select_gradient" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="hover_gradient" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.85" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.65" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="hover_gradient" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="InactiveOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="inactive" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.5" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="InactiveOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="inactive" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid x:Name="grid">
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition MinWidth="19" Width="Auto" />
                            <ColumnDefinition Width="Auto" />
                            <ColumnDefinition Width="*" />
                        </Grid.ColumnDefinitions>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition />
                        </Grid.RowDefinitions>
                        <ToggleButton x:Name="Expander" Style="{DynamicResource NuclearTreeViewItemToggleButton}" IsChecked="{Binding Path=IsExpanded, RelativeSource={RelativeSource TemplatedParent}}" ClickMode="Press" />

                        <Rectangle x:Name="select_gradient" Grid.Column="1" StrokeThickness="1" RadiusX="1" RadiusY="1" Opacity="0" IsHitTestVisible="False" Fill="{DynamicResource PressedBrush}" Stroke="{DynamicResource PressedBorderBrush}"/>
                        <Rectangle x:Name="inactive" Grid.Column="1" Fill="#FF999999" Stroke="#FF333333" StrokeThickness="1" RadiusX="2" RadiusY="2" Opacity="0" IsHitTestVisible="False" />

                        <Rectangle x:Name="hover_gradient" Stroke="{DynamicResource FocusBrush}" StrokeThickness="1" RadiusX="1" RadiusY="1" Opacity="0" IsHitTestVisible="False" Grid.Column="1" Fill="{DynamicResource MouseOverBrush}"/>
                        <Rectangle x:Name="highlight" Margin="1" StrokeThickness="1" RadiusX="0.5" RadiusY="0.5" Opacity="0" IsHitTestVisible="False" Grid.Column="1" Stroke="{DynamicResource MouseOverHighlightBrush}"/>

                        <Border Grid.Column="1" x:Name="Selection_Border" BorderThickness="{TemplateBinding BorderThickness}" Padding="{TemplateBinding Padding}">
                            <ContentPresenter HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" x:Name="PART_Header" ContentSource="Header" />
                        </Border>
                        <ItemsPresenter Grid.Column="1" Grid.ColumnSpan="2" Grid.Row="1" x:Name="ItemsHost" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="True" SourceName="Selection_Border">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" x:Name="HoverOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsExpanded" Value="false">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ItemsHost" />
                        </Trigger>
                        <Trigger Property="HasItems" Value="false">
                            <Setter Property="Visibility" Value="Hidden" TargetName="Expander" />
                        </Trigger>
                        <Trigger Property="IsSelected" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="SelectedOff_BeginStoryboard" Storyboard="{StaticResource SelectedOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource SelectedOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource InactiveOff}" x:Name="InactiveOff_BeginStoryboard" />
                            </MultiTrigger.ExitActions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource InactiveOn}" x:Name="InactiveOn_BeginStoryboard" />
                            </MultiTrigger.EnterActions>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsSelected" Value="true" />
                                <Condition Property="IsSelectionActive" Value="false" />
                            </MultiTrigger.Conditions>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>