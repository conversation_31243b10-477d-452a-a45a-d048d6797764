﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using System.Windows.Controls;
using System.Windows;
using System.Reflection;

namespace SiaSun.LMS.WPFClient.PICK.View
{
    public class BoxTypeDataTemplateSelector : DataTemplateSelector
    {
        public DataTemplate BoxTypeADataTemplate
        {
            get;set;
        }

        public DataTemplate BoxTypeBDataTemplate
        {
            get; set;
        }

        public DataTemplate BoxTypeCDataTemplate
        {
            get; set;
        }
        public DataTemplate BoxTypeDDataTemplate
        {
            get; set;
        }
        public DataTemplate BoxTypeEDataTemplate
        {
            get; set;
        }

        public DataTemplate BoxTypeARDataTemplate
        {
            get; set;
        }

        public DataTemplate BoxTypeBRDataTemplate
        {
            get; set;
        }

        public DataTemplate BoxTypeCRDataTemplate
        {
            get; set;
        }
        public DataTemplate BoxTypeDRDataTemplate
        {
            get; set;
        }
        public DataTemplate BoxTypeERDataTemplate
        {
            get; set;
        }

        public override DataTemplate SelectTemplate(object item, DependencyObject container)
        {
            DataTemplate resultDataTemplate=new DataTemplate();
            PICK.Model.Box box = (PICK.Model.Box)item;
            Type type = box.GetType();
            PropertyInfo property = type.GetProperty("Type");

             switch ((PICK.Model.Box_Type)property.GetValue(box, null))
            {
                case PICK.Model.Box_Type.A:
                    resultDataTemplate = BoxTypeADataTemplate;
                    break;
                case PICK.Model.Box_Type.B:
                    resultDataTemplate = BoxTypeBDataTemplate;
                    break;
                case PICK.Model.Box_Type.C:
                    resultDataTemplate = BoxTypeCDataTemplate;
                    break;
                case PICK.Model.Box_Type.D:
                    resultDataTemplate = BoxTypeDDataTemplate;
                    break;
                case PICK.Model.Box_Type.E:
                    resultDataTemplate = BoxTypeEDataTemplate;
                    break;

                case PICK.Model.Box_Type.AR:
                    resultDataTemplate = BoxTypeARDataTemplate;
                    break;
                case PICK.Model.Box_Type.BR:
                    resultDataTemplate = BoxTypeBRDataTemplate;
                    break;
                case PICK.Model.Box_Type.CR:
                    resultDataTemplate = BoxTypeCRDataTemplate;
                    break;
                case PICK.Model.Box_Type.DR:
                    resultDataTemplate = BoxTypeDRDataTemplate;
                    break;
                case PICK.Model.Box_Type.ER:
                    resultDataTemplate = BoxTypeERDataTemplate;
                    break;
            }

            return resultDataTemplate;
        }
    }
}
