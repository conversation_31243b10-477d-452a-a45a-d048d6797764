﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.Windows.Forms;
using System.ComponentModel;

namespace SiaSun.LMS.Common
{
    public class CommonUtil
    {
        public static void ClearAllEvents(object objectHasEvents, string eventName)
        {
            if (objectHasEvents == null)
            {
                return;
            }

            try
            {
                EventInfo[] events = objectHasEvents.GetType().GetEvents(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                if (events == null || events.Length < 1)
                {
                    return;
                }

                for (int i = 0; i < events.Length; i++)
                {
                    EventInfo ei = events[i];

                    if (ei.Name == eventName)
                    {
                        FieldInfo fi = ei.DeclaringType.GetField(eventName, BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
                        if (fi != null)
                        {
                            fi.SetValue(objectHasEvents, null);
                        }

                        break;
                    }
                }
            }
            catch
            {
            }
        }


        public void ClearEvent(Control pControl, string pEventName)
        {
            if (pControl == null) return;
            if (string.IsNullOrEmpty(pEventName)) return;

            BindingFlags mPropertyFlags = BindingFlags.Instance | BindingFlags.Public
                | BindingFlags.Static | BindingFlags.NonPublic;//筛选
            BindingFlags mFieldFlags = BindingFlags.Static | BindingFlags.NonPublic;
            Type controlType = typeof(System.Windows.Forms.Control);
            PropertyInfo propertyInfo = controlType.GetProperty("Events", mPropertyFlags);
            EventHandlerList eventHandlerList = (EventHandlerList)propertyInfo.GetValue(pControl, null);//事件列表
            FieldInfo fieldInfo = (typeof(Control)).GetField("Event" + pEventName, mFieldFlags);
            Delegate d = eventHandlerList[fieldInfo.GetValue(pControl)];

            if (d == null) return;
            EventInfo eventInfo = controlType.GetEvent(pEventName);

            foreach (Delegate dx in d.GetInvocationList())
                eventInfo.RemoveEventHandler(pControl, dx);//移除已订阅的pEventName类型事件

        }

    }
}
