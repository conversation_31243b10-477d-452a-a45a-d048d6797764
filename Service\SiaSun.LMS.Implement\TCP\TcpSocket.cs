﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Net.Sockets;
using System.Text;
using System.Threading;
using System.Threading.Tasks;


namespace SiaSun.LMS.Implement.TCP
{
    /// <summary>
    /// Tcp套接字操作类
    /// </summary>
    public class TcpSocket
    {
        protected Socket socket;
        protected string ip;
        protected int port;
        protected bool isServer;
        protected int backlog;
        protected byte[] buffer = new byte[1024 * 256];
        private bool close;

        # region 构造函数
        /// <summary>
        /// 初始化 Kenn.Common.TcpSocket 类的新实例
        /// </summary>
        /// <param name="ip">ip地址</param>
        /// <param name="port">端口</param>
        /// <param name="isServer">是否作为服务器</param>
        /// <param name="backlog">作为服务器时，挂起连接队列的最大长度</param>
        public TcpSocket(string ip, int port, bool isServer = false, int backlog = 10)
        {
            this.ip = ip;
            this.port = port;
            this.isServer = isServer;
            this.backlog = backlog;
        }
        #endregion

        #region 事件
        /// <summary>
        /// 启动成功时发生
        /// </summary>
        public event SocketOpenSuccessEventHandler OpenSuccess
        {
            add { onOpenSuccess += value; }
            remove { onOpenSuccess -= value; }
        }
        protected SocketOpenSuccessEventHandler onOpenSuccess;
        /// <summary>
        /// 启动失败时发生
        /// </summary>
        public event SocketOpenFailEventHandler OpenFail
        {
            add { onOpenFail += value; }
            remove { onOpenFail -= value; }
        }
        protected SocketOpenFailEventHandler onOpenFail;
        /// <summary>
        /// 作为服务端时，客户端接入成功时发生
        /// </summary>
        public event SocketAcceptSuccessEventHandler AcceptSuccess
        {
            add { onAcceptSuccess += value; }
            remove { onAcceptSuccess -= value; }
        }
        protected SocketAcceptSuccessEventHandler onAcceptSuccess;
        /// <summary>
        /// 作为服务端时，客户端接入失败时发生
        /// </summary>
        public event SocketAcceptFailEventHandler AcceptFail
        {
            add { onAcceptFail += value; }
            remove { onAcceptFail -= value; }
        }
        protected SocketAcceptFailEventHandler onAcceptFail;
        /// <summary>
        /// 关闭成功时发生
        /// </summary>
        public event SocketCloseSuccessEventHandler CloseSuccess
        {
            add { onCloseSuccess += value; }
            remove { onCloseSuccess -= value; }
        }
        protected SocketCloseSuccessEventHandler onCloseSuccess;
        /// <summary>
        /// 关闭失败时发生
        /// </summary>
        public event SocketCloseFailEventHandler CloseFail
        {
            add { onCloseFail += value; }
            remove { onCloseFail -= value; }
        }
        protected SocketCloseFailEventHandler onCloseFail;
        /// <summary>
        /// 发送信息成功时发生
        /// </summary>
        public event SocketSendSuccessEventHandler SendSuccess
        {
            add { onSendSuccess += value; }
            remove { onSendSuccess -= value; }
        }
        protected SocketSendSuccessEventHandler onSendSuccess;
        /// <summary>
        /// 发送信息失败时发生
        /// </summary>
        public event SocketSendFailEventHandler SendFail
        {
            add { onSendFail += value; }
            remove { onSendFail -= value; }
        }
        protected SocketSendFailEventHandler onSendFail;
        /// <summary>
        /// 接收信息成功时发生
        /// </summary>
        public event SocketReceiveSuccessEventHandler ReceiveSuccess
        {
            add { onReceiveSuccess += value; }
            remove { onReceiveSuccess -= value; }
        }
        protected SocketReceiveSuccessEventHandler onReceiveSuccess;
        /// <summary>
        /// 接收信息失败时发生
        /// </summary>
        public event SocketReceiveFailEventHandler ReceiveFail
        {
            add { onReceiveFail += value; }
            remove { onReceiveFail -= value; }
        }
        protected SocketReceiveFailEventHandler onReceiveFail;
        /// <summary>
        /// 连接意外断开时发生
        /// </summary>
        public event SocketDisconnectEventHandler Disconnect
        {
            add { onDisconnect += value; }
            remove { onDisconnect -= value; }
        }
        protected SocketDisconnectEventHandler onDisconnect;
        #endregion

        #region 方法
        /// <summary>
        /// 打开连接
        /// </summary>
        public void Open()
        {
            close = false;
            IPEndPoint endPoint = new IPEndPoint(string.IsNullOrEmpty(ip) ? IPAddress.Any : Dns.GetHostAddresses(ip)[0], port);
            socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            try
            {
                if (isServer)
                {
                    socket.Bind(endPoint);
                    socket.Listen(backlog);
                    socket.BeginAccept(new AsyncCallback(AcceptCallBack), null);
                }
                else
                {
                    socket.Connect(endPoint);
                }
                if (onOpenSuccess != null)
                {
                    foreach (SocketOpenSuccessEventHandler item in onOpenSuccess.GetInvocationList())
                    {
                        item.BeginInvoke(null, null);
                    }
                }
            }
            catch (Exception ex)
            {
                if (onOpenFail != null)
                {
                    foreach (SocketOpenFailEventHandler item in onOpenFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        protected virtual void AcceptCallBack(IAsyncResult ar)
        {
            try
            {
                Socket socket = this.socket.EndAccept(ar);
                if (onAcceptSuccess != null)
                {
                    foreach (SocketAcceptSuccessEventHandler item in onAcceptSuccess.GetInvocationList())
                    {
                        item.BeginInvoke(socket, null, null);
                    }
                }
                this.socket.BeginAccept(new AsyncCallback(AcceptCallBack), null);
            }
            catch (Exception ex)
            {
                if (onAcceptFail != null)
                {
                    foreach (SocketAcceptFailEventHandler item in onAcceptFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        /// <summary>
        /// 关闭连接
        /// </summary>
        public void Close()
        {
            close = true;
            try
            {
                socket.Shutdown(SocketShutdown.Both);
                if (isServer)
                {
                    Thread.Sleep(200);
                    socket = null;
                    socket.Dispose();
                }
                else
                {
                    socket.BeginDisconnect(false, CloseCallBack, socket);
                }
            }
            catch (Exception ex)
            {
                if (onCloseFail != null)
                {
                    foreach (SocketCloseFailEventHandler item in onCloseFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        protected virtual void CloseCallBack(IAsyncResult ar)
        {
            try
            {
                Socket socket = ar.AsyncState as Socket;
                socket.EndDisconnect(ar);
                socket = null;
                socket.Dispose();
                if (onCloseSuccess != null)
                {
                    foreach (SocketCloseSuccessEventHandler item in onCloseSuccess.GetInvocationList())
                    {
                        item.BeginInvoke(null, null);
                    }
                }
            }
            catch (Exception ex)
            {
                if (onCloseFail != null)
                {
                    foreach (SocketCloseFailEventHandler item in onCloseFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        /// <summary>
        /// 发送信息
        /// </summary>
        /// <param name="buffer">发送的数据</param>
        /// <param name="async">是否异步发送</param>
        public void Send(byte[] buffer, Socket socket = null)
        {
            if (socket == null)
            {
                socket = this.socket;
            }
            try
            {
                socket.BeginSend(buffer, 0, buffer.Length, 0, new AsyncCallback(SendCallBack), socket);
            }
            catch (Exception ex)
            {
                if (onSendFail != null)
                {
                    foreach (SocketSendFailEventHandler item in onSendFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        protected virtual void SendCallBack(IAsyncResult ar)
        {
            try
            {
                Socket socket = ar.AsyncState as Socket;
                socket.EndSend(ar);
                if (onSendSuccess != null)
                {
                    foreach (SocketSendSuccessEventHandler item in onSendSuccess.GetInvocationList())
                    {
                        item.BeginInvoke(null, null);
                    }
                }
            }
            catch (Exception ex)
            {
                if (onSendFail != null)
                {
                    foreach (SocketSendFailEventHandler item in onSendFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
                if (onDisconnect != null && !close)
                {
                    foreach (SocketDisconnectEventHandler item in onDisconnect.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        /// <summary>
        /// 接收信息
        /// </summary>
        public void Receive(Socket socket = null)
        {
            if (socket == null)
            {
                socket = this.socket;
            }
            try
            {
                socket.BeginReceive(buffer, 0, buffer.Length, 0, new AsyncCallback(ReceiveCallback), socket);
            }
            catch (Exception ex)
            {
                if (onReceiveFail != null)
                {
                    foreach (SocketReceiveFailEventHandler item in onReceiveFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        protected virtual void ReceiveCallback(IAsyncResult ar)
        {
            try
            {
                Socket socket = ar.AsyncState as Socket;
                int length = socket.EndReceive(ar);
                if (length > 0)
                {
                    byte[] data = new byte[length];
                    Array.Copy(buffer, data, length);
                    if (onReceiveSuccess != null)
                    {
                        foreach (SocketReceiveSuccessEventHandler item in onReceiveSuccess.GetInvocationList())
                        {
                            item.BeginInvoke(data, null, null);
                        }
                    }
                    socket.BeginReceive(buffer, 0, buffer.Length, SocketFlags.None, new AsyncCallback(ReceiveCallback), socket);
                }
                else
                {
                    if (onDisconnect != null && !close)
                    {
                        foreach (SocketDisconnectEventHandler item in onDisconnect.GetInvocationList())
                        {
                            item.BeginInvoke(new Exception(), null, null);
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                if (onReceiveFail != null)
                {
                    foreach (SocketReceiveFailEventHandler item in onReceiveFail.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
                if (onDisconnect != null && !close)
                {
                    foreach (SocketDisconnectEventHandler item in onDisconnect.GetInvocationList())
                    {
                        item.BeginInvoke(ex, null, null);
                    }
                }
            }
        }
        #endregion
    }
    public delegate void SocketOpenSuccessEventHandler();
    public delegate void SocketOpenFailEventHandler(Exception ex);
    public delegate void SocketAcceptSuccessEventHandler(Socket socket);
    public delegate void SocketAcceptFailEventHandler(Exception ex);
    public delegate void SocketCloseSuccessEventHandler();
    public delegate void SocketCloseFailEventHandler(Exception ex);
    public delegate void SocketSendSuccessEventHandler();
    public delegate void SocketSendFailEventHandler(Exception ex);
    public delegate void SocketReceiveSuccessEventHandler(byte[] receiveData);
    public delegate void SocketReceiveFailEventHandler(Exception ex);
    public delegate void SocketDisconnectEventHandler(Exception ex);
}
