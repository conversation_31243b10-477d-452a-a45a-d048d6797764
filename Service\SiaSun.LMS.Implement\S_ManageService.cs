﻿using System;
using System.Collections.Generic;
using System.Text;
using System.ServiceModel;
using System.Data;
using System.Collections;
using System.Linq;

using SiaSun.LMS.Model;
using SiaSun.LMS.Common;
using System.Reflection;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, InstanceContextMode = InstanceContextMode.Single, ConcurrencyMode = ConcurrencyMode.Multiple, MaxItemsInObjectGraph = int.MaxValue)]
    public partial class S_ManageService : S_BaseService, SiaSun.LMS.Interface.I_ManageService
    {

        public S_ManageService()
        {
        }

        public bool ManageCreate(string sManageType, string sMethodName, object[] lsObj, out string sResult)
        {
            sResult = string.Empty;
            return false;
        }
        
        public IList<SiaSun.LMS.Model.MANAGE_TYPE_PARAM> ManageTypeParamGetList(int MANAGE_TYPE_ID)
        {
            return this._P_MANAGE_TYPE_PARAM.GetList(MANAGE_TYPE_ID);
        }
        
        /// <summary>任务-调用
        /// 任务-调用
        /// </summary>
        /// <param name="sManageType">任务类型</param>
        /// <param name="sMethod">方法</param>
        /// <param name="lsObj">参数</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool InvokeManageService(string sManageType, string sMethodName, object[] lsObj, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            string sClassFullName = string.Format("SiaSun.LMS.Implement.Manage.{0}", sManageType);

            List<object> oPara = lsObj.ToList<object>();

            oPara.Add(sResult);

            Type t = this.GetType();

            Assembly complierAssembly = t.Assembly;

            object complierInstance = complierAssembly.CreateInstance(sClassFullName);

            Type type = complierInstance.GetType();

            object[] obj = oPara.ToArray();

            object oResult = null;

            //创建反射的所有公用方法
            MethodInfo[] lsMethodInfo = type.GetMethods();

            bool bFind = false;

            foreach (MethodInfo methodInfo in lsMethodInfo)
            {
                if (methodInfo.Name.Equals(sMethodName) && oPara.Count.Equals(methodInfo.GetParameters().Length))
                {
                    oResult = methodInfo.Invoke(complierInstance, obj);

                    bFind = true;

                    break;
                }
            }

            if (!bFind)
            {
                bResult = false;

                sResult = string.Format("未找到{0}类型的{1}方法", sClassFullName, sMethodName);
            }
            else
            {
                bResult = Convert.ToBoolean(oResult);

                sResult = obj[oPara.Count - 1].ToString();
            }

            //bResult = new SiaSun.LMS.Implement.Manage.ManageBase().Invoke(sClassFullName, sMethod, lsObj.ToList<object>(), out sResult);

            return bResult;
        }


        /// <summary>作业-动作
        /// 作业-动作
        /// </summary>
        /// <param name="WAREHOUSE">库房编码</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public void ControlTranslate(string WAREHOUSE)
        {
            string sResult = string.Empty;

            IList<SiaSun.LMS.Model.FLOW_PARA> lsFLOW_PARA = this._S_FlowService.FlowGetParameters("FLOW_MANAGE");

            string[] aFLOW_PARA = new string[lsFLOW_PARA.Count];

            SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE mt = new SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE();

            DataTable dt = this.GetList(string.Format("SELECT * FROM V_FLOW_CONTROL_ACTION ", WAREHOUSE));

            foreach (DataRow dr in dt.Rows)
            {
                int i = 0;

                foreach (SiaSun.LMS.Model.FLOW_PARA mFLOW_PARA in lsFLOW_PARA)
                {
                    aFLOW_PARA[i] = dr[mFLOW_PARA.FLOW_PARA_CODE].ToString();

                    i++;
                }

                mt.MANAGE_ID = Convert.ToInt32(dr["MANAGE_ID"]);

                mt.ACTION_EVENT = dr["FLOW_ACTION_EVENT"].ToString();

                mt.ACTION_EVENT = string.Format(mt.ACTION_EVENT, aFLOW_PARA);

                bool bResult = new SiaSun.LMS.Implement.S_FlowService().ManageEventExecute(mt, out sResult);

                SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModel(Convert.ToInt32(dr["CONTROL_ID"]));

                if (!bResult)
                {
                    if (sResult.Contains("牺牲"))
                    {
                        //this._log.Error(string.Format("处理任务异常: {0}", sResult));
                        this.CreateSysLog(Enum.LogThread.Control, "System", false, string.Format("处理任务异常: {0}", sResult));

                        continue;
                    }

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mt.MANAGE_ID);

                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();

                    mMANAGE_MAIN.MANAGE_REMARK = sResult + string.Format("【控制任务状态-{0}】", mIO_CONTROL.CONTROL_STATUS.ToString());

                    this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                }

                if (bResult)
                {
                    if (null != mIO_CONTROL)
                    {
                        mIO_CONTROL.PRE_CONTROL_STATUS = Convert.ToString(dr["CONTROL_STATUS"]);

                        this._P_IO_CONTROL.Update(mIO_CONTROL);
                    }
                }


            }
        }

        /// <summary>
        /// 处理控制任务更新状态 wdz add 2018-11-26
        /// </summary>
        public void ControlTranslate(string wareHouse, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            IList<Model.IO_CONTROL> lsIO_CONTROL = this._P_IO_CONTROL.GetListChanged();
            //List<Model.IO_CONTROL> lsControlFilter = lsIO_CONTROL.Where(r => r.CONTROL_STATUS.ToString() != r.PRE_CONTROL_STATUS && r.CONTROL_STATUS != 0 && r.CONTROL_STATUS != 7).ToList();

            foreach (Model.IO_CONTROL item in lsIO_CONTROL)
            {
                //清除非关联任务
                if (item.MANAGE_ID == 0 && (item.CONTROL_STATUS == 999 || item.CONTROL_STATUS == 990 || item.CONTROL_STATUS == 900))
                {
                    this.CreateSysLog(Enum.LogThread.Control, "ControlJob", Enum.LOG_LEVEL.Information, string.Format("S_ManageService:ControlTranslate():删除非关联管理任务的Control任务_ControlID[{0}]_条码[{1}]_起点[{2}]_终点[{3}]_状态[{4}]", item.CONTROL_ID, item.STOCK_BARCODE, item.START_DEVICE_CODE, item.END_DEVICE_CODE, item.CONTROL_STATUS));
                    this._P_IO_CONTROL.Delete(item.CONTROL_ID);
                    continue;
                }

                if (item.MANAGE_ID > 0)
                {
                    //处理关联管理任务的控制任务
                    switch (item.CONTROL_STATUS)
                    {
                        case 3:
                        case 980:
                            bResult = this.Invoke("ManageBase", "ManageError", new object[] { item.MANAGE_ID }, out sResult);
                            break;

                        case 10:
                        case 11:
                            bResult = this.Invoke("ManageBase", "ManageExecute", new object[] { item.MANAGE_ID }, out sResult);
                            break;

                        case 900:
                            bResult = this.Invoke("ManageBase", "ManageCancel", new object[] { item.MANAGE_ID }, out sResult);
                            break;

                        //     2020/09/21 @mxh    
                        //     对于3楼至5楼的出库任务，为提高效率，调度将在堆垛机取货完成并放货至站台结束后，上报状态码12
                        //     管理对于中间状态12，做出提前将出库货位置为空闲的操作
                        //case 12:
                        //    string CELL_CODE = item.START_DEVICE_CODE;
                        //    //3楼线边库出库转换货位编码
                        //    if (item.CELL_GROUP.Equals("26"))
                        //    {
                        //        CELL_CODE = string.Format("{0}" + CELL_CODE.Substring(1, CELL_CODE.Length - 1), "5");
                        //    }
                        //    int AREA_ID = 4;    //3期立体库区 
                        //    WH_CELL mWH_CELL = _P_WH_CELL.GetModel(AREA_ID, CELL_CODE);
                        //    if (null == mWH_CELL)
                        //    {
                        //        bResult = false;
                        //        sResult = string.Format("控制任务{0}3楼线边仓提前报完成定位货位失败", item.CONTROL_ID);
                        //    }
                        //    bResult = this._S_CellService.CellUpdateStatus(mWH_CELL.CELL_ID, Enum.CELL_STATUS.Nohave.ToString(), Enum.RUN_STATUS.Enable.ToString(), out sResult);
                        //    break;

                        case 970:
                            bResult = this.Invoke("ManageBase", "ManageReDownloadException", new object[] { item.MANAGE_ID }, out sResult);
                            break;

                        case 990:
                            bResult = this.Invoke("ManageBase", "ManageException", new object[] { item.MANAGE_ID }, out sResult);
                            break;

                        case 999:
                            Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(item.MANAGE_ID);
                            if (mMANAGE_MAIN != null)
                            {
                                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                                if (mMANAGE_TYPE != null)
                                {
                                    bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageComplete", new object[] { item.MANAGE_ID, true }, out sResult);
                                }
                                else
                                {
                                    bResult = false;
                                    sResult = string.Format("未找到任务类型_任务ID[{0}]_条码[{1}]_任务类型编码[{2}]", item.MANAGE_ID, item.STOCK_BARCODE, mMANAGE_MAIN.MANAGE_TYPE_CODE);
                                }
                            }
                            else
                            {
                                bResult = false;
                                sResult = string.Format("未找到任务信息_任务ID[{0}]_条码[{1}]", item.MANAGE_ID, item.STOCK_BARCODE);
                            }
                            break;

                        default:
                            break;
                    }

                    //调度状态变化 打日志
                    if (!bResult || (item.CONTROL_STATUS != 10 && item.CONTROL_STATUS != 11))
                    {
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("S_ManageService.ControlTranslate():根据Control状态[{0}]处理Manage任务[{1}]_条码[{4}]_ControlId[{2}]_ManageId[{3}]", item.CONTROL_STATUS, bResult ? "成功" : "失败 " + sResult, item.CONTROL_ID, item.MANAGE_ID, item.STOCK_BARCODE));
                    }

                    if (!bResult)
                    {
                        //将错误信息更新给MANAGE
                        Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(item.MANAGE_ID);
                        if (mMANAGE_MAIN != null)
                        {
                            mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();
                            mMANAGE_MAIN.MANAGE_REMARK = sResult + string.Format("_控制任务状态[{0}]", item.CONTROL_STATUS);

                            this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                        }
                    }
                }

                //处理过的CONTROL任务更新 PRE_CONTROL_STATUS
                //item.PRE_CONTROL_STATUS = Convert.ToString(item.CONTROL_STATUS);
                //this._P_IO_CONTROL.Update(item);
                //wdz alter 2019-07-17 防止处理期间调度更新CONTROL_STATUS
                this._P_Base_House.ExecuteNonQuery(string.Format("update IO_CONTROL set PRE_CONTROL_STATUS = {0} where CONTROL_ID ={1}", item.CONTROL_STATUS, item.CONTROL_ID));
            }
        }


        /// <summary>
        /// 作业-动作
        /// </summary>
        public void ControlTranslate(int conCurrentCount, out string sResult)
        {
            sResult = string.Empty;
            DataTable dtEventHandle = new DataTable("EventExecute");

            IList<Model.FLOW_PARA> lsFLOW_PARA = this._S_FlowService.FlowGetParameters("FLOW_CONTROL");

            string[] aFLOW_PARA = new string[lsFLOW_PARA.Count];

            Model.MANAGE_ACTION_EXCUTE mt = new Model.MANAGE_ACTION_EXCUTE();

            DataTable dt = this.GetList(string.Format("SELECT  * FROM V_FLOW_CONTROL_ACTION  order by manage_id "));

            if (!dtEventHandle.Columns.Contains("execute"))
            {
                dtEventHandle.Columns.Add("execute");
                dtEventHandle.Columns["execute"].DefaultValue = 0;
            }

            dtEventHandle.Merge(dt);

            if (dtEventHandle.Columns.Contains("MANAGE_ID"))
                dtEventHandle.PrimaryKey = new DataColumn[] { dtEventHandle.Columns["MANAGE_ID"] };

            DataRow[] drThreadExecute = dtEventHandle.Select("execute = 0 or execute is null");

            DataTable dtThreadExecute = dt.Clone();

            for (int i = 0; i < (drThreadExecute.Length > conCurrentCount ? conCurrentCount : drThreadExecute.Length); i++)
            {
                drThreadExecute[i]["execute"] = 1;
                dtThreadExecute.ImportRow(drThreadExecute[i]);
            }

            foreach (DataRow dr in dtThreadExecute.Rows)
            {
                int i = 0;

                foreach (SiaSun.LMS.Model.FLOW_PARA mFLOW_PARA in lsFLOW_PARA)
                {
                    aFLOW_PARA[i] = dr[mFLOW_PARA.FLOW_PARA_CODE].ToString();

                    i++;
                }

                mt.MANAGE_ID = Convert.ToInt32(dr["MANAGE_ID"]);
                mt.ACTION_EVENT = dr["FLOW_ACTION_EVENT"].ToString();
                mt.ACTION_EVENT = string.Format(mt.ACTION_EVENT, aFLOW_PARA);

                bool bResult = this._S_FlowService.ManageEventExecute(mt, out sResult);

                //wdz add 2018-03-21 log
                if (!new string[] { "10", "11" }.Contains(dr["CONTROL_STATUS"].ToString()))
                {
                    this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("S_ManageService.ControlTranslate():根据Control状态[{0}]处理Manage任务[{1}]_条码[{4}]_ControlId[{2}]_ManageId[{3}]", dr["CONTROL_STATUS"].ToString(), bResult ? "成功" : "失败 " + sResult, dr["CONTROL_ID"].ToString(), dr["MANAGE_ID"].ToString(), dr["STOCK_BARCODE"].ToString()));
                }

                Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModel(Convert.ToInt32(dr["CONTROL_ID"]));

                if (!bResult)
                {
                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mt.MANAGE_ID);

                    if (mMANAGE_MAIN != null)
                    {
                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();
                        mMANAGE_MAIN.MANAGE_REMARK = sResult + string.Format("【控制任务状态-{0}】", mIO_CONTROL.CONTROL_STATUS.ToString());

                        this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                    }
                    else
                    {
                        //this._log.Fatal(string.Format("执行事件失败{0} {1}", mt.MANAGE_ID, mt.ACTION_EVENT));
                        this.CreateSysLog(Enum.LogThread.Control, "System", false, string.Format("执行事件失败{0} {1}", mt.MANAGE_ID, mt.ACTION_EVENT));
                    }
                }

                if (bResult && null != mIO_CONTROL)
                {
                    mIO_CONTROL.PRE_CONTROL_STATUS = Convert.ToString(dr["CONTROL_STATUS"]);

                    this._P_IO_CONTROL.Update(mIO_CONTROL);
                }

                //wdz add 2018-01-24  对于完成的任务无论处理成功失败均更新PRE_CONTROL_STATUS状态，防止999但报错的任务多次触发执行
                if (mIO_CONTROL !=null && mIO_CONTROL.CONTROL_STATUS == int.Parse(Enum.CONTROL_STATUS.Finish.ToString("d")))
                {
                    mIO_CONTROL.PRE_CONTROL_STATUS = Convert.ToString(dr["CONTROL_STATUS"]);
                    this._P_IO_CONTROL.Update(mIO_CONTROL);
                }

                DataRow drFind = dtEventHandle.Rows.Find(Convert.ToInt32(dr["MANAGE_ID"]));

                int index = dtEventHandle.Rows.IndexOf(drFind);

                if (index != -1)
                    dtEventHandle.Rows.RemoveAt(index);
            }
        }

        /// <summary>
        /// 自动将符合条件的齐套箱下架到指定位置
        /// 2020-09-24 17:25:04
        /// </summary>
        /// <returns></returns>
        public void AutoMoveKitbox()
        {
            try
            {
                //获取配置项，考虑几个月之内的计划-提前几个小时下架齐套箱
                var autoMoveKitThConfig = this._S_SystemService.GetSysParameter("AutoMoveKitThreshold", "1-24");
                if (autoMoveKitThConfig == "Diasble")
                {
                    return;
                }
                var configSplit = autoMoveKitThConfig.Split('-');
                int createTh = 0, moveTh = 0;
                if (configSplit.Count() != 2 || !int.TryParse(configSplit[0], out createTh) || !int.TryParse(configSplit[1], out moveTh))
                {
                    this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox错误_配置项AutoMoveKitThreshold[{autoMoveKitThConfig}]有误");
                    return;
                }

                //获取配置项，三楼的目标终点站台
                string endStationCode;
                if (!this._S_SystemService.GetSysParameter("MoveToFloor3EndStation", out endStationCode))
                {
                    this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox错误_配置项MoveToFloor3EndStation[{autoMoveKitThConfig}]有误");
                    return;
                }
                var endCell = this._P_WH_CELL.GetModel(endStationCode);
                if (endCell == null)
                {
                    this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox错误_未找到编码为[{endStationCode}]的货位信息");
                    return;
                }

                //查询出未处理、处理失败的计划，查询个数根据AutoMoveKitPlanCount配置项来，未处理的排在前面
                var strQuery = $@"select * from (
                                    select * from PLAN_MAIN t 
                                        where PLAN_FROM_USER in ('0','2')
                                            and PLAN_TYPE_CODE='{Enum.PLAN_TYPE_CODE.PlanPick.ToString()}' 
                                            and PLAN_STATUS='{Enum.PLAN_STATUS.Finish.ToString()}' and PLAN_TO_DEPT='3' 
                                            and PLAN_CREATE_TIME is not null and PLAN_FROM_DEPT is not null 
                                            and PLAN_CREATE_TIME > '{DateTime.Now.AddMonths(-createTh).ToString("yyyy-MM-dd HH:mm:ss")}'
                                            and PLAN_FROM_DEPT < '{DateTime.Now.AddHours(moveTh).ToString("yyyy-MM-dd HH:mm:ss")}'
                                        order by PLAN_FROM_USER, PLAN_ID)
                                  where rownum <= {this._S_SystemService.GetSysParameter("AutoMoveKitPlanCount", "1")} ";

                var planToMove = this.GetList(strQuery);
                if (planToMove == null || planToMove.Rows.Count < 1)
                {
                    return;
                }

                //遍历每个计划
                foreach (DataRow planItem in planToMove.Rows)
                {
                    bool result = true;

                    try
                    {
                        //事务支持
                        this._P_Base_House.BeginTransaction();

                        //查询出本次循环计划下的所有齐套箱
                        strQuery = $@"select * from STORAGE_MAIN left join WH_CELL on STORAGE_MAIN.CELL_ID = WH_CELL.CELL_ID where 
                                    STORAGE_ID in (select STORAGE_ID from STORAGE_LIST where 
                                                    PLAN_LIST_ID in (select PLAN_LIST_ID from PLAN_LIST 
                                                                        where PLAN_ID ={planItem["PLAN_ID"].ToString()}))";

                        var storageToMove = this.GetList(strQuery);
                        if (storageToMove == null || storageToMove.Rows.Count < 1)
                        {
                            continue;
                        }

                        //遍历本计划下的每个齐套箱
                        foreach (DataRow storageItem in storageToMove.Rows)
                        {
                            var manageMain = this._P_MANAGE_MAIN.GetModelStockBarcode(storageItem["STOCK_BARCODE"].ToString());
                            //当齐套箱有任务时
                            if (manageMain != null )
                            {
                                //如果是下架到3楼的任务，则继续查看下一个齐套箱；
                                //如果有其他任务则记错误日志，跳出库存遍历循环，本计划的所有齐套箱下架任务回滚
                                if(manageMain.MANAGE_TYPE_CODE !=Enum.MANAGE_TYPE.ManageDown.ToString()&&
                                    manageMain.END_CELL_ID==endCell.CELL_ID)
                                {
                                    continue; 
                                }
                                else
                                {
                                    this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox失败_齐套箱[{storageItem["STOCK_BARCODE"].ToString()}]下架时存在任务_计划单号[{planItem["PLAN_CODE"]}]");
                                    result = false;
                                    break;
                                }
                            }

                            //判断库存是否在立库区
                            if (!new string[]{ "1","3"}.Contains(storageItem["AREA_ID"].ToString()))
                            {
                                this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox失败_齐套箱[{storageItem["STOCK_BARCODE"].ToString()}]不在立库区_无法下架_计划单号[{planItem["PLAN_CODE"]}]");
                                result = false;
                                break;
                            }

                            //获取库存列表
                            var storageLists = this._P_STORAGE_LIST.GetListStorageID(int.Parse(storageItem["STORAGE_ID"].ToString()));
                            if (storageLists == null || storageLists.Count < 1)
                            {
                                this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox失败_齐套箱[{storageItem["STOCK_BARCODE"].ToString()}]库存列表未找到_无法下架_计划单号[{planItem["PLAN_CODE"]}]");
                                result = false;
                                break;
                            }

                            manageMain = new MANAGE_MAIN()
                            {
                                CROSS_FLAG = "",
                                FULL_FLAG = "",
                                GOODS_TEMPLATE_ID = 0,
                                MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                                MANAGE_CONFIRM_TIME = "",
                                MANAGE_END_TIME = "",
                                MANAGE_ID = 0,
                                MANAGE_LEVEL = this._S_SystemService.GetSysParameter("DefaultTaskLevel", "0"),
                                MANAGE_OPERATOR = "齐套箱移动",
                                MANAGE_RELATE_CODE = "",
                                MANAGE_REMARK = "",
                                MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString(),
                                MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString(),
                                MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageDown.ToString(),
                                PLAN_ID = 0,
                                PLAN_TYPE_CODE = "",
                                START_CELL_ID = int.Parse(storageItem["CELL_ID"].ToString()),
                                STOCK_BARCODE = storageItem["STOCK_BARCODE"].ToString(),
                                CELL_MODEL = storageItem["CELL_MODEL"].ToString(),
                                END_CELL_ID = endCell.CELL_ID
                            };

                            var manageLists = new List<MANAGE_LIST>();

                            foreach(var storageList in storageLists)
                            {
                                manageLists.Add(new MANAGE_LIST()
                                {
                                    BOX_BARCODE = storageList.BOX_BARCODE,
                                    DETAIL_FLAG = "",
                                    MANAGE_ID = 0,
                                    MANAGE_LIST_ID = 0,
                                    MANAGE_LIST_QUANTITY = storageList.STORAGE_LIST_QUANTITY,
                                    MANAGE_LIST_REMARK = "",
                                    PLAN_LIST_ID = 0,
                                    STORAGE_LIST_ID = storageList.STORAGE_LIST_ID,
                                    STORAGE_LOCK_ID = 0,
                                    GOODS_ID = storageList.GOODS_ID,
                                    GOODS_PROPERTY1 = storageList.GOODS_PROPERTY1,
                                    GOODS_PROPERTY2 = storageList.GOODS_PROPERTY2,
                                    GOODS_PROPERTY3 = storageList.GOODS_PROPERTY3,
                                    GOODS_PROPERTY4 = storageList.GOODS_PROPERTY4,
                                    GOODS_PROPERTY5 = storageList.GOODS_PROPERTY5,
                                    GOODS_PROPERTY6 = storageList.GOODS_PROPERTY6,
                                    GOODS_PROPERTY7 = storageList.GOODS_PROPERTY7,
                                    GOODS_PROPERTY8 = storageList.GOODS_PROPERTY8,

                                });
                            }
                            string message;
                            result = new ManageDown().ManageCreate(manageMain, manageLists, false, false, true, out message);
                            if(!result)
                            {
                                this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox失败_齐套箱[{storageItem["STOCK_BARCODE"].ToString()}]下架任务生成失败_计划单号[{planItem["PLAN_CODE"]}]_信息[{message}]");
                                result = false;
                                break;
                            }
                        }
                    }
                    catch (Exception ex)
                    {
                        result = false;
                        throw;  //throw前会执行finally
                    }
                    finally
                    {
                        if (result)
                        {
                            this._P_Base_House.CommitTransaction();
                            this.ExecuteNonQuery_ReturnInt($"update PLAN_MAIN set PLAN_FROM_USER = '1' where PLAN_ID = {planItem["PLAN_ID"]}");
                        }
                        else
                        {
                            this._P_Base_House.RollBackTransaction();
                            this.ExecuteNonQuery_ReturnInt($"update PLAN_MAIN set PLAN_FROM_USER = '2' where PLAN_ID = {planItem["PLAN_ID"]}");
                        }
                    }
                }                
            }
            catch (Exception ex)
            {
                this.CreateSysLog(Enum.LogThread.Task, "System", false, $"AutoMoveKitbox异常_信息[{ex.Message}]");
                throw;
            }
        }


        /// <summary>
        /// 增加申请
        /// xcjt add 2017-01-13
        /// </summary>
        public bool ControlApplyAdd(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                this._P_IO_CONTROL_APPLY.Add(mIO_CONTROL_APPLY);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("写入IO_CONTROL_APPLY异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }
            return bResult;
        }

        /// <summary>
        /// 处理申请方法
        /// xcjt 2016-12-28
        /// </summary>
        public bool ControlApplyTask(out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            string mesout = string.Empty;

            IList<SiaSun.LMS.Model.IO_CONTROL_APPLY> lsIO_CONTROL_APPLY = this._P_IO_CONTROL_APPLY.GetList(0);

            foreach (SiaSun.LMS.Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY in lsIO_CONTROL_APPLY)
            {
                Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)this.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE).RequestObject;
                if (mAPPLY_TYPE != null)
                {
                    bResult = this.Invoke(mAPPLY_TYPE.APPLY_TYPE_CLASS, "ApplyHandle", new object[] { mIO_CONTROL_APPLY }, out sResult);
                }
            }
            return bResult;
        }

        public bool RecordCreate(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            bool updateFlag = true;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("任务号{0}不存在", MANAGE_ID);
                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

            SiaSun.LMS.Model.RECORD_MAIN mRECORD_MAIN = new Model.RECORD_MAIN();
            SiaSun.LMS.Model.RECORD_LIST mRECORD_LIST = null;
            SiaSun.LMS.Model.RECORD_DETAIL mRECORD_DETAIL = null;

            SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST = null;
            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);

            SiaSun.LMS.Model.WH_CELL mSTART_WH_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
            SiaSun.LMS.Model.WH_CELL mEND_WH_CELL = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

            try
            {
                if (mPLAN_MAIN != null)
                {
                    mRECORD_MAIN.PLAN_ID = mPLAN_MAIN.PLAN_ID;
                    mRECORD_MAIN.PLAN_CODE = mPLAN_MAIN.PLAN_CODE;
                    mRECORD_MAIN.PLAN_TYPE_CODE = mPLAN_MAIN.PLAN_TYPE_CODE;
                }
                mRECORD_MAIN.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                mRECORD_MAIN.MANAGE_TYPE_CODE = mMANAGE_MAIN.MANAGE_TYPE_CODE;
                mRECORD_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;

                if (mSTART_WH_CELL != null)
                    mRECORD_MAIN.START_POSITION = mSTART_WH_CELL.CELL_CODE;

                if (mEND_WH_CELL != null)
                    mRECORD_MAIN.END_POSITION = mEND_WH_CELL.CELL_CODE;

                mRECORD_MAIN.RECORD_OPERATOR = mMANAGE_MAIN.MANAGE_OPERATOR;
                mRECORD_MAIN.MANAGE_BEGIN_TIME = mMANAGE_MAIN.MANAGE_BEGIN_TIME;
                mRECORD_MAIN.MANAGE_END_TIME = mMANAGE_MAIN.MANAGE_END_TIME;
                mRECORD_MAIN.RECORD_REMARK = mMANAGE_MAIN.MANAGE_REMARK;
                mRECORD_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                mRECORD_MAIN.MANAGE_SOURCE = mMANAGE_MAIN.MANAGE_SOURCE;
                mRECORD_MAIN.MANAGE_RELATE_CODE = mMANAGE_MAIN.MANAGE_RELATE_CODE;
                // 2019-05-21 12:05:8 by ywz 
                mRECORD_MAIN.MANAGE_CONFIRM_TIME = mMANAGE_MAIN.MANAGE_CONFIRM_TIME;

                this._P_RECORD_MAIN.Add(mRECORD_MAIN);

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mRECORD_LIST = new Model.RECORD_LIST();

                    mRECORD_LIST.RECORD_ID = mRECORD_MAIN.RECORD_ID;
                    mRECORD_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;
                    mRECORD_LIST.RECORD_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                    mRECORD_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;
                    mRECORD_LIST.RECORD_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                    bResult = this._S_GoodsService.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mRECORD_LIST, mMANAGE_LIST, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    mRECORD_LIST.GOODS_PROPERTY1 = mMANAGE_LIST.GOODS_PROPERTY1;
                    mRECORD_LIST.GOODS_PROPERTY2 = mMANAGE_LIST.GOODS_PROPERTY2;
                    mRECORD_LIST.GOODS_PROPERTY3 = mMANAGE_LIST.GOODS_PROPERTY3;
                    mRECORD_LIST.GOODS_PROPERTY4 = mMANAGE_LIST.GOODS_PROPERTY4;
                    mRECORD_LIST.GOODS_PROPERTY5 = mMANAGE_LIST.GOODS_PROPERTY5;
                    mRECORD_LIST.GOODS_PROPERTY6 = mMANAGE_LIST.GOODS_PROPERTY6;
                    mRECORD_LIST.GOODS_PROPERTY7 = mMANAGE_LIST.GOODS_PROPERTY7;
                    mRECORD_LIST.GOODS_PROPERTY8 = mMANAGE_LIST.GOODS_PROPERTY8;

                    mRECORD_LIST.BOX_BARCODE = mMANAGE_LIST.BOX_BARCODE;

                    //是否为空 是否有明细

                    Model.PLAN_MAIN mPLAN_MAIN_CHECKCOMPLETE = null;

                    string sPlanComplete = string.Empty;

                    if (mMANAGE_TYPE != null && mMANAGE_TYPE.MANAGE_TYPE_GROUP != "3")
                    {
                        mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mPLAN_LIST != null)
                        {
                            //wdz alter 2017-12-06
                            if (mPLAN_MAIN != null && !string.IsNullOrEmpty(mPLAN_MAIN.PLAN_TYPE_CODE) && (
                                mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString() ||
                                //mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString() ||
                                mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString()))
                            {
                                //每箱只处理一次
                                if (updateFlag)
                                {
                                    mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY++;
                                    updateFlag = false;
                                }
                            }
                            else if (mPLAN_MAIN != null && !string.IsNullOrEmpty(mPLAN_MAIN.PLAN_TYPE_CODE) && 
                                mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanCheck.ToString())
                            {
                                //wdz add 2018-09-27
                                //盘点计划 每个计划单 处理一次
                                mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY++;
                            }
                            else 
                            {
                                if (mMANAGE_LIST.GOODS_ID == mPLAN_LIST.GOODS_ID)
                                {
                                    mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                                }
                            }

                            this._P_PLAN_LIST.Update(mPLAN_LIST);

                            mPLAN_MAIN_CHECKCOMPLETE = _P_PLAN_MAIN.GetModel(mPLAN_LIST.PLAN_ID);

                            if (mPLAN_MAIN_CHECKCOMPLETE != null)
                            {
                                mRECORD_LIST.RECORD_LIST_REMARK = mPLAN_MAIN_CHECKCOMPLETE.PLAN_RELATIVE_ID.ToString();

                                SiaSun.LMS.Model.PLAN_TYPE mPLAN_TYPE = this._P_PLAN_TYPE.GetModelPlanTypeCode(mPLAN_MAIN_CHECKCOMPLETE.PLAN_TYPE_CODE);

                                if (!this.Invoke(mPLAN_TYPE.PLAN_TYPE_CLASS, "PlanCheckComplete", new object[] { mPLAN_MAIN_CHECKCOMPLETE.PLAN_ID }, out sPlanComplete))
                                {
                                    bResult = this.Invoke(mPLAN_TYPE.PLAN_TYPE_CLASS, "PlanComplete", new object[] { mPLAN_MAIN_CHECKCOMPLETE.PLAN_ID, false }, out sPlanComplete);
                                    if (!bResult)
                                    {
                                        sResult = string.Format("计划完成时出错_{0}", sPlanComplete);
                                        return bResult;
                                    }
                                }
                            }                          
                        }
                    }

                    this._P_RECORD_LIST.Add(mRECORD_LIST);

                    IList<SiaSun.LMS.Model.MANAGE_DETAIL> lsMANAGE_DETAIL = this._P_MANAGE_DETAIL.GetListManageListID(mMANAGE_LIST.MANAGE_LIST_ID);

                    foreach (SiaSun.LMS.Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                    {
                        mRECORD_DETAIL = new Model.RECORD_DETAIL();

                        mRECORD_DETAIL.RECORD_LIST_ID = mRECORD_LIST.RECORD_LIST_ID;
                        mRECORD_DETAIL.BOX_BARCODE = mMANAGE_DETAIL.BOX_BARCODE;
                        mRECORD_DETAIL.GOODS_BARCODE = mMANAGE_DETAIL.GOODS_BARCODE;
                        mRECORD_DETAIL.RECORD_DETAIL_REMARK = mMANAGE_DETAIL.MANAGE_DETAIL_REMARK;

                        this._P_RECORD_DETAIL.Add(mRECORD_DETAIL);
                    }
                }

                this._P_IO_CONTROL.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_DETAIL.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_LIST.DeleteManageID(MANAGE_ID);
                this._P_MANAGE_MAIN.Delete(MANAGE_ID);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }


        /// <summary>
        /// 生成不关联管理任务的Control任务_wdz
        /// </summary>
        public bool ControlCreate(int paraControlType, string paraStockBarcode,string paraStartWhNo,string paraStartDevice, string paraEndWhNo, string paraEndDevice,string paraLevel,out string sResult, bool verifyRoute = true, bool wsNoticeControl = false)
        {
            bool bResult = true;
            sResult = string.Empty;
            int controlId = 0;

            try
            {
                // wdz add 2018-05-20
                if (!string.IsNullOrEmpty(paraStockBarcode) && this._P_IO_CONTROL.GetList(paraStockBarcode).Count > 0)
                {
                    bResult = false;
                    sResult = string.Format("控制任务已经存在_条码[{0}]", paraStockBarcode);
                    return bResult;
                }

                //验证路径            
                if (verifyRoute && !string.IsNullOrEmpty(paraStartDevice) && !string.IsNullOrEmpty(paraEndDevice))
                {
                    DataTable dtIoControlRouteDisable = this.GetList(string.Format("select * from IO_CONTROL_ROUTE where START_DEVICE = '{0}' and END_DEVICE ='{1}' and CONTROL_ROUTE_STATUS ='0' ", paraStartDevice, paraEndDevice));
                    if (dtIoControlRouteDisable != null && dtIoControlRouteDisable.Rows.Count > 0)
                    {
                        bResult = false;
                        sResult = string.Format("S_ManageService.ControlCreate:任务路径不可用_起点[{0}]_终点[{1}]", paraStartDevice, paraEndDevice);
                        return bResult;
                    }
                }

                Model.IO_CONTROL mIO_CONTROL = new IO_CONTROL();

                mIO_CONTROL.RELATIVE_CONTROL_ID = -1;
                mIO_CONTROL.MANAGE_ID = 0;
                mIO_CONTROL.STOCK_BARCODE = paraStockBarcode;
                mIO_CONTROL.CONTROL_TASK_TYPE = paraControlType;
                mIO_CONTROL.CONTROL_TASK_LEVEL = paraLevel;
                mIO_CONTROL.START_WAREHOUSE_CODE = paraStartWhNo;
                mIO_CONTROL.START_DEVICE_CODE = paraStartDevice;
                mIO_CONTROL.END_WAREHOUSE_CODE = paraEndWhNo;
                mIO_CONTROL.END_DEVICE_CODE = paraEndDevice;
                mIO_CONTROL.CONTROL_STATUS = 0;
                mIO_CONTROL.CONTROL_BEGIN_TIME = Common.StringUtil.GetDateTime();
                mIO_CONTROL.AGV_TASK = "0";
                mIO_CONTROL.PRE_CONTROL_STATUS = "0";

                this._P_IO_CONTROL.Add(mIO_CONTROL);
                controlId = mIO_CONTROL.CONTROL_ID;

                //wdz add 2018-03-03
                if (wsNoticeControl)
                {
                    this.ManageDownLoadWebService(mIO_CONTROL.CONTROL_ID);
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("下达Control时发生异常 {0}",ex.Message);
            }
            finally
            {
                this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("S_ManageService.ControlCreate():下达非关联管理任务的Control任务_条码[{0}]_起点[{1}]_终点[{2}]_CONTROL_ID[{5}]_处理{3} {4}", paraStockBarcode, paraStartDevice, paraEndDevice, bResult ? "成功" : "失败 ", sResult, controlId));
            }

            return bResult;
        }


        /// <summary>
        /// 获取当前空箱缓存值
        /// </summary>
        public bool GetCurrentBufferValue(string bufferCode, out int bufferValue)
        {
            bool bResult = true;
            bufferValue = 0;

            try
            {
                DataTable dtBuff = this.GetList(string.Format("select BUFFER_VALUE from IO_CONTROL_BUFFER where BUFFER_CODE='{0}'", bufferCode));
                if (dtBuff != null && dtBuff.Rows.Count == 1 && int.TryParse(dtBuff.Rows[0]["BUFFER_VALUE"].ToString(),out bufferValue))
                {
                    
                }
                else
                {
                    bResult = false;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                throw;
            }

            return bResult;
        }


        /// <summary>
        /// 使用web服务方式处理调度申请
        /// wdz add 2018-03-01
        /// </summary>
        public string WsControlApply(string controlApplyType,string deviceCode,string stockBarcode,string controlApplyPara)
        {
            string result = "成功";
            bool bResult = true;
            string message = string.Empty;

            try
            {
                Model.IO_CONTROL_APPLY ioControlApply = new IO_CONTROL_APPLY()
                {
                    APPLY_TASK_STATUS = 0,
                    CONTROL_APPLY_TYPE = controlApplyType,
                    CREATE_TIME = Common.StringUtil.GetDateTime(),
                    DEVICE_CODE = deviceCode,
                    STOCK_BARCODE = stockBarcode,
                    CONTROL_APPLY_PARAMETER = controlApplyPara,
                    //标记是通过Webservice接收的申请
                    CONTROL_APPLY_REMARK = "ScanerReport"
                };

                Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)this.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", ioControlApply.CONTROL_APPLY_TYPE).RequestObject;
                bResult = this.Invoke(mAPPLY_TYPE.APPLY_TYPE_CLASS, "ApplyHandle", new object[] { ioControlApply }, out message);
            }
            catch (Exception ex)
            {
                bResult = false;
                message = string.Format("程序发生异常_异常信息[{0}]",ex.Message);
            }
            finally
            {
                if(!bResult)
                {
                    this.CreateSysLog(Enum.LogThread.Apply, "System", Enum.LOG_LEVEL.Error, string.Format("S_ManageService.WsControlApply():处理Web服务申请失败_{0}", message));
                    result = "失败_" + message;
                }
            }

            return result;
        }

        /// <summary>
        /// 使用webserev通知调度
        /// wdz add 2018-03-03
        /// </summary>
        public void ManageDownLoadWebService(int controlId)
        {
            bool result = true;
            string message = string.Empty;

            try
            {
                Model.IO_CONTROL ioControl = this._P_IO_CONTROL.GetModel(controlId);
                if (ioControl == null)
                {
                    result = false;
                    message = string.Format("未找到控制任务_传入控制任务ID[{0}]", controlId);
                    return;
                }

                //this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Information, string.Format("S_ManageService.ManageDownLoadWebService():调用WebService前_ControlId[{0}]", controlId));

                //result = new Implement.Interface.SendInputTaskByID().NotifyMethod(ioControl.CONTROL_ID, out message);

                message = myWcsService.SendInputTaskByID(controlId);

                //this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Information, string.Format("S_ManageService.ManageDownLoadWebService():调用WebService后_{0}_ControlId[{1}]", message, controlId));

                result = result && message.StartsWith("true") ? true : false;
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("程序发生异常_{0}", ex.Message);
            }
            finally
            {
                if (result)
                {
                    this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Information, string.Format("S_ManageService.ManageDownLoadWebService():使用WebService通知WCS任务下达成功_ControlId[{0}]", controlId));
                }
                else
                {
                    this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Error, string.Format("S_ManageService.ManageDownLoadWebService():使用WebService通知WCS任务下达失败_{0}_ControlId[{1}]", message, controlId));
                }
            }
            return;
        }

        /// <summary>
        /// 下达货架指定排起始列到终止列内物料的下架任务
        /// 修货架期间专用批量下架方法
        /// </summary>
        public string ContinusManageDown(Model.SYS_USER user ,int row ,int startColumn,int endColumn,string endPosition, string storageType)
        {
            string message = "success";
            Hashtable htStorageTypeSql = new Hashtable();
            htStorageTypeSql.Add("all", "");
            htStorageTypeSql.Add("noKitboxAndEmptyBox", "and STORAGE_MAIN.CELL_MODEL in ('1','2','3','4','5','6')");

            try
            {
                this._P_Base_House.BeginTransaction();

                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModelByDeviceCode(endPosition);
                if (mWH_CELL_END == null )
                {
                    message = string.Format("未找到下架终点位置_传入站台号[{0}]", endPosition);
                    this._P_Base_House.RollBackTransaction();
                    return message;
                }

                DataTable dtManageConfirm = this.GetList(string.Format("select 0 from MANAGE_MAIN where START_CELL_ID in (select CELL_ID from WH_CELL where CELL_Z={0}) or END_CELL_ID in (select CELL_ID from WH_CELL where CELL_Z={0}) ", row));
                if (dtManageConfirm != null && dtManageConfirm.Rows.Count != 0)
                {
                    message = string.Format("所选货架排[{0}]存在未完成的出入库任务，请确保任务完成后再批量下架", row);
                    this._P_Base_House.RollBackTransaction();
                    return message;
                }

                DataTable dtStorageEnable = this.GetList(string.Format("select STORAGE_ID,STOCK_BARCODE,CELL_CODE,WH_CELL.CELL_ID,CELL_X,CELL_Y,CELL_Z from STORAGE_MAIN left join WH_CELL on STORAGE_MAIN.CELL_ID = WH_CELL.CELL_ID where CELL_Z={0} and CELL_X between {1} and {2} {3}", row, startColumn, endColumn, string.IsNullOrEmpty(storageType) ? "" : htStorageTypeSql[storageType]));
                if (dtStorageEnable == null || dtStorageEnable.Rows.Count == 0)
                {
                    message = string.Format("所选货架排[{0}]_起始列[{1}]_到终止排[{2}]内没有库存", row, startColumn, endColumn);
                    this._P_Base_House.RollBackTransaction();
                    return message;
                }

                foreach (DataRow dr in dtStorageEnable.Rows)
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(int.Parse(dr["STORAGE_ID"].ToString()));
                    if (mSTORAGE_MAIN == null)
                    {
                        message = string.Format("货位[{0}]的库存信息未找到", dr["CELL_CODE"].ToString());
                        this._P_Base_House.RollBackTransaction();
                        return message;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        message = string.Format("货位[{0}]_箱条码[{1}]的库存列表信息未找到", dr["CELL_CODE"].ToString(), mSTORAGE_MAIN.STOCK_BARCODE);
                        this._P_Base_House.RollBackTransaction();
                        return message;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = mWH_CELL_END.CELL_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = "5";
                    mMANAGE_MAIN.MANAGE_OPERATOR = user.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_REMARK = "批量下架";
                    mMANAGE_MAIN.MANAGE_SOURCE = "WES";
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageDown.ToString();
                    mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<MANAGE_LIST>();
                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new MANAGE_LIST();

                        mMANAGE_LIST.BOX_BARCODE = itemSTORAGE_LIST.BOX_BARCODE;
                        mMANAGE_LIST.GOODS_ID = itemSTORAGE_LIST.GOODS_ID;
                        mMANAGE_LIST.GOODS_PROPERTY1 = itemSTORAGE_LIST.GOODS_PROPERTY1;
                        mMANAGE_LIST.GOODS_PROPERTY2 = itemSTORAGE_LIST.GOODS_PROPERTY2;
                        mMANAGE_LIST.GOODS_PROPERTY3 = itemSTORAGE_LIST.GOODS_PROPERTY3;
                        mMANAGE_LIST.GOODS_PROPERTY4 = itemSTORAGE_LIST.GOODS_PROPERTY4;
                        mMANAGE_LIST.GOODS_PROPERTY5 = itemSTORAGE_LIST.GOODS_PROPERTY5;
                        mMANAGE_LIST.GOODS_PROPERTY6 = itemSTORAGE_LIST.GOODS_PROPERTY6;
                        mMANAGE_LIST.GOODS_PROPERTY7 = itemSTORAGE_LIST.GOODS_PROPERTY7;
                        mMANAGE_LIST.GOODS_PROPERTY8 = itemSTORAGE_LIST.GOODS_PROPERTY8;
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.PLAN_LIST_ID = 0;
                        mMANAGE_LIST.STORAGE_LIST_ID = itemSTORAGE_LIST.STORAGE_LIST_ID;

                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    bool bResult = new SiaSun.LMS.Implement.ManageDown().ManageCreate(mMANAGE_MAIN, lsMANAGE_LIST, false, false, true, out message);
                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction();
                        return message;
                    }
                }

                message = "success|" + dtStorageEnable.Rows.Count.ToString();
                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                message = string.Format("S_ManageService.ContinusManageDown():发生异常_{0}", ex.Message);
                this._P_Base_House.RollBackTransaction();
            }
            finally
            {
                //log
                this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, message.Contains("success"), message);
            }

            return message;
        }


        /// <summary>
        /// 开始拣选
        /// </summary>
        public string ManualPickStart(Model.SYS_USER user, string stockBarcode, string uniqueCode, string pickStation)
        {
            string message = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                string validRegexString = string.Empty;
                if (this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex", out validRegexString) &&
                    !Common.RegexValid.IsValidate(stockBarcode, validRegexString))
                {
                    message = string.Format("申请条码不符合规范 申请值-{0}", stockBarcode);
                    return message;
                }

                var storageMainCheck = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if(storageMainCheck != null)
                {
                    message = string.Format("箱号[{0}]存在库存", stockBarcode);
                    return message;
                }
                var manageMain = this._P_MANAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if(manageMain != null)
                {
                    message = string.Format("箱号[{0}]存在任务", stockBarcode);
                    return message;
                }

                #region 锁定库存

                var planMains = this._P_PLAN_MAIN.GetList_PLAN_GROUP(uniqueCode);
                if (planMains == null || planMains.Count < 1)
                {
                    message = string.Format("未找到唯一码[{0}]的计划", uniqueCode);
                    return message;
                }

                foreach (var planMain in planMains)
                {
                    var planLists = this._P_PLAN_LIST.GetListPlanID(planMain.PLAN_ID);
                    if (planLists == null || planLists.Count < 1)
                    {
                        message = string.Format("未找到计划ID[{0}]的计划列表信息", planMain.PLAN_ID);
                        return message;
                    }

                    if(planLists.All(t=> t.PLAN_LIST_PICKED_QUANTITY >= t.PLAN_LIST_QUANTITY))
                    {
                        message = string.Format("计划均已拣选");
                        return message;
                    }

                    foreach (var planList in planLists)
                    {
                        var lockQtyDt = this.GetList($"select sum(STORAGE_LOCK_QUANTITY) STORAGE_LOCK_QUANTITY from STORAGE_LOCK where PLAN_LIST_ID={planList.PLAN_LIST_ID}");
                        decimal lockQty = decimal.Zero;
                        if (lockQtyDt != null && lockQtyDt.Rows.Count > 0)
                        {
                            decimal.TryParse(lockQtyDt.Rows[0]["STORAGE_LOCK_QUANTITY"].ToString(), out lockQty);
                        }


                        if (lockQty >= planList.PLAN_LIST_QUANTITY )
                        {
                            continue;
                        }

                        var sql = $@"select STORAGE_LIST_ID,STORAGE_LIST_QUANTITY-STORAGE_LOCK_QUANTITY ENABLE_QUANTITY
                                     from V_STORAGE_LIST_LOCK
                                     where GOODS_ID = {planList.GOODS_ID}
                                         and STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
                                         and CELL_TYPE = 'Cell'
                                         and(GOODS_PROPERTY2 is null or GOODS_PROPERTY2 != '1')
                                         and GOODS_PROPERTY6 is null
                                         and GOODS_PROPERTY7 is null
                                         and GOODS_PROPERTY8 is null
                                         and V_STORAGE_LIST_LOCK.STOCK_BARCODE not in (select STOCK_BARCODE from MANAGE_MAIN)
                                         and STORAGE_LIST_ID not in (select STORAGE_LIST_ID from MANAGE_LIST)                                           
                                         and(select count(0) from IO_CONTROL_ROUTE where START_DEVICE = DEVICE_CODE and END_DEVICE = '{pickStation}' 
                                             and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE = 1) > 0
                                     order by AREA_ID , ENTRY_TIME";

                        var enableStorage = this.GetList(sql);
                        if(enableStorage == null || enableStorage.Rows.Count < 1)
                        {
                            message = string.Format("锁定计划[{0}]时失败_可用库存不足", planMain.PLAN_CODE);
                            return message;
                        }

                        var enableQtySum = int.Parse(enableStorage.Compute("Sum(ENABLE_QUANTITY)", "").ToString());
                        if(enableQtySum < planList.PLAN_LIST_QUANTITY - lockQty)
                        {
                            message = string.Format("锁定计划[{0}]时失败_可用库存[{1}]不足", planMain.PLAN_CODE, enableQtySum);
                            return message;
                        }

                        foreach (DataRow item in enableStorage.Rows)
                        {
                            if(lockQty >= planList.PLAN_LIST_QUANTITY)
                            {
                                break;
                            }

                            var thisRowQty = decimal.Parse(item["ENABLE_QUANTITY"].ToString());
                            var needToLockQty = planList.PLAN_LIST_QUANTITY - lockQty;
                            var thisRowLockQty = needToLockQty >= thisRowQty ? thisRowQty : thisRowQty - needToLockQty;

                            var storageLock = new STORAGE_LOCK()
                            {
                                DETAIL_FLAG = Enum.FLAG.Disable.ToString("d"),
                                PLAN_LIST_ID = planList.PLAN_LIST_ID,
                                STORAGE_LIST_ID = int.Parse(item["STORAGE_LIST_ID"].ToString()),
                                STORAGE_LOCK_FLAG = Enum.FLAG.Disable.ToString("d"),
                                STORAGE_LOCK_QUANTITY = thisRowLockQty,
                                STORAGE_LOCK_REMARK = $"人工拣选锁定_{user.USER_NAME}_{Common.StringUtil.GetDateTime()}"
                            };
                            this._P_STORAGE_LOCK.Add(storageLock);

                            lockQty += thisRowLockQty;
                        }


                    }                    
                }

                #endregion

                #region 原料箱下架

                var targetStorages = this.GetList(string.Format(@"select SM.STOCK_BARCODE from STORAGE_MAIN SM 
                                                                  inner join STORAGE_LIST SL on SM.STORAGE_ID=SL.STORAGE_ID 
                                                                  inner join STORAGE_LOCK SO on SL.STORAGE_LIST_ID=SO.STORAGE_LIST_ID 
                                                                  inner join PLAN_LIST PL on SO.PLAN_LIST_ID=PL.PLAN_LIST_ID 
                                                                  inner join PLAN_MAIN PM on PL.PLAN_ID=PM.PLAN_ID
                                                                  where PM.PLAN_ID in ({0}) 
                                                                  group by SM.STOCK_BARCODE", string.Join(",", planMains.Select(t => t.PLAN_ID))));

                if (targetStorages == null || targetStorages.Rows.Count < 1)
                {
                    message = string.Format("未找到锁定库存");
                    return message;
                }

                var endCell = this._P_WH_CELL.GetModelByDeviceCode(pickStation);
                if (endCell == null)
                {
                    message = string.Format("未找到人工拣选的终点位置[{0}]", pickStation);
                    return message;
                }

                foreach (DataRow item in targetStorages.Rows)
                {
                    var storageMain = this._P_STORAGE_MAIN.GetModelStockBarcode(item["STOCK_BARCODE"].ToString());
                    if (storageMain == null)
                    {
                        message = string.Format("未找到箱条码[{0}]的库存信息", item["STOCK_BARCODE"].ToString());
                        return message;
                    }
                    var storageLists = this._P_STORAGE_LIST.GetListStorageID(storageMain.STORAGE_ID);
                    if (storageLists == null || storageLists.Count < 1)
                    {
                        message = string.Format("未找到箱条码[{0}]的库存列表", item["STOCK_BARCODE"].ToString());
                        return message;
                    }
                    var storageCell = this._P_WH_CELL.GetModel(storageMain.CELL_ID);
                    if(storageCell == null)
                    {
                        message = string.Format("未找到箱条码[{0}]的库存货位", item["STOCK_BARCODE"].ToString());
                        return message;
                    }
                    if(storageCell.CELL_CODE == pickStation)
                    {
                        continue;
                    }
                    var manageCheck = this.GetList($"select * from MANAGE_MAIN where STOCK_BARCODE='{storageMain.STOCK_BARCODE}' and (MANAGE_TYPE_CODE != 'ManageDown' or END_CELL_ID !={endCell.CELL_ID})");
                    if (manageCheck != null && manageCheck.Rows.Count > 1)
                    {
                        message = string.Format("箱条码[{0}]存在任务", item["STOCK_BARCODE"].ToString());
                        return message;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = "ManageDown";
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.STOCK_BARCODE = storageMain.STOCK_BARCODE;
                    mMANAGE_MAIN.CELL_MODEL = storageMain.CELL_MODEL;
                    mMANAGE_MAIN.START_CELL_ID = storageMain.CELL_ID;
                    mMANAGE_MAIN.END_CELL_ID = endCell.CELL_ID;
                    mMANAGE_MAIN.MANAGE_OPERATOR = user.USER_NAME;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                    string manageLevel = string.Empty;
                    mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("MiniloadSortPlanLevel", out manageLevel) ? manageLevel : "0";
                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                    List<Model.MANAGE_LIST> listMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    List<Model.STORAGE_LIST> STORAGE_LISTS = this._P_STORAGE_LIST.GetListStorageID(storageMain.STORAGE_ID).ToList();

                    foreach (Model.STORAGE_LIST mSTORAGE_LIST in STORAGE_LISTS)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                        mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(mSTORAGE_LIST, mMANAGE_LIST, null);
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        listMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    if (listMANAGE_LIST.Count == 0)
                    {
                        message = string.Format("任务明细创建失败");
                        return message;
                    }

                    string sResult;
                    bool result = new ManageDown().ManageCreate(mMANAGE_MAIN, listMANAGE_LIST, false, false, true, out sResult);
                    if (!result)
                    {
                        message = string.Format("原料箱下架失败_信息[{0}]", sResult);
                        return message;
                    }
                }

                #endregion

            }
            catch (Exception ex)
            {
                message = string.Format("S_ManageService.ManualPickStart():发生异常_{0}", ex.Message);
            }
            finally
            {
                //log
                this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, string.IsNullOrEmpty(message), message);

                if (string.IsNullOrEmpty(message))
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }

            return message;
        }

        /// <summary>
        /// 生成人工拣选任务 2022-08-24 15:37:10.386
        /// </summary>
        public string ManualCreatePickTask(Model.SYS_USER user, string stockBarcode, string pickStation, string planUniqueCode, out string boxMap)
        {
            boxMap = string.Empty;
            string message = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                var planMains = this._P_PLAN_MAIN.GetList_PLAN_GROUP(planUniqueCode);
                if (planMains == null || planMains.Count < 1)
                {
                    message = string.Format("未找到唯一码[{0}]的计划", planUniqueCode);
                    return message;
                }
                var storageMain = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if(storageMain == null)
                {
                    message = string.Format("未找到托盘[{0}]的库存信息", stockBarcode);
                    return message;
                }
                var storageLists = this._P_STORAGE_LIST.GetListStorageID(storageMain.STORAGE_ID);
                if (storageLists == null || storageLists.Count < 1)
                {
                    message = string.Format("未找到箱条码[{0}]的库存列表", storageMain.STOCK_BARCODE);
                    return message;
                }
                var storageCell = this._P_WH_CELL.GetModel(storageMain.CELL_ID);
                if (storageCell == null)
                {
                    message = string.Format("未找到托盘[{0}]的货位信息", stockBarcode);
                    return message;
                }
                if(storageCell.CELL_CODE != pickStation)
                {
                    message = string.Format("库存记录货位[{0}]与拣选位置[{1}]不同", storageCell.CELL_CODE , pickStation);
                    return message;
                }
                var storageLock = this.GetList(string.Format(@"select * from (select * from STORAGE_LOCK                                                               
                                                               where STORAGE_LIST_ID in ({0}) and 
                                                                     PLAN_LIST_ID in (select PLAN_LIST_ID from PLAN_LIST where PLAN_ID in ({1}))) AA 
                                                               inner join STORAGE_LIST SL on AA.STORAGE_LIST_ID=SL.STORAGE_LIST_ID 
                                                               inner join PLAN_LIST PL on AA.PLAN_LIST_ID=PL.PLAN_LIST_ID
                                                               inner join PLAN_MAIN PM on PL.PLAN_ID=PM.PLAN_ID
                                                               order by BOX_BARCODE", 
                                                               string.Join(",", storageLists.Select(t => t.STORAGE_LIST_ID)),
                                                               string.Join(",", planMains.Select(t=>t.PLAN_ID))));
                if(storageLock == null || storageLock.Rows.Count < 1)
                {
                    message = string.Format("未找到托盘[{0}]的锁定记录", stockBarcode);
                    return message;
                }
                var manageMain = this._P_MANAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if(manageMain != null)
                {
                    message = string.Format("托盘[{0}]已存在任务", stockBarcode);
                    return message;
                }

                decimal[] gridQty = new decimal[6] { decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero, decimal.Zero };
                foreach (DataRow item in storageLock.Rows)
                {
                    manageMain = new MANAGE_MAIN()
                    {
                        CELL_MODEL = storageMain.CELL_MODEL,
                        STOCK_BARCODE = storageMain.STOCK_BARCODE,
                        MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime(),
                        MANAGE_LEVEL = "1",
                        MANAGE_TYPE_CODE = "ManageManualPick",
                        MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingSend.ToString(),
                        MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString(),
                        MANAGE_OPERATOR = user.USER_NAME,
                        PLAN_ID = 0,
                        END_CELL_ID = storageMain.CELL_ID,
                        START_CELL_ID = storageMain.CELL_ID,
                        MANAGE_REMARK = string.Format("人工拣选任务")
                    };
                    this._P_MANAGE_MAIN.Add(manageMain);

                    var manageList = new MANAGE_LIST();
                    var storageList = storageLists.Where(t => t.STORAGE_LIST_ID.ToString() == item["STORAGE_LIST_ID"].ToString()).FirstOrDefault();
                    manageList = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(storageList, manageList, null);
                    manageList.MANAGE_ID = manageMain.MANAGE_ID;
                    manageList.DETAIL_FLAG = "0";
                    manageList.MANAGE_LIST_QUANTITY = decimal.Parse(item["STORAGE_LOCK_QUANTITY"].ToString());
                    manageList.STORAGE_LOCK_ID = int.Parse(item["STORAGE_LOCK_ID"].ToString());
                    manageList.PLAN_LIST_ID = int.Parse(item["PLAN_LIST_ID"].ToString());
                    manageList.GOODS_PROPERTY6 = item["PLAN_RELATIVE_CODE"].ToString();
                    manageList.GOODS_PROPERTY7 = item["PLAN_PROJECT_CODE"].ToString();
                    manageList.GOODS_PROPERTY8 = item["PLAN_CODE"].ToString();

                    this._P_MANAGE_LIST.Add(manageList);

                    //已经生成了拣选任务的锁定库存，需要置位
                    this._P_Base_House.ExecuteNonQuery(string.Format("update STORAGE_LOCK set STORAGE_LOCK_FLAG='1' where STORAGE_LOCK_ID={0} ", item["STORAGE_LOCK_ID"].ToString()),"");

                    //更新计划状态和分配数量
                    var planList = this._P_PLAN_LIST.GetModel(manageList.PLAN_LIST_ID);
                    if(planList == null)
                    {
                        message = string.Format("托盘[{0}]下发拣选任务后未找到计划列表", stockBarcode);
                        return message;
                    }                     
                    var planMain = this._P_PLAN_MAIN.GetModel(planList.PLAN_ID);
                    if(planMain == null)
                    {
                        message = string.Format("托盘[{0}]下发拣选任务后未找到计划信息", stockBarcode);
                        return message;
                    }
                    planList.PLAN_LIST_ORDERED_QUANTITY += manageList.MANAGE_LIST_QUANTITY;
                    this._P_PLAN_LIST.Update(planList);

                    if(planMain.PLAN_STATUS == Enum.PLAN_STATUS.Waiting.ToString())
                    {
                        planMain.PLAN_STATUS = Enum.PLAN_STATUS.Executing.ToString();
                        this._P_PLAN_MAIN.Update(planMain);
                    }

                    gridQty[int.Parse(item["BOX_BARCODE"].ToString()) - 1] = manageList.MANAGE_LIST_QUANTITY;
                }

                boxMap = string.Join("|", gridQty);
            }
            catch (Exception ex)
            {
                message = string.Format("S_ManageService.ManualCreatePickTask():发生异常_{0}", ex.Message);
            }
            finally
            {
                //log
                this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, string.IsNullOrEmpty(message), message);

                if (string.IsNullOrEmpty(message))
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }

            return message;
        }
    
        /// <summary>
        /// 人工拣选任务，单个物料完成 2022-10-08 12:51:11.154
        /// </summary>
        /// <param name="user"></param>
        /// <param name="sourceBox"></param>
        /// <param name="targetBox"></param>
        /// <param name="gridNo"></param>
        /// <returns></returns>
        public string ManualPickGoodsFinish(Model.SYS_USER user, string sourceBox, string targetBox,string gridNo, decimal updatedQty, string pickStation)
        {
            string message = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                var manageInfoSql = $"select * from MANAGE_MAIN MM inner join MANAGE_LIST ML on MM.MANAGE_ID=ML.MANAGE_ID where MM.MANAGE_TYPE_CODE ='{Enum.MANAGE_TYPE.ManageManualPick.ToString()}' and MM.STOCK_BARCODE='{sourceBox}' and ML.BOX_BARCODE='{gridNo}'";
                var manageInfo = this.GetList(manageInfoSql);
                if (manageInfo == null && manageInfo.Rows.Count > 0)
                {
                    message = $"未找到箱号[{sourceBox}]_格子号[{gridNo}]的人工拣选任务";
                    return message;
                }

                foreach (DataRow item in manageInfo.Rows)
                {
                    var manageMain = this._P_MANAGE_MAIN.GetModel(int.Parse(item["MANAGE_ID"].ToString()));
                    if (manageMain == null)
                    {
                        message = $"未找到箱号[{sourceBox}]_格子号[{gridNo}]的人工拣选任务信息";
                        return message;
                    }
                    var manageLists = this._P_MANAGE_LIST.GetListManageID(manageMain.MANAGE_ID);
                    if (manageLists == null || manageLists.Count != 1)
                    {
                        message = $"未找到箱号[{sourceBox}]_格子号[{gridNo}]的人工拣选任务列表信息";
                        return message;
                    }
                    var manageList = manageLists.First(t => t.BOX_BARCODE == gridNo);
                    if (manageList == null)
                    {
                        message = $"未找到与箱号[{sourceBox}]_格子号[{gridNo}]匹配的人工拣选任务列表信息";
                        return message;
                    }
                    if(manageList.MANAGE_LIST_QUANTITY < updatedQty)
                    {
                        message = $"实际拣选数量[{updatedQty}]大于人工拣选任务数量[{manageList.MANAGE_LIST_QUANTITY}]_箱号[{sourceBox}]_格子号[{gridNo}]";
                        return message;
                    }
                    var sourceStorageMain = this._P_STORAGE_MAIN.GetModelStockBarcode(sourceBox);
                    if (sourceStorageMain == null)
                    {
                        message = $"未找到箱号[{sourceBox}]的库存信息";
                        return message;
                    }
                    var sourceStorageLists = this._P_STORAGE_LIST.GetListStorageID(sourceStorageMain.STORAGE_ID);
                    if (sourceStorageLists == null || sourceStorageLists.Count < 1)
                    {
                        message = $"未找到箱号[{sourceBox}]的库存列表信息";
                        return message;
                    }
                    var sourceStorageList = sourceStorageLists.First(t => t.STORAGE_LIST_ID == manageList.STORAGE_LIST_ID);
                    if (sourceStorageList == null)
                    {
                        message = $"未找到与箱号[{sourceBox}]_库存列表ID[{manageList.STORAGE_LIST_ID}]匹配的库存列表信息";
                        return message;
                    }

                    //实际拣选数量少于任务数量时，调整拣选任务数量
                    decimal shortQty = manageList.MANAGE_LIST_QUANTITY - updatedQty;
                    if (manageList.MANAGE_LIST_QUANTITY > updatedQty)
                    {
                        manageList.MANAGE_LIST_QUANTITY = updatedQty;
                        this._P_MANAGE_LIST.Update(manageList);
                    }

                    var targetStorageMain = this._P_STORAGE_MAIN.GetModelStockBarcode(targetBox);
                    if (targetStorageMain == null)
                    {
                        targetStorageMain = new STORAGE_MAIN();
                        targetStorageMain.GOODS_TEMPLATE_ID = 0;
                        targetStorageMain.CELL_ID = sourceStorageMain.CELL_ID;
                        targetStorageMain.STOCK_BARCODE = targetBox;
                        targetStorageMain.CELL_MODEL = Enum.CellModel.KitBox.ToString("d");
                        this._P_STORAGE_MAIN.Add(targetStorageMain);
                    }

                    //将库存移动到齐套箱
                    if (manageList.MANAGE_LIST_QUANTITY < sourceStorageList.STORAGE_LIST_QUANTITY)
                    {
                        var targetStorageList = new STORAGE_LIST()
                        {
                            BOX_BARCODE = "0",
                            PLAN_LIST_ID = manageList.PLAN_LIST_ID,
                            STORAGE_ID = targetStorageMain.STORAGE_ID,
                            STORAGE_LIST_QUANTITY = manageList.MANAGE_LIST_QUANTITY,
                            STORAGE_LIST_ID = 0,
                            STORAGE_LIST_REMARK = "",
                            UPDATE_TIME = Common.StringUtil.GetDateTime(),
                            ENTRY_TIME = Common.StringUtil.GetDateTime(),
                            GOODS_ID = manageList.GOODS_ID,
                            GOODS_PROPERTY1 = manageList.GOODS_PROPERTY1,
                            GOODS_PROPERTY2 = manageList.GOODS_PROPERTY2,
                            GOODS_PROPERTY3 = manageList.GOODS_PROPERTY3,
                            GOODS_PROPERTY4 = manageList.GOODS_PROPERTY4,
                            GOODS_PROPERTY5 = manageList.GOODS_PROPERTY5,
                            GOODS_PROPERTY6 = manageList.GOODS_PROPERTY6,
                            GOODS_PROPERTY7 = manageList.GOODS_PROPERTY7,
                            GOODS_PROPERTY8 = manageList.GOODS_PROPERTY8
                        };
                        this._P_STORAGE_LIST.Add(targetStorageList);

                        //短拣时标记异常
                        if(shortQty > 0)
                        {
                            sourceStorageList.GOODS_PROPERTY2 = "1";
                            sourceStorageList.STORAGE_LIST_REMARK = string.Format("人工拣选发现实物与库存数据不符,实物短缺[{0}]", shortQty);
                        }
                        sourceStorageList.STORAGE_LIST_QUANTITY -= manageList.MANAGE_LIST_QUANTITY;
                        this._P_STORAGE_LIST.Update(sourceStorageList);
                    }
                    else if (manageList.MANAGE_LIST_QUANTITY == sourceStorageList.STORAGE_LIST_QUANTITY)
                    {
                        sourceStorageList.BOX_BARCODE = "0";
                        sourceStorageList.STORAGE_ID = targetStorageMain.STORAGE_ID;
                        sourceStorageList.PLAN_LIST_ID = manageList.PLAN_LIST_ID;
                        sourceStorageList.UPDATE_TIME = Common.StringUtil.GetDateTime();
                        sourceStorageList.GOODS_PROPERTY1 = manageList.GOODS_PROPERTY1;
                        sourceStorageList.GOODS_PROPERTY2 = manageList.GOODS_PROPERTY2;
                        sourceStorageList.GOODS_PROPERTY3 = manageList.GOODS_PROPERTY3;
                        sourceStorageList.GOODS_PROPERTY4 = manageList.GOODS_PROPERTY4;
                        sourceStorageList.GOODS_PROPERTY5 = manageList.GOODS_PROPERTY5;
                        sourceStorageList.GOODS_PROPERTY6 = manageList.GOODS_PROPERTY6;
                        sourceStorageList.GOODS_PROPERTY7 = manageList.GOODS_PROPERTY7;
                        sourceStorageList.GOODS_PROPERTY8 = manageList.GOODS_PROPERTY8;
                        this._P_STORAGE_LIST.Update(sourceStorageList);
                    }
                    else
                    {
                        message = $"库存数量[{sourceStorageList.STORAGE_LIST_QUANTITY}]少于任务数量[{manageList.MANAGE_LIST_QUANTITY}]_库存列表ID[{manageList.STORAGE_LIST_ID}]_原料箱条码[{sourceBox}]";
                        return message;
                    }

                    //更新计划已拣选数量
                    var planList = this._P_PLAN_LIST.GetModel(manageList.PLAN_LIST_ID);
                    if (planList != null)
                    {
                        planList.PLAN_LIST_PICKED_QUANTITY += manageList.MANAGE_LIST_QUANTITY;
                        planList.PLAN_LIST_ORDERED_QUANTITY -= shortQty;
                        this._P_PLAN_LIST.Update(planList);
                    }

                    //删除或修改锁定表数量
                    var storageLock = this._P_STORAGE_LOCK.GetModel(manageList.STORAGE_LOCK_ID);
                    if (storageLock == null)
                    {
                        message = $"未找到ID[{manageList.STORAGE_LOCK_ID}]的锁定信息";
                        return message;
                    }
                    if (storageLock.STORAGE_LOCK_QUANTITY.Equals(manageList.MANAGE_LIST_QUANTITY + shortQty))
                    {
                        this._P_STORAGE_LOCK.Delete(manageList.STORAGE_LOCK_ID);
                    }
                    else
                    {
                        storageLock.STORAGE_LOCK_QUANTITY -= manageList.MANAGE_LIST_QUANTITY;
                        this._P_STORAGE_LOCK.Update(storageLock);
                    }
                    var result = new ManageBase().ManageComplete(manageMain, false, out message);
                    if (!result)
                    {
                        return message;
                    }

                    //如果发生短拣，则再次锁定短拣数量
                    if(shortQty > 0)
                    {
                        var sql = $@"select STORAGE_LIST_ID,STORAGE_LIST_QUANTITY-STORAGE_LOCK_QUANTITY ENABLE_QUANTITY
                                     from V_STORAGE_LIST_LOCK
                                     where GOODS_ID = {manageList.GOODS_ID}
                                         and STORAGE_LIST_QUANTITY > STORAGE_LOCK_QUANTITY
                                         and CELL_TYPE = 'Cell'
                                         and(GOODS_PROPERTY2 is null or GOODS_PROPERTY2 != '1')
                                         and GOODS_PROPERTY6 is null
                                         and GOODS_PROPERTY7 is null
                                         and GOODS_PROPERTY8 is null
                                         and V_STORAGE_LIST_LOCK.STOCK_BARCODE not in (select STOCK_BARCODE from MANAGE_MAIN)
                                         and STORAGE_LIST_ID not in (select STORAGE_LIST_ID from MANAGE_LIST)                                           
                                         and(select count(0) from IO_CONTROL_ROUTE where START_DEVICE = DEVICE_CODE and END_DEVICE = '{pickStation}' 
                                             and CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_MANAGE = 1) > 0
                                     order by AREA_ID , ENTRY_TIME";

                        var enableStorage = this.GetList(sql);
                        if (enableStorage == null || enableStorage.Rows.Count < 1)
                        {
                            message = string.Format("托盘[{0}]的物料[{1}]短拣[{2}]_重新锁定计划列表时[{3}]时失败", manageMain.STOCK_BARCODE, manageList.GOODS_ID, shortQty, planList.PLAN_LIST_ID);
                            return message;
                        }

                        var enableQtySum = int.Parse(enableStorage.Compute("Sum(ENABLE_QUANTITY)", "").ToString());
                        if (enableQtySum < planList.PLAN_LIST_QUANTITY - shortQty)
                        {
                            message = string.Format("短拣后重新锁定计划列表[{0}]时失败_可用库存[{1}]不足", planList.PLAN_LIST_ID, enableQtySum);
                            return message;
                        }

                        decimal shortLockQty = 0;
                        foreach (DataRow storageRow in enableStorage.Rows)
                        {
                            if (shortLockQty >= shortQty)
                            {
                                break;
                            }

                            var thisRowQty = decimal.Parse(item["ENABLE_QUANTITY"].ToString());
                            var needToLockQty = shortQty - shortLockQty;
                            var thisRowLockQty = needToLockQty >= thisRowQty ? thisRowQty : thisRowQty - needToLockQty;

                            var storageShortLock = new STORAGE_LOCK()
                            {
                                DETAIL_FLAG = Enum.FLAG.Disable.ToString("d"),
                                PLAN_LIST_ID = planList.PLAN_LIST_ID,
                                STORAGE_LIST_ID = int.Parse(item["STORAGE_LIST_ID"].ToString()),
                                STORAGE_LOCK_FLAG = Enum.FLAG.Disable.ToString("d"),
                                STORAGE_LOCK_QUANTITY = thisRowLockQty,
                                STORAGE_LOCK_REMARK = $"人工拣选短拣后锁定_{user.USER_NAME}_{Common.StringUtil.GetDateTime()}"
                            };
                            this._P_STORAGE_LOCK.Add(storageLock);

                            shortLockQty += thisRowLockQty;
                        }
                    }

                    this._log.DebugFormat(@"[人工拣选] 拣选任务完成时，单据列表{0}锁定列表{1}库存列表{2}锁定数量{3}单据数量{4}短拣数量{5},条码{6}"
                        , manageList.PLAN_LIST_ID
                        , manageList.STORAGE_LOCK_ID
                        , manageList.STORAGE_LIST_ID
                        , storageLock.STORAGE_LOCK_QUANTITY
                        , manageList.MANAGE_LIST_QUANTITY
                        , shortQty
                        , manageMain.STOCK_BARCODE);
                }
            }
            catch (Exception ex)
            {
                message = string.Format("S_ManageService.ManualPickGoodsFinish():发生异常_{0}", ex.Message);
            }
            finally
            {
                //log
                this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, string.IsNullOrEmpty(message), message);

                if (string.IsNullOrEmpty(message))
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }

            return message;
        }
   

        /// <summary>
        /// 本地盘点下达下架任务 2023-09-23
        /// </summary>
        /// <param name="user"></param>
        /// <param name="planCode"></param>
        /// <param name="goodsCodeList"></param>
        /// <param name="endCellCode"></param>
        /// <returns></returns>
        public string CreateCheckDownTask(Model.SYS_USER user,string planCode, List<string> goodsCodeList ,string endCellCode )
        {
            string message = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                var planCheck = this.GetList($"select * from PLAN_MAIN where PLAN_CODE ='{planCode}'");
                if(planCheck!=null && planCheck.Rows.Count > 0)
                {
                    message = $"计划单号[{planCode}]已存在";
                    return message;
                }

                var planMain = new PLAN_MAIN()
                {
                    PLAN_CODE = planCode,
                    PLAN_CREATER = user.USER_NAME,
                    PLAN_CREATE_TIME = Common.StringUtil.GetDateTime(),
                    PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanCheckManual.ToString(),
                    PLAN_STATUS = Enum.PLAN_STATUS.Finish.ToString()
                };
                this._P_PLAN_MAIN.Add(planMain);
                foreach (var item in goodsCodeList)
                {
                    var goodsMain = this._P_GOODS_MAIN.GetModel(item);
                    if (goodsMain == null)
                    {
                        message = $"物料[{item}]不存在";
                        return message;
                    }
                    var planList = new PLAN_LIST()
                    {
                        PLAN_ID = planMain.PLAN_ID,
                        GOODS_ID = goodsMain.GOODS_ID
                    };
                    this._P_PLAN_LIST.Add(planList);
                }

                var targetStock = this.GetList($@"select distinct STOCK_BARCODE from V_STORAGE_LIST 
                                                             where CELL_MODEL in ('1','2','3','4','5','6')  and GOODS_PROPERTY6 is null and GOODS_PROPERTY7 is null and GOODS_PROPERTY8 is null
                                                             and STOCK_BARCODE not in (select STOCK_BARCODE from MANAGE_MAIN)
                                                             and GOODS_CODE in ('{string.Join("','", goodsCodeList)}') ");

                if(targetStock == null || targetStock.Rows.Count < 1)
                {
                    message = "可下达料箱数量为0";
                    return message;
                }

                var endCell = this._P_WH_CELL.GetModelByDeviceCode(endCellCode);
                if(endCell == null)
                {
                    message = $"未找到编码为[{endCellCode}]的货位信息";
                    return message;
                }

                foreach (DataRow item in targetStock.Rows)
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(item["STOCK_BARCODE"].ToString());
                    if (mSTORAGE_MAIN == null)
                    {
                        this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, false, string.Format("PlanBase.CreateCheckDownTask:未找到立库库存_箱条码[{0}]", item["STOCK_BARCODE"].ToString()));
                        continue;
                    }
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                    if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count == 0)
                    {
                        this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, false, string.Format("PlanBase.CreateCheckDownTask:未找到库存列表_箱条码[{0}]", mSTORAGE_MAIN.STOCK_BARCODE));
                        continue;
                    }

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();
                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.END_CELL_ID = endCell.CELL_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = "";
                    mMANAGE_MAIN.MANAGE_OPERATOR = user.USER_NAME;
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageCheckDownLocal.ToString();
                    mMANAGE_MAIN.PLAN_ID = planMain.PLAN_ID;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanCheckManual.ToString();
                    mMANAGE_MAIN.PLAN_TYPE_CODE ="";
                    mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;
                    //mMANAGE_MAIN.MANAGE_RELATE_CODE = planCode;

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    foreach (Model.STORAGE_LIST itemSTORAGE_LIST in lsSTORAGE_LIST)
                    {
                        Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                        mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(itemSTORAGE_LIST, mMANAGE_LIST, null);
                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = itemSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                        mMANAGE_LIST.PLAN_LIST_ID = 0;
                        lsMANAGE_LIST.Add(mMANAGE_LIST);
                    }

                    Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    string sResult = "";
                    var bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, false, false, true }, out sResult);
                    if (!bResult)
                    {
                        this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, false, string.Format("PlanBase.CreateCheckDownTask:下达下夹任务失败_箱条码[{0}]", mSTORAGE_MAIN.STOCK_BARCODE));
                        continue;
                    }
                }
              
            }
            catch (Exception ex)
            {
                message = string.Format("S_ManageService.CreateCheckDownTask():发生异常_{0}", ex.Message);
            }
            finally
            {
                //log
                this.CreateSysLog(Enum.LogThread.Task, user.USER_NAME, string.IsNullOrEmpty(message), message);

                if (string.IsNullOrEmpty(message))
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }

            return message;
        }
    }
}