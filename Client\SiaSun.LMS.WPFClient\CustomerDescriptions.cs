﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.Xml;

namespace SiaSun.LMS.WPFClient
{
    /// <summary>
    /// 显示样式类
    /// </summary>
    public class CustomerDescriptions
    {
        /// <summary>
        /// 从FieldDescription.xml读取样式数据并返回数据集合
        /// </summary>
        public DataTable GetStyleDataTable(string TableName)
        {
            try
            {
                //获得描述信息
                XmlDocument xmlDoc = new XmlDocument();
                DataTable tableFieldDescription = new DataTable();

                //获得描述信息
                xmlDoc.Load(MainApp.File_FieldDescription_Path);
                //获得节点
                XmlNode xmlNode = xmlDoc.SelectSingleNode(string.Format(@"/Tables/Table[@Name='{0}']", TableName));
                if (xmlNode == null)
                    throw new Exception(string.Format("XML中找不到节点{0}", TableName));
                tableFieldDescription = new SiaSun.LMS.Common.XmlFiles().GetDataTable(xmlNode.OuterXml);
                return tableFieldDescription;
            }
            catch (Exception ex)
            {
                throw new Exception(string.Format("读取样式文件,节点{0}", TableName) + ex.Message, ex);
            }
        }

        /// <summary>
        /// 从Form_Style.xml中获得窗体样式
        /// </summary>
        public DataTable GetFormStyleDataTable(string FormName,string TableName)
        {
            try
            {
                //获得描述信息
                XmlDocument xmlDoc = new XmlDocument();
                DataTable tableFieldDescription = new DataTable();
               
                xmlDoc.Load(MainApp.File_FormStyles_Path);
                //获得节点
                XmlNode xmlNode = xmlDoc.SelectSingleNode(string.Format(@"/Styles/Form[@Name='{0}']", FormName));
                if (xmlNode == null)
                    throw new Exception(string.Format("XML中找不到窗体节点{0}", FormName));
                xmlNode = xmlNode.SelectSingleNode(string.Format(@"Table[@Name='{0}']", TableName));
                if (xmlNode == null)
                    throw new Exception(string.Format("XML中找不到窗体{0}的样式节点{1}", FormName));
                tableFieldDescription = new SiaSun.LMS.Common.XmlFiles().GetDataTable(xmlNode.OuterXml);

                return tableFieldDescription;
            }
            catch
            {
                try
                {
                    return GetStyleDataTable(TableName);
                }
                catch (Exception ex)
                {
                    throw new Exception(string.Format("读取窗体样式文件,节点{0}", TableName) + ex.Message, ex);
                }
            }
        }

        /// <summary>
        /// 设置默认值
        /// </summary>
        public void SetDefaultValue(DataTable tableSource,string WindowName,string TableName)
        {
            using (DataTable tableFieldDescription = string.IsNullOrEmpty(WindowName) ? this.GetStyleDataTable(TableName) : this.GetFormStyleDataTable(WindowName, TableName))
            {
                foreach (DataColumn col in tableSource.Columns)
                {
                    DataRow[] arDataRow = tableFieldDescription.Rows.Cast<DataRow>().ToArray();
                    if (arDataRow.Count(r => r["Column"].ToString() ==col.ColumnName) > 0)
                    {
                        DataRow row = arDataRow.First(r => r["Column"].ToString() == col.ColumnName);
                        if (!row.IsNull("DefaultValue") && !string.IsNullOrEmpty(row["DefaultValue"].ToString()))
                        {
                            col.DefaultValue = row["DefaultValue"];
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 获得ComboBox列相关的数据源
        /// </summary>
        public DataTable GetComboBoxDataTable(string DataBind)
        {
            if (DataBind.ToLower().Contains("select"))
                return MainApp._I_BaseService.GetList(DataBind);
            else
                return MainApp._I_SystemService.ITEM_LIST_GetDictionary(DataBind);
        }

        /// <summary>
        /// 根据数据源，更改列的名称，返回新的数据源，用于打印时生成报表和导出
        /// </summary>
        public DataTable GetReportDataTable(DataTable tableSource,string WindowName,string TableName,ref DataTable tableHeader)
        {
            DataTable tableReport = tableSource.Copy();
            using (DataTable tableFieldDescription = string.IsNullOrEmpty(WindowName) ? this.GetStyleDataTable(TableName) : this.GetFormStyleDataTable(WindowName, TableName))
            {
                //字典，存储ComboBox列的名称和数据源
                Dictionary<string, DataTable> dicComboPair = new Dictionary<string, DataTable>();
                DataRow[] arDataRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray();
                for (int i = tableReport.Columns.Count - 1; i >= 0; i--)
                {
                    //数据表显示的列
                    string strColumnName = tableReport.Columns[i].ColumnName;

                    //判断是否存在描述
                    if (arDataRowField.Count(r => r["Column"].ToString() == strColumnName) == 0)
                    {
                        tableReport.Columns.Remove(strColumnName);
                    }
                    else
                    {
                        DataRow rowField = arDataRowField.First(r => r["Column"].ToString() == strColumnName);

                        //判断显示信息是否空
                        if (rowField.IsNull("Header") || string.IsNullOrEmpty(rowField["Header"].ToString()))
                        {
                            tableReport.Columns.Remove(strColumnName);
                        }
                        else
                        {
                            //判断是否是Combox列，如果是则存储在字典中
                            if (rowField["ControlType"].ToString().ToLower() == "combobox" && !string.IsNullOrEmpty(rowField["DataBind"].ToString()))
                            {
                                //更改数据表的值类型
                                tableReport.Columns.Add(string.Format("{0}_1", rowField["Column"].ToString()), typeof(System.String));

                                //添加字典
                                dicComboPair.Add(rowField["Column"].ToString(), this.GetComboBoxDataTable(rowField["DataBind"].ToString()));
                            }
                        }
                    }
                }  

                //判断是否存在ComboBox列
                if (dicComboPair.Count > 0)
                {
                    foreach (DataRow rowReport in tableReport.Rows)
                    {
                        foreach (KeyValuePair<string, DataTable> pair in dicComboPair)
                        {
                            if (!rowReport.IsNull(pair.Key))
                            {
                                if (pair.Value.Select(string.Format("VALUE='{0}'", rowReport[pair.Key].ToString())).Length > 0)
                                {
                                    //复制ComboBox中显示的元素
                                    rowReport[string.Format("{0}_1", pair.Key)] = pair.Value.Select(string.Format("VALUE='{0}'", rowReport[pair.Key].ToString()))[0]["NAME"];
                                }
                            }
                        }
                    }
                    
                    //移除原有的列,并将复制的列名更改为原有的列名
                    foreach (KeyValuePair<string, DataTable> pair in dicComboPair)
                    {
                        tableReport.Columns.Remove(pair.Key);
                        tableReport.Columns[string.Format("{0}_1", pair.Key)].ColumnName = pair.Key;
                    }
                }

                //设置表头描述
                if (tableHeader != null)
                {
                    foreach (DataColumn col in tableReport.Columns)
                    {
                        if (tableFieldDescription.Select(string.Format("Column='{0}'", col.ColumnName)).Length > 0)
                        {
                            DataRow rowField = tableFieldDescription.Select(string.Format("Column='{0}'", col.ColumnName))[0];
                            tableHeader.Rows.Add(new object[] { rowField["Header"].ToString() });
                        }
                    }
                }
            }
            return tableReport;
        }

    }
}
