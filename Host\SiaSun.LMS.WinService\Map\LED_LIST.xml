﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="LED_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="LED_LIST" type="SiaSun.LMS.Model.LED_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="LED_LIST">
			<result property="LED_LIST_ID" column="led_list_id" />
			<result property="LED_ID" column="led_id" />
			<result property="LINE_NO" column="line_no" />
			<result property="AREA_X" column="area_x" />
			<result property="AREA_Y" column="area_y" />
			<result property="AREA_WIDTH" column="area_width" />
      <result property="AREA_HEIGHT" column="area_height" />
      <result property="FILE_NAME" column="file_name" />
      <result property="LINE_TEXT" column="line_text" />
      <result property="FONT_SIZE" column="font_size" />
      <result property="SHOW_STUNT" column="show_stunt" />
      <result property="RUN_SPEED" column="run_speed" />
      <result property="SHOW_TIME" column="show_time" />
      <result property="LED_LIST_REMARK" column="led_list_remark" />
      <result property="LED_LIST_PARA1" column="led_list_para1" />
      <result property="LED_LIST_PARA2" column="led_list_para2" />
      <result property="LED_LIST_PARA3" column="led_list_para3" />
      <result property="LED_LIST_PARA4" column="led_list_para4" />
      <result property="LED_LIST_PARA5" column="led_list_para5" />      
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="LED_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        led_list_id,
        led_id,
        line_no,
        area_x,
        area_y,
        area_width,
        area_height,
        file_name,
        line_text,
        font_size,
        show_stunt,
        run_speed,
        show_time,
        led_list_remark,
        led_list_para1,
        led_list_para2,
        led_list_para3,
        led_list_para4,
        led_list_para5
        From LED_LIST
      </select>
		
		<select id="LED_LIST_SELECT_BY_ID" parameterClass="int" extends = "LED_LIST_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
          led_list_id=#LED_LIST_ID# 
        </isParameterPresent>
			</dynamic>
		</select>

    <select id="LED_LIST_SELECT_BY_LED_ID" parameterClass="int" extends = "LED_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          led_id=#LED_ID# ORDER BY line_no
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="LED_LIST_SELECT_BY_LED_ID_LINE_NO" parameterClass="Hashtable" extends = "LED_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          led_id=#LED_ID# AND line_no=#LINE_NO#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="LED_LIST_INSERT" parameterClass="LED_LIST">
      Insert Into LED_LIST (
      led_list_id,
      led_id,
      line_no,
      area_x,
      area_y,
      area_width,
      area_height,
      file_name,
      line_text,
      font_size,
      show_stunt,
      run_speed,
      show_time,
      led_list_remark,
      led_list_para1,
      led_list_para2,
      led_list_para3,
      led_list_para4,
      led_list_para5
      )Values(
      #LED_LIST_ID#,
      #LED_ID#,
      #LINE_NO#,
      #AREA_X#,
      #AREA_Y#,
      #AREA_WIDTH#,
      #AREA_HEIGHT#,
      #FILE_NAME#,
      #LINE_TEXT#,
      #FONT_SIZE#,
      #SHOW_STUNT#,
      #RUN_SPEED#,
      #SHOW_TIME#,
      #LED_LIST_REMARK#,
      #LED_LIST_PARA1#,
      #LED_LIST_PARA2#,
      #LED_LIST_PARA3#,
      #LED_LIST_PARA4#,
      #LED_LIST_PARA5#
      )
    </insert>
		
		<update id="LED_LIST_UPDATE" parameterClass="LED_LIST">
      Update LED_LIST Set
      led_list_id=#LED_LIST_ID#,
      led_id=#LED_ID#,
      line_no=#LINE_NO#,
      area_x=#AREA_X#,
      area_y=#AREA_Y#,
      area_width=#AREA_WIDTH#,
      area_height=#AREA_HEIGHT#,
      file_name=#FILE_NAME#,
      line_text=#LINE_TEXT#,
      font_size=#FONT_SIZE#,
      show_stunt=#SHOW_STUNT#,
      run_speed=#RUN_SPEED#,
      show_time=#SHOW_TIME#,
      led_list_remark=#LED_LIST_REMARK#,
      led_list_para1=#LED_LIST_PARA1#,
      led_list_para2=#LED_LIST_PARA2#,
      led_list_para3=#LED_LIST_PARA3#,
      led_list_para4=#LED_LIST_PARA4#,
      led_list_para5=#LED_LIST_PARA5#
      <dynamic prepend="WHERE">
				<isParameterPresent>
          led_list_id=#LED_LIST_ID#
        </isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="LED_LIST_DELETE" parameterClass="int">
      Delete From LED_LIST
      <dynamic prepend="WHERE">
				<isParameterPresent>
          led_list_id=#LED_LIST_ID#
        </isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>