﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Collections.Concurrent;
using Microsoft.AspNet.SignalR;
using System.Windows;

namespace SignalServer.Models
{
    public class WorkHub: Hub<IClient>
    {
        /// <summary>
        /// 静态存储连接客户端的信息
        /// </summary>
        public static ConcurrentDictionary<string, Client> MessageClients = new ConcurrentDictionary<string, Client>();

        /// <summary>
        /// 客户端连接
        /// </summary>
        /// <returns></returns>
        public override Task OnConnected()
        {
            Application.Current.Dispatcher.Invoke(() =>
               ((MainWindow)Application.Current.MainWindow).WriteLog("客户端连接:ID:" + Context.ConnectionId));

            return base.OnConnected();
        }

        /// <summary>
        /// 客户端断连接
        /// </summary>
        /// <param name="stopCalled">true,客户端主动关闭</param>
        /// <returns></returns>
        public override Task OnDisconnected(bool stopCalled)
        {
            try
            {
                string pickStationCodeKey = MessageClients.SingleOrDefault((c) => c.Value.ConnectID == Context.ConnectionId).Key;
                if (stopCalled)
                {
                    //客户端主动关闭
                    this.Logout();

                    Application.Current.Dispatcher.Invoke(new Action(
                    () =>
                    {
                        MainWindow mainWindow = ((MainWindow)Application.Current.MainWindow);
                        mainWindow.UpdatePickStationUserInfo(pickStationCodeKey, 0, string.Empty, string.Empty, string.Empty, ClientStatus.Offline.ToString(), false);

                        mainWindow.WriteLog(string.Format("客户端主动关闭: 工作站编码:{0},连接ID{1}", pickStationCodeKey, Context.ConnectionId));

                    }
                    ));
                }
                else
                {
                    //客户端主动关闭
                    this.Logout();
                    //超时被动关闭
                    Application.Current.Dispatcher.Invoke(new Action(
                    () =>
                    {
                        MainWindow mainWindow = ((MainWindow)Application.Current.MainWindow);
                        mainWindow.UpdatePickStationUserInfo(pickStationCodeKey, 0, string.Empty, string.Empty, string.Empty, ClientStatus.Offline.ToString(), false);

                        mainWindow.WriteLog(string.Format("客户端被动超时关闭: 工作站编码:{0},连接ID{1}", pickStationCodeKey, Context.ConnectionId));

                    }
                    ));
                }

            }
            catch (Exception ex)
            {
                Application.Current.Dispatcher.Invoke(() =>
              ((MainWindow)Application.Current.MainWindow).WriteLog("断线异常:" + ex.Message));
            }

            return base.OnDisconnected(stopCalled);
        }

        public override Task OnReconnected()
        {
            string pickStationCodeKey = MessageClients.SingleOrDefault((c) => c.Value.ConnectID == Context.ConnectionId).Key;

            Application.Current.Dispatcher.Invoke(() =>
              ((MainWindow)Application.Current.MainWindow).WriteLog("客户端重新连接:ID:" + Context.ConnectionId));

            return base.OnReconnected();
        }


        /// <summary>
        /// 客户端登录
        /// 由客户端通过hubProxy.Invoke(“Login”)调用 
        /// </summary>
        /// <param name="pickStationCode">工作站编码</param>
        /// <param name="userId">用户ID</param>
        /// <param name="userCode">用户编码</param>
        /// <param name="userName">用户名称</param>
        /// <param name="userRole">用户角色</param>
        /// <returns>登录成功标志</returns>
        public bool Login(string pickStationCode,
            int userId,
            string userCode,
            string userName,
            string userRole)
        {
            bool bResult = false;

            if (!MessageClients.ContainsKey(pickStationCode))
            {
                //Console.WriteLine($"++ {pickStationCode} logged in");
                Client clientToAdd = null;

                Application.Current.Dispatcher.Invoke(new Action(
                    () =>
                    {
                        MainWindow mainWindow = ((MainWindow)Application.Current.MainWindow);
                        clientToAdd = mainWindow.UpdatePickStationUserInfo(pickStationCode, userId, userCode, userName, userRole, ClientStatus.Online.ToString(), true);
                    }
                    ));

                if (clientToAdd != null)
                {
                    clientToAdd.ConnectID = Context.ConnectionId;
                    bResult = MessageClients.TryAdd(pickStationCode, clientToAdd);
                }

                if (bResult)
                {
                    Application.Current.Dispatcher.Invoke(() =>
              ((MainWindow)Application.Current.MainWindow).WriteLog("用户登录成功:" + clientToAdd.LoginUserName));
                }
                else
                {
                    Application.Current.Dispatcher.Invoke(() =>
              ((MainWindow)Application.Current.MainWindow).WriteLog("用户登录失败:" + clientToAdd.LoginUserName));
                }
            }
            return bResult;
        }

        /// <summary>
        /// 客户端登出
        /// 由客户端通过hubProxy.Invoke(“Logout”)调用 
        /// </summary>
        public void Logout()
        {
            string id = Context.ConnectionId;

            if (!string.IsNullOrEmpty(id))
            {
                Client client = new Client();
                try
                {
                    string pickStationCodeKey = MessageClients.SingleOrDefault(s => s.Value.ConnectID == id).Key;

                    if (MessageClients.TryRemove(pickStationCodeKey, out client))
                    {

                        Application.Current.Dispatcher.Invoke(() =>
                        {
                            MainWindow mainWindow = ((MainWindow)Application.Current.MainWindow);
                            mainWindow.UpdatePickStationUserInfo(pickStationCodeKey, 0, string.Empty, string.Empty, string.Empty, ClientStatus.Offline.ToString(), false);

                            mainWindow.WriteLog(string.Format("登出成功:用户名:{0},工作站:{1},连接ID：{2}", client.LoginUserName, client.PickStationName, id));
                        });

                    }
                    else
                    {
                        Application.Current.Dispatcher.Invoke(() =>
                 ((MainWindow)Application.Current.MainWindow).WriteLog(string.Format("登出失败:工作站:{0},连接ID：{1}", client.PickStationName, id)));
                    }
                }
                catch (Exception ex)
                {
                    Application.Current.Dispatcher.Invoke(() =>
                ((MainWindow)Application.Current.MainWindow).WriteLog(string.Format("登出异常:连接ID：{0},异常信息:{1}", id, ex.Message)));
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="message"></param>
        public void BroadcastTextMessage(string message)
        {
            var name = Clients.CallerState.UserName;
            if (!string.IsNullOrEmpty(name) && !string.IsNullOrEmpty(message))
            {
                Clients.Others.BroadcastTextMessage(name, message);
            }
        }
    }
}
