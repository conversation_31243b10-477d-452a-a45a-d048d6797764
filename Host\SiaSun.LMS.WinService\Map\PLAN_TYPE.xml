﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="PLAN_TYPE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="PLAN_TYPE" type="SiaSun.LMS.Model.PLAN_TYPE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="PLAN_TYPE">
			<result property="PLAN_TYPE_ID" column="plan_type_id" />
			<result property="PLAN_TYPE_CODE" column="plan_type_code" />
			<result property="PLAN_TYPE_NAME" column="plan_type_name" />
			<result property="PLAN_TYPE_GROUP" column="plan_type_group" />
			<result property="PLAN_TYPE_INOUT" column="plan_type_inout" />
			<result property="PLAN_TYPE_REMARK" column="plan_type_remark" />
			<result property="PLAN_TYPE_ORDER" column="plan_type_order" />
			<result property="PLAN_TYPE_FLAG" column="plan_type_flag" />
			<result property="PLAN_TYPE_CLASS" column="plan_type_class" />
			<result property="MANAGE_TYPE_CODE" column="manage_type_code" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="PLAN_TYPE_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  plan_type_id,
				  plan_type_code,
				  plan_type_name,
				  plan_type_group,
				  plan_type_inout,
				  plan_type_remark,
				  plan_type_order,
				  plan_type_flag,
				  plan_type_class,
				  manage_type_code
			From PLAN_TYPE
		</select>
		
		<select id="PLAN_TYPE_SELECT_BY_ID" parameterClass="int" extends = "PLAN_TYPE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_type_id=#PLAN_TYPE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="PLAN_TYPE_SELECT_BY_PLAN_TYPE_CODE" parameterClass="int" extends = "PLAN_TYPE_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_type_code=#PLAN_TYPE_CODE#
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="PLAN_TYPE_INSERT" parameterClass="PLAN_TYPE">
      Insert Into PLAN_TYPE (
      plan_type_id,
      plan_type_code,
      plan_type_name,
      plan_type_group,
      plan_type_inout,
      plan_type_remark,
      plan_type_order,
      plan_type_flag,
      plan_type_class,
      manage_type_code
      )Values(
      #PLAN_TYPE_ID#,
      #PLAN_TYPE_CODE#,
      #PLAN_TYPE_NAME#,
      #PLAN_TYPE_GROUP#,
      #PLAN_TYPE_INOUT#,
      #PLAN_TYPE_REMARK#,
      #PLAN_TYPE_ORDER#,
      #PLAN_TYPE_FLAG#,
      #PLAN_TYPE_CLASS#,
      #MANAGE_TYPE_CODE#
      )
      <!--<selectKey  resultClass="int" type="post" property="PLAN_TYPE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="PLAN_TYPE_UPDATE" parameterClass="PLAN_TYPE">
      Update PLAN_TYPE Set
      plan_type_id=#PLAN_TYPE_ID#,
      plan_type_code=#PLAN_TYPE_CODE#,
      plan_type_name=#PLAN_TYPE_NAME#,
      plan_type_group=#PLAN_TYPE_GROUP#,
      plan_type_inout=#PLAN_TYPE_INOUT#,
      plan_type_remark=#PLAN_TYPE_REMARK#,
      plan_type_order=#PLAN_TYPE_ORDER#,
      plan_type_flag=#PLAN_TYPE_FLAG#,
      plan_type_class=#PLAN_TYPE_CLASS#,
      manage_type_code=#MANAGE_TYPE_CODE#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					plan_type_id=#PLAN_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="PLAN_TYPE_DELETE" parameterClass="int">
			Delete From PLAN_TYPE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_type_id=#PLAN_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>