﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// MANAGE_DETAIL
	/// </summary>
	public class P_MANAGE_DETAIL : P_Base_House
	{
		public P_MANAGE_DETAIL ()
		{
			//
			// TODO: 此处添加MANAGE_DETAIL的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<MANAGE_DETAIL> GetList()
		{
			return ExecuteQueryForList<MANAGE_DETAIL>("MANAGE_DETAIL_SELECT",null);
		}


        public IList<MANAGE_DETAIL> GetListManageListID(int MANAGE_LIST_ID)
        {
            return this.ExecuteQueryForList<MANAGE_DETAIL>("MANAGE_DETAIL_SELECT_BY_MANAGE_LIST_ID", MANAGE_LIST_ID);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(MANAGE_DETAIL manage_detail)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("MANAGE_DETAIL");
                manage_detail.MANAGE_DETAIL_ID = id;
            }
            return ExecuteInsert("MANAGE_DETAIL_INSERT",manage_detail);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(MANAGE_DETAIL manage_detail)
		{
			return ExecuteUpdate("MANAGE_DETAIL_UPDATE",manage_detail);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public MANAGE_DETAIL GetModel(System.Int32 MANAGE_DETAIL_ID)
		{
			return ExecuteQueryForObject<MANAGE_DETAIL>("MANAGE_DETAIL_SELECT_BY_ID",MANAGE_DETAIL_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 MANAGE_DETAIL_ID)
		{
			return ExecuteDelete("MANAGE_DETAIL_DELETE",MANAGE_DETAIL_ID);
		}

        public int DeleteManageID(System.Int32 MANAGE_ID)
        {
            return ExecuteDelete("MANAGE_DETAIL_DELETE_MANAGE_ID", MANAGE_ID);
        }
		

	}
}
