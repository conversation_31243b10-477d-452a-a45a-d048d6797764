﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using System.Collections.ObjectModel;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_OUT : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        List<Model.MANAGE_LIST> listMANAGE_LIST = null;

        string STORAGE_AREA_TYPE = string.Empty;
        public string teststr = string.Empty;
        public ObservableCollection<DataColumnChangeEventArgs> Errors { get; set; }

        /// <summary>
        /// 
        /// </summary>
        public MANAGE_OUT(string MANAGE_TYPE_CODE, string AREA_TYPE)
        {
            InitializeComponent();
            Errors = new ObservableCollection<DataColumnChangeEventArgs>();

            STORAGE_AREA_TYPE = AREA_TYPE;

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;
            
            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                                   ( (QueryWhere)=>
                                      {
                                          QueryWhere = string.Format("{0} AND {1}{2}",
                                          //wdz alter 2018-01-20
                                          //this.ucSplitPanel.U_GetSplitPropertyWhere(),
                                          " 1=1 ",
                                          //this.teststr,
                                          string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere,
                                          this.STORAGE_AREA_TYPE == string.Empty ? string.Empty : string.Format(" AND V_STORAGE_LIST.AREA_TYPE = '{0}' ", this.STORAGE_AREA_TYPE));

                                          this.StorageListBind(QueryWhere);
                                      }
                                    );
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //输送位置
            this.InitManagePosotion();
            //属性控件
            this.InitSplitPropertyPanel();
            //查询控件
            this.InitQueryControl();

        }


        private void Register_DataTable_Event()
        {
            //当输入列值改变后验证数据是否合法
            foreach (TabItem tabItem in this.ucStorageGroup.tabSplitProperty.Items)
            {
                if (tabItem.HasContent)
                {
                    DataTable tableSource = (tabItem.Content as UC.ucCommonDataGrid).U_DataSource.Table;

                    tableSource.ColumnChanged -= new DataColumnChangeEventHandler(table_ColumnChanged);
                    tableSource.ColumnChanged += new DataColumnChangeEventHandler(table_ColumnChanged);
                }
            }
        }


        /// <summary>
        /// 表单数据校验
        /// </summary>
        private void table_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "MANAGE_LIST_QUANTITY":
                    bResult = (Convert.ToDecimal(e.ProposedValue) <= Convert.ToDecimal(e.Row["STORAGE_LIST_QUANTITY"]));

                    sResult = string.Format("出库数量不能大于库存数量!");
                    e.Row.ClearErrors();
                    if (bResult)
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, sResult);
                        Errors.Add(e);
                       
                    }
                    break;
            }


        }

        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }


        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = "V_STORAGE_LIST";
            this.ucQuery.U_InitControl();
        }

        /// <summary>
        /// 初始化属性面板
        /// </summary>
        private void InitSplitPropertyPanel()
        {
            //wdz comment 2018-01-20
            //this.ucSplitPanel.U_SplitGroupColumn = "GOODS_TYPE_ID";
            //this.ucSplitPanel.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            //this.ucSplitPanel.U_SplitPropertyColumn = "GOODS_PROPERTY";
            //this.ucSplitPanel.U_SplitPropertyType = "GOODS_TYPE";
            //this.ucSplitPanel.U_InitControl();
        }

        private void StorageListBind(string QueryWhere)
        {
            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStorageGroup.U_Where = string.Format(" cell_status <> 'Pallet' and cell_id not in ( select start_cell_id from manage_main) and {0} ",
                                                            QueryWhere);

            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = true;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();

            Register_DataTable_Event();
        }


        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                    case "btnspselect":
                        this.spselect();
                        break;
                        
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = true;
            bool boolSingleResult = true;
            string strResult = string.Empty;
            string sSingleReslut = string.Empty;

            try
            {
                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                {
                    return;
                }

                //获得选中记录
                List<DataRowView> listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();

                //校验是否选中记录
                if (listDataRowView.Count == 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                    return;
                }

                //xcjt add 2017-04-12  
                //下架页面选中一条数据后将同托盘条码的条目都自动选中
                var checkedBarcode = (from r in listDataRowView select r["STOCK_BARCODE"]).ToList();
                foreach (DataRowView drv in listDataRowView[0].DataView)
                {
                    if (checkedBarcode.Contains(drv["STOCK_BARCODE"].ToString()))
                    {
                        this.ucStorageGroup.U_CheckRow(drv);
                    }
                }
                //重新获得选中记录
                listDataRowView = this.ucStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList<DataRowView>();


                //xcjt add 2016-12-30
                foreach (DataRowView drv in listDataRowView)
                {
                    if (drv["MANAGE_LIST_QUANTITY"].ToString() == "0" || drv["MANAGE_LIST_QUANTITY"].ToString() == "0.0")
                    {
                        drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                    }
                }

                this.ucStorageGroup.U_EndCurrentEdit();


                //判断数据是否合法
                if (this.ucStorageGroup.U_DataSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }

                var cell_id_group = from v in listDataRowView
                                    group v by v["CELL_ID"].ToString() into a
                                    select a;
                
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //获得所有的选定的条码
                    foreach (var cell_id in cell_id_group)
                    {
                        //获得起始位置
                        //int intStartAearID = aa.Value.AREA_ID;
                        int intStartPositionID = Convert.ToInt32(cell_id.Key);

                        var value_ceLl_id = from v in listDataRowView
                                            where v["CELL_ID"].ToString() == cell_id.Key
                                            select v;


                        listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(value_ceLl_id.Cast<DataRowView>().ToArray(), null);
                        //wdz add 2018-01-28 本地出库涉及到包含的计划信息全部清掉
                        foreach(var mMANAGE_LIST in listMANAGE_LIST.Where(r=>r.PLAN_LIST_ID!=0))
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Task, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("MANAGE_OUT.CreateTask():执行本地出库任务已清除库存的计划信息_容器条码[{0}]_计划单ID[{1}] ", value_ceLl_id.First()["STOCK_BARCODE"].ToString(), mMANAGE_LIST.PLAN_LIST_ID));
                            mMANAGE_LIST.PLAN_LIST_ID = 0;
                        }

                        Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.CELL_MODEL = value_ceLl_id.First()["CELL_MODEL"].ToString();
                        mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                        mMANAGE_MAIN.MANAGE_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];
                        mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                        mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                        mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                        mMANAGE_MAIN.PLAN_ID = 0;
                        mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                        mMANAGE_MAIN.START_CELL_ID = intStartPositionID;
                        mMANAGE_MAIN.STOCK_BARCODE = value_ceLl_id.First()["STOCK_BARCODE"].ToString();

                        boolSingleResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                "ManageCreate",
                                                new object[] { mMANAGE_MAIN,
                                                            listMANAGE_LIST,
                                                            true, 
                                                            this.ucManagePosition.U_AutoDownloadControlTask, 
                                                            this.ucManagePosition.U_AutoCompleteTask 
                                                        },
                                                out sSingleReslut);

                        if (!boolSingleResult)
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达失败 " + sSingleReslut + "\n";
                            break;
                        }
                        else
                        {
                            strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达成功 " + "\n";
                            boolResult = true;
                            continue;
                        }
                    }

                    MainApp._MessageDialog.ShowResult(boolResult, strResult);

                    //刷新
                    this.Refresh();
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 获得托盘集合列表
        /// </summary>
        private IDictionary<string, Model.WH_CELL> GetPalletKeyValuePair(DataRowView[] listDataRowView)
        {
            IDictionary<string, Model.WH_CELL> dicStack = new Dictionary<string, Model.WH_CELL>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack = rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (mWH_CELL != null && !dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, mWH_CELL);
                    }
                }
            }
            return dicStack;
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            this.ucManagePosition.U_Update();

            this.ucStorageGroup.U_InitControl();
        }


        private void spselect()
        {
            DataTable dt = MainApp._I_BaseService.GetList("select distinct stock_barcode from V_STORAGE_LIST t where goods_code !='emptyBox' and stock_barcode not in (select stock_barcode from manage_main )");
            string str = string.Empty;
            for(int r = 0; r < dt.Rows.Count; r++)
            {
              str+=string.Format("'{0}',",dt.Rows[r][0].ToString());
            }

            string resultstr = string.Format("stock_barcode in ({0})", str.Substring(0, str.Length - 1));
            this.teststr = resultstr;
        }

    }
}
