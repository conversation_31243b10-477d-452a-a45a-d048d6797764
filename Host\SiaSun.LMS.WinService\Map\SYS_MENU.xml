﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_MENU" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_MENU" type="SiaSun.LMS.Model.SYS_MENU, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_MENU">
			<result property="MENU_ID" column="menu_id" />
			<result property="MENU_PARENT_ID" column="menu_parent_id" />
			<result property="MENU_NAME" column="menu_name" />
      <result property="MENU_CLASS" column="menu_class" />
			<result property="MENU_PARAMETER" column="menu_parameter" />
      <result property="MENU_PARAMETER_SL" column="menu_parameter_sl" />
			<result property="MENU_SYSFLAG" column="menu_sysflag" />
			<result property="MENU_SELECTEDFLAG" column="menu_selectedflag" />
			<result property="MENU_IMAGE" column="menu_image" />
      <result property="MENU_IMAGE_SL" column="menu_image_sl" />
			<result property="MENU_CHILDNODEFLAG" column="menu_childnodeflag" />
			<result property="MENU_ORDER" column="menu_order" />
			<result property="MENU_REMARK" column="menu_remark" />
			<result property="MENU_GROUP" column="menu_group" />
			<result property="MENU_DEVELOPFLAG" column="menu_developflag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_MENU_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        menu_id,
        menu_parent_id,
        menu_name,
        menu_class,
        menu_parameter,
        menu_parameter_sl,
        menu_sysflag,
        menu_selectedflag,
        menu_image,
        menu_image_sl,
        menu_childnodeflag,
        menu_order,
        menu_remark,
        menu_group,
        menu_developflag
        From SYS_MENU
      </select>
		
		<select id="SYS_MENU_SELECT_BY_ID" parameterClass="int" extends = "SYS_MENU_SELECT" resultMap="SelectResult">		
			<dynamic prepend="WHERE">
				<isParameterPresent>
					menu_id=#MENU_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="SYS_MENU_SELECT_BY_MENU_PARAMETER" parameterClass="int" extends = "SYS_MENU_SELECT" resultMap="SelectResult">     
        where  MENU_SELECTEDFLAG = '1' ORDER BY MENU_ORDER
    </select>

    <select id="SYS_MENU_SELECT_BY_ROLE_ID" parameterClass="int" resultMap="SelectResult" extends="SYS_MENU_SELECT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          (MENU_ID in (select RELATION_ID2 from V_SYS_RELATION where RELATION_CODE='SYS_ROLE-SYS_MENU' and RELATION_ID1=#RELATION_ID1#) or nvl(MENU_PARAMETER,'-') = '-') and MENU_SELECTEDFLAG='1'  order by MENU_ORDER
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="SYS_MENU_SELECT_NOTBY_ROLE_ID" parameterClass="int" resultMap="SelectResult" extends="SYS_MENU_SELECT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          MENU_ID not in (select RELATION_ID2 from V_SYS_RELATION where RELATION_CODE='SYS_ROLE-SYS_MENU' and RELATION_ID1=#ROLE_ID#) and ISNULL(MENU_PARAMETER,'-') != '-'
          AND MENU_PARENT_ID NOT IN(SELECT MENU_ID FROM SYS_MENU WHERE MENU_NAME LIKE '%开发%') AND menu_selectedflag='1'
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="SYS_MENU_INSERT" parameterClass="SYS_MENU">
      Insert Into SYS_MENU (
      menu_id,
      menu_parent_id,
      menu_name,
      menu_class,
      menu_parameter,
      menu_parameter_sl,
      menu_toolbuttonflag,
      menu_sysflag,
      menu_image,
      menu_image_sl,
      menu_childnodeflag,
      menu_order,
      menu_remark,
      menu_group,
      menu_developflag
      )Values(
      #MENU_ID#,
      #MENU_PARENT_ID#,
      #MENU_NAME#,
      #MENU_CLASS#,
      #MENU_PARAMETER#,
      #MENU_PARAMETER_SL#,
      #MENU_SYSFLAG#,
      #MENU_SELECTEDFLAG#,
      #MENU_IMAGE#,
      #MENU_IMAGE_SL#,
      #MENU_CHILDNODEFLAG#,
      #MENU_ORDER#,
      #MENU_REMARK#,
      #MENU_GROUP#,
      #MENU_DEVELOPFLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="MENU_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_MENU_UPDATE" parameterClass="SYS_MENU">
      Update SYS_MENU Set
      menu_id=#MENU_ID#,
      menu_parent_id=#MENU_PARENT_ID#,
      menu_name=#MENU_NAME#,
      menu_class=#MENU_CLASS#,
      menu_parameter=#MENU_PARAMETER#,
      menu_parameter_sl=#MENU_PARAMETER_SL#,
      menu_sysflag=#MENU_SYSFLAG#,
      menu_selectedflag=#MENU_SELECTEDFLAG#,
      menu_image=#MENU_IMAGE#,
      menu_image_sl=#MENU_IMAGE_SL#,
      menu_childnodeflag=#MENU_CHILDNODEFLAG#,
      menu_order=#MENU_ORDER#,
      menu_remark=#MENU_REMARK#,
      menu_group=#MENU_GROUP#,
      menu_developflag=#MENU_DEVELOPFLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					menu_id=#MENU_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_MENU_DELETE" parameterClass="int">
			Delete From SYS_MENU
			<dynamic prepend="WHERE">
				<isParameterPresent>
					menu_id=#MENU_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>