﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 调用WCF接口下发Control任务
    /// wdz add 2018-03-01
    /// </summary>
    public class SendInputTaskByID : InterfaceBase
    {
        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(int controlId, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                result = this._S_WESJsonService.SendInputTaskByID(controlId, out message);

                if(result && !message.Contains("true"))
                {
                    result = false;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("程序运行中发生异常 {0}", ex.Message);
                throw;
            }
            finally
            {
                if(!result)
                {
                    this.CreateSysLog(Enum.LogThread.Interface, "System", Enum.LOG_LEVEL.Error, string.Format("handleResultReturnFromWCS.NotifyMethod:调用wcs服务写Control任务失败_{0}", message));
                }
            }

            return result;
        }

    }
}
