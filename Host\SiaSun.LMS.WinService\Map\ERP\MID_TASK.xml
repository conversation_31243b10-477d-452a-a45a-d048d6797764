﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="MID_TASK" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="MID_TASK" type="SiaSun.LMS.Model.MID_TASK, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="MID_TASK">
      <result property="FID" column="fid" />
      <result property="TASK_NO" column="task_no" />
      <result property="IO_FLAG" column="io_flag" />
      <!--<result property="GOODS_ID" column="goods_id" />-->
      <result property="GOODS_CODE" column="goods_code" />
      <!--<result property="GOODS_NAME" column="goods_name" />-->
      <result property="GOODS_PROPERTY" column="goods_property" />
      <result property="PLAN_QTY" column="plan_qty" />
      <result property="FINISH_QTY" column="finish_qty" />
      <result property="FINISH_FLAG" column="finish_flag" />
      <result property="ERROR_INFO" column="error_info" />
    </resultMap>
  </resultMaps>

  <statements>
    <select id="MID_TASK_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      fid,
      task_no,
      io_flag,
      <!--goods_id,-->
      goods_code,
      <!--goods_name,-->
      goods_property,
      plan_qty,
      finish_qty,
      finish_flag,
      error_info
      From MID_TASK
    </select>
    
    <select id="MID_TASK_SELECT_BY_TASK_NO" parameterClass="string" extends = "MID_TASK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          task_no=#TASK_NO#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MID_TASK_SELECT_BY_FINISH_FLAG" parameterClass="string" extends = "MID_TASK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          finish_flag=#FINISH_FLAG#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MID_TASK_SELECT_BY_FID" parameterClass="string" extends = "MID_TASK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          fid=#FID#
        </isParameterPresent>
      </dynamic>
    </select>

    <insert id="MID_TASK_INSERT" parameterClass="MID_TASK">
      Insert Into MID_TASK (
      task_no,
      io_flag,
      <!--goods_id,-->
      goods_code,
      <!--goods_name,-->
      goods_property,
      plan_qty,
      finish_qty,
      finish_flag,
      error_info
      )Values(
      #TASK_NO#,
      #IO_FLAG#,
      <!--#GOODS_ID#,-->
      #GOODS_CODE#,
      <!--#GOODS_NAME#,-->
      #GOODS_PROPERTY#,
      #PLAN_QTY#,
      #FINISH_QTY#,
      #FINISH_FLAG#,
      #ERROR_INFO#
      )
      <selectKey  resultClass="int" type="post" property="FID">
        select @@IDENTITY as value
      </selectKey>
    </insert>
    
    <update id="MID_TASK_UPDATE" parameterClass="MID_TASK">
      Update MID_TASK Set
      task_no=#TASK_NO#,
      io_flag=#IO_FLAG#,
      <!--goods_id=#GOODS_ID#,-->
      goods_code=#GOODS_CODE#,
      <!--goods_name=#GOODS_NAME#,-->
      goods_property=#GOODS_PROPERTY#,
      plan_qty=#PLAN_QTY#,
      finish_qty=#FINISH_QTY#,
      finish_flag=#FINISH_FLAG#,
      error_info=#ERROR_INFO#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          fid=#FID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="MID_TASK_DELETE" parameterClass="int">
      Delete From MID_TASK
      <dynamic prepend="WHERE">
        <isParameterPresent>
          fid=#FID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>