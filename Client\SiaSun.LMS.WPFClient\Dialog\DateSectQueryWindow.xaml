﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.Dialog.DateSectQueryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="DateBetweenQueryWindow" Height="300" Width="341" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        
        <Grid Grid.Row="0">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <Border Name="panelDateTime" Grid.Column="0" MinWidth="140">
                <WrapPanel Name="panelDateSect" VerticalAlignment="Center">
                    <CheckBox Name="chkboxDate" IsChecked="True" VerticalAlignment="Center" Margin="5,5,0,5"></CheckBox>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="从" VerticalAlignment="Center" Margin="5,5,1,5"></TextBlock>
                        <DatePicker Name="dtpStart" Margin="0,5,0,5" Width="90"></DatePicker>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="至" VerticalAlignment="Center" Margin="1,5,1,5"></TextBlock>
                        <DatePicker Name="dtpEnd" Margin="0,5,0,5" Width="90"></DatePicker>
                    </StackPanel>
                </WrapPanel>
            </Border>
            <uc:ucQuickQuery x:Name="ucQuery" Grid.Column="1" ></uc:ucQuickQuery>
        </Grid>

        <GroupBox Name="grpbHeader"  Grid.Row="1" Header="查询结果" Tag=" {0}-查询结果">
        <uc:ucCommonDataGrid x:Name="gridQuery"></uc:ucCommonDataGrid>
        </GroupBox>        
     </Grid>    
</ad:DocumentContent>
