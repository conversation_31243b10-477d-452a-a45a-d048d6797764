﻿using System;
using System.Collections.Generic;
using System.Linq;
using log4net;
using System.Diagnostics;
using System.Threading;
using System.Reflection;
using System.Configuration;

[assembly: log4net.Config.XmlConfigurator(ConfigFile = "Log4Net.config", Watch = true)]
namespace SiaSun.LMS.MonitorMemory
{
    class Program
    {
        public static ILog _log = log4net.LogManager.GetLogger(MethodBase.GetCurrentMethod().DeclaringType);

        static void Main(string[] args)
        {
            try
            {
                AppSettingsReader reader = new AppSettingsReader();
                string SERVICE_NAME = reader.GetValue("ServiceName", typeof(string)).ToString().TrimEnd();
                string PROCESS_NAME = reader.GetValue("ProcessName", typeof(string)).ToString().TrimEnd();
                int MEMORY_THRESHOLD = (int)reader.GetValue("MemoryThreshold", typeof(int));
                int RESTART_HOUR = (int)reader.GetValue("RestartHour", typeof(int));
                int POLL_INTERVAL = (int)reader.GetValue("PollInterval", typeof(int));

                LogAndPrint(string.Format("时间[{0}]_开始监控_服务名[{1}]_进程名[{2}]_内存阈值[{3}M]_重启时段[{4}时]", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), SERVICE_NAME, PROCESS_NAME, MEMORY_THRESHOLD, RESTART_HOUR));

                while (true)
                {
                    Process[] processArray = Process.GetProcessesByName(PROCESS_NAME);
                    if (processArray != null && processArray.Count() > 0)
                    {
                        PerformanceCounter pfPrivateWorkingSet = new PerformanceCounter("Process", "Working Set - Private", PROCESS_NAME);
                        double memorySize = pfPrivateWorkingSet.NextValue() / 1048576;

                        LogAndPrint(string.Format("时间[{0}]_进程[{1}]_私有工作集大小[{2}M]", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), PROCESS_NAME, memorySize));

                        if (memorySize > MEMORY_THRESHOLD && DateTime.Now.Hour == RESTART_HOUR)
                        {
                            RunDosProcess(new List<string>() { "net stop " + SERVICE_NAME });
                            LogAndPrint(string.Format("时间[{0}]_关闭服务_服务名[{1}]", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), SERVICE_NAME));

                            Thread.Sleep(60000);

                            RunDosProcess(new List<string>() { "net start " + SERVICE_NAME });
                            LogAndPrint(string.Format("时间[{0}]_启动服务_服务名[{1}]", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), SERVICE_NAME));
                        }
                    }
                    else
                    {
                        LogAndPrint(string.Format("时间[{0}]_进程未运行_进程名[{1}]", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), PROCESS_NAME));
                    }

                    Thread.Sleep(POLL_INTERVAL);
                }
            }
            catch(Exception ex)
            {
                LogAndPrint(string.Format("时间[{0}]_监控意外关闭_发生异常[{1}]", DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss"), ex.Message));
            }
        }

        /// <summary>
        /// 记录日志和打印输出
        /// </summary>
        /// <param name="message"></param>
        static void LogAndPrint(string message)
        {
            Console.WriteLine(message);
            _log.Debug(message);
        }

        /// <summary>
        /// 运行cmd命令
        /// </summary>
        /// <param name="commandList"></param>
        /// <returns></returns>
        static string RunDosProcess(List<string> commandList)
        {
            string result = string.Empty;

            try
            {
                Process process = new Process();
                process.StartInfo.FileName = "cmd.exe";

                process.StartInfo.UseShellExecute = false;  //不使用系统外壳程序启动进程
                process.StartInfo.CreateNoWindow = false;  //不显示dos程序窗口

                //重新定向标准输入，输入，错误输出
                process.StartInfo.RedirectStandardInput = true;
                process.StartInfo.RedirectStandardOutput = true;
                process.StartInfo.RedirectStandardError = true;

                process.Start();

                foreach (string command in commandList)
                {
                    process.StandardInput.WriteLine(command);
                }
                process.StandardInput.WriteLine("exit");

                //result = process.StandardOutput.ReadToEnd(); //获取结果 

                //process.WaitForExit();  //等待命令结束
                process.Close();  //进程结束
            }
            catch (Exception ex)
            {
                //log
                result = "error";
            }
            return result;
        }

    }
}
