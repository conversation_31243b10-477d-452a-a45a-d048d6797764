﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using SiaSun.LMS.WPFClient.MVVM.Message;
using SiaSun.LMS.WPFClient.PICK.View;


namespace SiaSun.LMS.WPFClient.PICK.Message
{
    public class MainRegister : MessageRegisterBase
    {
        public override void Register()
        {
            RegisterMsg("ShowBox", new Action<string>(s => MessageBox.Show(s)));

            RegisterMsg<ConfirmMsgArgs>("ShowConfirmBox", a =>
            {
                if (MessageBox.Show(a.Content, a.Title, MessageBoxButton.YesNo) == MessageBoxResult.Yes)
                    a.Result = true;
                else
                    a.Result = false;
            });

            RegisterMsg<GoodsCheckArgs>("ShowGoodsCheckView", a =>
            {
                View.GoodsImageCheckWindowView win = new GoodsImageCheckWindowView(a.GoodsCode,a.GoodsName,a.ManageId);
                win.Owner = RegInstance as Window;
                ViewModel.PickStationAutoBind2025CheckViewModel.GoodsCheckViewShow = true;
                win.ShowDialog();
                ViewModel.PickStationAutoBind2025CheckViewModel.GoodsCheckViewShow = false;

                a.Result = win.isCheckSuccess;
                //if (win.ShowDialog().Value)
                //{
                //    //
                //    a.Result = true;
                //}
                //else
                //{
                //    a.Result = false;
                //}
            });
        }
    }
}
