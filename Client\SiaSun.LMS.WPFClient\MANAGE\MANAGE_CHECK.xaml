﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_CHECK"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Width="1000"
    Height="650"
    Loaded="DocumentContent_Loaded">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="20" />
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="20" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="20" />
            <RowDefinition Height="200" />
            <RowDefinition Height="*" />
            <RowDefinition Height="20" />
        </Grid.RowDefinitions>

        <GroupBox
            Grid.Row="1"
            Grid.Column="1"
            Margin="1"
            Header="输入盘点物料，下达盘点任务">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="auto" />
                    <RowDefinition Height="auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="2*"/>
                </Grid.ColumnDefinitions>
                <TextBlock Grid.Column="0" Grid.Row="0" Text="盘点物料(用,隔开)：" HorizontalAlignment="Center"/>
                <TextBox
                    x:Name="tbxGoodsCode" Grid.Column="1"  HorizontalAlignment="Left"
                    Grid.Row="0" Width="500" Height="50" Margin="5"/>


                <TextBlock Text="盘点单号：" Grid.Column="0" Grid.Row="1"  HorizontalAlignment="Center"/>
                <TextBox x:Name="tbxPlanCode"  Grid.Column="1" Grid.Row="1" Width="500" Margin="5" Height="30" HorizontalAlignment="Left"/>

                <TextBlock Text="盘点站台：" Grid.Column="0" Grid.Row="2" HorizontalAlignment="Center"/>
                <ComboBox
                    x:Name="cbxStationCode"
                    Grid.Row="2" Grid.Column="1" Margin="5" Height="30" HorizontalAlignment="Left"
                    Width="500" />
                <Button
                        x:Name="btnCreateDownTask"
                        Grid.Row="3" Grid.Column="0" Grid.ColumnSpan="2"
                        Width="120"
                        Height="35"
                        Click="btnCreateDownTask_Click"
                        Content="下达盘点下架任务" HorizontalAlignment="Center"/>
          
               
            </Grid>
        </GroupBox>
        <GroupBox
            Grid.Row="2"
            Grid.Column="1"
            Margin="1"
            Header="开始托盘盘点">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="Auto" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Text="盘点托盘号：" Grid.Column="0" Grid.Row="0" HorizontalAlignment="Right"/>
                <TextBox x:Name="tbxStockBarcode" Width="200" Grid.Row="0" Grid.Column="1" Margin="5" Height="30"/>
                <Button Grid.Row="0" Grid.Column="2" Height="35"
                        x:Name="btnLoadStorage"
                        Width="120"
                        Content="加载库存信息"  Margin="10"
                        Click="btnLoadStorage_Click"/>
                <Button x:Name="btnSaveChange" Content="保存盘点结果" Width="100" Click="btnSaveChange_Click" Grid.Row="0" Grid.Column="3" Height="35"/>

                <uc:ucCommonDataGrid x:Name="gridStorageList" Grid.Row="1" Grid.Column="0" Grid.ColumnSpan="4"/>

            </Grid>
        </GroupBox>

    </Grid>
</ad:DocumentContent>