﻿using System;
using System.Collections.Generic;
using System.Configuration;
using System.Data;
using System.Linq;
using System.Windows;
using System.Net;
using System.Xml;

using SiaSun.LMS.Interface;

namespace SiaSun.LMS.WPFClient
{
    /// <summary>
    /// MainApp.xaml 的交互逻辑
    /// </summary>
    public partial class MainApp : Application
    {
        //本地样式文件路径
        public static string File_FieldDescription_Path = null;
        public static string File_FormStyles_Path = null;

        //本机地址
        public static IPAddress[] _IPAddressList;
        //应用程序路径
        public static string _APP_PATH;
        //服务地址
        public static string _BaseUrl;
        //语言
        public static string _Language;
        //用户角色
        public static SiaSun.LMS.Model.SYS_ROLE _ROLE;
        //用户
        public static SiaSun.LMS.Model.SYS_USER _USER;
        //通用对话框
        public static SiaSun.LMS.WPFClient.Dialog.MessageDialog _MessageDialog;
        //系统参数
        public static Dictionary<string, string> _SYS_PARAMETER;

        #region     ------业务接口定义

        /// <summary>
        /// 基础服务类
        /// </summary>
        public static SiaSun.LMS.Interface.I_BaseService _I_BaseService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_BaseService>(string.Format(_BaseUrl + "/BaseService")); }
        }

        /// <summary>
        /// 系统类
        /// </summary>
        public static SiaSun.LMS.Interface.I_SystemService _I_SystemService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_SystemService>(string.Format(_BaseUrl + "/SystemService")); }
        }

        /// <summary>
        /// 物料类
        /// </summary>
        public static SiaSun.LMS.Interface.I_GoodsService _I_GoodsService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_GoodsService>(string.Format(_BaseUrl + "/GoodsService")); }
        }

        /// <summary>
        /// 状态流程类
        /// </summary>
        public static SiaSun.LMS.Interface.I_FlowService _I_FlowService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_FlowService>(string.Format(_BaseUrl + "/FlowService")); }
        }

        /// <summary>
        /// 计划类
        /// </summary>
        public static SiaSun.LMS.Interface.I_PlanService _I_PlanService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_PlanService>(string.Format(_BaseUrl + "/PlanService")); }
        }

        /// <summary>
        /// 任务类
        /// </summary>
        public static SiaSun.LMS.Interface.I_ManageService _I_ManageService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_ManageService>(string.Format(_BaseUrl + "/ManageService")); }
        }

        /// <summary>
        /// 库存存储类
        /// </summary>
        public static SiaSun.LMS.Interface.I_StorageService _I_StorageService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_StorageService>(string.Format(_BaseUrl + "/StorageService")); }
        }

        /// <summary>
        /// 仓储货位类
        /// </summary>
        public static SiaSun.LMS.Interface.I_CellService _I_CellService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_CellService>(string.Format(_BaseUrl + "/CellService")); }
        }

        /// <summary>
        /// 显示屏类
        /// </summary>
        public static SiaSun.LMS.Interface.I_LEDService _I_LEDService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_LEDService>(string.Format(_BaseUrl + "/LEDService")); }
        }

        /// <summary>
        /// 与WMS接口类
        /// </summary>
        public static SiaSun.LMS.Interface.I_WESJsonService _I_WESJsonService
        {
            get { return SiaSun.LMS.Common.WCFHelper.Create<I_WESJsonService>(string.Format(_BaseUrl + "/WESJsonService")); }
        }
        #endregion

        #region         ------动态执行---EVENT_Execute

        /// <summary>
        /// 动态执行
        /// </summary>
        public static bool EVENT_Execute(string sEvent, out  string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Common.Compiler cp = new SiaSun.LMS.Common.Compiler();

            //-------------------------------加载SiaSun.LMS组件--------------------------------------------//
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.WPFClient.exe", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.Model.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.Interface.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/SiaSun.LMS.Common.dll", _APP_PATH));

            ////-------------------------------加载布局组件--------------------------------------------//
            cp.lsRef.Add(string.Format("{0}/AvalonDock.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/Fluent.dll", _APP_PATH));

            //-------------------------------加载WPF组件--------------------------------------------//
            cp.lsRef.Add("System.Xaml.dll");
            cp.lsRef.Add(string.Format("{0}/@Dll/PresentationCore.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/@Dll/PresentationFramework.dll", _APP_PATH));
            cp.lsRef.Add(string.Format("{0}/@Dll/WindowsBase.dll", _APP_PATH));


            //-------------------------------加添加程序集的引用--------------------------------------------//
            cp.lsUsing.Add("SiaSun.LMS.WPFClient");
            cp.lsUsing.Add("AvalonDock");
            cp.lsUsing.Add("Fluent");

            cp.strCode = sEvent;
            bResult = cp.Excute(out sResult);
            return bResult;
        }

        #endregion

        /// <summary>
        /// 获得样式表中定义的对象
        /// </summary>
        /// <returns></returns>
        public static object GetStyleResource(string Key)
        {
            return MainApp.Current.Resources[Key];
        }

        /// <summary>
        /// 启动项目
        /// </summary>
        protected override void OnStartup(StartupEventArgs e)
        {
            try
            {
                //获得语言
                _Language = SiaSun.LMS.Common.AppSettings.GetValue("Language");
                //获得服务地址
                _BaseUrl = SiaSun.LMS.Common.StringUtil.GetConfig("SiaSunSrvUrl");
                //获得执行程序的路径
                _APP_PATH = AppDomain.CurrentDomain.BaseDirectory;
                //获得IP地址
                _IPAddressList = Dns.GetHostAddresses(Dns.GetHostName());

                //测试时等待服务发布
                //System.Threading.Thread.Sleep(30000);
                //获取系统参数
                _SYS_PARAMETER = _I_SystemService.GetSysParameter();


                //获得文件存储的路径
                if (System.IO.File.Exists(_APP_PATH + string.Format(@"../../@Files\{0}\FieldDescription.xml", _Language)))
                {
                    File_FieldDescription_Path = _APP_PATH + string.Format(@"../../@Files\{0}\FieldDescription.xml", _Language);
                }
                else
                {
                    File_FieldDescription_Path = _APP_PATH + string.Format(@"@Files\{0}\FieldDescription.xml", _Language);
                }
                if (System.IO.File.Exists(_APP_PATH + string.Format(@"../../@Files\{0}\FormStyles.xml", _Language)))
                {
                    File_FormStyles_Path = _APP_PATH + string.Format(@"../../@Files\{0}\FormStyles.xml", _Language);
                }
                else
                {
                    File_FormStyles_Path = _APP_PATH + string.Format(@"@Files\{0}\FormStyles.xml", _Language);
                }

                //初始化实例
                _USER = new SiaSun.LMS.Model.SYS_USER();
                _MessageDialog = new Dialog.MessageDialog();
                base.OnStartup(e);
            }
            catch(Exception ex)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("客户端初始化运行参数时发生异常 {0}",ex.Message));
            }

        }


        public void Application_Startup(object sender, StartupEventArgs e)
        {

            Application currApp = Application.Current;

            currApp.StartupUri = new Uri("Login.xaml", UriKind.RelativeOrAbsolute);

        }


    }
}
