﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.TEMPLATE.GOODS_TEMPLATE"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="GOODS_TEMPLATE" Height="366" Width="510"  Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

            <uc:ucQuickQuery x:Name="ucQueryTemplate" Margin="1,1,1,3" Grid.Row="0" BorderBrush="Black"></uc:ucQuickQuery>
        
        <GroupBox Margin="2,5,2,1"  Header="计划工单-请选中计划工单记录"  Grid.Row="1">
                <uc:ucCommonDataGrid x:Name="gridTemplate" ></uc:ucCommonDataGrid>
        </GroupBox>

        <GridSplitter  Grid.Row="2" Height="2" HorizontalAlignment="Stretch" ></GridSplitter>

        <GroupBox Margin="2,5,2,1"  Header="计划清单"  Grid.Row="3">
            <!--<uc:ucSplitPropertyGridTab x:Name="gridTemplateList" ></uc:ucSplitPropertyGridTab>-->
            <uc:ucCommonDataGrid x:Name="gridTemplateList" ></uc:ucCommonDataGrid>
        </GroupBox>

        <GroupBox Grid.Row="4" Header="操作区-请根据流程点击操作按钮" Margin="1,5,1,1" >
            <StackPanel Name="tbarButton" Orientation="Horizontal"  ButtonBase.Click="tbarButton_Click" >
                <Button Name="btnAdd"  Width="70" Margin="5">新建</Button>
                <Button Name="btnEdit"  Width="70" Margin="5">编辑</Button>
            </StackPanel>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
