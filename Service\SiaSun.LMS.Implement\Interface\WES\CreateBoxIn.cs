﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 组箱入立库
    /// </summary>
    public class CreateBoxIn : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _boxNo = string.Empty;                  //箱号
            private string _boxType = string.Empty;                //箱子类型
            private string _fromPosition = string.Empty;           //起始位置
            private string _uniqueCode = string.Empty;             //唯一码
            private string _interfaceType = string.Empty;          //接口类型
            private string _interfaceSource = string.Empty;        //接口来源
            private string _boxColor = string.Empty;               //箱子颜色

            public List<FirstDetails> firstDetails { get; set; }     //一级明细
           
            private string _boxSource = string.Empty;               //接口作用标识
            private string _kitFlag = string.Empty;               //齐套箱标记
            private string _kitUseArea = string.Empty;               //齐套箱使用位置

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 起始位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 箱子颜色
            /// </summary>
            public string boxColor
            {
                get { return _boxColor; }
                set { _boxColor = value; }
            }

            /// <summary>
            /// 接口作用标识2019-未使用，直接用string.Contains来判断了
            /// </summary>
            public string BoxSource
            {
                get { return _boxSource; }
                set { _boxSource = value; }
            }

            /// <summary>
            /// 齐套箱标记 2020-09-22 14:59:36
            /// </summary>
            public string kitFlag
            {
                get { return _kitFlag; }
                set { _kitFlag = value; }
            }

            /// <summary>
            /// 齐套箱使用位置 2020-09-22 14:59:36
            /// </summary>
            public string kitUseArea
            {
                get { return _kitUseArea; }
                set { _kitUseArea = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _gridNo = string.Empty;    //格子号
            private string _itemCode = string.Empty;   //物料号
            private string _quantity = string.Empty;  //数量
            private string _priorOut = string.Empty;  //退库优先出库（0-默认；1-优先出库）

            private string _taskNo = string.Empty;          //任务单号
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号

            private string _invFollow = string.Empty;           //质量追溯码  2025-06-26新增
            private string _storageDate = string.Empty;           //WMS到货日期  2025-06-26新增


            /// <summary>
            /// 格子号
            /// </summary>
            public string gridNo
            {
                get { return _gridNo; }
                set { _gridNo = value; }
            }
            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
            /// <summary>
            /// 退库优先出库（0-默认；1-优先出库）
            /// </summary>
            public string priorOut
            {
                get { return _priorOut; }
                set { _priorOut = value; }
            }


            /// <summary>
            /// 任务单号 2020-09-22 15:00:51
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// 项目号 2020-09-22 15:00:55
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号 2020-09-22 15:00:58
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 质量追溯码  2025-06-26新增
            /// </summary>
            public string invFollow
            {
                get { return _invFollow; }
                set { _invFollow = value; }
            }

            /// <summary>
            /// WMS到货日期  2025-06-26新增
            /// </summary>
            public string storageDate
            {
                get { return _storageDate; }
                set { _storageDate = value; }
            }
        }
    

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.boxNo) || string.IsNullOrEmpty(taskInfo.boxType) || string.IsNullOrEmpty(taskInfo.fromPosition) || string.IsNullOrEmpty(taskInfo.uniqueCode) || taskInfo.firstDetails.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:入参存在空值");
                    return bResult;
                }

                string validRegexString = string.Empty;
                if (this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex", out validRegexString) &&
                    !Common.RegexValid.IsValidate(taskInfo.boxNo, validRegexString))
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:箱条码验证失败 传入值-{0}", taskInfo.boxNo);
                    return bResult;
                }

                if (!new string[] { "1", "2", "3", "4", "6" }.Contains(taskInfo.boxType))
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:箱类型不在预期范围内 传入值-{0}", taskInfo.boxType);
                    return bResult;
                }

                //2020-09-23 16:48:12 是否越库物料
                bool crossFlag = taskInfo.kitFlag == "1";

                //2020-09-23 15:47:40 添加越库物料不校验箱格关系
                if (!crossFlag)
                {
                    if (taskInfo.firstDetails.GroupBy(r => r.gridNo).Count() != taskInfo.firstDetails.GroupBy(r => r.itemCode).Count())
                    {
                        bResult = false;
                        outJson = string.Format("CreateBoxIn.NotifyMethod:请确保不同物料放置在不同格子中");
                        return bResult;
                    }
                    int maxGridNo = 0;
                    int boxType = 0;
                    if (int.TryParse(taskInfo.firstDetails.Max(r => r.gridNo), out maxGridNo) &&
                        int.TryParse(taskInfo.boxType, out boxType) &&
                        maxGridNo > boxType)
                    {
                        bResult = false;
                        outJson = string.Format("CreateBoxIn.NotifyMethod:格子号有误或者最大格子号大于箱容量");
                        return bResult;
                    }
                }

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(taskInfo.fromPosition);
                if (mWH_CELL_START == null)
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:传入起始位置有误 传入值_{0}", taskInfo.fromPosition);
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelRelateCode(taskInfo.uniqueCode);
                if (mMANAGE_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:传入唯一码已经存在任务 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                var goodsCheck = taskInfo.firstDetails.GroupBy(t => t.itemCode);
                if (goodsCheck.Count() != taskInfo.firstDetails.Count)
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:传入组箱信息中存在同种物料在不同格子 物料种类数-{0} 一级明细数-{1}", goodsCheck.Count(), taskInfo.firstDetails.Count);
                    return bResult;
                }

                //wdz add 2018-01-14 如果任务的起点没有固定条码扫描器 则指定终点到四楼货位申请站台
                bool isAutoSendControl = false;
                bool isSendControlIndependent = false;
                string endCellCode = string.Empty;
                string standardGoodsOutStation = string.Empty;

                if (mWH_CELL_START.CELL_PROPERTY == "NoScaner")
                {
                    System.Data.DataTable dtWaittingTask = this.GetList(string.Format("select 0 from IO_CONTROL where START_DEVICE_CODE ='{0}' and CONTROL_STATUS in ('0','7') ", mWH_CELL_START.CELL_CODE));
                    if (dtWaittingTask != null && dtWaittingTask.Rows.Count > 0)
                    {
                        bResult = false;
                        outJson = string.Format("CreateBoxIn.NotifyMethod:当前输送起点位置存在等待执行任务，请稍后再试 起点位置-{0}", taskInfo.fromPosition);
                        return bResult;
                    }

                    if (!this._S_SystemService.GetSysParameter("StandardGoodsInStation", out standardGoodsOutStation))
                    {
                        bResult = false;
                        outJson = string.Format("EmptyBoxIn.NotifyMethod:未能获取标准件出库站台_Key[StandardGoodsInStation]");
                        return bResult;
                    }
                    if (mWH_CELL_START.CELL_CODE == standardGoodsOutStation)
                    {
                        //判断如果是标准件入库站台入库，则下达控制任务
                        isAutoSendControl = true;
                    }
                    else
                    {
                        bResult = this._S_SystemService.GetSysParameter("MoveToFloor5EndStation", out endCellCode);
                        Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(endCellCode);
                        if (!bResult || mWH_CELL_END == null)
                        {
                            bResult = false;
                            outJson = string.Format("CreateBoxIn.NotifyMethod:起点位置没有固定条码扫描器且系统未找到货位申请站台 起点位置-{0}", taskInfo.fromPosition);
                            return bResult;
                        }

                        isAutoSendControl = false;
                        isSendControlIndependent = true;
                    }
                }
                else if (mWH_CELL_START != null && mWH_CELL_START.CELL_PROPERTY == "Scaner")
                {
                    isAutoSendControl = false;
                    isSendControlIndependent = false;
                }

                //wdz add 2019-02-27 搜索输入JSON字符串 如果发现targetWords(已“-”分隔)则认为是整理上架任务
                string targetWords = string.Empty;
                if (this._S_SystemService.GetSysParameter("ArrangeUpManageTargetWords", out targetWords))
                {
                    bool isArrangeTask = true;
                    foreach (string item in targetWords.Split('-'))
                    {
                        if (!inJson.Contains(item))
                        {
                            isArrangeTask = false;
                            break;
                        }
                    }

                    if (isArrangeTask)
                    {
                        bResult = this.ArrangeMethod(taskInfo, out outJson);
                        return bResult;
                    }
                }

                mMANAGE_MAIN = new Model.MANAGE_MAIN();
                //2020-09-22 15:50:25 越库物料箱型改为齐套箱
                //var targetCellFloor3 = this._P_WH_CELL.GetModelByDeviceCode(this._S_SystemService.GetSysParameter("", ""));///////////////待完善
                //if (targetCellFloor3 == null)
                //{
                //    bResult = false;
                //    outJson = string.Format("CreateBoxIn.NotifyMethod:越库齐套箱指定入三楼但未找到三楼的输送货位");
                //    return bResult;
                //}
                mMANAGE_MAIN.CELL_MODEL = crossFlag ? Enum.CellModel.KitBox.ToString("d") : taskInfo.boxType;
                mMANAGE_MAIN.CROSS_FLAG = taskInfo.kitUseArea;

                mMANAGE_MAIN.END_CELL_ID = 0;
                mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                string manageLevel = string.Empty;
                mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("DefaultTaskLevel", out manageLevel) ? manageLevel : "0";
                mMANAGE_MAIN.MANAGE_OPERATOR = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageIn.ToString();
                mMANAGE_MAIN.PLAN_ID = 0;
                mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                mMANAGE_MAIN.START_CELL_ID = mWH_CELL_START.CELL_ID;
                mMANAGE_MAIN.STOCK_BARCODE = taskInfo.boxNo;
                mMANAGE_MAIN.MANAGE_SOURCE = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mMANAGE_MAIN.MANAGE_RELATE_CODE = taskInfo.uniqueCode;

                List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                foreach (FirstDetails item in taskInfo.firstDetails)
                {
                    //2020-10-22 15:49:29
                    if (crossFlag)
                    {
                        if (string.IsNullOrEmpty(item.taskNo) || string.IsNullOrEmpty(item.projectNo) || string.IsNullOrEmpty(item.wbsNo))
                        {
                            bResult = false;
                            outJson = string.Format("CreateBoxIn.NotifyMethod:任务单号、项目号或者WBS号存在空值");
                            return bResult;
                        }
                    }

                    if (string.IsNullOrEmpty(item.gridNo) || string.IsNullOrEmpty(item.itemCode) || string.IsNullOrEmpty(item.quantity))
                    {
                        bResult = false;
                        outJson = string.Format("CreateBoxIn.NotifyMethod:入参存在空值");
                        return bResult;
                    }

                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(item.itemCode);
                    if (mGOODS_MAIN == null)
                    {
                        bResult = false;
                        outJson = string.Format("CreateBoxIn.NotifyMethod:传入的物料不存在 物料编码_{0}", item.itemCode);
                        return bResult;
                    }

                    double quantity = 0;
                    if (!double.TryParse(item.quantity, out quantity))
                    {
                        bResult = false;
                        outJson = string.Format("CreateBoxIn.NotifyMethod:传入的数量有误 数量值_{0}", item.quantity);
                        return bResult;
                    }

                    Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                    //mMANAGE_LIST.BOX_BARCODE = string.Format("{0}-{1}", taskInfo.boxNo, item.gridNo);
                    mMANAGE_LIST.BOX_BARCODE = crossFlag ? "" : item.gridNo;
                    mMANAGE_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;
                    mMANAGE_LIST.MANAGE_LIST_QUANTITY = Convert.ToDecimal(quantity);
                    mMANAGE_LIST.GOODS_PROPERTY1 = item.priorOut == "1" ? "1" : "0";

                    mMANAGE_LIST.GOODS_PROPERTY5 = item.invFollow;      //质量追溯码 2025-06-26

                    //2020-09-23 16:53:37
                    if (crossFlag)
                    {
                        mMANAGE_LIST.GOODS_PROPERTY6 = item.taskNo;         //任务单号
                        mMANAGE_LIST.GOODS_PROPERTY7 = item.projectNo;      //项目号
                        mMANAGE_LIST.GOODS_PROPERTY8 = item.wbsNo;          //WBS号
                    }

                    lsMANAGE_LIST.Add(mMANAGE_LIST);
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, true, true, false, isAutoSendControl }, out outJson);

                if (bResult && isSendControlIndependent)
                {
                    //下达到四楼货位分配扫描头的不关联管理任务的Control
                    bResult = this._S_ManageService.ControlCreate(3, taskInfo.boxNo, "1", taskInfo.fromPosition, "1", endCellCode, "5", out outJson);
                    if (!bResult)
                    {
                        bool bTemp = true;
                        string strTemp = string.Empty;
                        //下达Control失败后删除任务
                        bTemp = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCancel", new object[] { mMANAGE_MAIN.MANAGE_ID }, out strTemp);
                        //log
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("CreateBoxIn.NotifyMethod():管理任务生成成功_下达非关联管理任务的Control任务失败_{0}_取消管理任务{1} {2}", outJson, bTemp ? "成功" : "失败 ", strTemp));
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("CreateBoxIn.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }


        /// <summary>
        /// wdz add 2019-02-19
        /// 组箱接口被触发后，判断是合箱入库任务后单独处理
        /// </summary>
        private bool ArrangeMethod(InputParaMain inputInfo, out string message)
        {
            bool result = true;
            message = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();

                string defaultPlanOutStation = string.Empty;
                if (!this._S_SystemService.GetSysParameter("DefaultArrangeOutStation", out defaultPlanOutStation))
                {
                    result = false;
                    message = string.Format("默认整理出库站台未配置");
                    return result;
                }
                //替换掉老地址
                inputInfo.fromPosition = inputInfo.fromPosition.Replace("110122", defaultPlanOutStation);

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(inputInfo.fromPosition);
                if (mWH_CELL_START == null)
                {
                    result = false;
                    message = string.Format("传入起始位置有误 传入值_{0}", inputInfo.fromPosition);
                    return result;
                }

                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode("V" + inputInfo.fromPosition);
                if (mSTORAGE_MAIN == null)
                {
                    result = false;
                    message = string.Format("组箱接口被判别为合箱组箱_根据组箱位置[{0}]未找到整理计划下架的库存信息]", inputInfo.fromPosition);
                    return result;
                }

                IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                if (lsSTORAGE_LIST == null || lsSTORAGE_LIST.Count < 1)
                {
                    result = false;
                    message = string.Format("组箱接口被判别为合箱组箱_根据组箱位置[{0}]未找到整理计划下架的库存列表信息_库存ID[{1}]", inputInfo.fromPosition, mSTORAGE_MAIN.STORAGE_ID);
                    return result;
                }

                //将虚拟货位中的库存重新组合到新箱中
                Model.STORAGE_MAIN mSTORAGE_MAIN_EBOX = this._P_STORAGE_MAIN.GetModelStockBarcode(inputInfo.boxNo);
                if (mSTORAGE_MAIN_EBOX != null)
                {
                    //再次组箱如果此箱存在库存，则查看是否为空箱库存 且 整理置空标记为“1”
                    IList<Model.STORAGE_LIST> lsSTORAGE_LIST_EBOX = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_EBOX.STORAGE_ID);
                    if (lsSTORAGE_LIST_EBOX.Count != 1 || lsSTORAGE_LIST_EBOX[0].GOODS_ID != this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID ||
                        lsSTORAGE_LIST_EBOX[0].GOODS_PROPERTY3 != "1")
                    {
                        result = false;
                        message = string.Format("合箱后再次组箱使用的箱子存在库存_箱条码[{0}]_箱类型[{1}]", mSTORAGE_MAIN_EBOX.STOCK_BARCODE, mSTORAGE_MAIN_EBOX.CELL_MODEL);
                        return result;
                    }

                    mSTORAGE_MAIN_EBOX.CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mSTORAGE_MAIN_EBOX.CELL_MODEL = inputInfo.boxType;
                    mSTORAGE_MAIN_EBOX.STOCK_BARCODE = inputInfo.boxNo;
                    this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN_EBOX);

                    this._P_STORAGE_LIST.Delete(lsSTORAGE_LIST_EBOX[0].STORAGE_LIST_ID);
                }
                else
                {
                    mSTORAGE_MAIN_EBOX = new Model.STORAGE_MAIN();
                    mSTORAGE_MAIN_EBOX.CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    mSTORAGE_MAIN_EBOX.CELL_MODEL = inputInfo.boxType;
                    mSTORAGE_MAIN_EBOX.STOCK_BARCODE = inputInfo.boxNo;
                    this._P_STORAGE_MAIN.Add(mSTORAGE_MAIN_EBOX);
                }

                foreach (FirstDetails item in inputInfo.firstDetails)
                {
                    if (string.IsNullOrEmpty(item.gridNo) || string.IsNullOrEmpty(item.itemCode) || string.IsNullOrEmpty(item.quantity))
                    {
                        result = false;
                        message = string.Format("入参存在空值");
                        return result;
                    }

                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(item.itemCode);
                    if (mGOODS_MAIN == null)
                    {
                        result = false;
                        message = string.Format("传入的物料不存在_物料编码[{0}]", item.itemCode);
                        return result;
                    }

                    decimal quantity = 0;
                    if (!decimal.TryParse(item.quantity, out quantity))
                    {
                        result = false;
                        message = string.Format("传入的数量[{0}]有误", item.quantity);
                        return result;
                    }

                    //wdz add 2019-07-26
                    string stationWorkMode = string.Empty;
                    if (!this._S_SystemService.GetSysParameter("WorkingMode-" + mWH_CELL_START.CELL_CODE, out stationWorkMode))
                    {
                        result = false;
                        message = string.Format("未找到整理站台[{0}]的工作模式", mWH_CELL_START.CELL_CODE);
                        return result;
                    }

                    if (stationWorkMode == Enum.ArrangeWorkMode.Arrange.ToString())
                    {
                        //合箱模式下要完全校验每个STORAGE_LIST的物料和数量
                        var matchStorage = lsSTORAGE_LIST.Where(r => r.GOODS_ID == mGOODS_MAIN.GOODS_ID && r.STORAGE_LIST_QUANTITY == quantity);
                        if (matchStorage.Count() < 1)
                        {
                            result = false;
                            message = string.Format("未找到整理工位[{0}]上匹配的库存信息_物料条码[{1}]_数量[{2}]", mWH_CELL_START.CELL_CODE, mGOODS_MAIN.GOODS_CODE, item.quantity);
                            return result;
                        }

                        //2020-06-07
                        //Model.STORAGE_LIST mSTORAGE_LIST = matchStorage.FirstOrDefault();
                        Model.STORAGE_LIST mSTORAGE_LIST = matchStorage.OrderBy(r => r.UPDATE_TIME).FirstOrDefault();
                        mSTORAGE_LIST.BOX_BARCODE = item.gridNo;
                        mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN_EBOX.STORAGE_ID;
                        mSTORAGE_LIST.UPDATE_TIME = Common.StringUtil.GetDateTime();
                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else if (stationWorkMode == Enum.ArrangeWorkMode.ArrangeGoods.ToString())
                    {
                        //decimal storageQty = lsSTORAGE_LIST.Where(r => r.GOODS_ID == mGOODS_MAIN.GOODS_ID).Sum(x => x.STORAGE_LIST_QUANTITY);
                        //if (storageQty < quantity)
                        //{
                        //    result = false;
                        //    message = string.Format("整理工位的物料[{0}]的库存数量[{1}]少于组箱数量[{2}]", mGOODS_MAIN.GOODS_CODE, storageQty, quantity);
                        //    return result;
                        //}

                        if (lsSTORAGE_LIST.Where(r => r.GOODS_ID == mGOODS_MAIN.GOODS_ID && r.STORAGE_LIST_QUANTITY == quantity).Count() == 1)
                        {
                            Model.STORAGE_LIST mSTORAGE_LIST = lsSTORAGE_LIST.Where(r => r.GOODS_ID == mGOODS_MAIN.GOODS_ID).FirstOrDefault();
                            mSTORAGE_LIST.BOX_BARCODE = item.gridNo;
                            mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN_EBOX.STORAGE_ID;
                            mSTORAGE_LIST.UPDATE_TIME = Common.StringUtil.GetDateTime();
                            this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                        }
                        else
                        {
                            Model.STORAGE_LIST mSTORAGE_LIST = lsSTORAGE_LIST.Where(r => r.GOODS_ID == mGOODS_MAIN.GOODS_ID).FirstOrDefault();
                            mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= quantity;
                            this._P_STORAGE_LIST.Update(mSTORAGE_LIST);

                            Model.STORAGE_LIST mSTORAGE_LIST_NEW = new Model.STORAGE_LIST();
                            mSTORAGE_LIST_NEW.BOX_BARCODE = item.gridNo;
                            mSTORAGE_LIST_NEW.ENTRY_TIME = mSTORAGE_LIST.ENTRY_TIME;
                            mSTORAGE_LIST_NEW.GOODS_ID = mSTORAGE_LIST.GOODS_ID;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY1 = mSTORAGE_LIST.GOODS_PROPERTY1;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY2 = mSTORAGE_LIST.GOODS_PROPERTY2;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY3 = mSTORAGE_LIST.GOODS_PROPERTY3;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY4 = mSTORAGE_LIST.GOODS_PROPERTY4;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY5 = mSTORAGE_LIST.GOODS_PROPERTY5;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY6 = mSTORAGE_LIST.GOODS_PROPERTY6;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY7 = mSTORAGE_LIST.GOODS_PROPERTY7;
                            mSTORAGE_LIST_NEW.GOODS_PROPERTY8 = mSTORAGE_LIST.GOODS_PROPERTY8;
                            mSTORAGE_LIST_NEW.STORAGE_ID = mSTORAGE_MAIN_EBOX.STORAGE_ID;
                            mSTORAGE_LIST_NEW.STORAGE_LIST_QUANTITY = quantity;
                            mSTORAGE_LIST_NEW.UPDATE_TIME = Common.StringUtil.GetDateTime();
                            this._P_STORAGE_LIST.Add(mSTORAGE_LIST_NEW);
                        }

                    }
                    else
                    {
                        result = false;
                        message = string.Format("整理站台[{0}]的工作模式有误", mWH_CELL_START.CELL_CODE);
                        return result;
                    }
                }

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("合箱组箱时发生异常_信息[{0}]", ex.Message);
            }
            finally
            {
                if (result)
                {
                    this._P_Base_House.CommitTransaction();
                    this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Information, string.Format("CreateBoxIn.ArrangeMethod():组箱接口被判别为合箱组箱_生成整理上架任务成功_箱条码[{0}]", inputInfo.boxNo));
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                    this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Error, string.Format("CreateBoxIn.ArrangeMethod():组箱接口被判别为合箱组箱_生成整理上架任务失败_箱条码[{0}]_信息[{1}]", inputInfo.boxNo, message));
                }
            }

            return result;
        }

    }
}
