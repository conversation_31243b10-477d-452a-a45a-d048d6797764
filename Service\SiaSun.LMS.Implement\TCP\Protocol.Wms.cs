﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.TCP
{
    public partial class Protocol
    {
        /// <summary>
        /// 01
        /// 料箱到位应答
        /// done
        /// 响应PLC料箱到位
        /// </summary>
        /// <param name="plcNo">PLC编号</param>
        /// <param name="taskNo">任务号</param>
        /// <param name="status"></param>
        /// <param name="deviceNo">设备号</param>
        /// <param name="stockBarcode">箱码</param>
        /// <returns>发送的byte[]报文</returns>
        public byte[] BoxArriveResponse(short plcNo, short taskNo, short status, int deviceNo, string stockBarcode)
        {
            List<byte> bytes = new List<byte>();
            bytes.AddRange(new byte[2] { 0xfc, 0xfc });//起始符 //0 1
            bytes.Add(1);//报文名称 //2
            bytes.Add(1);//报文类型 //3（0表示请求报文，1表示确认回复 ）
            bytes.AddRange(ShortToBytes(plcNo));//PLC编号 //4 5
            bytes.AddRange(ShortToBytes(taskNo));//任务号 //6 7
            bytes.AddRange(ShortToBytes(status));//状态 //8 9
            bytes.AddRange(IntToBytes(deviceNo));//设备号 //10 11 12 13
            bytes.AddRange(StringToBytes(stockBarcode));//条码 //14 15 16 17 18 19 20 21 22 23
            bytes.AddRange(new byte[2] { 0x30, 0x30 });//备用 //24 25
            return Check(bytes);
        }

        /// <summary>
        /// 02
        /// 料箱离开请求
        /// done
        /// 向PLC发送箱子离开的命令
        /// </summary>
        /// <param name="plcNo">PLC编号</param>
        /// <param name="taskNo">任务号</param>
        /// <param name="command">命令(1：拣选完离开；2备用； )</param>
        /// <param name="deviceNo">设备号</param>
        /// <param name="stockBarcode">箱码</param>
        /// <returns>发送的byte[]报文</returns>
        public byte[] BoxLeaveRequest(short plcNo, short taskNo, short command, int deviceNo, string stockBarcode)
        {
            List<byte> bytes = new List<byte>();
            bytes.AddRange(new byte[2] { 0xfc, 0xfc });//起始符 //0 1
            bytes.Add(2);//报文名称 System.Text.Encoding.ASCII.GetBytes(string.Format("2"));  //2
            //bytes.AddRange(System.Text.Encoding.ASCII.GetBytes(string.Format("2")));//报文名称
            bytes.Add(0);//报文类型 //3（0表示请求报文，1表示确认回复 ）
            bytes.AddRange(ShortToBytes(plcNo));//PLC编号 //4 5
            bytes.AddRange(ShortToBytes(taskNo));//任务号 //6 7
            bytes.AddRange(ShortToBytes(command));//命令 //8 9 (1：拣选完离开；2备用； )
            bytes.AddRange(IntToBytes(deviceNo));//设备号 //10 11 12 13
            bytes.AddRange(StringToBytes(stockBarcode));//条码 //14 15 16 17 18 19 20 21 22 23
            bytes.AddRange(new byte[2] { 0x30, 0x30 });//备用 //24 25
            return Check(bytes);
        }

        /// <summary>
        /// 03
        /// 指示灯状态请求
        /// done
        /// 向PLC发送亮灯状态
        /// </summary>
        /// <param name="plcNo">PLC编号</param>
        /// <param name="status">灯亮为1 灭为0 [1,1,1,1,1,1]</param>
        /// <param name="deviceNo">设备号</param>
        /// <param name="stockBarcode">箱码</param>
        /// <returns>发送的byte[]报文</returns>
        public byte[] LightsStatusRequest(short plcNo, bool[] status, int deviceNo, string stockBarcode)
        {
            List<byte> bytes = new List<byte>();
            bytes.AddRange(new byte[2] { 0xfc, 0xfc });//起始符   //0 1
            bytes.Add(3);//报文名称  //2
            bytes.Add(0);//报文类型  //3 （0表示请求报文，1表示确认回复 ）
            bytes.AddRange(ShortToBytes(plcNo));//PLC编号 //4 5
            bytes.AddRange(ShortToBytes(0));//备用  //6 7
            bytes.AddRange(BooleanArrayToBytes(status));//开关灯状态 //8 9
            bytes.AddRange(IntToBytes(deviceNo));//设备号 //10 11 12 13
            bytes.AddRange(StringToBytes(stockBarcode));//条码 //14 15 16 17 18 19 20 21 22 23
            bytes.AddRange(new byte[2] { 0x30, 0x30 });//备用  //24 25
            return Check(bytes);
        }
    }
}
