<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>
    
    <Style TargetType="{x:Type TextBox}">
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Background" >
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource WhiteColor}"/>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="{StaticResource TextControlBorderBrush}" />
        <Setter Property="Template" Value="{DynamicResource TextBoxTemplate}" />
    </Style>
    
    <ControlTemplate x:Key="TextBoxTemplate" TargetType="{x:Type TextBox}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="1" />
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over_Border" Storyboard.TargetProperty="Opacity" To="1" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="0" />
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over_Border" Storyboard.TargetProperty="Opacity" To="0" />
            </Storyboard>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="DisabledOn">
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledVisualElement" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="DisabledOff">
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledVisualElement" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Collapsed}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <Grid>
            <Border x:Name="BorderBase" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" />
            <Border x:Name="Over" BorderBrush="{DynamicResource FocusBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" Opacity="0" />
            <Border x:Name="Over_Border" Margin="-1" BorderBrush="{DynamicResource MouseOverHighlightBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3.75" IsHitTestVisible="False" Opacity="0" />
            <ScrollViewer Margin="0" x:Name="PART_ContentHost" Foreground="{DynamicResource OutsideFontColor}" />
            <Border x:Name="DisabledVisualElement" Background="#A5FFFFFF" BorderBrush="#59C0C0C0" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" IsHitTestVisible="False" Visibility="Collapsed" />
            <Border x:Name="ReadOnlyVisualElement" Background="#66FFFFFF" CornerRadius="2.75" Visibility="Collapsed" />
            <Border x:Name="FocusVisualElement" BorderBrush="#FFB1703C" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" IsHitTestVisible="False" Opacity="0" />
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOn}" x:Name="FocusedOn_BeginStoryboard" />
                </Trigger.EnterActions>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsMouseOver" Value="True" />
                    <Condition Property="IsFocused" Value="False" />
                </MultiTrigger.Conditions>
                <MultiTrigger.ExitActions>
                    <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                </MultiTrigger.ExitActions>
                <MultiTrigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </MultiTrigger.EnterActions>
            </MultiTrigger>
            <Trigger Property="IsEnabled" Value="False">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource DisabledOn}" x:Name="DisabledOn_BeginStoryboard" />
                </Trigger.EnterActions>

                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource DisabledOff}"  />
                </Trigger.EnterActions>
                <Setter Property="Foreground" Value="#FF000000" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style TargetType="{x:Type PasswordBox}">
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Background" >
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource WhiteColor}"/>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="{StaticResource TextControlBorderBrush}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type PasswordBox}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="1" />
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over_Border" Storyboard.TargetProperty="Opacity" To="1" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="0" />
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over_Border" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="DisabledOn">
                            <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledVisualElement" Storyboard.TargetProperty="(UIElement.Visibility)">
                                <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Border x:Name="BorderBase" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" />
                        <Border x:Name="Over" BorderBrush="{DynamicResource FocusBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" Opacity="0" />
                        <Border x:Name="Over_Border" Margin="-1" BorderBrush="{DynamicResource MouseOverHighlightBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3.75" IsHitTestVisible="False" Opacity="0" />
                        <ScrollViewer Margin="0" x:Name="PART_ContentHost" Foreground="{DynamicResource OutsideFontColor}" />
                        <Border x:Name="DisabledVisualElement" Background="#A5FFFFFF" BorderBrush="#59C0C0C0" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" IsHitTestVisible="False" Visibility="Collapsed" />
                        <Border x:Name="ReadOnlyVisualElement" Background="#66FFFFFF" CornerRadius="2.75" Visibility="Collapsed" />
                        <Border x:Name="FocusVisualElement" BorderBrush="#FFB1703C" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" IsHitTestVisible="False" Opacity="0" />

                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOn}" x:Name="FocusedOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsFocused" Value="False" />
                            </MultiTrigger.Conditions>
                            <MultiTrigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </MultiTrigger.ExitActions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </MultiTrigger.EnterActions>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource DisabledOn}" x:Name="DisabledOn_BeginStoryboard" />
                            </Trigger.EnterActions>

                            <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style TargetType="{x:Type RichTextBox}">
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Background" >
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource WhiteColor}"/>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush" Value="{StaticResource TextControlBorderBrush}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RichTextBox}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="1" />
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over_Border" Storyboard.TargetProperty="Opacity" To="1" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="0" />
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over_Border" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="DisabledOn">
                            <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledVisualElement" Storyboard.TargetProperty="(UIElement.Visibility)">
                                <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Border x:Name="BorderBase" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" />
                        <Border x:Name="Over" BorderBrush="{DynamicResource FocusBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" Opacity="0" />
                        <Border x:Name="Over_Border" Margin="-1" BorderBrush="{DynamicResource MouseOverHighlightBrush}" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="3.75" IsHitTestVisible="False" Opacity="0" />
                        <ScrollViewer Margin="0" x:Name="PART_ContentHost" Foreground="{DynamicResource OutsideFontColor}" />
                        <Border x:Name="DisabledVisualElement" Background="#A5FFFFFF" BorderBrush="#59C0C0C0" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" IsHitTestVisible="False" Visibility="Collapsed" />
                        <Border x:Name="ReadOnlyVisualElement" Background="#66FFFFFF" CornerRadius="2.75" Visibility="Collapsed" />
                        <Border x:Name="FocusVisualElement" BorderBrush="#FFB1703C" BorderThickness="{TemplateBinding BorderThickness}" CornerRadius="2.75" IsHitTestVisible="False" Opacity="0" />

                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOn}" x:Name="FocusedOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <MultiTrigger>
                            <MultiTrigger.Conditions>
                                <Condition Property="IsMouseOver" Value="True" />
                                <Condition Property="IsFocused" Value="False" />
                            </MultiTrigger.Conditions>
                            <MultiTrigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </MultiTrigger.ExitActions>
                            <MultiTrigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </MultiTrigger.EnterActions>
                        </MultiTrigger>
                        <Trigger Property="IsEnabled" Value="False">
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource DisabledOn}" x:Name="DisabledOn_BeginStoryboard" />
                            </Trigger.EnterActions>

                            <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>