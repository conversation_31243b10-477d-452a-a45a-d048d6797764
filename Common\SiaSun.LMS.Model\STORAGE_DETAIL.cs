﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// STORAGE_DETAIL 
	/// </summary>
    [Serializable]
    [DataContract]
	public class STORAGE_DETAIL
	{
		public STORAGE_DETAIL()
		{
			
		}
		
		private int _storage_detail_id;
		private int _storage_list_id;
		private string _box_barcode;
		private string _goods_barcode;
		private string _storage_detail_remark;
		
		///<sumary>
		/// 库存明细编号
        ///</sumary>
        [DataMember]
		public int STORAGE_DETAIL_ID
		{
			get{return _storage_detail_id;}
			set{_storage_detail_id = value;}
		}
		///<sumary>
		/// 库存列表编号
        ///</sumary>
        [DataMember]
		public int STORAGE_LIST_ID
		{
			get{return _storage_list_id;}
			set{_storage_list_id = value;}
		}
		///<sumary>
		/// 箱条条码
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 物料条码
        ///</sumary>
        [DataMember]
		public string GOODS_BARCODE
		{
			get{return _goods_barcode;}
			set{_goods_barcode = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string STORAGE_DETAIL_REMARK
		{
			get{return _storage_detail_remark;}
			set{_storage_detail_remark = value;}
		}
	}
}
