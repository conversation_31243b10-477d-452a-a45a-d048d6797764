﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="MANAGE_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="MANAGE_LIST" type="SiaSun.LMS.Model.MANAGE_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="MANAGE_LIST">
			<result property="MANAGE_LIST_ID" column="manage_list_id" />
			<result property="STORAGE_LIST_ID" column="storage_list_id" />
			<result property="PLAN_LIST_ID" column="plan_list_id" />
			<result property="MANAGE_ID" column="manage_id" />
			<result property="GOODS_ID" column="goods_id" />
			<result property="MANAGE_LIST_QUANTITY" column="manage_list_quantity" />
			<result property="MANAGE_LIST_REMARK" column="manage_list_remark" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_PROPERTY1" column="goods_property1" />
			<result property="GOODS_PROPERTY2" column="goods_property2" />
			<result property="GOODS_PROPERTY3" column="goods_property3" />
			<result property="GOODS_PROPERTY4" column="goods_property4" />
			<result property="GOODS_PROPERTY5" column="goods_property5" />
			<result property="GOODS_PROPERTY6" column="goods_property6" />
			<result property="GOODS_PROPERTY7" column="goods_property7" />
			<result property="GOODS_PROPERTY8" column="goods_property8" />
      <result property="STORAGE_LOCK_ID" column="storage_lock_id" />
      <result property="DETAIL_FLAG" column="detail_flag" />
      <result property="ORIGIN_ENTRY_TIME" column="origin_entry_time" />

    </resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="MANAGE_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        manage_list_id,
        storage_list_id,
        plan_list_id,
        manage_id,
        goods_id,
        manage_list_quantity,
        manage_list_remark,
        box_barcode,
        goods_property1,
        goods_property2,
        goods_property3,
        goods_property4,
        goods_property5,
        goods_property6,
        goods_property7,
        goods_property8,
        storage_lock_id,
        detail_flag,
        origin_entry_time
        From MANAGE_LIST
      </select>
		
		<select id="MANAGE_LIST_SELECT_BY_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_list_id=#MANAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="MANAGE_LIST_SELECT_BY_MANAGE_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_id=#MANAGE_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_LIST_SELECT_BY_STORAGE_LOCK_ID" parameterClass="int" extends = "MANAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="MANAGE_LIST_INSERT" parameterClass="MANAGE_LIST">
      Insert Into MANAGE_LIST (
      manage_list_id,
      storage_list_id,
      plan_list_id,
      manage_id,
      goods_id,
      manage_list_quantity,
      manage_list_remark,
      box_barcode,
      goods_property1,
      goods_property2,
      goods_property3,
      goods_property4,
      goods_property5,
      goods_property6,
      goods_property7,
      goods_property8,
      storage_lock_id,
      detail_flag,
      origin_entry_time
      )Values(
      #MANAGE_LIST_ID#,
      #STORAGE_LIST_ID#,
      #PLAN_LIST_ID#,
      #MANAGE_ID#,
      #GOODS_ID#,
      #MANAGE_LIST_QUANTITY#,
      #MANAGE_LIST_REMARK#,
      #BOX_BARCODE#,
      #GOODS_PROPERTY1#,
      #GOODS_PROPERTY2#,
      #GOODS_PROPERTY3#,
      #GOODS_PROPERTY4#,
      #GOODS_PROPERTY5#,
      #GOODS_PROPERTY6#,
      #GOODS_PROPERTY7#,
      #GOODS_PROPERTY8#,
      #STORAGE_LOCK_ID#,
      #DETAIL_FLAG#,
      #ORIGIN_ENTRY_TIME#
      )
      <!--<selectKey  resultClass="int" type="post" property="MANAGE_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="MANAGE_LIST_UPDATE" parameterClass="MANAGE_LIST">
      Update MANAGE_LIST Set
      manage_list_id=#MANAGE_LIST_ID#,
      storage_list_id=#STORAGE_LIST_ID#,
      plan_list_id=#PLAN_LIST_ID#,
      manage_id=#MANAGE_ID#,
      goods_id=#GOODS_ID#,
      manage_list_quantity=#MANAGE_LIST_QUANTITY#,
      manage_list_remark=#MANAGE_LIST_REMARK#,
      box_barcode=#BOX_BARCODE#,
      goods_property1=#GOODS_PROPERTY1#,
      goods_property2=#GOODS_PROPERTY2#,
      goods_property3=#GOODS_PROPERTY3#,
      goods_property4=#GOODS_PROPERTY4#,
      goods_property5=#GOODS_PROPERTY5#,
      goods_property6=#GOODS_PROPERTY6#,
      goods_property7=#GOODS_PROPERTY7#,
      goods_property8=#GOODS_PROPERTY8#,
      storage_lock_id=#STORAGE_LOCK_ID#,
      detail_flag=#DETAIL_FLAG#,
      origin_entry_time=#ORIGIN_ENTRY_TIME#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					manage_list_id=#MANAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="MANAGE_LIST_DELETE" parameterClass="int">
			Delete From MANAGE_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_list_id=#MANAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

    <delete id="MANAGE_LIST_DELETE_MANAGE_ID" parameterClass="int">
      Delete From MANAGE_LIST
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_id=#MANAGE_ID#
        </isParameterPresent>
      </dynamic>
    </delete>
		
	</statements>
</sqlMap>