﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    [Serializable]
    [DataContract]
    public class FIELD_DESCRIPTION
    {
        private string _Column = "列名";
        private string _DbType = "string";
        private string _Header = "列描述";
        private string _ControlType = "TextBox";
        private string _DefaultValue = "";
        private int _ReadOnly = 0;
        private string _Validation = string.Empty;
        private string _DataBind = "";
        private string _Remark = "";
        private int _Order = 1;
        private int _AllowQuery = 0;
        private string _QueryOperation = string.Empty;

        /// <summary>
        /// 列名
        /// </summary>
        [DataMember]
        public string Column
        {
            get { return _Column; }
            set { _Column = value; }
        }

        /// <summary>
        /// 数据类型
        /// </summary>
        [DataMember]
        public string DbType
        {
            get { return _DbType; }
            set { _DbType = value; }
        }
        /// <summary>
        /// 显示名称
        /// </summary>
        [DataMember]
        public string Header
        {
            get { return _Header; }
            set { _Header = value; }
        }
        /// <summary>
        /// 控件类型
        /// </summary>
        [DataMember]
        public string ControlType
        {
            get { return _ControlType; }
            set { _ControlType = value; }
        }
        /// <summary>
        /// 默认值
        /// </summary>
        [DataMember]
        public string DefaultValue
        {
            get { return _DefaultValue; }
            set { _DefaultValue = value; }
        }
        /// <summary>
        /// 是否可读
        /// </summary>
        [DataMember]
        public int ReadOnly
        {
            get { return _ReadOnly; }
            set { _ReadOnly = value; }
        }
        /// <summary>
        /// 验证信息
        /// </summary>
        [DataMember]
        public string Validation
        {
            get { return _Validation; }
            set { _Validation = value; }
        }
        /// <summary>
        /// 数据绑定
        /// </summary>
        [DataMember]
        public string DataBind
        {
            get { return _DataBind; }
            set { _DataBind = value; }
        }
        /// <summary>
        /// 备注
        /// </summary>
        [DataMember]
        public string Remark
        {
            get { return _Remark; }
            set { _Remark = value; }
        }
        /// <summary>
        /// 序号
        /// </summary>
        [DataMember]
        public int Order
        {
            get { return _Order; }
            set { _Order = value; }
        }
        /// <summary>
        /// 是否允许查询
        /// </summary>
        [DataMember]
        public int AllowQuery
        {
            get { return _AllowQuery; }
            set { _AllowQuery = value; }
        }
        /// <summary>
        /// 查询符号
        /// </summary>
        [DataMember]
        public string QueryOperation
        {
            get { return _QueryOperation; }
            set { _QueryOperation = value; }
        }

        public FIELD_DESCRIPTION() {} 
        public FIELD_DESCRIPTION(string _column, string _header, string _dbType, string _controlType, string _defaultValue, int _readOnly, 
                                                        string _validation, string _dataBind, int _order, int _allowQuery)
        {
            this._Column = _column;
            this._Header = _header;
            this._DbType = string.IsNullOrEmpty(_dbType) ? this._DbType : _dbType;
            this._ControlType = string.IsNullOrEmpty(_controlType) ? this._ControlType : _controlType;
            this._DefaultValue = string.IsNullOrEmpty(_defaultValue) ? this._DefaultValue : _defaultValue;
            this._ReadOnly = _readOnly;
            this._Validation = string.IsNullOrEmpty(_validation) ? this._Validation : _validation;
            this._DataBind = string.IsNullOrEmpty(_dataBind) ? this._DataBind : _dataBind;
            this._Order = _order;
            this._AllowQuery = _allowQuery;
        }
    }
}
