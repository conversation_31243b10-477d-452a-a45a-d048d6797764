﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// TECHNICS_ROUTE
	/// </summary>
	public class P_TECHNICS_ROUTE : P_Base_House
	{
		public P_TECHNICS_ROUTE ()
		{
			//
			// TODO: 此处添加TECHNICS_ROUTE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<TECHNICS_ROUTE> GetList()
		{
			return ExecuteQueryForList<TECHNICS_ROUTE>("TECHNICS_ROUTE_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(TECHNICS_ROUTE technics_route)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("TECHNICS_ROUTE");
                technics_route.ROUTE_ID = id;
            }

            return ExecuteInsert("TECHNICS_ROUTE_INSERT",technics_route);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(TECHNICS_ROUTE technics_route)
		{
			return ExecuteUpdate("TECHNICS_ROUTE_UPDATE",technics_route);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public TECHNICS_ROUTE GetModel(System.Int32 ROUTE_ID)
		{
			return ExecuteQueryForObject<TECHNICS_ROUTE>("TECHNICS_ROUTE_SELECT_BY_ID",ROUTE_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 ROUTE_ID)
		{
			return ExecuteDelete("TECHNICS_ROUTE_DELETE",ROUTE_ID);
		}
		

	}
}
