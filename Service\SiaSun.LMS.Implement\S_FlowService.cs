﻿using System;
using System.Collections.Generic;
using System.Text;
using System.ServiceModel;
using System.Data;
using SiaSun.LMS.Model;
using SiaSun.LMS.Common;
using System.Collections;
using System.Linq;
using System.Reflection;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, InstanceContextMode = InstanceContextMode.Single, ConcurrencyMode = ConcurrencyMode.Multiple, MaxItemsInObjectGraph = int.MaxValue)] 
    public class S_FlowService : S_BaseService, SiaSun.LMS.Interface.I_FlowService
    {

        public S_FlowService()
        {
        }


        #region FLOW_PARA

        /// <summary>获取流程参数
        /// 获取流程参数
        /// </summary>
        /// <param name="FLOW_TYPE_CODE">流程类型</param>
        /// <returns></returns>
        public IList<SiaSun.LMS.Model.FLOW_PARA> FlowGetParameters(string FLOW_TYPE_CODE)
        {
            return this._P_FLOW_PARA.GetListFlowTypeCode(FLOW_TYPE_CODE);
        }

        #endregion

        #region FLOW_ACTION

        /// <summary>获取计划动作
        /// 获取计划动作
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <returns></returns>
        public DataTable PlanGetAction(string PLAN_ID)
        {
            DataTable dt = this.GetList(string.Format("SELECT * FROM V_FLOW_PLAN_ACTION WHERE PLAN_ID = {0}", PLAN_ID));

            return dt;
        }

        /// <summary>获取计划动作
        /// 获取计划动作
        /// </summary>
        /// <param name="PLAN_ID">计划编号</param>
        /// <param name="ACTION_DEFAULT">默认动作</param>
        /// <returns></returns>
        public DataTable PlanGetAction(string PLAN_ID, string FLOW_ACTION_DEFAULT)
        {
            DataTable dt = this.GetList(string.Format("SELECT * FROM V_FLOW_PLAN_ACTION WHERE FLOW_ACTION_DEFAULT ='{1}' AND PLAN_ID = {0}", PLAN_ID, FLOW_ACTION_DEFAULT));

            return dt;
        }

        /// <summary>获取任务动作
        /// 获取任务动作
        /// </summary>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <returns></returns>
        public DataTable ManageGetAction(string MANAGE_ID)
        {
            DataTable dt = this.GetList(string.Format("SELECT * FROM V_FLOW_MANAGE_ACTION WHERE MANAGE_ID = {0}", MANAGE_ID));

            return dt;
        }

        /// <summary>获取任务动作
        /// 获取任务动作
        /// </summary>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <param name="ACTION_CODE">动作编码</param>
        /// <returns></returns>
        public DataTable ManageGetAction(string MANAGE_ID, string ACTION_CODE)
        {
            DataTable dt = this.GetList(string.Format("SELECT * FROM V_FLOW_MANAGE_ACTION WHERE MANAGE_ID = {0} AND ACTION_CODE='{1}'", MANAGE_ID, ACTION_CODE));

            return dt;
        }

        /// <summary>获取控制动作
        /// 获取控制动作
        /// </summary>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <returns></returns>
        public DataTable ControlGetAction(string MANAGE_ID)
        {
            DataTable dt = this.GetList(string.Format("SELECT * FROM V_FLOW_CONTROL_ACTION WHERE MANAGE_ID = {0}", MANAGE_ID));

            return dt;
        }

        /// <summary>执行事件
        /// 执行事件
        /// </summary>
        /// <param name="sEvent">事件源</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool EventExecute(string sEvent, out  string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;


            SiaSun.LMS.Common.Compiler cp = new SiaSun.LMS.Common.Compiler();

            string sPath = AppDomain.CurrentDomain.BaseDirectory;

            cp.lsRef.Add(sPath + @"\WinService.exe");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Interface.dll");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Implement.dll");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Model.dll");

            cp.lsRef.Add(sPath + @"\SiaSun.LMS.Common.dll");

            cp.lsUsing.Add("SiaSun.LMS.WinService");

            cp.strCode = sEvent;

            bResult = cp.Excute(out sResult);

            //using (SiaSun.LMS.Common.Compiler cp = new SiaSun.LMS.Common.Compiler())
            //{
            //    string sPath = AppDomain.CurrentDomain.BaseDirectory;
            //    cp.lsRef.Add(sPath + @"\WinService.exe");
            //    cp.lsRef.Add(sPath + @"\SiaSun.LMS.Interface.dll");
            //    cp.lsRef.Add(sPath + @"\SiaSun.LMS.Implement.dll");
            //    cp.lsRef.Add(sPath + @"\SiaSun.LMS.Model.dll");
            //    cp.lsRef.Add(sPath + @"\SiaSun.LMS.Common.dll");
            //    cp.lsUsing.Add("SiaSun.LMS.WinService");
            //    cp.strCode = sEvent;

            //    bResult = cp.Excute(out sResult);
            //}

            return bResult;
        }

        /// <summary>执行任务事件
        /// 执行计划事件
        /// </summary>
        /// <param name="mPLAN_ACTION_EXCUTE">计划事件模型</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool PlanEventExecute(SiaSun.LMS.Model.PLAN_ACTION_EXCUTE mPLAN_ACTION_EXCUTE, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            if (null == mPLAN_ACTION_EXCUTE)
            {
                bResult = false;

                sResult = string.Format("操作方法不存在");

                return false;
            }

            bResult = this.EventExecute(mPLAN_ACTION_EXCUTE.ACTION_EVENT, out sResult);

            return bResult;
        }

        /// <summary>执行任务事件
        /// 执行任务事件
        /// </summary>
        /// <param name="mt">任务事件模型</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool ManageEventExecute(SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE mt, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            bResult = this.EventExecute(mt.ACTION_EVENT, out sResult);

            if (null == mt)
            {
                bResult = false;

                sResult = string.Format("mt不能为空");

                return false;
            }

            return bResult;
        }

        #endregion
    }
}
