﻿

namespace SiaSun.LMS.Implement.WDZ.Message
{
    //------------------------------------------------------------------------------
    // <auto-generated>
    //     此代码由工具生成。
    //     运行时版本:4.0.30319.42000
    //
    //     对此文件的更改可能会导致不正确的行为，并且如果
    //     重新生成代码，这些更改将会丢失。
    // </auto-generated>
    //------------------------------------------------------------------------------



    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.ServiceContractAttribute(ConfigurationName = "ISignalServerSrv")]
    public interface ISignalServerSrv
    {

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/ISignalServerSrv/ConnnectNdLoginPickStations", ReplyAction = "http://tempuri.org/ISignalServerSrv/ConnnectNdLoginPickStationsResponse")]
        ConnnectNdLoginPickStationsResponse ConnnectNdLoginPickStations(ConnnectNdLoginPickStationsRequest request);

        // CODEGEN: 正在生成消息协定，应为该操作具有多个返回值。
        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/ISignalServerSrv/ConnnectNdLoginPickStations", ReplyAction = "http://tempuri.org/ISignalServerSrv/ConnnectNdLoginPickStationsResponse")]
        System.Threading.Tasks.Task<ConnnectNdLoginPickStationsResponse> ConnnectNdLoginPickStationsAsync(ConnnectNdLoginPickStationsRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrder", ReplyAction = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrderResponse")]
        InformPickStationBindNewOrderResponse InformPickStationBindNewOrder(InformPickStationBindNewOrderRequest request);

        // CODEGEN: 正在生成消息协定，应为该操作具有多个返回值。
        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrder", ReplyAction = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrderResponse")]
        System.Threading.Tasks.Task<InformPickStationBindNewOrderResponse> InformPickStationBindNewOrderAsync(InformPickStationBindNewOrderRequest request);

        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrderMultiple", ReplyAction = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrderMultipleResponse" +
            "")]
        InformPickStationBindNewOrderMultipleResponse InformPickStationBindNewOrderMultiple(InformPickStationBindNewOrderMultipleRequest request);

        // CODEGEN: 正在生成消息协定，应为该操作具有多个返回值。
        [System.ServiceModel.OperationContractAttribute(Action = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrderMultiple", ReplyAction = "http://tempuri.org/ISignalServerSrv/InformPickStationBindNewOrderMultipleResponse" +
            "")]
        System.Threading.Tasks.Task<InformPickStationBindNewOrderMultipleResponse> InformPickStationBindNewOrderMultipleAsync(InformPickStationBindNewOrderMultipleRequest request);
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "ConnnectNdLoginPickStations", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class ConnnectNdLoginPickStationsRequest
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string group;

        public ConnnectNdLoginPickStationsRequest()
        {
        }

        public ConnnectNdLoginPickStationsRequest(string group)
        {
            this.group = group;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "ConnnectNdLoginPickStationsResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class ConnnectNdLoginPickStationsResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public bool ConnnectNdLoginPickStationsResult;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public string[] PickStationCodes;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 2)]
        public string sResult;

        public ConnnectNdLoginPickStationsResponse()
        {
        }

        public ConnnectNdLoginPickStationsResponse(bool ConnnectNdLoginPickStationsResult, string[] PickStationCodes, string sResult)
        {
            this.ConnnectNdLoginPickStationsResult = ConnnectNdLoginPickStationsResult;
            this.PickStationCodes = PickStationCodes;
            this.sResult = sResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "InformPickStationBindNewOrder", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class InformPickStationBindNewOrderRequest
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public string PickStationCode;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public string PlanGroupToBind;

        public InformPickStationBindNewOrderRequest()
        {
        }

        public InformPickStationBindNewOrderRequest(string PickStationCode, string PlanGroupToBind)
        {
            this.PickStationCode = PickStationCode;
            this.PlanGroupToBind = PlanGroupToBind;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "InformPickStationBindNewOrderResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class InformPickStationBindNewOrderResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public bool InformPickStationBindNewOrderResult;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public string sResult;

        public InformPickStationBindNewOrderResponse()
        {
        }

        public InformPickStationBindNewOrderResponse(bool InformPickStationBindNewOrderResult, string sResult)
        {
            this.InformPickStationBindNewOrderResult = InformPickStationBindNewOrderResult;
            this.sResult = sResult;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "InformPickStationBindNewOrderMultiple", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class InformPickStationBindNewOrderMultipleRequest
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public System.Collections.Generic.Dictionary<string, string> informDict;

        public InformPickStationBindNewOrderMultipleRequest()
        {
        }

        public InformPickStationBindNewOrderMultipleRequest(System.Collections.Generic.Dictionary<string, string> informDict)
        {
            this.informDict = informDict;
        }
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    [System.ServiceModel.MessageContractAttribute(WrapperName = "InformPickStationBindNewOrderMultipleResponse", WrapperNamespace = "http://tempuri.org/", IsWrapped = true)]
    public partial class InformPickStationBindNewOrderMultipleResponse
    {

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 0)]
        public bool InformPickStationBindNewOrderMultipleResult;

        [System.ServiceModel.MessageBodyMemberAttribute(Namespace = "http://tempuri.org/", Order = 1)]
        public string sResult;

        public InformPickStationBindNewOrderMultipleResponse()
        {
        }

        public InformPickStationBindNewOrderMultipleResponse(bool InformPickStationBindNewOrderMultipleResult, string sResult)
        {
            this.InformPickStationBindNewOrderMultipleResult = InformPickStationBindNewOrderMultipleResult;
            this.sResult = sResult;
        }
    }

    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public interface ISignalServerSrvChannel : ISignalServerSrv, System.ServiceModel.IClientChannel
    {
    }

    [System.Diagnostics.DebuggerStepThroughAttribute()]
    [System.CodeDom.Compiler.GeneratedCodeAttribute("System.ServiceModel", "*******")]
    public partial class SignalServerSrvClient : System.ServiceModel.ClientBase<ISignalServerSrv>, ISignalServerSrv
    {

        public SignalServerSrvClient()
        {
        }

        public SignalServerSrvClient(string endpointConfigurationName) :
                base(endpointConfigurationName)
        {
        }

        public SignalServerSrvClient(string endpointConfigurationName, string remoteAddress) :
                base(endpointConfigurationName, remoteAddress)
        {
        }

        public SignalServerSrvClient(string endpointConfigurationName, System.ServiceModel.EndpointAddress remoteAddress) :
                base(endpointConfigurationName, remoteAddress)
        {
        }

        public SignalServerSrvClient(System.ServiceModel.Channels.Binding binding, System.ServiceModel.EndpointAddress remoteAddress) :
                base(binding, remoteAddress)
        {
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        ConnnectNdLoginPickStationsResponse ISignalServerSrv.ConnnectNdLoginPickStations(ConnnectNdLoginPickStationsRequest request)
        {
            return base.Channel.ConnnectNdLoginPickStations(request);
        }

        public bool ConnnectNdLoginPickStations(string group, out string[] PickStationCodes, out string sResult)
        {
            ConnnectNdLoginPickStationsRequest inValue = new ConnnectNdLoginPickStationsRequest();
            inValue.group = group;
            ConnnectNdLoginPickStationsResponse retVal = ((ISignalServerSrv)(this)).ConnnectNdLoginPickStations(inValue);
            PickStationCodes = retVal.PickStationCodes;
            sResult = retVal.sResult;
            return retVal.ConnnectNdLoginPickStationsResult;
        }

        public System.Threading.Tasks.Task<ConnnectNdLoginPickStationsResponse> ConnnectNdLoginPickStationsAsync(ConnnectNdLoginPickStationsRequest request)
        {
            return base.Channel.ConnnectNdLoginPickStationsAsync(request);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        InformPickStationBindNewOrderResponse ISignalServerSrv.InformPickStationBindNewOrder(InformPickStationBindNewOrderRequest request)
        {
            return base.Channel.InformPickStationBindNewOrder(request);
        }

        public bool InformPickStationBindNewOrder(string PickStationCode, string PlanGroupToBind, out string sResult)
        {
            InformPickStationBindNewOrderRequest inValue = new InformPickStationBindNewOrderRequest();
            inValue.PickStationCode = PickStationCode;
            inValue.PlanGroupToBind = PlanGroupToBind;
            InformPickStationBindNewOrderResponse retVal = ((ISignalServerSrv)(this)).InformPickStationBindNewOrder(inValue);
            sResult = retVal.sResult;
            return retVal.InformPickStationBindNewOrderResult;
        }

        public System.Threading.Tasks.Task<InformPickStationBindNewOrderResponse> InformPickStationBindNewOrderAsync(InformPickStationBindNewOrderRequest request)
        {
            return base.Channel.InformPickStationBindNewOrderAsync(request);
        }

        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Advanced)]
        InformPickStationBindNewOrderMultipleResponse ISignalServerSrv.InformPickStationBindNewOrderMultiple(InformPickStationBindNewOrderMultipleRequest request)
        {
            return base.Channel.InformPickStationBindNewOrderMultiple(request);
        }

        public bool InformPickStationBindNewOrderMultiple(System.Collections.Generic.Dictionary<string, string> informDict, out string sResult)
        {
            InformPickStationBindNewOrderMultipleRequest inValue = new InformPickStationBindNewOrderMultipleRequest();
            inValue.informDict = informDict;
            InformPickStationBindNewOrderMultipleResponse retVal = ((ISignalServerSrv)(this)).InformPickStationBindNewOrderMultiple(inValue);
            sResult = retVal.sResult;
            return retVal.InformPickStationBindNewOrderMultipleResult;
        }

        public System.Threading.Tasks.Task<InformPickStationBindNewOrderMultipleResponse> InformPickStationBindNewOrderMultipleAsync(InformPickStationBindNewOrderMultipleRequest request)
        {
            return base.Channel.InformPickStationBindNewOrderMultipleAsync(request);
        }
    }
}

