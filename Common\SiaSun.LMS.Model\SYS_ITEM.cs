﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// SYS_ITEM 
	/// </summary>
	[Serializable]
    [DataContract]
	public class SYS_ITEM
	{
		public SYS_ITEM()
		{
			
		}
		
		private int _item_id;
		private int _item_parent_id;
		private string _item_code;
		private string _item_name;
		private int _item_order;
		private string _item_remark;
		private string _item_flag;
		
		///<sumary>
		/// 码表编号
		///</sumary>
        [DataMember]
        public int ITEM_ID
		{
			get{return _item_id;}
			set{_item_id = value;}
		}
		///<sumary>
		/// 码表父编号
		///</sumary>
        [DataMember]
        public int ITEM_PARENT_ID
		{
			get{return _item_parent_id;}
			set{_item_parent_id = value;}
		}
		///<sumary>
		/// 编码
		///</sumary>
        [DataMember]
        public string ITEM_CODE
		{
			get{return _item_code;}
			set{_item_code = value;}
		}
		///<sumary>
		/// 名称
		///</sumary>
        [DataMember]
        public string ITEM_NAME
		{
			get{return _item_name;}
			set{_item_name = value;}
		}
		///<sumary>
		/// 排序
		///</sumary>
        [DataMember]
        public int ITEM_ORDER
		{
			get{return _item_order;}
			set{_item_order = value;}
		}
		///<sumary>
		/// 备注
		///</sumary>
        [DataMember]
        public string ITEM_REMARK
		{
			get{return _item_remark;}
			set{_item_remark = value;}
		}
		///<sumary>
		/// 标记
		///</sumary>
        [DataMember]
        public string ITEM_FLAG
		{
			get{return _item_flag;}
			set{_item_flag = value;}
		}
	}
}
