﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ManageProductOut:ManageBase
    {

        /// <summary>
        /// 根据配盘方案 生成下架任务
        /// 调用界面 MANAGE_TEMPLATE_OUT.XAML
        /// </summary>
        /// <param name="mMANAGE_MAIN"></param>
        /// <param name="lsMANAGE_LIST"></param>
        /// <param name="bTrans"></param>
        /// <param name="bAutoSendControl"></param>
        /// <param name="bComplete"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 int Template_ID,
                                 bool bTrans,
                                 bool bAutoSendControl,
                                 bool bComplete, 
                                 out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int MANAGE_ID = 0;



            SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_END = null;

            if (mWH_CELL_END != null)
                mWH_AREA_END = this._P_WH_AREA.GetModel(mWH_CELL_END.AREA_ID);


            DataTable dt = this.GetList(string.Format(" select * from manage_main where manage_type_code = '{0}' and end_cell_id = {1}",
                                          mMANAGE_MAIN.MANAGE_TYPE_CODE, mMANAGE_MAIN.END_CELL_ID));

            if (dt != null && dt.Rows.Count > 0)
            {
                sResult = string.Format(" 站台{0} 存在未完成的呼叫要料任务", mWH_CELL_END.CELL_CODE);

                bResult = false;

                return bResult;
            }

            if (mWH_CELL_END.CELL_STATUS == Enum.CELL_STATUS.Pallet.ToString())
            {
                sResult = string.Format(" 站台{0} 存在托盘", mWH_CELL_END.CELL_CODE);

                bResult = false;

                return bResult;
            }

            string laneWaySql = string.Format(@"SELECT DEVICE_CODE,
                    LANE_WAY ,
                   SUM(STORAGE_LIST_QUANTITY) AS STORAGE_BALANCE,
                   dbo.OUTTASKBALANCE(DEVICE_CODE) AS OUT_TASK_BALANCE
                    FROM V_STORAGE_LIST
                    WHERE TEMPLATE_ID = {0}
                    AND CELL_TYPE = 'Cell'
                    AND DEVICE_CODE IN (SELECT START_DEVICE FROM IO_CONTROL_ROUTE WHERE END_DEVICE='{3}' AND CONTROL_ROUTE_STATUS = 1)
                     {1}
                     {2}                    
                    GROUP BY LANE_WAY,DEVICE_CODE",
                /*0*/ Template_ID,
                /*1*/string.Format(" AND ( CELL_STATUS = '{0}' or CELL_STATUS = '{1}') ", SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(), SiaSun.LMS.Enum.CELL_STATUS.Pallet.ToString()),
                /*2*/string.Format(" AND RUN_STATUS = '{0}'", SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString()),
                /*3*/mWH_CELL_END.DEVICE_CODE
                                             );

            DataTable dtLaneWay = this.GetList(laneWaySql);

            if (dtLaneWay != null && dtLaneWay.Rows.Count == 0)
            {
                sResult = string.Format("未找到可用出库巷道，请检查设备状态或库内是否有可用配盘");

                bResult = false;

                return bResult;
            }

            DataTable dtStorage = this.GetList(string.Format(" select storage_id from v_storage_list where template_id = {0} and run_status = '{1}' and device_code = '{2}' order by entry_time",
                                           Template_ID, Enum.RUN_STATUS.Enable.ToString(), dtLaneWay.Rows[0]["DEVICE_CODE"].ToString()));

            if (dtStorage != null && dtStorage.Rows.Count == 0)
            {
                sResult = string.Format("未找到可用配盘");

                bResult = false;

                return bResult;
            }

            SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(Convert.ToInt32(dtStorage.Rows[0]["storage_id"]));

            IList<SiaSun.LMS.Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);

            bResult = lsSTORAGE_LIST.Count > 0;

            if (!bResult)
            {
                sResult = string.Format("未找到库存");

                return bResult;
            }


            if (this._P_MANAGE_MAIN.GetModelStockBarcode(mSTORAGE_MAIN.STOCK_BARCODE) != null)
            {
                bResult = false;

                sResult = string.Format("托盘条码{0}已经存在任务", mSTORAGE_MAIN.STOCK_BARCODE);

                return bResult;
            }


            SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mSTORAGE_MAIN.CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_START = null;

            if (mWH_CELL_START != null)
                mWH_AREA_START = this._P_WH_AREA.GetModel(mWH_CELL_START.AREA_ID);


            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;

                mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST in lsSTORAGE_LIST)
                {
                    SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();

                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    mMANAGE_LIST.STORAGE_LIST_ID = mSTORAGE_LIST.STORAGE_ID;

                    mMANAGE_LIST.GOODS_ID = mSTORAGE_LIST.GOODS_ID;

                    mMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LIST.STORAGE_LIST_QUANTITY;

                    bResult = this._S_GoodsService.GoodsPropertySetValue(mSTORAGE_LIST.GOODS_ID, mMANAGE_LIST, mSTORAGE_LIST, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction();

                        return bResult;
                    }

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);

                }

                if (mWH_CELL_START != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (mWH_CELL_END != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                if (bAutoSendControl && mWH_AREA_START != null && mWH_AREA_START.AREA_TYPE.ToLower() == "liku")
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (bComplete && (mWH_AREA_START == null || mWH_AREA_START.AREA_TYPE.ToLower() == "xuniku"))
                {
                    bResult = this.ManageComplete(MANAGE_ID, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                sResult = string.Format("托盘{0}生成出库任务成功", mMANAGE_MAIN.STOCK_BARCODE);

                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }


        /// <summary>完成
        /// 完成
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="bTrans">是否独立事务</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            bResult = null != mMANAGE_MAIN;

            if (!bResult)
            {
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());

                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

            if (mMANAGE_TYPE == null)
            {
                bResult = false;

                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                           || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }

                if (!bResult)
                {
                    sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());

                    return bResult;
                }
                if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());

                    return bResult;
                }

                bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { MANAGE_ID }, out sResult);


                if (!bResult)
                {
                    sResult = string.Format("库存处理错误-{0}", sResult);

                    return bResult;
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }
    }
}
