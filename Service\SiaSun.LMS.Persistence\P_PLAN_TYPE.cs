﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// PLAN_TYPE
	/// </summary>
	public class P_PLAN_TYPE : P_Base_House
	{
		public P_PLAN_TYPE ()
		{
			//
			// TODO: 此处添加PLAN_TYPE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<PLAN_TYPE> GetList()
		{
			return ExecuteQueryForList<PLAN_TYPE>("PLAN_TYPE_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(PLAN_TYPE plan_type)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("PLAN_TYPE");
                plan_type.PLAN_TYPE_ID = id;
            }

            return ExecuteInsert("PLAN_TYPE_INSERT",plan_type);
		}

		/// <summary>
		/// 修改
		/// </summary>
		public int Update(PLAN_TYPE plan_type)
		{
			return ExecuteUpdate("PLAN_TYPE_UPDATE",plan_type);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public PLAN_TYPE GetModel(System.Int32 PLAN_TYPE_ID)
		{
			return ExecuteQueryForObject<PLAN_TYPE>("PLAN_TYPE_SELECT_BY_ID",PLAN_TYPE_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public PLAN_TYPE GetModelPlanTypeCode(string PLAN_TYPE_CODE)
        {
            return ExecuteQueryForObject<PLAN_TYPE>("PLAN_TYPE_SELECT_BY_PLAN_TYPE_CODE", PLAN_TYPE_CODE);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 PLAN_TYPE_ID)
		{
			return ExecuteDelete("PLAN_TYPE_DELETE",PLAN_TYPE_ID);
		}
		

	}
}
