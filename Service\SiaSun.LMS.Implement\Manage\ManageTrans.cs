﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 上层系统通过管理直接调度设备，不处理库存
    /// </summary>
    public class ManageTrans : ManageBase
    {
        /// <summary>
        /// 生成
        /// </summary>
        public bool ManageCreate(Model.MANAGE_MAIN mMANAGE_MAIN, List<Model.MANAGE_LIST> lsMANAGE_LIST, bool bTrans, bool bAutoSendControl, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                Model.MANAGE_MAIN mMANAGE_MAIN_VERIFY = this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                if(mMANAGE_MAIN_VERIFY!=null)
                {
                    bResult = false;
                    sResult = string.Format("条码[{0}]已存在任务", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                if (mMANAGE_TYPE == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    return bResult;
                }

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }

                if (bAutoSendControl)
                {
                    bResult = this.ManageDownLoadTrans(mMANAGE_MAIN.MANAGE_ID, string.Empty, false, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManageTrans.ManageCreate:异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }

        /// <summary>
        /// 任务完成
        /// </summary>
        public new bool ManageComplete(int manageId,bool trans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(trans);

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(manageId);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("ManageTrans.ManageComplete:未能找到任务 任务ID_{0}", manageId);
                    return bResult;
                }

                if(mMANAGE_MAIN.MANAGE_TYPE_CODE != Enum.MANAGE_TYPE.ManageTrans.ToString())
                {
                    bResult = false;
                    sResult = string.Format("ManageTrans.ManageComplete:只有非立库输送任务使用此方法完成 任务ID_{0}", manageId);
                    return bResult;
                }

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(manageId);
                if (lsMANAGE_LIST == null || lsMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("ManageTrans.ManageComplete:未能找到任务列表 任务ID_{0}", manageId);
                    return bResult;
                }

                //wdz add 2018-03-26 对于非立库任务，执行时获取AGV车号
                Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModelManageID(manageId);
                if (mIO_CONTROL != null)
                {
                    Model.MANAGE_LIST mMANAGE_LIST = lsMANAGE_LIST[0];
                    mMANAGE_LIST.GOODS_PROPERTY6 = mIO_CONTROL.AGV_NO.ToString();
                    this._P_MANAGE_LIST.Update(mMANAGE_LIST);
                }

                //是否更新液晶看板
                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);
                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                if (mWH_CELL_END != null && !string.IsNullOrEmpty(lsMANAGE_LIST[0].GOODS_PROPERTY5))
                {
                    foreach (Model.LCD_MAIN mLCD_MAIN in this._P_LCD_MAIN.GetList())
                    {
                        if (mLCD_MAIN.LCD_STATION.Split('|').Contains(mWH_CELL_END.CELL_CODE))
                        {
                            mLCD_MAIN.LCD_MESSAGE = string.Format("{0}-{1}-{2}", mWH_CELL_START == null ? "" : mWH_CELL_START.CELL_CODE, lsMANAGE_LIST[0].GOODS_PROPERTY5, mMANAGE_MAIN.STOCK_BARCODE);
                            mLCD_MAIN.LCD_READ_FLAG = "0";
                            mLCD_MAIN.LCD_UPDATE_TIME = Common.StringUtil.GetDateTime();
                            this._P_LCD_MAIN.Update(mLCD_MAIN);
                        }
                    }
                }

                //接口调用
                string isNoticeWms = string.Empty;
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    bResult = new Interface.noneMiniloadTranResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, lsMANAGE_LIST[0].GOODS_PROPERTY1, lsMANAGE_LIST[0].GOODS_PROPERTY2, lsMANAGE_LIST[0].GOODS_PROPERTY3, lsMANAGE_LIST[0].GOODS_PROPERTY4,  true, string.Empty, lsMANAGE_LIST[0].GOODS_PROPERTY6, out sResult);
                    if(!bResult)
                    {
                        return bResult;
                    }
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManageTrans.ManageComplete:任务完成时发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(trans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(trans);
                }
            }
            return bResult;
        }
    }
}
