﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucQuickQuery"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="67" d:DesignWidth="153">
    <Border>
        <ScrollViewer Margin="1" >
            <Grid>
                    <WrapPanel Name="panelQuery" Margin="1"  >
                        <WrapPanel Name="panelButton" Margin="1" ButtonBase.Click="StackPanel_Click" >
                            <Button Name="btnOK"  Margin="3,2,3,2"  Width="50">查询</Button>
                            <Button Name="btnClear"  Margin="3,2,3,2"   Width="50">重置</Button>
                            <Button Name="btnAdd"  Margin="3,2,3,2"   Width="50">添加</Button>
                        </WrapPanel>
                    </WrapPanel>
            </Grid>
        </ScrollViewer>
    </Border>
</UserControl>
