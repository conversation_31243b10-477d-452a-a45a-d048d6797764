﻿<Window x:Class="SiaSun.LMS.WPFClient.Role_Select"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="角色选择" Height="250" Width="350" WindowStyle="None" WindowStartupLocation="CenterScreen" Topmost="True">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="35"></RowDefinition>
        </Grid.RowDefinitions>
        <GroupBox Name="grpRole" Grid.Row="0" Header="双击选择角色" Margin="5"  >
            <ListBox Name="lbRole" Margin="10" DataContext="{Binding Path=ROLE_ID}" MouseDoubleClick="lbRole_MouseDoubleClick"></ListBox>
        </GroupBox>
        <StackPanel Grid.Row="1" Orientation="Horizontal" HorizontalAlignment="Center">
            <Button Name="btnOK" Width="60" Click="btnOK_Click">确定</Button>
            <Button Name="btnCancel" Width="60" Click="btnCancel_Click">取消</Button>
        </StackPanel>
    </Grid>
</Window>
