﻿<Window x:Class="SiaSun.LMS.WPFClient.Login"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="新松智能物流执行系统" MinHeight="160" MinWidth="450" WindowStartupLocation="CenterScreen" 
        AllowsTransparency="True" WindowStyle="None"  MaxWidth="460" MaxHeight="280" ShowInTaskbar="False" 
        mc:Ignorable="d" 
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
        d:DesignHeight="280" d:DesignWidth="400" 
        SizeToContent="WidthAndHeight">

    <Border BorderBrush="Gray" BorderThickness="1" CornerRadius="4" Height="231" Width="476">
        <Border.Background>
            <LinearGradientBrush EndPoint="1.117,1.196" StartPoint="0.032,0.132">
                <GradientStop Color="#FF404E53"/>
                <GradientStop Color="#B44F8EB1" Offset="0"/>
            </LinearGradientBrush>
        </Border.Background>
        <Border.Effect>
            <DropShadowEffect Direction="1" ShadowDepth="1"></DropShadowEffect>
        </Border.Effect>
        <Grid Width="490" Height="247">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="45"></RowDefinition>
            <!--<RowDefinition Height="*"></RowDefinition>-->
            <RowDefinition Height="Auto"></RowDefinition>
            <!--<RowDefinition Height="25"></RowDefinition>-->
        </Grid.RowDefinitions>
            <StackPanel Grid.Row="1" HorizontalAlignment="Center" VerticalAlignment="Center" Margin="0,0,56,0">
                <TextBlock HorizontalAlignment="Center" VerticalAlignment="Bottom" FontSize="24" Foreground="Gainsboro">新松自动化物流执行系统</TextBlock>
            </StackPanel>      
           
     
            <Grid Grid.Row="2" Margin="0,5,5,0">
                <!--<Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>-->
                
                    <Grid.ColumnDefinitions>
                        <ColumnDefinition Width="1*"></ColumnDefinition>
                        <ColumnDefinition Width="2.5*"></ColumnDefinition>
                    </Grid.ColumnDefinitions>

                <Image Source="/@Images/a2.jpg" HorizontalAlignment="Left" VerticalAlignment="Center" Stretch="Uniform" Margin="0,-43,0,41" Height="246" Width="184" Grid.ColumnSpan="2"></Image>

                    <Grid Grid.Column="1" Margin="10,11,-5,0" Width="260" Height="165" VerticalAlignment="Top">
                        <Grid.RowDefinitions>
                            <RowDefinition ></RowDefinition>
                            <RowDefinition ></RowDefinition>
                            <RowDefinition ></RowDefinition>
                            <RowDefinition ></RowDefinition>
                        </Grid.RowDefinitions>
                        <StackPanel Grid.Row="0" Orientation="Horizontal" Margin="0,0,0,5">
                            <Label Content="用户名："  FontSize="13" FontWeight="Medium" MinWidth="65"  Foreground="#FFFBFBFB"/>
                            <TextBox Name="txtName"  FontSize="13"  Width="160" TabIndex="0" Height="30" Text="" />
                     </StackPanel>

                        <StackPanel Grid.Row="1" Orientation="Horizontal" Margin="0,0,0,5">
                            <Label Content="密   码：" FontSize="13" FontWeight="Medium" MinWidth="65"  Foreground="#FFEFF1F5"/>
                            <PasswordBox Name="txtPassword"   FontSize="13" Width="160" Height="30" TabIndex="1" KeyUp="txtPassword_KeyUp" Password="" />
                    </StackPanel>

                        <StackPanel Grid.Row="2" Orientation="Horizontal" Margin="0,0,0,5">
                        <Label Content="语   言：" FontSize="13" FontWeight="Medium" MinWidth="65"  Foreground="#FFEFF1F5"/>
                            <ComboBox Name="cmbLanguage" FontSize="13" Width="160" Height="30" TabIndex="2" SelectedIndex="0" IsEnabled="False">
                                <ComboBoxItem Content="默认" Tag="Default"></ComboBoxItem>
                                <ComboBoxItem Content="English" Tag="en-US"> </ComboBoxItem>
                            </ComboBox>
                        </StackPanel>

                        <StackPanel Grid.Row="3" Orientation="Horizontal" Margin="0,0,0,5">
                            <Button Name="BtnOK" Width="120" Height="30" VerticalAlignment="Center" Click="BtnOK_Click" Margin="0,0,10,0">确认</Button>
                            <Button Name="BtnCancel" Width="120" Height="30" VerticalAlignment="Center" Click="BtnCancel_Click">取消</Button>
                        </StackPanel>
                    </Grid>
            </Grid>

    </Grid>
    </Border>
</Window>
