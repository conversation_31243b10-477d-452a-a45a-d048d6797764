﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.DZBQ
{
    /// <summary>
    /// 拣选工作站相关
    /// 电子标签操作方法类
    /// done
    /// </summary>
    public static class DZBQOpration
    {
        //通过IP地址创建电子标签的静态对象
        public static DZBQ dzbq = new DZBQ("DZBQ_CONTROLER_IP");
        public static LMS.Implement.S_BaseService _baseService = new S_BaseService();
        /// <summary>
        /// 电子标签连接
        /// 可以向其发送显示指令
        /// </summary>
        public static void Connect()
        {
            dzbq.Connect();

            //电子标签按钮触发事件
            dzbq.CallBackEvent -= Dzbq_CallBackEvent;
            dzbq.CallBackEvent += Dzbq_CallBackEvent;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 拣选工作站电子标签回传方法
        /// done
        /// </summary>
        /// <param name="rs">电子标签回传的数据</param>
        private static void Dzbq_CallBackEvent(RYB_PTL_API.RYB_PTL.RtnValueStruct rs)
        {
            /*
             * 配合电气调试时的测试代码
             * 已经注释
             */
            #region test

            /*
            switch (rs.Tagid)
            {
                case "0001":
                    Send("0002", 20);
                    TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, true, false, false, false, false }, 0, "1234567890");
                    //Send("0006", 4);
                    break;
                case "0002":
                    Send("0003", 30);
                    TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, false, true, false, false, false }, 0, "1234567890");
                    //Send("0006", 3);
                    break;
                case "0003":
                    Send("0004", 40);
                    TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, false, false, true, false, false }, 0, "1234567890");
                    //Send("0006", 2);
                    break;
                case "0004":
                    Send("0005", 50);
                    TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { true, true, true, true, true, true }, 0, "1234567890");
                    //Send("0006", 1);
                    break;
                case "0005":
                    TCP.CommunicationOperation.comm3.LightsStatus(3, new bool[6] { false, false, false, false, false, false }, 0, "1234567890");
                    TCP.CommunicationOperation.comm3.BoxLeave(3, 1, 1, 7103, "1234567890");//Send("0006", 1);
                                                                                           //Send("0006", 0);
                    break;
                case "0006":
                    Send("0007", 20);
                    TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, true, false, false, false, false }, 0, "1234567890");
                    //TCP.CommunicationOperation.comm.LightsStatus(1, new bool[6] { true, true, true, true, true, true }, 0, "1234567890");
                    //Send("0006", 0);
                    //TCP.CommunicationOperation.comm.BoxLeave(1, 1, 1, 7103, "1234567890");
                    //Send("0003", 30);
                    //TCP.CommunicationOperation.comm.BoxLeave();
                    break;
                case "0007":
                    Send("0008", 20);
                    TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, false, true, false, false, false }, 0, "1234567890");
                    //Send("0006", 4);
                    break;
                case "0008":
                    Send("0009", 30);
                    TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, false, false, true, false, false }, 0, "1234567890");
                    //Send("0006", 3);
                    break;
                case "0009":
                    Send("0010", 40);
                    TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { true, true, true, true, true, true }, 0, "1234567890");
                    //Send("0006", 2);
                    break;
                case "0010":
                    TCP.CommunicationOperation.comm2.BoxLeave(2, 1, 1, 7103, "1234567890");//Send("0006", 1);
                    TCP.CommunicationOperation.comm2.LightsStatus(2, new bool[6] { false, false, false, false, false, false }, 0, "1234567890");

                    break;

                case "0011":
                    Send("0012", 20);
                    TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, true, false, false, false, false }, 0, "1234567890");
                    //Send("0006", 4);
                    break;
                case "0012":
                    Send("0013", 30);
                    TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, false, true, false, false, false }, 0, "1234567890");
                    //Send("0006", 3);
                    break;
                case "0013":
                    Send("0014", 40);
                    TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { true, true, true, true, true, true }, 0, "1234567890");
                    //Send("0006", 2);
                    break;
                case "0014":
                    Send("0015", 50);
                    TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, false, false, false, true, false }, 0, "1234567890");
                    //Send("0006", 1);
                    break;
                case "0015":
                    //Send("0011", 15);
                    TCP.CommunicationOperation.comm1.LightsStatus(1, new bool[6] { false, false, false, false, false, false }, 0, "1234567890");
                    TCP.CommunicationOperation.comm1.BoxLeave(1, 1, 1, 7103, "1234567890");
                    //Send("0006", 0);
                    break;
            }
            */
            #endregion
            
            //WDZ.PickPositionOperation mPickPositionOperation = new WDZ.PickPositionOperation();  //等待重构

           /*
            * 通过电子标签的ID获得该电子标签所在的拣选工作点
            * 通过拣选工作点上记录的拣选任务MANAGE_MAIN的ID，获得拣选任务，并调用拣选任务完成方法
            * 然后切换到新的拣选任务
            */
            Model.T_PICK_POSITION mT_PICK_POSITION = new Persistence.P_T_PICK_POSITION().GetModelByDZBQ_MAC(rs.Tagid);

            if (mT_PICK_POSITION != null)
            {
                try
                {
                    int manage_id = Convert.ToInt32(mT_PICK_POSITION.DZBQ_PROPERTY1);

                    bool bResult = false;
                    string sResult = string.Empty;


                    //bResult = new ManagePick().ManageComplete(mT_PICK_POSITION, manage_id, true, out sResult);
                    bResult = new ManagePick().ManageComplete(mT_PICK_POSITION, manage_id,rs.Number, true, out sResult);

                    if (bResult)
                    {
                        WDZ.PickStationOperation mPickStationOperation = new WDZ.PickStationOperation(mT_PICK_POSITION.STATION_ID);

                        mPickStationOperation.MoveNewManage();
                    }

                    if (bResult)
                    {
                        //new Implement.S_BaseService()._log.Debug(string.Format("[拣选工作站] 电子标签拣选任务完成成功！任务编号:{0}",manage_id));
                        _baseService._log.Debug(string.Format("[拣选工作站] 电子标签拣选任务完成成功！任务编号:{0}", manage_id));

                    }
                    else
                    {
                        //new Implement.S_BaseService()._log.Debug(string.Format("[拣选工作站] 电子标签拣选任务完成失败！任务编号:{0},错误信息{1}", manage_id,sResult));
                        _baseService._log.Debug(string.Format("[拣选工作站] 电子标签拣选任务完成失败！任务编号:{0},错误信息{1}", manage_id, sResult));
               
                    }
                }
                catch (Exception ex)
                {
                    // new Implement.S_BaseService()._log.FatalFormat("[拣选工作站] 电子标签按钮回传异常,EX401 回传参数:Tagid:{0}-Number:{1}-Ip:{2}-KeyCode:{3}-Locator:{4};error:{5}", rs.Tagid, rs.Number, rs.Ip, rs.KeyCode, rs.Locator, ex.Message);
                    _baseService._log.FatalFormat("[拣选工作站] 电子标签按钮回传异常,EX401 回传参数:Tagid:{0}-Number:{1}-Ip:{2}-KeyCode:{3}-Locator:{4};error:{5}", rs.Tagid, rs.Number, rs.Ip, rs.KeyCode, rs.Locator, ex.Message);

                }

            }
        }



        /// <summary>
        /// 拣选工作站相关
        /// 下达电子标签显示任务
        /// done
        /// </summary>
        /// <param name="id">标签对应的id 例如"0001"</param>
        /// <param name="disNumber">显示的5位数字</param>
        /// <param name="imode">标签按钮灯光显示模式 默认为0，0-7 数字越大闪烁频率越快</param>
        /// <param name="icolorIndex">标签按钮灯光颜色 默认为6, 0-7</param>
        /// <returns></returns>
        public static void Send(string id, int disNumber, int imode = 0, int icolorIndex = 6)
        {
            dzbq.Send(id, disNumber, imode, icolorIndex);
        }

        /// <summary>
        /// 中断连接
        /// </summary>
        public static void Disconnect()
        {
            dzbq.DisConnect();
        }
    }
}
