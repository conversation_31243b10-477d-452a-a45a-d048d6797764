<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type Button}" BasedOn="{x:Null}">
        <Setter Property="FocusVisualStyle" Value="{DynamicResource NuclearButtonFocusVisual}" />
        <Setter Property="Foreground" Value="#FF042271" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="Height" Value="25"/>
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="1" />

        <Setter Property="Template" Value="{DynamicResource ButtonTemplate}" />
    </Style>

    <ControlTemplate x:Key="ButtonTemplate" TargetType="{x:Type Button}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgroundOver" Storyboard.TargetProperty="Opacity" To="1" />
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgroundOver_Highlight" Storyboard.TargetProperty="Opacity" To="0.65" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgroundOver" Storyboard.TargetProperty="Opacity" To="0" />
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgroundOver_Highlight" Storyboard.TargetProperty="Opacity" To="0" />
            </Storyboard>
            <Storyboard x:Key="PressedOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgroundPressed" Storyboard.TargetProperty="Opacity" To="0.84" />
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgoundPressed_Highlight" Storyboard.TargetProperty="Opacity" To="0.65" />
            </Storyboard>
            <Storyboard x:Key="PressedOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgroundPressed" Storyboard.TargetProperty="Opacity" To="0" />
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgoundPressed_Highlight" Storyboard.TargetProperty="Opacity" To="0" />
            </Storyboard>
            <Storyboard x:Key="DisabledOn">
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledVisualElement" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
                <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="DisabledOverlay" Storyboard.TargetProperty="(UIElement.Visibility)">
                    <DiscreteObjectKeyFrame KeyTime="00:00:00.1000000" Value="{x:Static Visibility.Visible}" />
                </ObjectAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>

            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>

            </Storyboard>
        </ControlTemplate.Resources>
        <Grid x:Name="Grid">
            <Border x:Name="BackgroundNorm" BorderThickness="1" CornerRadius="1.75" Background="{DynamicResource NormalBrush}" BorderBrush="{DynamicResource NormalBorderBrush}"/>
            <Border x:Name="BackgroundNorm_highlight" Margin="1" BorderBrush="{DynamicResource NormalHighlightBrush}" BorderThickness="1,0,1,1" CornerRadius="1" Opacity="0.65" />
            <Border x:Name="BackgroundOver" BorderThickness="1" CornerRadius="1.75" Opacity="0" Background="{DynamicResource MouseOverBrush}" BorderBrush="{DynamicResource MouseOverBorderBrush}"/>
            <Border x:Name="BackgroundOver_Highlight" Margin="1" BorderThickness="1,0,1,1" CornerRadius="1" Opacity="0" BorderBrush="{DynamicResource MouseOverHighlightBrush}"/>
            <Border x:Name="BackgroundPressed" BorderThickness="1" CornerRadius="1.75" Opacity="0" Background="{DynamicResource PressedBrush}" BorderBrush="{DynamicResource PressedBorderBrush}"/>
            <Border x:Name="BackgoundPressed_Highlight" Margin="1" BorderThickness="1,0,1,1" CornerRadius="1" Opacity="0" BorderBrush="{DynamicResource PressedHighlightBrush}"/>
            <Border x:Name="DisabledVisualElement" IsHitTestVisible="false" Background="{DynamicResource DisabledBackgroundBrush}" BorderBrush="{DynamicResource DisabledBorderBrush}" BorderThickness="1" Opacity="0" />

            <ContentPresenter x:Name="contentPresenter" Content="{TemplateBinding Content}" ContentTemplate="{TemplateBinding ContentTemplate}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" />
            <TextBlock Panel.ZIndex="1" x:Name="DisabledOverlay" Text="{TemplateBinding Content}" Foreground="#FF8E96A2" HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" VerticalAlignment="{TemplateBinding VerticalContentAlignment}" Margin="{TemplateBinding Padding}" Visibility="Collapsed" />
            <Border x:Name="FocusVisualElement" Margin="-1" BorderBrush="{DynamicResource FocusBrush}" BorderThickness="1" CornerRadius="2.75" IsHitTestVisible="false" Opacity="0" />
            <Border x:Name="DefaultBorder" Margin="-1" BorderBrush="{DynamicResource FocusBrush}" BorderThickness="1" CornerRadius="2.75" IsHitTestVisible="false" Opacity="0" />
        </Grid>

        <ControlTemplate.Triggers>
            <Trigger Property="IsDefault" Value="True">
                <Setter Property="Opacity" TargetName="DefaultBorder" Value="1" />
            </Trigger>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="FocusedOn_BeginStoryboard" Storyboard="{StaticResource FocusedOn}" />
                </Trigger.EnterActions>
                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="FocusedOff_BeginStoryboard" Storyboard="{StaticResource FocusedOff}" />
                </Trigger.ExitActions>
            </Trigger>
            <Trigger Property="IsKeyboardFocused" Value="true">

                <Trigger.ExitActions>
                    <BeginStoryboard x:Name="FocusedOff_BeginStoryboard1" Storyboard="{StaticResource FocusedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard x:Name="FocusedOn_BeginStoryboard1" Storyboard="{StaticResource FocusedOn}" />
                </Trigger.EnterActions>

            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </Trigger.EnterActions>

            </Trigger>
            <Trigger Property="IsPressed" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOff}" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="true">
                <Setter Property="Foreground" Value="#FF042271" />
                <Setter Property="Opacity" TargetName="DisabledVisualElement" Value="0" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">

                <!--<Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource DisabledOn}" />
                </Trigger.EnterActions>-->

                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                <Setter Property="Opacity" TargetName="DisabledVisualElement" Value="1" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>
</ResourceDictionary>