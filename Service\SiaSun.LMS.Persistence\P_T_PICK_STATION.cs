﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Jacky He
 *       日期：     2017/9/19
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// MANAGE_MAIN
    /// </summary>
    public class P_T_PICK_STATION : P_Base_House
    {
        public P_T_PICK_STATION()
        {
            //
            // TODO: 此处添加T_PICK_STATION的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<T_PICK_STATION> GetList()
        {
            return ExecuteQueryForList<T_PICK_STATION>("T_PICK_STATION_SELECT", null);
        }

        /// <summary>
        /// 得到数据表
        /// </summary>
        public DataTable GetTable()
        {
            return ExecuteQueryForDataTable("T_PICK_STATION_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(T_PICK_STATION t_pick_station)
        {
            //int id = this.GetId("T_PICK_STATION", "T_PICK_STATION_ID");
            //t_pick_station.STATION_ID = id;

            ExecuteInsert("T_PICK_STATION_INSERT", t_pick_station);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(T_PICK_STATION t_pick_station)
        {
            ExecuteUpdate("T_PICK_STATION_UPDATE", t_pick_station);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_STATION GetModel(System.Int32 STATION_ID)
        {
            return ExecuteQueryForObject<T_PICK_STATION>("T_PICK_STATION_SELECT_BY_ID", STATION_ID);
        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站的PlcNo获得拣选工作站实例
        /// done
        /// </summary>
        /// <param name="Property1">PlcNo</param>
        /// <returns>返回拣选工作站实例</returns>
        public T_PICK_STATION GetModelByPROPERTY1(String PROPERTY1)
        {
            return ExecuteQueryForObject<T_PICK_STATION>("T_PICK_STATION_SELECT_BY_PROPERTY1", PROPERTY1);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_STATION GetModel_BY_IP(String STATION_IP)
        {
            return ExecuteQueryForObject<T_PICK_STATION>("T_PICK_STATION_SELECT_BY_IP", STATION_IP);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_STATION GetModel_BY_CODE(String STATION_CODE)
        {
            return ExecuteQueryForObject<T_PICK_STATION>("T_PICK_STATION_SELECT_BY_STATION_CODE", STATION_CODE);
        }

        /// <summary>
        /// 得到模型
        /// wdz add 2018-01-27
        /// </summary>
        /// <returns></returns>
        public T_PICK_STATION GetModel_BY_CELL_ID(Int32 CELL_ID)
        {
            return ExecuteQueryForObject<T_PICK_STATION>("T_PICK_STATION_SELECT_BY_CELL_ID", CELL_ID);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete(System.Int32 STATION_ID)
        {
            ExecuteDelete("T_PICK_STATION_DELETE", STATION_ID);
        }        
    }
}
