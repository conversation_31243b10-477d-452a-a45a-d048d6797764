﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="MANAGE_TYPE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="MANAGE_TYPE" type="SiaSun.LMS.Model.MANAGE_TYPE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="MANAGE_TYPE">
			<result property="MANAGE_TYPE_ID" column="manage_type_id" />
			<result property="MANAGE_TYPE_CODE" column="manage_type_code" />
			<result property="MANAGE_TYPE_NAME" column="manage_type_name" />
			<result property="MANAGE_TYPE_INOUT" column="manage_type_inout" />
			<result property="MANAGE_TYPE_GROUP" column="manage_type_group" />
			<result property="MANAGE_TYPE_CLASS" column="manage_type_class" />
      <result property="STORAGE_TYPE_CLASS" column="storage_type_class" />
			<result property="MANAGE_TYPE_ORDER" column="manage_type_order" />
			<result property="MANAGE_TYPE_FLAG" column="manage_type_flag" />
      <result property="MANAGE_TYPE_PROPERTY" column="manage_type_property" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="MANAGE_TYPE_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        manage_type_id,
        manage_type_code,
        manage_type_name,
        manage_type_inout,
        manage_type_group,
        manage_type_class,
        storage_type_class,
        manage_type_order,
        manage_type_flag,
        manage_type_property
        From MANAGE_TYPE
      </select>
		
		<select id="MANAGE_TYPE_SELECT_BY_ID" parameterClass="int" extends = "MANAGE_TYPE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_type_id=#MANAGE_TYPE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE" parameterClass="string" extends = "MANAGE_TYPE_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_type_code=#MANAGE_TYPE_CODE#
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="MANAGE_TYPE_INSERT" parameterClass="MANAGE_TYPE">
      Insert Into MANAGE_TYPE (
      manage_type_id,
      manage_type_code,
      manage_type_name,
      manage_type_inout,
      manage_type_group,
      manage_type_class,
      storage_type_class,
      manage_type_order,
      manage_type_flag,
      manage_type_property
      )Values(
      #MANAGE_TYPE_ID#,
      #MANAGE_TYPE_CODE#,
      #MANAGE_TYPE_NAME#,
      #MANAGE_TYPE_INOUT#,
      #MANAGE_TYPE_GROUP#,
      #MANAGE_TYPE_CLASS#,
      #STORAGE_TYPE_CLASS#,
      #MANAGE_TYPE_ORDER#,
      #MANAGE_TYPE_FLAG#,
      #MANAGE_TYPE_PROPERTY#
      )
      <!--<selectKey  resultClass="int" type="post" property="MANAGE_TYPE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="MANAGE_TYPE_UPDATE" parameterClass="MANAGE_TYPE">
      Update MANAGE_TYPE Set
      manage_type_id=#MANAGE_TYPE_ID#,
      manage_type_code=#MANAGE_TYPE_CODE#,
      manage_type_name=#MANAGE_TYPE_NAME#,
      manage_type_inout=#MANAGE_TYPE_INOUT#,
      manage_type_group=#MANAGE_TYPE_GROUP#,
      manage_type_class=#MANAGE_TYPE_CLASS#,
      storage_type_class=#STORAGE_TYPE_CLASS#,
      manage_type_order=#MANAGE_TYPE_ORDER#,
      manage_type_flag=#MANAGE_TYPE_FLAG#,
      manage_type_property=#MANAGE_TYPE_PROPERTY#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					manage_type_id=#MANAGE_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="MANAGE_TYPE_DELETE" parameterClass="int">
			Delete From MANAGE_TYPE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_type_id=#MANAGE_TYPE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>