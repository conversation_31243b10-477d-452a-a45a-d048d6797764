﻿/***************************************************************************
 * 
 *       功能：     任务列表实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// MANAGE_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MANAGE_LIST
	{
		public MANAGE_LIST()
		{
			
		}
		
		private int _manage_list_id;
		private int _storage_list_id;
		private int _plan_list_id;
		private int _manage_id;
		private int _goods_id;
		private System.Decimal _manage_list_quantity;
		private string _manage_list_remark;
		private string _box_barcode;
		private string _goods_property1;
		private string _goods_property2;
		private string _goods_property3;
		private string _goods_property4;
		private string _goods_property5;
		private string _goods_property6;
		private string _goods_property7;
		private string _goods_property8;
        private int _storage_lock_id;
        private string _detail_flag;

		private string _origin_entry_time;

		///<sumary>
		/// 任务列表编号
		///</sumary>
		[DataMember]
		public int MANAGE_LIST_ID
		{
			get{return _manage_list_id;}
			set{_manage_list_id = value;}
		}
		///<sumary>
		/// 库存列表编号
        ///</sumary>
        [DataMember]
		public int STORAGE_LIST_ID
		{
			get{return _storage_list_id;}
			set{_storage_list_id = value;}
		}
		///<sumary>
		/// 计划列表编号
        ///</sumary>
        [DataMember]
		public int PLAN_LIST_ID
		{
			get{return _plan_list_id;}
			set{_plan_list_id = value;}
		}
		///<sumary>
		/// 任务编号
        ///</sumary>
        [DataMember]
		public int MANAGE_ID
		{
			get{return _manage_id;}
			set{_manage_id = value;}
		}
		///<sumary>
		/// 物料编号
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}

		///<sumary>
		/// 数量
        ///</sumary>
        [DataMember]
		public System.Decimal MANAGE_LIST_QUANTITY
		{
			get{return _manage_list_quantity;}
			set{_manage_list_quantity = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string MANAGE_LIST_REMARK
		{
			get{return _manage_list_remark;}
			set{_manage_list_remark = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY1
		{
			get{return _goods_property1;}
			set{_goods_property1 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY2
		{
			get{return _goods_property2;}
			set{_goods_property2 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY3
		{
			get{return _goods_property3;}
			set{_goods_property3 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY4
		{
			get{return _goods_property4;}
			set{_goods_property4 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY5
		{
			get{return _goods_property5;}
			set{_goods_property5 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY6
		{
			get{return _goods_property6;}
			set{_goods_property6 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY7
		{
			get{return _goods_property7;}
			set{_goods_property7 = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string GOODS_PROPERTY8
		{
			get{return _goods_property8;}
			set{_goods_property8 = value;}
		}

        ///<sumary>
		/// 
        ///</sumary>
        [DataMember]
        public int STORAGE_LOCK_ID
        {
            get { return _storage_lock_id; }
            set { _storage_lock_id = value; }
        }

        ///<sumary>
		/// 
        ///</sumary>
        [DataMember]
        public string DETAIL_FLAG
        {
            get { return _detail_flag; }
            set { _detail_flag = value; }
        }

		///<sumary>
		/// 原始入库时间 2020-10-15 19:32:25
		///</sumary>
		[DataMember]
		public string ORIGIN_ENTRY_TIME
		{
			get { return _origin_entry_time; }
			set { _origin_entry_time = value; }
		}
		
	}
}
