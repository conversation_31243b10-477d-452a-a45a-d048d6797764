﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_TEMPLETE_LIST 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_TEMPLATE_LIST
	{
        public GOODS_TEMPLATE_LIST()
		{
			
		}
		
		private int _goods_template_list_id;
		private int _goods_template_id;
		private int _goods_id;
		private System.Decimal _goods_template_quantity;
		private string _goods_template_remark;
		
		///<sumary>
		/// 物料模板列表编号
        ///</sumary>
        [DataMember]
		public int GOODS_TEMPLATE_LIST_ID
		{
			get{return _goods_template_list_id;}
			set{_goods_template_list_id = value;}
		}
		///<sumary>
		/// 物料模板编号
        ///</sumary>
        [DataMember]
		public int GOODS_TEMPLATE_ID
		{
			get{return _goods_template_id;}
			set{_goods_template_id = value;}
		}
		///<sumary>
		/// 物料编码
        ///</sumary>
        [DataMember]
		public int GOODS_ID
		{
			get{return _goods_id;}
			set{_goods_id = value;}
		}
		///<sumary>
		/// 数量
        ///</sumary>
        [DataMember]
		public System.Decimal GOODS_TEMPLATE_QUANTITY
		{
			get{return _goods_template_quantity;}
			set{_goods_template_quantity = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string GOODS_TEMPLATE_REMARK
		{
			get{return _goods_template_remark;}
			set{_goods_template_remark = value;}
		}
	}
}
