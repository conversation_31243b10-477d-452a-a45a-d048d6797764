﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Apply
{
    class ApplyAllocateCellThirdFloor : ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCellThirdFloor.ApplyHandle():开始处理申请_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mIO_CONTROL_APPLY.STOCK_BARCODE);

                if(mSTORAGE_MAIN != null && mMANAGE_MAIN == null)
                {
                    //TODO:执行入库流程
                    if (mSTORAGE_MAIN.IS_EXCEPTION == "1")
                    {
                        bResult = false;
                        sResult = string.Format("箱条码[{0}]库存已被标记为异常", mSTORAGE_MAIN.STOCK_BARCODE);
                        return bResult;
                    }

                    mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                    //mMANAGE_MAIN.MANAGE_OPERATOR = string.Format("{0}申请", mIO_CONTROL_APPLY.DEVICE_CODE);
                    mMANAGE_MAIN.MANAGE_OPERATOR = mSTORAGE_MAIN.STORAGE_PICK_OPERATOR;
                    mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                    mMANAGE_MAIN.START_CELL_ID = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE).CELL_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                    mMANAGE_MAIN.END_CELL_ID = 0;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    string manageLevel = string.Empty;
                    mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("ApplyTaskLevel", out manageLevel) ? manageLevel : "0";

                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();   //上架不需要使用TASK_SOURCE判断接口

                    //校验是否为齐套箱
                    if (mSTORAGE_MAIN.CELL_MODEL == Enum.CellModel.KitBox.ToString("d") && mSTORAGE_MAIN.KITBOX_UP_COMPLETE != "1")
                    {
                        mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageKitUp.ToString();
                    }
                    else
                    {
                        bResult = false;
                        sResult = string.Format(@"申请条码对应库存为非齐套箱，请检查箱子库存信息");
                        return bResult;
                    }

                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";

                    bool wsNoticeWcs = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE == "3";
                    Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;
                    bResult = this._S_ManageService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, true, true, wsNoticeWcs, false }, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                }
                else if(mSTORAGE_MAIN != null && mMANAGE_MAIN != null)
                {
                    //有库存，有任务
                    bResult = false;
                    sResult = string.Format("申请条码_[{0}]同时存在库存和任务，请根据箱子实际情况将多余的任务删除", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    return bResult;
                }
                else if(mSTORAGE_MAIN == null)
                {
                    //无库存
                    bResult = false;
                    sResult = string.Format("申请条码_[{0}]同时不存在库存，请检查箱子库存", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format(@"处理申请时发生异常 {0}_ApplyTaskIn", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, bResult, bResult ? "" : sResult);
                }

                this._S_SystemService.CreateSysLog(Enum.LogThread.Apply, string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), bResult ? Enum.LOG_LEVEL.Information : Enum.LOG_LEVEL.Error, string.Format("ApplyAllocateCellThirdFloor.ApplyHandle():结束处理申请_条码[{0}]_处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败 ", sResult));
            }

            return bResult;
        }
    }
}
