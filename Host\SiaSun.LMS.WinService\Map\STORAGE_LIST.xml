﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="STORAGE_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="STORAGE_LIST" type="SiaSun.LMS.Model.STORAGE_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="STORAGE_LIST">
			<result property="STORAGE_LIST_ID" column="storage_list_id" />
			<result property="STORAGE_ID" column="storage_id" />
			<result property="PLAN_LIST_ID" column="plan_list_id" />
			<result property="STORAGE_LIST_QUANTITY" column="storage_list_quantity" />
			<result property="GOODS_ID" column="goods_id" />
			<result property="ENTRY_TIME" column="entry_time" />
			<result property="UPDATE_TIME" column="update_time" />
			<result property="STORAGE_LIST_REMARK" column="storage_list_remark" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_PROPERTY1" column="goods_property1" />
			<result property="GOODS_PROPERTY2" column="goods_property2" />
			<result property="GOODS_PROPERTY3" column="goods_property3" />
			<result property="GOODS_PROPERTY4" column="goods_property4" />
			<result property="GOODS_PROPERTY5" column="goods_property5" />
			<result property="GOODS_PROPERTY6" column="goods_property6" />
			<result property="GOODS_PROPERTY7" column="goods_property7" />
			<result property="GOODS_PROPERTY8" column="goods_property8" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="STORAGE_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  storage_list_id,
				  storage_id,
				  plan_list_id,
				  storage_list_quantity,
				  goods_id,
				  entry_time,
				  update_time,
				  storage_list_remark,
				  box_barcode,
				  goods_property1,
				  goods_property2,
				  goods_property3,
				  goods_property4,
				  goods_property5,
				  goods_property6,
				  goods_property7,
				  goods_property8
			From STORAGE_LIST
		</select>
		
		<select id="STORAGE_LIST_SELECT_BY_ID" parameterClass="int" extends = "STORAGE_LIST_SELECT" resultMap="SelectResult">		
			<dynamic prepend="WHERE">
				<isParameterPresent>
					storage_list_id=#STORAGE_LIST_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="STORAGE_LIST_SELECT_BY_STORAGE_ID" parameterClass="int" extends = "STORAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_id=#STORAGE_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LIST_SELECT_BY_GOODS_PROPERTY" parameterClass="int" extends = "STORAGE_LIST_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          $GOODS_PROPERTY$
        </isParameterPresent>
      </dynamic>
    </select>

				
		<insert id="STORAGE_LIST_INSERT" parameterClass="STORAGE_LIST">
      Insert Into STORAGE_LIST (
      storage_list_id,
      storage_id,
      plan_list_id,
      storage_list_quantity,
      goods_id,
      entry_time,
      update_time,
      storage_list_remark,
      box_barcode,
      goods_property1,
      goods_property2,
      goods_property3,
      goods_property4,
      goods_property5,
      goods_property6,
      goods_property7,
      goods_property8
      )Values(
      #STORAGE_LIST_ID#,
      #STORAGE_ID#,
      #PLAN_LIST_ID#,
      #STORAGE_LIST_QUANTITY#,
      #GOODS_ID#,
      #ENTRY_TIME#,
      #UPDATE_TIME#,
      #STORAGE_LIST_REMARK#,
      #BOX_BARCODE#,
      #GOODS_PROPERTY1#,
      #GOODS_PROPERTY2#,
      #GOODS_PROPERTY3#,
      #GOODS_PROPERTY4#,
      #GOODS_PROPERTY5#,
      #GOODS_PROPERTY6#,
      #GOODS_PROPERTY7#,
      #GOODS_PROPERTY8#
      )
      <!--<selectKey  resultClass="int" type="post" property="STORAGE_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="STORAGE_LIST_UPDATE" parameterClass="STORAGE_LIST">
      Update STORAGE_LIST Set
      storage_list_id=#STORAGE_LIST_ID#,
      storage_id=#STORAGE_ID#,
      plan_list_id=#PLAN_LIST_ID#,
      storage_list_quantity=#STORAGE_LIST_QUANTITY#,
      goods_id=#GOODS_ID#,
      entry_time=#ENTRY_TIME#,
      update_time=#UPDATE_TIME#,
      storage_list_remark=#STORAGE_LIST_REMARK#,
      box_barcode=#BOX_BARCODE#,
      goods_property1=#GOODS_PROPERTY1#,
      goods_property2=#GOODS_PROPERTY2#,
      goods_property3=#GOODS_PROPERTY3#,
      goods_property4=#GOODS_PROPERTY4#,
      goods_property5=#GOODS_PROPERTY5#,
      goods_property6=#GOODS_PROPERTY6#,
      goods_property7=#GOODS_PROPERTY7#,
      goods_property8=#GOODS_PROPERTY8#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					storage_list_id=#STORAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="STORAGE_LIST_DELETE" parameterClass="int">
			Delete From STORAGE_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					storage_list_id=#STORAGE_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>