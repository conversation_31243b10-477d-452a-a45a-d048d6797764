﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// GOODS_TEMPLETE_LIST
	/// </summary>
	public class P_GOODS_TEMPLATE_LIST : P_Base_House
	{
		public P_GOODS_TEMPLATE_LIST ()
		{
			//
			// TODO: 此处添加GOODS_TEMPLETE_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<GOODS_TEMPLATE_LIST> GetList()
		{
			return ExecuteQueryForList<GOODS_TEMPLATE_LIST>("GOODS_TEMPLATE_LIST_SELECT",null);
		}


        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<GOODS_TEMPLATE_LIST> GetList(int TEMPLATE_ID)
        {
            return ExecuteQueryForList<GOODS_TEMPLATE_LIST>("GOODS_TEMPLATE_LIST_SELECT_BY_TEMPLATE_ID", TEMPLATE_ID);
        }


		/// <summary>
		/// 新建
		/// </summary>
		public int Add(GOODS_TEMPLATE_LIST goods_template_list)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("GOODS_TEMPLETE_LIST");
                goods_template_list.GOODS_TEMPLATE_LIST_ID = id;
            }

            return ExecuteInsert("GOODS_TEMPLATE_LIST_INSERT",goods_template_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(GOODS_TEMPLATE_LIST goods_template_list)
		{
			return ExecuteUpdate("GOODS_TEMPLATE_LIST_UPDATE",goods_template_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public GOODS_TEMPLATE_LIST GetModel(System.Int32 GOODS_TEMPLATE_LIST_ID)
		{
			return ExecuteQueryForObject<GOODS_TEMPLATE_LIST>("GOODS_TEMPLATE_LIST_SELECT_BY_ID",GOODS_TEMPLATE_LIST_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GOODS_TEMPLATE_LIST_ID)
		{
			return ExecuteDelete("GOODS_TEMPLATE_LIST_DELETE",GOODS_TEMPLATE_LIST_ID);
		}
		

	}
}
