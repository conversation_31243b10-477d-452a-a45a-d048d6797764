﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Reflection;

namespace SiaSun.LMS.WPFClient.PRO
{
    /// <summary>
    /// PRO_MANAGE.xaml 的交互逻辑
    /// </summary>
    public partial class PRO_MANAGE : AvalonDock.DocumentContent
    {
        public PRO_MANAGE()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 加载功能菜单
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //添加按钮
            this.AddMenuButton("panelMenu");
        }

        /// <summary>
        /// 添加按钮
        /// </summary>
        private void AddMenuButton(string MENU_CODE)
        {
            //try
            //{
            //    IList<Model.SYS_MENU_UNIT> listMENU_UNIT = MainApp._I_SystemService.MENU_UNIT_GetList(MENU_CODE);
            //    var queryGroup = from menu in listMENU_UNIT orderby menu.MENU_UNIT_GROUP group menu by menu.MENU_UNIT_GROUP into a select a;
            //    foreach (var q in queryGroup)
            //    {  
            //        foreach (Model.SYS_MENU_UNIT mMENU_UNIT in listMENU_UNIT.Where(r => r.MENU_UNIT_GROUP == q.Key).OrderBy(r => r.MENU_UNIT_ORDER))
            //        {
            //            Button btn = new Button();
            //            btn.MinWidth = 120;
            //            btn.Content = mMENU_UNIT.MENU_UNIT_NAME;
            //            btn.Style = (Style)MainApp.GetStyleResource("styleDefaultButton");
            //            btn.Margin = new Thickness(2,5,2,5);
            //            this.panelMenu.Children.Add(btn);
            //        }

            //        //添加分割
            //        TextBlock tbk = new TextBlock();
            //        tbk.Height = 1;
            //        tbk.Margin = new Thickness(10);
            //        tbk.Background = Brushes.DarkBlue;
            //        this.panelMenu.Children.Add(tbk);
            //    }
            //}
            //catch (Exception ex)
            //{
            //    MainApp._MessageDialog.ShowException(ex);
            //}
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Border_Click(object sender, RoutedEventArgs e)
        {
            //Button btn = e.OriginalSource as Button;
            //if (btn != null)
            //{
            //    try
            //    {
            //        MainWindow.mainWin.Cursor = Cursors.Wait;

            //        //显示说明
            //        this.grpManage.Header = string.Format(this.grpManage.Tag.ToString(), btn.Content.ToString());
            //        //清空控件
            //        this.gdManage.Children.Clear();

            //        //根据名称获得菜单单元对象
            //        Model.SYS_MENU_UNIT mMENU_UNIT = MainApp._I_SystemService.MENU_UNIT_GetModel_UNIT_NAME(btn.Content.ToString());
            //        if (mMENU_UNIT != null)
            //        {
            //            string strControlType = mMENU_UNIT.MENU_UNIT_CONTROL;
            //            string strParam = mMENU_UNIT.MENU_UNIT_PARAM;

            //            //获得控件类型
            //            Type typeControl = Type.GetType(strControlType);
            //            if (typeControl != null)
            //            {
            //                string[] arPara = strParam.Split(',');
            //                Type[] types = new Type[arPara.Length];
            //                for (int i = 0; i < types.Length; i++)
            //                {
            //                    types[i] = typeof(string);
            //                }
            //                ConstructorInfo cInfo = typeControl.GetConstructor(types);
            //                if (cInfo != null)
            //                {
            //                    object objUC = cInfo.Invoke(arPara);
            //                    if (objUC != null)
            //                    {
            //                        this.gdManage.Children.Add(objUC as UIElement);
            //                    }
            //                }
            //            }
            //        }
            //    }
            //    catch (Exception ex)
            //    {
            //        MainApp._MessageDialog.ShowException(ex);
            //    }
            //    finally
            //    {
            //        MainWindow.mainWin.Cursor = Cursors.Arrow;
            //    }
            //}
        }



    }
}
