﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace SSLMS.MobileUI.uc
{
    public partial class ucCONTROL_SEND : UserControl
    {
        public ucCONTROL_SEND()
        {
            InitializeComponent();

            this.Init();
        }

        /// <summary>
        /// 库存列表绑定
        /// </summary>
        private void STORAGE_LIST_Bind()
        {
            string STOCK_BARCODE = this.txtSTOCK_BARCODE.Text.ToString().Trim();

            this.uclSTORAGE_LIST.listXml = "V_ST_STORAGE_LIST";

            this.uclSTORAGE_LIST.listTable = "V_ST_STORAGE_LIST";

            this.uclSTORAGE_LIST.listWhere = string.Format(" AND STOCK_BARCODE = '{0}'", STOCK_BARCODE);

            this.uclSTORAGE_LIST.colGroup = "GOODS_TYPE_ID";

            this.uclSTORAGE_LIST.colSplit = "GOODS_PROPERTY";

            this.uclSTORAGE_LIST.Init();
        }

        private void Init()
        {

            this.cmbCELL_MODEL_Bind();

            this.cmbStation_Bind();

            this.txtSTOCK_BARCODE.Focus();    

            this.SET_CONTROL(true);

            this.ucCell1.Init();

            this.ucCell1.EmptySelect = true;

            this.cbCELL_MODEL.SelectedValue = "0";

            

            this.ucCell1.CellDoubleClick += new ucCell.CELLDoubleClickHandler(ucCell1_CellDoubleClick);
        }

        void ucCell1_CellDoubleClick(WH_CELL mST_CELL)
        {
            this.txtRow.Text = mST_CELL._cell_z.ToString().PadLeft(2, '0');

            this.txtLine.Text = mST_CELL._cell_x.ToString().PadLeft(2, '0');

            this.txtFloor.Text = mST_CELL._cell_y.ToString().PadLeft(2, '0');

            this.cbCELL_MODEL.SelectedValue = mST_CELL._cell_model;

            this.SET_CONTROL(true);
        }

        private void cmbCELL_MODEL_Bind()
        {
            try
            {
                this.cbCELL_MODEL.ValueMember = "value";

                cbCELL_MODEL.DisplayMember = "name";

                cbCELL_MODEL.DataSource = Program._I_SystemService.GetD1("CELL_MODEL");
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void cmbStation_Bind()
        {
            try
            {
                this.cmbStation.ValueMember = "CELL_CODE";

                cmbStation.DisplayMember = "CELL_NAME";

                System.Collections.Hashtable ht = new System.Collections.Hashtable();

                ht.Add("CELL_CODE", "0");

                ht.Add("CELL_NAME", "-");

                cmbStation.DataSource = SSLMS.MobileUI.Common.AddAll(Program._I_WHService.CELL_GetList_1(string.Empty, string.Empty, SSLMS.Model.CELL_TYPE.station.ToString(), string.Empty, SSLMS.Model.CELL_INOUT.IN.ToString()), ht);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }

        }
        private void btnSelect_Click(object sender, EventArgs e)
        {
            this.SET_CONTROL(this.ucCell1.Visible);
        }
        private void SET_CONTROL(bool bVisible)
        {

            this.ucCell1.Visible = !bVisible;

            this.panel1.Visible = bVisible;

            this.uclSTORAGE_LIST.Visible = bVisible;
        }

        private void txtSTOCK_BARCODE_KeyDown(object sender, KeyEventArgs e)
        {
            if (Keys.Enter != e.KeyCode)
            {
                return;
            }

            this.STORAGE_LIST_Bind();
        }

        private void btnDownload_Click(object sender, EventArgs e)
        {
            int MANAGE_ID = 0;

            string sResult = string.Empty;

            if (null == this.cmbStation.SelectedValue || this.cmbStation.SelectedValue.ToString() == "0")
            {

                MessageBox.Show( "Please Select Loading Station!");

                return;
            }

            string START_CELL_CODE = this.cmbStation.SelectedValue.ToString();

            string END_CELL_CODE = string.Format("{0}-{1}-{2}", this.txtRow.Text.TrimEnd(), this.txtLine.Text.TrimEnd(), this.txtFloor.Text.TrimEnd());

            string STOCK_BARCODE = this.txtSTOCK_BARCODE.Text.Trim();

            if (string.IsNullOrEmpty(STOCK_BARCODE))
            {
                this.txtSTOCK_BARCODE.Focus();

                this.txtSTOCK_BARCODE.SelectAll();

                MessageBox.Show("Please input Pallet Barcode!");

                return;
            }

            if (string.IsNullOrEmpty(txtRow.Text.TrimEnd()) || string.IsNullOrEmpty(txtLine.Text.TrimEnd()) || string.IsNullOrEmpty(txtFloor.Text.TrimEnd()))
            {

                MessageBox.Show( "Please Select Storage Cell!");

                return;
            }


            bool bResult = Program._I_ManageService.ManageCreateLoadingMoveTask(Program._USER, START_CELL_CODE, END_CELL_CODE, STOCK_BARCODE, this.cbCELL_MODEL.SelectedValue.ToString(), true,true, out sResult, out MANAGE_ID);

            if (bResult)
            {


                this.txtSTOCK_BARCODE.Text = string.Empty ;

                this.txtRow.Text = string.Empty;

                this.txtLine.Text = string.Empty;

                this.txtFloor.Text = string.Empty;

                this.cmbStation.SelectedValue = "0";

                this.cbCELL_MODEL.SelectedValue = "0";

                this.STORAGE_LIST_Bind();

            }
            else
            {
                MessageBox.Show( sResult);
            }

            this.txtSTOCK_BARCODE.Focus();
        }

    }
}
