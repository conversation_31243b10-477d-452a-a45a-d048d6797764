<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type ProgressBar}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ProgressBar}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="IndeterminateOn" RepeatBehavior="Forever">
                            <DoubleAnimationUsingKeyFrames Storyboard.TargetName="IndeterminateGradientFill" Storyboard.TargetProperty="(Shape.Fill).(Brush.Transform).(TransformGroup.Children)[0].X" RepeatBehavior="Forever">
                                <SplineDoubleKeyFrame KeyTime="0" Value="0" />
                                <SplineDoubleKeyFrame KeyTime="00:00:.5" Value="20" />
                            </DoubleAnimationUsingKeyFrames>
                            <ObjectAnimationUsingKeyFrames BeginTime="00:00:00" Duration="00:00:00.0010000" Storyboard.TargetName="IndeterminateRoot" Storyboard.TargetProperty="(UIElement.Visibility)">
                                <DiscreteObjectKeyFrame KeyTime="00:00:00" Value="{x:Static Visibility.Visible}" />
                            </ObjectAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Border x:Name="PART_Track" BorderThickness="1" CornerRadius="3" Opacity="0.825">
                            <Border.Background>
                                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                    <GradientStop Color="#FFFFFFFF" />
                                    <GradientStop Color="#FFD8D8D8" Offset="0.327" />
                                    <GradientStop Color="#FFDADADA" Offset="0.488" />
                                    <GradientStop Color="#FFBEBEBE" Offset="0.539" />
                                    <GradientStop Color="#FFD6D6D6" Offset="0.77" />
                                    <GradientStop Color="#FFFFFFFF" Offset="1" />
                                </LinearGradientBrush>
                            </Border.Background>
                            <Border.BorderBrush>
                                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                    <GradientStop Color="#FFBBBBBB" Offset="0" />
                                    <GradientStop Color="#FF7E7E7E" Offset="1" />
                                </LinearGradientBrush>
                            </Border.BorderBrush>
                        </Border>

                        <Rectangle x:Name="PART_Indicator" Margin="1" RadiusX="1.5" RadiusY="1.5" HorizontalAlignment="Left" Opacity="0.83" Fill="{DynamicResource ProgressBarIndicatorBrush}"/>
                        <Grid x:Name="IndeterminateRoot" Visibility="Collapsed">
                            <Rectangle x:Name="IndeterminateSolidFill" Margin="1" Fill="#FF6EA4FD" RadiusX="2" RadiusY="2" Width="Auto" />
                            <Rectangle x:Name="ProgressBarRootGradient" Margin="1" Panel.ZIndex="1" RadiusX="1.5" RadiusY="1.5">
                                <Rectangle.Fill>
                                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                        <GradientStop Color="#F6BCD5FF" Offset="0.046" />
                                        <GradientStop Color="#96D4E4FF" Offset="0.18" />
                                        <GradientStop Color="#4FFFFFFF" Offset="0.512" />
                                        <GradientStop Color="#00D6D6D6" Offset="0.521" />
                                        <GradientStop Color="#BABCD5FF" Offset="1" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <Rectangle x:Name="IndeterminateGradientFill" Margin="1" StrokeThickness="1" RadiusX="2" RadiusY="2" Opacity="0.7">
                                <Rectangle.Fill>
                                    <LinearGradientBrush EndPoint="0,1" StartPoint="20,1" MappingMode="Absolute" SpreadMethod="Repeat">
                                        <LinearGradientBrush.Transform>
                                            <TransformGroup>
                                                <TranslateTransform X="0" />
                                                <SkewTransform AngleX="-10" />
                                            </TransformGroup>
                                        </LinearGradientBrush.Transform>
                                        <GradientStop Color="#FFBCD5FF" Offset="0.088" />
                                        <GradientStop Color="#006EA4FD" Offset="0.475" />
                                        <GradientStop Color="#FFBCD5FF" Offset="0.899" />
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                        </Grid>
                        <Border x:Name="DisabledVisualElement" IsHitTestVisible="false" Background="#A5FFFFFF" BorderBrush="#66FFFFFF" BorderThickness="1" Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" TargetName="DisabledVisualElement" Value="1" />
                        </Trigger>
                        <Trigger Property="IsIndeterminate" Value="True">
                            <Trigger.ExitActions>
                                <StopStoryboard BeginStoryboardName="IndeterminateOn_BeginStoryboard"/>
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard x:Name="IndeterminateOn_BeginStoryboard" Storyboard="{StaticResource IndeterminateOn}" />
                            </Trigger.EnterActions>
                            <Setter Property="Visibility" TargetName="PART_Track" Value="Collapsed" />
                            <Setter Property="Visibility" TargetName="PART_Indicator" Value="Collapsed" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>