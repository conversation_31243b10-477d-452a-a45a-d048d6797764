﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 余箱回库任务下发
    /// </summary>
    public class BoxReturnInTask : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _boxNo = string.Empty;                  //箱号
            private string _boxType = string.Empty;                //箱子类型
            private string _fromPosition = string.Empty;           //起点位置
            private string _uniqueCode = string.Empty;             //唯一码
            private string _interfaceType = string.Empty;          //接口类型
            private string _interfaceSource = string.Empty;        //接口来源
            private string _boxColor = string.Empty;               //箱子颜色
            public List<FirstDetails> firstDetails { get; set; }       //一级明细

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 箱子颜色
            /// </summary>
            public string boxColor
            {
                get { return _boxColor; }
                set { _boxColor = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _gridNo = string.Empty;    //格子号
            private string _itemCode = string.Empty;   //物料号
            private string _quantity = string.Empty;  //数量

            private string _ertryTime = string.Empty;     //原始入库时间 2020-10-15 18:53:20
            private string _exceptionFlag = string.Empty;     //原始异常标记 2021-05-13 09:31:07

            private string _invFollow = string.Empty;           //质量追溯码  2025-06-26新增

            /// <summary>
            /// 格子号
            /// </summary>
            public string gridNo
            {
                get { return _gridNo; }
                set { _gridNo = value; }
            }
            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }

            /// <summary>
            /// 最初入库时间 2020-10-15 18:52:45
            /// </summary>
            public string ertryTime
            {
                get { return _ertryTime; }
                set { _ertryTime = value; }
            }

            /// <summary>
            /// 原始异常标记 2021-05-13 09:31:07
            /// </summary>
            public string exceptionFlag
            {
                get { return _exceptionFlag; }
                set { _exceptionFlag = value; }
            }

            /// <summary>
            /// 质量追溯码  2025-06-26新增
            /// </summary>
            public string invFollow
            {
                get { return _invFollow; }
                set { _invFollow = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.boxNo) || string.IsNullOrEmpty(taskInfo.boxType) || string.IsNullOrEmpty(taskInfo.fromPosition) || string.IsNullOrEmpty(taskInfo.uniqueCode) || taskInfo.firstDetails.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:入参存在空值");
                    return bResult;
                }

                string validRegexString = string.Empty;
                if(this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex",out validRegexString) &&
                    !Common.RegexValid.IsValidate(taskInfo.boxNo, validRegexString))
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:箱条码验证失败 传入值-{0}", taskInfo.boxNo);
                    return bResult;
                }

                if (!new string[] { "1", "2", "3", "4", "6" }.Contains(taskInfo.boxType))
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:箱类型不在预期范围内 传入值-{0}", taskInfo.boxType);
                    return bResult;
                }

                if (taskInfo.firstDetails.GroupBy(r => r.gridNo).Count() != taskInfo.firstDetails.GroupBy(r => r.itemCode).Count())
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:请确保不同物料放置在不同格子中");
                    return bResult;
                }
                int maxGridNo = 0;
                int boxType = 0;
                if (int.TryParse(taskInfo.firstDetails.Max(r => r.gridNo), out maxGridNo) &&
                    int.TryParse(taskInfo.boxType, out boxType) &&
                    maxGridNo > boxType)
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:格子号有误或者最大格子号大于箱容量");
                    return bResult;
                }

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(taskInfo.fromPosition);
                if (mWH_CELL_START == null)
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:传入起始位置有误 传入值_{0}", taskInfo.fromPosition);
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelRelateCode(taskInfo.uniqueCode);
                if (mMANAGE_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("CreateBoxIn.NotifyMethod:传入唯一码已经存在任务 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                var goodsCheck = taskInfo.firstDetails.GroupBy(t => t.itemCode);
                if (goodsCheck.Count() != taskInfo.firstDetails.Count)
                {
                    bResult = false;
                    outJson = string.Format("BoxReturnInTask.NotifyMethod:传入组箱信息中存在同种物料在不同格子 物料种类数-{0} 一级明细数-{1}", goodsCheck.Count(), taskInfo.firstDetails.Count);
                    return bResult;
                }

                //wdz add 2018-01-14 如果任务的起点没有固定条码扫描器 则指定终点到四楼货位申请站台
                bool isAutoSendControl = false;
                bool isSendControlIndependent = false;
                string endCellCode = string.Empty;
                string standardGoodsOutStation = string.Empty;

                if (mWH_CELL_START.CELL_PROPERTY == "NoScaner")
                {
                    System.Data.DataTable dtWaittingTask = this.GetList(string.Format("select 0 from IO_CONTROL where START_DEVICE_CODE ='{0}' and CONTROL_STATUS in ('0','7') ", mWH_CELL_START.CELL_CODE));
                    if (dtWaittingTask != null && dtWaittingTask.Rows.Count > 0)
                    {
                        bResult = false;
                        outJson = string.Format("BoxReturnInTask.NotifyMethod:当前输送起点位置存在等待执行任务，请稍后再试 起点位置-{0}", taskInfo.fromPosition);
                        return bResult;
                    }

                    if (!this._S_SystemService.GetSysParameter("StandardGoodsInStation", out standardGoodsOutStation))
                    {
                        bResult = false;
                        outJson = string.Format("BoxReturnInTask.NotifyMethod:未能获取标准件出库站台_Key[StandardGoodsInStation]");
                        return bResult;
                    }
                    if (mWH_CELL_START.CELL_CODE == standardGoodsOutStation)
                    {
                        //判断如果是标准件入库站台入库，则下达控制任务
                        isAutoSendControl = true;
                    }
                    else
                    {
                        bResult = this._S_SystemService.GetSysParameter("MoveToFloor5EndStation", out endCellCode);
                        Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(endCellCode);
                        if (!bResult || mWH_CELL_END == null)
                        {
                            bResult = false;
                            outJson = string.Format("BoxReturnInTask.NotifyMethod:起点位置没有固定条码扫描器且系统未找到货位申请站台 起点位置-{0}", taskInfo.fromPosition);
                            return bResult;
                        }

                        isAutoSendControl = false;
                        isSendControlIndependent = true;
                    }
                }
                else if (mWH_CELL_START != null && mWH_CELL_START.CELL_PROPERTY == "Scaner")
                {
                    isAutoSendControl = false;
                    isSendControlIndependent = false;
                }


                mMANAGE_MAIN = new Model.MANAGE_MAIN();
                mMANAGE_MAIN.CELL_MODEL = taskInfo.boxType;
                mMANAGE_MAIN.END_CELL_ID = 0;
                mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                string manageLevel = string.Empty;
                mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("DefaultTaskLevel", out manageLevel) ? manageLevel : "0";
                mMANAGE_MAIN.MANAGE_OPERATOR = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageIn.ToString();
                mMANAGE_MAIN.PLAN_ID = 0;
                mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                mMANAGE_MAIN.START_CELL_ID = mWH_CELL_START.CELL_ID;
                mMANAGE_MAIN.STOCK_BARCODE = taskInfo.boxNo;
                mMANAGE_MAIN.MANAGE_SOURCE = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mMANAGE_MAIN.MANAGE_RELATE_CODE = taskInfo.uniqueCode;

                List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                foreach (FirstDetails item in taskInfo.firstDetails)
                {
                    if (string.IsNullOrEmpty(item.gridNo) || string.IsNullOrEmpty(item.itemCode) || string.IsNullOrEmpty(item.quantity))
                    {
                        bResult = false;
                        outJson = string.Format("BoxReturnInTask.NotifyMethod:入参存在空值");
                        return bResult;
                    }

                    Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(item.itemCode);
                    if (mGOODS_MAIN == null)
                    {
                        bResult = false;
                        outJson = string.Format("BoxReturnInTask.NotifyMethod:传入的物料不存在 物料编码_{0}", item.itemCode);
                        return bResult;
                    }

                    double quantity = 0;
                    if (!double.TryParse(item.quantity, out quantity))
                    {
                        bResult = false;
                        outJson = string.Format("BoxReturnInTask.NotifyMethod:传入的数量有误 数量值_{0}", item.quantity);
                        return bResult;
                    }

                    Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                    //mMANAGE_LIST.BOX_BARCODE = string.Format("{0}-{1}", taskInfo.boxNo, item.gridNo);
                    mMANAGE_LIST.BOX_BARCODE = item.gridNo;
                    mMANAGE_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;
                    mMANAGE_LIST.MANAGE_LIST_QUANTITY = Convert.ToDecimal(quantity);
                    //保存原始入库时间 2020-10-15 19:16:53
                    DateTime dateTime;
                    if (DateTime.TryParse(item.ertryTime, out dateTime))
                    {
                        mMANAGE_LIST.ORIGIN_ENTRY_TIME = item.ertryTime;
                    }
                    //保存原始异常标记 2021-05-13 10:22:16
                    mMANAGE_LIST.GOODS_PROPERTY2 = item.exceptionFlag;
                    mMANAGE_LIST.GOODS_PROPERTY5 = item.invFollow;      //质量追溯码 2025-06-26

                    lsMANAGE_LIST.Add(mMANAGE_LIST);
                }

                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, true, true, false, isAutoSendControl }, out outJson);

                if (bResult && isSendControlIndependent)
                {
                    //下达到四楼货位分配扫描头的不关联管理任务的Control
                    bResult = this._S_ManageService.ControlCreate(3, taskInfo.boxNo, "1", taskInfo.fromPosition, "1", endCellCode, "5", out outJson);
                    if(!bResult)
                    {
                        bool bTemp = true;
                        string strTemp = string.Empty;
                        //下达Control失败后删除任务
                        bTemp = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCancel", new object[] { mMANAGE_MAIN.MANAGE_ID }, out strTemp);
                        //log
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("BoxReturnInTask.NotifyMethod():管理任务生成成功_下达非关联管理任务的Control任务失败_{0}_取消管理任务{1} {2}", outJson, bTemp ? "成功" : "失败 ", strTemp));
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("BoxReturnInTask.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
