﻿/***************************************************************************
 * 
 *       功能：     货位规划实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// WH_DESCRIPTION 
	/// </summary>
    [Serializable]
    [DataContract]
	public class WH_DESCRIPTION
	{
		public WH_DESCRIPTION()
		{
			
		}
        private int _warehouse_id;
		private int _description_id;
		private int _area_id;
		private int _logic_id;
		private int _start_z;
		private int _end_z;
		private int _start_x;
		private int _end_x;
		private int _start_y;
		private int _end_y;
		private string _device_code;
		private string _device_name;
		private string _lane_way;
		private string _shelf_type;
		private string _shelf_neighbour;
		private string _cell_model;
		private string _cell_logical_name;
		private string _cell_inout;
		private string _cell_type;
		private string _cell_storage_type;
		private string _cell_fork_type;
		private string _cell_fork_count;
        private int _cell_width;
        private int _cell_height;
		private string _description_flag;


        ///<sumary>
        /// 货位定义编号
        ///</sumary>
        [DataMember]
        public int WAREHOUSE_ID
        {
            get { return _warehouse_id; }
            set { _warehouse_id = value; }
        }

		///<sumary>
		/// 货位定义编号
        ///</sumary>
        [DataMember]
		public int DESCRIPTION_ID
		{
			get{return _description_id;}
			set{_description_id = value;}
		}
		///<sumary>
		/// 区域索引
        ///</sumary>
        [DataMember]
		public int AREA_ID
		{
			get{return _area_id;}
			set{_area_id = value;}
		}
		///<sumary>
		/// 存储区索引
        ///</sumary>
        [DataMember]
		public int LOGIC_ID
		{
			get{return _logic_id;}
			set{_logic_id = value;}
		}
		///<sumary>
		/// 起始排
        ///</sumary>
        [DataMember]
		public int START_Z
		{
			get{return _start_z;}
			set{_start_z = value;}
		}
		///<sumary>
		/// 终止排
        ///</sumary>
        [DataMember]
		public int END_Z
		{
			get{return _end_z;}
			set{_end_z = value;}
		}
		///<sumary>
		/// 起始列
        ///</sumary>
        [DataMember]
		public int START_X
		{
			get{return _start_x;}
			set{_start_x = value;}
		}
		///<sumary>
		/// 终止列
        ///</sumary>
        [DataMember]
		public int END_X
		{
			get{return _end_x;}
			set{_end_x = value;}
		}
		///<sumary>
		/// 起始层
        ///</sumary>
        [DataMember]
		public int START_Y
		{
			get{return _start_y;}
			set{_start_y = value;}
		}
		///<sumary>
		/// 终止层
        ///</sumary>
        [DataMember]
		public int END_Y
		{
			get{return _end_y;}
			set{_end_y = value;}
		}
		///<sumary>
		/// 设备编号
        ///</sumary>
        [DataMember]
		public string DEVICE_CODE
		{
			get{return _device_code;}
			set{_device_code = value;}
		}
		///<sumary>
		/// 设备描述
        ///</sumary>
        [DataMember]
		public string DEVICE_NAME
		{
			get{return _device_name;}
			set{_device_name = value;}
		}
		///<sumary>
		/// 巷道号
        ///</sumary>
        [DataMember]
		public string LANE_WAY
		{
			get{return _lane_way;}
			set{_lane_way = value;}
		}
		///<sumary>
		/// 货架类型 单深 双深
        ///</sumary>
        [DataMember]
		public string SHELF_TYPE
		{
			get{return _shelf_type;}
			set{_shelf_type = value;}
		}
		///<sumary>
		/// 相临排
        ///</sumary>
        [DataMember]
		public string SHELF_NEIGHBOUR
		{
			get{return _shelf_neighbour;}
			set{_shelf_neighbour = value;}
		}
		///<sumary>
		/// 货位尺寸
        ///</sumary>
        [DataMember]
		public string CELL_MODEL
		{
			get{return _cell_model;}
			set{_cell_model = value;}
		}
		///<sumary>
		/// 双深逻辑排
        ///</sumary>
        [DataMember]
		public string CELL_LOGICAL_NAME
		{
			get{return _cell_logical_name;}
			set{_cell_logical_name = value;}
		}
		///<sumary>
		/// 出入库类型
        ///</sumary>
        [DataMember]
		public string CELL_INOUT
		{
			get{return _cell_inout;}
			set{_cell_inout = value;}
		}
		///<sumary>
		/// 货位类型
        ///</sumary>
        [DataMember]
		public string CELL_TYPE
		{
			get{return _cell_type;}
			set{_cell_type = value;}
		}
		///<sumary>
		/// 库存类型 单托盘 多托盘
        ///</sumary>
        [DataMember]
		public string CELL_STORAGE_TYPE
		{
			get{return _cell_storage_type;}
			set{_cell_storage_type = value;}
		}
		///<sumary>
		/// 货叉类型
        ///</sumary>
        [DataMember]
		public string CELL_FORK_TYPE
		{
			get{return _cell_fork_type;}
			set{_cell_fork_type = value;}
		}
		///<sumary>
		/// 货叉数量
        ///</sumary>
        [DataMember]
		public string CELL_FORK_COUNT
		{
			get{return _cell_fork_count;}
			set{_cell_fork_count = value;}
		}

        ///<sumary>
        /// 货位宽度
        ///</sumary>
        [DataMember]
        public int CELL_WIDTH
        {
            get { return _cell_width; }
            set { _cell_width = value; }
        }

        ///<sumary>
        /// 货位高度
        ///</sumary>
        [DataMember]
        public int CELL_HEIGHT
        {
            get { return _cell_height; }
            set { _cell_height = value; }
        }

		///<sumary>
		/// 启用标识
        ///</sumary>
        [DataMember]
		public string DESCRIPTION_FLAG
		{
			get{return _description_flag;}
			set{_description_flag = value;}
		}
	}
}
