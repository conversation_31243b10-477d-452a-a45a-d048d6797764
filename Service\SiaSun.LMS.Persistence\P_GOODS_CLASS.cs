﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// GOODS_CLASS
	/// </summary>
	public class P_GOODS_CLASS : P_Base_House
	{
		public P_GOODS_CLASS ()
		{
			//
			// TODO: 此处添加GOODS_CLASS的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<GOODS_CLASS> GetList()
		{
			return ExecuteQueryForList<GOODS_CLASS>("GOODS_CLASS_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(GOODS_CLASS goods_class)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("GOODS_CLASS");
                goods_class.GOODS_CLASS_ID = id;
            }
            return ExecuteInsert("GOODS_CLASS_INSERT",goods_class);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(GOODS_CLASS goods_class)
		{
			return ExecuteUpdate("GOODS_CLASS_UPDATE",goods_class);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public GOODS_CLASS GetModel(System.Int32 GOODS_CLASS_ID)
		{
			return ExecuteQueryForObject<GOODS_CLASS>("GOODS_CLASS_SELECT_BY_ID",GOODS_CLASS_ID);
		}

        public GOODS_CLASS GetModel(string GOODS_CLASS_CODE)
        {
            return ExecuteQueryForObject<GOODS_CLASS>("GOODS_CLASS_SELECT_BY_GOODS_CLASS_CODE", GOODS_CLASS_CODE);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GOODS_CLASS_ID)
		{
			return ExecuteDelete("GOODS_CLASS_DELETE",GOODS_CLASS_ID);
		}
		

	}
}
