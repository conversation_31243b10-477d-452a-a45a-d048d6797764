﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// GOODS_CLASS 
	/// </summary>
    [Serializable]
    [DataContract]
	public class GOODS_CLASS
	{
		public GOODS_CLASS()
		{
			
		}
		
		private int _goods_class_id;
		private int _goods_class_parent_id;
        private int _goods_type_id;
		private string _goods_class_code;
		private string _goods_class_name;
		private string _goods_class_remark;
		private int _goods_class_order;
		private string _goods_class_flag;
		
		///<sumary>
		/// 物料分类编号
        ///</sumary>
        [DataMember]
		public int GOODS_CLASS_ID
		{
			get{return _goods_class_id;}
			set{_goods_class_id = value;}
		}
		///<sumary>
		/// 物料分类父编号
        ///</sumary>
        [DataMember]
		public int GOODS_CLASS_PARENT_ID
		{
			get{return _goods_class_parent_id;}
			set{_goods_class_parent_id = value;}
		}

        ///<sumary>
        /// 物料类型编号
        ///</sumary>
        [DataMember]
        public int GOODS_TYPE_ID
        {
            get { return _goods_type_id; }
            set { _goods_type_id = value; }
        }
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string GOODS_CLASS_CODE
		{
			get{return _goods_class_code;}
			set{_goods_class_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string GOODS_CLASS_NAME
		{
			get{return _goods_class_name;}
			set{_goods_class_name = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string GOODS_CLASS_REMARK
		{
			get{return _goods_class_remark;}
			set{_goods_class_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int GOODS_CLASS_ORDER
		{
			get{return _goods_class_order;}
			set{_goods_class_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string GOODS_CLASS_FLAG
		{
			get{return _goods_class_flag;}
			set{_goods_class_flag = value;}
		}
	}
}
