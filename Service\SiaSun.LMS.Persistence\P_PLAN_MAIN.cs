﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// PLAN_MAIN
	/// </summary>
	public class P_PLAN_MAIN : P_Base_House
	{
		public P_PLAN_MAIN ()
		{
			//
			// TODO: 此处添加PLAN_MAIN的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<PLAN_MAIN> GetList()
		{
			return ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(PLAN_MAIN plan_main)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("PLAN_MAIN");
                plan_main.PLAN_ID = id;
            }

            return ExecuteInsert("PLAN_MAIN_INSERT",plan_main);            
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(PLAN_MAIN plan_main)
		{
			return ExecuteUpdate("PLAN_MAIN_UPDATE",plan_main);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public PLAN_MAIN GetModel(System.Int32 PLAN_ID)
		{
			return ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_ID",PLAN_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        public PLAN_MAIN GetModelPlanListId(System.Int32 planListId)
        {
            return ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_LIST_ID", planListId);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public PLAN_MAIN GetModelPlanCode(string PLAN_CODE, string PLAN_GROUP, string PLAN_RELATIVE_CODE)
        {
            if (string.IsNullOrEmpty(PLAN_GROUP) && string.IsNullOrEmpty(PLAN_RELATIVE_CODE))
            {
                return this.ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_CODE", PLAN_CODE);
            }
            else
            {
                Hashtable ht = new Hashtable();
                ht.Add("PLAN_CODE", PLAN_CODE);
                ht.Add("PLAN_GROUP", PLAN_GROUP);
                ht.Add("PLAN_RELATIVE_CODE", PLAN_RELATIVE_CODE);

                return this.ExecuteQueryForObject<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_CODE_PLAN_GROUP_PLAN_RELATIVE_CODE", ht);
            }
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 PLAN_ID)
		{
			return ExecuteDelete("PLAN_MAIN_DELETE",PLAN_ID);
		}

        /// <summary>
        /// 拣选工作站相关
        /// hejiaji
        /// 20180101
        /// 通过任务包号查找计划列表
        /// </summary>
        /// <param name="PLAN_GROUP">PLAN_GROUP</param>
        /// <returns></returns>
        public IList<PLAN_MAIN> GetList_PLAN_GROUP(string PLAN_GROUP)
        {
            return this.ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_GROUP", PLAN_GROUP);
        }

        /// <summary>
        /// 2024 改造新增
        /// hejiaji
        /// 通过PLAN_RELATIVE_CODE查找计划列表 taskNo
        /// </summary>
        /// <param name="PLAN_RELATIVE_CODE">PLAN_RELATIVE_CODE</param>
        /// <returns></returns>
        public IList<PLAN_MAIN> GetList_PLAN_RELATIVE_CODE(string PLAN_RELATIVE_CODE)
        {
            return this.ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_RELATIVE_CODE", PLAN_RELATIVE_CODE);
        }


        /// <summary>
        /// 拣选工作站相关
        /// hejiaji
        /// 20180129
        /// 通过PLAN_GROUP找到所有没有绑定到拣选工作站的计划实例
        /// </summary>
        /// <param name="PLAN_GROUP">PLAN_GROUP</param>
        /// <returns></returns>
        public IList<PLAN_MAIN> GetListNotBindToPickStationBy_PLAN_GROUP_STATION_ID(string PLAN_GROUP,int STATION_ID)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PLAN_GROUP", PLAN_GROUP);
            ht.Add("STATION_ID", STATION_ID);
            return this.ExecuteQueryForList<PLAN_MAIN>("PLAN_MAIN_SELECT_BY_PLAN_GROUP_NotBindToPickStation", ht);
        }
    }
}
