﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 烟台来福士使用
    /// </summary>
    public class Storage_LFS : StorageBase
    {
        /// <summary>
        /// 生成库存
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageCreate(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

                if (null == mMANAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                    return bResult;
                }

                IList<MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN;

                SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST;

                mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelCellID(mMANAGE_MAIN.END_CELL_ID);

                if (null == mSTORAGE_MAIN)
                {
                    mSTORAGE_MAIN = new SiaSun.LMS.Model.STORAGE_MAIN();

                    mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                    mSTORAGE_MAIN.GOODS_TEMPLATE_ID = mMANAGE_MAIN.GOODS_TEMPLATE_ID;

                    mSTORAGE_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;

                    mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;

                    mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;

                    mSTORAGE_MAIN.STORAGE_REMARK = mMANAGE_MAIN.MANAGE_REMARK;

                    this._P_STORAGE_MAIN.Add(mSTORAGE_MAIN);
                }
                else
                {
                    mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                    mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;

                    this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                }


                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);


                    if (mSTORAGE_LIST != null)
                    {
                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY == 0)

                            this._P_STORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);
                        else

                            this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        
                        mSTORAGE_LIST = new SiaSun.LMS.Model.STORAGE_LIST();

                        mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;

                        mSTORAGE_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;

                        mSTORAGE_LIST.BOX_BARCODE = "";

                        mSTORAGE_LIST.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        mSTORAGE_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;

                        bResult = this._S_GoodsService.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mSTORAGE_LIST, mMANAGE_LIST, out sResult);

                        if (!bResult)
                        {
                            return bResult;
                        }

                        mSTORAGE_LIST.ENTRY_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                        mSTORAGE_LIST.STORAGE_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                        this._P_STORAGE_LIST.Add(mSTORAGE_LIST);
                    }

                }

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN_EXIST = this._P_STORAGE_MAIN.GetModelCellID(mMANAGE_MAIN.END_CELL_ID);

                if (mSTORAGE_MAIN_EXIST != null)
                {
                    if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN_EXIST.STORAGE_ID).Count == 0)
                    {
                        this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN_EXIST.STORAGE_ID);
                    }
                }          

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 删除库存
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageDelete(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            if (null == mMANAGE_MAIN)
            {
                bResult = false;

                sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                return bResult;
            }


            IList<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

            SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = null;

            SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST = null;

            try
            {
                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (null == mSTORAGE_LIST)
                    {
                        bResult = false;

                        sResult = string.Format("库存索引 {0} 不存在", mMANAGE_LIST.STORAGE_LIST_ID);

                        return bResult;
                    }

                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0)
                    {
                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        this._P_STORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);

                        if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_LIST.STORAGE_ID).Count == 0)
                        {
                            mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                            if (null != mSTORAGE_MAIN)
                            {
                                if (this._P_STORAGE_MAIN.GetListCellID(mSTORAGE_MAIN.CELL_ID).Count == 0)
                                {
                                    this._S_CellService.CellUpdateStatus(mSTORAGE_MAIN.CELL_ID, SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(), string.Empty, out sResult);
                                }

                                this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);
                            }
                        }
                    }

                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 库存转移
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageMove(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {
                SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

                if (null == mMANAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("管理任务索引 {0} 不存在", MANAGE_ID);

                    return bResult;
                }

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN;

                SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST;

                IList<MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(MANAGE_ID);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mMANAGE_LIST.STORAGE_LIST_ID);

                    if (null == mSTORAGE_LIST)
                    {
                        bResult = false;

                        sResult = string.Format("库存索引 {0} 不存在", mMANAGE_LIST.STORAGE_LIST_ID);

                        return bResult;
                    }


                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY -= mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    if (mSTORAGE_LIST.STORAGE_LIST_QUANTITY > 0)
                    {
                        this._P_STORAGE_LIST.Update(mSTORAGE_LIST);
                    }
                    else
                    {
                        this._P_STORAGE_LIST.Delete(mSTORAGE_LIST.STORAGE_LIST_ID);

                        if (this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_LIST.STORAGE_ID).Count == 0)
                        {
                            mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                            if (null != mSTORAGE_MAIN)
                            {
                                if (this._P_STORAGE_MAIN.GetListCellID(mSTORAGE_MAIN.CELL_ID).Count == 0)
                                {
                                    this._S_CellService.CellUpdateStatus(mSTORAGE_MAIN.CELL_ID, SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(), string.Empty, out sResult);
                                }

                                this._P_STORAGE_MAIN.Delete(mSTORAGE_MAIN.STORAGE_ID);
                            }

                        }
                    }

                    mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelCellID(mMANAGE_MAIN.END_CELL_ID);

                    if (null == mSTORAGE_MAIN)
                    {
                        mSTORAGE_MAIN = new SiaSun.LMS.Model.STORAGE_MAIN();
                        mSTORAGE_MAIN.GOODS_TEMPLATE_ID = mMANAGE_MAIN.GOODS_TEMPLATE_ID;
                        mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;
                        mSTORAGE_MAIN.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;
                        mSTORAGE_MAIN.CELL_MODEL = mMANAGE_MAIN.CELL_MODEL;
                        mSTORAGE_MAIN.FULL_FLAG = mMANAGE_MAIN.FULL_FLAG;
                        mSTORAGE_MAIN.STORAGE_REMARK = mMANAGE_MAIN.MANAGE_REMARK;

                        this._P_STORAGE_MAIN.Add(mSTORAGE_MAIN);
                    }
                    else
                    {
                        mSTORAGE_MAIN.CELL_ID = mMANAGE_MAIN.END_CELL_ID;

                        this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                    }


                    mSTORAGE_LIST = new SiaSun.LMS.Model.STORAGE_LIST();

                    mSTORAGE_LIST.STORAGE_ID = mSTORAGE_MAIN.STORAGE_ID;

                    mSTORAGE_LIST.PLAN_LIST_ID = mMANAGE_LIST.PLAN_LIST_ID;

                    mSTORAGE_LIST.BOX_BARCODE = string.Empty;

                    mSTORAGE_LIST.STORAGE_LIST_QUANTITY = mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                    mSTORAGE_LIST.GOODS_ID = mMANAGE_LIST.GOODS_ID;

                    bResult = this._S_GoodsService.GoodsPropertySetValue(mMANAGE_LIST.GOODS_ID, mSTORAGE_LIST, mMANAGE_LIST, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    mSTORAGE_LIST.ENTRY_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mSTORAGE_LIST.UPDATE_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mSTORAGE_LIST.STORAGE_LIST_REMARK = mMANAGE_LIST.MANAGE_LIST_REMARK;

                    this._P_STORAGE_LIST.Add(mSTORAGE_LIST);
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }


        /// <summary>
        /// 库存转移
        /// </summary>
        /// <param name="MANAGE_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageMove(string STOCK_BARCODE, int START_CELL_ID,int END_CELL_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            try
            {

                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = null;

                mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelCellID(START_CELL_ID);

                if (null == mSTORAGE_MAIN)
                {
                    bResult = false;

                    sResult = string.Format("未找到库存{0}", STOCK_BARCODE);

                    return bResult;
                }

                mSTORAGE_MAIN.CELL_ID = END_CELL_ID;

                this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }


    }
}
