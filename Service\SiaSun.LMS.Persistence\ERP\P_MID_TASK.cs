﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun-XCJT
 *       日期：     2016/12/26
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
    /// MID_TASK
	/// </summary>
    public class P_MID_TASK : P_Base_ERP
	{
        public P_MID_TASK()
		{
			//
            // TODO: 此处添加MID_TASK的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
        public IList<MID_TASK> GetList()
		{
            return ExecuteQueryForList<MID_TASK>("MID_TASK_SELECT", null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<MID_TASK> GetList(string FINISH_FLAGorTASK_NO)
        {
            if (FINISH_FLAGorTASK_NO.Length == 1)
            {
                //根据处理标志查找
                return this.ExecuteQueryForList<MID_TASK>("MID_TASK_SELECT_BY_FINISH_FLAG", FINISH_FLAGorTASK_NO);
            }
            else
            {
                //根据任务编号查找
                return this.ExecuteQueryForList<MID_TASK>("MID_TASK_SELECT_BY_TASK_NO", FINISH_FLAGorTASK_NO);
            }
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        public MID_TASK GetModel(int FID)
        {
            return ExecuteQueryForObject<MID_TASK>("MID_TASK_SELECT_BY_FID", FID);
        }

		/// <summary>
		/// 新建
		/// </summary>
        public int Add(MID_TASK mid_task)
		{
            return ExecuteInsert("MID_TASK_INSERT", mid_task);
		}

		/// <summary>
		/// 修改
		/// </summary>
        public int Update(MID_TASK mid_task)
		{
            return ExecuteUpdate("MID_TASK_UPDATE", mid_task);
		}		

		/// <summary>
		/// 删除
		/// </summary>
		public int Delete(int fid)
		{
            return ExecuteDelete("MID_TASK_DELETE", fid);
		}
	}
}
