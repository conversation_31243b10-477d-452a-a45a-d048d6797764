﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Speech.Synthesis;
namespace SiaSun.LMS.WPFClient.FLOW_ACTION
{
    /// <summary>
    /// MANAGE_ALARM.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_ALARM : Window
    {
        public SpeechSynthesizer synthesizer = new SpeechSynthesizer();

        public MANAGE_ALARM(string info, string flag)
        {
            InitializeComponent();
            //synthesizer.Volume = 100;
            var desk = System.Windows.SystemParameters.WorkArea;
            this.Left = desk.Right - this.Width;
            this.Top = desk.Bottom - this.Height;
            this.alarmtime.Content = $"警告！   报警时间:{DateTime.Now.ToString("T")}";
            txt.Text = $"{info}";
            for (int i = 0; i < 2; i++)
            {
                this.Dispatcher.Invoke(new Action(() =>
               synthesizer.SpeakAsync($"警告！，{info}")));
            }
        }

        private void Comform(object sender, RoutedEventArgs e)
        {
            synthesizer.Pause();
            synthesizer.Dispose();
            this.Close();
        }
    }
}
