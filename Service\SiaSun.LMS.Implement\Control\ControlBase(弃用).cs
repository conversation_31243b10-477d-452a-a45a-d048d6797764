﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ControlBase_弃用:ManageBase
    {

        public static DataTable dtEventHandle = new DataTable("EventExecute");

        //xcjt comment 2016-12-28
        //public static List<Model.IO_CONTROL_APPLY> lsHandleControlApply = new List<Model.IO_CONTROL_APPLY>();
        
        /// <summary>作业-动作
        /// 作业-动作
        /// </summary>
        /// <param name="WAREHOUSE">库房编码</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public void ControlTranslate(int conCurrentCount,out string sResult)
        {
            sResult = string.Empty;

            IList<SiaSun.LMS.Model.FLOW_PARA> lsFLOW_PARA = this._S_FlowService.FlowGetParameters("FLOW_CONTROL");

            string[] aFLOW_PARA = new string[lsFLOW_PARA.Count];

            SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE mt = new SiaSun.LMS.Model.MANAGE_ACTION_EXCUTE();

            DataTable dt = this.GetList(string.Format("SELECT  * FROM V_FLOW_CONTROL_ACTION  order by manage_id "));

            if (!dtEventHandle.Columns.Contains("execute"))
            {
                dtEventHandle.Columns.Add("execute");
                dtEventHandle.Columns["execute"].DefaultValue = 0;
            }

            dtEventHandle.Merge(dt);

            if (dtEventHandle.Columns.Contains("MANAGE_ID"))
                dtEventHandle.PrimaryKey = new DataColumn[] { dtEventHandle.Columns["MANAGE_ID"] };

            DataRow[] drThreadExecute = dtEventHandle.Select("execute = 0 or execute is null");

            DataTable dtThreadExecute = dt.Clone();

            for (int i = 0; i < (drThreadExecute.Length > conCurrentCount ? conCurrentCount : drThreadExecute.Length); i++)
            {
                drThreadExecute[i]["execute"] = 1;
                dtThreadExecute.ImportRow(drThreadExecute[i]);
            }

            foreach (DataRow dr in dtThreadExecute.Rows)
            {
                int i = 0;

                foreach (SiaSun.LMS.Model.FLOW_PARA mFLOW_PARA in lsFLOW_PARA)
                {
                    aFLOW_PARA[i] = dr[mFLOW_PARA.FLOW_PARA_CODE].ToString();

                    i++;
                }

                mt.MANAGE_ID = Convert.ToInt32(dr["MANAGE_ID"]);

                mt.ACTION_EVENT = dr["FLOW_ACTION_EVENT"].ToString();

                mt.ACTION_EVENT = string.Format(mt.ACTION_EVENT, aFLOW_PARA);

                bool bResult = this._S_FlowService.ManageEventExecute(mt, out sResult);

                SiaSun.LMS.Model.IO_CONTROL mIO_CONTROL = this._P_IO_CONTROL.GetModel(Convert.ToInt32(dr["CONTROL_ID"]));

                if (!bResult)
                {

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mt.MANAGE_ID);

                    if (mMANAGE_MAIN != null)
                    {

                        mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.Error.ToString();

                        mMANAGE_MAIN.MANAGE_REMARK = sResult + string.Format("【控制任务状态-{0}】", mIO_CONTROL.CONTROL_STATUS.ToString());

                        this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
                    }
                    else
                    {
                        //this._log.Fatal(string.Format("执行事件失败{0} {1}", mt.MANAGE_ID, mt.ACTION_EVENT));
                    }
                }

                if (bResult && null != mIO_CONTROL)
                {
                    mIO_CONTROL.PRE_CONTROL_STATUS = Convert.ToString(dr["CONTROL_STATUS"]);

                    this._P_IO_CONTROL.Update(mIO_CONTROL);


                }

                DataRow drFind = dtEventHandle.Rows.Find(Convert.ToInt32(dr["MANAGE_ID"]));

                int index = dtEventHandle.Rows.IndexOf(drFind);

                if (index != -1)
                    dtEventHandle.Rows.RemoveAt(index);
            }
        }

        /* xcjt comment 2016-12-28
        /// <summary>任务-申请
        /// 任务-申请
        /// </summary>
        /// <param name="CONTROL_APPLY_ID">申请编号</param>
        /// <param name="MANAGE_ID">任务编号</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool ControlApplyTask(out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            string mesout = string.Empty;

            //收集新生成的申请任务
            IList<SiaSun.LMS.Model.IO_CONTROL_APPLY> lsIO_CONTROL_APPLY = this._P_IO_CONTROL_APPLY.GetList(0);

            foreach (SiaSun.LMS.Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY in lsIO_CONTROL_APPLY)
            {
                if (!lsHandleControlApply.Contains(mIO_CONTROL_APPLY))
                {
                    lsHandleControlApply.Add(mIO_CONTROL_APPLY);
                }

            } //收集新生成的申请任务

            //处理申请任务
            foreach (Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY_HANDLE in lsHandleControlApply)
            {
                Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)this.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", mIO_CONTROL_APPLY_HANDLE.CONTROL_APPLY_TYPE).RequestObject;

                bResult = this.Invoke(mAPPLY_TYPE.APPLY_TYPE_CLASS, "ApplyHandle", new object[] { mIO_CONTROL_APPLY_HANDLE }, out sResult);

                lsHandleControlApply.Remove(mIO_CONTROL_APPLY_HANDLE);

            } //处理申请任务

            return bResult;
        }
        */

        /// <summary>
        /// 处理申请方法
        /// xcjt 2016-12-28
        /// </summary>
        public bool ControlApplyTask(out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            string mesout = string.Empty;

            IList<SiaSun.LMS.Model.IO_CONTROL_APPLY> lsIO_CONTROL_APPLY = this._P_IO_CONTROL_APPLY.GetList(0);

            foreach (SiaSun.LMS.Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY in lsIO_CONTROL_APPLY)
            {
                Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)this.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE).RequestObject;
                bResult = this.Invoke(mAPPLY_TYPE.APPLY_TYPE_CLASS, "ApplyHandle", new object[] { mIO_CONTROL_APPLY }, out sResult);
            } 

            return bResult;
        }




    }
}
