﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_USER" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_USER" type="SiaSun.LMS.Model.SYS_USER, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_USER">
			<result property="USER_ID" column="user_id" />
			<result property="USER_CODE" column="user_code" />
			<result property="USER_NAME" column="user_name" />
			<result property="USER_PASSWORD" column="user_password" />
			<result property="USER_REMARK" column="user_remark" />
			<result property="USER_ORDER" column="user_order" />
			<result property="USER_FLAG" column="user_flag" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_USER_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  user_id,
				  user_code,
				  user_name,
				  user_password,
				  user_remark,
				  user_order,
				  user_flag
			From SYS_USER
		</select>
		
		<select id="SYS_USER_SELECT_BY_ID" parameterClass="int" extends = "SYS_USER_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					user_id=#USER_ID# 
				</isParameterPresent>
			</dynamic>
		</select>


    <select id="SYS_USER_SELECT_BY_USER_CODE" parameterClass="int" extends = "SYS_USER_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          user_code=#USER_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="SYS_USER_SELECT_BY_ROLE_ID" parameterClass="int" resultMap="SelectResult" extends="SYS_USER_SELECT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          USER_ID !=0 AND USER_FLAG=1 AND USER_ID in (select RELATION_ID2 from V_SYS_RELATION where RELATION_CODE='SYS_ROLE-SYS_USER' and RELATION_ID1=#ROLE_ID#)
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="SYS_USER_SELECT_NOTBY_ROLE_ID" parameterClass="int" resultMap="SelectResult" extends="SYS_USER_SELECT">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          USER_ID !=0 AND USER_FLAG=1 AND USER_ID not in (select RELATION_ID2 from V_SYS_RELATION where RELATION_CODE='SYS_ROLE-SYS_USER' and RELATION_ID1=#ROLE_ID#)
        </isParameterPresent>
      </dynamic>
    </select>



    <select id="SYS_USER_LOGIN" parameterClass="System.Collections.Hashtable" resultMap="SelectResult" extends="SYS_USER_SELECT">
      WHERE USER_CODE= #USER_CODE# AND USER_PASSWORD= #USER_PASSWORD# AND USER_FLAG=1
    </select>


    <insert id="SYS_USER_INSERT" parameterClass="SYS_USER">
      Insert Into SYS_USER (
      user_id,
      user_code,
      user_name,
      user_password,
      user_remark,
      user_order,
      user_flag
      )Values(
      #USER_ID#,
      #USER_CODE#,
      #USER_NAME#,
      #USER_PASSWORD#,
      #USER_REMARK#,
      #USER_ORDER#,
      #USER_FLAG#
      )
      <!--<selectKey  resultClass="int" type="post" property="USER_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_USER_UPDATE" parameterClass="SYS_USER">
      Update SYS_USER Set
      user_id=#USER_ID#,
      user_code=#USER_CODE#,
      user_name=#USER_NAME#,
      user_password=#USER_PASSWORD#,
      user_remark=#USER_REMARK#,
      user_order=#USER_ORDER#,
      user_flag=#USER_FLAG#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					user_id=#USER_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_USER_DELETE" parameterClass="int">
			Delete From SYS_USER
			<dynamic prepend="WHERE">
				<isParameterPresent>
					user_id=#USER_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>