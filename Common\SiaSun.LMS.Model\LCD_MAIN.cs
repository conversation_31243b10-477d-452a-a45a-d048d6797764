﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     
 *       日期：     2018/1/4
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
 

namespace SiaSun.LMS.Model
{
    using System;

    /// <summary>
    /// LCD_MAIN 
    /// </summary>
    [Serializable]
    public class LCD_MAIN
    {
        public LCD_MAIN()
        {

        }

        private int _lcd_id;
        private string _lcd_code;
        private string _lcd_ip;
        private string _lcd_message;
        private string _lcd_remark;
        private string _lcd_update_time;
        private string _lcd_read_flag;
        private string _lcd_station;

        ///<sumary>
        /// ID
        ///</sumary>
        public int LCD_ID
        {
            get { return _lcd_id; }
            set { _lcd_id = value; }
        }
        ///<sumary>
        /// 看板编码
        ///</sumary>
        public string LCD_CODE
        {
            get { return _lcd_code; }
            set { _lcd_code = value; }
        }
        ///<sumary>
        /// 看板IP
        ///</sumary>
        public string LCD_IP
        {
            get { return _lcd_ip; }
            set { _lcd_ip = value; }
        }
        ///<sumary>
        /// 显示内容
        ///</sumary>
        public string LCD_MESSAGE
        {
            get { return _lcd_message; }
            set { _lcd_message = value; }
        }
        ///<sumary>
        /// 备注
        ///</sumary>
        public string LCD_REMARK
        {
            get { return _lcd_remark; }
            set { _lcd_remark = value; }
        }
        ///<sumary>
        /// 更新时间
        ///</sumary>
        public string LCD_UPDATE_TIME
        {
            get { return _lcd_update_time; }
            set { _lcd_update_time = value; }
        }
        ///<sumary>
        /// 已读标志
        ///</sumary>
        public string LCD_READ_FLAG
        {
            get { return _lcd_read_flag; }
            set { _lcd_read_flag = value; }
        }
        ///<sumary>
		/// LCD所显示的站台信息
		///</sumary>
		public string LCD_STATION
        {
            get { return _lcd_station; }
            set { _lcd_station = value; }
        }
    }
}
