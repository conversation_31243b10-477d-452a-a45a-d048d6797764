﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun-XCJT
 *       日期：     2016/12/26
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
    /// MID_GOODS_MAIN 
	/// </summary>
    [Serializable]
    [DataContract]
	public class MID_GOODS
	{
        public MID_GOODS()
		{			
		}

        private int _goods_id;
        private string _goods_code;
        private string _goods_name;
        private string _goods_property;
        private int _fmodifytime;

		///<sumary>
		/// 物料ID
		///</sumary>
        [DataMember]
        public int GOODS_ID
		{
            get { return _goods_id; }
            set { _goods_id = value; }
		}
		///<sumary>
		/// 物料编码
        ///</sumary>
        [DataMember]
        public string GOODS_CODE
		{
            get { return _goods_code; }
            set { _goods_code = value; }
		}
		///<sumary>
		/// 物料名称
        ///</sumary>
        [DataMember]
        public string GOODS_NAME
		{
            get { return _goods_name; }
            set { _goods_name = value; }
		}
		///<sumary>
		/// 物料属性
        ///</sumary>
        [DataMember]
        public string GOODS_PROPERTY
		{
            get { return _goods_property; }
            set { _goods_property = value; }
		}
		///<sumary>
		/// 更新次数
        ///</sumary>
        [DataMember]
        public int FMODIFYTIME
		{
            get { return _fmodifytime; }
            set { _fmodifytime = value; }
		}		
	}
}
