﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// FLOW_NODE
	/// </summary>
	public class P_FLOW_NODE : P_Base_House
	{
		public P_FLOW_NODE ()
		{
			//
			// TODO: 此处添加FLOW_NODE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<FLOW_NODE> GetList()
		{
			return ExecuteQueryForList<FLOW_NODE>("FLOW_NODE_SELECT",null);
		}

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(FLOW_NODE flow_node)
        {
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("FLOW_NODE");
                flow_node.FLOW_NODE_ID = id;
            }
            return ExecuteInsert("FLOW_NODE_INSERT", flow_node);
        }

		/// <summary>
		/// 修改
		/// </summary>
		public int Update(FLOW_NODE flow_node)
		{
			return ExecuteUpdate("FLOW_NODE_UPDATE",flow_node);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public FLOW_NODE GetModel(System.Int32 FLOW_NODE_ID)
		{
			return ExecuteQueryForObject<FLOW_NODE>("FLOW_NODE_SELECT_BY_ID",FLOW_NODE_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 FLOW_NODE_ID)
		{
			return ExecuteDelete("FLOW_NODE_DELETE",FLOW_NODE_ID);
		}
		

	}
}
