﻿/*
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// Miniload拣货确认回传接口
    /// 
    ///①拣选完成生成库存时写入STORAGE_LIST.PLAN_LIST_ID;
    ///②上架过扫描头时如果发现存在库存，则生成的MANAGE_LIST.PLAN_LIST_ID=STORAGE_LIST.PLAN_LIST_ID；
    ///③上架任务完成时，首先将MANAGE_LIST 对应的 PLAN_LIST 完成数量更新，如有SN码则写入SN码到PLAN_DETAIL，再在基类中判断计划是否可以完成；
    ///④计划完成方法中，如果发现是拣选计划，则判断与当前计划同组的计划是否全部可以完成，如果全部可以完成则触发上报WMS接口，搜集所有PLAN_GROUP的信息打包上报。
    /// </summary>
    public class miniloadPickConfirmFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public List<FirstDetails> firstDetails { get; set; }//一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号
            public List<SecondDetails> secondDetails { get; set; }//二级明细

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }
        }
        /// <summary>
        /// 入参_二级明细
        /// </summary>
        public class SecondDetails
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty;     //数量

            public List<ThirdDetails> thirdDetails { get; set; }  //三级明细

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }
        /// <summary>
        /// 入参_三级明细
        /// </summary>
        public class ThirdDetails
        {
            private string _snCode = string.Empty;        //SN号

            /// <summary>
            /// SN号
            /// </summary>
            public string snCode
            {
                get { return _snCode; }
                set { _snCode = value; }
            }            
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(int planId ,string planGroup, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();

            try
            {
                IList<Model.PLAN_MAIN> lsPLAN_MAIN = this._P_PLAN_MAIN.GetList_PLAN_GROUP(planGroup);
                var planMainUnfinished = lsPLAN_MAIN.Where(r => r.PLAN_STATUS != Enum.PLAN_STATUS.Finish.ToString() && r.PLAN_ID != planId);
                if (planMainUnfinished.Count() != 0)
                {
                    this.CreateSysLog(Enum.LogThread.Interface, "System", false, string.Format("miniloadPickConfirmFromWCS.NotifyMethod():调用Miniload拣选结果回传接口时发现计划组中有未完成的项目_计划组[{0}]", planGroup));
                    return bResult;
                }

                List<FirstDetails> lsFirstDetails = new List<FirstDetails>();

                foreach(Model.PLAN_MAIN itemPlanMain in lsPLAN_MAIN)
                {
                    IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(itemPlanMain.PLAN_ID);
                    List<SecondDetails> lsSecondDetails = new List<SecondDetails>();

                    foreach (Model.PLAN_LIST itemPlanList in lsPLAN_LIST)
                    {
                        IList<Model.PLAN_DETAIL> lsPLAN_DETAIL = this._P_PLAN_DETAIL.GetList(itemPlanList.PLAN_LIST_ID);
                        List<ThirdDetails> lsThirdDetails = new List<ThirdDetails>();

                        foreach (Model.PLAN_DETAIL itemPlanDetail in lsPLAN_DETAIL)
                        {
                            ThirdDetails thirdDetails = new ThirdDetails();
                            thirdDetails.snCode = itemPlanDetail.GOODS_BARCODE;

                            lsThirdDetails.Add(thirdDetails);
                        }

                        SecondDetails secondDetails = new SecondDetails();
                        secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(itemPlanList.GOODS_ID).GOODS_CODE;
                        secondDetails.quantity = itemPlanList.PLAN_LIST_FINISHED_QUANTITY.ToString();
                        secondDetails.thirdDetails = lsThirdDetails;

                        lsSecondDetails.Add(secondDetails);
                    }

                    FirstDetails firstDetails = new FirstDetails();
                    firstDetails.projectNo = itemPlanMain.PLAN_PROJECT_CODE;
                    firstDetails.taskNo = itemPlanMain.PLAN_RELATIVE_CODE;
                    firstDetails.wbsNo = itemPlanMain.PLAN_CODE;
                    firstDetails.secondDetails = lsSecondDetails;

                    lsFirstDetails.Add(firstDetails);
                }                    

                inputPara.uniqueCode = planGroup;
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";
                inputPara.firstDetails = lsFirstDetails;

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);
                bResult = this._S_WESJsonService.miniloadPickConfirmFromWCS(strInputParaJson, out sResult);
                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if(outputPara.responseCode!="1")
                    {
                        bResult = false;
                        sResult = string.Format(" miniloadPickConfirmFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("miniloadPickConfirmFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }
    }
}
*/

using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// Miniload拣货确认回传接口
    /// 
    ///①拣选完成生成库存时写入STORAGE_LIST.PLAN_LIST_ID;
    ///②上架过扫描头时如果发现存在库存，则生成的MANAGE_LIST.PLAN_LIST_ID=STORAGE_LIST.PLAN_LIST_ID；
    ///③上架任务完成时，首先将MANAGE_LIST 对应的 PLAN_LIST 完成数量更新，如有SN码则写入SN码到PLAN_DETAIL，再在基类中判断计划是否可以完成；
    ///④计划完成方法中，如果发现是拣选计划，则判断与当前计划同组的计划是否全部可以完成，如果全部可以完成则触发上报WMS接口，搜集所有PLAN_GROUP的信息打包上报。
    /// </summary>
    public class miniloadPickConfirmFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public List<FirstDetails> firstDetails { get; set; }//一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号
            public List<SecondDetails> secondDetails { get; set; }//二级明细

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }
        }
        /// <summary>
        /// 入参_二级明细
        /// </summary>
        public class SecondDetails
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty;     //数量
            private string _shortQuantity = string.Empty;     //短拣数量

            public List<ThirdDetails> thirdDetails { get; set; }  //三级明细

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }

            /// <summary>
            /// 短拣数量
            /// </summary>
            public string shortQuantity
            {
                get { return _shortQuantity; }
                set { _shortQuantity = value; }
            }
        }
        /// <summary>
        /// 入参_三级明细
        /// </summary>
        public class ThirdDetails
        {
            private string _boxNo = string.Empty;        //箱号
            private string _boxQuantity = string.Empty;     //箱内数量

            private string _invFollow = string.Empty;           //质量追溯码  2025-06-26新增


            public List<FourthDetails> fourthDetails { get; set; }  //四级明细

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }
            /// <summary>
            /// 箱内数量
            /// </summary>
            public string boxQuantity
            {
                get { return _boxQuantity; }
                set { _boxQuantity = value; }
            }

            /// <summary>
            /// 质量追溯码  2025-06-26新增
            /// </summary>
            public string invFollow
            {
                get { return _invFollow; }
                set { _invFollow = value; }
            }
        }
        /// <summary>
        /// 入参_四级明细
        /// </summary>
        public class FourthDetails
        {
            private string _snCode = string.Empty;        //SN号

            /// <summary>
            /// SN号
            /// </summary>
            public string snCode
            {
                get { return _snCode; }
                set { _snCode = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(int planId, string planGroup, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();

            try
            {
                IList<Model.PLAN_MAIN> lsPLAN_MAIN = this._P_PLAN_MAIN.GetList_PLAN_GROUP(planGroup);
                if (lsPLAN_MAIN == null || lsPLAN_MAIN.Count < 1)
                {
                    bResult = false;
                    sResult = string.Format(" miniloadPickConfirmFromWCS.NotifyMethod:未找到唯一码[{0}]对应的计划信息", planGroup);
                    return bResult;
                }
                var planMainUnfinished = lsPLAN_MAIN.Where(r => r.PLAN_STATUS != Enum.PLAN_STATUS.Finish.ToString() && r.PLAN_ID != planId);
                if (planMainUnfinished.Count() != 0)
                {
                    this.CreateSysLog(Enum.LogThread.Interface, "System", false, string.Format("miniloadPickConfirmFromWCS.NotifyMethod():调用Miniload拣选结果回传接口时发现计划组中有未完成的项目_计划组[{0}]", planGroup));
                    return bResult;
                }

                var pickTypeList = lsPLAN_MAIN.Select(r => r.PLAN_PICK_TYPE).Distinct().ToList();
                if (pickTypeList == null || pickTypeList.Count() != 1)
                {
                    bResult = false;
                    sResult = string.Format(" miniloadPickConfirmFromWCS.NotifyMethod:唯一码[{0}]对应的计划存在多个拣选类型或者未指定拣选类型", planGroup);
                    return bResult;
                }

                List<FirstDetails> lsFirstDetails = new List<FirstDetails>();

                if (pickTypeList[0] == "01")
                {
                    foreach (Model.PLAN_MAIN itemPlanMain in lsPLAN_MAIN)
                    {
                        IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(itemPlanMain.PLAN_ID);
                        List<SecondDetails> lsSecondDetails = new List<SecondDetails>();

                        //wdz alter 2019-03-14 针对唯智要求的传递箱号需求进行重写
                        ///////////old start
                        //foreach (Model.PLAN_LIST itemPlanList in lsPLAN_LIST)
                        //{
                        //    IList<Model.PLAN_DETAIL> lsPLAN_DETAIL = this._P_PLAN_DETAIL.GetList(itemPlanList.PLAN_LIST_ID);
                        //    List<ThirdDetails> lsThirdDetails = new List<ThirdDetails>();
                        //    foreach (Model.PLAN_DETAIL itemPlanDetail in lsPLAN_DETAIL)
                        //    {
                        //        ThirdDetails thirdDetails = new ThirdDetails();
                        //        thirdDetails.snCode = itemPlanDetail.GOODS_BARCODE;
                        //        lsThirdDetails.Add(thirdDetails);
                        //    }
                        //    SecondDetails secondDetails = new SecondDetails();
                        //    secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(itemPlanList.GOODS_ID).GOODS_CODE;
                        //    secondDetails.quantity = itemPlanList.PLAN_LIST_FINISHED_QUANTITY.ToString();
                        //    secondDetails.thirdDetails = lsThirdDetails;
                        //    lsSecondDetails.Add(secondDetails);
                        //}
                        ///////////old end

                        ///////////new start

                        foreach (Model.PLAN_LIST itemPlanList in lsPLAN_LIST)
                        {
                            List<ThirdDetails> lsThirdDetails = new List<ThirdDetails>();

                            // 2019-05-29 12:13:49 by ywz  
                            //DataTable dtStorageNeed = this.GetList(string.Format("select * from V_STORAGE_LIST where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}' and GOODS_ID = {3}", itemPlanMain.PLAN_RELATIVE_CODE, itemPlanMain.PLAN_PROJECT_CODE, itemPlanMain.PLAN_CODE, itemPlanList.GOODS_ID));
                            DataTable dtStorageNeed = this.GetList(string.Format("select * from V_STORAGE_LIST where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}' and GOODS_ID = {3} and STORAGE_LIST_QUANTITY > 0", itemPlanMain.PLAN_RELATIVE_CODE, itemPlanMain.PLAN_PROJECT_CODE, itemPlanMain.PLAN_CODE.TrimStart().TrimEnd(), itemPlanList.GOODS_ID));

                            if (dtStorageNeed != null && dtStorageNeed.Rows.Count > 0)
                            {
                                foreach (DataRow itemBoxRow in dtStorageNeed.Rows)
                                {
                                    List<FourthDetails> lsFourthDetails = new List<FourthDetails>();
                                    IList<Model.STORAGE_DETAIL> lsSTORAGE_DETAIL = this._P_STORAGE_DETAIL.GetList(int.Parse(itemBoxRow["STORAGE_LIST_ID"].ToString()));
                                    foreach (var itemStorageDetail in lsSTORAGE_DETAIL)
                                    {
                                        FourthDetails fourthDetails = new FourthDetails();
                                        fourthDetails.snCode = itemStorageDetail.GOODS_BARCODE;
                                        lsFourthDetails.Add(fourthDetails);
                                    }

                                    ThirdDetails thirdDetails = new ThirdDetails();
                                    thirdDetails.boxNo = itemBoxRow["STOCK_BARCODE"].ToString();
                                    thirdDetails.boxQuantity = itemBoxRow["STORAGE_LIST_QUANTITY"].ToString();
                                    thirdDetails.invFollow = itemBoxRow["GOODS_PROPERTY5"].ToString();  //质量追溯码 2025-06-26
                                    thirdDetails.fourthDetails = lsFourthDetails;
                                    lsThirdDetails.Add(thirdDetails);
                                }
                            }

                            SecondDetails secondDetails = new SecondDetails();
                            secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(itemPlanList.GOODS_ID).GOODS_CODE;
                            secondDetails.quantity = itemPlanList.PLAN_LIST_FINISHED_QUANTITY.ToString();
                            secondDetails.shortQuantity = (itemPlanList.PLAN_LIST_QUANTITY - itemPlanList.PLAN_LIST_FINISHED_QUANTITY).ToString();
                            secondDetails.thirdDetails = lsThirdDetails;
                            lsSecondDetails.Add(secondDetails);
                        }
                        ///////////new end

                        FirstDetails firstDetails = new FirstDetails();
                        firstDetails.projectNo = itemPlanMain.PLAN_PROJECT_CODE;
                        firstDetails.taskNo = itemPlanMain.PLAN_RELATIVE_CODE;
                        firstDetails.wbsNo = itemPlanMain.PLAN_CODE;
                        firstDetails.secondDetails = lsSecondDetails;

                        lsFirstDetails.Add(firstDetails);
                    }
                }
                else
                {
                    var planCodeInfo = lsPLAN_MAIN[0].PLAN_CODE.Split('|');
                    if (planCodeInfo.Count() != 2)
                    {
                        bResult = false;
                        sResult = string.Format(" miniloadPickConfirmFromWCS.NotifyMethod:计划编码[{0}]拆分后数量不为2", lsPLAN_MAIN[0].PLAN_CODE);
                        return bResult;
                    }

                    List<SecondDetails> lsSecondDetails = new List<SecondDetails>();

                    foreach (Model.PLAN_MAIN itemPlanMain in lsPLAN_MAIN)
                    {
                        List<ThirdDetails> lsThirdDetails = new List<ThirdDetails>();

                        IList<Model.PLAN_LIST> lsPLAN_LIST = this._P_PLAN_LIST.GetListPlanID(itemPlanMain.PLAN_ID);
                        foreach (Model.PLAN_LIST itemPlanList in lsPLAN_LIST)
                        {
                            // 2019-05-29 12:13:49 by ywz  
                            //DataTable dtStorageNeed = this.GetList(string.Format("select * from V_STORAGE_LIST where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}' and GOODS_ID = {3}", itemPlanMain.PLAN_RELATIVE_CODE, itemPlanMain.PLAN_PROJECT_CODE, itemPlanMain.PLAN_CODE, itemPlanList.GOODS_ID));
                            DataTable dtStorageNeed = this.GetList(string.Format("select * from V_STORAGE_LIST where GOODS_PROPERTY6='{0}' and GOODS_PROPERTY7='{1}' and GOODS_PROPERTY8='{2}' and GOODS_ID = {3} and STORAGE_LIST_QUANTITY > 0", itemPlanMain.PLAN_RELATIVE_CODE, itemPlanMain.PLAN_PROJECT_CODE, itemPlanMain.PLAN_CODE.Split('|')[0], itemPlanList.GOODS_ID));

                            if (dtStorageNeed != null && dtStorageNeed.Rows.Count > 0)
                            {
                                foreach (DataRow itemBoxRow in dtStorageNeed.Rows)
                                {
                                    List<FourthDetails> lsFourthDetails = new List<FourthDetails>();
                                    IList<Model.STORAGE_DETAIL> lsSTORAGE_DETAIL = this._P_STORAGE_DETAIL.GetList(int.Parse(itemBoxRow["STORAGE_LIST_ID"].ToString()));
                                    foreach (var itemStorageDetail in lsSTORAGE_DETAIL)
                                    {
                                        FourthDetails fourthDetails = new FourthDetails();
                                        fourthDetails.snCode = itemStorageDetail.GOODS_BARCODE;
                                        lsFourthDetails.Add(fourthDetails);
                                    }

                                    ThirdDetails thirdDetails = new ThirdDetails();
                                    thirdDetails.boxNo = itemBoxRow["STOCK_BARCODE"].ToString();
                                    thirdDetails.boxQuantity = itemBoxRow["STORAGE_LIST_QUANTITY"].ToString();
                                    thirdDetails.fourthDetails = lsFourthDetails;
                                    lsThirdDetails.Add(thirdDetails);
                                }
                            }

                            SecondDetails secondDetails = new SecondDetails();
                            secondDetails.itemCode = this._P_GOODS_MAIN.GetModel(itemPlanList.GOODS_ID).GOODS_CODE;
                            secondDetails.quantity = itemPlanList.PLAN_LIST_FINISHED_QUANTITY.ToString();
                            secondDetails.shortQuantity = (itemPlanList.PLAN_LIST_QUANTITY - itemPlanList.PLAN_LIST_FINISHED_QUANTITY).ToString();
                            secondDetails.thirdDetails = lsThirdDetails;
                            lsSecondDetails.Add(secondDetails);
                        }
                    }

                    FirstDetails firstDetails = new FirstDetails();
                    firstDetails.projectNo = lsPLAN_MAIN[0].PLAN_PROJECT_CODE;
                    firstDetails.taskNo = lsPLAN_MAIN[0].PLAN_RELATIVE_CODE;
                    firstDetails.wbsNo = planCodeInfo[0];
                    firstDetails.secondDetails = lsSecondDetails;

                    lsFirstDetails.Add(firstDetails);
                }

                inputPara.uniqueCode = planGroup;
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";
                inputPara.firstDetails = lsFirstDetails;

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);
                bResult = this._S_WESJsonService.miniloadPickConfirmFromWCS(strInputParaJson, out sResult);
                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if (outputPara.responseCode != "1")
                    {
                        bResult = false;
                        sResult = string.Format(" miniloadPickConfirmFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("miniloadPickConfirmFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }

    }
}

