﻿<Window x:Class="SiaSun.LMS.WPFClient.PICK.View.GoodsImageCheckWindowView"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
        mc:Ignorable="d"
        WindowStartupLocation="CenterScreen"
        Title="GoodsImageCheckWindowView" Height="450" Width="800"
        >
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="50"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="50"></RowDefinition>
        </Grid.RowDefinitions>

        <Border>
            <StackPanel Orientation="Horizontal"
                        HorizontalAlignment="Left"
                        VerticalAlignment="Center">
                <TextBlock FontWeight="Bold" FontSize="20" Margin="5">物料编码:</TextBlock>
                <TextBlock x:Name="tbGoodsCode" FontWeight="SemiBold" FontSize="20" MinWidth="150px" Margin="5"
                           ></TextBlock>
                <TextBlock FontWeight="Bold" FontSize="20" Margin="5">物料名称:</TextBlock>
                <TextBlock x:Name="tbGoodsName" FontWeight="SemiBold" FontSize="20" MinWidth="200px" Margin="5"
                           ></TextBlock>
            </StackPanel>
        </Border>

        <Border Grid.Row="1">
            <Image x:Name="imageGoods"></Image>
        </Border>

        <Border Grid.Row="2">
            <StackPanel Orientation="Horizontal" HorizontalAlignment="Center"
                        VerticalAlignment="Center">
                <TextBlock Margin="5" FontWeight="Bold">请扫描物料编码:</TextBlock>
                <TextBox Width="200" Margin="5"
                         x:Name="tBoxCheckCode" KeyDown="tBoxCheckCode_KeyDown"></TextBox>
                <Button x:Name="btnComfirm" Width="70" Margin="5"
                        Click="btnComfirm_Click">确认</Button>
                <Button x:Name="btnClear" Width="70" Margin="5"
                        Click="btnClear_Click">清空</Button>
            </StackPanel>
        </Border>
    </Grid>
</Window>
