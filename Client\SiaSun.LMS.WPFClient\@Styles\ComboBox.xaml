<ResourceDictionary   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
  mc:Ignorable="d"
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <ControlTemplate x:Key="ComboBoxToggleButton" TargetType="{x:Type ToggleButton}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="HoverOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="rectangleOver" Storyboard.TargetProperty="Opacity" To="0.8" />
                <ColorAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Background" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFFFFFFF" />
            </Storyboard>
            <Storyboard x:Key="HoverOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="rectangleOver" Storyboard.TargetProperty="Opacity" To="0" />
                <ColorAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Background" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFEAF2FB" />
            </Storyboard>
            <Storyboard x:Key="PressedOn">
                <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="rectanglePress" Storyboard.TargetProperty="Opacity" To="0.8" />
                <ColorAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Background" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFFFFFFF" />
            </Storyboard>
            <Storyboard x:Key="PressedOff">
                <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="rectanglePress" Storyboard.TargetProperty="Opacity" To="0" />
                <ColorAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Background" Storyboard.TargetProperty="(Shape.Fill).(SolidColorBrush.Color)" To="#FFEAF2FB" />
            </Storyboard>
            <Storyboard x:Key="CheckedOn">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundChecked" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="CheckedOff">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundChecked" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <Grid x:Name="grid">
            <Rectangle x:Name="Background" Fill="#FFEAF2FB" Stroke="#FF9EBBDE" IsHitTestVisible="false" />
            <Rectangle x:Name="BackgroundChecked" Margin="1" IsHitTestVisible="false" Opacity="0" >
                <Rectangle.Fill>
                    <SolidColorBrush Color="{DynamicResource WhiteColor}"/>
                </Rectangle.Fill>
            </Rectangle>
            <Rectangle x:Name="rectangleOver" Width="15" Stroke="#FFE8E8E8" HorizontalAlignment="Right" Opacity="0" Fill="{DynamicResource MouseOverBrush}"/>
            <Rectangle x:Name="rectanglePress" Width="15" Stroke="#FC9E9D9B" HorizontalAlignment="Right" Opacity="0" Fill="{DynamicResource PressedBrush}"/>
            <Rectangle x:Name="DisabledVisualElement" Margin="1" Fill="{DynamicResource DisabledBackgroundBrush}" IsHitTestVisible="false" Visibility="Collapsed" />
            <Path x:Name="BtnArrow" Margin="0,0,4,0" Width="6" Fill="{DynamicResource GlyphBrush}" Stretch="Uniform" HorizontalAlignment="Right" Data="F1 M 301.14,-189.041L 311.57,-189.041L 306.355,-182.942L 301.14,-189.041 Z " />
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsPressed" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOff}" x:Name="PressedOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource PressedOn}" x:Name="PressedOn_BeginStoryboard" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsMouseOver" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                </Trigger.EnterActions>

            </Trigger>
            <Trigger Property="IsChecked" Value="true">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource CheckedOff}" x:Name="CheckedOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource CheckedOn}" x:Name="CheckedOn_BeginStoryboard" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="IsEnabled" Value="False">
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                <Setter Property="Visibility" TargetName="DisabledVisualElement" Value="Visible" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <ControlTemplate x:Key="ComboBoxTextBox" TargetType="{x:Type TextBox}">
        <Border x:Name="PART_ContentHost" Focusable="False" Background="{TemplateBinding Background}" />
    </ControlTemplate>

    <Style TargetType="{x:Type ComboBox}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="6,2,25,2" />
        <Setter Property="Template" Value="{DynamicResource ComboBoxTemplate}" />
    </Style>
    
    <ControlTemplate x:Key="ComboBoxTemplate" TargetType="{x:Type ComboBox}">
        <ControlTemplate.Resources>
            <Storyboard x:Key="FocusedOn">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
            <Storyboard x:Key="FocusedOff">
                <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                    <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                </DoubleAnimationUsingKeyFrames>
            </Storyboard>
        </ControlTemplate.Resources>
        <Grid>
            <ToggleButton Grid.Column="2" Template="{DynamicResource ComboBoxToggleButton}" x:Name="ToggleButton" Focusable="false" IsChecked="{Binding Path=IsDropDownOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}" ClickMode="Press" />
            <ContentPresenter HorizontalAlignment="Left" Margin="1,3,10,3" x:Name="ContentSite" VerticalAlignment="Center" Content="{TemplateBinding SelectionBoxItem}" ContentTemplate="{TemplateBinding SelectionBoxItemTemplate}" ContentTemplateSelector="{TemplateBinding ItemTemplateSelector}" IsHitTestVisible="False" />

            <TextBox Visibility="Hidden" Template="{DynamicResource ComboBoxTextBox}" HorizontalAlignment="Left" Margin="1,3,10,3" x:Name="PART_EditableTextBox" Style="{x:Null}" VerticalAlignment="Center" Focusable="True" Background="Transparent" IsReadOnly="{TemplateBinding IsReadOnly}" />
            <Rectangle x:Name="DisabledVisualElement" Fill="{DynamicResource DisabledBackgroundBrush}" Stroke="{DynamicResource DisabledBorderBrush}" RadiusX="0" RadiusY="0" IsHitTestVisible="false" Opacity="0" />
            <Rectangle x:Name="FocusVisualElement" Margin="-1" Stroke="{DynamicResource FocusBrush}" StrokeThickness="1" IsHitTestVisible="false" Opacity="0" />
            <Popup IsOpen="{TemplateBinding IsDropDownOpen}" Placement="Bottom" x:Name="Popup" Focusable="False" AllowsTransparency="True" PopupAnimation="Slide" Margin="0,1,0,0">
                <Grid MaxHeight="{TemplateBinding MaxDropDownHeight}" MinWidth="{TemplateBinding ActualWidth}" x:Name="DropDown" SnapsToDevicePixels="True">
                    <Border x:Name="DropDownBorder" Margin="0,-1,0,0" BorderBrush="{DynamicResource ControlBorderBrush}" BorderThickness="1" CornerRadius="0,0,3,3" Background="{DynamicResource ControlBackgroundBrush}">
                        <ScrollViewer Margin="4,6,4,6" SnapsToDevicePixels="True" HorizontalScrollBarVisibility="Auto" VerticalScrollBarVisibility="Auto" CanContentScroll="True">

                            <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Contained" />

                        </ScrollViewer>
                    </Border>
                </Grid>
            </Popup>
        </Grid>
        <ControlTemplate.Triggers>
            <Trigger Property="IsFocused" Value="True">
                <Trigger.ExitActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard" />
                </Trigger.ExitActions>
                <Trigger.EnterActions>
                    <BeginStoryboard Storyboard="{StaticResource FocusedOn}" />
                </Trigger.EnterActions>
            </Trigger>
            <Trigger Property="HasItems" Value="false">
                <Setter Property="MinHeight" Value="95" TargetName="DropDownBorder" />
            </Trigger>
            <Trigger Property="IsEnabled" Value="false">
                <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                <Setter Property="Opacity" TargetName="DisabledVisualElement" Value="1" />
            </Trigger>
            <Trigger Property="IsGrouping" Value="true">
                <Setter Property="ScrollViewer.CanContentScroll" Value="false" />
            </Trigger>
            <Trigger Property="AllowsTransparency" SourceName="Popup" Value="true">
                <Setter Property="CornerRadius" Value="4" TargetName="DropDownBorder" />
                <Setter Property="Margin" Value="0,2,0,0" TargetName="DropDownBorder" />
            </Trigger>
            <Trigger Property="IsEditable" Value="true">
                <Setter Property="IsTabStop" Value="false" />
                <Setter Property="Visibility" Value="Visible" TargetName="PART_EditableTextBox" />
                <Setter Property="Visibility" Value="Hidden" TargetName="ContentSite" />
            </Trigger>
        </ControlTemplate.Triggers>
    </ControlTemplate>

    <Style d:IsControlPart="True" TargetType="{x:Type ComboBoxItem}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ComboBoxItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HighlightOn">
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgroundGradientOver" Storyboard.TargetProperty="Opacity" To="0.73" />
                        </Storyboard>
                        <Storyboard x:Key="HighlightOff">
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgroundGradientOver" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOn">
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="BackgroundGradientSelected" Storyboard.TargetProperty="Opacity" To="0.84" />
                        </Storyboard>
                        <Storyboard x:Key="SelectedOff">
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="BackgroundGradientSelected" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid SnapsToDevicePixels="true">
                        <Rectangle x:Name="BackgroundGradientOver" RadiusX="1" RadiusY="1" Stroke="{DynamicResource MouseOverBorderBrush}" Opacity="0" Fill="{DynamicResource MouseOverBrush}"/>
                        <Rectangle x:Name="BackgroundGradientSelected" StrokeThickness="1" RadiusX="1" RadiusY="1" Opacity="0" Fill="{DynamicResource PressedBrush}" Stroke="{DynamicResource PressedBorderBrush}"/>
                        <ContentPresenter x:Name="contentPresenter"  HorizontalAlignment="{TemplateBinding HorizontalContentAlignment}" Margin="{TemplateBinding Padding}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="Selector.IsSelected" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource SelectedOff}" x:Name="SelectedOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource SelectedOn}" x:Name="SelectedOn_BeginStoryboard1" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HighlightOff}" x:Name="HighlightOff_BeginStoryboard1" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HighlightOn}" />
                            </Trigger.EnterActions>
                        </Trigger>

                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource DisabledForegroundBrush}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>