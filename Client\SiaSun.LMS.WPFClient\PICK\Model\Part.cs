﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.WPFClient.PICK.Model
{
    /// <summary>
    /// Part
    /// 箱内区域
    /// writen by <PERSON><PERSON> He
    /// </summary>
    public class Part:MVVM.ObservableObject
    {
        
        private int _row;  //在Box中的所在行数 从1开始
        private int _column; //在Box中的所在列数 从1开始
        private string _part_code; //区域编码
        private int _index; //序列 从0开始
        private bool _isSelected = false;

        /// <summary>
        /// Row
        /// </summary>
        public int Row
        {
            get
            {
                return _row;
            }

            set
            {
                _row = value;
            }
        }

        /// <summary>
        /// Column
        /// </summary>
        public int Column
        {
            get
            {
                return _column;
            }

            set
            {
                _column = value;
            }
        }

        /// <summary>
        /// Part_code
        /// </summary>
        public string Part_code
        {
            get
            {
                return _part_code;
            }

            set
            {
                _part_code = value;
            }
        }

        /// <summary>
        /// Index
        /// </summary>
        public int Index
        {
            get
            {
                return _index;
            }

            set
            {
                _index = value;
            }
        }

        public bool IsSelected
        {
            get
            {
                return _isSelected;
            }

            set
            {
                _isSelected = value;
            }
        }

        public Part()
        {

        }

        /// <summary>
        /// Part构造函数
        /// </summary>
        /// <param name="row">所在行 从1开始</param>
        /// <param name="column">所在列 从1开始</param>
        public Part(int row, int column)
        {
            this._row = row;
            this._column = column;
        }

        /// <summary>
        /// Part构造函数
        /// </summary>
        /// <param name="row">所在行 从1开始</param>
        /// <param name="column">所在列 从1开始</param>
        /// <param name="part_code">区域编码</param>
        public Part(int row,int column,string part_code):this(row,column)
        {
            this.Part_code = part_code;
        }


    }
}
