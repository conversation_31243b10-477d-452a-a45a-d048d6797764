﻿using log4net;

[assembly: log4net.Config.XmlConfigurator(ConfigFile = "Log4Net.config", ConfigFileExtension = "config", Watch = true)]

namespace SignalServer.Models
{
    public class Log
    {
        private static ILog _logInfo = log4net.LogManager.GetLogger("MyInfoLogger");
        private static ILog _logError = log4net.LogManager.GetLogger("MyErrorLogger");

        public static void WriteInfo(string strLog,string methodName)
        {
            _logInfo.Info(string.Format("{0}<br>{1}", methodName, strLog));
        }

        public static void WriteDebug(string strLog, string methodName)
        {
            _logInfo.Debug(string.Format("{0}<br>{1}", methodName, strLog));
        }

        public static void WriteError(string strLog, string methodName)
        {
            _logError.Error(string.Format("{0}<br>{1}", methodName, strLog));
        }
    }
}
