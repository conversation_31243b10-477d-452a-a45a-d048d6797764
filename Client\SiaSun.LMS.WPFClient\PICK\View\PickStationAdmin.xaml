﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.PICK.View.PickStationAdmin"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
    xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="PickStationAdmin"
    d:DesignHeight="300"
    d:DesignWidth="500"
    Loaded="DocumentContent_Loaded"
    mc:Ignorable="d">

    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="Auto" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto" />
            <RowDefinition Height="*" />
        </Grid.RowDefinitions>

        <WrapPanel>
            <Border
                Name="btnArea"
                MinWidth="140"
                Margin="0,0,10,0">
                <WrapPanel VerticalAlignment="Center">
                    <Button
                        Name="stationReflesh"
                        Width="100"
                        Height="30"
                        Margin="5"
                        Click="stationReflesh_Click"
                        Content="刷新" />

                </WrapPanel>
            </Border>
        </WrapPanel>

        <GroupBox
            Grid.Row="1"
            Margin="1,5,1,1"
            Header="拣选工作站信息">
            <uc:ucCommonDataGrid x:Name="gridStation" Margin="1,2,1,1" />
        </GroupBox>

        <GridSplitter
            Grid.RowSpan="2"
            Grid.Column="1"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch" />

        <WrapPanel Grid.Column="2">
            <Border MinWidth="140" Margin="0,0,10,0">
                <WrapPanel VerticalAlignment="Center">
                    <Button
                        Name="orderReflesh"
                        Width="100"
                        Height="30"
                        Margin="5"
                        Click="orderReflesh_Click"
                        Content="刷新" />
                    <Button
                        Name="orderUrgent"
                        Width="100"
                        Height="30"
                        Margin="5"
                        Click="orderUrgent_Click"
                        Content="加急" />
                </WrapPanel>
            </Border>
        </WrapPanel>

        <GroupBox
            Grid.Row="1"
            Grid.Column="2"
            Margin="1,5,1,1"
            Header="唯一码信息">
            <uc:ucCommonDataGrid x:Name="gridOrder" Margin="1,2,1,1" />
        </GroupBox>


    </Grid>
</ad:DocumentContent>
