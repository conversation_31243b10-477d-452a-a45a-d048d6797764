﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.PLAN
{
    /// <summary>
    /// PLAN_IMPORT.xaml 的交互逻辑
    /// </summary>
    public partial class PLAN_IMPORT : AvalonDock.DocumentContent
    {
        string strPlanTypeCode = null;
        int intGoodsID = 0;
        string strImportType = null;

        public PLAN_IMPORT()
        {
            InitializeComponent();
        }

        public PLAN_IMPORT(string PLAN_TYPE_CODE, int GOODS_ID, string ImportType)
            : this()
        {
            this.strPlanTypeCode = PLAN_TYPE_CODE;
            this.intGoodsID = GOODS_ID;
            this.strImportType = ImportType;
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.ERPDataBind();
        }

        private void ERPDataBind()
        {
            this.gridImport.U_WindowName = this.GetType().Name;

            this.gridImport.U_TableName = this.strImportType;

            this.gridImport.U_XmlTableName = this.strImportType;

            this.gridImport.U_Where = " wmi_status = 0";

            this.gridImport.U_OrderField = "WMI_TASKNO";

            this.gridImport.U_DataAccess = "ERPMap";

            this.gridImport.U_AllowChecked = true;

            this.gridImport.U_AllowOperatData = false;

            this.gridImport.U_InitControl();

        }

        /// <summary>
        /// 按钮点击事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnImport":
                        this.ImportExcel();
                        break;
                    case "btnSave":
                        this.SavePlanOrder();
                        break;
                    case "btnRefresh":
                        //this.gridImport.ItemsSource = null;
                        break;
                    case "btnPrint":
                        this.PrintOrder();
                        break;
                    case "btnQuery":
                        this.ERPDataBind();
                        break;
                }
            }
        }

        /// <summary>
        /// 打印入库单
        /// </summary>
        private void PrintOrder()
        {
            //if (this.gridImport.ItemsSource == null)
            //    return;
            //using (DataTable table = (this.gridImport.ItemsSource as DataView).Table)
            //{
            //    new Dialog.ReportWindow(string.Format("制单人员：{0}", MainApp._USER.USER_CODE), this.GetType().Name,"PLAN_IMPORT_PRINT", table).ShowDialog();
            //}
        }

        /// <summary>
        /// 导入EXCEL数据
        /// </summary>
        private void ImportExcel()
        {
            //Dialog.ImportExcelDialog winImport = new Dialog.ImportExcelDialog(false,this.strImportType);
            //if (winImport.ShowDialog() == true)
            //{
            //    DataSet ds = winImport.U_ImportDataSet;

            //    this.gridImport.ItemsSource = ds.Tables[0].DefaultView;
            //    winImport.Close();
            //}
        }

        /// <summary>
        /// 保存
        /// </summary>
        private void SavePlanOrder()
        {
            string mesResult = string.Empty;

            string sResult = string.Empty;

            bool bResult = true;

            DataRowView[] drvCheck = this.gridImport.U_GetCheckedDataRows();

            var drvImport = from v in drvCheck
                            where v["WMI_STATUS"].ToString() == "0"
                            select v;

            if (drvImport.ToArray<DataRowView>().Length == 0)
            {
                MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                return;
            }

            int i = 0;

            foreach (var import in drvImport)
            {
                SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = new Model.PLAN_MAIN();

                List<SiaSun.LMS.Model.PLAN_LIST> lsPLAN_LIST = new List<SiaSun.LMS.Model.PLAN_LIST>();

                SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = MainApp._I_GoodsService.GoodsGetModelGoodsCode(import["WMI_PART"].ToString());

                if (mGOODS_MAIN == null)
                {
                    DataTable dtGoods = MainApp._I_BaseService.GetList(string.Format(" select * from U5PARTS where PAR_CODE = '{0}' ", import["WMI_PART"].ToString()), "ERPMap");

                    if (dtGoods != null && dtGoods.Rows.Count == 1)
                    {
                        mGOODS_MAIN = new Model.GOODS_MAIN();

                        mGOODS_MAIN.GOODS_CLASS_ID = 1;


                        mGOODS_MAIN.GOODS_CODE = dtGoods.Rows[0]["PAR_CODE"].ToString();

                        mGOODS_MAIN.GOODS_NAME = dtGoods.Rows[0]["PAR_DESC"].ToString();

                        mGOODS_MAIN.GOODS_FLAG = "1";

                        object[] outParam = new object[] { };


                        bResult = MainApp._I_BaseService.Invoke("PlanImport", "GoodsCreate", new object[] { mGOODS_MAIN, sResult }, out outParam);

                        if (!bResult)
                        {
                            MainApp._MessageDialog.ShowException(string.Format("导入接口表U5PARTS中物料{0}失败!", import["WMI_PART"].ToString()));

                            return;
                        }

                        mGOODS_MAIN = (SiaSun.LMS.Model.GOODS_MAIN)outParam[0];
                    }
                    else
                    {
                        MainApp._MessageDialog.ShowException(string.Format("接口表U5PARTS中不存在物料编码{0}", import["WMI_PART"].ToString()));

                        return;
                    }
                }


                mPLAN_MAIN.PLAN_CODE = import["WMI_TASKNO"].ToString();

                mPLAN_MAIN.PLAN_CREATE_TIME = import["WMI_CREATEDATE"].ToString();

                mPLAN_MAIN.PLAN_REMARK = import["WMI_COMMENT"].ToString();

                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();

                mPLAN_MAIN.PLAN_FLAG = "1";

                if (import["WMI_TASKTYPE"].ToString() == "R")
                {
                    mPLAN_MAIN.PLAN_TYPE_CODE = "PlanIn";
                }
                else if (import["WMI_TASKTYPE"].ToString() == "I")
                {
                    mPLAN_MAIN.PLAN_TYPE_CODE = "PlanOut";
                }

                SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();

                mPLAN_LIST.PLAN_ID = mPLAN_MAIN.PLAN_ID;

                mPLAN_LIST.GOODS_ID = mGOODS_MAIN.GOODS_ID;

                mPLAN_LIST.PLAN_LIST_QUANTITY = Convert.ToInt32(import["WMI_QTY"]);

                mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = 0;

                mPLAN_LIST.PLAN_LIST_FINISHED_QUANTITY = 0;

                lsPLAN_LIST.Add(mPLAN_LIST);

                bResult = MainApp._I_BaseService.Invoke("PlanImport", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST }, out sResult);

                if (!bResult)
                {
                    string.Format("导入接口表U5WMCSINTERFACE数据失败! WMI_TASKNO {0}", import["WMI_TASKNO"].ToString());

                    return;
                }
                else
                {
                    i++;
                    mesResult += string.Format("{0}条数据导入成功", i.ToString()) + "\n" + string.Format("导入单号{0}", import["WMI_TASKNO"].ToString());

                }

            }


            MainApp._MessageDialog.ShowResult(true, mesResult);


        }



    }
}
