﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.PICK.View
{
    /// <summary>
    /// PickStationAutoBindNewView.xaml 的交互逻辑
    /// </summary>
    public partial class PickStationAutoBindNewView : AvalonDock.DocumentContent
    {
        public PickStationAutoBindNewView()
        {
            InitializeComponent();
            ViewModel.PickStationAutoBindNewViewModel vm = new ViewModel.PickStationAutoBindNewViewModel(this);

            this.DataContext = vm;
            vm.PlayDingDanRequested += Vm_PlayDingDanRequested;

        }

        private void Vm_PlayDingDanRequested(object sender, EventArgs e)
        {
            try
            {
                Dispatcher.BeginInvoke(new Action(
                    () =>
                    {
                        this.mediaElementDingDan.Stop();
                        this.mediaElementDingDan.Position = TimeSpan.Zero;
                        this.mediaElementDingDan.Play();
                    }
                    ));

            }
            catch (Exception ex)
            {
                
            }
        }
    }
}
