﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="PLAN_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="PLAN_LIST" type="SiaSun.LMS.Model.PLAN_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="PLAN_LIST">
			<result property="PLAN_LIST_ID" column="plan_list_id" />
			<result property="PLAN_ID" column="plan_id" />
			<result property="PLAN_LIST_CODE" column="plan_list_code" />
			<result property="PLAN_LIST_QUANTITY" column="plan_list_quantity" />
			<result property="PLAN_LIST_ORDERED_QUANTITY" column="plan_list_ordered_quantity" />
			<result property="PLAN_LIST_FINISHED_QUANTITY" column="plan_list_finished_quantity" />
      <result property="PLAN_LIST_PICKED_QUANTITY" column="plan_list_picked_quantity" />
      <result property="GOODS_ID" column="goods_id" />
			<result property="GOODS_PROPERTY1" column="goods_property1" />
			<result property="GOODS_PROPERTY2" column="goods_property2" />
			<result property="GOODS_PROPERTY3" column="goods_property3" />
			<result property="GOODS_PROPERTY4" column="goods_property4" />
			<result property="GOODS_PROPERTY5" column="goods_property5" />
			<result property="GOODS_PROPERTY6" column="goods_property6" />
			<result property="GOODS_PROPERTY7" column="goods_property7" />
			<result property="GOODS_PROPERTY8" column="goods_property8" />
			<result property="PLAN_LIST_REMARK" column="plan_list_remark" />
      <result property="PLAN_LIST_QUANTITY_APPEND" column="plan_list_quantity_append" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="PLAN_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
        plan_list_id,
        plan_id,
        plan_list_code,
        plan_list_quantity,
        plan_list_ordered_quantity,
        plan_list_finished_quantity,
        plan_list_picked_quantity,
        goods_id,
        goods_property1,
        goods_property2,
        goods_property3,
        goods_property4,
        goods_property5,
        goods_property6,
        goods_property7,
        goods_property8,
        plan_list_remark,
        plan_list_quantity_append
        From PLAN_LIST
      </select>
		
		<select id="PLAN_LIST_SELECT_BY_ID" parameterClass="int" extends = "PLAN_LIST_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_list_id=#PLAN_LIST_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="PLAN_LIST_SELECT_BY_PLAN_ID" parameterClass="int" extends = "PLAN_LIST_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_id=#PLAN_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="PLAN_LIST_SELECT_BY_PLAN_ID_NO_COMPLETE" parameterClass="int" extends = "PLAN_LIST_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          <!--xcjt alter 2016-12-30-->
          <!--plan_id=#PLAN_ID# and plan_list_quantity != plan_list_finished_quantity-->
          plan_id=#PLAN_ID# and plan_list_quantity > plan_list_finished_quantity
        </isParameterPresent>
      </dynamic>
    </select>


    <insert id="PLAN_LIST_INSERT" parameterClass="PLAN_LIST">
      Insert Into PLAN_LIST (
      plan_list_id,
      plan_id,
      plan_list_code,
      plan_list_quantity,
      plan_list_ordered_quantity,
      plan_list_finished_quantity,
      plan_list_picked_quantity,
      goods_id,
      goods_property1,
      goods_property2,
      goods_property3,
      goods_property4,
      goods_property5,
      goods_property6,
      goods_property7,
      goods_property8,
      plan_list_remark,
      plan_list_quantity_append
      )Values(
      #PLAN_LIST_ID#,
      #PLAN_ID#,
      #PLAN_LIST_CODE#,
      #PLAN_LIST_QUANTITY#,
      #PLAN_LIST_ORDERED_QUANTITY#,
      #PLAN_LIST_FINISHED_QUANTITY#,
      #PLAN_LIST_PICKED_QUANTITY#,
      #GOODS_ID#,
      #GOODS_PROPERTY1#,
      #GOODS_PROPERTY2#,
      #GOODS_PROPERTY3#,
      #GOODS_PROPERTY4#,
      #GOODS_PROPERTY5#,
      #GOODS_PROPERTY6#,
      #GOODS_PROPERTY7#,
      #GOODS_PROPERTY8#,
      #PLAN_LIST_REMARK#,
      #PLAN_LIST_QUANTITY_APPEND#
      )
      <!--<selectKey  resultClass="int" type="post" property="PLAN_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>
		
		<update id="PLAN_LIST_UPDATE" parameterClass="PLAN_LIST">
      Update PLAN_LIST Set
      plan_list_id=#PLAN_LIST_ID#,
      plan_id=#PLAN_ID#,
      plan_list_code=#PLAN_LIST_CODE#,
      plan_list_quantity=#PLAN_LIST_QUANTITY#,
      plan_list_ordered_quantity=#PLAN_LIST_ORDERED_QUANTITY#,
      plan_list_finished_quantity=#PLAN_LIST_FINISHED_QUANTITY#,
      plan_list_picked_quantity=#PLAN_LIST_PICKED_QUANTITY#,
      goods_id=#GOODS_ID#,
      goods_property1=#GOODS_PROPERTY1#,
      goods_property2=#GOODS_PROPERTY2#,
      goods_property3=#GOODS_PROPERTY3#,
      goods_property4=#GOODS_PROPERTY4#,
      goods_property5=#GOODS_PROPERTY5#,
      goods_property6=#GOODS_PROPERTY6#,
      goods_property7=#GOODS_PROPERTY7#,
      goods_property8=#GOODS_PROPERTY8#,
      plan_list_remark=#PLAN_LIST_REMARK#,
      plan_list_quantity_append=#PLAN_LIST_QUANTITY_APPEND#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					plan_list_id=#PLAN_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="PLAN_LIST_DELETE" parameterClass="int">
			Delete From PLAN_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_list_id=#PLAN_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

    <delete id="PLAN_LIST_DELETE_BY_PLAN_ID" parameterClass="int">
      Delete From PLAN_LIST
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_id=#PLAN_ID#
        </isParameterPresent>
      </dynamic>
    </delete>
		
	</statements>
</sqlMap>