﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace SSLMS.MobileUI.uc
{
    public partial class ucMANAGE_CHANGE : ucBase
    {
        private DataTable _dtIO_PLAN_LIST = null;

        private DataTable dtSourceST_STORAGE_LIST = null;

        private DataRow _drIO_PLAN_LIST = null;

        private ST_STORAGE_LIST mSourceST_STORAGE_LIST = null;

        private IO_PLAN _mIO_PLAN = null;

        private bool _bZuPan
        {
            get { return !string.IsNullOrEmpty(this.txtSTOCK_BARCODE_Destinal.Text); }
        }

        private bool _bAddSTOCK
        {
            get
            {
                string STOCK_BARCODE = this.txtSTOCK_BARCODE_Destinal.Text.Trim();

                string sResult = string.Empty; ;

                return this._bZuPan && !this._i_st.STORAGE_LIST_CheckStock_1(STOCK_BARCODE, out  sResult);
            }
        }

        public ucMANAGE_CHANGE()
        {
            this.InitializeComponent();

            this.tab.TabPages.RemoveAt(0);

            this.Init();
        }

        public ucMANAGE_CHANGE(string PLAN_ID)
        {
            InitializeComponent();

            try
            {
                this._mIO_PLAN = this._i_io.PLAN_GetModel(Convert.ToInt32(PLAN_ID));

                if (null == _mIO_PLAN)
                {
                    this.tab.TabPages.RemoveAt(0);
                }

                this.Init();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void Init()
        {
            try
            {
                this.cbWAREHOUSE_Source_Bind();

                this.cbWAREHOUSE_Destinal_Bind();

                this.cbSTOCK_Bind();

                if (null != _mIO_PLAN)
                {
                    this.cbScpc.DisplayMember = "PLAN_BILL_NO";

                    this.cbScpc.ValueMember = "PLAN_BILL_NO";

                    this.cbScpc.DataSource = this._i_io.PLAN_LIST_GetList_Scpc(this._mIO_PLAN._plan_id);


                    this.cbOperation.DisplayMember = "Operation";

                    this.cbOperation.ValueMember = "Operation";

                    this.cbOperation.DataSource = this._i_io.PLAN_LIST_GetList_Operation(this._mIO_PLAN._plan_id);


                    this.PLAN_LIST_Bind();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbAREA_Destinal_SelectedValueChanged(object sender, EventArgs e)
        {
            if (cbAREA_Destinal.SelectedValue != null)
            {
                string AREA_ID = this.cbAREA_Destinal.SelectedValue.ToString();

                this.cbCELL_Destinal_Bind(AREA_ID);
            }
        }

        private void cbWAREHOUSE_Destinal_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.cbWAREHOUSE_Destinal.SelectedValue != null)
            {
                string WAREHOUSE_ID = Convert.ToString(this.cbWAREHOUSE_Destinal.SelectedValue);

                this.cbAREA_Destinal_Bind(WAREHOUSE_ID);
            }
        }

        private void cbWAREHOUSE_Source_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.cbWAREHOUSE_Source.SelectedValue != null)
            {
                string WAREHOUSE_ID = Convert.ToString(this.cbWAREHOUSE_Source.SelectedValue);

                this.cbAREA_Source_Bind(WAREHOUSE_ID);
            }
        }

        private void cbAREA_Source_SelectedValueChanged(object sender, EventArgs e)
        {
            if (cbAREA_Destinal.SelectedValue != null)
            {
                this.STORAGE_LIST_Source_Bind();
            }
        }

        private void PLAN_LIST_Bind()
        {
           // this.uclPLAN_LIST.listColumn += "<Field Column=\"STORAGE_LIST_QUANTITY\" fieldType=\"text\" bedit=\"true\" dbType=\"Decimal\" header=\"配盘数量\" index=\"70\" />";

            try
            {
                this.uclPLAN_LIST.listXml = "V_IO_PLAN_LIST";

                this.uclPLAN_LIST.listTable = "V_IO_PLAN_LIST";

                this.uclPLAN_LIST.listWhere = string.Format(" AND STORAGE_LIST_QUANTITY<PLAN_LIST_QUANTITY AND PLAN_ID ={0} AND GOODS_PROPERTY LIKE '{1}|{2}|%'", this._mIO_PLAN._plan_id, (null == this.cbScpc.SelectedValue) ? "%" : this.cbScpc.SelectedValue.ToString(), (null == this.cbOperation.SelectedValue) ? "%" : this.cbOperation.SelectedValue.ToString());

                this.uclPLAN_LIST.colGroup = "GOODS_TYPE_ID";

                this.uclPLAN_LIST.colSplit = "GOODS_PROPERTY";

                this.uclPLAN_LIST.Init();

                this._dtIO_PLAN_LIST = this.uclPLAN_LIST.GetTable();

                if (this._dtIO_PLAN_LIST != null)
                {
                    this._dtIO_PLAN_LIST.DefaultView.Sort = "GOODS_ID";
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbWAREHOUSE_Source_Bind()
        {
            try
            {
                cbWAREHOUSE_Source.ValueMember = "WAREHOUSE_ID";

                cbWAREHOUSE_Source.DisplayMember = "WAREHOUSE_NAME";

                cbWAREHOUSE_Source.DataSource = this._i_st.WAREHOUSE_GetList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbWAREHOUSE_Destinal_Bind()
        {
            try
            {
                cbWAREHOUSE_Destinal.ValueMember = "WAREHOUSE_ID";

                cbWAREHOUSE_Destinal.DisplayMember = "WAREHOUSE_NAME";

                cbWAREHOUSE_Destinal.DataSource = this._i_st.WAREHOUSE_GetList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbAREA_Source_Bind(string WAREHOUSE_ID)
        {
            try
            {
                cbAREA_Source.ValueMember = "AREA_ID";

                cbAREA_Source.DisplayMember = "AREA_NAME";

                cbAREA_Source.DataSource = this._i_st.AREA_GetList_1(WAREHOUSE_ID, SSLMS.Model.AREA_TYPE.xuniku.ToString());
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbAREA_Destinal_Bind(string WAREHOUSE_ID)
        {
            try
            {
                cbAREA_Destinal.ValueMember = "AREA_ID";

                cbAREA_Destinal.DisplayMember = "AREA_NAME";

                cbAREA_Destinal.DataSource = this._i_st.AREA_GetList_1(WAREHOUSE_ID, SSLMS.Model.AREA_TYPE.xuniku.ToString());
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbCELL_Destinal_Bind(string AREA_ID)
        {
            try
            {
                tscbCELL_Destinal.ValueMember = "CELL_ID";

                tscbCELL_Destinal.DisplayMember = "CELL_CODE";

                tscbCELL_Destinal.DataSource = this._i_st.CELL_GetList(string.Empty, AREA_ID, "station", string.Empty);

            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void cbSTOCK_Bind()
        {
            try
            {
                this.cbSTOCK.ValueMember = "GOODS_ID";

                this.cbSTOCK.DisplayMember = "GOODS_NAME";

                Hashtable ht = new Hashtable();

                ht.Add("GOODS_ID", 0);

                ht.Add("GOODS_NAME", "-");

                this.cbSTOCK.DataSource = SSLMS.MobileUI.Common.AddAll(this._i_st.GOODS_GetList(this.cbAREA_Destinal.SelectedValue.ToString(), "2"), ht);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void STORAGE_LIST_Source_Bind()
        {
            try
            {
                string AREA_ID = this.cbAREA_Source.SelectedValue.ToString();

                string STOCK_BARCODE = this.txtSTOCK_BARCODE_Source.Text.Trim();

                string GOODS = this.txtGOODS_Source.Text.Trim();

                this.uclSTORAGE_LIST_Source.listXml = "V_ST_STORAGE_LIST";

                this.uclSTORAGE_LIST_Source.listTable = "V_ST_STORAGE_LIST";

                if (_mIO_PLAN != null)
                {
                    this.uclSTORAGE_LIST_Source.listWhere = string.Format(@" AND AREA_ID = {0} AND STOCK_BARCODE = '{1}' AND (GOODS_CODE LIKE '%{2}%' OR GOODS_NAME LIKE '%{2}%' OR GOODS_REMARK LIKE '%{2}%') AND '{4}|{5}|%' LIKE GOODS_PROPERTY
                                                                   AND (select count(0) from io_plan_list where V_ST_STORAGE_LIST.Goods_Id= IO_PLAN_LIST.Goods_Id AND IO_PLAN_LIST.GOODS_PROPERTY LIKE '{4}|{5}|%' AND PLAN_ID = {3})>0 ", AREA_ID, STOCK_BARCODE, GOODS, this._mIO_PLAN._plan_id, (null == this.cbScpc.SelectedValue) ? "%" : this.cbScpc.SelectedValue.ToString(), (null == this.cbOperation.SelectedValue) ? "%" : this.cbOperation.SelectedValue.ToString());
                }
                else
                {
                    this.uclSTORAGE_LIST_Source.listWhere = string.Format(@"  AND AREA_ID = {0} AND STOCK_BARCODE = '{1}' AND (GOODS_CODE LIKE '%{2}%' OR GOODS_NAME LIKE '%{2}%' OR GOODS_REMARK LIKE '%{2}%') 
                                                                   ", AREA_ID, STOCK_BARCODE, GOODS);
                }

                this.uclSTORAGE_LIST_Source.colGroup = "GOODS_TYPE_ID";

                this.uclSTORAGE_LIST_Source.colSplit = "GOODS_PROPERTY";

                this.uclSTORAGE_LIST_Source.Init();

                dtSourceST_STORAGE_LIST = this.uclSTORAGE_LIST_Source.GetTable();

                if (dtSourceST_STORAGE_LIST.Rows.Count > 0)
                {
                    dtSourceST_STORAGE_LIST.DefaultView.Sort = "STORAGE_LIST_ID";
                }

                foreach (TabPage tp in this.uclSTORAGE_LIST_Source.tabGoodsType.TabPages)
                {
                    (tp.Tag as ucGrid).dgv.DoubleClick += new EventHandler(this.DataGrid_DoubleClick);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }        
        
        private void STORAGE_LIST_Destinal_Bind()
        {
            try
            {
                string AREA_ID = this.cbAREA_Destinal.SelectedValue.ToString();

                string STOCK_BARCODE = this.txtSTOCK_BARCODE_Destinal.Text.Trim();

                string GOODS = this.txtGOODS_Destinal.Text.Trim();

                this.uclSTORAGE_LIST_Destinal.listXml = "V_ST_STORAGE_LIST";

                this.uclSTORAGE_LIST_Destinal.listTable = "V_ST_STORAGE_LIST";

                this.uclSTORAGE_LIST_Destinal.listWhere = string.Format(" AND AREA_ID = {0} AND STOCK_BARCODE = '{1}' AND (GOODS_CODE LIKE '%{2}%' OR GOODS_NAME LIKE '%{2}%' OR GOODS_REMARK LIKE '%{2}%')", AREA_ID, STOCK_BARCODE, GOODS);

                this.uclSTORAGE_LIST_Destinal.colGroup = "GOODS_TYPE_ID";

                this.uclSTORAGE_LIST_Destinal.colSplit = "GOODS_PROPERTY";

                this.uclSTORAGE_LIST_Destinal.GOODS_TYPE_SQL = "SELECT GOODS_TYPE_ID,GOODS_TYPE_NAME FROM B_GOODS_TYPE";

                this.uclSTORAGE_LIST_Destinal.Init();

                this.uclSTORAGE_LIST_Destinal.SetEditColumns("STORAGE_LIST_QUANTITY", Color.GreenYellow);

                this.uclSTORAGE_LIST_Destinal.SetEditColumns("scpc", Color.GreenYellow);

                this.uclSTORAGE_LIST_Destinal.SetEditColumns("Operation", Color.GreenYellow);

                this.uclSTORAGE_LIST_Destinal.SetEditColumns("xtm", Color.GreenYellow);

                foreach (TabPage tp in this.uclSTORAGE_LIST_Destinal.tabGoodsType.TabPages)
                {
                    DataTable dt = (tp.Tag as ucGrid).dgv.DataSource as DataTable;

                    dt.ColumnChanged += new DataColumnChangeEventHandler(dt_ColumnChanged);
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }

        }

        private void DataGrid_DoubleClick(object sender, EventArgs e)
        {
            try
            {
                MyDataGrid dgvSource = sender as MyDataGrid;

                MyDataGrid dgvDesctianl = (SSLMS.MobileUI.Common.GetControl(this.uclSTORAGE_LIST_Destinal, dgvSource.Name) as MyDataGrid);

                if (dgvDesctianl != null)
                {
                    (dgvDesctianl.DataSource as DataTable).DefaultView.Sort = "STORAGE_LIST_ID";

                    int index = -1;

                    index = (dgvDesctianl.DataSource as DataTable).DefaultView.Find(dgvSource.GetCurDataRow()["STORAGE_LIST_ID"]);

                    if (index == -1)
                    {
                        string scpc = string.Empty;

                        if ((dgvSource.DataSource as DataTable).Columns.Contains("scpc") && (dgvDesctianl.DataSource as DataTable).Columns.Contains("scpc") && this._mIO_PLAN != null)
                        {
                            scpc = dgvSource.GetCurDataRow()["scpc"].ToString();

                            dgvSource.GetCurDataRow()["scpc"] = _mIO_PLAN._plan_bill_no;

                            string operation = string.Empty;

                            if ((dgvSource.DataSource as DataTable).Columns.Contains("Operation") && (dgvDesctianl.DataSource as DataTable).Columns.Contains("Operation") && this.cbOperation.SelectedValue != null)
                            {
                                operation = dgvSource.GetCurDataRow()["Operation"].ToString();

                                dgvSource.GetCurDataRow()["Operation"] = this.cbOperation.SelectedValue.ToString();
                            }

                            (dgvDesctianl.DataSource as DataTable).Rows.Add(dgvSource.GetCurDataRow().ItemArray);

                            dgvSource.GetCurDataRow()["scpc"] = scpc;

                            if (operation != string.Empty)
                            {
                                dgvSource.GetCurDataRow()["Operation"] = operation;
                            }
                        }

                        else
                        {
                            (dgvDesctianl.DataSource as DataTable).Rows.Add(dgvSource.GetCurDataRow().ItemArray);
                        }
                    }

                    (dgvDesctianl.DataSource as DataTable).DefaultView.Sort = null;
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        private void dt_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            try
            {
                bool bResult = true;

                string sResult = string.Empty;

                switch (e.Column.ColumnName)
                {
                    case "STORAGE_LIST_QUANTITY":

                        int index = -1;

                        if (_mIO_PLAN != null)
                        {
                            index = this._dtIO_PLAN_LIST.DefaultView.Find(e.Row["GOODS_ID"]);

                            this._drIO_PLAN_LIST = this._dtIO_PLAN_LIST.DefaultView[index].Row;
                        }

                        mSourceST_STORAGE_LIST = this._i_st.STORAGE_LIST_GetModel(Convert.ToInt32(e.Row["STORAGE_LIST_ID"]));

                        if (null == mSourceST_STORAGE_LIST)
                        {
                            e.Row.SetColumnError(e.Column, "未找到源库存");
                        }
                        else
                        {
                            if (Convert.ToInt32(e.ProposedValue) < 0)
                            {
                                e.Row.SetColumnError(e.Column, "请输入大于0的数量");
                            }
                            else if (Convert.ToInt32(e.ProposedValue) > mSourceST_STORAGE_LIST._storage_list_quantity)
                            {
                                e.Row.SetColumnError(e.Column, "数量应小于源库存数量");
                            }
                            else if (_mIO_PLAN != null && Convert.ToInt32(e.ProposedValue) > Convert.ToDecimal(_drIO_PLAN_LIST["PLAN_LIST_QUANTITY"]) - Convert.ToDecimal(_drIO_PLAN_LIST["STORAGE_LIST_QUANTITY"]))
                            {
                                e.Row.SetColumnError(e.Column, "数量应小于计划未配盘数量");
                            }
                            else
                            {
                                e.Row.SetColumnError(e.Column, null);
                            }
                        }

                        if (_mIO_PLAN != null)
                        {
                            bResult = mSourceST_STORAGE_LIST != null && Convert.ToInt32(e.ProposedValue) > 0 && Convert.ToInt32(e.ProposedValue) <= mSourceST_STORAGE_LIST._storage_list_quantity
                                      && Convert.ToInt32(e.ProposedValue) <= Convert.ToDecimal(_drIO_PLAN_LIST["PLAN_LIST_QUANTITY"]) - Convert.ToDecimal(_drIO_PLAN_LIST["STORAGE_LIST_QUANTITY"]);
                        }

                        else
                        {
                            bResult = mSourceST_STORAGE_LIST != null && Convert.ToInt32(e.ProposedValue) > 0 && Convert.ToInt32(e.ProposedValue) <= mSourceST_STORAGE_LIST._storage_list_quantity;
                        }

                        break;
                }

                if (!bResult)
                {
                    e.Row.RowError = "该行数据有错误!";
                }
                else
                {
                    e.Row.RowError = string.Empty;
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        public override void OnRead(object sender, Symbol.Barcode.ReaderData readerData)
        {
            try
            {
                if (this.tab.TabPages[this.tab.SelectedIndex] == tpSource)
                {
                    if (this.txtGOODS_Source.Focused)
                    {
                        this.txtGOODS_Source.Text = readerData.Text;

                        this.txtSTOCK_BARCODE_Source.Focus();
                    }

                    if (this.txtSTOCK_BARCODE_Source.Focused)
                    {
                        this.txtSTOCK_BARCODE_Source.Text = readerData.Text;

                        this.txtGOODS_Source.Focus();
                    }

                    STORAGE_LIST_Source_Bind();
                }

                if (this.tab.TabPages[this.tab.SelectedIndex] == tpTarget)
                {
                    if (this.txtGOODS_Destinal.Focused)
                    {
                        this.txtGOODS_Destinal.Text = readerData.Text.TrimEnd();

                        foreach(TabPage tp in this.uclSTORAGE_LIST_Source.tabGoodsType.TabPages)
                        {
                            int index = -1;

                            MyDataGrid dgvSource = (tp.Tag as ucGrid).dgv;

                            MyDataGrid dgvDesctianl = (SSLMS.MobileUI.Common.GetControl(this.uclSTORAGE_LIST_Destinal, dgvSource.Name) as ucGrid).dgv;

                            (dgvSource.DataSource as DataTable).DefaultView.Sort = "GOODS_ID";

                            index = (dgvSource.DataSource as DataTable).DefaultView.Find(this.txtGOODS_Destinal.Text);

                            if (index != -1)
                            {
                                (dgvDesctianl.DataSource as DataTable).Rows.Add((dgvSource.DataSource as DataTable).DefaultView[index].Row.ItemArray);
                            }
                            else
                            {
                                (dgvDesctianl.DataSource as DataTable).DefaultView.Sort = "GOODS_ID";
                            }

                            (dgvDesctianl.DataSource as DataTable).DefaultView.Sort = null;
                        }

                        if (this.txtSTOCK_BARCODE_Destinal.Focused)
                        {
                            this.txtSTOCK_BARCODE_Destinal.Text = readerData.Text.TrimEnd();

                            this.STORAGE_LIST_Destinal_Bind();
                        }
                    }
                }

               // base.OnRead(sender, readerData);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }

        #region 计划事件

        private void cbOperation_SelectedValueChanged(object sender, EventArgs e)
        {
            this.PLAN_LIST_Bind();
        }

        #endregion

        #region 源货位事件

        private void Source_KeyDown(object sender, KeyEventArgs e)
        {
            if (Keys.Enter != e.KeyCode)
            {
                return;
            }

            this.STORAGE_LIST_Source_Bind();
        }

        #endregion

        #region 目的货位事件

        private void Destinal_KeyDown(object sender, KeyEventArgs e)
        {
            if (Keys.Enter != e.KeyCode)
            {
                return;
            }

            this.STORAGE_LIST_Destinal_Bind();
        }

        #endregion

        private void tsbSave_Click(object sender, EventArgs e)
        {
            try
            {
                if (null == this.tscbCELL_Destinal.SelectedValue)
                {
                    MessageBox.Show("请选择货位！");

                    return;
                }

                if (this._bAddSTOCK)
                {
                    if (this.cbSTOCK.SelectedValue == null || this.cbSTOCK.SelectedValue.ToString().Equals("0"))
                    {
                        MessageBox.Show("请选择搬运容器！");

                        return;
                    }
                }

                foreach (TabPage tp in this.uclSTORAGE_LIST_Destinal.tabGoodsType.TabPages)
                {
                    (tp.Tag as ucGrid).dgv.BindingContext[(tp.Tag as ucGrid).dgv.DataSource].EndCurrentEdit();

                    DataTable dt = (tp.Tag as ucGrid).dgv.DataSource as DataTable;

                    if (dt.HasErrors)
                    {
                        MessageBox.Show("数据校验错误!");

                        return;
                    }
                }

                if (DialogResult.OK != MessageBox.Show("确认?", "提示", MessageBoxButtons.OKCancel, 0, MessageBoxDefaultButton.Button1))
                {
                    return;
                }

                bool bResult = true;

                string sResult = string.Empty;

                if (this._i_st.STORAGE_LIST_CheckStock_2(this.txtSTOCK_BARCODE_Destinal.Text.TrimEnd(), this.cbAREA_Destinal.SelectedValue.ToString(), out sResult))
                {
                    MessageBox.Show(sResult);

                    return;
                }

                int MANAGE_ID = 0;

                IO_MANAGE mIO_MANAGE = new IO_MANAGE();

                mIO_MANAGE._plan_id = 0;

                mIO_MANAGE._manage_tasktype = SSLMS.Model.MANAGE_TYPE.MOVE.ToString("d");

                mIO_MANAGE._manage_mode = SSLMS.Model.MANAGE_MODE.hand.ToString("d");

                mIO_MANAGE._manage_status = SSLMS.Model.MANAGE_STATUS.ddzx.ToString();

                mIO_MANAGE._stock_barcode = this.txtSTOCK_BARCODE_Destinal.Text.TrimEnd();

                mIO_MANAGE._end_cell_id = Convert.ToInt32(this.tscbCELL_Destinal.SelectedValue);

                mIO_MANAGE._manage_operator = this._USER._user_name;

                IList<IO_MANAGE_LIST> lsIO_MANAGE_LIST = new List<IO_MANAGE_LIST>();

                foreach (TabPage tp in this.uclSTORAGE_LIST_Destinal.tabGoodsType.TabPages)
                {
                    MyDataGrid dgvSTORAGE_LIST = (tp.Tag as ucGrid).dgv;

                    foreach (DataRow drSTORAGE_LIST in (tp.Tag as ucGrid).CommonCol().Rows)
                    {
                        if (DataRowState.Added.Equals(drSTORAGE_LIST.RowState) || DataRowState.Modified.Equals(drSTORAGE_LIST.RowState))
                        {
                            IO_MANAGE_LIST mIO_MANAGE_LIST = new IO_MANAGE_LIST();

                            mIO_MANAGE_LIST._goods_id = Convert.ToInt32(drSTORAGE_LIST["GOODS_ID"]);

                            mIO_MANAGE_LIST._goods_property = Convert.ToString(drSTORAGE_LIST["GOODS_PROPERTY"]);

                            mIO_MANAGE_LIST._storage_list_id = Convert.ToInt32(drSTORAGE_LIST["STORAGE_LIST_ID"]);

                            mIO_MANAGE_LIST._manage_list_quantity = Convert.ToDecimal(drSTORAGE_LIST["STORAGE_LIST_QUANTITY"]);

                            if (mIO_MANAGE_LIST._manage_list_quantity <= 0)
                            {
                                MessageBox.Show("配盘数量错误!");

                                return;
                            }

                            mIO_MANAGE_LIST._manage_list_remark = Convert.ToString(drSTORAGE_LIST["STORAGE_LIST_REMARK"]);

                            lsIO_MANAGE_LIST.Add(mIO_MANAGE_LIST);
                        }
                    }
                }

                bResult = this._i_io.MANAGE_Create(mIO_MANAGE, lsIO_MANAGE_LIST.ToArray<IO_MANAGE_LIST>(), out sResult, out MANAGE_ID);

                if (bResult)
                {
                    bResult = this._i_io.MANAGE_Complete(MANAGE_ID, out sResult);

                    if (bResult)
                    {
                        if (this._bAddSTOCK && (0 != Convert.ToInt32(this.cbSTOCK.SelectedValue)))
                        {
                            bResult = this._i_io.MANAGE_Create_3(this._USER, Convert.ToInt32(this.tscbCELL_Destinal.SelectedValue), Convert.ToInt32(this.cbSTOCK.SelectedValue), 1, this.txtSTOCK_BARCODE_Destinal.Text.Trim(), out sResult, out MANAGE_ID);

                            if (bResult)
                            {
                                bResult = this._i_io.MANAGE_Complete(MANAGE_ID, out sResult);

                                if (bResult)
                                {
                                    sResult = string.Format("组盘成功");
                                }
                                else
                                {
                                    sResult = string.Format("组盘失败\n{0}", sResult);

                                    this._i_io.MANAGE_Cancel(MANAGE_ID, out sResult);
                                }
                            }
                            else
                            {
                                sResult = string.Format("组盘失败\n{0}", sResult);
                            }
                        }


                        this.STORAGE_LIST_Source_Bind();

                        this.STORAGE_LIST_Destinal_Bind();

                        if (null != this._mIO_PLAN)
                        {
                            //this.tscbScpc.DataSource = this._i_io.PLAN_LIST_GetList_Scpc(this._mIO_PLAN._plan_id);

                            //this.tscbSequence.DataSource = this._i_io.PLAN_LIST_GetList_Scpc(this._mIO_PLAN._plan_id);

                            this.PLAN_LIST_Bind();
                        }

                        MessageBox.Show(sResult);

                    }
                    else
                    {
                        this._i_io.MANAGE_Cancel(MANAGE_ID, out sResult);

                        MessageBox.Show(sResult);
                    }
                }
                else
                {
                    MessageBox.Show(sResult);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！" + ex.Message);
            }
        }
    }
}
