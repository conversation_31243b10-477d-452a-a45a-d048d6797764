﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}</ProjectGuid>
    <OutputType>Exe</OutputType>
    <RootNamespace>SiaSun.LMS.Scaner</RootNamespace>
    <AssemblyName>SiaSun.LMS.Scaner</AssemblyName>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <AutoGenerateBindingRedirects>true</AutoGenerateBindingRedirects>
    <TargetFrameworkProfile />
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationIcon>logo.ico</ApplicationIcon>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="log4net">
      <HintPath>..\..\Lib\log4net.dll</HintPath>
    </Reference>
    <Reference Include="PresentationFramework" />
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="AsynTcpClient.cs" />
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="TcpScaner.cs" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="logo.ico" />
    <Content Include="ScanerConfig.xml">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <None Include="App.config" />
    <Content Include="Log4Net.config">
      <SubType>Designer</SubType>
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="..\..\Common\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj">
      <Project>{fd10f3e5-a233-4f52-b4ee-33189d84dbef}</Project>
      <Name>SiaSun.LMS.Common</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Common\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj">
      <Project>{67d2bf1b-cf66-4657-91b3-8eedfc46bd40}</Project>
      <Name>SiaSun.LMS.Model</Name>
    </ProjectReference>
    <ProjectReference Include="..\..\Service\SiaSun.LMS.Interface\SiaSun.LMS.Interface.csproj">
      <Project>{612d8c56-ecec-48b9-87f9-af80bf81e070}</Project>
      <Name>SiaSun.LMS.Interface</Name>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>