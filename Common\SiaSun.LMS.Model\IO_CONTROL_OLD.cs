﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     LaiHaMa
 *       日期：     2010-9-14
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
	
	/// <summary>
	/// IO_CONTROL_OLD 
	/// </summary>
	[Serializable]
	public class IO_CONTROL_OLD
	{
		public IO_CONTROL_OLD()
		{
			
		}
		
		private System.Decimal _fid;
		private System.Decimal _frelativeid;
		private System.Decimal _fmanageid;
		private string _fstockbarcode;
		private string _ftasklevel;
		private System.Decimal _fstartcellid;
		private System.Decimal _fendcellid;
		private string _fbegintime;
		private string _fendtime;
		private string _fstatus;
		private string _fremark;
		
		///<sumary>
		/// 控制编号
		///</sumary>
		public System.Decimal FID
		{
			get{return _fid;}
			set{_fid = value;}
		}
		///<sumary>
		/// 关联编号
		///</sumary>
		public System.Decimal FRELATIVEID
		{
			get{return _frelativeid;}
			set{_frelativeid = value;}
		}
		///<sumary>
		/// 任务编号
		///</sumary>
		public System.Decimal FMANAGEID
		{
			get{return _fmanageid;}
			set{_fmanageid = value;}
		}
		///<sumary>
		/// 托盘条码
		///</sumary>
		public string FSTOCKBARCODE
		{
			get{return _fstockbarcode;}
			set{_fstockbarcode = value;}
		}
		///<sumary>
		/// 任务级别
		///</sumary>
		public string FTASKLEVEL
		{
			get{return _ftasklevel;}
			set{_ftasklevel = value;}
		}
		///<sumary>
		/// 起始货位
		///</sumary>
		public System.Decimal FSTARTCELLID
		{
			get{return _fstartcellid;}
			set{_fstartcellid = value;}
		}
		///<sumary>
		/// 终止货位
		///</sumary>
		public System.Decimal FENDCELLID
		{
			get{return _fendcellid;}
			set{_fendcellid = value;}
		}
		///<sumary>
		/// 开始时间
		///</sumary>
		public string FBEGINTIME
		{
			get{return _fbegintime;}
			set{_fbegintime = value;}
		}
		///<sumary>
		/// 结束时间
		///</sumary>
		public string FENDTIME
		{
			get{return _fendtime;}
			set{_fendtime = value;}
		}
		///<sumary>
		/// 状态
		///</sumary>
		public string FSTATUS
		{
			get{return _fstatus;}
			set{_fstatus = value;}
		}
		///<sumary>
		/// 备注
		///</sumary>
		public string FREMARK
		{
			get{return _fremark;}
			set{_fremark = value;}
		}
	}
}
