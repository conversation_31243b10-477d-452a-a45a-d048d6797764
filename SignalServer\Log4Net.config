﻿<!-- Level的级别，由高到低 -->
<!-- None > Fatal > ERROR > WARN > DEBUG > INFO > ALL-->
<!-- 解释：如果level是ERROR，则在cs文件里面调用log4net的info()方法，则不会写入到日志文件中-->
<log4net>
  <!--错误日志类-->
  <logger name="MyErrorLogger">
    <!--日志类的名字-->
    <level value="ALL" />
    <!--定义记录的日志级别-->
    <appender-ref ref="ErrorAppender" />
    <!--记录到哪个介质中去-->
  </logger>
  <!--信息日志类-->
  <logger name="MyInfoLogger">
    <level value="ALL" />
    <appender-ref ref="InfoAppender" />
  </logger>
  <!--错误日志附加介质-->
  <appender name="ErrorAppender" type="log4net.Appender.RollingFileAppender">
    <!-- name属性指定其名称,type则是log4net.Appender命名空间的一个类的名称,意思是,指定使用哪种介质-->
    <param name="File" value="Log\\LogError\\" />
    <!--日志输出到exe程序这个相对目录下-->
    <param name="AppendToFile" value="true" />
    <!--输出的日志不会覆盖以前的信息-->
    <param name="MaxSizeRollBackups" value="100" />
    <!--备份文件的个数-->
    <param name="MaxFileSize" value="102400" />
    <!--当个日志文件的最大大小-->
    <param name="StaticLogFileName" value="false" />
    <!--是否使用静态文件名-->
    <param name="DatePattern" value="yyyyMMdd&quot;.htm&quot;" />
    <!--日志文件名-->
    <param name="RollingStyle" value="Date" />
    <!--文件创建的方式，这里是以Date方式创建-->
    <!--错误日志布局-->
    <layout type="log4net.Layout.PatternLayout">
      <param name="ConversionPattern" value="&lt;HR COLOR=#ff4757&gt;%n&lt;div class=&quot;%p&quot; style=&quot;background-color:#ff4757;&quot;&gt;异常时间：%d [%t] &lt;BR&gt;%n异常级别：%-5p &lt;BR&gt;%n异 常 类：%c [%a] &lt;BR&gt; &lt;/div&gt;%n&lt;div style=&quot;background-color:#f1f2f6;padding:5px;&quot;&gt;%m &lt;/div&gt; &lt;BR&gt;%n &lt;HR Size=1&gt;"  />
    </layout>
  </appender>
  <!--信息日志附加介质-->
  <appender name="InfoAppender" type="log4net.Appender.RollingFileAppender">
    <param name="File" value="Log\\LogInfo\\" />
    <param name="AppendToFile" value="true" />
    <param name="MaxFileSize" value="102400" />
    <param name="MaxSizeRollBackups" value="100" />
    <param name="StaticLogFileName" value="false" />
    <param name="DatePattern" value="yyyyMMdd&quot;.htm&quot;" />
    <param name="RollingStyle" value="Date" />
    <!--信息日志布局-->
    <layout type="log4net.Layout.PatternLayout">
      <!--<param name="Header" value=""/>-->
      <!--<param name="Footer" value=""/>-->
      <param name="ConversionPattern" value="&lt;HR COLOR=#1e90ff&gt;%n&lt;div class=&quot;%p&quot; style=&quot;background-color:#1e90ff;&quot;&gt;日志时间：%d [%t] &lt;BR&gt;%n日志级别：%-5p &lt;BR&gt;%n日 志 类：%c [%a] &lt;BR&gt; &lt;/div&gt;%n&lt;div style=&quot;background-color:#7bed9f;padding:5px;&quot;&gt;%m &lt;/div&gt; &lt;BR&gt;%n &lt;HR Size=1&gt;"  />
    </layout>
    <filter type="log4net.Filter.LevelRangeFilter">
      <levelMax value="FATAL"/>
      <levelMin value="INFO"/>
    </filter>
    <filter type="log4net.Filter.DenyAllFilter"/><!--不加这个过滤器也可以-->
    
  </appender>
</log4net>