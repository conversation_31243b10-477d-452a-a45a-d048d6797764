﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_RELATION
	/// </summary>
	public class P_SYS_RELATION : P_Base_House
	{
		public P_SYS_RELATION ()
		{
			//
			// TODO: 此处添加SYS_RELATION的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_RELATION> GetList()
		{
			return ExecuteQueryForList<SYS_RELATION>("SYS_RELATION_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_RELATION sys_relation)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("SYS_RELATION");
                sys_relation.RELATION_ID = id;
            }

            return ExecuteInsert("SYS_RELATION_INSERT",sys_relation);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_RELATION sys_relation)
		{
			return ExecuteUpdate("SYS_RELATION_UPDATE",sys_relation);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_RELATION GetModelRelationCode(string RELATION_CODE)
		{
            return ExecuteQueryForObject<SYS_RELATION>("SYS_RELATION_SELECT_BY_RELATION_CODE", RELATION_CODE);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 RELATION_ID)
		{
			return ExecuteDelete("SYS_RELATION_DELETE",RELATION_ID);
		}
		

	}
}
