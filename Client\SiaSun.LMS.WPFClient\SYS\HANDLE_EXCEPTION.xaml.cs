﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Data;
using System.Windows.Controls;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// HANDLE_EXCEPTION.xaml 的交互逻辑
    /// </summary>
    public partial class HANDLE_EXCEPTION :  AvalonDock.DocumentContent
    {
        private string _opriation;
        public HANDLE_EXCEPTION(string opriation)
        {
            this._opriation = opriation;

            InitializeComponent();
        }
        
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            LoadData();

            foreach (TabItem item in this.tbcExceptionTab.Items)
            {
                if (item != null && item.Name != _opriation)
                {
                    item.Visibility = Visibility.Collapsed;
                }
                else if(item != null && item.Name == _opriation)
                {
                    item.IsSelected = true;
                }
            }
        }

        /// <summary>
        /// 初始化加载数据
        /// </summary>
        public void LoadData()
        {
            switch (this._opriation)
            {
                case "ResetPlan":
                    DataTable dtPlanId = MainApp._I_BaseService.GetList(string.Format("select PLAN_ID from PLAN_MAIN where PLAN_TYPE_CODE = 'PlanPick' and PLAN_STATUS = '{0}'", Enum.PLAN_STATUS.Executing.ToString()));
                    this.cbxPlanId.ItemsSource = dtPlanId.DefaultView;
                    this.cbxPlanId.SelectedValuePath = "PLAN_ID";
                    this.cbxPlanId.DisplayMemberPath = "PLAN_ID";
                    break;

                case "StorageException":
                    this.cbxValue.Items.Add("异常");
                    this.cbxValue.Items.Add("正常");
                    this.cbxValue.SelectedIndex = 0;
                    break;

                case "SetGoodsPriorOut":
                    this.cbxValueGoodsPriorOut.Items.Add("优先");
                    this.cbxValueGoodsPriorOut.Items.Add("不优先");
                    this.cbxValueGoodsPriorOut.SelectedIndex = 0;
                    break;

                case "ContinuousManageDown":
                    this.cbxManageDownRow.Items.Add("选择要下架的排");
                    this.cbxManageDownRow.Items.Add("1排");
                    this.cbxManageDownRow.Items.Add("2排");
                    this.cbxManageDownRow.Items.Add("3排");
                    this.cbxManageDownRow.Items.Add("4排");
                    this.cbxManageDownRow.Items.Add("5排");
                    this.cbxManageDownRow.Items.Add("6排");
                    this.cbxManageDownRow.Items.Add("7排");
                    this.cbxManageDownRow.Items.Add("8排");
                    this.cbxManageDownRow.Items.Add("9排");
                    this.cbxManageDownRow.Items.Add("10排");
                    this.cbxManageDownRow.Items.Add("11排");
                    this.cbxManageDownRow.Items.Add("12排");
                    this.cbxManageDownRow.Items.Add("13排");
                    this.cbxManageDownRow.Items.Add("14排");
                    this.cbxManageDownRow.Items.Add("15排");
                    this.cbxManageDownRow.Items.Add("16排");
                    this.cbxManageDownRow.Items.Add("17排");
                    this.cbxManageDownRow.Items.Add("18排");
                    this.cbxManageDownRow.Items.Add("19排");
                    this.cbxManageDownRow.Items.Add("20排");
                    this.cbxManageDownRow.Items.Add("21排");
                    this.cbxManageDownRow.Items.Add("22排");
                    this.cbxManageDownRow.Items.Add("23排");
                    this.cbxManageDownRow.Items.Add("24排");
                    this.cbxManageDownRow.SelectedIndex = 0;

                    this.cbxStationName.Items.Add("21092五楼料箱1期紧急出库站台");
                    this.cbxStationName.Items.Add("21098五楼料箱1期合箱工作站");
                    this.cbxStationName.Items.Add("41027五楼料箱拣选工作站(1号)");
                    this.cbxStationName.Items.Add("41012五楼料箱拣选工作站(2号)");
                    this.cbxStationName.Items.Add("41001五楼料箱拣选工作站(3号)");
                    this.cbxStationName.Items.Add("22134五楼料箱2期紧急出库站台");
                    this.cbxStationName.Items.Add("22141五楼料箱2期合箱工作站");
                    this.cbxStationName.Items.Add("32057五楼料箱拣选工作站(4号)");
                    this.cbxStationName.Items.Add("32019五楼料箱拣选工作站(5号)");
                    this.cbxStationName.Items.Add("32003五楼料箱拣选工作站(6号)");
                    this.cbxStationName.SelectedIndex = 0;
                    break;

                case "ArrangeStationWorkMode":
                    DataTable dtWorkMode21098 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-21098'");
                    if (dtWorkMode21098 != null && dtWorkMode21098.Rows.Count > 0)
                    {
                        if(dtWorkMode21098.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.ArrangeWorkMode.Arrange.ToString())
                        {
                            this.rbtn21098Arrange.IsChecked = true;
                        }
                        else if(dtWorkMode21098.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.ArrangeWorkMode.ArrangeGoods.ToString())
                        {
                            this.rbtn21098ArrangeGoods.IsChecked = true;
                        }
                        else if(dtWorkMode21098.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.ArrangeWorkMode.Emerg.ToString())
                        {
                            this.rbtn21098Emerg.IsChecked = true;
                        }
                    }
                    DataTable dtWorkMode22141 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-22141'");
                    if (dtWorkMode22141 != null && dtWorkMode22141.Rows.Count > 0)
                    {
                        if (dtWorkMode22141.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.ArrangeWorkMode.Arrange.ToString())
                        {
                            this.rbtn22141Arrange.IsChecked = true;
                        }
                        else if(dtWorkMode22141.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.ArrangeWorkMode.ArrangeGoods.ToString())
                        {
                            this.rbtn22141ArrangeGoods.IsChecked = true;
                        }
                        else if (dtWorkMode22141.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.ArrangeWorkMode.Emerg.ToString())
                        {
                            this.rbtn22141Emerg.IsChecked = true;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        
        /// <summary>
        /// 重置计划
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReset_Click(object sender, RoutedEventArgs e)
        {
            bool result = true;
            string message = "";

            int planId = 0;
            if (this.cbxPlanId.SelectedValue != null && int.TryParse(this.cbxPlanId.SelectedValue.ToString(), out planId) && planId<=0)
            {
                MainApp._MessageDialog.ShowResult(false, "请正确选择计划ID");
                return;
            }

            try
            {
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmExecute,string.Format("计划[{0}]重置", this.cbxPlanId.SelectedValue)) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    result = MainApp._I_PlanService.PlanResetStatus(planId, out message);
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("程序发生异常_{0}", ex.Message);
            }
            finally
            {
                MainApp._MessageDialog.ShowResult(result, result ? "成功" : message);
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Plan, MainApp._USER.USER_NAME, result?Enum.LOG_LEVEL.Information:Enum.LOG_LEVEL.Error, string.Format("HANDLE_EXCEPTION.btnReset_Click():用户重置计划{0}_计划ID[1]", result ? "成功" : "失败" + message, this.cbxPlanId.SelectedValue));
            }

            LoadData();
        }
        
        /// <summary>
        /// 设置箱异常状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetStorageException_Click(object sender, RoutedEventArgs e)
        {
            if (!Common.RegexValid.IsValidate(this.tbxStockBarcode.Text.TrimEnd(),MainApp._SYS_PARAMETER["BoxBarcodeValidRegex"]))
            {
                //传入的条码不符合标准
                MainApp._MessageDialog.ShowResult(false, string.Format("输入的条码[{0}]不符合标准", this.tbxStockBarcode.Text));
                return;
            }
            bool isException = this.cbxValue.SelectedValue.ToString() == "异常" ? true : false;
            string outMessage = string.Empty;
            if (MainApp._I_StorageService.SetStorageException(MainApp._USER, this.tbxStockBarcode.Text.TrimEnd(), isException, out outMessage))
            {
                MainApp._MessageDialog.ShowResult(true, string.Format("条码[{0}]的箱库存已被标记为[{1}]", this.tbxStockBarcode.Text, this.cbxValue.SelectedValue.ToString()));
            }
            else
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("标记箱库存异常失败_箱条码[{0}]_错误信息[{1}]", this.tbxStockBarcode.Text, outMessage));
            }
        }

        /// <summary>
        /// 设置格子异常状态
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetStorageException1_Click(object sender, RoutedEventArgs e)
        {
            if (!Common.RegexValid.IsValidate(this.tbxStockBarcode1.Text.TrimEnd(), MainApp._SYS_PARAMETER["BoxBarcodeValidRegex"]))
            {
                //传入的条码不符合标准
                MainApp._MessageDialog.ShowResult(false, string.Format("输入的条码[{0}]不符合标准", this.tbxStockBarcode1.Text.TrimEnd()));
                return;
            }
            if (string.IsNullOrEmpty(this.tbxGoodsCode.Text.TrimEnd()))
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("物料编码不能为空"));
                return;
            }
            if (string.IsNullOrEmpty(this.tbxExceptionRemark.Text.TrimEnd()))
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("异常描述不能为空"));
                return;
            }

            Model.GOODS_MAIN mGOODS_MAIN = (Model.GOODS_MAIN)MainApp._I_BaseService.GetModel("GOODS_MAIN_SELECT_BY_GOODS_CODE", this.tbxGoodsCode.Text.TrimEnd()).RequestObject;
            if (mGOODS_MAIN == null)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("根据输入的物料编码[{0}]未找到物料信息", this.tbxGoodsCode.Text.TrimEnd()));
                return;
            }

            DataTable dtTargetStorageList = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_LIST left join STORAGE_MAIN on STORAGE_LIST.STORAGE_ID=STORAGE_MAIN.STORAGE_ID where STORAGE_LIST.GOODS_ID = {0} and STORAGE_MAIN.STOCK_BARCODE = '{1}'", mGOODS_MAIN.GOODS_ID, this.tbxStockBarcode1.Text.TrimEnd()));
            if (dtTargetStorageList == null || dtTargetStorageList.Rows.Count < 1)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("根据输入的物料编码[{0}]和箱号[{1}]未找到库存信息", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd()));
                return;
            }

            if (dtTargetStorageList.Rows[0]["GOODS_PROPERTY2"].ToString() == "1")
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("物料编码[{0}]和箱号[{1}]所指定的格子已被标记为异常库存_无法再次标记", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd()));
                return;
            }

            try
            {
                string updateCmd = string.Format("update STORAGE_LIST set GOODS_PROPERTY2 = '1' , STORAGE_LIST_REMARK = '{0}' where STORAGE_LIST_ID = {1}", this.tbxExceptionRemark.Text.TrimEnd(), dtTargetStorageList.Rows[0]["STORAGE_LIST_ID"]);
                int refcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt(updateCmd);

                if (refcount > 0)
                {
                    MainApp._MessageDialog.ShowResult(true, string.Format("标记格子异常库存成功_物料编码[{0}]_箱号[{1}]", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd()));
                    MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Storage, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Information, String.Format("标记格子异常库存成功_物料编码[{0}]_箱号[{1}]", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd()));
                }
                else
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("标记格子异常库存失败_影响行数[{2}]不大于0_物料编码[{0}]_箱号[{1}]", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd(), refcount));
                    MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Storage, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Error, String.Format("标记格子异常库存失败_影响行数[{2}]不大于0_物料编码[{0}]_箱号[{1}]", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd(), refcount));
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("标记格子异常库存失败_物料编码[{0}]_箱号[{1}]_失败信息[{2}]", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd(), ex.Message));
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Storage, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Error, String.Format("标记格子异常库存失败_物料编码[{0}]_箱号[{1}]_失败信息[{2}]", this.tbxGoodsCode.Text.TrimEnd(), this.tbxStockBarcode1.Text.TrimEnd(), ex.Message));
                return;
            }

        }

        /// <summary>
        /// 设置优先出库
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnSetGoodsPriorOut_Click(object sender,RoutedEventArgs e)
        {
            if (!Common.RegexValid.IsValidate(this.tbxStockBarcodeGoodsPriorOut.Text.TrimEnd(), MainApp._SYS_PARAMETER["BoxBarcodeValidRegex"]))
            {
                //传入的条码不符合标准
                MainApp._MessageDialog.ShowResult(false, string.Format("输入的箱条码[{0}]不符合标准", this.tbxStockBarcodeGoodsPriorOut.Text));
                return;
            }

            if(string.IsNullOrEmpty(this.tbxGoodsCodeGoodsPriorOut.Text.TrimEnd()))
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("输入的物料编码不能为空"));
                return;
            }

            bool isPriorOut = this.cbxValueGoodsPriorOut.SelectedValue.ToString() == "优先" ? true : false;

            string outMessage = string.Empty;
            if (MainApp._I_StorageService.SetGoodsPriorOut(MainApp._USER, this.tbxStockBarcodeGoodsPriorOut.Text.TrimEnd(), this.tbxGoodsCodeGoodsPriorOut.Text.TrimEnd(), isPriorOut, out outMessage))
            {
                MainApp._MessageDialog.ShowResult(true, string.Format("条码[{0}]箱中的物料[{1}]库存已被标记为[{2}]", this.tbxStockBarcodeGoodsPriorOut.Text, this.tbxGoodsCodeGoodsPriorOut.Text, this.cbxValueGoodsPriorOut.SelectedValue.ToString()));
            }
            else
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("设置物料优先出库失败_错误信息[{0}]",  outMessage));
            }
        }

        /// <summary>
        /// 连接拣选工作站 不能断线重连前使用的过渡方案
        /// </summary>
        private void  btnConnectPickStation_Click(object sender, RoutedEventArgs e)
        {
            string message = MainApp._I_SystemService.ConnectToPickStation(MainApp._USER);
            MainApp._MessageDialog.ShowResult(message.Contains("success"), message.Replace("success", "成功"));
        }

        /// <summary>
        /// 按排列连续下架 修理货架专用功能
        /// </summary>
        private void btnContinusManageDown_Click(object sender, RoutedEventArgs e)
        {
            if (this.cbxManageDownRow.SelectedIndex == 0)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("请选择要下架物料所在的排"));
                return;
            }

            int startColumn = 0; int endColumn = 0;
            if (!int.TryParse(this.tbxManageDownColumnStart.Text, out startColumn) || !int.TryParse(this.tbxManageDownColumnEnd.Text, out endColumn))
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("起始列[{0}]或者终止列[{1}]输入有误", this.tbxManageDownColumnStart.Text, this.tbxManageDownColumnEnd.Text));
                return;
            }
            if (startColumn < 2 || startColumn > 85)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("起始列[{0}]不在有效范围（2-85）内", startColumn));
                return;
            }
            if (endColumn < 2 || endColumn > 85)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("终止列[{0}]不在有效范围（2-85）内", endColumn));
                return;
            }
            if (startColumn > endColumn)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("起始列[{0}]应小于或等于终止列[{1}]", startColumn, endColumn));
                return;
            }

            string message = MainApp._I_ManageService.ContinusManageDown(MainApp._USER, this.cbxManageDownRow.SelectedIndex, startColumn, endColumn, this.cbxStationName.SelectedValue.ToString().Substring(0, 5), this.cbxIsContainKitandenpty.IsChecked != null && (bool)this.cbxIsContainKitandenpty.IsChecked ? "noKitboxAndEmptyBox" : "all");
            if(message.Contains("success"))
            {
                MainApp._MessageDialog.ShowResult(true, string.Format("成功下达[{0}]个下架任务", message.Split('|')[1]));
            }
            else
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("下达任务失败_[{0}]", message));
            }
        }

        /// <summary>
        /// 执行SQL语句
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnRunSqlCommand_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if(this.tbxAllowCode.Text!= Math.Abs(DateTime.Now.Day-DateTime.Now.Hour).ToString())
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("准许码有误[{0}]_请与开发人员核对", this.tbxAllowCode.Text));
                    return;
                }
                if(string.IsNullOrEmpty(this.tbxSqlCommandText.Text))
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("SQL命令不能为空[{0}]", this.tbxSqlCommandText.Text));
                    return;
                }

                int refCount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt(this.tbxSqlCommandText.Text.TrimEnd());
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("执行SQL语句[{0}]成功_影响行数[{1}]", this.tbxSqlCommandText.Text, refCount));

                MainApp._MessageDialog.ShowResult(refCount>0, string.Format("SQL命令执行完毕_影响行数[{0}]", refCount));
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("SQL命令执行过程中发生异常[{0}]", ex.Message));
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("执行SQL语句[{0}]时发生异常_信息[{1}]", this.tbxSqlCommandText.Text, ex.Message));
            }
        }
        
        /// <summary>
        /// 解锁拣选时标记的异常库存
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnUnlockExceptionStorage_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!Common.RegexValid.IsValidate(this.tbxUnlockBoxBarcode.Text.TrimEnd(), MainApp._SYS_PARAMETER["BoxBarcodeValidRegex"]))
                {
                    //传入的条码不符合标准
                    MainApp._MessageDialog.ShowResult(false, string.Format("输入的箱条码[{0}]不符合标准", this.tbxUnlockBoxBarcode.Text));
                    return;
                }

                if (string.IsNullOrEmpty(this.tbxUnlockGoodsBarcode.Text.TrimEnd()))
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("输入的物料编码不能为空"));
                    return;
                }

                DataTable dtStorageMain = MainApp._I_BaseService.GetList(string.Format("select STORAGE_ID from STORAGE_MAIN where STOCK_BARCODE='{0}'", this.tbxUnlockBoxBarcode.Text.TrimEnd()));
                if (dtStorageMain == null || dtStorageMain.Rows.Count < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("未找到库存信息_箱条码[{0}]", this.tbxUnlockBoxBarcode.Text.TrimEnd()));
                    return;
                }

                DataTable dtGoodsMain = MainApp._I_BaseService.GetList(string.Format("select GOODS_ID from GOODS_MAIN where GOODS_CODE ='{0}'", this.tbxUnlockGoodsBarcode.Text.TrimEnd()));
                if (dtGoodsMain == null || dtGoodsMain.Rows.Count < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("未找到物料信息_物料编码[{0}]", this.tbxUnlockGoodsBarcode.Text.TrimEnd()));
                    return;
                }

                DataTable dtStorageList = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_LIST where GOODS_ID ={0} and STORAGE_ID ={1}", dtGoodsMain.Rows[0]["GOODS_ID"], dtStorageMain.Rows[0]["STORAGE_ID"]));
                if (dtStorageList == null || dtStorageList.Rows.Count != 1)
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("未找到库存单信息或单箱中出现两个格子存放同一种物料_箱条码[{0}]_物料编码[{1}]", this.tbxUnlockBoxBarcode.Text.TrimEnd(), this.tbxUnlockGoodsBarcode.Text.TrimEnd()));
                    return;
                }

                if (dtStorageList.Rows[0]["GOODS_PROPERTY2"].ToString() != "1")
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("库存并未锁定_无需解锁_箱条码[{0}]_物料编码[{1}]", this.tbxUnlockBoxBarcode.Text.TrimEnd(), this.tbxUnlockGoodsBarcode.Text.TrimEnd()));
                    return;
                }

                int refCount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt(string.Format("update STORAGE_LIST set GOODS_PROPERTY2 ='0' where  STORAGE_LIST_ID ={0}", dtStorageList.Rows[0]["STORAGE_LIST_ID"]));
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("解锁拣选标记的异常库存成功_箱条码[{0}]_物料条码[{1}]_影响行数[{2}]", this.tbxUnlockBoxBarcode.Text, this.tbxUnlockGoodsBarcode.Text, refCount));

                MainApp._MessageDialog.ShowResult(refCount > 0, string.Format("解锁拣选标记的异常库存成功_影响行数[{0}]", refCount));
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("解锁拣选标记的异常库存时程序异常[{0}]", ex.Message));
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("解锁拣选标记的异常库存时程序异常_箱条码[{0}]_物料条码[{1}]_异常信息[{2}]", this.tbxUnlockBoxBarcode.Text, this.tbxUnlockGoodsBarcode.Text, ex.Message));
            }
        }

        /// <summary>
        /// 合箱线工作模式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnArrangeSattionWorkMode_Click(object sender, RoutedEventArgs e)
        {
            bool result = true;
            string message = string.Empty;

            try
            {
                if (this.rbtn21098Arrange.IsChecked == true && this.rbtn21098Emerg.IsChecked == false && this.rbtn21098ArrangeGoods.IsChecked == false)
                {
                    //获取当前模式
                    DataTable dtCurrentMode21098 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Arrange' and PARAMETER_KEY = 'WorkingMode-21098'");
                    if (dtCurrentMode21098 != null && dtCurrentMode21098.Rows.Count > 0)
                    {
                        //校验任务和库存
                        DataTable dtManageTo21098 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='21098'");
                        if (dtManageTo21098 != null && dtManageTo21098.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为21098的任务_无法切换模式";
                            return;
                        }
                        DataTable dtStorage21098 = MainApp._I_BaseService.GetList("select * from V_STORAGE_LIST where CELL_CODE ='21098'");
                        if (dtStorage21098 != null && dtStorage21098.Rows.Count > 0)
                        {
                            result = false;
                            message = "整理工位21098存在库存_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Arrange' where PARAMETER_KEY = 'WorkingMode-21098'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新整理工位21098工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "21098模式更新为[合箱模式]");
                        }
                    }
                }
                else if (this.rbtn21098Arrange.IsChecked == false && this.rbtn21098Emerg.IsChecked == true && this.rbtn21098ArrangeGoods.IsChecked == false)
                {
                    //获取当前模式
                    DataTable dtCurrentMode21098 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Emerg' and PARAMETER_KEY = 'WorkingMode-21098'");
                    if (dtCurrentMode21098 != null && dtCurrentMode21098.Rows.Count > 0)
                    {
                        //校验任务和库存
                        DataTable dtManageTo21098 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='21098'");
                        if (dtManageTo21098 != null && dtManageTo21098.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为21098的任务_无法切换模式";
                            return;
                        }
                        DataTable dtStorage21098 = MainApp._I_BaseService.GetList("select * from V_STORAGE_LIST where CELL_CODE ='21098'");
                        if (dtStorage21098 != null && dtStorage21098.Rows.Count > 0)
                        {
                            result = false;
                            message = "整理工位21098存在库存_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Emerg' where PARAMETER_KEY = 'WorkingMode-21098'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新整理工位21098工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "21098模式更新为[紧急出库模式]");
                        }
                    }
                }
                else if (this.rbtn21098Arrange.IsChecked == false && this.rbtn21098Emerg.IsChecked == false && this.rbtn21098ArrangeGoods.IsChecked == true)
                {
                    //获取当前模式
                    DataTable dtCurrentMode21098 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='ArrangeGoods' and PARAMETER_KEY = 'WorkingMode-21098'");
                    if (dtCurrentMode21098 != null && dtCurrentMode21098.Rows.Count > 0)
                    {
                        //校验任务和库存
                        DataTable dtManageTo21098 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='21098'");
                        if (dtManageTo21098 != null && dtManageTo21098.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为21098的任务_无法切换模式";
                            return;
                        }
                        DataTable dtStorage21098 = MainApp._I_BaseService.GetList("select * from V_STORAGE_LIST where CELL_CODE ='21098'");
                        if (dtStorage21098 != null && dtStorage21098.Rows.Count > 0)
                        {
                            result = false;
                            message = "整理工位21098存在库存_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='ArrangeGoods' where PARAMETER_KEY = 'WorkingMode-21098'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新整理工位21098工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "21098模式更新为[整理未满箱模式]");
                        }
                    }
                }
                else
                {
                    result = false;
                    message = "整理工位21098工作模式选择有误";
                    return;
                }

                if (this.rbtn22141Arrange.IsChecked == true && this.rbtn22141Emerg.IsChecked == false && this.rbtn22141ArrangeGoods.IsChecked == false)
                {
                    //获取当前模式
                    DataTable dtCurrentMode22141 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Arrange' and PARAMETER_KEY = 'WorkingMode-22141'");
                    if (dtCurrentMode22141 != null && dtCurrentMode22141.Rows.Count > 0)
                    {
                        DataTable dtManageTo22141 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='22141'");
                        if (dtManageTo22141 != null && dtManageTo22141.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为22141的任务_无法切换模式";
                            return;
                        }
                        DataTable dtStorage22141 = MainApp._I_BaseService.GetList("select * from V_STORAGE_LIST where CELL_CODE ='22141'");
                        if (dtStorage22141 != null && dtStorage22141.Rows.Count > 0)
                        {
                            result = false;
                            message = "整理工位22141存在库存_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Arrange' where PARAMETER_KEY = 'WorkingMode-22141'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新整理工位22141工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "22141模式更新为[合箱模式]");
                        }
                    }
                }
                else if (this.rbtn22141Arrange.IsChecked == false && this.rbtn22141Emerg.IsChecked == true && this.rbtn22141ArrangeGoods.IsChecked == false)
                {
                    //获取当前模式
                    DataTable dtCurrentMode22141 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Emerg' and PARAMETER_KEY = 'WorkingMode-22141'");
                    if (dtCurrentMode22141 != null && dtCurrentMode22141.Rows.Count > 0)
                    {
                        DataTable dtManageTo22141 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='22141'");
                        if (dtManageTo22141 != null && dtManageTo22141.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为22141的任务_无法切换模式";
                            return;
                        }
                        DataTable dtStorage22141 = MainApp._I_BaseService.GetList("select * from V_STORAGE_LIST where CELL_CODE ='22141'");
                        if (dtStorage22141 != null && dtStorage22141.Rows.Count > 0)
                        {
                            result = false;
                            message = "整理工位22141存在库存_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Emerg' where PARAMETER_KEY = 'WorkingMode-22141'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新整理工位22141工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "22141模式更新为[紧急出库模式]");
                        }
                    }
                }
                else if (this.rbtn22141Arrange.IsChecked == false && this.rbtn22141Emerg.IsChecked == false && this.rbtn22141ArrangeGoods.IsChecked == true)
                {
                    //获取当前模式
                    DataTable dtCurrentMode22141 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='ArrangeGoods' and PARAMETER_KEY = 'WorkingMode-22141'");
                    if (dtCurrentMode22141 != null && dtCurrentMode22141.Rows.Count > 0)
                    {
                        DataTable dtManageTo22141 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='22141'");
                        if (dtManageTo22141 != null && dtManageTo22141.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为22141的任务_无法切换模式";
                            return;
                        }
                        DataTable dtStorage22141 = MainApp._I_BaseService.GetList("select * from V_STORAGE_LIST where CELL_CODE ='22141'");
                        if (dtStorage22141 != null && dtStorage22141.Rows.Count > 0)
                        {
                            result = false;
                            message = "整理工位22141存在库存_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='ArrangeGoods' where PARAMETER_KEY = 'WorkingMode-22141'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新整理工位22141工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "22141模式更新为[整理未满箱模式]");
                        }
                    }
                }
                else
                {
                    result = false;
                    message = "整理工位22141工作模式选择有误";
                    return;
                }
            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("更新整理工位状态时异常_信息[{0}]", ex.Message);
            }
            finally
            {
                MainApp._MessageDialog.ShowResult(result, message);
                LoadData();
            }
        }

        /// <summary>
        /// 删除出库完成时没有成功删除的STORAGE_MAIN
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnDeleteStorageMain_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                if (!Common.RegexValid.IsValidate(this.tbxStockBarcodeToDelet.Text.TrimEnd(), MainApp._SYS_PARAMETER["BoxBarcodeValidRegex"]))
                {
                    //传入的条码不符合标准
                    MainApp._MessageDialog.ShowResult(false, string.Format("输入的箱条码[{0}]不符合标准", this.tbxStockBarcodeToDelet.Text));
                    return;
                }

                DataTable dtStorageMain = MainApp._I_BaseService.GetList(string.Format("select STORAGE_ID from STORAGE_MAIN where STOCK_BARCODE='{0}'", this.tbxStockBarcodeToDelet.Text.TrimEnd()));
                if (dtStorageMain == null || dtStorageMain.Rows.Count < 1)
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("未找到库存信息_箱条码[{0}]", this.tbxStockBarcodeToDelet.Text.TrimEnd()));
                    return;
                }

                DataTable dtStorageList = MainApp._I_BaseService.GetList(string.Format("select * from STORAGE_LIST where STORAGE_ID ={0}",  dtStorageMain.Rows[0]["STORAGE_ID"]));
                if (dtStorageList != null && dtStorageList.Rows.Count > 0)
                {
                    MainApp._MessageDialog.ShowResult(false, string.Format("库存的STORAGE_LIST存在_不能执行删除操作_箱条码[{0}]_", this.tbxStockBarcodeToDelet.Text.TrimEnd()));
                    return;
                }

                int refCount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt(string.Format("delete from STORAGE_MAIN  where  STOCK_BARCODE ='{0}'", this.tbxStockBarcodeToDelet.Text.TrimEnd()));
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("人工删除残留的STORAGE_MAIN库存_箱条码[{0}]_影响行数[{1}]", this.tbxStockBarcodeToDelet.Text, refCount));

                MainApp._MessageDialog.ShowResult(refCount > 0, string.Format("人工删除残留的STORAGE_MAIN库存成功_影响行数[{0}]", refCount));
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowResult(false, string.Format("人工删除残留的STORAGE_MAIN库存时程序异常[{0}]", ex.Message));
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("人工删除残留的STORAGE_MAIN库存时程序异常_箱条码[{0}]_异常信息[{1}]", this.tbxStockBarcodeToDelet.Text, ex.Message));
            }
        }
    }
}
