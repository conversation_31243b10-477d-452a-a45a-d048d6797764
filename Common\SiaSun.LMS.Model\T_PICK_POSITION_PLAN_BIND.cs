﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Jacky He
 *       日期：     2017/9/19
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;


    /// <summary>
    /// T_PICK_POSITION_PLAN_BIND 
    /// </summary>
    [Serializable]
    [DataContract]
    public class T_PICK_POSITION_PLAN_BIND
    {
        public T_PICK_POSITION_PLAN_BIND()
        {

        }

        private int _pick_position_plan_bind_id;
        private int _pick_position_id;
        private int _pick_station_id;
        private int _plan_id;
        private int _user_id;
        private int _cell_id;
        private string _bind_time;
        private string _bind_remark;
        private string _bind_property1;
        private string _bind_property2;
        private string _bind_property3;
        private string _flag;
        private string _remark;

        ///<sumary>
        /// ID
        ///</sumary>
        [DataMember]
        public int PICK_POSITION_PLAN_BIND_ID
        {
            get { return _pick_position_plan_bind_id; }
            set { _pick_position_plan_bind_id = value; }
        }
        ///<sumary>
        /// 拣选点ID
        ///</sumary>
        [DataMember]
        public int PICK_POSITION_ID
        {
            get { return _pick_position_id; }
            set { _pick_position_id = value; }
        }
        ///<sumary>
        /// 拣选工作站ID
        ///</sumary>
        [DataMember]
        public int PICK_STATION_ID
        {
            get { return _pick_station_id; }
            set { _pick_station_id = value; }
        }
        ///<sumary>
        /// WBS计划PLAN_MAIN的PLAN_ID
        ///</sumary>
        [DataMember]
        public int PLAN_ID
        {
            get { return _plan_id; }
            set { _plan_id = value; }
        }
        ///<sumary>
        /// 操作用户
        ///</sumary>
        [DataMember]
        public int USER_ID
        {
            get { return _user_id; }
            set { _user_id = value; }
        }
        ///<sumary>
        /// 拣选点对应站台位置
        ///</sumary>
        [DataMember]
        public int CELL_ID
        {
            get { return _cell_id; }
            set { _cell_id = value; }
        }
        ///<sumary>
        /// 绑定时间
        ///</sumary>
        [DataMember]
        public string BIND_TIME
        {
            get { return _bind_time; }
            set { _bind_time = value; }
        }
        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string BIND_REMARK
        {
            get { return _bind_remark; }
            set { _bind_remark = value; }
        }
        ///<sumary>
        /// 2024 改造 
        /// 唯一码
        ///</sumary>
        [DataMember]
        public string BIND_PROPERTY1
        {
            get { return _bind_property1; }
            set { _bind_property1 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BIND_PROPERTY2
        {
            get { return _bind_property2; }
            set { _bind_property2 = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string BIND_PROPERTY3
        {
            get { return _bind_property3; }
            set { _bind_property3 = value; }
        }
        ///<sumary>
        /// 标志位 0 表示未锁定 1表示已锁定
        ///</sumary>
        [DataMember]
        public string FLAG
        {
            get { return _flag; }
            set { _flag = value; }
        }
        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public string REMARK
        {
            get { return _remark; }
            set { _remark = value; }
        }
    }
}
