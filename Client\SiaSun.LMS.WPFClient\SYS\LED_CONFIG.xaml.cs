﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.SYS
{
    public partial class LED_CONFIG : AvalonDock.DocumentContent
    {
        public LED_CONFIG()
        {
            InitializeComponent();

            this.ucQueryLED.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQueryLED_U_Query);

            this.gridLED_MAIN.gridApp.MouseDoubleClick+=new MouseButtonEventHandler(gridApp_MouseDoubleClick);
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                this.InitQueryControl();

                this.LED_MAIN_Bind(string.Empty);

                this.LED_LIST_Bind(0);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void InitQueryControl()
        {
            this.ucQueryLED.U_WindowName = this.GetType().Name;
            this.ucQueryLED.U_XmlTableName = "LED_MAIN";
            this.ucQueryLED.U_InitControl();
        }

        private void LED_MAIN_Bind(string QueryWhere)
        {
            try
            {
                this.gridLED_MAIN.U_WindowName = this.GetType().Name;
                this.gridLED_MAIN.U_TableName = "LED_MAIN";
                this.gridLED_MAIN.U_XmlTableName = "LED_MAIN";
                this.gridLED_MAIN.U_OrderField = "LED_ID";
                this.gridLED_MAIN.U_Where = string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere;
                this.gridLED_MAIN.U_AllowOperatData = true;
                this.gridLED_MAIN.U_AllowAdd = System.Windows.Visibility.Collapsed;
                this.gridLED_MAIN.U_AllowCancel = System.Windows.Visibility.Collapsed;
                this.gridLED_MAIN.U_AllowSave = System.Windows.Visibility.Visible;
                this.gridLED_MAIN.U_AllowEdit = System.Windows.Visibility.Collapsed;
                this.gridLED_MAIN.U_AllowDelete = System.Windows.Visibility.Collapsed;
                this.gridLED_MAIN.U_AllowChecked = true;
                this.gridLED_MAIN.U_AllowPage = false;
                this.gridLED_MAIN.U_InitControl();

                if (this.gridLED_MAIN.U_DataSource != null)
                {
                    //当输入列值改变后验证数据是否合法
                    DataTable tableSourceLED_MAIN = this.gridLED_MAIN.U_DataSource.Table;

                    tableSourceLED_MAIN.ColumnChanged += new DataColumnChangeEventHandler(tableSourceLED_MAIN_ColumnChanged);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        void ucQueryLED_U_Query(string QueryWhere)
        {
            this.LED_MAIN_Bind(QueryWhere);
        }

        void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (gridLED_MAIN.gridApp.SelectedItem == null)
            {
                this.gridLED_MAIN.U_DataSource = null;
                return;
            }

            //获得双击的LED_MAIN
            DataRowView rowViewLed = this.gridLED_MAIN.gridApp.SelectedItem as DataRowView;

            int intLED_ID = (rowViewLed== null||string.IsNullOrEmpty(rowViewLed["LED_ID"].ToString())) ? 0 : Convert.ToInt32(rowViewLed["LED_ID"].ToString());

            this.LED_LIST_Bind(intLED_ID);
        }

        /// <summary>
        /// LED_MAIN表单数据列校验
        /// </summary>
        private void tableSourceLED_MAIN_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;

            string sResult = string.Empty;
            //判断列
            switch (e.Column.ColumnName)
            {
                case "SCREEN_WIDTH":

                case "SCREEN_HEIGHT":

                    if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(e.ProposedValue.ToString()))
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入正整数");
                    }

                    else if (Convert.ToInt32(e.ProposedValue) % 16 != 0)
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入16的倍数");
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }

                    break;

                case "LINE_NUM":

                    if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(e.ProposedValue.ToString()))
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入正整数");
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }

                    break;
            }
            if (!bResult)
            {
                e.Row.RowError = "数据校验失败";
            }
            else
            {
                e.Row.RowError = string.Empty;
            }
        }

        /// <summary>
        /// LED_LIST表单数据列校验
        /// </summary>
        private void tableSourceLED_LIST_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            bool bResult = true;
            string sResult = string.Empty;

            SiaSun.LMS.Model.LED_LIST mLED_LIST = MainApp._I_LEDService.LED_LIST_GetModel(Convert.ToInt32(e.Row["LED_LIST_ID"]));

            //判断列
            switch (e.Column.ColumnName)
            {
                case "LINE_NO":

                    if (null == mLED_LIST)
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "未找到LED_LIST");
                    }
                    else
                    {
                        if (mLED_LIST.LINE_NO != Convert.ToInt32(e.ProposedValue))
                        {
                            bResult = false;

                            e.Row.SetColumnError(e.Column, "行号不可修改");
                        }
                        else
                            e.Row.SetColumnError(e.Column, null);
                    }

                    break;

                case "AREA_X":

                case "AREA_Y":

                case "AREA_WIDTH":

                case "AREA_HEIGHT":

                    if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(e.ProposedValue.ToString()))
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入正整数");
                    }

                    else if (Convert.ToInt32(e.ProposedValue) % 16 != 0)
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入16的倍数");
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }

                    break;

                case "FONT_SIZE":

                    if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(e.ProposedValue.ToString()))
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入正整数");
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }

                    break;

                case "RUN_SPEED":

                    if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(e.ProposedValue.ToString()))
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "请输入正整数");
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }

                    break;

                case "FILE_NAME":

                    if (string.IsNullOrEmpty(e.ProposedValue.ToString()) || e.ProposedValue == null)
                    {
                        bResult = false;

                        e.Row.SetColumnError(e.Column, "不能为空");
                    }
                    else
                    {
                        e.Row.SetColumnError(e.Column, null);
                    }

                    break;
            }
            if (!bResult)
            {
                e.Row.RowError = "数据校验失败";
            }
            else
            {
                e.Row.RowError = string.Empty;
            }
        }

        /// <summary>
        /// LED_LIST列表绑定
        /// </summary>
        private void LED_LIST_Bind(int LED_ID)
        {
            this.gridLED_LIST.U_Clear();

            //设置属性
            this.gridLED_LIST.U_WindowName = this.GetType().Name;
            this.gridLED_LIST.U_TableName = "LED_LIST";
            this.gridLED_LIST.U_XmlTableName = "LED_LIST";
            this.gridLED_LIST.U_OrderField = "LED_LIST_ID";
            this.gridLED_LIST.U_Where = LED_ID == 0 ? string.Empty : string.Format("LED_ID='{0}'",LED_ID);

            this.gridLED_LIST.U_AllowAdd = System.Windows.Visibility.Collapsed;
            this.gridLED_LIST.U_AllowCancel = System.Windows.Visibility.Collapsed;
            this.gridLED_LIST.U_AllowSave = System.Windows.Visibility.Visible;
            this.gridLED_LIST.U_AllowEdit = System.Windows.Visibility.Collapsed;
            this.gridLED_LIST.U_AllowDelete = System.Windows.Visibility.Collapsed;
            this.gridLED_LIST.U_AllowChecked = false;
            this.gridLED_LIST.U_AllowPage = false;

            try
            {
                this.gridLED_LIST.U_InitControl();

                if (this.gridLED_LIST.U_DataSource != null)
                {
                    //当输入列值改变后验证数据是否合法
                    DataTable tableSourceLED_LIST = this.gridLED_LIST.U_DataSource.Table;

                    tableSourceLED_LIST.ColumnChanged += new DataColumnChangeEventHandler(tableSourceLED_LIST_ColumnChanged);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnInit":
                        this.Init();
                        break;
                    case "btnClose":
                        this.CloseServer();
                        break;
                    case "btnOpen":
                        this.OpenServer();
                        break;
                    case "btnSend":
                        this.Send();
                        break;
                    case "btnSave":
                        this.Save();
                        break;

                    case "btnServerSend":
                        this.ServerSave();
                        break;
                }
            }
        }

        //生成LED_LIST
        private void Init()
        {
            bool bResult=true;

            string sResult=string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                if (this.gridLED_MAIN.U_DataSource.Table.Rows.Count != 0)
                {
                    bResult = MainApp._I_LEDService.INIT_LED_LIST(out sResult);

                    if (bResult)
                    {
                        this.LED_LIST_Bind(0);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;

                MainApp._MessageDialog.ShowResult(bResult, sResult);
            }
        }

        //关闭服务
        private void CloseServer()
        {
            bool bResult = true;

            string sResult = string.Empty;

            DataRowView[] listDataRowView = null;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //获得选中记录
                listDataRowView = this.gridLED_MAIN.U_GetCheckedDataRows();

                //校验是否选中记录
                if (listDataRowView.Length == 0)
                {
                    bResult = false;

                    sResult = "请选择记录";

                    return;
                }

                List<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.LED_MAIN>(listDataRowView.ToArray(), null);

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    //关闭服务-0，开启服务-1
                    mLED_MAIN.AUTO_FLAG = "0";

                    MainApp._I_LEDService.LED_MAIN_Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this.gridLED_MAIN.U_Update();
                }

                MainWindow.mainWin.Cursor = Cursors.Arrow;

                MainApp._MessageDialog.ShowResult(bResult, sResult);
            }
        }

        //开启服务
        private void OpenServer()
        {
            bool bResult = true;

            string sResult = string.Empty;

            DataRowView[] listDataRowView = null;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //获得选中记录
                listDataRowView = this.gridLED_MAIN.U_GetCheckedDataRows();

                //校验是否选中记录
                if (listDataRowView.Length == 0)
                {
                    bResult = false;

                    sResult = "请选择记录";

                    return;
                }

                List<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.LED_MAIN>(listDataRowView.ToArray(), null);

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    //关闭服务-0，开启服务-1
                    mLED_MAIN.AUTO_FLAG = "1";

                    MainApp._I_LEDService.LED_MAIN_Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this.gridLED_MAIN.U_Update();
                }

                MainWindow.mainWin.Cursor = Cursors.Arrow;

                MainApp._MessageDialog.ShowResult(bResult, sResult);
            }
        }

        //重新发送
        private void Send()
        {
            bool bResult = true;

            string sResult = string.Empty;

            DataRowView[] listDataRowView = null;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //获得选中记录
                listDataRowView = this.gridLED_MAIN.U_GetCheckedDataRows();

                //校验是否选中记录
                if (listDataRowView.Length == 0)
                {
                    bResult = false;

                    sResult = "请选择记录";

                    return;
                }

                List<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.LED_MAIN>(listDataRowView.ToArray(), null);

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    //1已发送,0未发送
                    mLED_MAIN.LED_STATUS = "0";

                    MainApp._I_LEDService.LED_MAIN_Update(mLED_MAIN);
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this.gridLED_MAIN.U_Update();
                }

                MainWindow.mainWin.Cursor = Cursors.Arrow;

                MainApp._MessageDialog.ShowResult(bResult, sResult);
            }
        }

        /// <summary>
        /// 默认值发送
        /// </summary>
        private void Save()
        {
            bool bResult = true;

            string sResult = string.Empty;

            DataRowView[] listDataRowView = null;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                #region 校验

                #region 校验内容

                if (string.IsNullOrEmpty(this.txtSEND_TEXT.Text.Trim()) || this.txtSEND_TEXT.Text.Trim().Split('|').Length==0)
                {
                    bResult = false;

                    sResult = string.Format("默认发送文本不能为空");

                    return;
                }

                string[] arrSEND_TEXT = txtSEND_TEXT.Text.Trim().Split('|');

                //行数
                int LineCount = arrSEND_TEXT.Length;

                //发送内容
                string SendTxt = this.txtSEND_TEXT.Text.Trim();

                #endregion

                #region 校验选中项

                //获得选中记录
                listDataRowView = this.gridLED_MAIN.U_GetCheckedDataRows();

                //校验是否选中记录
                if (listDataRowView.Length == 0)
                {
                    bResult = false;

                    sResult = "请选择记录";

                    return;
                }

                #endregion

                #endregion

                List<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.LED_MAIN>(listDataRowView.ToArray(), null);

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    bResult=MainApp._I_LEDService.AddLedDefaltTxt(mLED_MAIN, LineCount, SendTxt, out sResult);                   
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this.gridLED_MAIN.U_Update();

                    this.gridLED_LIST.U_Update();
                }

                MainWindow.mainWin.Cursor = Cursors.Arrow;

                MainApp._MessageDialog.ShowResult(bResult, sResult);
            }
        }

        /// <summary>
        /// 服务端发送测试
        /// </summary>
        private void ServerSave()
        {
            bool bResult = true;

            string sResult = string.Empty;

            DataRowView[] listDataRowView = null;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                #region 校验

                #region 校验内容

                if (string.IsNullOrEmpty(this.txtSEND_TEXT.Text.Trim()) || this.txtSEND_TEXT.Text.Trim().Split('|').Length == 0)
                {
                    bResult = false;

                    sResult = string.Format("默认发送文本不能为空");

                    return;
                }

                string[] arrSEND_TEXT = txtSEND_TEXT.Text.Trim().Split('|');

                //行数
                int LineCount = arrSEND_TEXT.Length;

                //发送内容
                string SendTxt = this.txtSEND_TEXT.Text.Trim();

                #endregion

                #region 校验选中项

                //获得选中记录
                listDataRowView = this.gridLED_MAIN.U_GetCheckedDataRows();

                //校验是否选中记录
                if (listDataRowView.Length == 0)
                {
                    bResult = false;

                    sResult = "请选择记录";

                    return;
                }

                #endregion

                #endregion

                List<SiaSun.LMS.Model.LED_MAIN> lsLED_MAIN = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.LED_MAIN>(listDataRowView.ToArray(), null);

                foreach (SiaSun.LMS.Model.LED_MAIN mLED_MAIN in lsLED_MAIN)
                {
                    bResult=MainApp._I_LEDService.AddLedTxt(mLED_MAIN.DEVICE_CODE, LineCount, SendTxt, out sResult);
                }
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this.gridLED_MAIN.U_Update();

                    this.gridLED_LIST.U_Update();
                }

                MainWindow.mainWin.Cursor = Cursors.Arrow;

                MainApp._MessageDialog.ShowResult(bResult, sResult);
            }
        }


    }
}
