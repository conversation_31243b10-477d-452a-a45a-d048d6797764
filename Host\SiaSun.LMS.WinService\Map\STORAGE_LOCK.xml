﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="STORAGE_LOCK" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="STORAGE_LOCK" type="SiaSun.LMS.Model.STORAGE_LOCK, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="STORAGE_LOCK">
      <result property="STORAGE_LOCK_ID" column="storage_lock_id" />
      <result property="STORAGE_LIST_ID" column="storage_list_id" />
      <result property="PLAN_LIST_ID" column="plan_list_id" />
      <result property="STORAGE_LOCK_QUANTITY" column="storage_lock_quantity" />
      <result property="STORAGE_LOCK_FLAG" column="storage_lock_flag" />
      <result property="DETAIL_FLAG" column="detail_flag" />
      <result property="STORAGE_LOCK_REMARK" column="storage_lock_remark" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="STORAGE_LOCK_SELECT" parameterClass="int" resultMap="SelectResult">
      select
      storage_lock_id,
      storage_list_id,
      plan_list_id,
      storage_lock_quantity,
      storage_lock_flag,
      detail_flag,
      storage_lock_remark
      from STORAGE_LOCK
    </select>
    
    <select id="STORAGE_LOCK_SELECT_BY_STORAGE_LOCK_ID" parameterClass="int" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          STORAGE_LOCK_ID=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="STORAGE_LOCK_SELECT_BY_STORAGE_LIST_ID" parameterClass="int" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          STORAGE_LIST_ID=#STORAGE_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_PLAN_LIST_ID" parameterClass="int" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          PLAN_LIST_ID=#PLAN_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_PLAN_LIST_ID_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          PLAN_LIST_ID=#PLAN_LIST_ID# and STORAGE_LOCK_FLAG =#STORAGE_LOCK_FLAG#;
        </isParameterPresent>
      </dynamic>
    </select>
    
    <!--<select id="STORAGE_LOCK_SELECT_BY_STATION_GROUP_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          STORAGE_LIST_ID in ( select STORAGE_LIST_ID from STORAGE_LIST where STORAGE_ID in ( select STORAGE_ID from STORAGE_MAIN where cell_id  in ( select cell_id from wh_cell where cell_group = #STATION_GROUP#))) and STORAGE_LOCK_FLAG =#STORAGE_LOCK_FLAG#;
        </isParameterPresent>
      </dynamic>
    </select>-->
    <select id="STORAGE_LOCK_SELECT_BY_STATION_GROUP_STORAGE_LOCK_FLAG" parameterClass="Hashtable"  resultMap="SelectResult">
      SELECT [STORAGE_LOCK_ID]
      ,[STORAGE_LOCK].[STORAGE_LIST_ID]
      ,[STORAGE_LOCK].[PLAN_LIST_ID]
      ,[STORAGE_LOCK_QUANTITY]
      ,[STORAGE_LOCK_FLAG]
      ,[DETAIL_FLAG]
      ,[STORAGE_LOCK_REMARK]
      FROM [INSPUR].[dbo].[STORAGE_LOCK]
      inner join storage_list on storage_lock.STORAGE_LIST_ID = storage_list.STORAGE_LIST_ID
      inner join STORAGE_MAIN on storage_main.STORAGE_ID = storage_list.STORAGE_ID
      <dynamic prepend="WHERE">
        <isParameterPresent>
           cell_id  in ( select cell_id from wh_cell where cell_group = #STATION_GROUP#) 
           and [STORAGE_LOCK].[PLAN_LIST_ID]  IN (SELECT PLAN_LIST_ID FROM PLAN_LIST WHERE PLAN_ID=#PLAN_ID#)
           and STORAGE_LOCK_FLAG =#STORAGE_LOCK_FLAG# order by storage_main.STORAGE_REMARK;
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_PICK_STATION_ID_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_list_id in (select plan_list_id from plan_list where plan_id in (select plan_id from T_PICK_POSITION_PLAN_BIND where pick_station_id=#PICK_STATION_ID#)) AND storage_lock_flag=#STORAGE_LOCK_FLAG#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_PLAN_GROUP_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_list_id in (select plan_list_id from plan_list where plan_id in 
          (select plan_id from T_PICK_POSITION_PLAN_BIND where plan_id in 
          (
          select plan_id from plan_main where plan_group=#PLAN_GROUP#
          ))) AND storage_lock_flag=#STORAGE_LOCK_FLAG#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="STORAGE_LOCK_SELECT_BY_PICK_POSITION_ID_STORAGE_LOCK_FLAG" parameterClass="Hashtable" extends = "STORAGE_LOCK_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          plan_list_id in (select plan_list_id from plan_list where plan_id in (select plan_id from T_PICK_POSITION_PLAN_BIND where pick_position_id=#PICK_POSITION_ID#)) AND storage_lock_flag=#STORAGE_LOCK_FLAG#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <insert id="STORAGE_LOCK_INSERT" parameterClass="STORAGE_LOCK">
      Insert Into STORAGE_LOCK (
      storage_lock_id,
      storage_list_id,
      plan_list_id,
      storage_lock_quantity,
      storage_lock_flag,
      detail_flag,
      storage_lock_remark
      )Values(
      #STORAGE_LOCK_ID#,
      #STORAGE_LIST_ID#,
      #PLAN_LIST_ID#,
      #STORAGE_LOCK_QUANTITY#,
      #STORAGE_LOCK_FLAG#,
      #DETAIL_FLAG#,
      #STORAGE_LOCK_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="STORAGE_LOCK_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="STORAGE_LOCK_UPDATE" parameterClass="STORAGE_LOCK">
      Update STORAGE_LOCK Set
      <!--storage_lock_id=#STORAGE_LOCK_ID#,-->
      storage_list_id=#STORAGE_LIST_ID#,
      plan_list_id =#PLAN_LIST_ID#,
      storage_lock_quantity=#STORAGE_LOCK_QUANTITY#,
      storage_lock_flag=#STORAGE_LOCK_FLAG#,
      detail_flag =#DETAIL_FLAG#,
      storage_lock_remark =#STORAGE_LOCK_REMARK#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="STORAGE_LOCK_DELETE" parameterClass="int">
      Delete From STORAGE_LOCK
      <dynamic prepend="WHERE">
        <isParameterPresent>
          storage_lock_id=#STORAGE_LOCK_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>