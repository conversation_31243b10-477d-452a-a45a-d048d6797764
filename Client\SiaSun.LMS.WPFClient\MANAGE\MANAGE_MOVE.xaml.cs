﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_MOVE_OUT.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_MOVE : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        List<Model.MANAGE_LIST> listMANAGE_LIST = null;

        /// <summary>
        ///
        /// </summary>
        public MANAGE_MOVE(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

        }

        void ucManagePosition_U_StartPositionChanged()
        {
            this.StorageListBind(string.Format(" CELL_ID = {0}", this.ucManagePosition.U_START_POSITION_ID));
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitManagePosotion();

            this.ucManagePosition.U_StartPositionChanged += new UC.ucManagePosition.U_StartPositionValueChangedHandler(ucManagePosition_U_StartPositionChanged);

        }

        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
        }


        private void StorageListBind(string QueryWhere)
        {
            this.ucStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            //this.ucStorageGroup.U_AppendFieldStyles = this.GetColumnDescriptionList();
            this.ucStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStorageGroup.U_Where = string.Format("AREA_TYPE= 'LiKu' AND {0} ", QueryWhere);
            this.ucStorageGroup.U_AllowOperatData = false;
            this.ucStorageGroup.U_AllowChecked = false;
            this.ucStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStorageGroup.U_InitControl();
        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool boolResult = false;

            string strResult = "\n";

            try
            {
                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                DataTable tableSource = this.ucStorageGroup.U_DataSource;

                foreach (DataRow dr in tableSource.Rows)
                {
                    dr["MANAGE_LIST_QUANTITY"] = dr["STORAGE_LIST_QUANTITY"];
                }
                //获得选中记录
                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableSource, null);
                //校验是否选中记录
                if (listMANAGE_LIST.Count == 0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                    return;
                }

                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.Title) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //调用入库函数配盘入库

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL = this.ucStorageGroup.U_DataSource.Rows[0]["CELL_MODEL"].ToString();
                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = this.ucStorageGroup.U_DataSource.Rows[0]["STOCK_BARCODE"].ToString();

                    boolResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                             "ManageCreate",
                                                              new object[] { mMANAGE_MAIN,
                                                                              listMANAGE_LIST, 
                                                                               true, 
                                                                               this.ucManagePosition.U_CheckStockExistStorage,
                                                                               this.ucManagePosition.U_AutoCompleteTask,
                                                                               this.ucManagePosition.U_AutoDownloadControlTask, 
                                                                               },
                                                               out strResult);

                    //检验执行结果
                    if (boolResult)
                    {
                        this.Refresh();
                    }

                    MainApp._MessageDialog.ShowResult(boolResult, strResult);
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 获得托盘集合列表
        /// </summary>
        private IDictionary<string, Model.WH_CELL> GetPalletKeyValuePair(List<DataRowView> listDataRowView)
        {
            IDictionary<string, Model.WH_CELL> dicStack = new Dictionary<string, Model.WH_CELL>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack = rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (mWH_CELL != null && !dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, mWH_CELL);
                    }
                }
            }
            return dicStack;
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            //刷新
            this.ucManagePosition.U_Refresh();

            //wdz alter 2018-01-22
            //this.ucStorageGroup.U_InitControl();
            this.ucStorageGroup.U_Clear();
        }


    }
}
