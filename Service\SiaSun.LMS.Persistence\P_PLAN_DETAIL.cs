﻿/***************************************************************************
 * 
 *       功能：     计划列表明细持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// PLAN_DETAIL
	/// </summary>
	public class P_PLAN_DETAIL : P_Base_House
	{
		public P_PLAN_DETAIL ()
		{
			//
			// TODO: 此处添加PLAN_DETAIL的构造函数
			//
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<PLAN_DETAIL> GetList()
		{
			return ExecuteQueryForList<PLAN_DETAIL>("PLAN_DETAIL_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<PLAN_DETAIL> GetList(int PLAN_LIST_ID)
        {
            return ExecuteQueryForList<PLAN_DETAIL>("PLAN_DETAIL_SELECT_BY_PLAN_LIST_ID", PLAN_LIST_ID);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(PLAN_DETAIL plan_detail)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("PLAN_DETAIL");
                plan_detail.PLAN_DETAIL_ID = id;
            }

            return ExecuteInsert("PLAN_DETAIL_INSERT",plan_detail);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(PLAN_DETAIL plan_detail)
		{
			return ExecuteUpdate("PLAN_DETAIL_UPDATE",plan_detail);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public PLAN_DETAIL GetModel(System.Int32 PLAN_DETAIL_ID)
		{
			return ExecuteQueryForObject<PLAN_DETAIL>("PLAN_DETAIL_SELECT_BY_ID",PLAN_DETAIL_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 PLAN_DETAIL_ID)
		{
			return ExecuteDelete("PLAN_DETAIL_DELETE",PLAN_DETAIL_ID);
		}

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int DeletePlanID(int PLAN_ID)
        {
            return ExecuteDelete("PLAN_DETAIL_DELETE_BY_PLAN_ID", PLAN_ID);
        }

	}
}
