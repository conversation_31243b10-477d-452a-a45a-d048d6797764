﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Runtime.Serialization;
using System.ServiceModel;
using System.Text;

namespace SignalServer.Services
{
    // 注意: 使用“重构”菜单上的“重命名”命令，可以同时更改代码和配置文件中的接口名“ISignalServerSrv”。
    [ServiceContract]
    public interface ISignalServerSrv
    {
        /// <summary>
        /// 获得所有已登录的拣选工作站列表
        /// </summary>
        [OperationContract]
        bool ConnnectNdLoginPickStations(string group,out List<string> PickStationCodes,out string sResult);

        /// <summary>
        /// 通知拣选工作站绑定新订单
        /// </summary>
        /// <param name="PickStationCode">拣选工作站编码</param>
        /// <param name="PlanGroupToBind">新订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        [OperationContract]
        bool InformPickStationBindNewOrder(string PickStationCode, string PlanGroupToBind, out string sResult);


        /// <summary>
        /// 通知拣选工作站绑定新订单
        /// </summary>
        /// <param name="informDict">key:拣选工作站编码；value:订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        [OperationContract]
        bool InformPickStationBindNewOrderMultiple(Dictionary<string,string> informDict, out string sResult);
    }
}
