﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="MANAGE_DETAIL" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="MANAGE_DETAIL" type="SiaSun.LMS.Model.MANAGE_DETAIL, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="MANAGE_DETAIL">
			<result property="MANAGE_DETAIL_ID" column="manage_detail_id" />
			<result property="MANAGE_LIST_ID" column="manage_list_id" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_BARCODE" column="goods_barcode" />
			<result property="MANAGE_DETAIL_REMARK" column="manage_detail_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	  <select id="MANAGE_DETAIL_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  manage_detail_id,
				  manage_list_id,
				  box_barcode,
				  goods_barcode,
				  manage_detail_remark
			From MANAGE_DETAIL
		</select>
		
		<select id="MANAGE_DETAIL_SELECT_BY_ID" parameterClass="int" extends = "MANAGE_DETAIL_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_detail_id=#MANAGE_DETAIL_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="MANAGE_DETAIL_SELECT_BY_MANAGE_LIST_ID" parameterClass="int" extends = "MANAGE_DETAIL_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_list_id=#MANAGE_LIST_ID#
        </isParameterPresent>
      </dynamic>
    </select>
				
		<insert id="MANAGE_DETAIL_INSERT" parameterClass="MANAGE_DETAIL">
      Insert Into MANAGE_DETAIL (
      manage_detail_id,
      manage_list_id,
      box_barcode,
      goods_barcode,
      manage_detail_remark
      )Values(
      #MANAGE_DETAIL_ID#,
      #MANAGE_LIST_ID#,
      #BOX_BARCODE#,
      #GOODS_BARCODE#,
      #MANAGE_DETAIL_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="MANAGE_DETAIL_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="MANAGE_DETAIL_UPDATE" parameterClass="MANAGE_DETAIL">
      Update MANAGE_DETAIL Set
      manage_detail_id=#MANAGE_DETAIL_ID#,
      manage_list_id=#MANAGE_LIST_ID#,
      box_barcode=#BOX_BARCODE#,
      goods_barcode=#GOODS_BARCODE#,
      manage_detail_remark=#MANAGE_DETAIL_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					manage_detail_id=#MANAGE_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="MANAGE_DETAIL_DELETE" parameterClass="int">
			Delete From MANAGE_DETAIL
			<dynamic prepend="WHERE">
				<isParameterPresent>
					manage_detail_id=#MANAGE_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

    <delete id="MANAGE_DETAIL_DELETE_MANAGE_ID" parameterClass="int">
      Delete From MANAGE_DETAIL
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_list_id in ( select manage_list_id from manage_main where manage_id = #MANAGE_ID#) 
        </isParameterPresent>
      </dynamic>
    </delete>
		
	</statements>
</sqlMap>