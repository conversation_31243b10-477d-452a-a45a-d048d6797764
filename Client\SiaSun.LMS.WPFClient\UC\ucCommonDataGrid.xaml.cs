﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

using System.Data;
using System.Reflection;
using System.ComponentModel;
using System.Xml;

using System.Xml.Linq;
using System.Windows.Markup;
using System.IO;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucCommonDataGrid.xaml 的交互逻辑
    /// </summary>
    public partial class ucCommonDataGrid : UserControl
    {
        public string strCheckedColumnName = "选择";
        private DataTable tableSplit = null;


        #region     ------定义变量属性

        private string strSourceSql = string.Empty;
        private string strTableName = string.Empty;
        private string strXmlTableName = string.Empty;
        private string strField = "*";
        private string strOrderField = string.Empty;
        private string strWhere = string.Empty;
        private string strAppendFilter = string.Empty;          //人工自定义查询的条件
        private string strGroupBy = string.Empty;
        private Dictionary<string, string> dicDefaultValue = new Dictionary<string, string>();
        private string _windowName = null;
        private string strDataAccess = "HouseMap";
        //xcjt add 2017-01-03
        //视图中指定保存的数据表
        private string strSaveDataTable = string.Empty;
        //tzyg add 2017-03-16
        //控制是否显示物料位置模板图片窗体
        private bool isShowGoodsPositionTemplate = false;

        /// <summary>
        /// 关联窗体,若果不为空，则加载显示WindowStyles.xml中的样式，否则加载FieldDescription.xml中的样式
        /// </summary>
        public string U_WindowName
        {
            get { return this._windowName; }
            set { this._windowName = value; }
        }

        /// <summary>
        /// 强制设置数据源的查询语句，如果设置该值则说明是一个复杂的多表查询，则strField,strOrderField,strWhere等条件不起作用
        /// </summary>
        public string U_DataSource_SQL
        {
            get { return strSourceSql; }
            set { strSourceSql = value; }
        }

        /// <summary>
        /// 数据表名称，用于获取数据库中数据源
        /// </summary>
        public string U_TableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// XML文件中表名，用于设置显示样式
        /// </summary>
        public string U_XmlTableName
        {
            get { return strXmlTableName; }
            set { strXmlTableName = value; }
        }

        /// <summary>
        /// 查询字段
        /// </summary>
        public string U_Fields
        {
            get { return strField; }
            set { strField = value; }
        }

        /// <summary>
        /// 排序字段
        /// </summary>
        public string U_OrderField
        {
            get { return strOrderField; }
            set { strOrderField = value; }
        }

        /// <summary>
        /// 查询条件
        /// </summary>
        public string U_Where
        {
            get { return strWhere; }
            set { strWhere = value; }
        }

        /// <summary>
        /// 追加查询条件
        /// </summary>
        public string U_AppendWhere
        {
            get { return strAppendFilter; }
            set { strAppendFilter = value; }
        }

        /// <summary>
        /// 分组列
        /// </summary>
        public string U_GroupBy
        {
            get { return strGroupBy; }
            set { strGroupBy = value; }
        }

        /// <summary>
        /// 数据源
        /// </summary>
        public DataView U_DataSource
        {
            get { return (this.gridApp.ItemsSource as DataView); }
            set
            {
                this.gridApp.ItemsSource = value;
            }
        }

        /// <summary>
        /// 默认列值集合
        /// </summary>
        public Dictionary<string, string> U_DefaultRowValues
        {
            get { return dicDefaultValue; }
            set { dicDefaultValue = value; }
        }

        /// <summary>
        /// 选定的项目
        /// </summary>
        public object U_SelectedItem
        {
            get { return this.gridApp.SelectedItem; }
        }

        public string U_DataAccess
        {
            get { return strDataAccess; }
            set { strDataAccess = value; }
        }

        /// <summary>
        /// xcjt add 2017-01-03
        /// 视图中要保存的数据表 
        /// </summary>
        public string U_SaveDataTable
        {
            get { return strSaveDataTable; }
            set { strSaveDataTable = value; }
        }

        /// <summary>
        /// tzyg add 2017-03-16
        /// 控制是否显示物料位置模板图片窗体
        /// </summary>
        public bool U_IsShowGoodsPositionTemplate
        {
            get { return isShowGoodsPositionTemplate; }
            set { isShowGoodsPositionTemplate = value; }
        }
        #endregion

        #region     ------拆分组合列属性

        private string strSplitPropertyType = string.Empty;
        private string strSplitPropertyKey = string.Empty;
        private string strSplitColumnName = string.Empty;
        private bool bSplitColumnCombine = false;

        /// <summary>
        /// 拆分列分组类型,如：GOODS_TYPE
        /// </summary>
        public string U_SplitPropertyType
        {
            get { return strSplitPropertyType; }
            set { strSplitPropertyType = value; }
        }

        /// <summary>
        /// 拆分列分组标识的值,如：GOODS_TYPE_ID的值
        /// </summary>
        public string U_SplitPropertyKey
        {
            get { return strSplitPropertyKey; }
            set { strSplitPropertyKey = value; }
        }

        /// <summary>
        /// 数据源中Tab页显示分组依据列名,如：GOODS_PROPERTY
        /// </summary>
        public string U_SplitPropertyColumn
        {
            get { return strSplitColumnName; }
            set { strSplitColumnName = value; }
        }


        /// <summary>
        /// 拆分的属性是否最终组合保存到一个字段中
        /// </summary>
        public bool U_SplitColumnCombine
        {
            get { return bSplitColumnCombine; }
            set { bSplitColumnCombine = value; }
        }

        #endregion

        #region     ------明细属性

        string strDetailTableName;
        string strDetailColumnName;

        /// <summary>
        /// 明细表名
        /// </summary>
        public string U_DetailTableName
        {
            get { return this.strDetailTableName; }
            set { this.strDetailTableName = value; }
        }

        /// <summary>
        /// 明细表列名
        /// </summary>
        public string U_DetailRelatvieColumn
        {
            get { return this.strDetailColumnName; }
            set { this.strDetailColumnName = value; }
        }

        #endregion

        #region     ------外观属性

        private bool boolAllowCheck = true;
        private bool boolAllowPage = true;
        private ContextMenu menuStrip;
        private List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listAppendDescription = null;

        /// <summary>
        /// 是否显示全选、反选框
        /// </summary>
        public bool U_AllowChecked
        {
            get { return boolAllowCheck; }
            set
            {
                boolAllowCheck = value;
                this.chkBoxCheckAll.Visibility = value ? Visibility.Visible : System.Windows.Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 是否允许分页显示
        /// </summary>
        public bool U_AllowPage
        {
            get { return boolAllowPage; }
            set
            {
                boolAllowPage = value;
                this.tbarPage.Visibility = value ? Visibility.Visible : System.Windows.Visibility.Collapsed;
            }
        }

        /// <summary>
        /// 右键显示菜单
        /// </summary>
        public ContextMenu U_MenuStrip
        {
            get { return menuStrip; }
            set { menuStrip = value; }
        }

        /// <summary>
        /// 追加的显示的描述列
        /// </summary>
        public List<SiaSun.LMS.Model.FIELD_DESCRIPTION> U_AppendFieldStyles
        {
            get { return listAppendDescription; }
            set { listAppendDescription = value; }
        }

        /// <summary>
        /// 是否允许编辑数据，如果允许编辑则显示编辑工具栏
        /// </summary>
        public bool U_AllowOperatData
        {
            get { return (tbarButton.Visibility == System.Windows.Visibility.Visible); }
            set
            {
                tbarButton.Visibility = value ? Visibility.Visible : Visibility.Collapsed;
                this.btnAdd.Visibility = value ? Visibility.Visible : Visibility.Hidden;
                this.btnEdit.Visibility = value ? Visibility.Visible : Visibility.Hidden;
                this.gridApp.CanUserAddRows = this.gridApp.CanUserDeleteRows = value;
            }
        }

        /// <summary>
        /// 是否允许添加操作
        /// </summary>
        public Visibility U_AllowAdd
        {
            get { return this.btnAdd.Visibility; }
            set { this.btnAdd.Visibility = value; this.gridApp.CanUserAddRows = (value == System.Windows.Visibility.Visible); }
        }

        /// <summary>
        /// 是否显示编辑按钮
        /// </summary>
        public Visibility U_AllowEdit
        {
            get { return this.btnEdit.Visibility; }
            set { this.btnEdit.Visibility = value; }
        }

        /// <summary>
        /// 是否显示删除按钮
        /// </summary>
        public Visibility U_AllowDelete
        {
            get { return this.btnDelete.Visibility; }
            set { this.btnDelete.Visibility = value; this.gridApp.CanUserDeleteRows = (value == System.Windows.Visibility.Visible); }
        }

        /// <summary>
        /// 是否显示取消按钮
        /// </summary>
        public Visibility U_AllowCancel
        {
            get { return this.btnCancel.Visibility; }
            set { this.btnCancel.Visibility = value; }
        }

        /// <summary>
        /// 是否显示保存按钮
        /// </summary>
        public Visibility U_AllowSave
        {
            get { return this.btnSave.Visibility; }
            set { this.btnSave.Visibility = value; }
        }


        #endregion

        #region     ------分页属性

        private int intRowCountPerPage = 100;
        private int intCurPageIndex = 1;
        private int intPageSum = 0;
        private int intRowSum = 0;

        #endregion

        #region     ------合计属性

        private string strTotalColumnName = string.Empty;

        /// <summary>
        /// 合计总数列名
        /// </summary>
        public string U_TotalColumnName
        {
            get { return strTotalColumnName; }
            set
            {
                strTotalColumnName = value;
                this.txtTotal.Visibility = string.IsNullOrEmpty(strTotalColumnName) ? System.Windows.Visibility.Collapsed : Visibility.Visible;
            }
        }

        #endregion


        //xcjt add 2017-01-03
        //增加保存按钮的自定义事件，以便在用户保存时保留日志
        public delegate void SavedEventHandler(object sender, SaveEventArgs e);
        public event SavedEventHandler SaveClicked;
        public class SaveEventArgs:RoutedEventArgs
        {
            private readonly int affectCount;

            public int AffectCount
            {
                get { return affectCount; }
            } 

            public SaveEventArgs(int parmAffectCount)
            {
                this.affectCount = parmAffectCount;
            }
        }
        protected virtual void OnSaved(SaveEventArgs e)
        {
            if (SaveClicked != null)
            { 
                SaveClicked(this, e);  
            }
        }



        /// <summary>
        /// 构造函数
        /// </summary>
        public ucCommonDataGrid()
        {
            InitializeComponent();
        }

        #region     ------获得查询语句

        /// <summary>
        /// 获得数据源查询语句
        /// </summary>
        private string GetSql()
        {
            string strSql = string.Empty;
            string strSubSql = string.Empty;

            //判断是否设置复杂查询语句
            if (strSourceSql.Length > 0)
            {
                strSubSql = string.Format("(SELECT T.*,ROW_NUMBER() OVER(ORDER BY {2}) AS ROWNUMBER FROM ({0} WHERE 1=1 {1} {3}) T)",
                                        strSourceSql,
                                        (strWhere.TrimEnd().Length == 0 ? string.Empty : "AND " + strWhere),
                                         (string.IsNullOrEmpty(strOrderField) ? "1" : strOrderField),
                                        (strAppendFilter.TrimEnd().Length == 0 ? string.Empty : "AND " + strAppendFilter));
                //检查是否分页
                if (boolAllowPage)
                {
                    strSql = string.Format("SELECT * FROM {0} A WHERE ROWNUMBER BETWEEN {1} AND {2}",
                                            strSubSql,
                                            intRowCountPerPage * (intCurPageIndex - 1) + 1,
                                            intRowCountPerPage * intCurPageIndex);
                }
                else
                {
                    strSql = string.Format("SELECT * FROM {0} A", strSubSql);
                }
            }
            else
            {
                if (strTableName.Length > 0)
                {
                    strSubSql = string.Format("(SELECT {1},ROW_NUMBER() OVER(ORDER BY {0}) AS ROWNUMBER FROM {2} WHERE 1=1 {3} {4} {5}) T",
                                            (string.IsNullOrEmpty(strOrderField) ? "1" : strOrderField),
                                            strField.Replace("*", string.Format("{0}.*", strTableName)),
                                            strTableName,
                                            (strWhere.TrimEnd().Length == 0 ? string.Empty : "AND " + strWhere),
                                            (strAppendFilter.TrimEnd().Length == 0 ? string.Empty : "AND " + strAppendFilter),
                                            (strGroupBy.TrimEnd().Length == 0 ? string.Empty : " GROUP BY " + strGroupBy));
                    //检查是否分页
                    if (boolAllowPage)
                    {
                        strSql = string.Format("SELECT * FROM {0} WHERE ROWNUMBER BETWEEN {1} AND {2}",
                                                strSubSql,
                                                intRowCountPerPage * (intCurPageIndex - 1) + 1,
                                                intRowCountPerPage * intCurPageIndex);
                    }
                    else
                    {
                        strSql = string.Format("SELECT * FROM {0}", strSubSql);
                    }
                }
            }
            //return strSql;
            return MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? strSql.Replace("ISNULL", "NVL") : strSql;
        }

        /// <summary>
        /// 获得分页合计总数语句
        /// </summary>
        private string GetPageTotalSql()
        {
            string strSql = string.Empty;
            string strSubSql = string.Empty;
            if (strTotalColumnName.Length > 0)
            {
                //判断是否设置复杂查询语句
                if (strSourceSql.Length > 0)
                {
                    strSubSql = string.Format("(SELECT T.*,ROW_NUMBER() OVER(ORDER BY {2}) AS ROWNUMBER FROM ({0} WHERE 1=1 {1} {3}) T)",
                                                strSourceSql,
                                                (strWhere.Length == 0 ? string.Empty : "AND " + strWhere),
                                                (string.IsNullOrEmpty(strOrderField) ? "1" : strOrderField),
                                                (strAppendFilter.TrimEnd().Length == 0 ? string.Empty : "AND " + strAppendFilter));
                    //检查是否分页
                    if (boolAllowPage)
                    {
                        strSql = string.Format("SELECT SUM({3}) FROM {0} WHERE ROWNUMBER BETWEEN {1} AND {2}",
                                                    strSubSql,
                                                    intRowCountPerPage * (intCurPageIndex - 1) + 1,
                                                    intRowCountPerPage * intCurPageIndex,
                                                    strTotalColumnName);
                    }
                    else
                    {
                        strSql = string.Format("SELECT SUM({1}) FROM {0}", strSubSql, strTotalColumnName);
                    }
                }
                else
                {
                    strSubSql = string.Format("(SELECT ROW_NUMBER() OVER(ORDER BY {0}) AS ROWNUMBER, {1} FROM {2} WHERE 1=1 {3} {4}) T",
                                                (string.IsNullOrEmpty(strOrderField) ? "1" : strOrderField),
                                                strTotalColumnName,
                                                strTableName,
                                                (strWhere.Length == 0 ? string.Empty : "AND " + strWhere),
                                                (strAppendFilter.TrimEnd().Length == 0 ? string.Empty : "AND " + strAppendFilter));
                    //检查是否分页
                    if (boolAllowPage)
                    {
                        strSql = string.Format("SELECT SUM({0}) FROM {1} WHERE ROWNUMBER BETWEEN {2} AND {3}",
                                                    strTotalColumnName,
                                                    strSubSql,
                                                    intRowCountPerPage * (intCurPageIndex - 1) + 1,
                                                    intRowCountPerPage * intCurPageIndex);
                    }
                    else
                    {
                        strSql = string.Format("SELECT SUM({0}) FROM {1}", strTotalColumnName, strSubSql);
                    }

                }
            }
            //return strSql;
            return MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? strSql.Replace("ISNULL", "NVL") : strSql;
        }

        /// <summary>
        /// 获得合计总数语句
        /// </summary>
        private string GetTotalSql()
        {
            string strSql = string.Empty;
            if (strTotalColumnName.Length > 0)
            {
                //判断是否设置复杂查询语句
                if (strSourceSql.Length > 0)
                {
                    strSql = string.Format("SELECT SUM({1}) FROM ({0} WHERE 1=1 {2} {3}) T",
                                            strSourceSql,
                                            strTotalColumnName,
                                            (strWhere.TrimEnd().Length == 0 ? string.Empty : "AND " + strWhere),
                                            (strAppendFilter.TrimEnd().Length == 0 ? string.Empty : "AND " + strAppendFilter));
                }
                else
                {
                    strSql = string.Format("SELECT SUM({0}) FROM {1} WHERE {2} {3}",
                                            strTotalColumnName,
                                            strTableName,
                                            (strWhere.TrimEnd().Length == 0 ? "1=1" : strWhere),
                                            (strAppendFilter.TrimEnd().Length == 0 ? string.Empty : "AND " + strAppendFilter));
                }
            }
            //return strSql;
            return MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? strSql.Replace("ISNULL", "NVL") : strSql;
        }

        #endregion

        /// <summary>
        /// 加载数据
        /// </summary>
        public void U_InitControl()
        {
            //右键菜单
            InitMenuStrip();

            try
            {
                //获得排序列
                if (string.IsNullOrEmpty(strSourceSql)&& string.IsNullOrEmpty(strOrderField))
                {
                    strOrderField = this.U_GetDefaultOrderColumn(strXmlTableName);
                }

                //判断是否允许分页显示
                if (boolAllowPage)
                {
                    //加载第一页数据
                    this.intCurPageIndex = 1;
                    //加载数据源
                    this.LoadPageData();
                }
                else
                {
                    //加载数据源
                    LoadData();
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadData()
        {
            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;
                //获得选中索引
                int index = this.gridApp.SelectedIndex;

                //设置XML表名
                strXmlTableName = strXmlTableName.Length == 0 ? strTableName : strXmlTableName;

                //获得查询语句
                string strSql = GetSql();
                //获得数据源
                using (DataSet dsSource = new DataSet())
                {
                    DataTable tableSource = MainApp._I_BaseService.GetList(strSql,U_DataAccess);
                    dsSource.Tables.Add(tableSource);
                    
                    //判断是否样式已经加载
                    if (this.gridApp.Columns.Count == 0)
                    {
                        //加载样式
                        this.U_TranslateDataGridViewStyle(string.IsNullOrEmpty(_windowName) ? string.Empty : _windowName, strXmlTableName, listAppendDescription, (this.tbarButton.Visibility == System.Windows.Visibility.Visible));
                        //加载拆分列样式
                        this.TranslateSplitColumnStyles(tableSource);
                        //添加"选中"列
                        if (this.boolAllowCheck)
                        {
                            this.U_AddCheckBoxDataGridColumn();
                        }
                    }

                    //设置数据源
                    this.gridApp.ItemsSource = tableSource.DefaultView;
                    //默认选择第一行
                    if (this.gridApp.HasItems)
                    {
                        //重新选中索引
                        if (this.gridApp.Items.Count > index)
                        {
                            this.gridApp.SelectedIndex = index < 0 ? 0 : index;
                        }
                    }
                    
                    //解析各个属性值
                    this.U_TanslateDataTable(tableSource, false);

                    //如果是可编辑状态则设置默认值
                    if (this.btnAdd.Visibility == System.Windows.Visibility.Visible || this.btnEdit.Visibility == System.Windows.Visibility.Visible)
                    {
                        //读取XML设置默认值
                        this.U_SetDataTableConstraints(strXmlTableName);

                        //从默认值列表中获得默认值
                        //设置默认值
                        foreach (KeyValuePair<string, string> pair in dicDefaultValue)
                        {
                            if (tableSource.Columns.Contains(pair.Key))
                            {
                                tableSource.Columns[pair.Key].DefaultValue = pair.Value;
                            }
                        }
                    }

                    //显示总行数
                    intRowSum = this.GetRowSumCount();

                    //获得总页数
                    intPageSum = this.GetPageSumCount();

                    //计算合计数
                    if (!string.IsNullOrEmpty(strTotalColumnName))
                    {
                        ComputTotal();
                    }

                    
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        #region      -------拆分属性列

        /// <summary>
        /// 加载拆分列
        /// </summary>
        private void TranslateSplitColumnStyles(DataTable tableSource)
        {
            //this.gridApp.U_SplitPropertyType = strSplitPropertyType;
            //this.gridApp.U_SplitPropertyKey = strSplitPropertyKey;
            //this.gridApp.U_SplitPropertyColumn = strSplitColumnName;
            //this.gridApp.U_AllowOperatData = this.U_AllowOperatData;

            //拆分属性列
            this.U_TranslateSplitColumnStyles(tableSource);
        }

        /// <summary>
        /// 解析数据源,解析组合属性，设置各个属性值
        /// </summary>
        public void U_TanslateDataTable(DataTable tableSource, bool HasChanges)
        {
            this.U_TanslateSplitDataTable(tableSource, HasChanges);
        }

        /// <summary>
        /// 校验拆分属性填写是否合法
        /// </summary>
        public bool U_Check_Split_Property(string ColumnName, out string sResult)
        {
            sResult = string.Empty;
            return this.U_CheckSplitProperty(ColumnName, out sResult);
        }

        #endregion

        /// <summary>
        /// 清除并初始化控件变量和属性
        /// </summary>
        public void U_Clear()
        {
            //参数初始化
            strTableName = string.Empty;
            strField = "*";
            strOrderField = string.Empty;
            strWhere = string.Empty;
            strAppendFilter = string.Empty;
            dicDefaultValue.Clear();

            if (listAppendDescription != null)
            {
                listAppendDescription.Clear();
            }

            strSplitPropertyType = string.Empty;
            strSplitPropertyKey = string.Empty;
            strSplitColumnName = string.Empty;

            strTotalColumnName = string.Empty;

            //清除数据和显示样式
            using (DataView dataViewSource = this.gridApp.ItemsSource as DataView)
            {
                if (dataViewSource != null)
                {
                    (dataViewSource.Table).Constraints.Clear();
                    (dataViewSource.Table).Rows.Clear();
                    (dataViewSource.Table).Columns.Clear();
                    this.gridApp.Columns.Clear();
                    this.gridApp.ItemsSource = null;
                }
            }
        }


        /// <summary>
        /// 刷新重新加载数据
        /// </summary>
        public void U_Update()
        {

            //获得选中索引
            int index = this.gridApp.SelectedIndex;

            //设置追加的查询条件为空
            strAppendFilter = string.Empty;

            //判断是否分页显示
            if (this.boolAllowPage)
            {
                //加载分页数据
                this.LoadPageData();
            }
            else
            {
                //更新数据源
                LoadData();
            }
            //重新选中索引
            if (this.gridApp.Items.Count > index)
            {
                this.gridApp.SelectedIndex = index;
            }
        }

        /// <summary>
        /// 结束编辑操作
        /// </summary>
        public void U_EndCurrentEdit()
        {
            //重新加载
            using (DataTable tableSource = this.U_DataSource.Table)
            {
                //启用
                tableSource.DataSet.EnforceConstraints = true;

                //结束编辑
                this.gridApp.CommitEdit(DataGridEditingUnit.Row, true);

                //组合属性
                
                if( this.U_SplitColumnCombine)
                this.U_CombinSplitPropertyTable();
            }
        }

        /// <summary>
        /// 全选或反选
        /// </summary>
        private void chkBoxCheckAll_Checked(object sender, RoutedEventArgs e)
        {
            this.U_CheckedAllRows((this.chkBoxCheckAll.IsChecked == true));
        }

        #region     ------分页

        /// <summary>
        /// 获得总行数
        /// </summary>
        private int GetRowSumCount()
        {
            DataTable objSum = null;
            if (strSourceSql.Length > 0)
            {
                string strSql = string.Format("SELECT COUNT(0) FROM ({0} WHERE 1=1 {1}) A", strSourceSql, (strWhere.Length == 0 ? string.Empty : "AND " + strWhere));
                objSum = MainApp._I_BaseService.GetList(MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? strSql.Replace("ISNULL", "NVL") : strSql, U_DataAccess);
            }
            else
            {
                string strSql = string.Format("SELECT COUNT(0) FROM {0} WHERE 1=1 AND {1}", strTableName, string.Format("{0} AND {1}", string.IsNullOrEmpty(strWhere) ? "1=1" : strWhere, string.IsNullOrEmpty(strAppendFilter) ? "1=1" : strAppendFilter));

                objSum = MainApp._I_BaseService.GetList(MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? strSql.Replace("ISNULL", "NVL") : strSql, U_DataAccess);
            }
            return (objSum.Rows.Count==0 ? 0 : Convert.ToInt32(objSum.Rows[0][0]));
        }

        /// <summary>
        /// 获得总页数
        /// </summary>
        private int GetPageSumCount()
        {
            return (intRowSum / intRowCountPerPage + ((0 == intRowSum % intRowCountPerPage) ? 0 : 1));
        }

        /// <summary>
        /// 显示分页信息
        /// </summary>
        private void ShowPage()
        {
            //总行数
            this.txtLineNote.Text = string.Format(this.txtLineNote.Tag.ToString(), this.intRowSum.ToString());
            //总页数
            this.txtPageNote.Text = string.Format(this.txtPageNote.Tag.ToString(), this.intPageSum.ToString());
            //每页行数
            this.txtChangeRowPrePage.Text = this.intRowCountPerPage.ToString();
            //当前页
            this.txtJumpPage.Text = this.intCurPageIndex.ToString();
        }

        /// <summary>
        /// 点击分页按钮
        /// </summary>
        private void tbarPage_Click(object sender, RoutedEventArgs e)
        {
            Button btnPage = e.Source as Button;
            if (btnPage != null)
            {
                switch (btnPage.Name)
                {
                    case "btnFirstPage":
                        this.intCurPageIndex = 1;
                        break;
                    case "btnPreviousPage":
                        this.intCurPageIndex = (intCurPageIndex > 1 ? intCurPageIndex - 1 : 1);
                        break;
                    case "btnNextPage":
                        this.intCurPageIndex = (intCurPageIndex < intPageSum ? intCurPageIndex + 1 : intCurPageIndex);
                        break;
                    case "btnLastPage":
                        this.intCurPageIndex = intPageSum;
                        break;
                }

                //加载分页数据
                this.LoadPageData();
            }
        }

        /// <summary>
        /// 加载分页数据
        /// </summary>
        private void LoadPageData()
        {
            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //加载数据源
                LoadData();

                //显示分页信息
                ShowPage();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 跳转页面
        /// </summary>
        void PageTextBox_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                TextBox txtInput = e.Source as TextBox;
                if (txtInput != null)
                {
                    //验证是否输入非零正整数
                    if (!SiaSun.LMS.Common.RegexValid.IsValidPositive(txtInput.Text))
                    {
                        //设置焦点
                        txtInput.SelectAll();
                        txtInput.Focus();
                        return;
                    }

                    //判断是哪个控件
                    if (txtInput.Equals(this.txtChangeRowPrePage))       //改变每页显示的行数
                    {
                        //每页显示行数
                        this.intRowCountPerPage = Convert.ToInt32(txtInput.Text.Trim());
                    }
                    else if (txtInput.Equals(this.txtJumpPage))
                    {
                        //判断是否大于总页数
                        if (Convert.ToInt32(txtInput.Text.Trim()) > this.intPageSum)
                        {
                            //选中并设置焦点
                            txtInput.Text = this.intPageSum.ToString();
                            txtInput.SelectAll();
                            return;
                        }

                        //跳转当前页
                        this.intCurPageIndex = Convert.ToInt32(txtInput.Text.Trim());
                    }

                    //加载分页数据
                    this.LoadPageData();
                }
            }
        }


        #endregion

        #region      ------数据操作工具栏

        /// <summary>
        /// 通用按钮
        /// </summary>
        private void tarCommon_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.Source as Button;
            if (btn == null) return;

            try
            {
                switch (btn.Name)
                {
                    case "btnDetail":
                        this.ShowDetail();
                        break;
                    case "btnPrint":
                        this.Print();
                        break;
                    case "btnExportExcel":
                        this.ExporTotExcel();
                        break;
                    case "btnRefresh":
                        this.U_Update();
                        break;
                    case "btnQuery":
                        this.Query();
                        break;
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 数据操作按钮
        /// </summary>
        private void tbarButton_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.Source as Button;
            if (btn == null) return;

            try
            {
                switch (btn.Name)
                {
                    case "btnAdd":
                        this.AddData();
                        break;
                    case "btnEdit":
                        this.EditData();
                        break;
                    case "btnDelete":
                        this.DeleteData();
                        break;
                    case "btnCancel":
                        this.CancelData();
                        break;
                    case "btnSave":
                        this.SaveData();
                        break;
                }

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 添加记录
        /// </summary>
        private void AddData()
        {
            //获得数据源
            using (DataTable tableSource = (this.gridApp.ItemsSource as DataView).Table)
            {
                //忽略约束
                tableSource.DataSet.EnforceConstraints = false;

                //显示编辑窗口
                DataRow rowSource = tableSource.NewRow();
                tableSource.Rows.Add(rowSource);

                SiaSun.LMS.WPFClient.Dialog.EditPanelDialog _winEdit = new Dialog.EditPanelDialog();
                _winEdit.U_WindowName = null;
                _winEdit.U_XmlTableName = this.strTableName;
                _winEdit.U_InitWindow(this.gridApp, rowSource);
                if (_winEdit.ShowDialog() == false)
                {
                    tableSource.RejectChanges();
                }
            }
        }

        /// <summary>
        /// 编辑记录
        /// </summary>
        private void EditData()
        {
            if (this.gridApp.SelectedItem != null)
            {
                DataRow rowSource = (this.gridApp.SelectedItem as DataRowView).Row;

                SiaSun.LMS.WPFClient.Dialog.EditPanelDialog _winEdit = new Dialog.EditPanelDialog();
                _winEdit.U_WindowName = null;
                _winEdit.U_XmlTableName = this.strTableName;
                _winEdit.U_InitWindow(this.gridApp, rowSource);
                if (_winEdit.ShowDialog() == false)
                {
                    rowSource.Table.RejectChanges();
                }
            }
        }

        /// <summary>
        /// 删除记录
        /// </summary>
        private void DeleteData()
        {
            if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Delete, string.Empty) == Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                //结束编辑
                this.gridApp.CommitEdit();

                //删除选中记录
                if (this.boolAllowCheck)
                {
                    DataRowView[] arCheckedRowView = this.U_GetCheckDataRow();
                    if (arCheckedRowView.Length > 0)
                    {
                        for (int i = arCheckedRowView.Length - 1; i >= 0; i--)
                        {
                            arCheckedRowView[i].Row.Delete();
                        }
                    }
                }
                else
                {
                    IList<object> listItem = (IList<object>)this.gridApp.SelectedItems;

                    //检查是否选中整行数据
                    if (listItem.Count == 0)
                    {
                        MainApp._MessageDialog.Show(Enum.MessageConverter.SelectCount);
                        return;
                    }

                    for (int i = listItem.Count - 1; i >= 0; i--)
                    {
                        if (listItem[i] is DataRowView)
                        {
                            (listItem[i] as DataRowView).Row.Delete();
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 取消更改
        /// </summary>
        public void CancelData()
        {
            if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Cancel) == Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                //获得数据源编辑
                DataView dataViewSource = this.gridApp.ItemsSource as DataView;

                //取消更改
                dataViewSource.Table.RejectChanges();

                //取消选中
                this.chkBoxCheckAll.IsChecked = false;
            }
        }

        /// <summary>
        /// 保存并提交数据更新
        /// </summary>
        private void SaveData()
        {
            if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.Save) == Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                try
                {
                    if (this.U_DataSource != null)
                    {
                        //结束编辑
                        this.U_EndCurrentEdit();

                        //提交更新
                        //xcjt alter 2017-01-03
                        //int intAffect = MainApp._I_BaseService.Save(this.U_DataSource.Table, strTableName,U_DataAccess);
                        int intAffect = MainApp._I_BaseService.Save(this.U_DataSource.Table, string.IsNullOrEmpty(strSaveDataTable) ? strTableName : strSaveDataTable, U_DataAccess);
                        MainApp._MessageDialog.Show(intAffect > 0);
                        if (intAffect > 0)
                        {
                            //刷新
                            this.U_Update();

                            //xcjt add 2017-01-03
                            SaveEventArgs newArgs = new SaveEventArgs(intAffect);
                            OnSaved(newArgs);
                        }
                    }
                }
                catch (ConstraintException straintEx)
                {
                    MainApp._MessageDialog.ShowException(straintEx);
                    return;
                }
                catch (NoNullAllowedException nullEx)
                {
                    MainApp._MessageDialog.ShowException(nullEx);
                    return;
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                    return;
                }
            }
        }

        /// <summary>
        /// 查询
        /// </summary>
        private void Query()
        {
            Dialog.QueryDialog winQuery = new Dialog.QueryDialog();
            winQuery.U_WindowName = this._windowName;
            winQuery.U_XmlTableName = this.strTableName;
            if (winQuery.ShowDialog() == true)
            {
                //设置追加查询条件
                this.strAppendFilter = winQuery.U_Where;
                //重新加载数据
                LoadData();
                //显示分页信息
                ShowPage();
                //取消选中
                this.chkBoxCheckAll.IsChecked = false;
            }
        }

        /// <summary>
        /// 直接打印
        /// </summary>
        private void Print()
        {
            if (this.gridApp.ItemsSource != null)
            {
                using (DataTable tableSource = (this.gridApp.ItemsSource as DataView).Table)
                {
                    new SiaSun.LMS.WPFClient.Dialog.ReportWindow("表单打印", this._windowName, this.strTableName, tableSource).ShowDialog();
                }
            }
        }

        /// <summary>
        /// 导出Excel
        /// </summary>
        private void ExporTotExcel()
        {
            try
            {
                using (DataTable tableExport = this.GetExportTable())
                {
                    if (tableExport.Rows.Count > 0)
                    {
                        //导出文件
                        using (System.ComponentModel.BackgroundWorker bkWorkerExport = new System.ComponentModel.BackgroundWorker())
                        {
                            bkWorkerExport.DoWork += new System.ComponentModel.DoWorkEventHandler(bkWorkerExport_DoWork);
                            bkWorkerExport.RunWorkerAsync(tableExport);
                            bkWorkerExport.Dispose();
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 显示明细数据
        /// </summary>
        private void ShowDetail()
        {
            //tzyg add 2017-03-16
            if(this.isShowGoodsPositionTemplate)
            {
                if (this.gridApp.SelectedItem != null)
                {
                    DataRowView drv = this.gridApp.SelectedItem as DataRowView;
                    if (drv != null)
                    {
                        Dialog.GoodsPositionTemplate dg = new Dialog.GoodsPositionTemplate(drv["GOODS_CODE"].ToString());
                        dg.ShowDialog();
                    }
                }
            }

            if (string.IsNullOrEmpty(this.strDetailTableName) || string.IsNullOrEmpty(this.strDetailColumnName))
                return;
            if (this.gridApp.SelectedItem != null)
            {
                DataRowView drv = this.gridApp.SelectedItem as DataRowView;
                if (drv != null)
                {
                    Dialog.DataGridDialog dg = new Dialog.DataGridDialog("明细信息", strDetailTableName, string.Format("{0}={1}",this.strDetailColumnName,drv[this.strDetailColumnName]).ToString(), this.strDetailColumnName, false, null);
                    dg.ShowDialog();
                }
            }
        }

        /// <summary>
        /// 获得导出的数据源
        /// </summary>
        private DataTable GetExportTable()
        {
            DataTable tableExport = null;
            CustomerDescriptions cusDescription = new CustomerDescriptions();

            DataTable tableSource = (this.gridApp.ItemsSource as DataView).Table;

            tableExport = tableSource.Copy();

            //加载描述数据
            if (this.gridApp.ItemsSource != null)
            {
                using (DataTable tableFieldDescription = string.IsNullOrEmpty(this._windowName) ? cusDescription.GetStyleDataTable(this.strTableName) : cusDescription.GetFormStyleDataTable(_windowName, strTableName))
                {
                    //获得拆分数据
                    using (DataTable tableSplit = this.GetGoodsPropertyTable())
                    {
                        DataRow[] arSplit = tableSplit.Rows.Cast<DataRow>().ToArray();
                        DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray();
                        using ( tableSource = (this.gridApp.ItemsSource as DataView).Table)
                        {
                            //复制
                            tableExport = tableSource.Copy();
                            foreach (DataColumn col in tableSource.Columns)
                            {
                                //如果不存在列则删除
                                if (arRowField.Count(r => r["Column"].ToString() == col.ColumnName) == 0 && arSplit.Count(r => r["Column"].ToString() == col.ColumnName) == 0)
                                {
                                    tableExport.Columns.Remove(col.ColumnName);
                                }
                                else
                                {
                                    if (arRowField.Count(r => r["Column"].ToString() == col.ColumnName) > 0)
                                    {
                                        DataRow rowFiled = arRowField.First(r => r["Column"].ToString() == col.ColumnName);
                                        if (rowFiled.IsNull("Header") || string.IsNullOrEmpty(rowFiled["Header"].ToString()))
                                        {
                                            tableExport.Columns.Remove(col.ColumnName);
                                        }
                                        else
                                        {
                                            tableExport.Columns[col.ColumnName].ColumnName = rowFiled["Header"].ToString();
                                        }
                                    }
                                    else
                                    {
                                        DataRow rowSplit = arSplit.First(r => r["Column"].ToString() == col.ColumnName);
                                        if (rowSplit.IsNull("Header") || string.IsNullOrEmpty(rowSplit["Header"].ToString()))
                                        {
                                        tableExport.Columns.Remove(col.ColumnName);
                                        }
                                        else
                                        {
                                            tableExport.Columns[col.ColumnName].ColumnName = rowSplit["Header"].ToString();
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return tableExport;
        }

        /// <summary>
        /// 后台执行导出工作
        /// </summary>
        void bkWorkerExport_DoWork(object sender, System.ComponentModel.DoWorkEventArgs e)
        {
            if (e.Argument != null)
            {
                new SiaSun.LMS.Common.Excel().ExportAllToExcel((e.Argument as DataTable), string.Format("{0}", SiaSun.LMS.Common.StringUtil.GetDateTime()));
            }
        }

        #endregion

        #region     ------ContextMenu右键菜单

        /// <summary>
        /// 初始化右键菜单
        /// </summary>
        private void InitMenuStrip()
        {
            if (menuStrip != null)
            {
                ContextMenu menuGrid = (this.Resources["menuGrid"] as ContextMenu);
                if (menuGrid != null)
                {
                    //合并菜单                
                    MenuItem[] arItem = menuGrid.Items.Cast<MenuItem>().ToArray();
                    menuGrid.Items.Clear();
                    menuStrip.Items.Add(arItem);
                }
            }
        }

        /// <summary>
        /// 上下文菜单按钮事件
        /// </summary>
        private void menuGrid_Click(object sender, RoutedEventArgs e)
        {
            MenuItem menuItem = e.OriginalSource as MenuItem;
            if (menuItem != null)
            {
                switch (menuItem.Name)
                {
                    case "menuItemCopy":
                        this.Copy();
                        break;
                    case "menuItemDeepCopy":
                        this.DeepCopy();
                        break;
                    case "menuItemSetDefault":
                        this.Reset();
                        break;
                }
            }
        }

        /// <summary>
        /// 复制
        /// </summary>
        private void Copy()
        {
            if (this.gridApp.CurrentCell == null || this.gridApp.CurrentItem == null)
                return;

            string strValue = string.Empty;
            FrameworkElement element = this.gridApp.CurrentCell.Column.GetCellContent(this.gridApp.CurrentItem);

            //判断类型
            if (element is TextBlock)
            {
                strValue = (element as TextBlock).Text;
            }
            else if (element is ComboBox)
            {
                strValue = (element as ComboBox).Text;
            }

            //剪切数据
            if (!string.IsNullOrEmpty(strValue))
            {
                Clipboard.SetText(strValue, TextDataFormat.UnicodeText);
            }
        }

        /// <summary>
        /// 深度复制
        /// </summary>
        private void DeepCopy()
        {
            if (this.gridApp.CurrentCell == null)
                return;

            string strValue = string.Empty;
            FrameworkElement element = this.gridApp.CurrentCell.Column.GetCellContent(this.gridApp.CurrentItem);

            //判断类型
            if (element is TextBlock)
            {
                strValue = (element as TextBlock).Text;
            }
            else if (element is ComboBox)
            {
                strValue = (element as ComboBox).SelectedValue.ToString();
            }

            //剪切数据
            if (!string.IsNullOrEmpty(strValue))
            {
                Clipboard.SetText(strValue, TextDataFormat.UnicodeText);
            }
        }

        /// <summary>
        /// 重置
        /// </summary>
        private void Reset()
        {
            if (this.gridApp.CurrentCell == null)
                return;

            DataRowView dataRowView = this.gridApp.CurrentCell.Item as DataRowView;
            if (dataRowView != null)
            {
                string strValue = string.Empty;
                FrameworkElement element = this.gridApp.CurrentCell.Column.GetCellContent(this.gridApp.CurrentItem);

                //判断类型
                if (element is TextBlock)
                {
                    (element as TextBlock).Text = strValue;
                }
                else if (element is ComboBox)
                {
                    (element as ComboBox).SelectedIndex = -1;
                }
            }
        }

        #endregion

        #region     ------计算总计

        /// <summary>
        /// 计算总计数量
        /// </summary>
        private void ComputTotal()
        {
            if (strTotalColumnName.Length > 0)
            {
                if (this.gridApp.HasItems)
                {
                    DataGridColumn[] arColumn = this.gridApp.Columns.Cast<DataGridColumn>().ToArray();
                    if (arColumn.Length > 0)
                    {
                        if (arColumn.Count(col => col is DataGridTextColumn && ((col as DataGridTextColumn).Binding as Binding).Path.Path == strTotalColumnName) > 0)
                        {
                            DataGridColumn column = arColumn.Single(col => col is DataGridTextColumn && ((col as DataGridTextColumn).Binding as Binding).Path.Path == strTotalColumnName);
                            if (column != null)
                            {
                                //获得分页合计数量
                                if (boolAllowPage)
                                {
                                    //合计
                                    DataTable objSumOnePage = MainApp._I_BaseService.GetList(GetPageTotalSql(),U_DataAccess);
                                    //总计
                                    DataTable objSumAllPage = MainApp._I_BaseService.GetList(GetTotalSql(),U_DataAccess);
                                    this.txtTotal.Text = string.Format(this.txtTotal.Tag.ToString(),
                                                                        (objSumOnePage.Rows.Count == 0 ? "0" : objSumOnePage.Rows[0][0].ToString()),
                                                                        (objSumAllPage.Rows.Count==0 ? "0" : objSumAllPage.Rows[0][0].ToString()));
                                }
                                else
                                {
                                    //总计
                                    DataTable objSum = MainApp._I_BaseService.GetList(GetTotalSql(),U_DataAccess);
                                    this.txtTotal.Text = string.Format(this.txtTotal.Tag.ToString(),
                                                                        (objSum.Rows.Count ==0 ? "0" : objSum.Rows[0][0].ToString()),
                                                                        (objSum.Rows.Count == 0 ? "0" : objSum.Rows[0][0].ToString()));
                                }
                            }
                        }
                    }
                }
            }
        }

        #endregion

        #region     ------CheckBox选中框

        /// <summary>
        /// 获得所有选定的记录
        /// </summary>
        public DataRowView[] U_GetCheckedDataRows()
        {
            return this.U_GetCheckDataRow();
        }

        /// <summary>
        /// 取消选中
        /// </summary>
        public void U_RejectCheckedRow()
        {
            DataRowView[] arRowView = this.U_GetCheckDataRow();
            foreach (DataRowView rowView in arRowView)
            {
                this.U_CheckedRow(false, rowView);
            }
        }
        #endregion

        /// <summary>
        /// 鼠标双击事件
        /// </summary>
        private void gridApp_MouseDoubleClick(object sender, MouseButtonEventArgs e)
        {
            if (!this.U_AllowOperatData && this.gridApp.ItemsSource != null)
            {
                this.ShowDetail();
            }
        }



        /// <summary>
        /// 根据Xml文件，获得该数据表的第一个字段作为默认字段
        /// </summary>
        public string U_GetDefaultOrderColumn(string TableName)
        {
            XmlDocument xmlDoc = new XmlDocument();
            xmlDoc.Load(MainApp.File_FieldDescription_Path);
            XmlNode xn = xmlDoc.SelectSingleNode(string.Format(@"/Tables/Table[@Name='{0}']", TableName));
            if (xn == null)
                throw new Exception(string.Format("未定义{0}的描述信息！", TableName));
            return xn.ChildNodes[0].Attributes["Column"].Value;
        }

        /// <summary>
        /// 显示样式
        /// </summary>
        public void U_TranslateDataGridViewStyle(string FormName, string XmlTableName, List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listFIELD_DESCRIPTION, bool AllowEdit)
        {
            CustomerDescriptions cusDescription = new CustomerDescriptions();
            //加载描述数据
            using (DataTable tableFieldDescription = string.IsNullOrEmpty(FormName) ? cusDescription.GetStyleDataTable(XmlTableName) : cusDescription.GetFormStyleDataTable(FormName, XmlTableName))
            {
                //清空所有列
                this.gridApp.Columns.Clear();
                
                //根据序号排序并添加列显示
                DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                var queryField = from row in arRowField orderby Convert.ToInt32(row["Order"].ToString()) select row;

                //判断控件类型设置显示列样式
                foreach (DataRow rowField in queryField)
                {
                    try
                    {
                        //判断控件类型
                        string strControlType = rowField["ControlType"].ToString().ToLower();
                        if (!AllowEdit)
                            AllowEdit = !Convert.ToBoolean(Convert.ToInt32(rowField["ReadOnly"]));
                        switch (strControlType.ToLower())
                        {
                            case "checkbox":
                                this.U_AddCheckBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                            case "combobox":
                                this.U_AddComboBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                            case "elementcombox":
                                this.U_AddEelementComboBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                            case "comboboxquery":
                                this.U_AddComboBoxQueryColumnStyle(rowField, AllowEdit, -1);
                                break;
                            default:
                                this.U_AddTextBoxColumnStyle(rowField, AllowEdit, -1);
                                break;
                        }
                    }
                    catch (Exception ex)
                    {
                        throw new Exception(string.Format("U_TranslateDataGridViewStyle():{0}," + ex.Message, rowField["Column"].ToString()), ex);
                    }
                }

                //追加列的描述
                this.AppenFieldsDataGridViewStyle(tableFieldDescription, listFIELD_DESCRIPTION, AllowEdit);
            }
        }




        #region     ------添加DataGridView列和样式

        /// <summary>
        /// 添加CheckBox列
        /// </summary>
        internal DataGridColumn U_AddCheckBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            DataGridCheckBoxColumn dgv = new DataGridCheckBoxColumn();
            dgv.IsThreeState = false;

            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;
                bind.TargetNullValue = "0";
                dgv.Binding = bind;
            }

            //添加列样式
            this.U_AddDataColumnStyle(dgv, rowField, AllowEdit, Index);
            return dgv;
        }

        /// <summary>
        /// 添加TextBox列
        /// </summary>
        internal DataGridColumn U_AddTextBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            DataGridTextColumn dgv = new DataGridTextColumn();
            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;

                dgv.Binding = bind;

            }

            //添加列样式
            this.U_AddDataColumnStyle(dgv, rowField, AllowEdit, Index);
            return dgv;
        }

        /// <summary>
        /// 添加ComboBox列
        /// </summary>
        internal DataGridColumn U_AddComboBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            DataGridComboBoxColumn dgvCmbCol = new DataGridComboBoxColumn();
            
            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;
                dgvCmbCol.SelectedValueBinding = bind;
            }

            //wdz alter 2017-08-16
            //dgvCmbCol.DisplayMemberPath = "NAME".ToLower();
            //dgvCmbCol.SelectedValuePath = "VALUE".ToLower();
            dgvCmbCol.DisplayMemberPath = MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? "NAME" : "name";
            dgvCmbCol.SelectedValuePath = MainApp._SYS_PARAMETER["DbProvider"].ToLower() == "oracle" ? "VALUE" : "value";
            dgvCmbCol.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString()).DefaultView;

            //添加列样式
            this.U_AddDataColumnStyle(dgvCmbCol, rowField, AllowEdit, Index);
            return dgvCmbCol;
        }

        /// <summary>
        /// 
        /// </summary>
        internal DataGridColumn U_AddComboBoxQueryColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            //DataGridComboBoxColumn dgvCmbCol = new DataGridComboBoxColumn();
            DataGridTemplateColumn edtc = new DataGridTemplateColumn();
          
            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                //Binding bind = new Binding(rowField["Column"].ToString());
                //bind.Mode = BindingMode.TwoWay;
                //dgvCmbCol.SelectedValueBinding = bind;


                #region edtc
                DataTable dtedtc = new DataTable();

                if (!Resources.Contains(rowField["Column"].ToString() + "dt"))
                {
                    Resources.Add(rowField["Column"].ToString() + "dt", dtedtc);
                }

                XNamespace ns = "http://schemas.microsoft.com/winfx/2006/xaml/presentation";

                //XNamespace uc = "clr-namespace:DataGridComboBoxQuery;assembly=DataGridComboBoxQuery";//应用项目中的DataGridComboBoxQuery项目
                XNamespace uc = "clr-namespace:ExampleClassLibrary;assembly=ExampleClassLibrary";//应用DLL

                XElement xDataTemplate =
                    new XElement(
                        ns + "DataTemplate",
                        new XAttribute("xmlns", "http://schemas.microsoft.com/winfx/2006/xaml/presentation"),
                         new XElement(ns + "TextBlock", new XAttribute("Text", "{Binding Path="+rowField["Column"].ToString()+"}"))// car 是DataGrid itemsource中要显示的字段名
                    );
                XElement xEditDataTemplate =
                new XElement(
                    ns + "DataTemplate",
                    new XAttribute("xmlns", "http://schemas.microsoft.com/winfx/2006/xaml/presentation"),
                     new XElement(uc + "ComboBoxQuery",
                         new XAttribute("ComboBoxDataTable", "{DynamicResource "+rowField["Column"].ToString()+"dt"+"}"),
                         new XAttribute("ComboBoxDisplayMemberPath", "name"),//name 是ComboBox itemsource中要显示的字段名
                         new XAttribute("ComboBoxSelectValuePath", "value"),//value 是ComboBox itemsource中要显示的字段名
                         new XAttribute("SelectItem", "{Binding "+rowField["Column"].ToString()+",Mode=TwoWay}"),// 是DataGrid itemsource中要显示的字段名
                         //new XAttribute("SelectValue", "{Binding " + rowField["Column"].ToString() + ",Mode=TwoWay}"), // 获得ID              
                         new XAttribute("ComboBoxIsEditable", "True"),
                         new XAttribute("ComboBoxIsTextSearchCaseSensitive", "True"),
                         new XAttribute("ComboBoxIsTextSearchEnabled", "True"))
                );

                XmlReader xr = xDataTemplate.CreateReader();
                DataTemplate dataTemplate = System.Windows.Markup.XamlReader.Load(xr) as DataTemplate;

                XmlReader xrEdit = xEditDataTemplate.CreateReader();
                DataTemplate EditDataTemplate = System.Windows.Markup.XamlReader.Load(xrEdit) as DataTemplate;

                Resources[rowField["Column"].ToString() + "dt"] = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString());
                edtc.Header = rowField["Column"].ToString();
                edtc.CellTemplate = dataTemplate;

                edtc.CellEditingTemplate = EditDataTemplate;

                
                #endregion
            }

            //dgvCmbCol.DisplayMemberPath = "NAME".ToLower();
            //dgvCmbCol.SelectedValuePath = "VALUE".ToLower();
            //dgvCmbCol.ItemsSource = new CustomerDescriptions().GetComboBoxDataTable(rowField["DataBind"].ToString()).DefaultView;

            //添加列样式
            this.U_AddDataColumnStyle(edtc, rowField, AllowEdit, Index);
            return edtc;
        }

        /// <summary>
        /// 添加EelementBox列
        /// </summary>
        internal DataGridColumn U_AddEelementComboBoxColumnStyle(DataRow rowField, bool AllowEdit, int Index)
        {
            List<string> listItem = new List<string>();
            DataGridComboBoxColumn dgvElementCmbCol = new DataGridComboBoxColumn();

            //设置绑定信息
            if (!rowField.IsNull("Column"))
            {
                Binding bind = new Binding(rowField["Column"].ToString());
                bind.Mode = BindingMode.TwoWay;
                dgvElementCmbCol.TextBinding = bind;
            }

            foreach (string s in rowField["DataBind"].ToString().Split('|'))
            {
                //分解字符串添加ITEM数据
                if (s.TrimEnd().Length > 0)
                {
                    listItem.Add(s.TrimEnd());
                }
            }
            dgvElementCmbCol.ItemsSource = listItem;
            //添加列样式
            this.U_AddDataColumnStyle(dgvElementCmbCol, rowField, AllowEdit, Index);
            return dgvElementCmbCol;
        }

        /// <summary>
        /// 添加列并设置列样式
        /// </summary>
        internal void U_AddDataColumnStyle(DataGridColumn dgvCol, DataRow rowField, bool AllowEdit, int Index)
        {
            //添加列
            if (!this.gridApp.Columns.Contains(dgvCol))
            {
                //是否显示
                dgvCol.Visibility = string.IsNullOrEmpty(rowField["Header"].ToString()) ? System.Windows.Visibility.Hidden : Visibility.Visible;

                //判断是否可见
                if (AllowEdit)
                {
                    if (dgvCol.Visibility == System.Windows.Visibility.Visible)
                    {
                        //是否是否可读
                        if (rowField.Table.Columns.Contains("ReadOnly"))
                        {
                            dgvCol.IsReadOnly = (!rowField.IsNull("ReadOnly") && Convert.ToInt32(rowField["ReadOnly"].ToString()) == 1);
                        }
                        else
                        {
                            dgvCol.IsReadOnly = !AllowEdit;
                        }
                    }
                    else
                    {
                        if (rowField.Table.Columns.Contains("ReadOnly"))
                        {
                            dgvCol.IsReadOnly = (!rowField.IsNull("ReadOnly") && Convert.ToInt32(rowField["ReadOnly"].ToString()) == 1);
                        }
                        else
                        {
                            dgvCol.IsReadOnly = !AllowEdit;
                        }
                    }
                }
                else
                {
                    dgvCol.IsReadOnly = !AllowEdit;
                }

                //设置标题，可编辑时，特殊显示
                dgvCol.Header = (dgvCol.IsReadOnly || dgvCol.Visibility == System.Windows.Visibility.Hidden) ? rowField["Header"].ToString() : string.Format("[{0}]", rowField["Header"].ToString());

                //add column
                if (Index < 0)
                {
                    this.gridApp.Columns.Add(dgvCol);
                }
                else
                {
                    this.gridApp.Columns.Insert(Index, dgvCol);
                }
            }
        }

        #endregion


        /// <summary>
        /// 添加追加的列描述
        /// </summary>
        private void AppenFieldsDataGridViewStyle(DataTable tableFieldDescription, List<SiaSun.LMS.Model.FIELD_DESCRIPTION> listFIELD_DESCRIPTION, bool AllowEdit)
        {
            //判断列表是否空
            if (listFIELD_DESCRIPTION == null)
                return;

            //添加新行
            DataRow rowDescription = tableFieldDescription.NewRow();

            //追加描述列
            foreach (SiaSun.LMS.Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION in listFIELD_DESCRIPTION)
            {
                try
                {
                    //赋值
                    rowDescription["Column"] = mFIELD_DESCRIPTION.Column;
                    rowDescription["Header"] = mFIELD_DESCRIPTION.Header;
                    rowDescription["DbType"] = mFIELD_DESCRIPTION.DbType;
                    rowDescription["Validation"] = mFIELD_DESCRIPTION.Validation;
                    rowDescription["DefaultValue"] = mFIELD_DESCRIPTION.DefaultValue;
                    rowDescription["ReadOnly"] = Convert.ToInt32(mFIELD_DESCRIPTION.ReadOnly);
                    rowDescription["ControlType"] = mFIELD_DESCRIPTION.ControlType;
                    rowDescription["Order"] = mFIELD_DESCRIPTION.Order;
                    rowDescription["DataBind"] = mFIELD_DESCRIPTION.DataBind;
                    rowDescription["Remark"] = mFIELD_DESCRIPTION.Remark;

                    //判断类型
                    switch (mFIELD_DESCRIPTION.ControlType.ToLower())
                    {
                        case "checkbox":
                            this.U_AddCheckBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                        case "combobox":
                            this.U_AddComboBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                        case "elementcombox":
                            this.U_AddEelementComboBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                        default:
                            this.U_AddTextBoxColumnStyle(rowDescription, AllowEdit, -1);
                            break;
                    }
                }
                catch (Exception ex)
                {
                    throw new Exception(string.Format("AppenFieldsDataGridViewStyle():{0}," + ex.Message, rowDescription["Column"].ToString()), ex);
                }
            }
        }


        /// <summary>
        /// 向DataGridView中插入CheckBox列
        /// </summary>
        public void U_AddCheckBoxDataGridColumn()
        {
            //判断是否已经存在
            if (this.gridApp.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) == 0)
            {
                DataGridCheckBoxColumn chkBoxCol = new DataGridCheckBoxColumn();
                chkBoxCol.Header = strCheckedColumnName;
                chkBoxCol.IsThreeState = false;
                chkBoxCol.CanUserReorder = false;
                this.gridApp.Columns.Insert(0, chkBoxCol);
            }
        }


        /// <summary>
        /// 设置数据源列的默认值
        /// </summary>
        public void U_SetDataTableConstraints(string XmlTableName)
        {
            if (this.gridApp.ItemsSource == null)
                return;

            //获得数据源
            using (DataTable tableSource = (this.gridApp.ItemsSource as DataView).Table)
            {
                //加载描述数据
                CustomerDescriptions cusDescription = new CustomerDescriptions();
                using (DataTable tableFieldDescription = this._windowName == null ? cusDescription.GetStyleDataTable(XmlTableName) : cusDescription.GetFormStyleDataTable(this._windowName, XmlTableName))
                {
                    //获得数组
                    DataRow[] arRowField = tableFieldDescription.Rows.Cast<DataRow>().ToArray<DataRow>();
                    var query = from qRow in arRowField where qRow["Header"].ToString().Length > 0 || qRow["Column"].ToString() == "USER_PASSWORD" select qRow;

                    //设置每个列的默认值
                    foreach (DataRow rowField in query)
                    {
                        DataColumn column = tableSource.Columns[rowField["Column"].ToString()];
                        if (column == null)
                            continue;
                        try
                        {
                            //判断默认值是否存在
                            if (rowField.Table.Columns.Contains("DefaultValue") && !rowField.IsNull("DefaultValue") && !String.IsNullOrEmpty(rowField["DefaultValue"].ToString()))
                            {
                                //设置默认值
                                column.DefaultValue = rowField["DefaultValue"];

                            }

                            //判断是否存在校验
                            if (rowField.Table.Columns.Contains("Validation") && !rowField.IsNull("Validation") && !String.IsNullOrEmpty(rowField["Validation"].ToString()))
                            {
                                //获得校验规则
                                string[] arStringValidate = rowField["Validation"].ToString().ToLower().Split('|');
                                foreach (string strValidation in arStringValidate)
                                {
                                    //设置校验约束
                                    //非空值
                                    if (strValidation.Contains("nonenull"))
                                    {
                                        column.AllowDBNull = false;
                                    }
                                    //值唯一
                                    if (strValidation.Contains("unique"))
                                    {
                                        column.Unique = true;
                                    }
                                    //最大长度
                                    if (strValidation.Contains("maxlength"))
                                    {
                                        string[] arValidateLength = strValidation.Split('=');
                                        if (arValidateLength.Length == 2)
                                        {
                                            column.MaxLength = Convert.ToInt32(strValidation.Split('=')[1]);
                                        }
                                    }
                                }
                            }
                        }
                        catch (Exception ex)
                        {
                            string strResult = ex.Message;
                            continue;
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 全选所有行
        /// </summary>
        public void U_CheckedAllRows(bool IsChecked)
        {
            if (this.gridApp.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) > 0)
            {
                foreach (object item in this.gridApp.Items)
                {
                    if (item is DataRowView)
                    {
                        DataRowView viewRow = item as DataRowView;
                        DataGridColumn col = this.gridApp.Columns.First(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName);
                        CheckBox chkBox = col.GetCellContent(viewRow) as CheckBox;
                        if (chkBox != null)
                        {
                            chkBox.IsChecked = IsChecked;
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 根据条件选定DataGrid的行
        /// </summary>
        public bool U_SelectDataGridViewRow(string ColumnName, string CellValue)
        {
            bool finded = false;
            if (this.gridApp.ItemsSource is DataView)
            {
                object[] arItems = this.gridApp.Items.Cast<object>().ToArray();
                var query = from item in arItems where item is DataRowView && (item as DataRowView)[ColumnName].ToString() == CellValue select item;
                if (query.Count() > 0)
                {
                    this.gridApp.SelectedItem = query.First();
                }
            }
            return finded;
        }



        /// <summary>
        /// 加载拆分列
        /// </summary>
        public void U_TranslateSplitColumnStyles(DataTable tableSource)
        {
            //检查是否设置拆分列
            if (strSplitPropertyKey.Length == 0)
                return;

            if (this.gridApp.Columns.Count(r => r is DataGridTextColumn && (((r as DataGridTextColumn).Binding as Binding).Path as PropertyPath).Path == strSplitColumnName) == 0)
                return;

            //获得该列
            DataGridColumn columnProperty = this.gridApp.Columns.First(r => r is DataGridTextColumn && (((r as DataGridTextColumn).Binding as Binding).Path as PropertyPath).Path == strSplitColumnName);


            ////获得索引
            int index = this.gridApp.Columns.IndexOf(columnProperty);

            bool allowEdit = !columnProperty.IsReadOnly;

            this.gridApp.Columns.RemoveAt(index);
            //获得拆分数据源
            tableSplit = GetGoodsPropertyTable();

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit != null && tableSplit.Rows.Count > 0)
            {
                //转化为数组
                DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                //排序
                var query = from rowSplit in arSplitRow orderby rowSplit["GOODS_PROPERTY_ORDER"] descending select rowSplit;
                StringBuilder strBuilderSplit = new StringBuilder();

                //获得属性列索引
                foreach (DataRow row in query)
                {
                    if (columnProperty == null)
                        continue;

                    DataGridColumn column = null;
                    //判断控件类型
                    string strControlType = row["ControlType"].ToString().ToLower();
                    switch (strControlType)
                    {
                        case "checkbox":
                            column = this.U_AddCheckBoxColumnStyle(row, allowEdit, index);
                            break;
                        case "combobox":
                            column = this.U_AddComboBoxColumnStyle(row, allowEdit, index);
                            break;
                        case "elementcombobox":
                            column = this.U_AddEelementComboBoxColumnStyle(row, allowEdit, index);
                            break;
                        case "comboboxquery":
                            column = this.U_AddComboBoxQueryColumnStyle(row, allowEdit, index);
                            break;
                        default:
                            column = this.U_AddTextBoxColumnStyle(row, allowEdit, index);
                            break;
                    }

                    //添加DataTable列
                    if (!tableSource.Columns.Contains(row["Column"].ToString()))
                    {
                        tableSource.Columns.Add(row["Column"].ToString(), typeof(System.String));
                    }
                }
            }
        }

        /// <summary>
        /// 获得拆分属性数据源
        /// </summary>
        private DataTable GetGoodsPropertyTable()
        {
            //tzyg add 2017-03-25
            if (string.IsNullOrEmpty(strSplitPropertyKey))
            {
                strSplitPropertyKey = "1";
            }

            DataTable tableSplitSource = MainApp._I_BaseService.GetList(string.Format("select * from goods_property where goods_property_flag='1'and goods_type_id = {0} order by goods_property_order", strSplitPropertyKey));
            //设置别名
            tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Column";
            tableSplitSource.Columns["GOODS_PROPERTY_NAME"].ColumnName = "Header";
            tableSplitSource.Columns["GOODS_PROPERTY_DATASOURCE"].ColumnName = "DataBind";
            tableSplitSource.Columns["GOODS_PROPERTY_FIELDTYPE"].ColumnName = "ControlType";
            //tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Field";
            tableSplitSource.Columns["GOODS_PROPERTY_VALID"].ColumnName = "Validation";

            return tableSplitSource;
        }

        /// <summary>
        /// 解析数据源,解析组合属性，设置各个属性值
        /// </summary>
        public void U_TanslateSplitDataTable(DataTable tableSource, bool HasChanges)
        {
            //检查是否设置拆分列
            if (strSplitPropertyKey.Length == 0)
                return;

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit != null && tableSplit.Rows.Count > 0)
            {
                //转化为数组
                DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                //排序
                var query = from rowSplit in arSplitRow orderby rowSplit["GOODS_PROPERTY_ORDER"] select rowSplit;

                //获得属性列索引
                foreach (DataRow row in query)
                {
                    //添加DataTable列
                    if (!tableSource.Columns.Contains(row["Column"].ToString()))
                    {
                        tableSource.Columns.Add(row["Column"].ToString(), typeof(System.String));
                    }
                }

                //判断是否存在记录
                if (tableSource.Rows.Count > 0 && tableSource.Columns.Contains(strSplitColumnName))
                {
                    //解析DataTable中的行数据
                    DataRow[] arRow = tableSource.Rows.Cast<DataRow>().ToArray<DataRow>();
                    var queryRow = from row in arRow where (HasChanges ? (row.RowState != DataRowState.Unchanged) : (row.RowState == DataRowState.Unchanged)) select row;

                    //合并组合各个属性值
                    foreach (DataRow rowSource in queryRow)
                    {
                        int i = 0;
                        string[] arStrProperty = rowSource[strSplitColumnName].ToString().Split('|');
                        foreach (DataRow rowProperty in query)
                        {
                            rowSource[rowProperty["Column"].ToString()] = (i < arStrProperty.Length) ? arStrProperty[i] : string.Empty;
                            i++;
                        }
                    }
                }
            }
        }


        /// <summary>
        /// 更新组合属性数据源,将各个属性值组合并成属性字符串
        /// </summary>
        public void U_CombinSplitPropertyTable()
        {
            //检查是否设置拆分列
            if (strSplitColumnName.Length == 0 || strSplitPropertyKey.Length == 0 || strSplitPropertyType.Length == 0)
            return;

            //判断是否设置了该种类型的物料拆分列的信息
            if (tableSplit != null && tableSplit.Rows.Count > 0)
            {
                //获得数据源
                DataView dataViewSource = (this.gridApp.ItemsSource as DataView);
                if (dataViewSource != null)
                {
                    //转化为数组
                    DataRow[] arSplitRow = tableSplit.Rows.Cast<DataRow>().ToArray<DataRow>();

                    //排序
                    var query = from rowSplit in arSplitRow orderby Convert.ToInt32(rowSplit["GOODS_PROPERTY_ORDER"]) select rowSplit;

                    //遍历每一行设置组合属性的值
                    DataRow[] arDataRow = dataViewSource.Table.Rows.Cast<DataRow>().ToArray();
                    //查找数据更改的行并遍历
                    var querySource = from rowQuery in arDataRow where rowQuery.RowState == DataRowState.Added || rowQuery.RowState == DataRowState.Detached || rowQuery.RowState == DataRowState.Modified select rowQuery;
                    foreach (DataRow rowSource in querySource)
                    {
                        StringBuilder strBuilderSplit = new StringBuilder();
                        foreach (DataRow row in query)
                        {
                            //拆分属性值
                            string strPropertyCode = row["Column"].ToString();
                            //获得默认值
                            //string strDefaultValue = row.IsNull("DefaultValue") ? string.Empty : row["DefaultValue"].ToString();
                            //获得属性值
                            string strPropertyValue = rowSource.IsNull(strPropertyCode) ? string.Empty : rowSource[strPropertyCode].ToString();

                            if (strBuilderSplit.Length == 0)
                                strBuilderSplit.Append(strPropertyValue);
                            else
                                strBuilderSplit.Append('|').Append(strPropertyValue);
                        }

                        //获得组合后的值
                        rowSource[strSplitColumnName] = strBuilderSplit.ToString();
                    }
                }
            }
        }

        /// <summary>
        /// 校验拆分属性填写是否合法
        /// </summary>
        public bool U_CheckSplitProperty(string ColumnName, out string sResult)
        {
            bool boolResult = true;
            sResult = string.Empty;

            //判断数据源是否空
            if (this.gridApp.ItemsSource != null)
            {
                //获得数据源
                using (DataTable tableSource = (this.gridApp.ItemsSource as DataView).Table.GetChanges(DataRowState.Added | DataRowState.Detached | DataRowState.Modified))
                {
                    if (tableSource != null)
                    {
                        //遍历所有行，检查是否合法
                        foreach (DataRow rowSource in tableSource.Rows)
                        {
                            if (tableSplit != null)
                            {
                                //遍历拆分属性
                                foreach (DataRow rowSplit in tableSplit.Rows)
                                {
                                    //检查是否空
                                    string strPropertyCode = rowSplit["Column"].ToString();
                                    //判断是否允许空
                                    if (!rowSplit.IsNull("Validation") && rowSplit["Validation"].ToString() == "1")
                                    {
                                        //判断属性值是否空
                                        if (rowSource.IsNull(strPropertyCode) || rowSource[strPropertyCode].ToString().Length == 0)
                                        {
                                            boolResult = false;
                                            sResult = string.Format("{0},{1}的属性值不允许空！", rowSplit["Validation"].ToString(), rowSplit["header"].ToString());
                                            return boolResult;
                                        }
                                    }

                                    //判断是否唯一
                                    //if (!rowSplit.IsNull("IsUnique") && Convert.ToInt32(rowSplit["IsUnique"]) == 1)
                                    //{ 
                                    //判断是否存在类似的库存信息
                                    //if (MainApp._I_BaseService.Exist("STORAGE_LIST", string.Format("{0} LIKE '%{1}%'",ColumnName,rowSource[strPropertyCode].ToString())))
                                    //{
                                    //    sResult = string.Format("请检查{0}是否已经存在！", rowSplit["header"].ToString());
                                    //    boolResult = false;
                                    //    return boolResult;
                                    //}
                                    //}
                                }
                            }
                        }
                    }
                }
            }
            return boolResult;
        }

        /// <summary>
        /// 获得选中的数据行
        /// </summary>
        public DataRowView[] U_GetCheckDataRow()
        {
            List<DataRowView> listDataRow = new List<DataRowView>();
            if (this.gridApp.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) > 0)
            {
                foreach (object item in this.gridApp.Items)
                {
                    if (item is DataRowView)
                    {
                        DataRowView viewRow = item as DataRowView;

                        DataGridColumn col = this.gridApp.Columns.First(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName);
                        CheckBox chkBox = col.GetCellContent(viewRow) as CheckBox;
                        if (chkBox != null && chkBox.IsChecked == true)
                        {
                            listDataRow.Add(viewRow);
                        }
                    }
                }
            }
            return listDataRow.ToArray();
        }


        /// <summary>
        /// 设置行是否选择
        /// </summary>
        public void U_CheckedRow(bool IsChecked, DataRowView rowViewChecked)
        {
            if (this.gridApp.Columns.Count(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName) > 0)
            {
                DataGridColumn col = this.gridApp.Columns.First(r => r is DataGridCheckBoxColumn && (r as DataGridCheckBoxColumn).Header.ToString() == strCheckedColumnName);
                CheckBox chkBox = col.GetCellContent(rowViewChecked) as CheckBox;
                if (chkBox != null)
                {
                    chkBox.IsChecked = IsChecked;
                }
            }
        }



    }
}
