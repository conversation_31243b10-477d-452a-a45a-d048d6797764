﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class TemplateBase:S_BaseService
    {
        public TemplateBase()
        {
        }

        /// <summary>
        ///  计划创建 
        /// </summary>
        /// <param name="mPLAN_MAIN">计划模型</param>
        /// <param name="lsPLAN_LIST">计划列表</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool TemplateCreate(SiaSun.LMS.Model.GOODS_TEMPLATE mGOODS_TEMPLATE,
                               DataTable dtTEMPLATE_LIST,
                               out int TEMPLATE_ID,
                               out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            TEMPLATE_ID = 0;

            try
            {
                this._P_Base_House.BeginTransaction();

                if (null != this._P_GOODS_TEMPLETE.GetModel(mGOODS_TEMPLATE.GOODS_TEMPLATE_ID))
                {

                    this._P_GOODS_TEMPLETE.Update(mGOODS_TEMPLATE);
                }
                else
                {
                    if (null != this._P_GOODS_TEMPLETE.GetModel(mGOODS_TEMPLATE.GOODS_TEMPLATE_CODE))
                    {
                        bResult = false;

                        sResult = string.Format("{0} 配备方案已经存在!", mGOODS_TEMPLATE.GOODS_TEMPLATE_CODE);

                        this._P_Base_House.RollBackTransaction();

                        return bResult;
                    }

                    this._P_GOODS_TEMPLETE.Add(mGOODS_TEMPLATE);
                }


                foreach (DataRow drTEMPLATE_LIST in dtTEMPLATE_LIST.Rows)
                    {

                        if (drTEMPLATE_LIST.RowState != DataRowState.Deleted)
                        {
                            drTEMPLATE_LIST["GOODS_TEMPLATE_ID"] = mGOODS_TEMPLATE.GOODS_TEMPLATE_ID;
                        }
                    }

                this.Save(dtTEMPLATE_LIST, "GOODS_TEMPLATE_LIST");


                this._P_Base_House.CommitTransaction();
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }

            TEMPLATE_ID = mGOODS_TEMPLATE.GOODS_TEMPLATE_ID;

            return bResult;
        }

        /// <summary>创建
        /// 创建
        /// </summary>
        /// <param name="mPLAN_MAIN">计划模型</param>
        /// <param name="lsPLAN_LIST">计划列表</param>
        /// <param name="sResult">返回结果</param>
        /// <returns></returns>
        public bool TemplateCreate(SiaSun.LMS.Model.GOODS_TEMPLATE mGOODS_TEMPLATE,
                                   IList<SiaSun.LMS.Model.GOODS_TEMPLATE_LIST> lsGOODS_TEMPLATE_LIST,
                                   out int TEMPLATE_ID,
                                   out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            TEMPLATE_ID = 0;

            try
            {
                this._P_Base_House.BeginTransaction();

                if (null != this._P_GOODS_TEMPLETE.GetModel(mGOODS_TEMPLATE.GOODS_TEMPLATE_ID))
                {
                    this._P_GOODS_TEMPLETE.Update(mGOODS_TEMPLATE);
                }
                else
                {
                    if (null != this._P_GOODS_TEMPLETE.GetModel(mGOODS_TEMPLATE.GOODS_TEMPLATE_CODE))
                    {
                        bResult = false;

                        sResult = string.Format("{0} 配盘方案已经存在!", mGOODS_TEMPLATE.GOODS_TEMPLATE_CODE);

                        return bResult;
                    }

                    this._P_GOODS_TEMPLETE.Add(mGOODS_TEMPLATE);
                }

                foreach (SiaSun.LMS.Model.GOODS_TEMPLATE_LIST mTEMPLATE_LIST in lsGOODS_TEMPLATE_LIST)
                {

                    if (mTEMPLATE_LIST.GOODS_TEMPLATE_ID.Equals(0))
                    {
                        mTEMPLATE_LIST.GOODS_TEMPLATE_ID = mGOODS_TEMPLATE.GOODS_TEMPLATE_ID;
                        this._P_GOODS_TEMPLETE_LIST.Add(mTEMPLATE_LIST);
                    }
                    else
                    {
                        this._P_GOODS_TEMPLETE_LIST.Update(mTEMPLATE_LIST);
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                this._P_Base_House.RollBackTransaction();
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }

            TEMPLATE_ID = mGOODS_TEMPLATE.GOODS_TEMPLATE_ID; ;

            return bResult;
        }

       
    }
}
