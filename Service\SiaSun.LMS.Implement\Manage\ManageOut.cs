﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ManageOut:ManageBase
    {
        /// <summary>
        /// 根据计划生成出库任务
        /// 调用界面 MANAGE_PLAN_OUT.XAML
        /// </summary>
        /// <param name="mMANAGE_MAIN"></param>
        /// <param name="lsPLAN_LIST"></param>
        /// <param name="lsMANAGE_LIST"></param>
        /// <param name="bTrans"></param>
        /// <param name="bAutoSendControl"></param>
        /// <param name="bComplete"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                  List<SiaSun.LMS.Model.PLAN_LIST> lsPLAN_LIST,
                                  List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                  bool bTrans,
                                  bool bAutoSendControl,
                                  bool bComplete, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_START = null;

            if (mWH_CELL_START != null)
                mWH_AREA_START = this._P_WH_AREA.GetModel(mWH_CELL_START.AREA_ID);

            SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_END = null;

            if (mWH_CELL_END != null)
                mWH_AREA_END = this._P_WH_AREA.GetModel(mWH_CELL_END.AREA_ID);

            IList<SiaSun.LMS.Model.STORAGE_MAIN> lsSTORAGE_MAIN = null;

            if (mMANAGE_MAIN.STOCK_BARCODE == string.Empty)
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListCellID(mMANAGE_MAIN.START_CELL_ID);
            else
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = null;

            SiaSun.LMS.Model.PLAN_LIST mPLAN_LIST = null;

            bResult = lsSTORAGE_MAIN.Count > 0;

            if (!bResult)
            {
                sResult = string.Format("未找到库存");

                return bResult;
            }

            //校验库存位置和任务位置是否相符 2022-04-18
            if (lsSTORAGE_MAIN.Any(t => t.CELL_ID != mWH_CELL_START.CELL_ID))
            {
                bResult = false;
                sResult = string.Format(@"条码[{0}]的库存位置与任务起点不相符", mMANAGE_MAIN.STOCK_BARCODE);
                return bResult;
            }


            if (!string.IsNullOrEmpty( lsSTORAGE_MAIN[0].STOCK_BARCODE) && this._P_MANAGE_MAIN.GetModelStockBarcode(lsSTORAGE_MAIN[0].STOCK_BARCODE) != null)
            {
                bResult = false;

                sResult = string.Format("托盘条码{0}已经存在任务", lsSTORAGE_MAIN[0].STOCK_BARCODE);

                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                if (lsPLAN_LIST!=null && lsPLAN_LIST.Count>0)
                {
                    mPLAN_LIST = this._P_PLAN_LIST.GetModel(lsPLAN_LIST[0].PLAN_LIST_ID);

                    mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mPLAN_LIST.PLAN_ID);

                    //wdz add 2017-12-05
                    if(mPLAN_MAIN.PLAN_TYPE_CODE==Enum.PLAN_TYPE_CODE.PlanOutEmerg.ToString()
                        || mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString())
                    {
                        mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WMS.ToString();
                    }
                }

                mMANAGE_MAIN.STOCK_BARCODE = lsSTORAGE_MAIN[0].STOCK_BARCODE;

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {

                    if (lsPLAN_LIST != null)
                    {
                        foreach (SiaSun.LMS.Model.PLAN_LIST temp_mPLAN_LIST in lsPLAN_LIST)
                        {
                            if (mMANAGE_LIST.GOODS_ID == temp_mPLAN_LIST.GOODS_ID)
                            {
                                mMANAGE_LIST.PLAN_LIST_ID = temp_mPLAN_LIST.PLAN_LIST_ID;

                                temp_mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                                this._P_PLAN_LIST.Update(temp_mPLAN_LIST);

                                break;
                            }
                        }
                    }

                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);

                }

                if (mWH_CELL_START != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (mWH_CELL_END != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (bAutoSendControl && mWH_AREA_START != null && mWH_AREA_START.AREA_TYPE.ToLower() == "liku")
                {
                    bResult = this.ManageDownLoad(mMANAGE_MAIN.MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (bComplete && (mWH_AREA_START == null || mWH_AREA_START.AREA_TYPE.ToLower() == "xuniku"))
                {
                    bResult = this.ManageComplete(mMANAGE_MAIN.MANAGE_ID, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                sResult = string.Format("托盘{0}生成任务成功", mMANAGE_MAIN.STOCK_BARCODE);

                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

       /// <summary>
       /// 生成无计划出库任务
       /// 调用界面 MANAGE_OUT.XAML
       /// </summary>
       /// <param name="mMANAGE_MAIN"></param>
       /// <param name="lsMANAGE_LIST"></param>
       /// <param name="bTrans"></param>
       /// <param name="bAutoSendControl"></param>
       /// <param name="bComplete"></param>
       /// <param name="sResult"></param>
       /// <returns></returns>
        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM,
                                 out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int MANAGE_ID = 0;

            SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_START= null;

            if( mWH_CELL_START != null)
                mWH_AREA_START = this._P_WH_AREA.GetModel(mWH_CELL_START.AREA_ID);

            SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_END=null;

            if( mWH_CELL_END != null)
                mWH_AREA_END = this._P_WH_AREA.GetModel(mWH_CELL_END.AREA_ID);

            if (mWH_CELL_START != null
                && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                && (mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Have.ToString() || mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Pallet.ToString())
                && mWH_CELL_START.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
            {
                bResult = false;

                sResult = string.Format("起始位置{0}不可用", mWH_CELL_START.CELL_CODE);

                return bResult;
            }

            if (mWH_CELL_END != null
                && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                && mWH_CELL_END.CELL_STATUS != Enum.CELL_STATUS.Nohave.ToString()
                && mWH_CELL_END.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
            {
                bResult = false;

                sResult = string.Format("终止位置{0}不可用", mWH_CELL_END.CELL_CODE);

                return bResult;
            }


            IList<SiaSun.LMS.Model.STORAGE_MAIN> lsSTORAGE_MAIN = null;

            if (mMANAGE_MAIN.STOCK_BARCODE == string.Empty)
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListCellID(mMANAGE_MAIN.START_CELL_ID);
            else
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

            bResult = lsSTORAGE_MAIN.Count > 0;

            if (!bResult)
            {
                sResult = string.Format("未找到库存");

                return bResult;
            }

            //校验库存位置和任务位置是否相符 2022-04-18
            if (lsSTORAGE_MAIN.Any(t => t.CELL_ID != mWH_CELL_START.CELL_ID))
            {
                bResult = false;
                sResult = string.Format(@"条码[{0}]的库存位置与任务起点不相符", mMANAGE_MAIN.STOCK_BARCODE);
                return bResult;
            }


            if (this._P_MANAGE_MAIN.GetModelStockBarcode(lsSTORAGE_MAIN[0].STOCK_BARCODE) != null)
            {
                bResult = false;

                sResult = string.Format("托盘条码{0}已经存在任务", lsSTORAGE_MAIN[0].STOCK_BARCODE);

                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                mMANAGE_MAIN.STOCK_BARCODE = lsSTORAGE_MAIN[0].STOCK_BARCODE;

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    mMANAGE_LIST.PLAN_LIST_ID = 0;

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);

                }
               
                if (mWH_CELL_START != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (mWH_CELL_END != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                if (mMANAGE_TYPE_PARAM.U_AutoDownloadControlTask && mWH_AREA_START!=null && mWH_AREA_START.AREA_TYPE.ToLower() == "liku")
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (mMANAGE_TYPE_PARAM.U_AutoCompleteTask && (mWH_AREA_START == null || mWH_AREA_START.AREA_TYPE.ToLower() ==  "xuniku"))
                {
                    bResult = this.ManageComplete(MANAGE_ID, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                sResult = string.Format("托盘{0}生成出库任务成功", mMANAGE_MAIN.STOCK_BARCODE);

                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                         List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                         bool bTrans,
                         bool bAutoSendControl,
                         bool bComplete, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int MANAGE_ID = 0;

            SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_START = null;

            if (mWH_CELL_START != null)
                mWH_AREA_START = this._P_WH_AREA.GetModel(mWH_CELL_START.AREA_ID);

            SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

            SiaSun.LMS.Model.WH_AREA mWH_AREA_END = null;

            if (mWH_CELL_END != null)
                mWH_AREA_END = this._P_WH_AREA.GetModel(mWH_CELL_END.AREA_ID);

            if (mWH_CELL_START != null
                && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                && (mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Have.ToString() || mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Pallet.ToString())
                && mWH_CELL_START.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
            {
                bResult = false;

                sResult = string.Format("起始位置{0}不可用", mWH_CELL_START.CELL_CODE);

                return bResult;
            }

            if (mWH_CELL_END != null
                && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString() || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString())
                && mWH_CELL_END.CELL_STATUS != Enum.CELL_STATUS.Nohave.ToString()
                && mWH_CELL_END.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
            {
                bResult = false;

                sResult = string.Format("终止位置{0}不可用", mWH_CELL_END.CELL_CODE);

                return bResult;
            }


            IList<SiaSun.LMS.Model.STORAGE_MAIN> lsSTORAGE_MAIN = null;

            if (mMANAGE_MAIN.STOCK_BARCODE == string.Empty)
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListCellID(mMANAGE_MAIN.START_CELL_ID);
            else
                lsSTORAGE_MAIN = this._P_STORAGE_MAIN.GetListStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);

            bResult = lsSTORAGE_MAIN.Count > 0;

            if (!bResult)
            {
                sResult = string.Format("未找到库存");

                return bResult;
            }

            //校验库存位置和任务位置是否相符 2022-04-18
            if (lsSTORAGE_MAIN.Any(t => t.CELL_ID != mWH_CELL_START.CELL_ID))
            {
                bResult = false;
                sResult = string.Format(@"条码[{0}]的库存位置与任务起点不相符", mMANAGE_MAIN.STOCK_BARCODE);
                return bResult;
            }


            if (this._P_MANAGE_MAIN.GetModelStockBarcode(lsSTORAGE_MAIN[0].STOCK_BARCODE) != null)
            {
                bResult = false;

                sResult = string.Format("托盘条码{0}已经存在任务", lsSTORAGE_MAIN[0].STOCK_BARCODE);

                return bResult;
            }

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                mMANAGE_MAIN.STOCK_BARCODE = lsSTORAGE_MAIN[0].STOCK_BARCODE;

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    //mMANAGE_LIST.PLAN_LIST_ID = 0;

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);

                    //wdz add 2018-04-29 库存有SN码则记录SN码
                    IList<Model.STORAGE_DETAIL> lsSTORAGE_DETAIL = this._P_STORAGE_DETAIL.GetList(mMANAGE_LIST.STORAGE_LIST_ID);
                    if (lsSTORAGE_DETAIL != null && lsSTORAGE_DETAIL.Count != 0)
                    {
                        foreach (Model.STORAGE_DETAIL itemSTORAGE_DETAIL in lsSTORAGE_DETAIL)
                        {
                            Model.MANAGE_DETAIL mMANAGE_DETAIL = new Model.MANAGE_DETAIL();
                            mMANAGE_DETAIL.GOODS_BARCODE = itemSTORAGE_DETAIL.GOODS_BARCODE;
                            mMANAGE_DETAIL.MANAGE_LIST_ID = mMANAGE_LIST.MANAGE_LIST_ID;
                            this._P_MANAGE_DETAIL.Add(mMANAGE_DETAIL);
                        }
                    }
                }

                if (mWH_CELL_START != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (mWH_CELL_END != null)
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                    if (!bResult)
                    {
                        sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                if (bAutoSendControl && mWH_AREA_START != null && mWH_AREA_START.AREA_TYPE.ToLower() == "liku")
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                if (bComplete && (mWH_AREA_START == null || mWH_AREA_START.AREA_TYPE.ToLower() == "xuniku"))
                {
                    bResult = this.ManageComplete(MANAGE_ID, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }

                sResult = string.Format("托盘{0}生成出库任务成功", mMANAGE_MAIN.STOCK_BARCODE);

                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }


        /// <summary>
        /// 根据物料ID及物料属性 生成批量下架任务
        /// 调用界面 MANAGE_STOCK_OUT.XAML
        /// 
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="GOODS_ID"></param>
        /// <param name="GOODS_PROPERTY"></param>
        /// <param name="QUANTITY"></param>
        /// <param name="WAREHOUSE_ID"></param>
        /// <param name="AREA_ID"></param>
        /// <param name="LOGIC_ID"></param>
        /// <param name="END_CELL_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ManageCreate(SiaSun.LMS.Model.SYS_USER mSYS_USER,
                                 string MANAGE_TYPE_CODE,
                                 int GOODS_ID,
                                 string GOODS_PROPERTY_CONDITION,
                                 decimal MANAGE_LIST_QUANTITY,
                                 int END_CELL_ID,
                                 bool bTrans,
                                 Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM,
                                 out string sResult)
        {
            bool bResult = false;

            sResult = string.Empty;

            SiaSun.LMS.Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(END_CELL_ID);

            bResult = null != mWH_CELL;

            if (!bResult)
            {
                sResult = "出库站台错误";

                return bResult;
            }

            try
            {

                DataTable dtLANEWAY = null;

                bResult = this._S_CellService.OutLaneWayGetList(END_CELL_ID, GOODS_ID, GOODS_PROPERTY_CONDITION, out dtLANEWAY, out sResult);

                if (!bResult)
                {
                    return bResult;
                }

                int iSumQuantity = 0;

                foreach (DataRow drLANEWAY in dtLANEWAY.Rows)
                {
                    iSumQuantity += Convert.ToInt32(drLANEWAY["STORAGE_BALANCE"]);
                }

                bResult = iSumQuantity >= MANAGE_LIST_QUANTITY;

                if (!bResult)
                {
                    sResult = string.Format("库存{0}小于需求{1},请重新输入数量!", iSumQuantity, MANAGE_LIST_QUANTITY);

                    return bResult;
                }

                this._P_Base_House.BeginTransaction(bTrans);

                decimal MANAGE_LIST_QUANTITY_FINISHED = 0;

                while (MANAGE_LIST_QUANTITY_FINISHED < MANAGE_LIST_QUANTITY)
                {
                    int START_CELL_ID = 0;

                    string STOCK_BARCODE = string.Empty;

                    decimal MANAGE_ORDERED_QUANTITY = 0;

                    DataView dvLANEWAY = dtLANEWAY.DefaultView;

                    dvLANEWAY.RowFilter = "STORAGE_BALANCE > 0";

                    dvLANEWAY.Sort = "OUT_TASK_BALANCE asc,STORAGE_BALANCE desc";

                    bResult = dvLANEWAY.Count > 0;

                    if (!bResult)
                    {
                        sResult = "库存数量不足";

                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }

                    int iLANEWAY = Convert.ToInt32(dvLANEWAY[0]["LANE_WAY"]);

                    bResult = this._S_CellService.CellOutAllocate(iLANEWAY, END_CELL_ID, GOODS_ID, GOODS_PROPERTY_CONDITION, MANAGE_LIST_QUANTITY - MANAGE_LIST_QUANTITY_FINISHED, out START_CELL_ID, out STOCK_BARCODE, out MANAGE_ORDERED_QUANTITY, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = MANAGE_TYPE_CODE;

                    mMANAGE_MAIN.PLAN_ID = 0;

                    mMANAGE_MAIN.STOCK_BARCODE = STOCK_BARCODE;

                    mMANAGE_MAIN.CELL_MODEL = string.Empty;

                    mMANAGE_MAIN.START_CELL_ID = START_CELL_ID;

                    mMANAGE_MAIN.END_CELL_ID = END_CELL_ID;

                    mMANAGE_MAIN.MANAGE_OPERATOR = mSYS_USER.USER_NAME;

                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();

                    mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelCellID(START_CELL_ID);

                    IList<SiaSun.LMS.Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);

                    List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();

                    foreach (SiaSun.LMS.Model.STORAGE_LIST mSTORAGE_LIST in lsSTORAGE_LIST)
                    {

                        Model.MANAGE_LIST mMANAGE_LIST = new SiaSun.LMS.Model.MANAGE_LIST();

                        mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                        mMANAGE_LIST.PLAN_LIST_ID = 0;

                        mMANAGE_LIST.STORAGE_LIST_ID = mSTORAGE_LIST.STORAGE_LIST_ID;

                        mMANAGE_LIST.GOODS_ID = mSTORAGE_LIST.GOODS_ID;

                        bResult = this._S_GoodsService.GoodsPropertySetValue(mSTORAGE_LIST.GOODS_ID, mMANAGE_LIST, mSTORAGE_LIST, out sResult);

                        if (!bResult)
                        {
                            return bResult;
                        }
                        mMANAGE_LIST.BOX_BARCODE = mSTORAGE_LIST.BOX_BARCODE;

                        mMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LIST.STORAGE_LIST_QUANTITY;

                        mMANAGE_LIST.MANAGE_LIST_REMARK = mSTORAGE_LIST.STORAGE_LIST_REMARK;

                        lsMANAGE_LIST.Add(mMANAGE_LIST);

                    }

                    bResult = this.ManageCreate(mMANAGE_MAIN,lsMANAGE_LIST,false,mMANAGE_TYPE_PARAM,out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }

                    dvLANEWAY[0].Row["STORAGE_BALANCE"] = Convert.ToDecimal(dvLANEWAY[0].Row["STORAGE_BALANCE"]) - MANAGE_ORDERED_QUANTITY;

                    if (dvLANEWAY.Count > 0)
                        dvLANEWAY[0].Row["OUT_TASK_BALANCE"] = Convert.ToDecimal(dvLANEWAY[0].Row["OUT_TASK_BALANCE"]) + 1;

                    MANAGE_LIST_QUANTITY_FINISHED += MANAGE_ORDERED_QUANTITY;
                }

                this._P_Base_House.CommitTransaction(bTrans);

            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        /// <summary>
        /// 完成
        /// </summary>
        public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (null == mMANAGE_MAIN)
            {
                bResult = false;
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
            if (mMANAGE_TYPE == null)
            {
                bResult = false;
                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                           || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());
                    return bResult;
                }

                if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());
                    return bResult;
                }

                bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID }, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("库存处理错误-{0}", sResult);
                    return bResult;
                }

                bResult = this.Invoke("StorageBase", "StorageDelete", new object[] { MANAGE_ID }, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("库存处理错误-{0}", sResult);
                    return bResult;
                }

                //接口调用
                string isNoticeWms = string.Empty;
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);
                    if (mPLAN_MAIN != null)
                    {
                        //紧急配料出库
                        if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanOutEmerg.ToString())
                        {
                            bResult = new Interface.urgentBoxReceiveFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_ID, out sResult);
                        }
                        //齐套箱出库
                        else if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanKitOut.ToString())
                        {
                            bResult = new Interface.neatBoxInfoReceiveFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_ID, out sResult);
                        }
                        //整理未满箱
                        else if (mPLAN_MAIN.PLAN_TYPE_CODE == Enum.PLAN_TYPE_CODE.PlanArrangeOut.ToString())
                        {
                            //wdz comment 2018-03-07
                            //bResult = new Interface.urgentBoxReceiveFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_ID, out sResult);
                        }


                        if (!bResult)
                        {
                            return bResult;
                        }
                    }
                }

                bResult = base.ManageComplete(MANAGE_ID, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }


    }
}
