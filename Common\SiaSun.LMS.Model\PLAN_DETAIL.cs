﻿/***************************************************************************
 * 
 *       功能：     计划列表明细实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// PLAN_DETAIL 
	/// </summary>
    [Serializable]
    [DataContract]
	public class PLAN_DETAIL
	{
		public PLAN_DETAIL()
		{
			
		}
		
		private int _plan_detail_id;
		private int _plan_list_id;
		private string _box_barcode;
		private string _goods_barcode;
		private string _plan_detail_remark;
		
		///<sumary>
		/// 计划明细编号
        ///</sumary>
        [DataMember]
		public int PLAN_DETAIL_ID
		{
			get{return _plan_detail_id;}
			set{_plan_detail_id = value;}
		}
		///<sumary>
		/// 计划列表编号
        ///</sumary>
        [DataMember]
		public int PLAN_LIST_ID
		{
			get{return _plan_list_id;}
			set{_plan_list_id = value;}
		}
		///<sumary>
		/// 箱条码
        ///</sumary>
        [DataMember]
		public string BOX_BARCODE
		{
			get{return _box_barcode;}
			set{_box_barcode = value;}
		}
		///<sumary>
		/// 物料条码
        ///</sumary>
        [DataMember]
		public string GOODS_BARCODE
		{
			get{return _goods_barcode;}
			set{_goods_barcode = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string PLAN_DETAIL_REMARK
		{
			get{return _plan_detail_remark;}
			set{_plan_detail_remark = value;}
		}
	}
}
