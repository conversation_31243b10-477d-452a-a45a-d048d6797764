﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    public class KitOutOutputPara: InterfaceBase.OutputPara
    {
        //齐套箱总箱数
        private string _kitBoxCount = "0";
        public string kitBoxCount
        {
            get { return _kitBoxCount; }
            set { _kitBoxCount = value; }
        }
    }

    /// <summary>
    /// 齐套箱出库任务下发
    /// </summary>
    public class KitBoxOutTask : InterfaceBase
    {       
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _uniqueCode = string.Empty;          //唯一码
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源
            private string _toLocation = string.Empty;          //终点位置
            private string _articleNumber = string.Empty;          //集货单号
            public List<FirstDetails> firstDetails { get; set; }     //一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 终点位置
            /// </summary>
            public string toLocation
            {
                get { return _toLocation; }
                set { _toLocation = value; }
            }

            public string articleNumber
            {
                get { return _articleNumber; }
                set { _articleNumber = value; }
            }
        }
        /// <summary>
        /// 入参_一级明细
        /// </summary>
        public class FirstDetails
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            int iPlanId = 0;
            KitOutOutputPara outputPara = new KitOutOutputPara();
            
            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("KitBoxOutTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.toLocation) || string.IsNullOrEmpty(taskInfo.uniqueCode) || taskInfo.firstDetails.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("KitBoxOutTask.NotifyMethod:入参存在空值");
                    return bResult;
                }

                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(taskInfo.toLocation);
                if (mWH_CELL_END == null)
                {
                    bResult = false;
                    outJson = string.Format("KitBoxOutTask.NotifyMethod:传入终点位置有误 传入值_{0}", taskInfo.toLocation);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModelPlanCode(taskInfo.uniqueCode, string.Empty, string.Empty);
                if (mPLAN_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("KitBoxOutTask.NotifyMethod:传入唯一码已存在计划 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                mPLAN_MAIN = new Model.PLAN_MAIN();
                mPLAN_MAIN.PLAN_CODE = taskInfo.uniqueCode;
                mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mPLAN_MAIN.PLAN_CREATE_TIME = Common.StringUtil.GetDateTime();
                mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mPLAN_MAIN.PLAN_INOUT_STATION = mWH_CELL_END.CELL_CODE;             
                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanKitOut.ToString();
                mPLAN_MAIN.BACKUP_FILED3 = taskInfo.articleNumber;
                string planLevel = string.Empty;
                mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("KitOutPlanLevel", out planLevel) ? planLevel : "0";

                List<Model.PLAN_LIST> lsPLAN_LIST = new List<Model.PLAN_LIST>();

                foreach (FirstDetails item in taskInfo.firstDetails)
                {
                    if (string.IsNullOrEmpty(item.projectNo) || string.IsNullOrEmpty(item.taskNo) || string.IsNullOrEmpty(item.wbsNo))
                    //if (string.IsNullOrEmpty(item.wbsNo))
                    {
                        bResult = false;
                        outJson = string.Format("KitBoxOutTask.NotifyMethod:入参存在空值");
                        return bResult;
                    }

                    Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                    mPLAN_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("kitBox").GOODS_ID;
                    mPLAN_LIST.PLAN_LIST_QUANTITY = 1;
                    mPLAN_LIST.GOODS_PROPERTY6 = item.taskNo;
                    mPLAN_LIST.GOODS_PROPERTY7 = item.projectNo;
                    mPLAN_LIST.GOODS_PROPERTY8 = item.wbsNo;
                    lsPLAN_LIST.Add(mPLAN_LIST);
                }

                object[] invokeOutParams = new object[] { };
                bResult = this.Invoke("PlanBase", "PlanCreate", new object[] { mPLAN_MAIN, lsPLAN_LIST, iPlanId, outJson }, out invokeOutParams);
                int.TryParse(invokeOutParams[2].ToString(), out iPlanId);
                if (!bResult)
                {
                    outJson = invokeOutParams[3].ToString();
                    return bResult;
                }

                bResult = this.Invoke("PlanBase", "PlanOutDownLoad", new object[] { iPlanId }, out outJson);
                
                bool planCancel = false;
                //齐套箱出库成功后判断是否所有的计划单都被取消（所要物料包含在其他齐套出库中）
                if (bResult)
                {
                    //如果齐套箱出库计划单都已删除则取消计划，但不影响最终的bResult，既给WMS报成功
                    IList<Model.PLAN_LIST> lsPLAN_LIST_VERIFY = this._P_PLAN_LIST.GetListPlanID(iPlanId);
                    if (lsPLAN_LIST_VERIFY.Count < 1)
                    {
                        planCancel = true;
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("KitBoxOutTask.NotifyMethod():计划生成成功_执行计划失败_计划所需出库的齐套物料均包含在其他相同出库位置的齐套出库任务中_准备删除计划_计划ID[{0}]_唯一码[{1}]", iPlanId, taskInfo.uniqueCode));
                    }

                    //计算齐套箱数量
                    DataTable dtBoxCount = this.GetList(string.Format("select count(distinct STOCK_BARCODE) as BOX_COUNT from MANAGE_MAIN where PLAN_ID ={0} ", iPlanId));
                    if (dtBoxCount != null && dtBoxCount.Rows.Count != 0)
                    {
                        int boxCountTemp = 0;
                        int.TryParse(dtBoxCount.Rows[0]["BOX_COUNT"].ToString(), out boxCountTemp);
                        outputPara.kitBoxCount = boxCountTemp.ToString();
                    }
                }

                if (!bResult || planCancel)
                {
                    string strTemp = string.Empty;
                    if (!this.Invoke("PlanBase", "PlanCancel", new object[] { iPlanId, false }, out strTemp))
                    {
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("KitBoxOutTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划失败_{1}_计划ID[{2}]", outJson, strTemp, iPlanId));
                    }
                    else
                    {
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("KitBoxOutTask.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划成功_计划ID[{1}]", outJson, iPlanId));
                    }

                    outJson = string.Format("KitBoxOutTask.NotifyMethod:执行计划失败 {0}", outJson);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("KitBoxOutTask.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.kitBoxCount = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
