﻿//#define DEPLOY
using System;
using System.Collections.Generic;
using System.Linq;
using System.ServiceProcess;
using System.Text;
using System.Reflection;
using Quartz;
using Quartz.Impl;


namespace SiaSun.LMS.WinService
{
    static class Program
    {
        public static string _HouseUrl = string.Empty;
        public static string _InterfaceUrl = string.Empty;
        public static log4net.ILog sysLog = log4net.LogManager.GetLogger("WinServiceLog");

        static ISchedulerFactory schedulerFactory = new StdSchedulerFactory();
        static IScheduler scheduler;

        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        static void Main()
        {
#if DEPLOY
            ServiceBase[] ServicesToRun;
            
            ServicesToRun = new ServiceBase[] 
			{ 
				new WMSService() 
			};
            ServiceBase.Run(ServicesToRun);
#else 

            try
            {
                //获取系统参数
                //MainApp._SysParameter = MainApp.BaseService._S_SystemService.GetSysParameter("1");

                Program._HouseUrl = SiaSun.LMS.Common.StringUtil.GetConfig("SiaSunSrvUrl");
                Program._InterfaceUrl = SiaSun.LMS.Common.StringUtil.GetConfig("InterfaceUrl");

                ServiceHostGroup.StartAllConfiguredServices("House",_HouseUrl);
                ServiceHostGroup.StartAllConfiguredServices("All", _InterfaceUrl);

                scheduler = schedulerFactory.GetScheduler();
                scheduler.Start();

                //临时注释 2020-09-23 10:42:25
                //SiaSun.LMS.Implement.TCP.CommunicationOperation.Launch();
                //SiaSun.LMS.Implement.DZBQ.DZBQOpration.Connect();
                //SiaSun.LMS.Implement.YiChuanPtlDevice.PtlDeviceCommOperation.Connect();

                while (true)
                {
                    System.Threading.Thread.Sleep(2000);
                }
            }
            catch (Exception ex)
            {
                sysLog.Fatal("系统异常", ex);
            }
#endif
        }
    }
}
