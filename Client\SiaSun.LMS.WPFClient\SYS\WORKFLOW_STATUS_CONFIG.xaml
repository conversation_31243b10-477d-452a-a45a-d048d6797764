﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.WORKFLOW_STATUS_CONFIG"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="WORKFLOW_STATUS_CONFIG" Height="300" Width="300" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        
        <Border Grid.Row="0" Margin="1,5,1,5">
            <WrapPanel>
                <TextBlock Text="配置流程类型:" Margin="5"></TextBlock>
                <ComboBox Name="cmbNodeType"  Margin="5" Width="120" SelectionChanged="cmbNodeType_SelectionChanged"></ComboBox>
            </WrapPanel>
        </Border>
        <uc:ucMdiDataGridControl x:Name="ucMdiGridFlowActions" Grid.Row="1" Margin="1"></uc:ucMdiDataGridControl>
    </Grid>
</ad:DocumentContent>
