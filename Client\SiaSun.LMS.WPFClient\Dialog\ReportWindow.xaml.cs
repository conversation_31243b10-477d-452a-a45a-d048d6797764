﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.IO;
using System.Data;
using System.Windows.Xps.Packaging;
using CodeReason.Reports;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// ReportWindow.xaml 的交互逻辑
    /// </summary>
    public partial class ReportWindow : Window
    {
        private string strTitle,strWindowName, strTableName;
        private DataTable tableReportSource = null;

        /// <summary>
        /// 构造函数
        /// </summary>
        public ReportWindow(string Title,string WindowName,string TableName,System.Data.DataTable DataSource)
        {
            InitializeComponent();

            this.strTitle = Title;
            this.strWindowName = WindowName;
            this.strTableName = TableName;
            this.tableReportSource = DataSource;
        }

        public ReportWindow(string Title, string WindowName, string TableName, string Where)
        {
            InitializeComponent();

            this.strTitle = Title;
            this.strWindowName = WindowName;
            this.strTableName = TableName;

            try
            {
                this.tableReportSource = MainApp._I_BaseService.GetList(string.Format("SELECT * FROM {0} WHERE {1}", strTableName, (string.IsNullOrEmpty(Where) ? "1=1" : Where)));
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex.Message);
                this.Close();
            }
        }

        /// <summary>
        /// 加载窗体
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                //加载报表文档
                ReportDocument reportDocument = this.LoadReportDocument();
                reportDocument.ReportTitle = string.Format("{0}    制单日期:{1}", strTitle, SiaSun.LMS.Common.StringUtil.GetDate());

                //加载报表
                this.LoadReport(reportDocument);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex.Message);
                this.Close();
            }
        }
        
        /// <summary>
        /// 加载报表文档
        /// </summary>
        private ReportDocument LoadReportDocument()
        {
            ReportDocument reportDocument = new ReportDocument();
            try
            {
                using (StreamReader reader = new StreamReader(new FileStream(MainApp._APP_PATH + @"@Templates\DynamicReport.xaml", FileMode.Open, FileAccess.Read)))
                {
                    reportDocument.XamlData = reader.ReadToEnd();
                    reportDocument.XamlImagePath = System.IO.Path.Combine(Environment.CurrentDirectory, @"@Templates\");
                    reader.Close();
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex.Message);
            }
            return reportDocument;
        }
        
        /// <summary>
        /// 获得报表数据
        /// </summary>
        private void LoadReport(ReportDocument reportDocument)
        {
            ReportData data = new ReportData();

            DataTable tableHeader = new DataTable("Header");
            tableHeader.Columns.Add();

            DataTable tableReport = new CustomerDescriptions().GetReportDataTable(this.tableReportSource, this.strWindowName, this.strTableName, ref tableHeader);
            tableReport.TableName = "Data";

            data.DataTables.Add(tableReport);
            data.DataTables.Add(tableHeader);

            //设置报表数据
            XpsDocument xps = reportDocument.CreateXpsDocument(data);
            documentViewer.Document = xps.GetFixedDocumentSequence();
        }
    }
}
