﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.INTERFACE_TEST"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="INTERFACE_TEST" Height="600" Width="800"  Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        
        <TextBlock Grid.Row="0" Margin="10">模拟接口操作</TextBlock>

        <WrapPanel Grid.Row="1" Orientation="Vertical">
            <StackPanel Orientation="Horizontal">
                <Label  Margin="10"  Content="接口："></Label>
                <ComboBox x:Name="cbxInterfaceName"  Margin="10"  Width="150">
                </ComboBox>
                <Button x:Name="btnInvoke" Content="调用"  Margin="10"   Width="50" Click="btnInvoke_Click"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal" VerticalAlignment="Top">
                <Label  Margin="10"  Content="入参："></Label>
                <TextBox  Margin="10"  x:Name="tbxJson" Width="700" Height="200"></TextBox>
            </StackPanel>
        </WrapPanel>





        <!--<TabControl Grid.Row="1" >
            <TabItem x:Name="tabHandleResultReturnFromWCS" Header="执行结果回传">
                <Grid Background="#FFE5E5E5">
                </Grid>
            </TabItem>
            <TabItem x:Name="tabCountResultReturnFromWCS" Header="盘点结果回传">
                <Grid Background="#FFE5E5E5"/>
            </TabItem>
            <TabItem x:Name="tabMiniloadPickConfirmFromWCS" Header="Miniload拣货回传">
                <Grid Background="#FFE5E5E5"/>
            </TabItem>
            <TabItem x:Name="tabNeatBoxInfoReceiveFromWCS" Header="齐套箱出库信息回传">
                <Grid Background="#FFE5E5E5"/>
            </TabItem>
            <TabItem x:Name="tabUrgentBoxReceiveFromWCS" Header="紧急配料出库信息回传">
                <Grid Background="#FFE5E5E5"/>
            </TabItem>
            <TabItem x:Name="tabNoneMiniloadHandleResultReturnFromWCS" Header="非立库输送任务结果回传">
                <Grid Background="#FFE5E5E5"/>
            </TabItem>
        </TabControl>-->

        <!--<WrapPanel Grid.Row="1"  Orientation="Vertical" ButtonBase.Click="WrapPanel_Click">
            <StackPanel Orientation="Horizontal">
                <Button x:Name="countResultReturnFromWCS" Content="盘点结果回传接口"  Margin="10"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <Button x:Name="handleResultReturnFromWCS" Content="执行结果回传接口"  Margin="10"/>
            </StackPanel> 
            <StackPanel Orientation="Horizontal">
                <Button x:Name="miniloadPickConfirmFromWCS" Content="Miniload拣货确认回传接口"  Margin="10"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <Button x:Name="neatBoxInfoReceiveFromWCS" Content="齐套箱出库箱号信息回传接口"  Margin="10"/>
            </StackPanel>
            <StackPanel Orientation="Horizontal">
                <Button x:Name="urgentBoxReceiveFromWCS" Content="紧急配料出库箱号信息回传接口"  Margin="10"/>
            </StackPanel>
        </WrapPanel>-->
    </Grid>
</ad:DocumentContent>
