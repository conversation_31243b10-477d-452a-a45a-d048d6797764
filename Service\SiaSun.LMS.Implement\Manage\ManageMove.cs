﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    public class ManageMove:ManageBase
    {
        /// <summary>
        /// 移库任务 任务完成生成库存
        /// </summary>
        public bool ManageCreate(Model.MANAGE_MAIN mMANAGE_MAIN, 
            List<Model.MANAGE_LIST> lsMANAGE_LIST,
            bool bTrans, 
            Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM, 
            out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                if (mMANAGE_TYPE == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    return bResult;
                }

                if (this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    bResult = false;
                    sResult = string.Format("托盘条码{0}已经存在任务", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                if (mWH_CELL_START != null 
                    && (mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Have.ToString() || mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Pallet.ToString())
                    && mWH_CELL_START.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    bResult = false;
                    sResult = string.Format("起始位置{0}不可用", mWH_CELL_START.CELL_CODE);
                    return bResult;
                }

                if (mWH_CELL_END != null && mWH_CELL_END.CELL_STATUS != Enum.CELL_STATUS.Nohave.ToString()
                    && mWH_CELL_END.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    bResult = false;
                    sResult = string.Format("终止位置{0}不可用", mWH_CELL_END.CELL_CODE);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction(bTrans);

                //校验库存位置和任务位置是否相符 2022-04-18
                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format(@"未找到条码[{0}]的库存信息", mMANAGE_MAIN.STOCK_BARCODE);
                    this._P_Base_House.RollBackTransaction(bTrans);
                    return bResult;
                }
                if (mSTORAGE_MAIN.CELL_ID != mWH_CELL_START.CELL_ID)
                {
                    bResult = false;
                    sResult = string.Format(@"条码[{0}]的库存位置ID[{1}]与任务起点ID[{2}]不相符", mMANAGE_MAIN.STOCK_BARCODE, mSTORAGE_MAIN.CELL_ID, mWH_CELL_START.CELL_ID);
                    this._P_Base_House.RollBackTransaction(bTrans);
                    return bResult;
                }

                if (mMANAGE_MAIN.END_CELL_ID == 0)
                {
                    if (mSTORAGE_MAIN != null)
                    {
                        mMANAGE_MAIN.END_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    }
                }
                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }

                if (mMANAGE_TYPE_PARAM.U_AutoCompleteTask)
                {
                    bResult = _S_ManageService.Invoke(
                                                     mMANAGE_TYPE.MANAGE_TYPE_CLASS,
                                                     "ManageComplete",
                                                     new object[] { mMANAGE_MAIN.MANAGE_ID, false },
                                                     out sResult
                                                     );
                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }
                else
                {
                    if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                                   || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);
                            this._P_Base_House.RollBackTransaction(bTrans);
                            return bResult;
                        }
                    }

                    if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                                 || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);
                            this._P_Base_House.RollBackTransaction(bTrans);
                            return bResult;
                        }
                    }
                }

                if (mMANAGE_TYPE_PARAM.U_AutoDownloadControlTask)
                {
                    bResult = this.ManageDownLoad(mMANAGE_MAIN.MANAGE_ID, string.Empty, false, out sResult);
                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }
                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);
                bResult = false;
                sResult = ex.Message;
            }

            return bResult;
        }

        public bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                         List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                         bool bTrans,
                         bool bCheckStorage,
                         bool bComplete,
                         bool bAutoSendControl,
                         out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            int MANAGE_ID = 0;

            try
            {
                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                if (mMANAGE_TYPE == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);
                    return bResult;
                }

                if (this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    bResult = false;
                    sResult = string.Format("托盘条码{0}已经存在任务", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);
                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Have.ToString() || mWH_CELL_START.CELL_STATUS != Enum.CELL_STATUS.Pallet.ToString())
                                           && mWH_CELL_START.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    bResult = false;
                    sResult = string.Format("起始位置{0}不可用", mWH_CELL_START.CELL_CODE);
                    return bResult;
                }

                if (mWH_CELL_END != null && mWH_CELL_END.CELL_STATUS != Enum.CELL_STATUS.Nohave.ToString()
                           && mWH_CELL_END.RUN_STATUS != Enum.RUN_STATUS.Enable.ToString())
                {
                    bResult = false;
                    sResult = string.Format("终止位置{0}不可用", mWH_CELL_END.CELL_CODE);
                    return bResult;
                }

                //xcjt add 2017-01-04
                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == "ManageMove" && mWH_CELL_START.CELL_MODEL == "Height" && mWH_CELL_END.CELL_MODEL == "Height%")
                {
                    bResult = false;
                    sResult = string.Format("不能由高货位倒库到标准货位");
                    return bResult;
                }

                //校验库存位置和任务位置是否相符 2022-04-18
                SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE);
                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format(@"未找到条码[{0}]的库存信息", mMANAGE_MAIN.STOCK_BARCODE);
                    return bResult;
                }
                if (mSTORAGE_MAIN.CELL_ID != mWH_CELL_START.CELL_ID)
                {
                    bResult = false;
                    sResult = string.Format(@"条码[{0}]的库存位置ID[{1}]与任务起点ID[{2}]不相符", mMANAGE_MAIN.STOCK_BARCODE, mSTORAGE_MAIN.CELL_ID, mWH_CELL_START.CELL_ID);
                    return bResult;
                }

                if (mMANAGE_MAIN.END_CELL_ID == 0)
                {
                    if (mSTORAGE_MAIN != null)
                    {
                        mMANAGE_MAIN.END_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                    }
                }

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;
                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }

                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                this._P_Base_House.BeginTransaction(bTrans);


                if (bComplete)
                {
                    bResult = _S_ManageService.Invoke(
                                                     mMANAGE_TYPE.MANAGE_TYPE_CLASS,
                                                     "ManageComplete",
                                                     new object[] { MANAGE_ID, false },
                                                     out sResult
                                                     );
                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }
                else
                {
                    if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                                   || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);
                            this._P_Base_House.RollBackTransaction(bTrans);
                            return bResult;
                        }
                    }

                    if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                                 || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);
                            this._P_Base_House.RollBackTransaction(bTrans);
                            return bResult;
                        }
                    }
                }

                if (bAutoSendControl)
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);
                        return bResult;
                    }
                }
                this._P_Base_House.CommitTransaction(bTrans);
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }


        /// <summary>
        /// 完成
        /// </summary>
        public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            bResult = null != mMANAGE_MAIN;

            if (!bResult)
            {
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());

                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

            if (mMANAGE_TYPE == null)
            {
                bResult = false;

                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                return bResult;
            }

            try
            {
                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                                           || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                   mWH_CELL_END != null ? mWH_CELL_END.CELL_STATUS: SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }

                if (!bResult)
                {
                    sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());

                    return bResult;
                }
                if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                   mWH_CELL_START != null ? mWH_CELL_START.CELL_STATUS: SiaSun.LMS.Enum.CELL_STATUS.Full.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());

                    return bResult;
                }
                else if (mMANAGE_MAIN.MANAGE_OPERATOR != "自动倒库")
                {
                    //手动倒库更新货位组状态(终点货位组可用货位-1)
                    string wh_cell_group_code = mWH_CELL_END.CELL_CODE.Substring(3);
                    WH_CELL_GROUP mWH_CELL_GROUP = _P_WH_CELL_GROUP.GetModelByGroupCode(wh_cell_group_code);
                    if (mWH_CELL_GROUP == null)
                    {
                        bResult = false;
                        sResult = $"未找到{mWH_CELL_END.CELL_CODE}所在货位组{wh_cell_group_code}";
                        return bResult;
                    }
                    mWH_CELL_GROUP.AVAIL_LOC_NUM -= 1;
                    _P_WH_CELL_GROUP.Update(mWH_CELL_GROUP);
                }

                //xcjt alter 2017-01-04
                //bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { mMANAGE_MAIN.MANAGE_ID }, out sResult);
                if (mMANAGE_MAIN.MANAGE_TYPE_CODE == "ManageMerge")
                {
                    bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { mMANAGE_MAIN.MANAGE_ID }, out sResult);
                }
                else
                {
                    bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageMove", new object[] { mMANAGE_MAIN.STOCK_BARCODE, mMANAGE_MAIN.START_CELL_ID, mMANAGE_MAIN.END_CELL_ID }, out sResult);
                }

                if (!bResult)
                {
                    sResult = string.Format("库存处理错误-{0}", sResult);

                    return bResult;
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);

            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            return bResult;
        }
    }
}
