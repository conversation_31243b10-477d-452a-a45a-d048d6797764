﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
	using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// LED_LIST
	/// </summary>
	[Serializable]
    [DataContract]
	public class LED_LIST
	{
        public LED_LIST()
		{
		}

        private int _led_list_id;
        private int _led_id;
        private int _line_no;
        private int _area_x;
        private int _area_y;
        private int _area_width;
        private int _area_height;
        private string _file_name;
        private string _line_text;
        private int _font_size;
        private int _show_stunt;
        private int _run_speed;
        private int _show_time;
        private string _led_list_remark;
        private string _led_list_para1;
        private string _led_list_para2;
        private string _led_list_para3;
        private string _led_list_para4;
        private string _led_list_para5;

        ///<sumary>
        /// 
        ///</sumary>
        [DataMember]
        public int LED_LIST_ID
        {
            get { return _led_list_id; }
            set { _led_list_id = value; }
        }

		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
        public int LED_ID
		{
            get { return _led_id; }
            set { _led_id = value; }
		}

        ///<sumary>
        /// 行号（图文区号，从0开始）
        ///</sumary>
        [DataMember]
        public int LINE_NO
        {
            get { return _line_no; }
            set { _line_no = value; }
        }

        ///<sumary>
        /// 图文区横坐标，显示屏左上角横坐标，最小为0
        ///</sumary>
        [DataMember]
        public int AREA_X
        {
            get { return _area_x; }
            set { _area_x = value; }
        }

        ///<sumary>
        /// 图文区纵坐标，显示屏左上角纵坐标，最小为0
        ///</sumary>
        [DataMember]
        public int AREA_Y
        {
            get { return _area_y; }
            set { _area_y = value; }
        }

        ///<sumary>
        /// 图文区宽度,不大于屏幕宽度
        ///</sumary>
        [DataMember]
        public int AREA_WIDTH
        {
            get { return _area_width; }
            set { _area_width = value; }
        }

        ///<sumary>
        /// 图文区高度,不大于屏幕高度
        ///</sumary>
        [DataMember]
        public int AREA_HEIGHT
        {
            get { return _area_height; }
            set { _area_height = value; }
        }

        ///<sumary>
        /// 每一行对应的txt文件
        ///</sumary>
        [DataMember]
        public string FILE_NAME
        {
            get { return _file_name; }
            set { _file_name = value; }
        }

        ///<sumary>
        /// 每一行发送的内容
        ///</sumary>
        [DataMember]
        public string LINE_TEXT
        {
            get { return _line_text; }
            set { _line_text = value; }
        }

        ///<sumary>
        /// 每一行的字体（图文区高度为16,对应字号为10）
        ///</sumary>
        [DataMember]
        public int FONT_SIZE
        {
            get { return _font_size; }
            set { _font_size = value; }
        }

        ///<sumary>
        /// 显示特技，4向左连移
        ///</sumary>
        [DataMember]
        public int SHOW_STUNT
        {
            get { return _show_stunt; }
            set { _show_stunt = value; }
        }

        ///<sumary>
        /// 速度，8
        ///</sumary>
        [DataMember]
        public int RUN_SPEED
        {
            get { return _run_speed; }
            set { _run_speed = value; }
        }

        ///<sumary>
        /// 停留时间，单位0.5s
        ///</sumary>
        [DataMember]
        public int SHOW_TIME
        {
            get { return _show_time; }
            set { _show_time = value; }
        }


        ///<sumary>
        /// 备注
        ///</sumary>
        [DataMember]
        public string LED_LIST_REMARK
        {
            get { return _led_list_remark; }
            set { _led_list_remark = value; }
        }

        ///<sumary>
        /// 参数1
        ///</sumary>
        [DataMember]
        public string LED_LIST_PARA1
        {
            get { return _led_list_para1; }
            set { _led_list_para1 = value; }
        }

        ///<sumary>
        /// 参数2
        ///</sumary>
        [DataMember]
        public string LED_LIST_PARA2
        {
            get { return _led_list_para2; }
            set { _led_list_para2 = value; }
        }

        ///<sumary>
        /// 参数3
        ///</sumary>
        [DataMember]
        public string LED_LIST_PARA3
        {
            get { return _led_list_para3; }
            set { _led_list_para3 = value; }
        }

        ///<sumary>
        /// 参数4
        ///</sumary>
        [DataMember]
        public string LED_LIST_PARA4
        {
            get { return _led_list_para4; }
            set { _led_list_para4 = value; }
        }

        ///<sumary>
        /// 参数5
        ///</sumary>
        [DataMember]
        public string LED_LIST_PARA5
        {
            get { return _led_list_para5; }
            set { _led_list_para5 = value; }
        }
		
	}
}
