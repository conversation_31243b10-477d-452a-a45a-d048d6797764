﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    /// <summary>
    /// 拣选任务
    /// </summary>
    public class ManagePick : ManageBase
    {
        /// <summary>
        /// 拣选工作站相关
        /// 根据拣选工作站与锁定的库存
        /// 生成拣选任务
        /// 20180125
        /// done
        /// </summary>
        /// <param name="mSTORAGE_LOCK">锁定库存</param>
        /// <param name="bTrans">是否开启新事务</param>
        /// <param name="bAutoComplete">是否自动完成</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool ManageCreate(Model.T_PICK_STATION mT_PICK_STATION, Model.STORAGE_LOCK mSTORAGE_LOCK,
                                 bool bTrans,
                                 bool bAutoComplete,
                                 out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);
                
                Model.STORAGE_LIST mSTORAGE_LIST = this._P_STORAGE_LIST.GetModel(mSTORAGE_LOCK.STORAGE_LIST_ID);

                if (mSTORAGE_LIST == null)
                {
                    bResult = false;
                    sResult = String.Format("未找到锁定库存{0}对应的库存{1}", mSTORAGE_LOCK.STORAGE_LOCK_ID,mSTORAGE_LOCK.STORAGE_LIST_ID);
                    return bResult;
                }

                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModel(mSTORAGE_LIST.STORAGE_ID);

                if (mSTORAGE_MAIN==null)
                {
                    bResult = false;
                    sResult = String.Format("未找到库存{0}对应的库存主表{1}", mSTORAGE_LIST.STORAGE_LIST_ID, mSTORAGE_LIST.STORAGE_ID);
                    return bResult;
                }

                Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mSTORAGE_LOCK.PLAN_LIST_ID);

                if (mPLAN_LIST == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到锁定库存ID{0}对应的拣选计划明细表ID:{1}", mSTORAGE_LOCK.STORAGE_LOCK_ID, mSTORAGE_LOCK.PLAN_LIST_ID);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mPLAN_LIST.PLAN_ID);

                if (mPLAN_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到锁定库存ID{0}对应的拣选计划主表ID:{1}",mSTORAGE_LOCK.STORAGE_LOCK_ID, mPLAN_LIST.PLAN_ID);
                    return bResult;
                }
                /*
                 * 通过拣选的锁定库存，查询到其计划绑定的拣选工作点
                 * 通过拣选工作点可以查询到拣选工作点与WBS计划的绑定关系
                 */
                Model.T_PICK_POSITION mT_PICK_POSITION = this._P_T_PICK_POSITION.GetModelByPLAN_LIST_ID(mSTORAGE_LOCK.PLAN_LIST_ID);

                if (mT_PICK_POSITION == null)
                {
                    bResult = false;
                    sResult = String.Format("未找到拣选点信息");
                    return bResult;
                }

                //Model.T_PICK_POSITION_PLAN_BIND mT_PICK_POSITION_PLAN_BIND = this._P_T_PICK_POSITION_PLAN_BIND.GetModel_By_PICK_POSITION_ID_FLAG(mT_PICK_POSITION.POSITION_ID,"1");
                Model.T_PICK_POSITION_PLAN_BIND mT_PICK_POSITION_PLAN_BIND = 
                    this._P_T_PICK_POSITION_PLAN_BIND.GetModel_By_PICK_POSITION_ID_PLAN_LIST_ID_FLAG(
                        mT_PICK_POSITION.POSITION_ID,mSTORAGE_LOCK.PLAN_LIST_ID, "1");

                if (mT_PICK_POSITION_PLAN_BIND==null)
                {
                    bResult = false;
                    sResult = String.Format("未找到拣选点与WBS计划绑定的信息");
                    return bResult;
                }
                /*
                 * 生成拣选任务前，需要确保锁定库存没有生成过拣选任务
                 * 如果生成过，就不能再生成
                 */
                List<Model.MANAGE_LIST> checkMANAGE_LISTS = this._P_MANAGE_LIST.GetListBySTORAGE_LOCK_ID(mSTORAGE_LOCK.STORAGE_LOCK_ID).ToList();

                if (checkMANAGE_LISTS.Count > 0)
                {
                    bResult = false;
                    sResult = string.Format("此锁定库存已经生成了拣选任务，无法再生成拣选任务");
                    return bResult;
                }
                /*
                 * 开始创建拣选任务
                 * 根据锁定库存 库存信息 组装拣选任务
                 */

                Model.MANAGE_MAIN pMANAGE_MAIN = new Model.MANAGE_MAIN();

                pMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                pMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;

                pMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                pMANAGE_MAIN.MANAGE_LEVEL = "1";
                pMANAGE_MAIN.MANAGE_TYPE_CODE = "ManagePick";
                pMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingSend.ToString();
                pMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                Model.SYS_USER mSYS_USER = this._P_SYS_USER.GetModel(mT_PICK_POSITION_PLAN_BIND.USER_ID);

                if (mSYS_USER != null)
                {
                    pMANAGE_MAIN.MANAGE_OPERATOR = mSYS_USER.USER_NAME;
                }
                else
                {
                    pMANAGE_MAIN.MANAGE_OPERATOR = mT_PICK_POSITION_PLAN_BIND.USER_ID.ToString();
                }

                pMANAGE_MAIN.PLAN_ID = mT_PICK_POSITION_PLAN_BIND.PLAN_ID;
                //拣选任务起点是拣选工作站对应的站台
                pMANAGE_MAIN.START_CELL_ID = mT_PICK_STATION.WH_CELL_ID;
                //拣选任务目的点是拣选点的对应站台
                pMANAGE_MAIN.END_CELL_ID = mT_PICK_POSITION.WH_CELL_ID;
                pMANAGE_MAIN.MANAGE_REMARK = string.Format("拣选工作站拣选任务");

                this._P_MANAGE_MAIN.Add(pMANAGE_MAIN);

                Model.MANAGE_LIST pMANAGE_LIST = new Model.MANAGE_LIST();

                pMANAGE_LIST =new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(mSTORAGE_LIST, pMANAGE_LIST, null);

                pMANAGE_LIST.MANAGE_ID = pMANAGE_MAIN.MANAGE_ID;
                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(mSTORAGE_LIST.GOODS_ID);
                if (mGOODS_MAIN == null)
                {
                    sResult = string.Format("物料ID:{0}未找到", mSTORAGE_LIST.GOODS_ID);
                    bResult = false;
                    return bResult;
                }

                //表示是否是序列号拣选
                pMANAGE_LIST.DETAIL_FLAG = Common.StringUtil.GetConfig("ForbidSNPick") == "1" ? "0" : mGOODS_MAIN.GOODS_CONST_PROPERTY2;//0 非序列号拣选 1序列号拣选
                //锁定库存的锁定数量即时生成的拣选任务数量
                pMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LOCK.STORAGE_LOCK_QUANTITY;
                //拣选任务要与锁定库存绑定，以确保拣选任务完成时，删除锁定库存
                pMANAGE_LIST.STORAGE_LOCK_ID = mSTORAGE_LOCK.STORAGE_LOCK_ID;
                //拣选任务应该与拣选计划绑定
                pMANAGE_LIST.PLAN_LIST_ID = mSTORAGE_LOCK.PLAN_LIST_ID;

                //拣选完成时，根据任务明细生成齐套箱库存
                pMANAGE_LIST.GOODS_PROPERTY6 = mPLAN_MAIN.PLAN_RELATIVE_CODE;
                pMANAGE_LIST.GOODS_PROPERTY7 = mPLAN_MAIN.PLAN_PROJECT_CODE;
                pMANAGE_LIST.GOODS_PROPERTY8 = mPLAN_MAIN.PLAN_CODE;


                this._P_MANAGE_LIST.Add(pMANAGE_LIST);
                //已经生成了拣选任务的锁定库存，需要置位
                mSTORAGE_LOCK.STORAGE_LOCK_FLAG = Enum.FLAG.Enable.ToString("d");
                this._P_STORAGE_LOCK.Update(mSTORAGE_LOCK);
            }
            catch (Exception ex)
            {
               
                bResult = false;

                sResult = ex.Message+"EX201";
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult,bTrans);
            }
            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 拣选任务完成方法
        /// 通常由拣选工作点上的电子标签按钮触发
        /// done
        /// </summary>
        /// <param name="mT_PICK_POSITION">拣选工作点实例</param>
        /// <param name="MANAGE_ID">拣选任务ID</param>
        /// <param name="bTrans">是否开启事务</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool ManageComplete(Model.T_PICK_POSITION mT_PICK_POSITION,int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;



            Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (mMANAGE_MAIN == null)
            {
                bResult = false;
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                return bResult;
            }
            List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

            if(MANAGE_LISTS.Count!=1)
            {
                bResult = false;
                sResult = string.Format("任务ID{0}对应的任务明细不符合规定", MANAGE_ID.ToString());
                return bResult;
            }

            /*
             * 判断是否是A类物料序列号拣选
             */
            List<Model.MANAGE_DETAIL> MANAGE_DETAILS=null;
            if (MANAGE_LISTS[0].DETAIL_FLAG == "1")
            {
                MANAGE_DETAILS = this._P_MANAGE_DETAIL.GetListManageListID(MANAGE_LISTS[0].MANAGE_LIST_ID).ToList();

                if (MANAGE_DETAILS == null)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务是A类物料拣选，需要扫描序列号进行拣选", MANAGE_ID.ToString());
                    return bResult;
                }

                if (MANAGE_DETAILS.Count != MANAGE_LISTS[0].MANAGE_LIST_QUANTITY)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务是A类物料拣选，需要扫描序列号进行拣选,扫描的序列号数量与拣选数量不相等", MANAGE_ID.ToString());
                    return bResult;
                }
            }


            try
            {
                /*
                 * 拣选任务完成
                 */
                this._P_Base_House.BeginTransaction(bTrans);


                Storage.List mList = new Storage.List();

                bResult = mList.Pick(mMANAGE_MAIN, MANAGE_LISTS, MANAGE_DETAILS, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("拣选任务ID:{0}完成时，库存处理异常!,EX901,异常信息:{1}",MANAGE_ID.ToString(),sResult);
                    return bResult;
                }

                //更新计划已拣选数量
                foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
                {
                    Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                    if (mPLAN_LIST != null)
                    {
                        mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                        //20180528
                        //保证拣选工作站拣选任务完成效果
                        if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                        {
                            bResult = false;
                            sResult = string.Format("拣选任务ID:{0}完成时，拣选计划单ID:{1}拣选数量:{2}将大于分配数量:{3}",MANAGE_ID,mPLAN_LIST.PLAN_LIST_ID,mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY,mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY);
                            return bResult;
                        }
                        this._P_PLAN_LIST.Update(mPLAN_LIST);
                    }
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
                //testing
                bResult = new Plan.PlanPick().ContinueLockAndOut(mT_PICK_POSITION, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManagePick完成时发生异常 {0},EX202", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }
            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 拣选任务完成方法
        /// 通常由拣选工作点上的电子标签按钮触发
        /// TESTING
        /// 20180731
        /// </summary>
        /// <param name="mT_PICK_POSITION">拣选工作点实例</param>
        /// <param name="MANAGE_ID">拣选任务ID</param>
        /// <param name="bTrans">是否开启事务</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool ManageComplete(Model.T_PICK_POSITION mT_PICK_POSITION, int MANAGE_ID,string strDZBQ_QUANTITY, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            int DZBQ_QUANTITY;

            bResult = int.TryParse(strDZBQ_QUANTITY, out DZBQ_QUANTITY);
            if (!bResult)
            {
                sResult = string.Format("电子标签反馈的字符:{0}无法转换为整数",strDZBQ_QUANTITY);

                return bResult;
            }

            Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
            if (mMANAGE_MAIN == null)
            {
                bResult = false;
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                return bResult;
            }
            List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

            if (MANAGE_LISTS.Count != 1)
            {
                bResult = false;
                sResult = string.Format("任务ID{0}对应的任务明细不符合规定", MANAGE_ID.ToString());
                return bResult;
            }

            /*
             * 判断是否是A类物料序列号拣选
             */
            List<Model.MANAGE_DETAIL> MANAGE_DETAILS = null;
            if (MANAGE_LISTS[0].DETAIL_FLAG == "1")
            {
                MANAGE_DETAILS = this._P_MANAGE_DETAIL.GetListManageListID(MANAGE_LISTS[0].MANAGE_LIST_ID).ToList();

                if (MANAGE_DETAILS == null)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务是A类物料拣选，需要扫描序列号进行拣选", MANAGE_ID.ToString());
                    return bResult;
                }

                if (MANAGE_DETAILS.Count != MANAGE_LISTS[0].MANAGE_LIST_QUANTITY)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务是A类物料拣选，需要扫描序列号进行拣选,扫描的序列号数量与拣选数量不相等", MANAGE_ID.ToString());
                    return bResult;
                }
            }
            
            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                //20180731添加限制条件 20180801
                if ((MANAGE_LISTS[0].MANAGE_LIST_QUANTITY.ToString().Contains("."))||DZBQ_QUANTITY==MANAGE_LISTS[0].MANAGE_LIST_QUANTITY)
                {
                    //电子标签返回的数量与拣选任务数量相符

                    #region if 
                    /*
                     * 拣选任务完成
                     */
                    Storage.List mList = new Storage.List();

                    bResult = mList.Pick(mMANAGE_MAIN, MANAGE_LISTS, MANAGE_DETAILS, out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("拣选任务ID:{0}完成时，库存处理异常!,EX901,异常信息:{1}", MANAGE_ID.ToString(), sResult);
                        return bResult;
                    }

                    //更新计划已拣选数量
                    foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
                    {
                        Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mPLAN_LIST != null)
                        {
                            mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;

                            //20180528
                            //保证拣选工作站拣选任务完成效果
                            if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                            {
                                bResult = false;
                                sResult = string.Format("拣选任务ID:{0}完成时，拣选计划单ID:{1}拣选数量:{2}将大于分配数量:{3}", MANAGE_ID, mPLAN_LIST.PLAN_LIST_ID, mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY, mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY);
                                return bResult;
                            }
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                        }
                    }
                    #endregion
                }
                else
                {
                    //电子标签返回的数量与拣选任务数量不相等

                    #region if 
                    /*
                     * 拣选任务完成
                     */

                    this._log.Info(string.Format("调整电子标签数量流程开始,任务ID:{0},任务数量:{1},调整后数量:{2}",MANAGE_ID, MANAGE_LISTS[0].MANAGE_LIST_QUANTITY,DZBQ_QUANTITY));
                    Storage.List mList = new Storage.List();
                    if(DZBQ_QUANTITY> MANAGE_LISTS[0].MANAGE_LIST_QUANTITY)
                    {
                        bResult = false;
                        sResult = string.Format("拣选任务ID:{0}完成时，电子标签数量调整错误，回传数量:{1}大于任务数量:{2}!,EX9011", MANAGE_ID.ToString(),DZBQ_QUANTITY,MANAGE_LISTS[0].MANAGE_LIST_QUANTITY );
                        return bResult;
                    }
                    decimal updateMANAGE_LIST_QUANTITY = MANAGE_LISTS[0].MANAGE_LIST_QUANTITY - DZBQ_QUANTITY;

                    MANAGE_LISTS[0].MANAGE_LIST_QUANTITY = DZBQ_QUANTITY;
                    this._P_MANAGE_LIST.Update(MANAGE_LISTS[0]);
                   

                    bResult = mList.Pick(mMANAGE_MAIN, MANAGE_LISTS, updateMANAGE_LIST_QUANTITY, MANAGE_DETAILS, out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("拣选任务ID:{0}完成时，库存处理异常!,EX901,异常信息:{1}", MANAGE_ID.ToString(), sResult);
                        return bResult;
                    }

                    //更新计划已拣选数量
                    foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
                    {
                        Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                        if (mPLAN_LIST != null)
                        {
                            mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                            mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY -= updateMANAGE_LIST_QUANTITY;

                            //20180528
                            //保证拣选工作站拣选任务完成效果
                            if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY)
                            {
                                bResult = false;
                                sResult = string.Format("拣选任务ID:{0}完成时，拣选计划单ID:{1}拣选数量:{2}将大于分配数量:{3}", MANAGE_ID, mPLAN_LIST.PLAN_LIST_ID, mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY, mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY);
                                return bResult;
                            }
                            this._P_PLAN_LIST.Update(mPLAN_LIST);
                        }
                    }
                    #endregion
                }
                this._log.Info(string.Format("调整电子标签数量流程临近结束,任务ID:{0},任务数量:{1},调整后数量:{2}", MANAGE_ID, MANAGE_LISTS[0].MANAGE_LIST_QUANTITY, DZBQ_QUANTITY));

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
                /*
                 * 解决A类物料拣选时，出现的事务性问题。
                 * 20180930 下午
                 * hejiaji
                 */
                if (!bResult)
                {
                    sResult = string.Format("ManagePick完成时完成异常 EXSP303 {0}",sResult);
                    return bResult;
                }
                //testing
                bResult = new Plan.PlanPick().ContinueLockAndOut(mT_PICK_POSITION, false, out sResult);

                if (!bResult)
                {
                    sResult = string.Format("ManagePick ContinueLockAndOut方法异常 EXSP304 {0}",sResult);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManagePick完成时发生异常 {0},EX202", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                    this._log.Info(string.Format("调整电子标签数量流程结束, 成功！任务ID:{0},任务数量:{1},调整后数量:{2}", MANAGE_ID, MANAGE_LISTS[0].MANAGE_LIST_QUANTITY, DZBQ_QUANTITY));

                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                    this._log.Info(string.Format("调整电子标签数量流程结束, 失败！任务ID:{0},任务数量:{1},调整后数量:{2},error:{3}", MANAGE_ID, MANAGE_LISTS[0].MANAGE_LIST_QUANTITY, DZBQ_QUANTITY,sResult));

                }
            }
            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 手动完成拣选任务
        /// 2018-04
        /// test
        /// </summary>
        /// <param name="MANAGE_ID">任务ID</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                    return bResult;
                }
                List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

                if (MANAGE_LISTS.Count != 1)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务明细不符合规定", MANAGE_ID.ToString());
                    return bResult;
                }

                List<Model.MANAGE_DETAIL> MANAGE_DETAILS = this._P_MANAGE_DETAIL.GetListManageListID(MANAGE_LISTS[0].MANAGE_LIST_ID).ToList();

                this._P_Base_House.BeginTransaction(bTrans);

                Storage.List mList = new Storage.List();

                bResult = mList.Pick(mMANAGE_MAIN, MANAGE_LISTS, null, out sResult);
                if (!bResult)
                {
                    sResult = string.Format("拣选任务ID:{0}完成时，库存处理异常!", MANAGE_ID.ToString());
                    return bResult;
                }

                //更新计划已拣选数量
                foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
                {
                    Model.PLAN_LIST mPLAN_LIST = this._P_PLAN_LIST.GetModel(mMANAGE_LIST.PLAN_LIST_ID);

                    if (mPLAN_LIST != null)
                    {
                        mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY += mMANAGE_LIST.MANAGE_LIST_QUANTITY;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);
                    }
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManagePick完成时发生异常 {0},EX203", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }
            return bResult;
        }

        public new  bool ManageCancel(int MANAGE_ID, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());
                    return bResult;
                }
                List<Model.MANAGE_LIST> MANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();

                if (MANAGE_LISTS.Count==0)
                {
                    bResult = false;
                    sResult = string.Format("任务ID{0}对应的任务明细不符合规定", MANAGE_ID.ToString());
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                foreach(Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTS)
                {
                    List<Model.MANAGE_DETAIL> MANAGE_DETAILS = this._P_MANAGE_DETAIL.GetListManageListID(mMANAGE_LIST.MANAGE_LIST_ID).ToList();

                    if (MANAGE_DETAILS.Count > 0)
                    {
                        foreach(Model.MANAGE_DETAIL mMANAGE_DETAIL in MANAGE_DETAILS)
                        {
                            this._P_MANAGE_DETAIL.Delete(mMANAGE_DETAIL.MANAGE_DETAIL_ID);
                        }
                    }

                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);
                }

                this._P_MANAGE_MAIN.Delete(mMANAGE_MAIN.MANAGE_ID);

                List<Model.T_PICK_STATION> T_PICK_STATIONS= this._P_T_PICK_STATION.GetList().ToList();

                if (T_PICK_STATIONS.Count > 0)
                {
                    foreach(Model.T_PICK_STATION mT_PICK_STATION in T_PICK_STATIONS)
                    {
                        if(!string.IsNullOrEmpty(mT_PICK_STATION.PROPERTY5))
                        {
                            if (mT_PICK_STATION.PROPERTY5.Equals(MANAGE_ID.ToString()))
                            {
                                mT_PICK_STATION.PROPERTY5 = string.Empty;

                                this._P_T_PICK_STATION.Update(mT_PICK_STATION);
                            }
                        }
                       
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("ManagePick取消时发生异常 {0},EX204", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }
    }
}
