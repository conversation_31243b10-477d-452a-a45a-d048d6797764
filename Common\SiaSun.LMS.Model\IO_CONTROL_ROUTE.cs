﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// IO_CONTROL_ROUTE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class IO_CONTROL_ROUTE
	{
		public IO_CONTROL_ROUTE()
		{
			
		}
		
		private int _control_route_id;
		private int _control_route_type;
		private string _control_route_code;
		private string _control_route_name;
		private string _start_device;
		private string _end_device;
		private int _control_route_status;
		private string _control_route_remark;
        private int _control_route_manage;

        ///<sumary>
        /// 路径编号
        ///</sumary>
        [DataMember]
		public int CONTROL_ROUTE_ID
		{
			get{return _control_route_id;}
			set{_control_route_id = value;}
		}
		///<sumary>
		/// 类型
        ///</sumary>
        [DataMember]
		public int CONTROL_ROUTE_TYPE
		{
			get{return _control_route_type;}
			set{_control_route_type = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string CONTROL_ROUTE_CODE
		{
			get{return _control_route_code;}
			set{_control_route_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string CONTROL_ROUTE_NAME
		{
			get{return _control_route_name;}
			set{_control_route_name = value;}
		}
		///<sumary>
		/// 开始设备
        ///</sumary>
        [DataMember]
		public string START_DEVICE
		{
			get{return _start_device;}
			set{_start_device = value;}
		}
		///<sumary>
		/// 结束设备
        ///</sumary>
        [DataMember]
		public string END_DEVICE
		{
			get{return _end_device;}
			set{_end_device = value;}
		}
		///<sumary>
		/// 状态
        ///</sumary>
        [DataMember]
		public int CONTROL_ROUTE_STATUS
		{
			get{return _control_route_status;}
			set{_control_route_status = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string CONTROL_ROUTE_REMARK
		{
			get{return _control_route_remark;}
			set{_control_route_remark = value;}
		}

        ///<sumary>
        /// 管理软件设置路径状态用
        ///</sumary>
        [DataMember]
        public int CONTROL_ROUTE_MANAGE
        {
            get { return _control_route_manage; }
            set { _control_route_manage = value; }
        }
    }
}
