<ResourceDictionary   xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
  mc:Ignorable="d"
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style x:Key="{x:Static ToolBar.ButtonStyleKey}" TargetType="{x:Type Button}" BasedOn="{x:Null}">
        <Setter Property="FocusVisualStyle" Value="{DynamicResource NuclearButtonFocusVisual}" />
        <Setter Property="Foreground" Value="#FF042271" />
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="MinHeight" Value="18"/>
        <Setter Property="MinWidth" Value="50"/>
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="3" />
        <Setter Property="Template" Value="{DynamicResource ButtonTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.CheckBoxStyleKey}" TargetType="{x:Type CheckBox}">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource CheckBoxFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />
        <Setter Property="Template" Value="{DynamicResource CheckBoxTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.RadioButtonStyleKey}" TargetType="{x:Type RadioButton}">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="FocusVisualStyle" Value="{DynamicResource RadioButtonFocusVisual}" />
        <Setter Property="Foreground" Value="{StaticResource OutsideFontColor}" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="HorizontalContentAlignment" Value="Left" />
        <Setter Property="VerticalContentAlignment" Value="Top" />
        <Setter Property="Padding" Value="4,1,0,0" />
        <Setter Property="Template" Value="{DynamicResource RadioButtonTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.ComboBoxStyleKey}" TargetType="{x:Type ComboBox}">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Center" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="6,2,25,2" />

        <Setter Property="Template" Value="{DynamicResource ComboBoxTemplate}" />
    </Style>

    <Style x:Key="{x:Static ToolBar.TextBoxStyleKey}" TargetType="{x:Type TextBox}">
        <Setter Property="FontSize" Value="10"/>
        <Setter Property="KeyboardNavigation.TabNavigation" Value="None" />
        <Setter Property="FocusVisualStyle" Value="{x:Null}" />
        <Setter Property="AllowDrop" Value="true" />
        <Setter Property="Background" >
            <Setter.Value>
                <SolidColorBrush Color="{DynamicResource WhiteColor}"/>
            </Setter.Value>
        </Setter>
        <Setter Property="HorizontalContentAlignment" Value="Stretch" />
        <Setter Property="VerticalContentAlignment" Value="Stretch" />
        <Setter Property="FontFamily" Value="Trebuchet MS" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="Padding" Value="4" />
        <Setter Property="BorderThickness" Value="1" />
        <Setter Property="BorderBrush">
            <Setter.Value>
                <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                    <GradientStop Color="#FFABAEB3" />
                    <GradientStop Color="#FFE2E8EE" Offset="1" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
        <Setter Property="Template" Value="{DynamicResource TextBoxTemplate}" />
    </Style> 
    
    <Style x:Key="ToolBarHorizontalOverflowButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="{StaticResource ToolBarToggleButtonHorizontalBackground}" />
        <Setter Property="MinHeight" Value="0" />
        <Setter Property="MinWidth" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundOver" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundOver_Highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="0.65" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundOver" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundOver_Highlight" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Border SnapsToDevicePixels="true" x:Name="Bd" CornerRadius="0,0,0,0" Background="{DynamicResource NormalBrush}"/>
                        <Border x:Name="BackgroundOver" BorderThickness="1" CornerRadius="1.75" Opacity="0" Background="{DynamicResource MouseOverBrush}" BorderBrush="{DynamicResource MouseOverBorderBrush}"/>
                        <Border x:Name="BackgroundOver_Highlight" BorderThickness="1,0,1,1" CornerRadius="1" Opacity="0" Margin="0,0,0,0" BorderBrush="{DynamicResource MouseOverHighlightBrush}"/>
                        <Canvas SnapsToDevicePixels="true" HorizontalAlignment="Right" Margin="7,2,2,2" VerticalAlignment="Bottom" Width="6" Height="7">
                            <Path Stroke="White" Data="M 1 1.5 L 6 1.5" />
                            <Path Stroke="{TemplateBinding Foreground}" Data="M 0 0.5 L 5 0.5" />
                            <Path Fill="White" Data="M 0.5 4 L 6.5 4 L 3.5 7 Z" />
                            <Path Fill="{TemplateBinding Foreground}" Data="M -0.5 3 L 5.5 3 L 2.5 6 Z" />
                        </Canvas>
                        <Border x:Name="FocusVisualElement" Margin="-1" BorderBrush="#FFE99862" BorderThickness="1" CornerRadius="2.75" IsHitTestVisible="false" Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOff}" x:Name="HoverOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOn}" x:Name="FocusedOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource ToolBarGripper}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>
    
    <Style x:Key="ToolBarThumbStyle" TargetType="{x:Type Thumb}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <Border SnapsToDevicePixels="True" Background="Transparent" Padding="{TemplateBinding Padding}" CornerRadius="0,0,0,0">
                        <Rectangle>
                            <Rectangle.Fill>
                                <DrawingBrush TileMode="Tile" Viewbox="0,0,4,4" ViewboxUnits="Absolute" Viewport="0,0,4,4" ViewportUnits="Absolute">
                                    <DrawingBrush.Drawing>
                                        <DrawingGroup>
                                            <GeometryDrawing Brush="White" Geometry="M 1 1 L 1 3 L 3 3 L 3 1 z" />
                                            <GeometryDrawing Brush="{StaticResource ToolBarGripper}" Geometry="M 0 0 L 0 2 L 2 2 L 2 0 z" />
                                        </DrawingGroup>
                                    </DrawingBrush.Drawing>
                                </DrawingBrush>
                            </Rectangle.Fill>
                        </Rectangle>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Cursor" Value="SizeAll" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>    

    <Style x:Key="ToolBarVerticalOverflowButtonStyle" TargetType="{x:Type ToggleButton}">
        <Setter Property="Background" Value="{StaticResource ToolBarToggleButtonVerticalBackground}" />
        <Setter Property="MinHeight" Value="0" />
        <Setter Property="MinWidth" Value="0" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToggleButton}">
                    <Border SnapsToDevicePixels="true" x:Name="Bd" Background="{TemplateBinding Background}" CornerRadius="0,0,3,3">
                        <Canvas SnapsToDevicePixels="true" HorizontalAlignment="Right" Margin="2,7,2,2" VerticalAlignment="Bottom" Width="7" Height="6">
                            <Path Stroke="White" Data="M 1.5 1 L 1.5 6" />
                            <Path Stroke="{TemplateBinding Foreground}" Data="M 0.5 0 L 0.5 5" />
                            <Path Fill="White" Data="M 3.5 0.5 L 7 3.5 L 4 6.5 Z" />
                            <Path Fill="{TemplateBinding Foreground}" Data="M 3 -0.5 L 6 2.5 L 3 5.5 Z" />
                        </Canvas>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsMouseOver" Value="true">
                            <Setter Property="Background" TargetName="Bd" Value="{StaticResource ToolBarButtonHover}" />
                        </Trigger>
                        <Trigger Property="IsKeyboardFocused" Value="true">
                            <Setter Property="Background" TargetName="Bd" Value="{StaticResource ToolBarButtonHover}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{StaticResource ToolBarGripper}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type ToolBar}">
        <Setter Property="Background" Value="{StaticResource ToolBarHorizontalBackground}" />
        <Setter Property="BorderBrush" Value="#FFB1703C" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ToolBar}">
                    <Grid SnapsToDevicePixels="true" Margin="3,1,1,1" x:Name="Grid">
                        <Grid HorizontalAlignment="Right" x:Name="OverflowGrid">
                            <ToggleButton IsEnabled="{TemplateBinding HasOverflowItems}" FocusVisualStyle="{x:Null}" x:Name="OverflowButton" Style="{StaticResource ToolBarHorizontalOverflowButtonStyle}" ClickMode="Press" IsChecked="{Binding Path=IsOverflowOpen, Mode=TwoWay, RelativeSource={RelativeSource TemplatedParent}}">
                                <ToggleButton.Background>
                                    <LinearGradientBrush EndPoint="0.5,1" StartPoint="0.5,0">
                                        <GradientStop Color="#FFFFFFFF" Offset="0" />
                                        <GradientStop Color="#FF8AAEDA" Offset="0.521" />
                                        <GradientStop Color="#FFC6D6EC" Offset="0.194" />
                                        <GradientStop Color="#FFB4C9E5" Offset="0.811" />
                                        <GradientStop Color="#FFB7C8E0" Offset="0.507" />
                                        <GradientStop Color="#FFD1DEF0" Offset="1" />
                                    </LinearGradientBrush>
                                </ToggleButton.Background>
                            </ToggleButton>
                            <Popup Focusable="false" AllowsTransparency="true" IsOpen="{Binding Path=IsOverflowOpen, RelativeSource={RelativeSource TemplatedParent}}" Placement="Bottom" PopupAnimation="{DynamicResource {x:Static SystemParameters.ComboBoxPopupAnimationKey}}" StaysOpen="False" x:Name="OverflowPopup">
                                <Border x:Name="Shdw">
                                    <Border BorderThickness="1,1,1,1" BorderBrush="{TemplateBinding BorderBrush}">
                                        <Border.Background>
                                            <LinearGradientBrush EndPoint="1.204,0.5" StartPoint="0.056,0.5">
                                                <GradientStop Color="#FFFFFFFF" Offset="0" />
                                                <GradientStop Color="#FFD4D7DB" Offset="1" />
                                            </LinearGradientBrush>
                                        </Border.Background>
                                        <ToolBarOverflowPanel Focusable="true" SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" FocusVisualStyle="{x:Null}" Margin="2" x:Name="PART_ToolBarOverflowPanel" WrapWidth="200" KeyboardNavigation.DirectionalNavigation="Cycle" KeyboardNavigation.TabNavigation="Cycle" />
                                    </Border>
                                </Border>
                            </Popup>
                        </Grid>
                        <Border Margin="0,0,11,0" x:Name="MainPanelBorder" BorderThickness="1" CornerRadius="0,0,0,0" Padding="{TemplateBinding Padding}" Background="{DynamicResource NormalBrush}" BorderBrush="{DynamicResource NormalBorderBrush}">
                            <Grid>

                                <DockPanel KeyboardNavigation.TabIndex="1" KeyboardNavigation.TabNavigation="Local">
                                    <Thumb Padding="6,5,1,6" Margin="-3,-1,0,0" x:Name="ToolBarThumb" Style="{StaticResource ToolBarThumbStyle}" Width="10" />
                                    <ContentPresenter SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" HorizontalAlignment="Center" Margin="4,0,4,0" x:Name="ToolBarHeader" VerticalAlignment="Center" ContentSource="Header" />
                                    <ToolBarPanel SnapsToDevicePixels="{TemplateBinding SnapsToDevicePixels}" Margin="0,1,2,2" x:Name="PART_ToolBarPanel" IsItemsHost="true" Background="{DynamicResource NormalBrush}"/>
                                </DockPanel>
                            </Grid>
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsOverflowOpen" Value="true">
                            <Setter Property="IsEnabled" TargetName="ToolBarThumb" Value="false" />
                        </Trigger>
                        <Trigger Property="Header" Value="{x:Null}">
                            <Setter Property="Visibility" TargetName="ToolBarHeader" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="ToolBarTray.IsLocked" Value="true">
                            <Setter Property="Visibility" TargetName="ToolBarThumb" Value="Collapsed" />
                        </Trigger>
                        <Trigger Property="HasDropShadow" SourceName="OverflowPopup" Value="true">
                            <Setter Property="Margin" TargetName="Shdw" Value="0,0,5,5" />
                            <Setter Property="SnapsToDevicePixels" TargetName="Shdw" Value="true" />
                            <Setter Property="Background" TargetName="Shdw" Value="#71000000" />
                        </Trigger>
                        <Trigger Property="Orientation" Value="Vertical">
                            <Setter Property="Margin" TargetName="Grid" Value="1,3,1,1" />
                            <Setter Property="Style" TargetName="OverflowButton" Value="{StaticResource ToolBarVerticalOverflowButtonStyle}" />
                            <Setter Property="Height" TargetName="ToolBarThumb" Value="10" />
                            <Setter Property="Width" TargetName="ToolBarThumb" Value="Auto" />
                            <Setter Property="Margin" TargetName="ToolBarThumb" Value="-1,-3,0,0" />
                            <Setter Property="Padding" TargetName="ToolBarThumb" Value="5,6,6,1" />
                            <Setter Property="Margin" TargetName="ToolBarHeader" Value="0,0,0,4" />
                            <Setter Property="Margin" TargetName="PART_ToolBarPanel" Value="1,0,2,2" />
                            <Setter Property="DockPanel.Dock" TargetName="ToolBarThumb" Value="Top" />
                            <Setter Property="DockPanel.Dock" TargetName="ToolBarHeader" Value="Top" />
                            <Setter Property="HorizontalAlignment" TargetName="OverflowGrid" Value="Stretch" />
                            <Setter Property="VerticalAlignment" TargetName="OverflowGrid" Value="Bottom" />
                            <Setter Property="Placement" TargetName="OverflowPopup" Value="Right" />
                            <Setter Property="Margin" TargetName="MainPanelBorder" Value="0,0,0,11" />
                            <Setter Property="Background" Value="{StaticResource ToolBarVerticalBackground}" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
        <Setter Property="Foreground" Value="#FF000000" />
    </Style>

</ResourceDictionary>