﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;

namespace SiaSun.LMS.Implement
{
    public class ManageStockIn:ManageIn
    {
        /// <summary>
        /// 生成托盘入库任务
        /// MANAGE_STOCK_IN.XAML
        /// </summary>
        public new bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 bool bCheckStorage,
                                 bool bComplete,
                                 bool bAutoSendControl,
                                 bool wsNoticeControl,
                                 out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            int MANAGE_ID = 0;

            try
            {
                SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

                if (mMANAGE_TYPE == null)
                {
                    bResult = false;

                    sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                    return bResult;
                }

                if (!string.IsNullOrEmpty(mMANAGE_MAIN.STOCK_BARCODE) && (this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.LiKu.ToString(), out sResult)))
                {
                    sResult = string.Format("托盘{0}已经在立库区", mMANAGE_MAIN.STOCK_BARCODE);
                    return false;
                }

                if (this._S_StorageService.StorageCheck(mMANAGE_MAIN.STOCK_BARCODE, Enum.AREA_TYPE.XuNiKu.ToString(), out sResult) && bCheckStorage)
                {
                    sResult = string.Format("托盘{0}库存校验失败\n {1} ", mMANAGE_MAIN.STOCK_BARCODE, "已经存在库存");
                    return false;
                }

                if (!string.IsNullOrEmpty(mMANAGE_MAIN.STOCK_BARCODE) && this._P_MANAGE_MAIN.GetModelStockBarcode(mMANAGE_MAIN.STOCK_BARCODE) != null)
                {
                    bResult = false;

                    sResult = string.Format("托盘条码{0}已经存在任务", mMANAGE_MAIN.STOCK_BARCODE);

                    return bResult;
                }

                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                this._P_MANAGE_MAIN.Add(mMANAGE_MAIN);

                foreach (SiaSun.LMS.Model.MANAGE_LIST mMANAGE_LIST in lsMANAGE_LIST)
                {
                    mMANAGE_LIST.MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                    this._P_MANAGE_LIST.Add(mMANAGE_LIST);
                }

                MANAGE_ID = mMANAGE_MAIN.MANAGE_ID;

                if (bComplete)
                {
                    bResult = _S_ManageService.Invoke(
                                                     mMANAGE_TYPE.MANAGE_TYPE_CLASS,
                                                     "ManageComplete",
                                                     new object[] { MANAGE_ID, false },
                                                     out sResult
                                                     );
                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }
                else
                {
                    if (mWH_CELL_START != null)
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新开始货位{0}状态错误\n{1}", mWH_CELL_START.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }

                    if (mWH_CELL_END != null)
                    {
                        bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID, string.Empty, SiaSun.LMS.Enum.RUN_STATUS.Selected.ToString(), out sResult);

                        if (!bResult)
                        {
                            sResult = string.Format("更新结束货位{0}状态错误\n{1}", mWH_CELL_END.CELL_CODE, sResult);

                            this._P_Base_House.RollBackTransaction(bTrans);

                            return bResult;
                        }
                    }
                }

                int controlId = 0;
                if (bAutoSendControl)
                {
                    bResult = this.ManageDownLoad(MANAGE_ID, string.Empty, false, false, out controlId, out sResult);

                    if (!bResult)
                    {
                        this._P_Base_House.RollBackTransaction(bTrans);

                        return bResult;
                    }
                }


                this._P_Base_House.CommitTransaction(bTrans);

                if (wsNoticeControl)
                {
                    //wdz add 2018-03-03
                    this._S_ManageService.ManageDownLoadWebService(controlId);
                }
            }
            catch (Exception ex)
            {
                this._P_Base_House.RollBackTransaction(bTrans);

                bResult = false;

                sResult = ex.Message;
            }

            return bResult;
        }

        public new bool ManageCreate(SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 bool bCheckStorage,
                                 bool bComplete,
                                 bool bAutoSendControl,
                                 out string sResult)
        {
            return this.ManageCreate(mMANAGE_MAIN, lsMANAGE_LIST, bTrans, bCheckStorage, bComplete, bAutoSendControl, false, out sResult);
        }


        /// <summary>
        /// 任务完成
        /// </summary>
        public new bool ManageComplete(int MANAGE_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(MANAGE_ID);

            bResult = null != mMANAGE_MAIN;

            if (!bResult)
            {
                sResult = string.Format("未能找到任务{0}", MANAGE_ID.ToString());

                return bResult;
            }

            SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);

            if (mMANAGE_TYPE == null)
            {
                bResult = false;

                sResult = string.Format("未找到任务类型{0}", mMANAGE_MAIN.MANAGE_TYPE_CODE);

                return bResult;
            }

            try
            {

                SiaSun.LMS.Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mMANAGE_MAIN.START_CELL_ID);

                SiaSun.LMS.Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mMANAGE_MAIN.END_CELL_ID);

                this._P_Base_House.BeginTransaction(bTrans);

                if (mWH_CELL_START != null && (mWH_CELL_START.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_START.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.START_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Nohave.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }

                if (!bResult)
                {
                    sResult = string.Format("更新起始位置{0}状态错误\n", mMANAGE_MAIN.START_CELL_ID.ToString());

                    return bResult;
                }
                if (mWH_CELL_END != null && (mWH_CELL_END.CELL_TYPE.TrimEnd() == Enum.CELL_TYPE.Cell.ToString()
                    || mWH_CELL_END.CELL_STORAGE_TYPE.TrimEnd() == Enum.CELL_STORAGE_TYPE.Single.ToString()))
                {
                    bResult = this._S_CellService.CellUpdateStatus(mMANAGE_MAIN.END_CELL_ID,
                                                                   SiaSun.LMS.Enum.CELL_STATUS.Pallet.ToString(),
                                                                   SiaSun.LMS.Enum.RUN_STATUS.Enable.ToString(),
                                                                   out sResult);
                }
                if (!bResult)
                {
                    sResult = string.Format("更新终止位置{0}状态错误\n", mMANAGE_MAIN.END_CELL_ID.ToString());

                    return bResult;
                }

                //wdz add 2018-01-04 2019-06-29
                string supplyEmptyBoxFromFloorOneConfig = string.Empty;
                if (!this._S_SystemService.GetSysParameter("SupplyEmptyBoxFromFloorOneConfig", out supplyEmptyBoxFromFloorOneConfig) ||
                    !supplyEmptyBoxFromFloorOneConfig.Contains(mWH_CELL_END.CELL_CODE))
                {
                    bResult = this.Invoke(mMANAGE_TYPE.STORAGE_TYPE_CLASS.TrimEnd(), "StorageCreate", new object[] { MANAGE_ID }, out sResult);
                    if (!bResult)
                    {
                        sResult = string.Format("库存处理错误-{0}", sResult);
                        return bResult;
                    }
                }

                //接口调用
                string isNoticeWms = string.Empty;
                if (mMANAGE_MAIN.MANAGE_SOURCE == Enum.TASK_SOURCE.WMS.ToString() &&
                    this._S_SystemService.GetSysParameter("WmsInterfaceStatus", out isNoticeWms) && isNoticeWms == Enum.FLAG.Enable.ToString())
                {
                    bResult = new Interface.handleResultReturnFromWCS().NotifyMethod(mMANAGE_MAIN.MANAGE_RELATE_CODE, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

                bResult = base.ManageComplete(mMANAGE_MAIN, false, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;

                sResult = ex.Message;

                //this._log.Fatal(string.Format("调用方法{0}发生异常。{1}", MethodBase.GetCurrentMethod().Name, MANAGE_ID), ex);
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction(bTrans);
                }
                else
                {
                    this._P_Base_House.RollBackTransaction(bTrans);
                }
            }

            //this._log.Info(string.Format("任务完成处理完毕{0} {1}", MethodBase.GetCurrentMethod().Name, MANAGE_ID));


            return bResult;
        }

    }
}
