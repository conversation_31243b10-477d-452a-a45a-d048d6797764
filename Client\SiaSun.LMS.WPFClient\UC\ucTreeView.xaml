﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucTreeView"
             xmlns:s="clr-namespace:System;assembly=mscorlib"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="224">

    <UserControl.Resources>
        
        <!--带CheckBox的选项-->
        <ControlTemplate x:Key="templateCheckBoxTreeViewItem" TargetType="TreeViewItem">
            <StackPanel Orientation="Horizontal"  Background="{TemplateBinding Background}" Tag="{TemplateBinding Tag}">
                <CheckBox Margin="3,1,2,1" Name="chkChecked" Click="chkChecked_Click" Tag="{TemplateBinding Tag}"></CheckBox>
                <TextBlock Margin="1" Text="{TemplateBinding Header}" Tag="{TemplateBinding Tag}"></TextBlock>
            </StackPanel>
            
            <ControlTemplate.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="PaleGoldenrod"></Setter>
                    <Setter Property="Cursor" Value="Hand"></Setter>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="LightGreen"></Setter>
                </Trigger>
            </ControlTemplate.Triggers>
        </ControlTemplate>   
        
        <Style x:Key="styleTreeViewItem" TargetType="TreeViewItem">
            <Setter Property="MinHeight" Value="18"></Setter>
            <Style.Triggers>
                <Trigger Property="IsMouseOver" Value="True">
                    <Setter Property="Background" Value="PaleGoldenrod"></Setter>
                    <Setter Property="Cursor" Value="Hand"></Setter>
                </Trigger>
                <Trigger Property="IsSelected" Value="True">
                    <Setter Property="Background" Value="LightGreen"></Setter>
                </Trigger>
            </Style.Triggers>
        </Style>
    </UserControl.Resources>
    
    <GroupBox Name="grpBoxHeader" Header="树控件" Tag="请选中{0}节点">
        <Grid>
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
            </Grid.RowDefinitions>
            <WrapPanel Grid.Row="0" >
                <TextBox Name="txtQuickQuery" Width="120" Margin="2" KeyDown="txtQuickQuery_KeyDown"></TextBox>
                <Button Name="btnQuery"  Width="60" Margin="5,1,5,1" Click="btnQuery_Click">查询</Button>
            </WrapPanel>
            <TreeView Name="tvwList" Margin="1,5,1,5" Grid.Row="1" HorizontalAlignment="Stretch" Cursor="Hand" SelectedItemChanged="tvwList_SelectedItemChanged">
            </TreeView>
        </Grid>
    </GroupBox>        
</UserControl>
