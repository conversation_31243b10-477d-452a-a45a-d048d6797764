﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="RECORD_DETAIL" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="RECORD_DETAIL" type="SiaSun.LMS.Model.RECORD_DETAIL, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="RECORD_DETAIL">
			<result property="RECORD_DETAIL_ID" column="record_detail_id" />
			<result property="RECORD_LIST_ID" column="record_list_id" />
			<result property="BOX_BARCODE" column="box_barcode" />
			<result property="GOODS_BARCODE" column="goods_barcode" />
			<result property="RECORD_DETAIL_REMARK" column="record_detail_remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="RECORD_DETAIL_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  record_detail_id,
				  record_list_id,
				  box_barcode,
				  goods_barcode,
				  record_detail_remark
			From RECORD_DETAIL
		</select>
		
		<select id="RECORD_DETAIL_SELECT_BY_ID" parameterClass="int" extends = "RECORD_DETAIL_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					record_detail_id=#RECORD_DETAIL_ID# 
				</isParameterPresent>
			</dynamic>
		</select>
		

				
		<insert id="RECORD_DETAIL_INSERT" parameterClass="RECORD_DETAIL">
      Insert Into RECORD_DETAIL (
      record_detail_id,
      record_list_id,
      box_barcode,
      goods_barcode,
      record_detail_remark
      )Values(
      #RECORD_DETAIL_ID#,
      #RECORD_LIST_ID#,
      #BOX_BARCODE#,
      #GOODS_BARCODE#,
      #RECORD_DETAIL_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="RECORD_DETAIL_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="RECORD_DETAIL_UPDATE" parameterClass="RECORD_DETAIL">
      Update RECORD_DETAIL Set
      record_detail_id=#RECORD_DETAIL_ID#,
      record_list_id=#RECORD_LIST_ID#,
      box_barcode=#BOX_BARCODE#,
      goods_barcode=#GOODS_BARCODE#,
      record_detail_remark=#RECORD_DETAIL_REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					record_detail_id=#RECORD_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="RECORD_DETAIL_DELETE" parameterClass="int">
			Delete From RECORD_DETAIL
			<dynamic prepend="WHERE">
				<isParameterPresent>
					record_detail_id=#RECORD_DETAIL_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>