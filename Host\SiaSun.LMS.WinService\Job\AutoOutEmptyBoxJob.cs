﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    /// <summary>
    /// 自动补给空箱
    /// </summary>
    [DisallowConcurrentExecution]
    public class AutoOutEmptyBoxJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                string supplyEmptyBoxFromWarehouseConfig = string.Empty;
                if (MainApp.BaseService._S_SystemService.GetSysParameter("SupplyEmptyBoxFromWarehouseConfig", out supplyEmptyBoxFromWarehouseConfig))
                {
                    foreach (var areaConfig in supplyEmptyBoxFromWarehouseConfig.Split('|'))
                    {
                        //配置格式 使能-阈值-站台
                        string[] config = areaConfig.Split('-');
                        if (config.Count() != 3)
                        {
                            Program.sysLog.WarnFormat("AutoOutEmptyBoxJob.Execute:自动空箱出库补充到拣选工作站时解析配置[{0}]有误", areaConfig);
                            return;
                        }

                        //判断使能是否有效
                        if (config[0] != Enum.FLAG.Enable.ToString())
                        {
                            continue;
                        }

                        string jsonStringIn = "\"quantity\":\"{0}\",\"boxType\":\"{1}\",\"toPosition\":\"{2}\",\"uniqueCode\":\"{3}\",\"interfaceType\":1,\"interfaceSource\":\"WES\"";
                        System.Data.DataTable dtCurrentBufferCount = MainApp._I_BaseService.GetList(string.Format("select * from IO_CONTROL_BUFFER where BUFFER_CODE ='{0}'", config[2]));
                        System.Data.DataTable dtCurrentManageCount = MainApp._I_BaseService.GetList(string.Format("select count(0) from V_MANAGE where MANAGE_TYPE_CODE ='ManageStockOut' and END_POSITION = '{0}' ", config[2]));

                        //阈值>缓存数量+任务数量 则再次出库
                        int thresdhold, bufferCount, manageCount = 0;
                        if (int.TryParse(config[1], out thresdhold) && 
                            int.TryParse(dtCurrentBufferCount.Rows[0]["BUFFER_VALUE"].ToString(), out bufferCount) &&
                            int.TryParse(dtCurrentManageCount.Rows[0][0].ToString(), out manageCount) &&
                            thresdhold > bufferCount + manageCount)
                        {
                            int needQty = thresdhold - bufferCount - manageCount;
                            string jsonStringOut = string.Empty;
                            jsonStringIn = "{" + string.Format(jsonStringIn, needQty, "1", config[2], "BoxSupply" + DateTime.Now.ToString("yyyyMMddHHmmss")) + "}";
                            new SiaSun.LMS.Implement.Interface.EmptyBoxOut().NotifyMethod(jsonStringIn, out jsonStringOut);

                            if (!jsonStringOut.Contains("成功"))
                            {
                                //库存不足时寻找合适的库存量出库
                                for (int i = 1; i < needQty; i++)
                                {
                                    if (jsonStringOut.Contains(i.ToString()))
                                    {
                                        jsonStringIn = "\"quantity\":\"{0}\",\"boxType\":\"{1}\",\"toPosition\":\"{2}\",\"uniqueCode\":\"{3}\",\"interfaceType\":1,\"interfaceSource\":\"WES\"";
                                        jsonStringIn = "{" + string.Format(jsonStringIn, i, "1", config[2], "BoxSupply" + DateTime.Now.ToString("yyyyMMddHHmmss")) + "}";
                                        new SiaSun.LMS.Implement.Interface.EmptyBoxOut().NotifyMethod(jsonStringIn, out jsonStringOut);
                                        jsonStringOut = string.Format("由于库存不足将出库数量改为[{0}]", i) + jsonStringOut;
                                        break;
                                    }
                                }

                                Program.sysLog.WarnFormat("AutoOutEmptyBoxJob.Execute:自动空箱出库补充到拣选工作站空箱缓存失败_报文[{0}]", jsonStringOut);
                            }
                            else
                            {
                                MainApp._I_BaseService.CreateSysLog(SiaSun.LMS.Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Information, "AutoOutEmptyBoxJob.Execute():自动空箱出库补充到拣选工作站空箱缓存成功");
                            }
                        }

                    }
                }
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("AutoOutEmptyBoxJob.Execute:自动空箱下架补充空箱缓存区域4异常", ex);
            }
        }

        /*2019-04-18修改前备份
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                int iEmptyBoxThresdhold4 = 0;
                int iCurrentBufferCount4 = 0;
                int iCurrentManageCount4 = 0;
                string sEmptyBoxThresdhold4 = string.Empty;
                string autoSupplyEmptyBox4 = string.Empty;
                string emptyBoxOutStation4 = string.Empty;
                string jsonStringIn = "\"quantity\":\"{0}\",\"boxType\":\"{1}\",\"toPosition\":\"{2}\",\"uniqueCode\":\"{3}\",\"interfaceType\":1,\"interfaceSource\":\"WES\"";

                if (MainApp.BaseService._S_SystemService.GetSysParameter("IsAutoSupplyEmptyBox-4", out autoSupplyEmptyBox4) &&
                    autoSupplyEmptyBox4 == "Enable" &&
                    MainApp.BaseService._S_SystemService.GetSysParameter("EmptyBoxBufferStation-4", out emptyBoxOutStation4) &&
                    MainApp.BaseService._S_SystemService.GetSysParameter("EmptyBoxBufferThreshold-4", out sEmptyBoxThresdhold4) &&
                    int.TryParse(sEmptyBoxThresdhold4, out iEmptyBoxThresdhold4)
                    )
                {
                    System.Data.DataTable dtCurrentBufferCount4 = MainApp._I_BaseService.GetList(string.Format("select * from IO_CONTROL_BUFFER where BUFFER_CODE ='{0}'", emptyBoxOutStation4));
                    System.Data.DataTable dtCurrentManageCount4 = MainApp._I_BaseService.GetList(string.Format("select count(0) from V_MANAGE where MANAGE_TYPE_CODE ='ManageStockOut' and END_POSITION = '{0}' ", emptyBoxOutStation4));

                    if (int.TryParse(dtCurrentBufferCount4.Rows[0]["BUFFER_VALUE"].ToString(), out iCurrentBufferCount4) &&
                        int.TryParse(dtCurrentManageCount4.Rows[0][0].ToString(), out iCurrentManageCount4) &&
                        iEmptyBoxThresdhold4 > iCurrentBufferCount4 + iCurrentManageCount4)
                    {
                        int needQty = iEmptyBoxThresdhold4 - iCurrentBufferCount4 - iCurrentManageCount4;
                        string jsonStringOut = string.Empty;
                        jsonStringIn = "{" + string.Format(jsonStringIn, needQty, "1", emptyBoxOutStation4, "BoxSupply" + DateTime.Now.ToString("yyyyMMddHHmmss")) + "}";
                        new SiaSun.LMS.Implement.Interface.EmptyBoxOut().NotifyMethod(jsonStringIn, out jsonStringOut);

                        if (!jsonStringOut.Contains("成功"))
                        {
                            //库存不足时寻找合适的库存量出库
                            for (int i = 1; i < needQty; i++)
                            {
                                if (jsonStringOut.Contains(i.ToString()))
                                {
                                    jsonStringIn = "\"quantity\":\"{0}\",\"boxType\":\"{1}\",\"toPosition\":\"{2}\",\"uniqueCode\":\"{3}\",\"interfaceType\":1,\"interfaceSource\":\"WES\"";
                                    jsonStringIn = "{" + string.Format(jsonStringIn, i, "1", emptyBoxOutStation4, "BoxSupply" + DateTime.Now.ToString("yyyyMMddHHmmss")) + "}";
                                    new SiaSun.LMS.Implement.Interface.EmptyBoxOut().NotifyMethod(jsonStringIn, out jsonStringOut);
                                    jsonStringOut = string.Format("由于库存不足将出库数量改为[{0}]", i) + jsonStringOut;
                                    break;
                                }
                            }

                            Program.sysLog.WarnFormat("AutoOutEmptyBoxJob.Execute:自动空箱出库补充到拣选工作站空箱缓存失败 {0}", jsonStringOut);
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(SiaSun.LMS.Enum.LogThread.Task, "System", Enum.LOG_LEVEL.Information, "AutoOutEmptyBoxJob.Execute():自动空箱出库补充到拣选工作站空箱缓存成功");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("AutoOutEmptyBoxJob.Execute:自动空箱下架补充空箱缓存区域4异常", ex);
            }
        }
        */
    }
}
