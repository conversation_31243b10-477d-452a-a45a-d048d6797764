﻿using System;
using System.Text;
using System.Net;
using System.Net.Sockets;
using System.Net.NetworkInformation;
using System.Threading;

namespace SiaSun.LMS.Scaner
{
    /// <summary>
    /// Tcp协议异步通讯类(客户端)
    /// </summary>
    public class AsynTcpClient
    {
        private string _deviceIpAddress;
        private int _deviceIpPort;
        private int _reConnectInterval;
        private Socket _socket;
        private bool _alive;
        private bool _connected;
        //private const int LOOP_INTERVAL=1000

        private StringBuilder _sbReceivedData;
        public StringBuilder SbReceivedData
        {
            get { return _sbReceivedData; }
            set { _sbReceivedData = value; }
        }

        private Thread _thread;

        public delegate void ReceivedEventHandler(string prmReceivedData);
        public event ReceivedEventHandler OnReceive;

        public delegate void DisConnectEventHandler();
        public event DisConnectEventHandler OnDisConnect;

        public delegate void HeartBeatEventHandler(bool isAlive,bool isConnected);
        public event HeartBeatEventHandler OnHeartBeat;


        public AsynTcpClient(string prmAddress,int prmPort,int reConnectInterval)
        {
            this._deviceIpAddress = prmAddress;
            this._deviceIpPort = prmPort;
            this._reConnectInterval = reConnectInterval;

            _sbReceivedData = new StringBuilder();

            _thread = new Thread(new ThreadStart(() => 
            {
                while(true)
                {
                    try
                    {
                        Ping ping = new Ping();
                        PingReply pingReply0 = ping.Send(IPAddress.Parse(this._deviceIpAddress));
                        PingReply pingReply1 = ping.Send(IPAddress.Parse(this._deviceIpAddress));
                        PingReply pingReply2 = ping.Send(IPAddress.Parse(this._deviceIpAddress));

                        _alive = !(pingReply0.Status != IPStatus.Success && pingReply1.Status != IPStatus.Success && pingReply2.Status != IPStatus.Success);
                    }
                    catch(Exception ex)
                    {
                        _alive = false;
                    }

                    if(!_alive && _socket!=null)
                    {
                        _connected = false;
                        _socket.Shutdown(SocketShutdown.Both);
                        _socket.Close();
                        _socket = null;
                    }

                    OnHeartBeat?.Invoke(_alive, _connected);

                    Thread.Sleep(_reConnectInterval);
                }
            }));
            _thread.Start();
        }

        /// <summary>
        /// Tcp协议异步连接服务器
        /// </summary>
        public void AsynConnect()
        {
            ////主机IP
            //IPEndPoint serverIp = new IPEndPoint(IPAddress.Parse(this._DeviceIpAddress), this._DeviceIpPort);
            //Socket tcpClient = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
            //tcpClient.BeginConnect(serverIp, asyncResult =>
            //{
            //    tcpClient.EndConnect(asyncResult);
            //    AsynRecive(tcpClient);
            //}, null);
        }


        /// <summary>
        /// Tcp协议同步连接服务器
        /// </summary>
        public string SyncConnect()
        {
            string result = "成功";

            try
            {
                //主机IP
                IPEndPoint serverIp = new IPEndPoint(IPAddress.Parse(this._deviceIpAddress), this._deviceIpPort);
                //改为在类中定义
                //Socket tcpClient = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);

                _socket = new Socket(AddressFamily.InterNetwork, SocketType.Stream, ProtocolType.Tcp);
                _socket.Connect(serverIp);

                //改为直接引用类中的socket
                //AsynRecive(tcpClient);
                AsynRecive();

                _connected = true;
            }
            catch (Exception ex)
            {
                _connected = false;
                result = string.Format("程序发生异常_{0}",ex.Message);
            }
            return result;
        }

        /// <summary>
        /// 异步连接客户端回调函数
        /// </summary>
        /// <param name="tcpClient">socket</param>
        public void AsynRecive()
        {
            byte[] data = new byte[16];
            if (_socket != null)
            {
                _socket.BeginReceive(data, 0, data.Length, SocketFlags.None, asyncResult =>
                {
                    if (_connected)
                    {
                        int length = _socket.EndReceive(asyncResult);
                    //判断是否为关闭连接
                    if (!_socket.Connected || length <= 0)
                        {
                            _connected = false;
                            _socket.Close();
                            this.OnDisConnect?.Invoke();
                            return;
                        }

                        this._sbReceivedData.Append(Encoding.UTF8.GetString(data));

                        if (this.OnReceive != null)
                        {
                            this.OnReceive(this._sbReceivedData.ToString());
                        }

                        AsynRecive();
                    }
                }, null);
            }
        }

        /// <summary>
        /// 异步发送消息
        /// </summary>
        /// <param name="tcpClient">客户端套接字</param>
        /// <param name="message">发送消息</param>
        public void AsynSend(Socket tcpClient, string message)
        {
            byte[] data = Encoding.UTF8.GetBytes(message);
            tcpClient.BeginSend(data, 0, data.Length, SocketFlags.None, asyncResult =>
            {
                //完成发送消息
                int length = tcpClient.EndSend(asyncResult);
                Console.WriteLine("client-->-->server:{0}", message);
            }, null);
        }
    }
}
