﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.TEMPLATE.MANAGE_PRODUCT_OUT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                 xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="MANAGE_PLAN_DOWNT" Height="372" Width="508" Loaded="DocumentContent_Loaded">


    <Grid >
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>

        <WrapPanel Grid.Row="0"  HorizontalAlignment="Left" VerticalAlignment="Center">
            <StackPanel  Name="panelGoods" Orientation="Horizontal" Margin="5,5,5,5" >
                <TextBlock Text="产品:" Margin="2" VerticalAlignment="Center" />
                <ComboBox Name="cmbGoods" Margin="2,0,2,0" MinWidth="120" MinHeight="23" />
            </StackPanel>

            <StackPanel Name="panelTemplate"  Orientation="Horizontal" Margin="{Binding ElementName=panelGoods,Path=Margin}">
                <TextBlock Text="配备方案:" Margin="2" VerticalAlignment="Center"></TextBlock>
                <ComboBox Name="cmbTemplate" Margin="2,0,0,0" MinWidth="120" IsEditable="True"  AllowDrop="True" IsTextSearchEnabled="True"  StaysOpenOnEdit="True"></ComboBox>
            </StackPanel>
        </WrapPanel>

        <uc:ucSplitPropertyGridTab x:Name="gridTemplateList" Grid.Row="1"></uc:ucSplitPropertyGridTab>
        
        <WrapPanel Grid.Row="2" Margin="3" HorizontalAlignment="Left" VerticalAlignment="Center" ButtonBase.Click="WrapPanel_Click">
            <uc:ucManagePosition x:Name="ucManagePosition"  Grid.Row="0"></uc:ucManagePosition>
            <Button Name="btnCallTemplate"  Width="60">生产要料</Button>
            <Button Name="btnPalletBack"  Width="60">空托盘返库</Button>
        </WrapPanel>
    </Grid>

</ad:DocumentContent>
