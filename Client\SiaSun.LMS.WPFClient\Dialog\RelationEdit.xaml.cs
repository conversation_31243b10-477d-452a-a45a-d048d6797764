﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// RelationEdit.xaml 的交互逻辑
    /// </summary>
    public partial class RelationEdit : AvalonDock.DocumentContent
    {
        string strRelationCode = string.Empty;
        string strWhere = string.Empty;
        string strOrderFields = string.Empty;

        /// <summary>
        /// 构造函数
        /// </summary>
        /// <param name="RELATION_CODE">关系编码</param>
        /// <param name="Style">显示样式</param>
        /// <param name="WHERE">筛选条件</param>
        public RelationEdit(string RELATION_CODE, string WHERE,string OrderFields)
        {
            InitializeComponent();

            strRelationCode = RELATION_CODE;
            strWhere = WHERE;
            strOrderFields = OrderFields;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.ucRelationManage.U_RelationCode = this.strRelationCode;
            this.ucRelationManage.U_Where = this.strWhere;
            this.ucRelationManage.U_OrderFields = strOrderFields;
            this.ucRelationManage.U_InitControl();
        }
    }
}
