﻿using Microsoft.AspNet.SignalR.Client;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Net;
using System.Text;
using System.Threading.Tasks;
using System.Configuration;

namespace SiaSun.LMS.WPFClient.PICK.Message
{
    public class MessageSrv : IMessageSrv
    {
        public event Action<string, string, MessageType> NewTextMessage;
        public event Action ConnectionReconnecting;
        public event Action ConnectionReconnected;
        public event Action ConnectionClosed;
        private IHubProxy hubProxy;
        private HubConnection connection;
        private string url = ConfigurationManager.AppSettings["SignalRServerUrl"];

        public Task ConnectAsync()
        {
            connection = new HubConnection(url);
            hubProxy = connection.CreateHubProxy("WorkHub");
            hubProxy.On<string, string>("BroadcastTextMessage", (n, m) => NewTextMessage?.Invoke(n, m, MessageType.Broadcast));
            connection.Reconnecting += Reconnecting;
            connection.Reconnected += Reconnected;
            connection.Closed += Disconnected;

            ServicePointManager.DefaultConnectionLimit = 10;
            return connection.Start();
        }

        public void Disconnect()
        {
            connection.Stop();
            connection.Dispose();
        }
        private void Disconnected()
        {
            ConnectionClosed?.Invoke();
        }

        private void Reconnected()
        {
            ConnectionReconnected?.Invoke();
        }

        private void Reconnecting()
        {
            ConnectionReconnecting?.Invoke();
        }


        /// <summary>
        /// 客户端向服务端登录
        /// </summary>
        /// <param name="pickStationCode"></param>
        /// <param name="userId"></param>
        /// <param name="userCode"></param>
        /// <param name="userName"></param>
        /// <param name="userRole"></param>
        /// <returns></returns>
        public Task<bool> LoginAsync(string pickStationCode,
            int userId,
            string userCode,
            string userName,
            string userRole)
        {
            return hubProxy.Invoke<bool>("Login", new object[] { pickStationCode, userId, userCode, userName, userRole });
        }

        /// <summary>
        /// 客户端登出
        /// </summary>
        /// <returns></returns>
        public Task LogoutAsync()
        {
            return hubProxy.Invoke("Logout");
        }

        /// <summary>
        /// 发送消息
        /// </summary>
        /// <param name="msg"></param>
        /// <returns></returns>
        public Task SendBroadcastMessageAsync(string msg)
        {
            return hubProxy.Invoke("BroadcastTextMessage", msg);
        }
    }
}
