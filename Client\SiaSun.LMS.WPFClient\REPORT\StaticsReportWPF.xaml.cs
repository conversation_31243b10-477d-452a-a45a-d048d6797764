﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using Microsoft.Reporting.WinForms;
using System.Data.SqlClient;
using System.Collections;

namespace SiaSun.LMS.WPFClient.REPORT
{
    /// <summary>
    /// StaticsReportWPF.xaml 的交互逻辑
    /// </summary>
    public partial class StaticsReportWPF : AvalonDock.DocumentContent
    {
        #region 定义全局变量

        public string StrSQL = string.Empty;

        public string StrWhere = string.Empty;

        public string ReportType = string.Empty;

        public string GoodsType = string.Empty;

        public string ReportTitle = string.Empty;

        public string ReportDSName = string.Empty;

        public string StrFromDate = string.Empty;

        public string StrToDate = string.Empty;

        public string StrFromDateBefore = string.Empty;

        public string StrToDateBefore = string.Empty;

        public string StrFromDateNow = string.Empty;

        public string StrToDateNow = string.Empty;

        public string StrYear = string.Empty;

        DataTable dtReport = null;

        Microsoft.Reporting.WinForms.ReportViewer report;

        #endregion

        public StaticsReportWPF()
        {
            InitializeComponent();
            
        }

        public StaticsReportWPF(string strReportType, DataTable DataSource)
        {
            InitializeComponent();
            this.dtReport = DataSource;
            this.ReportType = strReportType;
        }

        private void Grid_Loaded(object sender, RoutedEventArgs e)
        {
            //利用WINFORM的ReportViewer
            report = this.windowsFormsHost1.Child as Microsoft.Reporting.WinForms.ReportViewer;
            report.PrinterSettings.DefaultPageSettings.Margins = new System.Drawing.Printing.Margins(10, 20, 10, 20);

            //动态绑定combobox
            GetCombobox();

            //取本月的第一天
            this.datePickerFrom.SelectedDate = DateTime.Now.AddDays(1-DateTime.Now.Day);
            this.datePickerTo.SelectedDate = DateTime.Now;

        }

        //动态绑定combobox
        private void GetCombobox()
        {
            //动态绑定combobox
            this.cmbReportType.ItemsSource = MainApp._I_BaseService.GetList("SELECT * FROM REPORT_DATA").DefaultView;

            this.cmbReportType.DisplayMemberPath = "ReportName";

            this.cmbReportType.SelectedValuePath = "ID";

            this.cmbContract.ItemsSource = MainApp._I_BaseService.GetList("select distinct goods_const_property3 as value,goods_const_property3 as name from goods_main where goods_const_property3 is not null order by goods_const_property3 ").DefaultView;

            this.cmbContract.DisplayMemberPath = "value";

            this.cmbContract.SelectedValuePath = "name";
        }

        /// <summary>
        /// 查询
        /// </summary>
        private void btnQuery_Click(object sender, RoutedEventArgs e)
        {
            if (this.cmbReportType.SelectedItem == null)
            {
                MessageBox.Show("请选择报表类型");
                return;
            }
            if (this.datePickerFrom.SelectedDate.Value == null || this.datePickerTo.SelectedDate.Value == null)
            {
                MessageBox.Show("请选择日期");
                return;
            }
            try
            {
                GetReportData();
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "查询出错");
                return;
            }
        }

        /// <summary>
        /// 下拉列表
        /// </summary>
        private void GetReportData()
        {
            
            this.StrFromDate = this.datePickerFrom.SelectedDate.Value.ToString("yyyy-MM-dd 00:00:00");

            this.StrToDate = this.datePickerTo.SelectedDate.Value.ToString("yyyy-MM-dd 24:00:00");


            DataTable dt = new DataTable();

            //根据combobox的ID取到REPORT_DATA表中的一行数据
            dt = MainApp._I_BaseService.GetList(string.Format("SELECT * FROM REPORT_DATA WHERE ID ={0}",this.cmbReportType.SelectedIndex));

            //取到一行数据中的标题，报表页,数据集，存储过程名，出入库类型，物料
            this.GetRecordListInOut(dt.Rows[0]["Title"].ToString(), dt.Rows[0]["rdlc"].ToString(), dt.Rows[0]["Ds"].ToString(),
                                     dt.Rows[0]["StorageName"].ToString(),this.cmbContract.Text.ToString(),Convert.ToInt32(dt.Rows[0]["GoodsId"]));

        }

        /// <summary>
        /// 调用存储过程
        /// </summary>
        /// <returns></returns>
        public DataTable GetDataTable(string storedProcedure,string plan_type,int goods_type_id)
        {
            SqlConnection conn = new SqlConnection();
            //conn.ConnectionString = @SiaSun.LMS.Common.StringUtil.GetConfig("strConnection");//连接数据库

            conn.ConnectionString = MainApp._I_BaseService.MessageConverter_GetKeyValue("ConnectionString");
            conn.Open();
            SqlCommand cmd = new SqlCommand(storedProcedure, conn);//其中Proc为存储过程名称
            
            //添加变量
            cmd.Parameters.Add("@begintime", SqlDbType.VarChar).Value = this.StrFromDate;
            cmd.Parameters.Add("@endtime", SqlDbType.VarChar).Value = this.StrToDate;
            cmd.Parameters.Add("@plantype", SqlDbType.VarChar).Value = plan_type;
            cmd.Parameters.Add("@goodstypeid", SqlDbType.Int).Value = goods_type_id;

            cmd.CommandType = CommandType.StoredProcedure;//指定执行类型为存储过程
            DataTable dt = new DataTable();
            //执行存储过程
            SqlDataAdapter sda = new SqlDataAdapter(cmd);
            //将结果填充到datatable中
            sda.Fill(dt);
            
            return dt;//返回Datatable
        }

        /// <summary>
        /// 物料入/出库统计
        /// </summary>
        private void GetRecordListInOut(string reportTitle, string reportResource, string reportDSName,string storedProcedure,string plan_type, int goods_type_id)
        {
            //设置报表页眉内容
            this.ReportTitle = reportTitle;

            //为报表查看器提供报表文件
            this.report.LocalReport.ReportEmbeddedResource = reportResource;

            //设置参数，参数名和参数值
            ReportParameter paratitle = new ReportParameter("ParaTitle", this.ReportTitle);
            this.report.LocalReport.SetParameters(new ReportParameter[] { paratitle });

            //设置数据源，数据集名称和datatable
            this.report.LocalReport.DataSources.Clear();

            //数据集名称
            this.ReportDSName = reportDSName;

            ReportDataSource rdsDATA = new ReportDataSource(this.ReportDSName, GetDataTable(storedProcedure,plan_type, goods_type_id));

            this.report.LocalReport.DataSources.Add(rdsDATA);

            //显示报表
            this.report.RefreshReport();
        }

        /// <summary>
        /// 重置
        /// </summary>
        private void btnReset_Click(object sender, RoutedEventArgs e)
        {
            this.cmbReportType.SelectedValue = -1;
        }

    }
}
