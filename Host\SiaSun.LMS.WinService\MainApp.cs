﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.WinService
{
    public static class MainApp
    {
        public static SiaSun.LMS.Interface.I_BaseService _I_BaseService = new SiaSun.LMS.Implement.S_BaseService();

        //wdz add 2017-08-16
        public static SiaSun.LMS.Implement.S_BaseService BaseService = new SiaSun.LMS.Implement.S_BaseService();
        //public static SiaSun.LMS.Implement.S_CellService CellService = new SiaSun.LMS.Implement.S_CellService();
        //public static SiaSun.LMS.Implement.S_FlowService FlowService = new SiaSun.LMS.Implement.S_FlowService();
        //public static SiaSun.LMS.Implement.S_GoodsService GoodsService = new SiaSun.LMS.Implement.S_GoodsService();
        //public static SiaSun.LMS.Implement.S_LEDService LEDService = new SiaSun.LMS.Implement.S_LEDService();
        //public static SiaSun.LMS.Implement.S_ManageService ManageService = new SiaSun.LMS.Implement.S_ManageService();
        //public static SiaSun.LMS.Implement.S_PDAService PDAService = new SiaSun.LMS.Implement.S_PDAService();
        //public static SiaSun.LMS.Implement.S_PlanService PlanService = new SiaSun.LMS.Implement.S_PlanService();
        //public static SiaSun.LMS.Implement.S_StorageService S_StorageService = new SiaSun.LMS.Implement.S_StorageService();
        //public static SiaSun.LMS.Implement.S_SystemService S_SystemService = new SiaSun.LMS.Implement.S_SystemService();

        //从数据库中读取系统参数
        //public static Dictionary<string, string> _SysParameter;

    }
}
