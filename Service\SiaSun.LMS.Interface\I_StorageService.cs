﻿using System;
using System.Collections.Generic;

using System.Text;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.STORAGE_LIST))]
    [ServiceKnownType(typeof(DataTable))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.T_PICK_POSITION))]
    [ServiceKnownType(typeof(List<SiaSun.LMS.Model.T_PICK_POSITION>))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.T_PICK_STATION))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.STORAGE_MAIN))]
    [ServiceKnownType(typeof(List<SiaSun.LMS.Model.STORAGE_LIST>))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.T_PICK_POSITION_PLAN_BIND))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_DETAIL))]
    [ServiceKnownType(typeof(List<SiaSun.LMS.Model.MANAGE_DETAIL>))]

    public partial interface I_StorageService
    {
        [OperationContract]
        bool StorageCheck(string STOCK_BARCODE,
                          string AREA_TYPE,
                          out string sResult);

        [OperationContract]
        Model.T_PICK_STATION Get_T_PICK_STATION_BY_IP(string ip, out string sResult);

        [OperationContract]
        List<Model.T_PICK_POSITION> Get_T_PICK_POSITION_BY_STATION_ID(int STATION_ID, out string sResult);

        [OperationContract]
        bool Get_PICK_STATION_STORAGE_BY_STATION_ID(int STATION_ID, out Model.STORAGE_MAIN mSTORAGE_MAIN, out List<Model.STORAGE_LIST> mSTORAGE_LISTS,out MANAGE_MAIN mMANAGE_MAIN,out List<MANAGE_LIST> mMANAGE_LISTS,  out string sResult);

        [OperationContract]
        Model.STORAGE_MAIN Get_STORAGE_MAIN_BY_CELL_ID(int WH_CELL_ID);

        /// <summary>
        /// 设置库存是否异常标志
        /// wdz add 2018-04-18
        /// </summary>
        [OperationContract]
        bool SetStorageException(Model.SYS_USER user, string stockBarcode, bool isException, out string sResult);

        /// <summary>
        /// 设置物料优先出库
        /// wdz add 2018-05-01
        /// </summary>
        [OperationContract]
        bool SetGoodsPriorOut(Model.SYS_USER user, string stockBarcode, string goodsCode, bool isPriorOut, out string sResult);




        [OperationContract]
        Model.T_PICK_POSITION_PLAN_BIND Get_T_PICK_POSITION_PLAN_BIND_BY_PICK_POSITION_ID(int PICK_POSITION_ID);

        [OperationContract]
        bool BindOrderCodeToPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyima, out string sResult);

        /// <summary>
        /// 2024改造
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickStationID"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        [OperationContract]
        bool BindOrderCodeToPickStationNew2024(Model.SYS_USER mSYS_USER, int PickStationID, string weiyima, out string sResult);


        [OperationContract]
        bool UnBindOrderPickStation(Model.SYS_USER mSYS_USER, int PickStationID,out string weiyima, out string sResult);

        [OperationContract]
        bool LockAndOutOrderByPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyima, out string sResult);

        [OperationContract]
        bool LockAndOutOrderPartlyByPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyima, out string sResult);

        [OperationContract]
        bool ManageDownOutByPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyima, out string sResult);


        [OperationContract]
        bool CreateManageDetailsForManagePick(Model.SYS_USER mSYS_USER, List<Model.MANAGE_DETAIL> lsMANAGE_DETAIL, out string sResult);


        [OperationContract]
        bool ErrorOperationUnBindOrder(Model.SYS_USER mSYS_USER, int PickStationID, out string weiyima, out string sResult);

        [OperationContract]
        bool ErrorOperationUnLock(Model.SYS_USER mSYS_USER, int PickStationID, out string weiyima, out string sResult);

        [OperationContract]
        bool PrevBindOrderCodeToPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyima, out string sResult);

        [OperationContract]
        bool OperatePickStationBindOrder(out string sResult);

        [OperationContract]
        bool UnAtuoBindOrderPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyimaUnBind, out string sResult);

        [OperationContract]
        bool UnAtuoBindOrderPickStationNew2024(Model.SYS_USER mSYS_USER, int PickStationID, string weiyimaUnBind, out string sResult);

        [OperationContract]
        bool AgentAutoBindOrder(Model.SYS_USER mSYS_USER, string weiyima, out string sResult);




        [OperationContract]
        bool UnBindOrderPickPosition(Model.SYS_USER mSYS_USER, int PickPositionID, out string weiyima, out string sResult);

        [OperationContract]
        bool LockAndOutOrderByPickPosition(Model.SYS_USER mSYS_USER, int PickPositionID, string weiyima, out string sResult);

        [OperationContract]
        bool LockAndOutOrderPartlyByPickPosition(Model.SYS_USER mSYS_USER, int PickPositionID, string weiyima, out string sResult);

        [OperationContract]
        bool ManageDownOutByPickPosition(Model.SYS_USER mSYS_USER, int PickPositionID, string weiyima, out string sResult);

        [OperationContract]
        bool ErrorOperationUnBindOrderPosition(Model.SYS_USER mSYS_USER, int PickPositionID, out string weiyima, out string sResult);

    }
}
