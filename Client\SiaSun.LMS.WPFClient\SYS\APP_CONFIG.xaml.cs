﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;
using System.Xml;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// APP_CONFIG.xaml 的交互逻辑
    /// </summary>
    public partial class APP_CONFIG : AvalonDock.DocumentContent
    {
        string strAppName = null;
        DataTable tableApp = null;
        SiaSun.LMS.Common.XmlFiles xmlDoc = null;

        public APP_CONFIG()
        {
            InitializeComponent();
            this.Loaded += new RoutedEventHandler(APP_CONFIG_Loaded);
        }

        void APP_CONFIG_Loaded(object sender, RoutedEventArgs e)
        {
            //加载数据
            LoadData();
        }

        /// <summary>
        /// 加载数据
        /// </summary>
        private void LoadData()
        {
            //初始化表集合
            if (tableApp == null)
            {
                tableApp = new DataTable();
                tableApp.Columns.Add("key");
                tableApp.Columns.Add("value");
                //设置唯一约束
                tableApp.Columns[0].Unique = true;
            }

            //清除数据
            tableApp.Rows.Clear();

            //加载数据
            try
            {
                strAppName = System.Reflection.Assembly.GetExecutingAssembly().GetName().Name + ".exe.config";
                xmlDoc = new SiaSun.LMS.Common.XmlFiles(strAppName);
                if (xmlDoc != null)
                {
                    //找到所有的添加节点
                    foreach (System.Xml.XmlNode node in xmlDoc.SelectNodes("/configuration/appSettings/add"))
                    {
                        tableApp.Rows.Add(node.Attributes[0].InnerText, node.Attributes[1].InnerText);
                    }
                }

                //接受更改
                tableApp.AcceptChanges();

                //设置数据源
                this.gridApp.ItemsSource = tableApp.DefaultView;
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 添加配置信息
        /// </summary>
        private void AddConfig(XmlNode nodeParent, DataRow rowApp)
        {
            //判断值类型
            if (rowApp.IsNull(rowApp.Table.Columns[0]) || string.IsNullOrEmpty(rowApp[0].ToString()))
            {
                MainApp._MessageDialog.Show("App_CheckKey", null);
                return;
            }

            XmlNode nodeAdd = nodeParent.AppendChild(xmlDoc.CreateNode(XmlNodeType.Element, "add", nodeParent.NamespaceURI));
            nodeAdd.Attributes.Append(xmlDoc.CreateAttribute("key")).InnerText = rowApp[0].ToString();
            nodeAdd.Attributes.Append(xmlDoc.CreateAttribute("value")).InnerText = rowApp[1].ToString();
        }

        /// <summary>
        /// 更新配置信息
        /// </summary>
        private void UpdateConfig(XmlNode nodeParent, DataRow rowApp)
        {
            //判断值类型
            if (rowApp.IsNull(rowApp.Table.Columns[0]) || string.IsNullOrEmpty(rowApp[0].ToString()))
            {
                MainApp._MessageDialog.Show("App_CheckKey", null);
                return;
            }
            XmlNode nodeFind = nodeParent.SelectSingleNode("//add[@key='" + rowApp[0].ToString() + "']");
            if (nodeFind != null)
            {
                nodeFind.Attributes[1].InnerText = rowApp[1].ToString();
            }
        }

        /// <summary>
        /// 删除配置信息
        /// </summary>
        private void DeleteConfig(XmlNode nodeParent, DataRow rowApp)
        {
            XmlNode nodeFind = nodeParent.SelectSingleNode("//add[@key='" + rowApp[0, DataRowVersion.Original].ToString() + "']");
            if (nodeFind != null)
            {
                nodeParent.RemoveChild(nodeFind);
            }
        }

        /// <summary>
        /// 保存
        /// </summary>
        private void btnSave_Click(object sender, RoutedEventArgs e)
        {
            if (xmlDoc == null)
                return;

            //提交保存
            this.gridApp.CommitEdit(DataGridEditingUnit.Row, true);

            if (MainApp._MessageDialog.ShowDialog("Save",null)== Sid.Windows.Controls.TaskDialogResult.Ok)
            {
                DataTable tableChanages = tableApp.GetChanges();
                if (tableChanages != null)
                {
                    //获得上级节点
                    XmlNode nodeTop = xmlDoc.FindNode("configuration/appSettings");
                    if (nodeTop != null)
                    {
                        foreach (DataRow rowApp in tableChanages.Rows)
                        {
                            //判断状态
                            if (rowApp.RowState == DataRowState.Added)         //添加
                            {
                                AddConfig(nodeTop, rowApp);
                            }
                            else if (rowApp.RowState == DataRowState.Modified)  //保存更新
                            {
                                UpdateConfig(nodeTop, rowApp);
                            }
                            else if (rowApp.RowState == DataRowState.Deleted)    //删除
                            {
                                DeleteConfig(nodeTop, rowApp);
                            }
                        }

                        //保存更新
                        xmlDoc.Save(strAppName);
                    }
                }
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void btnUpdate_Click(object sender, RoutedEventArgs e)
        {
            //加载数据
            this.LoadData();
        }
    }
}
