﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.GOODS.GOODS_CLASS_EDIT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="GOODS_CLASS_EDIT" Height="300" Width="600" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <uc:ucTreeView x:Name="tvwGoodsClass" Grid.Column="0"></uc:ucTreeView>
        <GridSplitter Grid.Column="1" Grid.RowSpan="2" VerticalAlignment="Stretch"  FlowDirection="LeftToRight"></GridSplitter>
        <GroupBox Name="grpBoxMenu" Grid.Column="2"  Tag=" {0}-菜单信息" Header="菜单信息">
            <Grid>
                <uc:ucCommonDataGrid Grid.Row="0" x:Name="gridGoodsClass" Margin="1" U_AllowPage="False"></uc:ucCommonDataGrid>
            </Grid>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
