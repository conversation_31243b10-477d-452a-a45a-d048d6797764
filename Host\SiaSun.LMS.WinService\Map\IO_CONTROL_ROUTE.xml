﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="IO_CONTROL_ROUTE" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="IO_CONTROL_ROUTE" type="SiaSun.LMS.Model.IO_CONTROL_ROUTE, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="IO_CONTROL_ROUTE">
			<result property="CONTROL_ROUTE_ID" column="control_route_id" />
			<result property="CONTROL_ROUTE_TYPE" column="control_route_type" />
			<result property="CONTROL_ROUTE_CODE" column="control_route_code" />
			<result property="CONTROL_ROUTE_NAME" column="control_route_name" />
			<result property="START_DEVICE" column="start_device" />
			<result property="END_DEVICE" column="end_device" />
			<result property="CONTROL_ROUTE_STATUS" column="control_route_status" />
			<result property="CONTROL_ROUTE_REMARK" column="control_route_remark" />
      <result property="CONTROL_ROUTE_MANAGE" column="control_route_manage" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="IO_CONTROL_ROUTE_SELECT" parameterClass="int" resultMap="SelectResult">
        Select
          control_route_id,
          control_route_type,
          control_route_code,
          control_route_name,
          start_device,
          end_device,
          control_route_status,
          control_route_remark,
          control_route_manage
        From IO_CONTROL_ROUTE
      </select>
		
		<select id="IO_CONTROL_ROUTE_SELECT_BY_ID" parameterClass="int" extends = "IO_CONTROL_ROUTE_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					control_route_id=#CONTROL_ROUTE_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="IO_CONTROL_ROUTE_SELECT_BY_START_DEVICE_END_DEVICE" parameterClass="Hashtable" extends = "IO_CONTROL_ROUTE_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          start_device=#START_DEVICE# and end_device=#END_DEVICE# AND CONTROL_ROUTE_STATUS = '1' and CONTROL_ROUTE_MANAGE=1
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="IO_CONTROL_ROUTE_INSERT" parameterClass="IO_CONTROL_ROUTE">
      Insert Into IO_CONTROL_ROUTE (
      control_route_id,
      control_route_type,
      control_route_code,
      control_route_name,
      start_device,
      end_device,
      control_route_status,
      control_route_remark,
      control_route_manage
      )Values(
      #CONTROL_ROUTE_ID#,
      #CONTROL_ROUTE_TYPE#,
      #CONTROL_ROUTE_CODE#,
      #CONTROL_ROUTE_NAME#,
      #START_DEVICE#,
      #END_DEVICE#,
      #CONTROL_ROUTE_STATUS#,
      #CONTROL_ROUTE_REMARK#,
      #CONTROL_ROUTE_MANAGE#
      )
      <!--<selectKey  resultClass="int" type="post" property="CONTROL_ROUTE_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="IO_CONTROL_ROUTE_UPDATE" parameterClass="IO_CONTROL_ROUTE">
      Update IO_CONTROL_ROUTE Set
      control_route_id=#CONTROL_ROUTE_ID#,
      control_route_type=#CONTROL_ROUTE_TYPE#,
      control_route_code=#CONTROL_ROUTE_CODE#,
      control_route_name=#CONTROL_ROUTE_NAME#,
      start_device=#START_DEVICE#,
      end_device=#END_DEVICE#,
      control_route_status=#CONTROL_ROUTE_STATUS#,
      control_route_remark=#CONTROL_ROUTE_REMARK#,
      control_route_manage=#CONTROL_ROUTE_MANAGE#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					control_route_id=#CONTROL_ROUTE_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="IO_CONTROL_ROUTE_DELETE" parameterClass="int">
			Delete From IO_CONTROL_ROUTE
			<dynamic prepend="WHERE">
				<isParameterPresent>
					control_route_id=#CONTROL_ROUTE_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>