﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Windows.Forms;
using System.Data;
using System.Collections;
using System.Xml;

namespace SSLMS.MobileUI
{
    public class Common
    {
        /// <summary>
        /// 获得分离字符串中的某组字符
        /// </summary>
        /// <param name="ControlName"></param>
        /// <param name="ch"></param>
        /// <param name="index"></param>
        /// <returns></returns>
        public static string GetSplitStr(string ControlName, char[] ch, int index)
        {
            string[] arrTmp = ControlName.Split(ch);

            return arrTmp[(1 == arrTmp.Length) ? 0 : index];
        }

        public static Control GetControl(Control ctrl, string cName)
        {
            Control ctlChooseResult = new Control();

            Control ctlResult = new Control();

            foreach (Control c in ctrl.Controls)
            {
                if (c.Name == cName)
                {
                    ctlChooseResult = c;
                }
                else
                {
                    if (c.Controls.Count != 0)
                    {
                        ctlChooseResult = GetControl(c, cName);
                    }
                }
                if (ctlChooseResult.Name == cName)
                {
                    ctlResult = ctlChooseResult;

                    break;
                }
            }

            return ctlResult;
        }

        public static string GetUrl(string sKey)
        {
            string strUrl = string.Empty;

            try
            {
                XmlDocument xd = new XmlDocument();

                xd.Load(System.IO.Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().GetName().CodeBase) + @"\App.config");

                XmlElement xe = xd.DocumentElement;

                XmlNode xn = xe.SelectSingleNode(string.Format("/Fields/Table[@Name='{0}']", sKey));

                XmlNodeList xnl = xn.ChildNodes;

                foreach (XmlNode xmlField in xnl)
                {
                    strUrl = xmlField.Attributes["Name"].InnerText.Trim();
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message, "服务器地址读取错误,请检查App.config");

                Application.Exit();
            }

            return strUrl;
        }

        public static DataTable AddAll(DataTable dt, Hashtable ht)
        {
            if (0 == ht.Count)
            {
                ht.Add("name", "-");

                ht.Add("value", "");
            }

            DataTable dtResult = dt;

            if (null == dtResult)
            {
                dtResult = new DataTable();

                foreach (DictionaryEntry d in ht)
                {
                    dtResult.Columns.Add(d.Key.ToString());
                }
            }

            if (0 == dtResult.Columns.Count)
            {
                dtResult = new DataTable();

                foreach (DictionaryEntry d in ht)
                {
                    dtResult.Columns.Add(d.Key.ToString());
                }
            }

            DataRow dr = dtResult.NewRow();

            foreach (DictionaryEntry d in ht)
            {
                dr[d.Key.ToString()] = d.Value;
            }

            dtResult.Rows.InsertAt(dr, 0);

            return dtResult;
        }
    }
}
