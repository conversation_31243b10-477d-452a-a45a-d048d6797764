﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucSotckOut"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="167" d:DesignWidth="229">
    <Grid>

        <Border Margin="0,2,0,0">
            <WrapPanel>
                <StackPanel Name="panelStockType" Orientation="Horizontal" Margin="5,2,5,2" Visibility="Collapsed">
                    <TextBlock VerticalAlignment="Center" MinWidth="80">托盘类型:</TextBlock>
                    <ComboBox Name="cmbStockType" Margin="2,0,2,0" MinWidth="120" MinHeight="23"></ComboBox>
                </StackPanel>

                <StackPanel Name="panelStockInType" Orientation="Horizontal" Margin="5,2,5,2">
                    <TextBlock MinWidth="80" VerticalAlignment="Center">出库方式:</TextBlock>
                    <RadioButton Name="rbManual"   Content="人工"  Checked="rbSingle_Checked" VerticalAlignment="Center"></RadioButton>
                    <RadioButton Name="rbAuto"  Content="自动" Checked="rbSingle_Checked" VerticalAlignment="Center"></RadioButton>
                </StackPanel>



                <StackPanel Name="panelStockQuantity" Orientation="Horizontal" Margin="5,2,5,2">
                    <TextBlock VerticalAlignment="Center" MinWidth="80">需求数量:</TextBlock>
                    <TextBox Name="txtStockQuantity" Margin="2,0,2,0" MinWidth="120" MinHeight="23" Text="1"></TextBox>
                </StackPanel>

            </WrapPanel>
        </Border>

    </Grid>
</UserControl>
