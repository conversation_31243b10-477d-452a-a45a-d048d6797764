﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace SignalServer.Models
{
    public class Client : INotifyPropertyChanged
    {
        private int pickStationID;
        private string pickStationCode;
        private string pickStationName;
        private int loginUserID;
        private string loginUserCode;
        private string loginUserName;
        private string loginUserRole;
        private string status;

        private string connectID;
        private bool isLogin;

        public event PropertyChangedEventHandler PropertyChanged;

        public int PickStationID
        {
            get
            {
                return pickStationID;
            }

            set
            {
                if (pickStationID != value)
                {
                    pickStationID = value;
                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("PickStationID"));
                    }
                }

            }
        }

        public string PickStationCode
        {
            get
            {
                return pickStationCode;
            }

            set
            {
                if (pickStationCode != value)
                {
                    pickStationCode = value;
                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("PickStationCode"));
                    }
                }
            }
        }

        public string PickStationName
        {
            get
            {
                return pickStationName;
            }

            set
            {
                if (pickStationName != value)
                {
                    pickStationName = value;
                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("pickStationName"));
                    }
                }
            }
        }

        public int LoginUserID
        {
            get
            {
                return loginUserID;
            }
            set
            {
                if (loginUserID != value)
                {
                    loginUserID = value;

                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("LoginUserID"));
                    }
                }
            }
        }

        public string LoginUserCode
        {
            get
            {
                return loginUserCode;
            }

            set
            {
                if (loginUserCode != value)
                {
                    loginUserCode = value;

                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("LoginUserCode"));
                    }
                }
            }
        }

        public string LoginUserName
        {
            get
            {
                return loginUserName;
            }

            set
            {
                if (loginUserName != value)
                {
                    loginUserName = value;

                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("LoginUserName"));
                    }
                }
            }
        }

        public string LoginUserRole
        {
            get
            {
                return loginUserRole;
            }

            set
            {
                if (loginUserRole != value)
                {
                    loginUserRole = value;

                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("loginUserRole"));
                    }
                }
            }
        }

        public string Status
        {
            get
            {
                return status;
            }

            set
            {
                if (status != value)
                {
                    status = value;

                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("Status"));
                    }
                }
            }
        }

        public bool IsLogin
        {
            get
            {
                return this.isLogin;
            }
            set
            {
                if (isLogin != value)
                {
                    isLogin = value;

                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("IsLogin"));
                    }
                }
            }
        }

        public string ConnectID
        {
            get
            {
                return connectID;
            }

            set
            {
                if (connectID != value)
                {
                    connectID = value;


                    if (PropertyChanged != null)
                    {
                        this.PropertyChanged.Invoke(this, new PropertyChangedEventArgs("ConnectID"));
                    }
                }
            }
        }
    }
}
