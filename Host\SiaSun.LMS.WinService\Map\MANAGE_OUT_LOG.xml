﻿<?xml version="1.0" encoding="utf-8" ?>
<sqlMap namespace="MANAGE_OUT_LOG" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="MANAGE_OUT_LOG" type="SiaSun.LMS.Model.MANAGE_OUT_LOG, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="MANAGE_OUT_LOG">
      <result property="MANAGE_OUT_LOG_ID" column="manage_out_log_id" />
      <result property="MANAGE_ID" column="manage_id" />
      <result property="MANAGE_OUT_LOG_STOCK" column="manage_out_log_stock" />
      <result property="MANAGE_OUT_LOG_GOODS" column="manage_out_log_goods" />
      <result property="MANAGE_OUT_LOG_DATE" column="manage_out_log_date" />
      <result property="MANAGE_OUT_LOG_FLAG" column="manage_out_log_flag" />
      <result property="MANAGE_OUT_LOG_REMARK" column="manage_out_log_remark" />
    </resultMap>
  </resultMaps>
  <statements>

    <select id="MANAGE_OUT_LOG_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      manage_out_log_id,
      manage_id,
      manage_out_log_stock,
      manage_out_log_goods,
      manage_out_log_date,
      manage_out_log_flag,
      manage_out_log_remark
      From MANAGE_OUT_LOG
    </select>

    <select id="MANAGE_OUT_LOG_SELECT_BY_ID" parameterClass="int" extends = "MANAGE_OUT_LOG_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_out_log_id=#MANAGE_OUT_LOG_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="MANAGE_OUT_LOG_SELECT_BY_MANAGE_ID" parameterClass="int" extends = "MANAGE_OUT_LOG_SELECT" resultMap="SelectResult">
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_id=#MANAGE_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <insert id="MANAGE_OUT_LOG_INSERT" parameterClass="MANAGE_OUT_LOG">
      Insert Into MANAGE_OUT_LOG (
      manage_out_log_id,
      manage_id,
      manage_out_log_stock,
      manage_out_log_goods,
      manage_out_log_date,
      manage_out_log_flag,
      manage_out_log_remark
      )Values(
      #MANAGE_OUT_LOG_ID#,
      #MANAGE_ID#,
      #MANAGE_OUT_LOG_STOCK#,
      #MANAGE_OUT_LOG_GOODS#,
      #MANAGE_OUT_LOG_DATE#,
      #MANAGE_OUT_LOG_FLAG#,
      #MANAGE_OUT_LOG_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="MANAGE_OUT_LOG_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="MANAGE_OUT_LOG_UPDATE" parameterClass="MANAGE_OUT_LOG">
      Update MANAGE_OUT_LOG Set
      manage_out_log_id=#MANAGE_OUT_LOG_ID#,
      manage_id=#MANAGE_ID#,
      manage_out_log_stock=#MANAGE_OUT_LOG_STOCK#,
      manage_out_log_goods=#MANAGE_OUT_LOG_GOODS#,
      manage_out_log_date=#MANAGE_OUT_LOG_DATE#,
      manage_out_log_flag=#MANAGE_OUT_LOG_FLAG#,
      manage_out_log_remark=#MANAGE_OUT_LOG_REMARK#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_out_log_id=#MANAGE_OUT_LOG_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="MANAGE_OUT_LOG_DELETE" parameterClass="int">
      Delete From MANAGE_OUT_LOG
      <dynamic prepend="WHERE">
        <isParameterPresent>
          manage_out_log_id=#MANAGE_OUT_LOG_ID#
        </isParameterPresent>
      </dynamic>
    </delete>


  </statements>
</sqlMap>
