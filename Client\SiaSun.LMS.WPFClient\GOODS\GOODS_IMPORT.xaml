﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.PLAN.GOODS_IMPORT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="PLAN_IMPORT" Height="215" Width="475" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <GroupBox Grid.Row="0"  Header="导入计划信息" Tag="导入{0}计划信息">
            <uc:DataGridTemplate x:Name="gridImport" AutoGenerateColumns="True" Grid.Row="0" Margin="1,1,1,0" IsReadOnly="True"></uc:DataGridTemplate>
        </GroupBox>
        <GroupBox Grid.Row="1"  Header="操作区">
            <Border >
                <WrapPanel HorizontalAlignment="Center" Margin="3" ButtonBase.Click="WrapPanel_Click">
                    <Button Name="btnImport"  Width="80">打开EXCEL</Button>
                    <Button Name="btnSave"  Width="80">保存</Button>
                </WrapPanel>
            </Border>
        </GroupBox>
    </Grid>
</ad:DocumentContent>
