﻿<UserControl x:Class="SiaSun.LMS.WPFClient.PICK.View.Information"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:local="clr-namespace:SiaSun.LMS.WPFClient.PICK.View"
             mc:Ignorable="d"
             MinHeight="27" MinWidth="100">
    <Grid>
        <WrapPanel>
            <StackPanel Orientation="Horizontal" Margin="5">
                <Label Margin="2">lbl1</Label>
                <TextBlock Margin="2">tb1</TextBlock>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="5">
                <Label Margin="2">lbl2</Label>
                <TextBlock Margin="2">tb2</TextBlock>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="5">
                <Label Margin="2">lbl3</Label>
                <TextBlock Margin="2">tb3</TextBlock>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="5">
                <Label Margin="2">lbl4</Label>
                <TextBlock Margin="2">tb4</TextBlock>
            </StackPanel>
            <StackPanel Orientation="Horizontal" Margin="5">
                <Label Margin="2">lbl5</Label>
                <TextBlock Margin="2">tb5</TextBlock>
            </StackPanel>
        </WrapPanel>
    </Grid>
</UserControl>
