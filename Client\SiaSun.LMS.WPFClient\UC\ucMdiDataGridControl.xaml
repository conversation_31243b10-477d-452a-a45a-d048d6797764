﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucMdiDataGridControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             d:DesignHeight="300" d:DesignWidth="600">
    <Grid Name="gridLayOut">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="1.5*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        <GroupBox Name="grpboxParent" Header="请选择{0}记录" Grid.Column="0" Margin="2">
            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*" ></RowDefinition>
                </Grid.RowDefinitions>
                <Grid>
                    <Border Margin="2" BorderThickness="1" BorderBrush="DarkGray">
                        <uc:ucQuickQuery x:Name="ucQueryQuick" Margin="1"></uc:ucQuickQuery>
                    </Border>
                </Grid>
                <uc:ucCommonDataGrid x:Name="gridParent" Grid.Row="1" U_AllowPage="False" Margin="1" ></uc:ucCommonDataGrid>
            </Grid>
        </GroupBox>
        <GridSplitter Grid.Column="1" VerticalAlignment="Stretch" FlowDirection="LeftToRight"></GridSplitter>
        <GroupBox Name="grpboxChild" Tag=" {0}" Header="子列表"   Grid.Column="2" Margin="2">
            <uc:ucCommonDataGrid x:Name="gridChild"  U_AllowPage="False" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>
    </Grid>
</UserControl>
