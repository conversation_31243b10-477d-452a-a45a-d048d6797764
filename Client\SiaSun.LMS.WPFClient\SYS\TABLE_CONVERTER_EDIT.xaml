﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.SYS.TABLE_CONVERTER_EDIT"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
         xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="ImportExcel" Height="300" Width="467" Loaded="DocumentContent_Loaded">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="2*"></RowDefinition>
        </Grid.RowDefinitions>

        <uc:ucQuickQuery x:Name="ucQkQuery" Grid.Row="0" MinHeight="40"></uc:ucQuickQuery>
        <GroupBox Grid.Row="1"  Header="结构转换信息" Margin="1">
            <uc:ucCommonDataGrid x:Name="gridTableConverter" ></uc:ucCommonDataGrid>
        </GroupBox>
        <GridSplitter Grid.Row="2" HorizontalAlignment="Stretch" Height="2"  ></GridSplitter>
        
        <GroupBox Grid.Row="3" Header="结构转换信息列表" Margin="1">
            <uc:ucCommonDataGrid x:Name="gridTableConverterList"></uc:ucCommonDataGrid>            
        </GroupBox>

    </Grid>
</ad:DocumentContent>
