﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Text.RegularExpressions;

namespace SSLMS.MobileUI.uc
{
    public partial class ucStockIn : UserControl
    {
        private ucManagePosition ucMANAGE_POSITION = new ucManagePosition();

        MANAGE_TYPE mMANAGE_TYPE = null;

        public ucStockIn(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            ucMANAGE_POSITION.Dock = DockStyle.Fill;

            this.ucMANAGE_POSITION.U_SaveButtonClicked += new ucManagePosition.U_SaveButtonClickHandler(ucMANAGE_POSITION_U_SaveButtonClicked);

            this.panelPosition.Controls.Add(ucMANAGE_POSITION);

            this.mMANAGE_TYPE = (MANAGE_TYPE)Program._I_PDAService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            this.InitManagePosition();

            this.cmbStockType_Bind();

 
        }

        void ucMANAGE_POSITION_U_SaveButtonClicked()
        {
            bool bResult = true;

            string strResult = string.Empty;


            if (cmbStockType.SelectedValue == null)
            {
                MessageBox.Show("请选择托盘类型！", "系统提示");
                return;
            }

            Match mCode = Regex.Match(this.ucMANAGE_POSITION.U_STOCK_BARCODE, "^[0-9]{6}$");

            if (!mCode.Success)
            {
                MessageBox.Show("托盘条码校验失败！", "系统提示");

                return;
            }

            //判断输送位置是否合法
            bResult = this.ucMANAGE_POSITION.U_CHECK_WAREHOUSE();

            if (!bResult)
            {
                return;
            }

            if (MessageBox.Show("是否保存?", "系统提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                return;

            try
            {

                List<MANAGE_LIST> listMANAGE_LIST = new List<MANAGE_LIST>();

                MANAGE_LIST mMANAGE_LIST = new MANAGE_LIST();

                mMANAGE_LIST.GOODS_ID = Convert.ToInt32(this.cmbStockType.SelectedValue);

                mMANAGE_LIST.GOODS_IDSpecified = true;

                mMANAGE_LIST.MANAGE_LIST_QUANTITY = 1;

                mMANAGE_LIST.MANAGE_LIST_QUANTITYSpecified = true;

                listMANAGE_LIST.Add(mMANAGE_LIST);

                MANAGE_MAIN mMANAGE_MAIN = new MANAGE_MAIN();

                mMANAGE_MAIN.PLAN_ID = 0;

                mMANAGE_MAIN.PLAN_IDSpecified = true;

                mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                mMANAGE_MAIN.STOCK_BARCODE = this.ucMANAGE_POSITION.U_STOCK_BARCODE.TrimEnd();

                mMANAGE_MAIN.CELL_MODEL = this.ucMANAGE_POSITION.U_CELL_MODEL;

                mMANAGE_MAIN.START_CELL_ID = this.ucMANAGE_POSITION.U_START_POSITION_ID;

                mMANAGE_MAIN.START_CELL_IDSpecified = true;

                mMANAGE_MAIN.END_CELL_ID = this.ucMANAGE_POSITION.U_END_POSITION_ID;

                mMANAGE_MAIN.END_CELL_IDSpecified = true;

                mMANAGE_MAIN.MANAGE_OPERATOR = Program._USER.USER_NAME;

                mMANAGE_MAIN.MANAGE_BEGIN_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                mMANAGE_MAIN.MANAGE_STATUS = Model.MANAGE_STATUS.WaitingSend.ToString();

                mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                bResult = Program._I_PDAService.ManageCreate(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                          "ManageCreate",
                                                          mMANAGE_MAIN,
                                                          listMANAGE_LIST.ToArray<MANAGE_LIST>(),
                                                          true,
                                                          this.ucMANAGE_POSITION.U_CheckStockExistStorage,
                                                          this.ucMANAGE_POSITION.U_AutoCompleteTask,
                                                          this.ucMANAGE_POSITION.U_AutoDownloadControlTask,
                                                          out strResult);


                if (bResult)
                {
                    this.ucMANAGE_POSITION.U_Refresh();
                }

                if (bResult)
                    MessageBox.Show("保存成功\n" + strResult);
                else
                    MessageBox.Show("保存失败\n" + strResult);

            }
            catch (Exception ex)
            {
                bResult = false;

                strResult = ex.Message;
            }

        }

        private void InitManagePosition()
        {
            try
            {
                this.ucMANAGE_POSITION.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);

                this.ucMANAGE_POSITION.U_AllowShowStockBarcode = true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        private void cmbStockType_Bind()
        {
            //初始化数据
            cmbStockType.DisplayMember = "GOODS_NAME";
            cmbStockType.ValueMember = "GOODS_ID";
            cmbStockType.DataSource = Program._I_PDAService.GetList(" select * from goods_main where goods_class_id = 2");
        }

    }
}
