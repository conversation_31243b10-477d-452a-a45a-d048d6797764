﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;

namespace SiaSun.LMS.WPFClient.UC.Validate
{
    public class ValidateLengthRule:ValidationRule
    {
        int minLength;
        int maxLength;

        public int MinLength
        {
            get { return this.minLength; }
            set { this.minLength = value; }
        }

        public int MaxLength
        {
            get { return maxLength; }
            set { maxLength = value; }
        }

        public override ValidationResult Validate(object value, System.Globalization.CultureInfo cultureInfo)
        {
            string strValue = (string)value;
            if (strValue.Length < this.minLength || strValue.Length > this.maxLength)
            {
                return new ValidationResult(false, string.Format("输入值的长度超出范围: ({0} - {1})", minLength, maxLength));
            }

            return ValidationResult.ValidResult;
        }
    }
}
