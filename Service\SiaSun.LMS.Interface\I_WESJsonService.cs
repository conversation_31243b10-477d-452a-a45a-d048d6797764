﻿using System.Text;
using System.ServiceModel;
using System.Data;

namespace SiaSun.LMS.Interface
{

    [ServiceContract()]
    public interface I_WESJsonService
    {
        #region WES

        /// <summary>
        /// 非立库输送任务接口
        /// </summary>
        /// <param name="jsonstring">Json任务信息</param>
        /// <returns></returns>
        [OperationContract]
        string NoneMiniloadTrans(string jsonstring);

        /// <summary>
        /// 组箱入立库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string CreateBoxIn(string jsonstring);

        /// <summary>
        /// 空箱出库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string EmptyBoxOut(string jsonstring);

        /// <summary>
        /// 空箱入库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string EmptyBoxIn(string jsonstring);

        /// <summary>
        /// 盘点任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string StockCheckTask(string jsonstring);

        /// <summary>
        /// Miniload拣选任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string MiniloadSortTask(string jsonstring);

        /// <summary>
        /// 紧急配料出库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string EmergSortOutTask(string jsonstring);

        /// <summary>
        /// 齐套箱出库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string KitBoxOutTask(string jsonstring);

        /// <summary>
        /// 整理未满箱任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string ArrangeEmptyBoxTask(string jsonstring);

        /// <summary>
        /// 余箱回库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string BoxReturnInTask(string jsonstring);

        /// <summary>
        /// 齐套箱出库时余料回库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string KitBoxReturnInTask(string jsonstring);

        /// <summary>
        /// 物料同步
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        [OperationContract]
        string MaterialSyn(string jsonstring);

        /// <summary>
        /// 取消Miniload拣选任务
        /// </summary>
        [OperationContract]
        string CancelMiniloadSortTask(string jsonstring);



        /// <summary>
        /// 取消Miniload拣选任务
        /// 2024 改造新增
        /// </summary>
        [OperationContract]
        string MiniloadSortTaskCancel(string jsonstring);
        #endregion

        #region WMS

        /// <summary>
        /// 盘点结果回传接口
        /// </summary>
        [OperationContract]
        bool countResultReturnFromWCS(string inParm, out string outParm);

        /// <summary>
        /// 执行结果回传接口
        /// </summary>
        [OperationContract]
        bool handleResultReturnFromWCS(string inParm, out string outParm);

        /// <summary>
        /// Miniload拣货确认回传接口
        /// </summary>
        [OperationContract]
        bool miniloadPickConfirmFromWCS(string inParm, out string outParm);

        /// <summary>
        /// 齐套箱出库箱号信息回传接口
        /// </summary>
        [OperationContract]
        bool neatBoxInfoReceiveFromWCS(string inParm, out string outParm);

        /// <summary>
        /// 紧急配料出库箱号信息回传接口
        /// </summary>
        [OperationContract]
        bool urgentBoxReceiveFromWCS(string inParm, out string outParm);

        #endregion


        #region
        /// <summary>
        /// 一楼齐套出库任务完成
        /// wdz add 2022-09-05
        /// </summary>
        [OperationContract]
        string KitOutComplete(string jsonstring);
        #endregion


    }
}