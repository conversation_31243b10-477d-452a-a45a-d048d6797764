﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Ptl.Device;
using Ptl.Device.Communication;
using Ptl.Device.Communication.Command;

namespace SiaSun.LMS.Implement.YiChuanPtlDevice
{
    public static class PtlDeviceCommOperation
    {
        public static PtlDeviceComm ptlDeviceComm = new PtlDeviceComm("************");

        public static void Connect()
        {
            ptlDeviceComm.InitPtlDevice();
        }

        public static void PickDisplay(string address, int showInt, string pickMode, string goodsCode, string description, string goodsUnit)
        {
            ptlDeviceComm.PickDisplay(address, showInt, pickMode, goodsCode, description, goodsUnit);
        }

        public static void BoxLeaveDisplay(string addresss)
        {
            ptlDeviceComm.BoxLeaveDisplay(addresss);
        }
    }
}
