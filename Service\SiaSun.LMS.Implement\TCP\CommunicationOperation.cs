﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.TCP
{
    /// <summary>
    /// 拣选工作站相关
    /// 拣选工作站电气通信方法类
    /// 三台PLC各有一个通讯静态对象
    /// done
    /// 20180101
    /// </summary>
    public static class CommunicationOperation
    {
        //public static Communication comm = new Communication("192.168.2.12", 2000);
         
        public static Communication comm1 = new Communication("172.10.10.204", 2000);

        public static Communication comm2 = new Communication("172.10.10.202", 2000);

        public static Communication comm3 = new Communication("172.10.10.200", 2000);

        public static Communication comm4 = new Communication("172.10.11.78", 2000);//4 

        public static Communication comm5 = new Communication("172.10.11.74", 2000);//5

        public static Communication comm6 = new Communication("172.10.11.70", 2000);//6
        /// <summary>
        /// 建立连接，确定事件相应
        /// done
        /// </summary>
        public static void Launch()
        {
            comm1.Launch();
            comm2.Launch();
            comm3.Launch();
            
            comm4.Launch();
            comm5.Launch();
            comm6.Launch();


            comm1.BoxArrived -= Comm_BoxArrived;
            comm2.BoxArrived -= Comm_BoxArrived;
            comm3.BoxArrived -= Comm_BoxArrived;

            comm1.BoxArrived += Comm_BoxArrived;
            comm2.BoxArrived += Comm_BoxArrived;
            comm3.BoxArrived += Comm_BoxArrived;

            comm4.BoxArrived -= Comm_BoxArrived;
            comm5.BoxArrived -= Comm_BoxArrived;
            comm6.BoxArrived -= Comm_BoxArrived;

            comm4.BoxArrived += Comm_BoxArrived;
            comm5.BoxArrived += Comm_BoxArrived;
            comm6.BoxArrived += Comm_BoxArrived;

        }

        /// <summary>
        /// 料箱到位的相应方法
        /// done
        /// </summary>
        /// <param name="e">PLC传入参数</param>
        private static void Comm_BoxArrived(PlcEventArgs e)
        {
            try
            {

                if (e != null)
                {
                    WDZ.PickStationOperation pickStationOperation = new WDZ.PickStationOperation(e);
                    
                    //10：空箱到位；11满箱到位；12按钮申请离开；13满箱进入（设备号为条码设备）
                    switch (e.Status)
                    {
                        case 10:
                            //10：空箱到位
                            pickStationOperation.EmptyBoxHasArrivedPickPosition();
                            break;
                        case 11:
                            //11满箱到位
                            //呈现拣选任务
                            //分配电子标签
                            //指示灯光
                            pickStationOperation.FullBoxHasArrivedPickStation();
                            break;
                        case 12:
                            //12按钮申请离开
                            //准许离开
                            //将库存移入暂存货位
                            pickStationOperation.BoxAllowToLeave();
                            break;
                        case 13:
                            //13满箱进入 条码扫描
                            //生成拣选任务
                            pickStationOperation.FullBoxHasArrivedBarcodeScaner();
                            break;
                    }
                }


            }
            catch (Exception ex)
            {
                new Implement.S_BaseService()._log.FatalFormat(string.Format("[拣选工作站]  料箱到位信号处理程序异常！{0}-{1} EX110", ex.Message,ex.StackTrace));
            }
        }

        /// <summary>
        /// 终止连接
        /// done
        /// </summary>
        public static void Dispose()
        {
            comm1.Dispose();
            comm2.Dispose();
            comm3.Dispose();

            comm4.Dispose();
            comm5.Dispose();
            comm6.Dispose();
        }

    }
}
