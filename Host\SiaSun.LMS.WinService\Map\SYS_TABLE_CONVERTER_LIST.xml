﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_TABLE_CONVERTER_LIST" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_TABLE_CONVERTER_LIST" type="SiaSun.LMS.Model.SYS_TABLE_CONVERTER_LIST, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_TABLE_CONVERTER_LIST">
			<result property="TABLE_CONVERTER_LIST_ID" column="table_converter_list_id" />
			<result property="TABLE_CONVERTER_ID" column="table_converter_id" />
			<result property="TABLE_NAME" column="table_name" />
			<result property="COLUMN_NAME" column="column_name" />
			<result property="CONVERT_COLUMN_NAME" column="convert_column_name" />
			<result property="UNIQUE_FLAG" column="unique_flag" />
			<result property="ISNULL_FLAG" column="isnull_flag" />
			<result property="REMARK" column="remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_TABLE_CONVERTER_LIST_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  table_converter_list_id,
				  table_converter_id,
				  table_name,
				  column_name,
				  convert_column_name,
				  unique_flag,
				  isnull_flag,
				  remark
			From SYS_TABLE_CONVERTER_LIST
		</select>
		
		<select id="SYS_TABLE_CONVERTER_LIST_SELECT_BY_ID" parameterClass="int" extends = "SYS_TABLE_CONVERTER_LIST_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					table_converter_list_id=#TABLE_CONVERTER_LIST_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="SYS_TABLE_CONVERTER_LIST_SELECT_CONVERTER_ID" parameterClass="int" extends = "SYS_TABLE_CONVERTER_LIST_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          TABLE_CONVERTER_ID=#TABLE_CONVERTER_ID#
        </isParameterPresent>
      </dynamic>
    </select>
		

				
		<insert id="SYS_TABLE_CONVERTER_LIST_INSERT" parameterClass="SYS_TABLE_CONVERTER_LIST">
      Insert Into SYS_TABLE_CONVERTER_LIST (
      table_converter_list_id,
      table_converter_id,
      table_name,
      column_name,
      convert_column_name,
      unique_flag,
      isnull_flag,
      remark
      )Values(
      #TABLE_CONVERTER_LIST_ID#,
      #TABLE_CONVERTER_ID#,
      #TABLE_NAME#,
      #COLUMN_NAME#,
      #CONVERT_COLUMN_NAME#,
      #UNIQUE_FLAG#,
      #ISNULL_FLAG#,
      #REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="TABLE_CONVERTER_LIST_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_TABLE_CONVERTER_LIST_UPDATE" parameterClass="SYS_TABLE_CONVERTER_LIST">
      Update SYS_TABLE_CONVERTER_LIST Set
      table_converter_list_id=#TABLE_CONVERTER_LIST_ID#,
      table_converter_id=#TABLE_CONVERTER_ID#,
      table_name=#TABLE_NAME#,
      column_name=#COLUMN_NAME#,
      convert_column_name=#CONVERT_COLUMN_NAME#,
      unique_flag=#UNIQUE_FLAG#,
      isnull_flag=#ISNULL_FLAG#,
      remark=#REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					table_converter_list_id=#TABLE_CONVERTER_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_TABLE_CONVERTER_LIST_DELETE" parameterClass="int">
			Delete From SYS_TABLE_CONVERTER_LIST
			<dynamic prepend="WHERE">
				<isParameterPresent>
					table_converter_list_id=#TABLE_CONVERTER_LIST_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>