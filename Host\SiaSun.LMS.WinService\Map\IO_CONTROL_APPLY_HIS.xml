﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="IO_CONTROL_APPLY_HIS" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="IO_CONTROL_APPLY_HIS" type="SiaSun.LMS.Model.IO_CONTROL_APPLY_HIS, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="IO_CONTROL_APPLY_HIS">
      <result property="CONTROL_APPLY_ID" column="control_apply_id" />
      <result property="CONTROL_ID" column="control_id" />
      <result property="CONTROL_APPLY_TYPE" column="control_apply_type" />
      <result property="WAREHOUSE_CODE" column="warehouse_code" />
      <result property="DEVICE_CODE" column="device_code" />
      <result property="STOCK_BARCODE" column="stock_barcode" />
      <result property="APPLY_TASK_STATUS" column="apply_task_status" />
      <result property="CONTROL_ERROR_TEXT" column="control_error_text" />
      <result property="MANAGE_ERROR_TEXT" column="manage_error_text" />
      <result property="CREATE_TIME" column="create_time" />
      <result property="HANDLE_TIME" column="handle_time" />
      <result property="CONTROL_APPLY_PARAMETER" column="control_apply_parameter" />
      <result property="CONTROL_APPLY_PARA01" column="control_apply_para01" />
      <result property="CONTROL_APPLY_PARA02" column="control_apply_para02" />
      <result property="CONTROL_APPLY_REMARK" column="control_apply_remark" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="IO_CONTROL_APPLY_HIS_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      control_apply_id,
      control_id,
      control_apply_type,
      warehouse_code,
      device_code,
      stock_barcode,
      apply_task_status,
      control_error_text,
      manage_error_text,
      create_time,
      handle_time,
      control_apply_parameter,
      control_apply_para01,
      control_apply_para02,
      control_apply_remark
      From IO_CONTROL_APPLY_HIS
    </select>

    <select id="IO_CONTROL_APPLY_HIS_SELECT_BY_ID" parameterClass="int" extends = "IO_CONTROL_APPLY_HIS_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          control_apply_id=#CONTROL_APPLY_ID#
        </isParameterPresent>
      </dynamic>
    </select>



    <insert id="IO_CONTROL_APPLY_HIS_INSERT" parameterClass="IO_CONTROL_APPLY_HIS">
      Insert Into IO_CONTROL_APPLY_HIS (
      control_apply_id,
      control_id,
      control_apply_type,
      warehouse_code,
      device_code,
      stock_barcode,
      apply_task_status,
      control_error_text,
      manage_error_text,
      create_time,
      handle_time,
      control_apply_parameter,
      control_apply_para01,
      control_apply_para02,
      control_apply_remark
      )Values(
      #CONTROL_APPLY_ID#,
      #CONTROL_ID#,
      #CONTROL_APPLY_TYPE#,
      #WAREHOUSE_CODE#,
      #DEVICE_CODE#,
      #STOCK_BARCODE#,
      #APPLY_TASK_STATUS#,
      #CONTROL_ERROR_TEXT#,
      #MANAGE_ERROR_TEXT#,
      #CREATE_TIME#,
      #HANDLE_TIME#,
      #CONTROL_APPLY_PARAMETER#,
      #CONTROL_APPLY_PARA01#,
      #CONTROL_APPLY_PARA02#,
      #CONTROL_APPLY_REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="CONTROL_APPLY_ID">
        select @@IDENTITY as value
      </selectKey>-->
    </insert>

    <update id="IO_CONTROL_APPLY_HIS_UPDATE" parameterClass="IO_CONTROL_APPLY_HIS">
      Update IO_CONTROL_APPLY_HIS Set
      control_apply_id=#CONTROL_APPLY_ID#,
      control_id=#CONTROL_ID#,
      control_apply_type=#CONTROL_APPLY_TYPE#,
      warehouse_code=#WAREHOUSE_CODE#,
      device_code=#DEVICE_CODE#,
      stock_barcode=#STOCK_BARCODE#,
      apply_task_status=#APPLY_TASK_STATUS#,
      control_error_text=#CONTROL_ERROR_TEXT#,
      manage_error_text=#MANAGE_ERROR_TEXT#,
      create_time=#CREATE_TIME#,
      handle_time=#HANDLE_TIME#,
      control_apply_parameter=#CONTROL_APPLY_PARAMETER#,
      control_apply_para01=#CONTROL_APPLY_PARA01#,
      control_apply_para02=#CONTROL_APPLY_PARA02#,
      control_apply_remark=#CONTROL_APPLY_REMARK#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          control_apply_id=#CONTROL_APPLY_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="IO_CONTROL_APPLY_HIS_DELETE" parameterClass="int">
      Delete From IO_CONTROL_APPLY_HIS
      <dynamic prepend="WHERE">
        <isParameterPresent>
          control_apply_id=#CONTROL_APPLY_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>