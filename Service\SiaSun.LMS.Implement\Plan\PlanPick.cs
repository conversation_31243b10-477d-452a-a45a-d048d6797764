﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Plan
{
    /// <summary>
    /// 拣选工作站相关
    /// 拣选计划操作相关方法
    /// doing
    /// </summary>
    public class PlanPick : PlanBase
    {
        private static object synchronied = new object();
        /// <summary>
        /// 拣选工作站
        /// 按订单锁定出库库存
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanLock(Model.SYS_USER mSYS_USER,int PICK_STATION_ID,bool bTrans,out string sResult)
        {
            lock (synchronied)
            {
                bool bResult = true;
                sResult = string.Empty;

                try
                {
                    this._P_Base_House.BeginTransaction(bTrans);

                    List<Model.T_PICK_POSITION_PLAN_BIND> T_PICK_POSITION_PLAN_BINDS
                        = this._P_T_PICK_POSITION_PLAN_BIND.GetListByPICK_STATION_ID(PICK_STATION_ID).ToList();
                    bResult = T_PICK_POSITION_PLAN_BINDS.Count > 0;

                    if (!bResult)
                    {
                        sResult = string.Format("拣选工作站未成功绑定拣选订单");
                        return bResult;
                    }

                    foreach (Model.T_PICK_POSITION_PLAN_BIND fT_PICK_POSITION_PLAN_BIND in T_PICK_POSITION_PLAN_BINDS)
                    {
                        #region foreach
                        bResult = this.WBSLock(mSYS_USER, fT_PICK_POSITION_PLAN_BIND.PLAN_ID, false, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }

                        fT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Enable.ToString("d");//已经锁定了库存

                        this._P_T_PICK_POSITION_PLAN_BIND.Update(fT_PICK_POSITION_PLAN_BIND);
                        #endregion
                    }

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.StackTrace;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult, bTrans);
                }
                return bResult;
            }            
        }


        /// <summary>
        /// 拣选工作站
        /// 按订单锁定出库库存
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanLockNew2024(Model.SYS_USER mSYS_USER, int PICK_POSITION_ID, bool bTrans, out string sResult)
        {
            lock (synchronied)
            {
                bool bResult = true;
                sResult = string.Empty;

                try
                {
                    this._P_Base_House.BeginTransaction(bTrans);

                    List<Model.T_PICK_POSITION_PLAN_BIND> T_PICK_POSITION_PLAN_BINDS
                        = this._P_T_PICK_POSITION_PLAN_BIND.GetListByPICK_POSITION_ID(PICK_POSITION_ID).ToList();
                    bResult = T_PICK_POSITION_PLAN_BINDS.Count > 0;

                    if (!bResult)
                    {
                        sResult = string.Format("拣选工作站未成功绑定拣选订单");
                        return bResult;
                    }

                    foreach (Model.T_PICK_POSITION_PLAN_BIND fT_PICK_POSITION_PLAN_BIND in T_PICK_POSITION_PLAN_BINDS)
                    {
                        #region foreach
                        bResult = this.WBSLock(mSYS_USER, fT_PICK_POSITION_PLAN_BIND.PLAN_ID, false, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }

                        fT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Enable.ToString("d");//已经锁定了库存

                        this._P_T_PICK_POSITION_PLAN_BIND.Update(fT_PICK_POSITION_PLAN_BIND);
                        #endregion
                    }

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.StackTrace;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult, bTrans);
                }
                return bResult;
            }
        }


        /// <summary>
        /// 拣选工作站
        /// 按订单锁定出库库存
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanLockPartly(Model.SYS_USER mSYS_USER, int PICK_STATION_ID, bool bTrans, out string sResult)
        {
            lock (synchronied)
            {
                bool bResult = true;
                sResult = string.Empty;

                try
                {
                    this._P_Base_House.BeginTransaction(bTrans);

                    List<Model.T_PICK_POSITION_PLAN_BIND> T_PICK_POSITION_PLAN_BINDS
                        = this._P_T_PICK_POSITION_PLAN_BIND.GetListByPICK_STATION_IDPartly(PICK_STATION_ID).ToList();
                    bResult = T_PICK_POSITION_PLAN_BINDS.Count > 0;

                    if (!bResult)
                    {
                        sResult = string.Format("拣选工作站未成功绑定拣选订单");
                        return bResult;
                    }

                    foreach (Model.T_PICK_POSITION_PLAN_BIND fT_PICK_POSITION_PLAN_BIND in T_PICK_POSITION_PLAN_BINDS)
                    {
                        #region foreach
                        bResult = this.WBSLockPartly(mSYS_USER, fT_PICK_POSITION_PLAN_BIND.PLAN_ID, false, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }

                        fT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Enable.ToString("d");//已经锁定了库存

                        this._P_T_PICK_POSITION_PLAN_BIND.Update(fT_PICK_POSITION_PLAN_BIND);
                        #endregion
                    }

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.StackTrace;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult, bTrans);
                }

                return bResult;

            }
                
        }

        /// <summary>
        /// 改造2024
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_POSITION_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanLockPartlyNew2024(Model.SYS_USER mSYS_USER, int PICK_POSITION_ID, bool bTrans, out string sResult)
        {
            lock (synchronied)
            {
                bool bResult = true;
                sResult = string.Empty;

                try
                {
                    this._P_Base_House.BeginTransaction(bTrans);

                    List<Model.T_PICK_POSITION_PLAN_BIND> T_PICK_POSITION_PLAN_BINDS
                        = this._P_T_PICK_POSITION_PLAN_BIND.GetListByPICK_POSITION_IDPartly(PICK_POSITION_ID).ToList();
                    bResult = T_PICK_POSITION_PLAN_BINDS.Count > 0;

                    if (!bResult)
                    {
                        sResult = string.Format("拣选工作站未成功绑定拣选订单");
                        return bResult;
                    }

                    foreach (Model.T_PICK_POSITION_PLAN_BIND fT_PICK_POSITION_PLAN_BIND in T_PICK_POSITION_PLAN_BINDS)
                    {
                        #region foreach
                        bResult = this.WBSLockPartly(mSYS_USER, fT_PICK_POSITION_PLAN_BIND.PLAN_ID, false, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }

                        fT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Enable.ToString("d");//已经锁定了库存

                        this._P_T_PICK_POSITION_PLAN_BIND.Update(fT_PICK_POSITION_PLAN_BIND);
                        #endregion
                    }

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.StackTrace;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult, bTrans);
                }

                return bResult;

            }

        }


        /// <summary>
        /// 拣选工作站相关
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PLAN_ID">WBS计划ID</param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool WBSLock(Model.SYS_USER mSYS_USER, int PLAN_ID,bool bTrans,out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.PLAN_MAIN wbsPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            bResult =! (wbsPLAN_MAIN == null);

            if (!bResult)
            {
                sResult = string.Format("未找到{0}对应的计划", PLAN_ID);
                return bResult;
            }

            List<Model.PLAN_LIST> wbsPLAN_LISTS = this._P_PLAN_LIST.GetListPlanID(PLAN_ID).ToList();

            bResult = wbsPLAN_LISTS.Count > 0;
            if (!bResult)
            {
                sResult = string.Format("未找到{0}对应的计划明细", PLAN_ID);
                return bResult;
            }

            string sMsg = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);
                foreach (Model.PLAN_LIST mPLAN_LIST in wbsPLAN_LISTS)
                {
                    bResult &= this.PlanListLock(mSYS_USER, mPLAN_LIST, false, out sResult);

                    if (!string.IsNullOrEmpty(sResult))
                    {
                        sMsg += string.Format("{0}\n", sResult);
                    }
                }

                if (!bResult)
                {
                    sResult = sMsg;
                    return bResult;
                }

                //更新单据状态
                wbsPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Executing.ToString();
                wbsPLAN_MAIN.PLAN_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                this._P_PLAN_MAIN.Update(wbsPLAN_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.ToString();
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PLAN_ID">WBS计划ID</param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool WBSLockPartly(Model.SYS_USER mSYS_USER, int PLAN_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.PLAN_MAIN wbsPLAN_MAIN = this._P_PLAN_MAIN.GetModel(PLAN_ID);

            bResult = !(wbsPLAN_MAIN == null);

            if (!bResult)
            {
                sResult = string.Format("未找到{0}对应的计划", PLAN_ID);
                return bResult;
            }

            List<Model.PLAN_LIST> wbsPLAN_LISTS = this._P_PLAN_LIST.GetListPlanID(PLAN_ID).ToList();

            bResult = wbsPLAN_LISTS.Count > 0;
            if (!bResult)
            {
                sResult = string.Format("未找到{0}对应的计划明细", PLAN_ID);
                return bResult;
            }

            string sMsg = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);
                foreach (Model.PLAN_LIST mPLAN_LIST in wbsPLAN_LISTS)
                {
                    bResult &= this.PlanListLockPartly(mSYS_USER, mPLAN_LIST, false, out sResult);

                    if (!string.IsNullOrEmpty(sResult))
                    {
                        sMsg += string.Format("{0}\n", sResult);
                    }
                }

                sResult = sMsg;
                if (!bResult)
                {
                    return bResult;
                }

                //更新单据状态
                wbsPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Executing.ToString();
                wbsPLAN_MAIN.PLAN_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                this._P_PLAN_MAIN.Update(wbsPLAN_MAIN);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.ToString();
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// WBS计划的明细进行锁定
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="mPLAN_LIST"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool PlanListLock(Model.SYS_USER mSYS_USER,Model.PLAN_LIST mPLAN_LIST,bool bTrans,out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                decimal PLAN_LIST_QUANTITY = mPLAN_LIST.PLAN_LIST_QUANTITY - mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY;
                decimal STORAGE_LIST_QUANTITY_LOCK = 0;
                string LOCK_REMARK = string.Format("单据锁定-{0}-{1}", mSYS_USER.USER_NAME, Common.StringUtil.GetDateTime());

                Storage.List mStorageList = new Storage.List();
                bResult = mStorageList.Lock(mPLAN_LIST.PLAN_LIST_ID
                    , mPLAN_LIST.GOODS_ID
                    , PLAN_LIST_QUANTITY
                    , LOCK_REMARK
                    , out STORAGE_LIST_QUANTITY_LOCK
                    , out sResult);

                if (!bResult)
                {
                    return bResult;
                }
                
                mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += STORAGE_LIST_QUANTITY_LOCK;

                this._P_PLAN_LIST.Update(mPLAN_LIST);

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// WBS计划的明细进行锁定
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="mPLAN_LIST"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool PlanListLockPartly(Model.SYS_USER mSYS_USER, Model.PLAN_LIST mPLAN_LIST, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                decimal PLAN_LIST_QUANTITY = mPLAN_LIST.PLAN_LIST_QUANTITY - mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY;
                decimal STORAGE_LIST_QUANTITY_LOCK = 0;
                string LOCK_REMARK = string.Format("单据锁定-{0}-{1}", mSYS_USER.USER_NAME, Common.StringUtil.GetDateTime());

                Storage.List mStorageList = new Storage.List();
                bResult = mStorageList.LockPartly(mPLAN_LIST.PLAN_LIST_ID
                    , mPLAN_LIST.GOODS_ID
                    , PLAN_LIST_QUANTITY
                    , LOCK_REMARK
                    , out STORAGE_LIST_QUANTITY_LOCK
                    , out sResult);

                if (!bResult)
                {
                    return bResult;
                }

                mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY += STORAGE_LIST_QUANTITY_LOCK;

                this._P_PLAN_LIST.Update(mPLAN_LIST);

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }



        /// <summary>
        /// 拣选工作站相关
        /// 按订单锁定库存下达出库任务
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanCreateManageOut(Model.SYS_USER mSYS_USER, int PICK_STATION_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                List<Model.STORAGE_LOCK> STORAGE_LOCKS = 
                    this._P_STORAGE_LOCK.GetListByPickStationIdAndFlag(PICK_STATION_ID, Enum.FLAG.Disable.ToString("d")).ToList();


                bResult = STORAGE_LOCKS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以被使用的已锁定库存");
                    return bResult;
                }

                List<Model.STORAGE_MAIN> STORAGE_MAINS =
                    this._P_STORAGE_MAIN.GetListByPickStationIdAndFlagForManageDown(PICK_STATION_ID, Enum.FLAG.Disable.ToString("d")).ToList();

                bResult = STORAGE_MAINS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以下架的已锁定库存");
                    return bResult;
                }

                foreach(Model.STORAGE_MAIN mSTORAGE_MAIN in STORAGE_MAINS)
                {
                    //ManageDown
                    bResult &= this.ManageDownCreate(mSYS_USER, PICK_STATION_ID, mSTORAGE_MAIN, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.StackTrace;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_POSITION_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanCreateManageOutByPickPosition(Model.SYS_USER mSYS_USER, int PICK_POSITION_ID, int PICK_STATION_ID,  bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                
                this._P_Base_House.BeginTransaction(bTrans);

                List<Model.STORAGE_LOCK> STORAGE_LOCKS =
                    this._P_STORAGE_LOCK.GetListByPickPositionIdAndFlag(PICK_POSITION_ID, Enum.FLAG.Disable.ToString("d")).ToList();


                bResult = STORAGE_LOCKS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以被使用的已锁定库存");
                    return bResult;
                }

                List<Model.STORAGE_MAIN> STORAGE_MAINS =
                    this._P_STORAGE_MAIN.GetListByPickPositionIdAndFlagForManageDown(PICK_POSITION_ID, Enum.FLAG.Disable.ToString("d")).ToList();

                bResult = STORAGE_MAINS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以下架的已锁定库存");
                    return bResult;
                }

                foreach (Model.STORAGE_MAIN mSTORAGE_MAIN in STORAGE_MAINS)
                {
                    //ManageDown
                    bResult &= this.ManageDownCreate(mSYS_USER, PICK_STATION_ID, mSTORAGE_MAIN, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.StackTrace;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 按订单锁定库存下达出库任务
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_POSITION_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PlanCreateManageOutByPICK_POSITION(Model.SYS_USER mSYS_USER, int PICK_POSITION_ID, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);
                Model.T_PICK_POSITION mT_PICK_POSITION = this._P_T_PICK_POSITION.GetModel(PICK_POSITION_ID);

                if (mT_PICK_POSITION == null)
                {
                    sResult = "pick_position null";
                    bResult = false;
                    return bResult;
                }
                List<Model.STORAGE_LOCK> STORAGE_LOCKS =
                    this._P_STORAGE_LOCK.GetListByPickPositionIdAndFlag(PICK_POSITION_ID, Enum.FLAG.Disable.ToString("d")).ToList();


                bResult = STORAGE_LOCKS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以被使用的已锁定库存");
                    return bResult;
                }

                List<Model.STORAGE_MAIN> STORAGE_MAINS =
                    this._P_STORAGE_MAIN.GetListByPickPositionIdAndFlagForManageDown(PICK_POSITION_ID, Enum.FLAG.Disable.ToString("d")).ToList();

                bResult = STORAGE_MAINS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以下架的已锁定库存");
                    return bResult;
                }
                
                foreach (Model.STORAGE_MAIN mSTORAGE_MAIN in STORAGE_MAINS)
                {
                    //ManageDown
                    bResult &= this.ManageDownCreate(mSYS_USER, mT_PICK_POSITION.STATION_ID, mSTORAGE_MAIN, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.StackTrace;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 下达拣选工作站下架任务
        /// done
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="mSTORAGE_MAIN"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool ManageDownCreate(Model.SYS_USER mSYS_USER,int PICK_STATION_ID,Model.STORAGE_MAIN mSTORAGE_MAIN,out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty ;
            try
            {
                Model.T_PICK_STATION mT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PICK_STATION_ID);
                if (mT_PICK_STATION == null)
                {
                    sResult = string.Format("PICK_STATION ERROR");
                    bResult = false;
                    return bResult;
                }

               
                Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                mMANAGE_MAIN.MANAGE_TYPE_CODE = "ManageDown";
                mMANAGE_MAIN.PLAN_ID = 0;
                mMANAGE_MAIN.STOCK_BARCODE = mSTORAGE_MAIN.STOCK_BARCODE;
                mMANAGE_MAIN.CELL_MODEL = mSTORAGE_MAIN.CELL_MODEL;
                mMANAGE_MAIN.START_CELL_ID = mSTORAGE_MAIN.CELL_ID;
                mMANAGE_MAIN.END_CELL_ID = mT_PICK_STATION.WH_CELL_ID;//目标位置由拣选工作站决定
                mMANAGE_MAIN.MANAGE_OPERATOR = mSYS_USER.USER_NAME;
                mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                string manageLevel = string.Empty;
                mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("MiniloadSortPlanLevel", out manageLevel) ? manageLevel : "0";
                mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                List<Model.MANAGE_LIST> listMANAGE_LIST = new List<Model.MANAGE_LIST>();

                List<Model.STORAGE_LIST> STORAGE_LISTS = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).ToList();

                foreach (Model.STORAGE_LIST mSTORAGE_LIST in STORAGE_LISTS)
                {
                    Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                    mMANAGE_LIST = new Common.CloneObjectValues().CloneModelValue<Model.STORAGE_LIST, Model.MANAGE_LIST>(mSTORAGE_LIST, mMANAGE_LIST, null);
                    mMANAGE_LIST.MANAGE_LIST_QUANTITY = mSTORAGE_LIST.STORAGE_LIST_QUANTITY;
                    listMANAGE_LIST.Add(mMANAGE_LIST);
                }

                if (listMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("任务明细创建失败");
                    return bResult;
                }

                bResult = new ManageDown().ManageCreate(mMANAGE_MAIN, listMANAGE_LIST, false, false, true, out sResult);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format(ex.Message);
                return bResult;
            }
            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 当拣选任务完成后，如果拣选点对应的拣选计划已经拣选完毕，应该继续锁定
        /// 其他WBS到这个拣选工作点
        /// testing
        /// </summary>
        /// <param name="mT_PICK_POSITION"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ContinueLockAndOut(Model.T_PICK_POSITION mT_PICK_POSITION, bool bTrans,out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);
              
                Model.T_PICK_POSITION_PLAN_BIND mT_PICK_POSITION_PLAN_BIND = this._P_T_PICK_POSITION_PLAN_BIND.GetModel_By_PICK_POSITION_ID(mT_PICK_POSITION.POSITION_ID);

                if (mT_PICK_POSITION_PLAN_BIND == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作点对应的绑定计划对应表！");
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mT_PICK_POSITION_PLAN_BIND.PLAN_ID);

                if (mPLAN_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选点对应的绑定计划{0}", mT_PICK_POSITION_PLAN_BIND.PLAN_ID);
                    return bResult;
                }

                List<Model.PLAN_LIST> PLAN_LISTS = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID).ToList();

                if (PLAN_LISTS.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("拣选点绑定的计划明细信息为空");
                    return bResult;
                }

                bool bComplete = true;

                foreach(Model.PLAN_LIST mPLAN_LIST in PLAN_LISTS)
                {
                    if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY != mPLAN_LIST.PLAN_LIST_QUANTITY)
                    {
                        bComplete = false;
                    }
                }
                
                if (!bComplete)
                {
                    bResult = true;
                    return bResult;
                }
                else
                {
                    //continue
                    //mPLAN_MAIN.PLAN_GROUP
                    Model.SYS_USER mSYS_USER = this._P_SYS_USER.GetModel(mT_PICK_POSITION_PLAN_BIND.USER_ID);
                    if (mSYS_USER == null)
                    {
                        bResult = false;
                        sResult = string.Format("用户信息{0}为空", mT_PICK_POSITION_PLAN_BIND.USER_ID);
                        return bResult;
                    }

                    //this._P_T_PICK_POSITION_PLAN_BIND.Delete(mT_PICK_POSITION_PLAN_BIND.PICK_POSITION_PLAN_BIND_ID);

                    List<Model.PLAN_MAIN> conPLAN_MAINS 
                        = this._P_PLAN_MAIN.GetListNotBindToPickStationBy_PLAN_GROUP_STATION_ID(mPLAN_MAIN.PLAN_GROUP, mT_PICK_POSITION.STATION_ID).ToList();

                    if (conPLAN_MAINS.Count > 0)
                    {
                        this._P_T_PICK_POSITION_PLAN_BIND.Delete(mT_PICK_POSITION_PLAN_BIND.PICK_POSITION_PLAN_BIND_ID);

                        Model.PLAN_MAIN todoPLAN_MAIN = conPLAN_MAINS.OrderBy(t => t.PLAN_CODE).ToList()[0];

                        Model.T_PICK_POSITION_PLAN_BIND aT_PICK_POSITION_PLAN_BIND = new Model.T_PICK_POSITION_PLAN_BIND();

                        aT_PICK_POSITION_PLAN_BIND.PICK_POSITION_ID = mT_PICK_POSITION.POSITION_ID;
                        aT_PICK_POSITION_PLAN_BIND.PLAN_ID = todoPLAN_MAIN.PLAN_ID;
                        aT_PICK_POSITION_PLAN_BIND.PICK_STATION_ID = mT_PICK_POSITION.STATION_ID;
                        aT_PICK_POSITION_PLAN_BIND.CELL_ID = mT_PICK_POSITION.WH_CELL_ID;
                        //aT_PICK_POSITION_PLAN_BIND.USER_ID = 0;
                        aT_PICK_POSITION_PLAN_BIND.BIND_TIME = Common.StringUtil.GetDateTime();
                        aT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Enable.ToString("d");//1 表示被锁定

                        this._P_T_PICK_POSITION_PLAN_BIND.Add(aT_PICK_POSITION_PLAN_BIND);

                        bResult = this.WBSLock(mSYS_USER, todoPLAN_MAIN.PLAN_ID, false, out sResult);
                        if (!bResult)
                        {
                            return bResult;
                        }
                        //下达下架任务
                        bResult =this.PlanCreateManageOutByPICK_POSITION(mSYS_USER, mT_PICK_POSITION.POSITION_ID, false, out sResult);

                        if (!bResult)
                        {
                            return bResult;
                        }
                    }
                   
                    this._P_T_PICK_POSITION_PLAN_BIND.Delete(mT_PICK_POSITION_PLAN_BIND.PICK_POSITION_PLAN_BIND_ID);

                }


            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.StackTrace;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }

        /// <summary>
        /// tesing
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="bTrans"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PrevPlanLockPartly(Model.SYS_USER mSYS_USER, int PICK_STATION_ID,string prevPlanGroup, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                List<Model.T_PICK_POSITION_PLAN_BIND> T_PICK_POSITION_PLAN_BINDS
                    = this._P_T_PICK_POSITION_PLAN_BIND.GetListByPICK_STATION_IDPrevPartly(PICK_STATION_ID, prevPlanGroup).ToList();
                bResult = T_PICK_POSITION_PLAN_BINDS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("拣选工作站未成功绑定拣选订单");
                    return bResult;
                }

                foreach (Model.T_PICK_POSITION_PLAN_BIND fT_PICK_POSITION_PLAN_BIND in T_PICK_POSITION_PLAN_BINDS)
                {
                    #region foreach
                    bResult = this.WBSLockPartly(mSYS_USER, fT_PICK_POSITION_PLAN_BIND.PLAN_ID, false, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }

                    fT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Enable.ToString("d");//已经锁定了库存

                    this._P_T_PICK_POSITION_PLAN_BIND.Update(fT_PICK_POSITION_PLAN_BIND);
                    #endregion
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.StackTrace;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 按订单锁定库存下达出库任务
        /// testing
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool PrevPlanCreateManageOut(Model.SYS_USER mSYS_USER, int PICK_STATION_ID, string prevPlanGroup,bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                List<Model.STORAGE_LOCK> STORAGE_LOCKS =
                    this._P_STORAGE_LOCK.GetListByPrevPlanGroupAndFlag(prevPlanGroup, Enum.FLAG.Disable.ToString("d")).ToList();


                bResult = STORAGE_LOCKS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以被使用的已锁定库存");
                    return bResult;
                }

                List<Model.STORAGE_MAIN> STORAGE_MAINS =
                    this._P_STORAGE_MAIN.GetListByPickStationIdPrevPlanGroupAndFlagForManageDown(
                        PICK_STATION_ID, prevPlanGroup,Enum.FLAG.Disable.ToString("d")).ToList();

                bResult = STORAGE_MAINS.Count > 0;

                if (!bResult)
                {
                    sResult = string.Format("未找到可以下架的已锁定库存");
                    return bResult;
                }

                foreach (Model.STORAGE_MAIN mSTORAGE_MAIN in STORAGE_MAINS)
                {
                    //ManageDown
                    bResult &= this.ManageDownCreate(mSYS_USER, PICK_STATION_ID, mSTORAGE_MAIN, out sResult);
                    if (!bResult)
                    {
                        return bResult;
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.StackTrace;
            }
            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }


    }
}
