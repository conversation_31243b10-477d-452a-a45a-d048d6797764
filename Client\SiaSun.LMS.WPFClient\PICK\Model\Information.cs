﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.WPFClient.PICK.Model
{
    public class Information : MVVM.ObservableObject
    {
        private string _title;
        private string _header;
        private string _content;
        private string _msg;
        private string _subTitle;
        private string _remark;
        private int _index;
        private bool _isHighLight;
        private string _headerText; //抬头文本 ywz 2023-10-03


        public string Title
        {
            get
            {
                return _title;
            }

            set
            {
                if(_title != value)
                {
                    _title = value;
                    RaisePropertyChanged("Title");
                }
            }
        }

        public string SubTitle
        {
            get
            {
                return _subTitle;
            }

            set
            {
                if (_subTitle != value)
                {
                    _subTitle = value;
                    RaisePropertyChanged("SubTitle");
                }
            }
        }

        public string Header
        {
            get
            {
                return _header;
            }

            set
            {
                if (_header != value)
                { _header = value;
                    RaisePropertyChanged("Header");
                }
            }
        }

        /// <summary>
        /// 抬头文本 ywz 2023-10-03
        /// </summary>
        public string HeaderText
        {
            get
            {
                return _headerText;
            }

            set
            {
                if (_headerText != value)
                {
                    _headerText = value;
                    RaisePropertyChanged("HeaderText");
                }
            }
        }

        public string Content
        {
            get
            {
                return _content;
            }

            set
            {
                if(_content != value)
                {
                    _content = value;
                    RaisePropertyChanged("Content");
                }
            }
        }

        public string Msg
        {
            get
            {
                return _msg;
            }

            set
            {
                if(_msg != value)
                {
                    _msg = value;
                    RaisePropertyChanged("Msg");
                }
            }
        }

        public string Remark
        {
            get
            {
                return _remark;
            }

            set
            {
                if (_remark != value)
                {
                    _remark = value;
                    RaisePropertyChanged("Remark");
                }
            }
        }

        public int Index
        {
            get
            {
                return _index;
            }

            set
            {
                if(_index != value)
                {
                    _index = value;
                    RaisePropertyChanged("Index");
                }
            }
        }

        public bool IsHighLight
        {
            get
            {
                return _isHighLight;
            }

            set
            {
                if(_isHighLight != value)
                {
                    _isHighLight = value;
                    RaisePropertyChanged("IsHighLight");
                }
            }
        }

        public Information()
        {

        }
        public Information(int index,string title,string subtitle,string header,string content,string msg,string remark,bool isHighLight,string headerText)
        {
            this._index = index;
            this._title = title;
            this._subTitle = subtitle;
            this._header = header;
            this._content = content;
            this._msg = msg;
            this._remark = remark;
            this._isHighLight = isHighLight;
            this._headerText = headerText;
        }
        
    }
}
