﻿<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="3.5">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.30729</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{B0824E81-F3D7-4BCF-95B9-E017246AF97F}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SSLMS.MobileUI</RootNamespace>
    <AssemblyName>SSLMS.MobileUI</AssemblyName>
    <ProjectTypeGuids>{4D628B5B-2FBC-4AA6-8C16-197242AEB884};{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}</ProjectTypeGuids>
    <PlatformFamilyName>WindowsCE</PlatformFamilyName>
    <PlatformID>E2BECB1F-8C8C-41ba-B736-9BE7D946A398</PlatformID>
    <OSVersion>5.0</OSVersion>
    <DeployDirSuffix>SSLMS.MobileUI</DeployDirSuffix>
    <TargetFrameworkVersion>v3.5</TargetFrameworkVersion>
    <FormFactorID>
    </FormFactorID>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>2.0</OldToolsVersion>
    <NativePlatformName>Windows CE</NativePlatformName>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <StartupObject>SSLMS.MobileUI.Program</StartupObject>
    <ApplicationIcon>%40Images\SiasunWMS.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE;$(PlatformFamilyName)</DefineConstants>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <ErrorReport>prompt</ErrorReport>
    <FileAlignment>512</FileAlignment>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE;$(PlatformFamilyName)</DefineConstants>
    <NoStdLib>true</NoStdLib>
    <NoConfig>true</NoConfig>
    <ErrorReport>prompt</ErrorReport>
    <FileAlignment>512</FileAlignment>
    <WarningLevel>4</WarningLevel>
    <GenerateSerializationAssemblies>Off</GenerateSerializationAssemblies>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="mscorlib" />
    <Reference Include="Symbol, Version=1.6.1.1, Culture=neutral, PublicKeyToken=68ec8db391f150ca">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Symbol.dll</HintPath>
    </Reference>
    <Reference Include="Symbol.Barcode.Design, Version=1.6.1.1, Culture=neutral, PublicKeyToken=68ec8db391f150ca, processorArchitecture=MSIL">
      <SpecificVersion>False</SpecificVersion>
      <HintPath>..\..\lib\Symbol.Barcode.Design.dll</HintPath>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Data" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Runtime.Serialization" />
    <Reference Include="System.ServiceModel" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="System.Xml.Linq" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Common.cs" />
    <Compile Include="FrmCellSelect.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="FrmCellSelect.Designer.cs">
      <DependentUpon>FrmCellSelect.cs</DependentUpon>
    </Compile>
    <Compile Include="LOGIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="LOGIN.designer.cs">
      <DependentUpon>LOGIN.cs</DependentUpon>
    </Compile>
    <Compile Include="MAIN.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="MAIN.designer.cs">
      <DependentUpon>MAIN.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <EmbeddedResource Include="FrmCellSelect.resx">
      <DependentUpon>FrmCellSelect.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="LOGIN.resx">
      <DependentUpon>LOGIN.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="MAIN.resx">
      <DependentUpon>MAIN.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\Common\ucGrid.resx">
      <DependentUpon>ucGrid.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\Common\ucList.resx">
      <DependentUpon>ucList.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\ucCell.resx">
      <DependentUpon>ucCell.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\ucManagePosition.resx">
      <DependentUpon>ucManagePosition.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\ucMANAGE_PLAN_IN.resx">
      <DependentUpon>ucMANAGE_PLAN_IN.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\ucPLAN_QUERY.resx">
      <DependentUpon>ucPLAN_QUERY.cs</DependentUpon>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <EmbeddedResource Include="uc\ucStockIn.resx">
      <DependentUpon>ucStockIn.cs</DependentUpon>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <Compile Include="proxy\CFClientBase.cs" />
    <Compile Include="proxy\Enum.cs" />
    <Compile Include="proxy\S_PDAService.cs" />
    <Compile Include="uc\Common\DataGridCustomCheckBoxColumn.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\DataGridCustomColumnBase.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\DataGridCustomComboBoxColumn.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\DataGridCustomDateTimePickerColumn.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\DataGridCustomTextBoxColumn.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\DataGridCustomUpDownColumn.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\myDataGrid.cs">
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="uc\Common\ucGrid.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\Common\ucGrid.Designer.cs">
      <DependentUpon>ucGrid.cs</DependentUpon>
    </Compile>
    <Compile Include="uc\Common\ucList.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\Common\ucList.designer.cs">
      <DependentUpon>ucList.cs</DependentUpon>
    </Compile>
    <Compile Include="uc\ucCell.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\ucCell.Designer.cs">
      <DependentUpon>ucCell.cs</DependentUpon>
    </Compile>
    <Compile Include="uc\ucManagePosition.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\ucManagePosition.Designer.cs">
      <DependentUpon>ucManagePosition.cs</DependentUpon>
    </Compile>
    <Compile Include="uc\ucMANAGE_PLAN_IN.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\ucMANAGE_PLAN_IN.Designer.cs">
      <DependentUpon>ucMANAGE_PLAN_IN.cs</DependentUpon>
    </Compile>
    <Compile Include="uc\ucPLAN_QUERY.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\ucPLAN_QUERY.Designer.cs">
      <DependentUpon>ucPLAN_QUERY.cs</DependentUpon>
    </Compile>
    <Compile Include="uc\ucStockIn.cs">
      <SubType>UserControl</SubType>
    </Compile>
    <Compile Include="uc\ucStockIn.Designer.cs">
      <DependentUpon>ucStockIn.cs</DependentUpon>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Content Include="%40Images\SiasunWMS.ico">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="App.config">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  <Import Condition="'$(TargetFrameworkVersion)' == 'v1.0'" Project="$(MSBuildBinPath)\Microsoft.CompactFramework.CSharp.v1.targets" />
  <Import Condition="'$(TargetFrameworkVersion)' == 'v2.0'" Project="$(MSBuildBinPath)\Microsoft.CompactFramework.CSharp.targets" />
  <ProjectExtensions>
    <VisualStudio>
      <FlavorProperties GUID="{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}">
        <HostingProcess disable="1" />
      </FlavorProperties>
    </VisualStudio>
  </ProjectExtensions>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
  <Import Condition="'$(TargetFrameworkVersion)' == 'v3.5'" Project="$(MSBuildBinPath)\Microsoft.CompactFramework.CSharp.targets" />
</Project>