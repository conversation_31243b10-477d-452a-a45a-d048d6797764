﻿using System;
using System.Data;
using System.Linq;
using System.Text;
using Quartz;

namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class SystemCleanJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            try
            {
                string strDel = string.Empty;
                int refCount = 0;

                Program.sysLog.DebugFormat("SystemCleanJob开始");

                //清除sys_log 和control_apply_his
                string sSysLogClearTh = string.Empty;
                int iSysLogClearTh = 0;
                if (MainApp.BaseService._S_SystemService.GetSysParameter("SysLogClearThreshold", out sSysLogClearTh) &&
                    int.TryParse(sSysLogClearTh, out iSysLogClearTh) && iSysLogClearTh > 0)
                {
                    //清除sys_log
                    strDel = string.Format(@"delete from SYS_LOG where LOG_DATE<'{0}'", System.DateTime.Now.AddMonths(-iSysLogClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清理{1}个月前的sys_log日志信息，删除{0}条", refCount, sSysLogClearTh);
                    }

                    //清除control_apply_his
                    strDel = string.Format(@"delete from IO_CONTROL_APPLY_HIS where CREATE_TIME<'{0}'", System.DateTime.Now.AddMonths(-iSysLogClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清理{1}个月前的IO_CONTROL_APPLY_HIS历史申请信息，删除{0}条", refCount, sSysLogClearTh);
                    }
                }

                //清除计划
                string sPlanClearTh = string.Empty;
                int iPlanClearTh = 0;
                if (MainApp.BaseService._S_SystemService.GetSysParameter("PlanClearThreshold", out sPlanClearTh) &&
                    int.TryParse(sPlanClearTh, out iPlanClearTh) && iPlanClearTh > 0)
                {
                    //清除PLAN_DETAIL
                    strDel = string.Format(@"delete from PLAN_DETAIL where PLAN_DETAIL.PLAN_LIST_ID in (select PLAN_LIST.PLAN_LIST_ID from PLAN_LIST where PLAN_LIST.PLAN_ID in (select PLAN_MAIN.PLAN_ID from PLAN_MAIN where PLAN_MAIN.PLAN_CREATE_TIME < '{0}'))", DateTime.Now.AddMonths(-iPlanClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清除{1}个月前的PLAN_DETAIL信息，删除{0}条", refCount, sPlanClearTh);
                    }
                    //清除PLAN_LIST
                    strDel = string.Format(@"delete from PLAN_LIST where PLAN_LIST.PLAN_ID in (select PLAN_MAIN.PLAN_ID from PLAN_MAIN where PLAN_MAIN.PLAN_CREATE_TIME < '{0}')", DateTime.Now.AddMonths(-iPlanClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清除{1}个月前的PLAN_LIST信息，删除{0}条", refCount, sPlanClearTh);
                    }

                    //清除PLAN_MAIN
                    strDel = string.Format(@"delete from PLAN_MAIN where PLAN_MAIN.PLAN_CREATE_TIME < '{0}'", DateTime.Now.AddMonths(-iPlanClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清除{1}个月前的PLAN_MAIN信息，删除{0}条", refCount, sPlanClearTh);
                    }
                }

                //清除记录信息
                string sRecordClearTh = string.Empty;
                int iRecordClearTh = 0;
                if (MainApp.BaseService._S_SystemService.GetSysParameter("RecordClearThreshold", out sRecordClearTh) &&
                    int.TryParse(sRecordClearTh, out iRecordClearTh) && iRecordClearTh > 0)
                {
                    //清除RECORD_DETAIL
                    strDel = string.Format(@"delete from RECORD_DETAIL where RECORD_DETAIL.RECORD_LIST_ID in (select RECORD_LIST.RECORD_LIST_ID from RECORD_LIST where RECORD_LIST.RECORD_ID in (select RECORD_MAIN.RECORD_ID from RECORD_MAIN where RECORD_MAIN.MANAGE_BEGIN_TIME < '{0}'))", DateTime.Now.AddMonths(-iRecordClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清除{1}个月前的RECORD_DETAIL信息，删除{0}条", refCount, iRecordClearTh);
                    }
                    //清除RECORD_LIST
                    strDel = string.Format(@"delete from RECORD_LIST where RECORD_LIST.RECORD_ID in (select RECORD_MAIN.RECORD_ID from RECORD_MAIN where RECORD_MAIN.MANAGE_BEGIN_TIME < '{0}')", DateTime.Now.AddMonths(-iRecordClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清除{1}个月前的RECORD_LIST信息，删除{0}条", refCount, iRecordClearTh);
                    }

                    //清除RECORD_MAIN
                    strDel = string.Format(@"delete from RECORD_MAIN where RECORD_MAIN.MANAGE_BEGIN_TIME < '{0}'", DateTime.Now.AddMonths(-iRecordClearTh).ToString("yyyy-MM-dd HH:mm:ss"));
                    refCount = MainApp.BaseService.ExecuteNonQuery_ReturnInt(strDel);
                    if (refCount > 0)
                    {
                        Program.sysLog.InfoFormat("清除{1}个月前的RECORD_MAIN信息，删除{0}条", refCount, iRecordClearTh);
                    }
                }
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("Job_SystemClean结束", ex);
                throw ex;
            }
            finally
            {
                Program.sysLog.DebugFormat("Job_SystemClean结束");
            }
        }
    }
}
