﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace SSLMS.MobileUI.uc
{
    public partial class ucMANAGE_QUERY : UserControl
    {
        public ucMANAGE_QUERY()
        {
            InitializeComponent();
            this.ucdgvIO_MANAGE.dgv.CurrentCellChanged += new EventHandler(dgv_CurrentCellChanged);
            this.Init();
        }

        public DataRow drMANAGE
        {
            get
            {
                return this.ucdgvIO_MANAGE.dgv.GetCurDataRow();

            }
        }

        private void Init()
        {
            this.MANAGE_Bind();

            this.dgv_CurrentCellChanged(null, null);
        }

        void dgv_CurrentCellChanged(object sender, EventArgs e)
        {
              string MANAGE_ID  = "0";

            if (drMANAGE!= null)
            {
                MANAGE_ID = drMANAGE["MANAGE_ID"].ToString();
            }



            this.MANAGE_LIST_Bind(MANAGE_ID);

        }


        private void MANAGE_Bind()
        {
            this.ucdgvIO_MANAGE.sTable = "V_IO_MANAGE";

            this.ucdgvIO_MANAGE.sXml = "V_IO_MANAGE";

            this.ucdgvIO_MANAGE.sWhere = string.Empty;

            this.ucdgvIO_MANAGE.Init();
        }

        private void MANAGE_LIST_Bind(string MANAGE_ID)
        {
            this.uclMANAGE_LIST.listXml = "V_IO_MANAGE_LIST";

            this.uclMANAGE_LIST.listTable = "V_IO_MANAGE_LIST";

            this.uclMANAGE_LIST.listWhere = string.Format("AND MANAGE_ID ={0}", MANAGE_ID);

            this.uclMANAGE_LIST.colGroup = "GOODS_TYPE_ID";

            this.uclMANAGE_LIST.colSplit = "GOODS_PROPERTY";

            this.uclMANAGE_LIST.Init();
        }


        private void btnRefresh_Click(object sender, EventArgs e)
        {
            this.Init();
        }

    }
}
