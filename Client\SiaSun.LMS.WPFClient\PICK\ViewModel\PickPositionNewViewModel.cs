﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using SiaSun.LMS.WPFClient.MVVM;
using SiaSun.LMS.Model;
using System.Data;

using System.Collections.ObjectModel;
using System.Windows.Input;
//using SiaSun.LMS.WPFClient.PICK.ViewModel.Box;
using SiaSun.LMS.WPFClient.PICK.Model;

using System.Drawing.Printing;
using System.Drawing;
using SiaSun.LMS.WPFClient.MVVM.Message;
using Microsoft.AspNet.SignalR.Client.Hubs;

namespace SiaSun.LMS.WPFClient.PICK.ViewModel
{
    public class PickPositionNewViewModel : ObservableObject
    {
        private T_PICK_POSITION _mT_PICK_POSITION = new T_PICK_POSITION();
        private DataTable _PlanDataTable;
        private string _PositionStockBarcode;
        private int _Plan_ID;
        private bool _bPick;

        #region print define
        public PrintDocument printDocumentForWbs = new PrintDocument();
        public PrinterHelper printHelperForWbs = new PrinterHelper();
        private int width_p = 100;//单位是mm
        private int height_p = 30;//单位是mm
        private int margin_lr = 2;//左右边距
        private int margin_tb = 2;//上下边距
        #endregion

        #region error_operation_define
        private string _CheckStringForOperation;
        private bool _ErrerOperationShow;
        #endregion
        public string PositionName
        {
            get
            {
                return _mT_PICK_POSITION.POSITON_NAME;
            }

            set
            {
                if(_mT_PICK_POSITION.POSITON_NAME != value)
                {
                    _mT_PICK_POSITION.POSITON_NAME = value;
                    RaisePropertyChanged("PositionName");
                }
            }
        }

        public string PositionCode
        {
            get
            {
                return _mT_PICK_POSITION.POSITION_CODE;
            }

            set
            {
                if (_mT_PICK_POSITION.POSITION_CODE != value)
                {
                    _mT_PICK_POSITION.POSITION_CODE = value;
                    RaisePropertyChanged("POSITION_CODE");
                }
            }
        }

        public DataTable PlanDataTable
        {
            get
            {
                return _PlanDataTable;
            }

            set
            {
                if (_PlanDataTable!= value)
                {
                    _PlanDataTable = value;
                    RaisePropertyChanged("PlanDataTable");
                }
            }

        }

        public string PositionStockBarcode
        {
            get
            {
                return _PositionStockBarcode;
            }

            set
            {
                if(_PositionStockBarcode != value)
                {
                    _PositionStockBarcode = value;
                    RaisePropertyChanged("PositionStockBarcode");
                }
            }
        }

        public int Plan_ID
        {
            get
            {
                return _Plan_ID;
            }

            set
            {
                if (_Plan_ID != value)
                {
                    _Plan_ID = value;
                    RaisePropertyChanged("Plan_ID");
                }
            }
        }

        public bool bPick
        {
            get
            {
                return _bPick;
            }

            set
            {
                if (_bPick != value)
                {
                    _bPick = value;
                    RaisePropertyChanged("bPick");
                }
            }
        }
        public string CheckStringForOperation
        {
            get
            {
                return _CheckStringForOperation;
            }

            set
            {
                if (_CheckStringForOperation != value)
                {
                    _CheckStringForOperation = value;
                    RaisePropertyChanged("CheckStringForOperation");
                }
            }
        }

        public bool ErrerOperationShow
        {
            get
            {
                return _ErrerOperationShow;
            }

            set
            {
                if (_ErrerOperationShow != value)
                {
                    _ErrerOperationShow = value;
                    RaisePropertyChanged("ErrerOperationShow");
                }
            }
        }

        private T_PICK_STATION fatherT_PICK_STATION;

        public PickPositionNewViewModel(T_PICK_POSITION mT_PICK_POSITION, T_PICK_STATION mT_PICK_STATION,IMessageManager parentMessageManager) : this()
        {
            ErrerOperationShow = false;

            this._mT_PICK_POSITION = mT_PICK_POSITION;
            this.fatherT_PICK_STATION = mT_PICK_STATION;
            SiaSun.LMS.Model.STORAGE_MAIN mSTORAGE_MAIN = MainApp._I_StorageService.Get_STORAGE_MAIN_BY_CELL_ID(mT_PICK_POSITION.WH_CELL_ID);
            if (mSTORAGE_MAIN != null)
            {
                this._PositionStockBarcode = mSTORAGE_MAIN.STOCK_BARCODE;
            }
            this.MsgManager = parentMessageManager;
            //this._PlanDataTable =
            //   MainApp._I_BaseService.GetList(string.Format(@"SELECT PLAN_CODE,PLAN_ID FROM PLAN_MAIN 
            //                                                                        WHERE PLAN_RELATIVE_CODE 
            //                                                                        IN (SELECT T_PICK_STATION.PLAN_GROUP_NAME FROM T_PICK_STATION	
            //                                                                        WHERE T_PICK_STATION.STATION_ID={0})",
            //                                                                       this._mT_PICK_POSITION.STATION_ID));
            //this._PlanDataTable =
            //  MainApp._I_BaseService.GetList(string.Format(@"SELECT PLAN_CODE,PLAN_ID FROM PLAN_MAIN 
            //                                                                        WHERE PLAN_GROUP 
            //                                                                        IN (SELECT T_PICK_STATION.PLAN_GROUP_CODE FROM T_PICK_STATION	
            //                                                                        WHERE T_PICK_STATION.STATION_ID={0})",
            //                                                                      this._mT_PICK_POSITION.STATION_ID));

            this._PlanDataTable =
              MainApp._I_BaseService.GetList(string.Format(@"SELECT PLAN_CODE,PLAN_ID FROM PLAN_MAIN 
                                                                                    WHERE PLAN_GROUP 
                                                                                    IN (SELECT T_PICK_POSITION.POSITION_CODE FROM T_PICK_POSITION	
                                                                                    WHERE T_PICK_POSITION.POSITION_ID={0})",
                                                                                  this._mT_PICK_POSITION.POSITION_ID));

            SiaSun.LMS.Model.T_PICK_POSITION_PLAN_BIND mT_PICK_POSITION_PLAN_BIND
                = MainApp._I_StorageService.Get_T_PICK_POSITION_PLAN_BIND_BY_PICK_POSITION_ID(mT_PICK_POSITION.POSITION_ID);

            if (mT_PICK_POSITION_PLAN_BIND != null)
            {
                this._Plan_ID = mT_PICK_POSITION_PLAN_BIND.PLAN_ID;
            }

            if((!string.IsNullOrEmpty(mT_PICK_POSITION.POSITION_CODE)) &&string.IsNullOrEmpty(mT_PICK_POSITION.REMARK) )
            {
                this.bPick = true;
            }
            else
            {
                this.bPick= false;
            }
            //InitPrintSetting();
        }

        public PickPositionNewViewModel()
        {
            //this._PlanDataTable = MainApp._I_BaseService.GetList(@"SELECT PLAN_ID,PLAN_CODE,PLAN_RELATIVE_CODE,
            //PLAN_GROUP,PLAN_TYPE_CODE,PLAN_CREATE_TIME,PLAN_GROUP FROM PLAN_MAIN");
            //Message.MainRegister mPickPositionMsgRegister = new Message.MainRegister();
            //this.MsgManager = MVVM.Message.MessageManager.Default;

            //mPickPositionMsgRegister.MsgManager = this.MsgManager;
            //mPickPositionMsgRegister.Register();

        }

        #region method
        private void InitInfo()
        {
            PrinterSettings printSetting = new PrinterSettings();
            printSetting.PrintRange = PrintRange.AllPages;
            int width_in = MM2Inch(width_p);
            int height_in = MM2Inch(height_p);
            PageSettings pageSetting = new PageSettings(printSetting);
            pageSetting.PaperSize = new PaperSize("customer", width_in, height_in);

            int margin_lr_in = MM2Inch(margin_lr);
            int margin_tb_in = MM2Inch(margin_tb);
            pageSetting.Margins = new Margins(margin_lr_in, margin_lr_in, margin_tb_in, margin_tb_in);
            this.printDocumentForWbs.DefaultPageSettings = pageSetting;
        }

        private void CreateInfoForWbs()
        {
            List<PrintInfo> lstPrintInfos = new List<PrintInfo>();
            SiaSun.LMS.Model.PLAN_MAIN mPLAN_MAIN = MainApp._I_PlanService.PlanGetModel(this._Plan_ID);
           if (mPLAN_MAIN == null)
            {
                return;
            }
            if (fatherT_PICK_STATION == null)
            {
                return;
            }
            //标题配置
            PrintInfo p1 = new PrintInfo();
            p1.PrtType = PrintType.Text;
            p1.PrtColor = Color.Black;
            p1.Content = "WBS:"+mPLAN_MAIN.PLAN_CODE;
            p1.Size = 8;
            p1.FontStyle = System.Drawing.FontStyle.Bold;
            p1.Start = new System.Drawing.Point(2, 2);
            lstPrintInfos.Add(p1);

            //标题配置
            PrintInfo p2 = new PrintInfo();
            p2.PrtType = PrintType.Text;
            p2.PrtColor = Color.Black;
            p2.Content = "描述:" + mPLAN_MAIN.PLAN_DESCRIPTION;
            p2.Size = 8;
            p2.FontStyle = System.Drawing.FontStyle.Bold;
            p2.Start = new System.Drawing.Point(2, 5);
            lstPrintInfos.Add(p2);

            //标题配置
            PrintInfo p3 = new PrintInfo();
            p3.PrtType = PrintType.Text;
            p3.PrtColor = Color.Black;
            p3.Content = "抬头文本:" + mPLAN_MAIN.PLAN_HEADTEXT;
            p3.Size = 8;
            p3.FontStyle = System.Drawing.FontStyle.Bold;
            p3.Start = new System.Drawing.Point(2, 8);
            lstPrintInfos.Add(p3);

            //打印时间
            PrintInfo p5 = new PrintInfo();
            p5.PrtType = PrintType.Text;
            p5.PrtColor = Color.Black;
            p5.Content ="由"+ fatherT_PICK_STATION.STATION_NAME+"打印于"+DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
            p5.Size = 8;
            p5.FontStyle = System.Drawing.FontStyle.Bold;
            p5.Start = new System.Drawing.Point(2, 25);

            lstPrintInfos.Add(p5);

            PrintInfo p6 = new PrintInfo();
            p6.PrtType = PrintType.BarcodeImage;
            p6.BarcodeSettings = new BarcodeSetting
            {
                BarcodeType = BarcodeLib.TYPE.CODE128,
                BarcodeEncodeData = mPLAN_MAIN.PLAN_CODE,
                BarcodeHeight = 30,
                BarcodeWidth = 80,
                BarcodeBackColor = Color.White,
                BarcodeForeColor = Color.Black,
                BarcodeIncludeLabel = false,
                BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                BarcodeMinBarWidth = 0.1,
                BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                BarcodePrintDpi = 300,
                BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                BarcodeAutoBarWidth = true,
            };

            p6.Start = new System.Drawing.Point(5, 12);

            lstPrintInfos.Add(p6);
            printHelperForWbs.PrintInfos = lstPrintInfos;
        }

        private void PrintDocumentForWbs_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            g.PageScale = 1;
            g.PageUnit = GraphicsUnit.Millimeter;//单位
           
            printHelperForWbs.Print(g);
        }

        /// <summary>
        /// 转换毫米到百分之一英寸
        /// </summary>
        /// <param name="mm"></param>
        /// <returns></returns>
        private int MM2Inch(int mm)
        {
            return (int)(mm * 100.0f / 25.4f);
        }

        private void InitPrintSetting()
        {
            InitInfo();
            CreateInfoForWbs();
            printDocumentForWbs.PrintPage += PrintDocumentForWbs_PrintPage;
        }

        private DataTable GetDataTableOfPositionStorage()
        {
            DataTable dt = new DataTable();
            try
            {
                DataTable sourceTable = new DataTable();

                sourceTable = MainApp._I_BaseService.GetList(string.Format(@"SELECT * FROM V_STORAGE_LIST WHERE CELL_ID ={0} ORDER BY STORAGE_LIST_ID", _mT_PICK_POSITION.WH_CELL_ID));

                if (sourceTable != null)
                {
                    dt.Columns.Add("物料编码");
                    dt.Columns.Add("物料名称");
                    dt.Columns.Add("数量");
                    dt.Columns.Add("单位");
                    dt.Columns.Add("拣选任务单号");
                    dt.Columns.Add("拣选项目号");
                    dt.Columns.Add("拣选WBS号");
                    dt.Columns.Add("ABC分类");
                    //dt.Columns.Add("容器条码");
                    //dt.Columns.Add("货位编码");

                    for(int r = 0; r < sourceTable.Rows.Count; r++)
                    {
                        DataRow dr = dt.NewRow();

                        dr["物料编码"] = sourceTable.Rows[r]["GOODS_CODE"].ToString();
                        dr["物料名称"] = sourceTable.Rows[r]["GOODS_NAME"].ToString();
                        dr["数量"] = sourceTable.Rows[r]["STORAGE_LIST_QUANTITY"].ToString();
                        dr["单位"] = sourceTable.Rows[r]["GOODS_UNITS"].ToString();
                        dr["拣选任务单号"] = sourceTable.Rows[r]["GOODS_PROPERTY6"].ToString();
                        dr["拣选项目号"] = sourceTable.Rows[r]["GOODS_PROPERTY7"].ToString();
                        dr["拣选WBS号"] = sourceTable.Rows[r]["GOODS_PROPERTY8"].ToString();
                        dr["ABC分类"] = sourceTable.Rows[r]["GOODS_CONST_PROPERTY1"].ToString();
                        //dr["容器条码"] = sourceTable.Rows[r]["STOCK_BARCODE"].ToString();
                        //dr["货位编码"] = sourceTable.Rows[r]["CELL_CODE"].ToString();

                        dt.Rows.Add(dr);
                    }
                }
            }
            catch (Exception ex)
            {
                dt = new DataTable();
            }

            return dt;
        } 
        #endregion

        #region command
        void PickExecute()
        {

            //if (this._mT_PICK_POSITION != null)
            //{
            //    string msgstr = string.Format("拣选按钮触发:\nPositionCode:{0}\nPositionName:{1}\n托盘条码:{2}"
            //        , this._mT_PICK_POSITION.POSITION_CODE
            //         , this._mT_PICK_POSITION.POSITON_NAME
            //       , this._PositionStockBarcode);

            //    var msg = new MVVM.Message.ConfirmMsgArgs("是否要把当前按钮颜色变成蓝色？", msgstr
            //                );
            //    this.MsgManager.SendMsg("ShowConfirmBox", msg);
            //    if (msg.Result)
            //    {
            //        this.MsgManager.SendMsg("ShowBox", "Pick");
            //        this.bPick = true;
            //    }
            //    else
            //    { this.MsgManager.SendMsg("ShowBox", "Cancel");
            //        this.bPick = false;
            //    }
            //}
            if (this._mT_PICK_POSITION != null)
            {
                if (this.Plan_ID == 0)
                {
                    return;
                }
                Dialog.DataGridDialog dialog = new Dialog.DataGridDialog(this._mT_PICK_POSITION.POSITON_NAME + "WBS清单明细", "V_PICK_POSITION_BIND", string.Format("PLAN_ID={0}", this._Plan_ID), "PICK_POSITION_PLAN_BIND_ID", true, "");

                dialog.MinWidth = 800;
                dialog.MinHeight = 400;
                dialog.MaxHeight = 700;
                dialog.ShowDialog();
                //string msgstr = string.Format("按钮触发:\nPositionCode:{0}\nPositionName:{1}\n托盘条码:{2}"
                //    , this._mT_PICK_POSITION.POSITION_CODE
                //     , this._mT_PICK_POSITION.POSITON_NAME
                //   , this._PositionStockBarcode);

                //var msg = new MVVM.Message.ConfirmMsgArgs("是否要把当前按钮颜色变成蓝色？", msgstr);
                //this.MsgManager.SendMsg("ShowConfirmBox", msg);
                //if (msg.Result)
                //{
                //    this.MsgManager.SendMsg("ShowBox", "Pick");
                //    this.bPick = true;
                //}
                //else
                //{
                //    this.MsgManager.SendMsg("ShowBox", "Cancel");
                //    this.bPick = false;
                //}

            }
            //this.MsgManager.SendMsg("ShowBox", "这是提示");

        }

        bool CanPickExecute()
        {
            return true;
        }

        public ICommand CommandPick { get { return new RelayCommand(PickExecute, CanPickExecute); } }

        void LockExecute()
        {

            if (this._mT_PICK_POSITION != null)
            {
                if (this.Plan_ID == 0)
                {
                    return;
                }
                Dialog.DataGridDialog dialog = new Dialog.DataGridDialog(this._mT_PICK_POSITION.POSITON_NAME + "WBS待拣明细", "V_PICK_POSITION_BIND", string.Format("PLAN_ID={0} AND PLAN_LIST_PICKED_QUANTITY<PLAN_LIST_QUANTITY", this._Plan_ID), "PICK_POSITION_PLAN_BIND_ID", true, "");

                dialog.MinWidth = 800;
                dialog.MinHeight = 400;
                dialog.MaxHeight = 700;
                dialog.ShowDialog();
                //string msgstr = string.Format("按钮触发:\nPositionCode:{0}\nPositionName:{1}\n托盘条码:{2}"
                //    , this._mT_PICK_POSITION.POSITION_CODE
                //     , this._mT_PICK_POSITION.POSITON_NAME
                //   , this._PositionStockBarcode);

                //var msg = new MVVM.Message.ConfirmMsgArgs("是否要把当前按钮颜色变成蓝色？", msgstr);
                //this.MsgManager.SendMsg("ShowConfirmBox", msg);
                //if (msg.Result)
                //{
                //    this.MsgManager.SendMsg("ShowBox", "Pick");
                //    this.bPick = true;
                //}
                //else
                //{
                //    this.MsgManager.SendMsg("ShowBox", "Cancel");
                //    this.bPick = false;
                //}

            }
            //this.MsgManager.SendMsg("ShowBox", "这是提示");

        }

        bool CanLockExecute()
        {
            return true;
        }

        public ICommand CommandLock { get { return new RelayCommand(LockExecute, CanLockExecute); } }

        void PrintWBSContentExecute()
        {
            //if (this.Plan_ID == 0)
            //{
            //    return;
            //}
            //printDocumentForWbs.Print();

            //new
            try
            {
                DataTable PrintDataTable = this.GetDataTableOfPositionStorage();

                if (PrintDataTable.Rows.Count == 0)
                {
                    return;
                }

                string HeadTitle = string.Format("齐套箱 {0} 库存明细", this._PositionStockBarcode);
                string SubTitle = string.Format("用户:{0}/{1} 于{2} 在{3}打印此表格",
                    MainApp._USER.USER_NAME,
                    MainApp._USER.USER_CODE,
                    Common.StringUtil.GetDateTime(),
                    this._mT_PICK_POSITION.POSITON_NAME);

                PrintHelperForDataTable printHelperForDataTable = new PrintHelperForDataTable();

                printHelperForDataTable.Print(PrintDataTable, string.Format("{0}|{1}", HeadTitle, SubTitle));

            }
            catch(Exception ex)
            {
               
            }

        }

        bool CanPrintWBSContentExecute()
        {
            return true;
        }

        public ICommand CmdPrintWBSContent { get { return new RelayCommand(PrintWBSContentExecute, CanPrintWBSContentExecute); } }



        private MVVM.RelayCommand<Object> _MouseUpCommand;
        public MVVM.RelayCommand<Object> MouseUpCommand
        {
            get
            {
                if (_MouseUpCommand == null)
                {
                    //_MouseUpCommand = new MVVM.RelayCommand<Object>(
                    //      new Action<object>(
                    //          o => System.Windows.MessageBox.Show("todo 库存-WBS信息")));
                    _MouseUpCommand = new MVVM.RelayCommand<Object>(
                          new Action<object>(
                              o =>
                              {
                                  string sResult = string.Empty;
                                  //MainWindow.mainWin.ActivatForm("SiaSun.LMS.WPFClient.Dialog.DataGridWindow", "title", new object[] { "", "GOODS_WERKS_LGORT_PROPERTY", "GOODS_WERKS_LGORT_PROPERTY_ID>0", "GOODS_WERKS_LGORT_PROPERTY_ORDER", true, "" }, out sResult);
                                  Dialog.DataGridDialog dialog = new Dialog.DataGridDialog(_mT_PICK_POSITION.POSITON_NAME + "库存", "V_STORAGE_LIST", string.Format("CELL_ID ={0}", _mT_PICK_POSITION.WH_CELL_ID), "STORAGE_LIST_ID", true, "");
                                  dialog.MinWidth = 800;
                                  dialog.MinHeight = 400;
                                  dialog.ShowDialog();
                              }));
                }
                return _MouseUpCommand;
            }
        }


        void CommandLockAllExecute()
        {
            string sResult = string.Empty;
            bool bResult = false;

            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码:{0}对应的订单锁定至{1}?", this.PositionCode, this.PositionName));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    bResult = MainApp._I_StorageService.LockAndOutOrderByPickPosition(MainApp._USER, this._mT_PICK_POSITION.POSITION_ID, this.PositionCode, out sResult);

                    this.ShowResult(bResult, sResult);
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }

        }

        bool CanCommandLockAllExecute()
        {
            return true;
        }

        public ICommand CommandLockAll { get { return new RelayCommand(CommandLockAllExecute, CanCommandLockAllExecute); } }


        void CommandLockPartlyExecute()
        {
            string sResult = string.Empty;
            bool bResult = false;

            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码:{0}对应的订单锁定至{1}?本次锁定为部分锁定，如果物料短缺，也可以锁定成功!", this.PositionCode, this.PositionName));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    bResult = MainApp._I_StorageService.LockAndOutOrderPartlyByPickPosition(MainApp._USER, this._mT_PICK_POSITION.POSITION_ID, this.PositionCode, out sResult);

                    //this.ShowResult(bResult, sResult);
                    if (bResult)
                    {
                        if (string.IsNullOrEmpty(sResult))
                        {
                            MsgManager.SendMsg("ShowBox", string.Format("锁定库存成功!"));
                        }
                        else
                        {
                            MsgManager.SendMsg("ShowBox", string.Format("锁定库存成功!以下物料缺少库存{0}", sResult));
                        }
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }

        }

        bool CanCommandLockPartlyExecute()
        {
            return true;
        }

        public ICommand CommandLockPartly { get { return new RelayCommand(CommandLockPartlyExecute, CanCommandLockPartlyExecute); } }


        //CommandOut
        void CommandOutExecute()
        {
            string sResult = string.Empty;
            bool bResult = false;
            try
            {
                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否将唯一码:{0}对应的订单锁定下架至{1}?", this.PositionCode, this.PositionName));
                MsgManager.SendMsg("ShowConfirmBox", msg);
                if (msg.Result)
                {
                    bResult = MainApp._I_StorageService.ManageDownOutByPickPosition(MainApp._USER, this._mT_PICK_POSITION.POSITION_ID, this.PositionCode, out sResult);

                    this.ShowResult(bResult, sResult);
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                this.ShowResult(bResult, sResult);
            }

        }

        bool CanCommandOutExecute()
        {
            return true;
        }

        public ICommand CommandOut { get { return new RelayCommand(CommandOutExecute, CanCommandOutExecute); } }


        //CommandUnBind
        void CommandUnBindExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {

                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否解除{0}所绑定的拣选订单,唯一码:{1}?", this.PositionName, this.PositionCode));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    string group = string.Empty;
                    bResult = MainApp._I_StorageService.UnBindOrderPickPosition(MainApp._USER, _mT_PICK_POSITION.POSITION_ID, out group, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }

        }

        bool CanCommandUnBindExecute()
        {
            return true;
        }

        public ICommand CommandUnBind { get { return new RelayCommand(CommandUnBindExecute, CanCommandUnBindExecute); } }

        public void ShowResult(bool bResult, string sResult)
        {
            if (bResult)
            {
                MsgManager.SendMsg("ShowBox", "操作成功!");
            }
            else
            {
                MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
            }
        }



        void ErrorOperationSubmitExecute()
        {

            var msg = new MVVM.Message.ConfirmMsgArgs(
                            "请确认!",
                            "是否开启异常处理模式？");
            MsgManager.SendMsg("ShowConfirmBox", msg);
            if (msg.Result)
            {
                if (this._CheckStringForOperation.Equals("`123qwe"))
                {
                    ErrerOperationShow = true;
                    MsgManager.SendMsg("ShowBox", "yes");
                }
                else
                {
                    ErrerOperationShow = false;
                    MsgManager.SendMsg("ShowBox", "验证码错误");
                }

            }
            else
            {
                MsgManager.SendMsg("ShowBox", "已取消");
            }
        }

        bool CanErrorOperationSubmitExecute()
        {
            return true;
        }

        public ICommand CmdErrorOperationSubmit { get { return new RelayCommand(ErrorOperationSubmitExecute, CanErrorOperationSubmitExecute); } }


        /// <summary>
        /// 强制将唯一码与拣选工作工位断开
        /// </summary>
        void ErrorOperationUnBindOrderExecute()
        {
            string sResult = string.Empty;
            bool bResult = true;
            try
            {

                var msg = new MVVM.Message.ConfirmMsgArgs(
                                "请确认!",
                                string.Format("是否强制解除{0}所绑定的拣选订单,唯一码:{1}?", _mT_PICK_POSITION.POSITON_NAME, this.PositionCode));
                MsgManager.SendMsg("ShowConfirmBox", msg);

                if (msg.Result)
                {
                    string group = string.Empty;
                    bResult = MainApp._I_StorageService.ErrorOperationUnBindOrderPosition(MainApp._USER, _mT_PICK_POSITION.POSITION_ID, out group, out sResult);
                    if (bResult)
                    {
                        MsgManager.SendMsg("ShowBox", "操作成功!");
                    }
                    else
                    {
                        MsgManager.SendMsg("ShowBox", string.Format("操作失败!原因:{0}", sResult));
                    }
                }
                else
                {
                    MsgManager.SendMsg("ShowBox", "已取消操作!");
                }


            }
            catch (Exception ex)
            {

            }
        }
        bool CanErrorOperationUnBindOrderExecute()
        {
            return true;
        }
        public ICommand CmdErrorOperationUnBindOrder { get { return new RelayCommand(ErrorOperationUnBindOrderExecute, CanErrorOperationUnBindOrderExecute); } }

        #endregion
    }
}
