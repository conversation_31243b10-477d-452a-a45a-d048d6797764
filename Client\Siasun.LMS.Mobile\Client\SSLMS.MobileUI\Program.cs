﻿using System;
using System.Collections.Generic;
using System.ServiceModel;
using System.Windows.Forms;

namespace SSLMS.MobileUI
{
    public static class Program
    {
        public static string _BaseUrl = SSLMS.MobileUI.Common.GetUrl("SiaSunSrvUrl");

        public static BasicHttpBinding bind = new BasicHttpBinding(BasicHttpSecurityMode.None);

        public static I_PDAServiceClient _I_PDAService = new I_PDAServiceClient(bind, new EndpointAddress(_BaseUrl + "/PDAService"));
       

        //public static SystemService.SYS_ROLE _ROLE;

        public static SYS_USER _USER = new SYS_USER();


        /// <summary>
        /// 应用程序的主入口点。
        /// </summary>
        [MTAThread]
        static void Main()
        {
            bind.MaxReceivedMessageSize = 65535000;

            Application.Run(new LOGIN());
        }
    }
}