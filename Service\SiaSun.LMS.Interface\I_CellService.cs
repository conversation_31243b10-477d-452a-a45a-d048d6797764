﻿using System;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.ServiceModel;

using SiaSun.LMS.Model;

namespace SiaSun.LMS.Interface
{
    [ServiceContract]
    [ServiceKnownType(typeof(object))]
    public partial interface I_CellService
    {
        [OperationContract]
        DataTable WAREHOUSE_GetList(int USER_ID,string WAREHOUSE_TYPE);

        [OperationContract]
        DataTable AREA_GetList(int WAREHOUSE_ID, string AREA_TYPE);

        [OperationContract]
        DataTable CELL_Z_GetList(int AREA_ID);

        [OperationContract]
        DataTable CELL_Z_GetList_AREA(int WAREHOUSEID, int AREA_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z(int WAREHOUSE_ID, string CELL_Z);

        [OperationContract]
        IList<SiaSun.LMS.Model.WH_CELL> CELL_GetList_Z_ByArea( int Area_ID,string CELL_Z);


        [OperationContract]
        bool CellInit();

        [OperationContract]
        bool CellCreate(int WAREHOUSE_ID,
                        int AREA_ID,
                        int LOGIC_ID,
                        int z_begin,
                        int z_end,
                        int x_begin,
                        int x_end,
                        int y_begin,
                        int y_end,
                        string DEVICE_CODE,
                        string LANE_WAY,
                        string SHELF_TYPE,
                        string SHELF_NEIGHBOUR,
                        string CELL_MODEL,
                        string CELL_LOGICAL_NAME,
                        string CELL_INOUT,
                        string CELL_TYPE,
                        string CELL_STORAGE_TYPE,
                        string CELL_FORK_TYPE,
                        int CELL_FORK_COUNT,
                        int CELL_WIDTH,
                        int CELL_HEIGHT
                         );

        [OperationContract]
        bool StationCreate(int WAREHOUSE_ID,
                        int AREA_ID,
                        int LOGIC_ID,
                        string DEVICE_CODE,
                        string DEVICE_NAME,
                        string LANE_WAY,
                        string SHELF_TYPE,
                        string SHELF_NEIGHBOUR,
                        string CELL_MODEL,
                        string CELL_LOGICAL_NAME,
                        string CELL_INOUT,
                        string CELL_TYPE,
                        string CELL_STORAGE_TYPE,
                        string CELL_FORK_TYPE
                        );

        [OperationContract]
        bool CellInAllocate(string START_DEVICE_CODE,
                            string CELL_MODEL,
                            int MANAGE_ID,
                            int FORK_COUNT,
                            out int END_CELL_ID,
                            out string sResult);
        

        [OperationContract]
        bool CellOutAllocate(int LANE_WAY,
                             int END_CELL_ID,
                             int GOODS_ID,
                             string GOODS_PROPERTY,
                             decimal MANAGE_LIST_QUANTITY,
                             out int START_CELL_ID,
                             out string STOCK_BARCODE,
                             out decimal STORAGE_LIST_QUANTITY,
                             out string sResult);
        [OperationContract]
        bool CellUpdateStatus(int CELL_ID, 
                              string CELL_STATUS, 
                              string RUN_STATUS, 
                              out string sResult);

    }
}
