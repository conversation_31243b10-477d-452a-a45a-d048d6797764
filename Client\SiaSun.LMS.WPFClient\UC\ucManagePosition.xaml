﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucManagePosition"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             d:DesignHeight="211" d:DesignWidth="225" >
    <Border>
        <Grid>
            <WrapPanel ButtonBase.Click="WrapPanel_Click" Margin="0,0,0,6">

                <StackPanel  Name="panelStockBarcode" Orientation="Horizontal" Margin="5,5,5,5" >
                    <TextBlock Text="容器条码:" Margin="2" VerticalAlignment="Center"  />
                    <TextBox Name="txtStockBarcode" Margin="2,0,2,0" MinWidth="120" MinHeight="23" KeyDown="StockBarcode_KeyDown" />
                </StackPanel>
                <StackPanel  Name="panelCellModel" Orientation="Horizontal" Margin="5,5,5,5" >
                    <TextBlock Text="容器规格:" Margin="2" VerticalAlignment="Center" />
                    <ComboBox Name="cmbCellModel" Margin="2,0,2,0" MinWidth="120" MinHeight="23" SelectionChanged="cmbCellModel_SelectionChanged"/>
                </StackPanel>

                <StackPanel  Name="panelOccupyPercent" Orientation="Horizontal" Margin="5,5,5,5" >
                    <TextBlock Text="容器用率:" Margin="2" VerticalAlignment="Center" />
                    <ComboBox Name="cmbOccupyPercent" Margin="2,0,2,0" MinWidth="120" MinHeight="23" SelectionChanged="cmbOccupyPercent_SelectionChanged" />
                </StackPanel>

                <StackPanel Name="panelStartPosition"  Orientation="Horizontal" Margin="{Binding ElementName=panelCellModel,Path=Margin}">
                    <TextBlock Text="起始位置:" Margin="2" VerticalAlignment="Center"></TextBlock>
                    <ComboBox Name="cmbStartPosition" Margin="2,0,0,0" MinWidth="120" IsEditable="True"  AllowDrop="True" IsTextSearchEnabled="True" SelectionChanged="cmbStartPosition_SelectionChanged" StaysOpenOnEdit="True"></ComboBox>
                    <Button Name="btnStartPosition" Margin="1" Width="23" Height="21" >
                        <!--<Button.Background>
                            <ImageBrush ImageSource="/@Images/cell.png">
                            </ImageBrush>
                        </Button.Background>-->
                    </Button>
                </StackPanel>

                <StackPanel Name="panelEndPosition" Orientation="Horizontal" Margin="{Binding ElementName=panelCellModel,Path=Margin}">
                    <TextBlock Text="目标位置:" Margin="2" VerticalAlignment="Center"></TextBlock>

                    <ComboBox Name="cmbEndPosition" Margin="2,0,0,1" MinWidth="120" DisplayMemberPath="CELL_NAME" IsEditable="True" IsTextSearchEnabled="True" SelectedValuePath="CELL_ID" SelectionChanged="cmbEndPosition_SelectionChanged"></ComboBox>
                    <Button Name="btnEndPosition" Margin="1" Width="23" Height="21" Background="{Binding ElementName=btnStartPosition,Path=Background}"></Button>
                </StackPanel>

            </WrapPanel>
           
        </Grid>
    </Border>
        
    
</UserControl>
