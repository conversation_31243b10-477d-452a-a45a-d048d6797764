﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_STOCK_OUT : AvalonDock.DocumentContent
    {
        string strGoodsClassCode = string.Empty;
        string strAreaType = string.Empty;

        SiaSun.LMS.Model.MANAGE_TYPE mMANAGE_TYPE = null;

        Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM = null;


        List<Model.MANAGE_LIST> listMANAGE_LIST = null;

        public MANAGE_STOCK_OUT(string MANAGE_TYPE_CODE, string GOODS_CLASS_CODE ,string areaType)
        {
            InitializeComponent();

            this.strGoodsClassCode = GOODS_CLASS_CODE;
            this.strAreaType = areaType;

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler
                                        ((QueryWhere) =>
                                        {
                                            QueryWhere = string.Format(" and {0} and AREA_TYPE = '{1}' ", string.IsNullOrEmpty(QueryWhere) ? "1=1" : QueryWhere, areaType);
                                            this.StorageListBind(QueryWhere);
                                        }
                                        );

        }


        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.grpbManage.Header = string.Format(this.grpbManage.Tag.ToString(), this.Title);
            try
            {
                if (mMANAGE_TYPE == null)
                {
                    MainApp._MessageDialog.ShowException("未找到任务类型");
                    return;
                }
                else
                {
                    ManagePositin_Init();

                    ucStockOut_Init();

                    this.InitQueryControl();
                    
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 位置控件初始化
        /// </summary>
        private void ManagePositin_Init()
        {
            try
            {

                this.ucManagePosition.U_InitControl(this.mMANAGE_TYPE.MANAGE_TYPE_ID);
                mMANAGE_TYPE_PARAM = this.ucManagePosition.U_InitControl(this.mMANAGE_TYPE.MANAGE_TYPE_ID);

            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = "V_STORAGE_LIST";
            this.ucQuery.U_InitControl();
        }


        private void ucStockOut_Init()
        {
            try
            {
                Model.GOODS_CLASS mGOODS_CLASS = (Model.GOODS_CLASS)MainApp._I_BaseService.GetModel("GOODS_CLASS_SELECT_BY_GOODS_CLASS_CODE", strGoodsClassCode).RequestObject;

                this.ucStockOut.U_GoodsClassID = mGOODS_CLASS.GOODS_CLASS_ID;

                //this.ucStockOut.U_StockTypeChanged += new UC.ucSotckOut.U_StockTypeValueChangedHandler(ucStockOut_U_StockTypeChanged);

                //this.ucStockOut.U_Query += new UC.ucSotckOut.U_QueryHandler(ucTaskInput_U_Query);

                this.ucStockOut.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //void ucTaskInput_U_Query()
        //{
        //    this.StorageListBind();
        //}

        //void ucStockOut_U_StockTypeChanged()
        //{
        //    this.StorageListBind();
        //}

        private void Refresh()
        {
            try
            {
                this.ucManagePosition.U_Refresh();

                string queryWhere = this.ucQuery.U_GetQuery();

                this.StorageListBind(string.Format(" and AREA_TYPE='{1}' and {0}", string.IsNullOrEmpty(queryWhere) ? "1=1" : queryWhere, strAreaType));



            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

      

        private void StorageListBind(string QueryWhere )
        {
            this.ucStockStorageGroup.U_WindowName = this.GetType().Name;
            this.ucStockStorageGroup.U_TableName = "V_STORAGE_LIST";
            this.ucStockStorageGroup.U_XmlTableName = "V_STORAGE_LIST";
            this.ucStockStorageGroup.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.ucStockStorageGroup.U_OrderField = "STORAGE_LIST_ID";
            this.ucStockStorageGroup.U_Where = string.Format(" {0} and cell_id not in ( select start_cell_id from manage_main) {1} ", 
                                                            this.ucStockOut.U_StockType==0?string.Format("goods_class_id = {0}",this.ucStockOut.U_GoodsClassID):string.Format("goods_id ={0} " ,this.ucStockOut.U_StockType),
                                                            QueryWhere);
            this.ucStockStorageGroup.U_AllowOperatData = false;
            this.ucStockStorageGroup.U_AllowChecked = true;
            this.ucStockStorageGroup.U_AllowShowPage = true;

            //拆分列属性
            this.ucStockStorageGroup.U_SplitPropertyType = "GOODS_TYPE";
            this.ucStockStorageGroup.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.ucStockStorageGroup.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.ucStockStorageGroup.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.ucStockStorageGroup.U_InitControl();
        }


        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnCreateTask":
                        this.CreateStockTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建并下达任务
        /// </summary>
        private void CreateStockTask()
        {
            bool boolResult = true;

            string strResult = string.Empty;

            boolResult = this.ucManagePosition.U_CHECK_WAREHOUSE();

            if (!boolResult)
                return;

            if (MainApp._MessageDialog.ShowDialog("ConfirmCreateTask", null) == Sid.Windows.Controls.TaskDialogResult.Cancel)
                return;


            try
            {
                if (this.ucStockOut.U_StockOutAuto)
                {
                    boolResult = CreateContinuouseStockOut(out strResult);
                }
                else
                {
                    boolResult = CreateSingleStockOut(out strResult);
                }
            }
            catch (Exception ex)
            {
                boolResult = false;

                strResult = ex.Message;
            }

            if (boolResult)
            {
                //刷新
                this.Refresh();
            }

            MainApp._MessageDialog.ShowResult(boolResult, strResult);

        }


        /// <summary>
        /// 单一托盘出库
        /// </summary>
        private bool CreateSingleStockOut(out string strResult)
        {
            bool boolResult = false;
            bool boolSingleResult = true;
            strResult = "\n";
            string sSingleReslut = string.Empty;

            try
            {
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                {
                    return false;
                }

                List<DataRowView> listDataRowView = this.ucStockStorageGroup.U_GetCheckedDataRows().Cast<DataRowView>().ToList();
                if (listDataRowView.Count == 0)
                {
                    strResult = Enum.MessageConverter.SelectCount.ToString();
                    return false;
                }

                foreach (DataRowView drv in listDataRowView)
                {
                    drv["MANAGE_LIST_QUANTITY"] = drv["STORAGE_LIST_QUANTITY"];
                }

                var cell_id_group = from v in listDataRowView
                                    group v by v["CELL_ID"].ToString() into a
                                    select a;

                foreach (var cell_id in cell_id_group)
                {
                    int intStartPositionID = Convert.ToInt32(cell_id.Key);

                    var value_ceLl_id = from v in listDataRowView
                                        where v["CELL_ID"].ToString() == cell_id.Key
                                        select v;

                    DataRowView[] drvGroup = value_ceLl_id.Cast<DataRowView>().ToArray();

                    listMANAGE_LIST = new Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(drvGroup, null);

                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.CELL_MODEL= value_ceLl_id.First()["CELL_MODEL"].ToString();
                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                    mMANAGE_MAIN.START_CELL_ID = intStartPositionID;
                    mMANAGE_MAIN.STOCK_BARCODE = drvGroup[0]["STOCK_BARCODE"].ToString();

                    boolSingleResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                         "ManageCreate",
                                          new object[] { mMANAGE_MAIN,
                                                             listMANAGE_LIST,
                                                             true, 
                                                             this.ucManagePosition.U_AutoDownloadControlTask, 
                                                             this.ucManagePosition.U_AutoCompleteTask 
                                                            },
                                          out sSingleReslut);

                    if (!boolSingleResult)
                    {
                        strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达失败 " + sSingleReslut + "\n";
                        break;
                    }
                    else
                    {
                        strResult += value_ceLl_id.Cast<DataRowView>().ToArray()[0]["CELL_CODE"].ToString() + " 任务下达成功 " + "\n";
                        boolResult = true;
                    }
                }

                this.Refresh();
            }
            catch (Exception ex)
            {
                boolResult = false;
                strResult = ex.Message;
            }
            return boolResult;
        }


        ///// <summary>
        ///// 获得托盘集合列表
        ///// </summary>
        private IDictionary<string, int> GetPalletKeyValuePair(List<DataRowView> listDataRowView)
        {
            IDictionary<string, int> dicStack = new Dictionary<string, int>();
            foreach (DataRowView rowView in listDataRowView)
            {
                string stack =  rowView["STOCK_BARCODE"].ToString();
                if (stack != string.Empty)
                {
                    //获得货位编号
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", Convert.ToInt32(rowView["CELL_ID"])).RequestObject;
                    if (!dicStack.ContainsKey(stack))
                    {
                        dicStack.Add(stack, Convert.ToInt32(rowView["CELL_ID"]));
                    }
                }
            }
            return dicStack;
        }
        /// <summary>
        /// 连续托盘出库任务
        /// </summary>
        private bool CreateContinuouseStockOut( out string strResult)
        {
            bool boolResult = true;
            strResult = string.Empty;

            boolResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                        "ManageCreate",
                                                         new object[] {
                                                                       MainApp._USER,
                                                                       this.mMANAGE_TYPE.MANAGE_TYPE_CODE, 
                                                                       Convert.ToInt32(this.ucStockOut.U_StockType),
                                                                       string.Empty,
                                                                       Convert.ToDecimal(this.ucStockOut.U_StockOutQuantity),
                                                                       this.ucManagePosition.U_END_POSITION_ID,
                                                                       true,
                                                                       mMANAGE_TYPE_PARAM                                                                       
                                                                       },
                                                        out strResult);

            return boolResult;
        }



    }
}
