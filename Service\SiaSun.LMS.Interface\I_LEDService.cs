﻿using System;
using System.Collections.Generic;
using System.Text;
using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_LEDService
    {
        [OperationContract]
        bool INIT_LED_LIST(out string sResult);

        [OperationContract]
        SiaSun.LMS.Model.LED_MAIN LED_MAIN_GetModel(int LED_ID);

        [OperationContract]
        SiaSun.LMS.Model.LED_LIST LED_LIST_GetModel(int LED_LIST_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.LED_MAIN> LED_MAIN_GetList_AUTO_FLAG_LED_STATUS(string AUTO_FLAG, string LED_STATUS);
        
        [OperationContract]
        IList<SiaSun.LMS.Model.LED_LIST> LED_LIST_GetList_LED_ID(int LED_ID);

        [OperationContract]
        void LED_MAIN_Update(SiaSun.LMS.Model.LED_MAIN mLED_MAIN);

        /// <summary>
        /// 向固定LED发送的内容-发送内容用|分隔
        /// </summary>
        /// <param name="DeviceCode">设备编码</param>
        /// <param name="LineCount">内容总行号</param>
        /// <param name="SendTxt">发送内容，每行用|分隔</param>
        [OperationContract]
        bool AddLedTxt(string DeviceCode, int LineCount, string SendTxt, out string sResult);

        [OperationContract]
        bool AddLedLineTxt(string DeviceCode, int LineNum, string SendTxt, out string sResult);

        [OperationContract]
        bool ClearLedLineTxt(string DeviceCode, int LineNum, out string sResult);

        /// <summary>
        /// 向固定LED发送内容-发送内容用|分隔
        /// </summary>
        /// <param name="mLED_MAIN">屏幕Model</param>
        /// <param name="LineCount">内容总行号</param>
        /// <param name="SendTxt">发送内容，每行用|分隔</param>
        [OperationContract]
        bool AddLedDefaltTxt(SiaSun.LMS.Model.LED_MAIN mLED_MAIN, int LineCount, string SendTxt, out string sResult);
    }
}
