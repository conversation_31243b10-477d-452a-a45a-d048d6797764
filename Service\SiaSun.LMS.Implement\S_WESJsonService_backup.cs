﻿using System;
using System.Collections.Generic;
using System.Text;
using SiaSun.LMS.Interface;
using System.Data;
using SiaSun.LMS.Persistence;
using System.ServiceModel;
using System.Reflection;
using System.IO;
using System.Xml;
using System.Linq;
using IBatisNet.Common;
using log4net;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
     InstanceContextMode = InstanceContextMode.Single,
     ConcurrencyMode = ConcurrencyMode.Multiple,
     MaxItemsInObjectGraph = int.MaxValue)]
    public partial class S_WESJsonService : S_BaseService, I_WESJsonService
    {
        public S_WESJsonService()
        {
        }

        /// <summary>
        /// 非立库输送任务接口
        /// </summary>
        /// <param name="jsonstring">Json任务信息</param>
        /// <returns></returns>
        public string NoneMiniloadTrans(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.NoneMiniloadTrans[] taskinfo = Common.JsonHelper.Deserialize<WESJson.NoneMiniloadTrans[]>(jsonstring);

            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string palletNo = taskinfo[i].palletNo;               //托盘号
                string fromPosition = taskinfo[i].fromPosition;       //起点位置
                string toPosition = taskinfo[i].toPosition;           //终点位置
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源

                //数据转换

            }

            return "1";

        }

        /// <summary>
        /// 组箱入立库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string CreateBoxIn(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.CreateBoxInMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.CreateBoxInMain[]>(jsonstring);

            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string boxNo = taskinfo[i].boxNo;                     //箱号
                string boxType = taskinfo[i].boxType;                 //箱子类型
                string fromPosition = taskinfo[i].fromPosition;       //起始位置
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源
                string boxColor = taskinfo[i].boxColor;               //箱子颜色
                //一级明细
                string gridNo = taskinfo[i].createBoxInList[0].gridNo;          //格子号
                string itemCode = taskinfo[i].createBoxInList[0].itemCode;        //物料号
                string quantity = taskinfo[i].createBoxInList[0].quantity;        //数量

                //数据转换


            }


            return "1";
        }

        /// <summary>
        /// 空箱出库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string EmptyBoxOut(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.EmptyBoxOut[] taskinfo = Common.JsonHelper.Deserialize<WESJson.EmptyBoxOut[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string quantity = taskinfo[i].quantity;                //数量
                string boxType = taskinfo[i].boxType;                  //类型
                string toPosition = taskinfo[i].toPosition;            //终点位置
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源

                //数据转换

            }
            return "1";
        }

        /// <summary>
        /// 空箱入库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string EmptyBoxIn(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.EmptyBoxIn[] taskinfo = Common.JsonHelper.Deserialize<WESJson.EmptyBoxIn[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string boxNo = taskinfo[i].boxNo;                     //箱号
                string boxType = taskinfo[i].boxType;                 //箱子类型
                string fromPosition = taskinfo[i].fromPosition;       //起始位置
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string boxColor = taskinfo[i].boxColor;               //箱子颜色
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源

                //数据转换
            }
            return "1";
        }
        
        /// <summary>
        /// 盘点任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string StockCheckTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.StockCheckTaskMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.StockCheckTaskMain[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string countCode = taskinfo[i].countCode;             //盘点计划号
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源
                //一级明细
                string itemCode = taskinfo[i].stockCheckTaskList[0].itemCode; //物料号

                //数据转换

            }
            return "1";
        }

        /// <summary>
        /// Miniload拣选任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string MiniloadSortTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.MiniloadSortTaskMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.MiniloadSortTaskMain[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string pickType = taskinfo[i].pickType;                       //拣选方式
                string uniqueCode = taskinfo[i].uniqueCode;                   //唯一码
                string interfaceType = taskinfo[i].interfaceType;             //接口类型
                string interfaceSource = taskinfo[i].interfaceSource;         //接口来源
                //一级明细
                string projectNo = taskinfo[i].miniloadSortTaskList[0].projectNo;     //项目号
                string wbsNo = taskinfo[i].miniloadSortTaskList[0].wbsNo;             //WBS号
                string taskNo = taskinfo[i].miniloadSortTaskList[0].taskNo;           //任务单号
                string description = taskinfo[i].miniloadSortTaskList[0].description; //WBS描述
                //二级明细
                string itemCode = taskinfo[i].miniloadSortTaskList[0].miniloadSortTaskDetail[0].itemCode;   //物料号
                string quantity = taskinfo[i].miniloadSortTaskList[0].miniloadSortTaskDetail[0].quantity;   //数量

                //数据转换

            }
            return "1";
        }

        /// <summary>
        /// 紧急配料出库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string EmergSortOutTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.EmergSortOutTaskMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.EmergSortOutTaskMain[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string taskNo = taskinfo[i].taskNo;                           //任务单号
                string uniqueCode = taskinfo[i].uniqueCode;                   //唯一码
                string interfaceType = taskinfo[i].interfaceType;             //接口类型
                string interfaceSource = taskinfo[i].interfaceSource;         //接口来源
                //一级明细
                string itemCode = taskinfo[i].emergSortOutTaskList[0].itemCode; //物料号
                string quantity = taskinfo[i].emergSortOutTaskList[0].quantity; //数量

                //数据转换

            }
            return "1";
        }

        /// <summary>
        /// 齐套箱出库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string KitBoxOutTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.KitBoxOutTaskMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.KitBoxOutTaskMain[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string uniqueCode = taskinfo[i].uniqueCode;                   //唯一码
                string interfaceType = taskinfo[i].interfaceType;             //接口类型
                string interfaceSource = taskinfo[i].interfaceSource;         //接口来源
                string toLocation = taskinfo[i].toLocation;                   //终点位置
                //一级明细
                string projectNo = taskinfo[i].kitBoxOutTaskList[0].projectNo;     //项目号
                string wbsNo = taskinfo[i].kitBoxOutTaskList[0].wbsNo;             //WBS号
                string taskNo = taskinfo[i].kitBoxOutTaskList[0].taskNo;           //任务单号

                //数据转换


            }
            return "1";
        }

        /// <summary>
        /// 整理未满箱任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string ArrangeEmptyBoxTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.ArrangeEmptyBoxTask[] taskinfo = Common.JsonHelper.Deserialize<WESJson.ArrangeEmptyBoxTask[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string boxType = taskinfo[i].boxType;               //箱型
                string emptyQuantity = taskinfo[i].emptyQuantity;   //空格数
                string boxQuantity = taskinfo[i].boxQuantity;       //箱数
                string uniqueCode = taskinfo[i].uniqueCode;                   //唯一码
                string interfaceType = taskinfo[i].interfaceType;             //接口类型
                string interfaceSource = taskinfo[i].interfaceSource;         //接口来源

                //数据转换

            }
            return "1";
        }

        /// <summary>
        /// 余箱回库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string BoxReturnInTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.BoxReturnInTaskMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.BoxReturnInTaskMain[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string boxNo = taskinfo[i].boxNo;                     //箱号
                string boxType = taskinfo[i].boxType;                 //箱子类型
                string fromPosition = taskinfo[i].fromPosition;       //起始位置
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源
                string boxColor = taskinfo[i].boxColor;               //箱子颜色
                //一级明细
                string gridNo = taskinfo[i].boxReturnInTaskList[0].gridNo;     //格子号
                string itemCode = taskinfo[i].boxReturnInTaskList[0].itemCode; //物料号
                string qunatity = taskinfo[i].boxReturnInTaskList[0].quantity; //数量
                
                //数据转换


            }
            return "1";
        }

        /// <summary>
        /// 齐套箱出库时余料回库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string KitBoxReturnInTask(string jsonstring)
        {
            //解析Json字符串中的信息，并将数据进行处理
            WESJson.KitBoxReturnInTaskMain[] taskinfo = Common.JsonHelper.Deserialize<WESJson.KitBoxReturnInTaskMain[]>(jsonstring);
            for (int i = 0; i < taskinfo.Length; i++)
            {
                //根据所有的任务信息进行处理
                //按照指定的数据mode将数据转化为该数据mode
                string interfaceType = taskinfo[i].interfaceType;     //接口类型
                string interfaceSource = taskinfo[i].interfaceSource; //接口来源
                string boxNo = taskinfo[i].boxNo;                     //箱号
                string fromPosition = taskinfo[i].fromPosition;       //起始位置
                string uniqueCode = taskinfo[i].uniqueCode;           //唯一码
                //一级明细
                string projectNo = taskinfo[i].kitBoxReturnInTaskList[0].projectNo;     //项目号
                string wbsNo = taskinfo[i].kitBoxReturnInTaskList[0].wbsNo;             //WBS号
                string taskNo = taskinfo[i].kitBoxReturnInTaskList[0].taskNo;           //任务单号
                //二级明细
                string itemCode = taskinfo[i].kitBoxReturnInTaskList[0].kitBoxReturnInTaskDetail[0].itemCode;   //物料号
                string quantity = taskinfo[i].kitBoxReturnInTaskList[0].kitBoxReturnInTaskDetail[0].quantity;   //数量

            }
            return "1";
        }

        /// <summary>
        /// 物料主数据同步任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string MaterialSyn(string jsonstring)
        {
            return "1";
        }

    }
}
