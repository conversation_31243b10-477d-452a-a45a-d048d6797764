﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Data;
using System.Windows.Controls;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// HANDLE_EXCEPTION.xaml 的交互逻辑
    /// </summary>
    public partial class KIT_MODE :  AvalonDock.DocumentContent
    {
        private string _opriation;
        public KIT_MODE(string opriation)
        {
            this._opriation = opriation;

            InitializeComponent();
        }
        
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            LoadData();

           
        }

        /// <summary>
        /// 初始化加载数据
        /// </summary>
        public void LoadData()
        {
            switch (this._opriation)
            {
                case "KitOutWorkMode":
                    DataTable dtWorkModeKitOut1 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-KitOut1'");
                    if (dtWorkModeKitOut1 != null && dtWorkModeKitOut1.Rows.Count > 0)
                    {
                        if(dtWorkModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Manual.ToString())
                        {
                            this.rbtnManual1.IsChecked = true;
                        }
                        else if(dtWorkModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Auto.ToString())
                        {
                            this.rbtnAuto1.IsChecked = true;
                        }
                    }
                    DataTable dtPlanModeKitOut1 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'PlanMode-KitOut1'");
                    if (dtPlanModeKitOut1 != null && dtPlanModeKitOut1.Rows.Count > 0)
                    {
                        if (dtPlanModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.Mix.ToString())
                        {
                            this.rbtnMix1.IsChecked = true;
                        }
                        else if (dtPlanModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.One.ToString())
                        {
                            this.rbtnOne1.IsChecked = true;
                        }
                    }
                    DataTable dtWorkModeKitOut2 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-KitOut2'");
                    if (dtWorkModeKitOut2 != null && dtWorkModeKitOut2.Rows.Count > 0)
                    {
                        if (dtWorkModeKitOut2.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Manual.ToString())
                        {
                            this.rbtnManual2.IsChecked = true;
                        }
                        else if (dtWorkModeKitOut2.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Auto.ToString())
                        {
                            this.rbtnAuto2.IsChecked = true;
                        }
                    }
                    DataTable dtWorkModeKitOut3 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-KitOut3'");
                    if (dtWorkModeKitOut3 != null && dtWorkModeKitOut3.Rows.Count > 0)
                    {
                        if (dtWorkModeKitOut3.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Manual.ToString())
                        {
                            this.rbtnManual3.IsChecked = true;
                        }
                        else if (dtWorkModeKitOut3.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Auto.ToString())
                        {
                            this.rbtnAuto3.IsChecked = true;
                        }
                    }
                    DataTable dtPlanModeKitOut3 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'PlanMode-KitOut3'");
                    if (dtPlanModeKitOut3 != null && dtPlanModeKitOut3.Rows.Count > 0)
                    {
                        if (dtPlanModeKitOut3.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.Mix.ToString())
                        {
                            this.rbtnMix3.IsChecked = true;
                        }
                        else if (dtPlanModeKitOut3.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.One.ToString())
                        {
                            this.rbtnOne3.IsChecked = true;
                        }
                    }
                    DataTable dtWorkModeKitOut4 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-KitOut4'");
                    if (dtWorkModeKitOut4 != null && dtWorkModeKitOut4.Rows.Count > 0)
                    {
                        if (dtWorkModeKitOut4.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Manual.ToString())
                        {
                            this.rbtnManual4.IsChecked = true;
                        }
                        else if (dtWorkModeKitOut4.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Auto.ToString())
                        {
                            this.rbtnAuto4.IsChecked = true;
                        }
                    }
                    DataTable dtWorkModeKitOut5 = MainApp._I_BaseService.GetList("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-KitOut5'");
                    if (dtWorkModeKitOut5 != null && dtWorkModeKitOut5.Rows.Count > 0)
                    {
                        if (dtWorkModeKitOut5.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Manual.ToString())
                        {
                            this.rbtnManual5.IsChecked = true;
                        }
                        else if (dtWorkModeKitOut5.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Auto.ToString())
                        {
                            this.rbtnAuto5.IsChecked = true;
                        }
                    }
                    break;
                default:
                    break;
            }
        }
        
        /// <summary>
        /// 重置计划
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void btnReset_Click(object sender, RoutedEventArgs e)
        {

            LoadData();
        }




        /// <summary>
        /// 合箱线工作模式
        /// </summary>
        /// <param name="sender"></param>
        /// <param name="e"></param>
        private void BtnWorkMode_Click(object sender, RoutedEventArgs e)
        {
            bool result = true;
            string message = string.Empty;

            try
            {
                if (this.rbtnAuto1.IsChecked == true && this.rbtnManual1.IsChecked == false)//自动模式
                {
                    //获取当前模式
                    DataTable dtCurrentModeKit1 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Auto' and PARAMETER_KEY = 'WorkingMode-KitOut1'");
                    if (dtCurrentModeKit1 != null && dtCurrentModeKit1.Rows.Count > 0)
                    {
                        //校验任务
                        //DataTable dtManageTo710313 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='710313'");
                        //if (dtManageTo710313 != null && dtManageTo710313.Rows.Count > 0)
                        //{
                        //    result = false;
                        //    message = "存在终点位置为710313的任务_无法切换模式";
                        //    return;
                        //}
                       
                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Auto' where PARAMETER_KEY = 'WorkingMode-KitOut1'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新一楼齐套出库工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "一楼齐套出库模式更新为[自动模式]");
                        }
                    }
                }
                else if (this.rbtnAuto1.IsChecked == false && this.rbtnManual1.IsChecked == true)
                {
                    //获取当前模式
                    DataTable dtCurrentModeKit1 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Manual' and PARAMETER_KEY = 'WorkingMode-KitOut1'");
                    if (dtCurrentModeKit1 != null && dtCurrentModeKit1.Rows.Count > 0)
                    {
                        //校验任务和库存
                        //DataTable dtManageTo710313 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION ='710313'");
                        //if (dtManageTo710313 != null && dtManageTo710313.Rows.Count > 0)
                        //{
                        //    result = false;
                        //    message = "存在终点位置为710313的任务_无法切换模式";
                        //    return;
                        //}
                       
                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Manual' where PARAMETER_KEY = 'WorkingMode-KitOut1'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新一楼齐套出库工作模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "一楼齐套出库模式更新为[人工模式]");
                        }
                    }
                }                
                else
                {
                    result = false;
                    message = "一楼齐套出库工作模式选择有误";
                    return;
                }

                if (this.rbtnOne1.IsChecked == true && this.rbtnMix1.IsChecked == false)
                {
                    //获取当前模式
                    DataTable dtCurrentModeKit1 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='One' and PARAMETER_KEY = 'PlanMode-KitOut1'");
                    if (dtCurrentModeKit1 != null && dtCurrentModeKit1.Rows.Count > 0)
                    {
                        //校验任务
                        DataTable dtManageTo710313 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION IN ('710313')");
                        if (dtManageTo710313 != null && dtManageTo710313.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为【710313】的任务_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='One' where PARAMETER_KEY = 'PlanMode-KitOut1'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新一楼齐套出库单据模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "一楼齐套出库单据模式更新为[混单模式]");
                        }
                    }
                }
                else if (this.rbtnOne1.IsChecked == false && this.rbtnMix1.IsChecked == true)
                {
                    //获取当前模式
                    DataTable dtCurrentModeKit1 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Mix' and PARAMETER_KEY = 'PlanMode-KitOut1'");
                    if (dtCurrentModeKit1 != null && dtCurrentModeKit1.Rows.Count > 0)
                    {
                        //校验任务
                        DataTable dtManageTo710313 = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION IN ('710313')");
                        if (dtManageTo710313 != null && dtManageTo710313.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为【710313】的任务_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Mix' where PARAMETER_KEY = 'PlanMode-KitOut1'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新一楼齐套出库单据模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "一楼齐套出库单据模式更新为[混单模式]");
                        }
                    }
                }
                else
                {
                    result = false;
                    message = "齐套出库单据模式选择有误";
                    return;
                }


                if (this.rbtnOne3.IsChecked == true && this.rbtnMix3.IsChecked == false)
                {
                    //获取当前模式
                    DataTable dtCurrentModeKit3 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='One' and PARAMETER_KEY = 'PlanMode-KitOut3'");
                    if (dtCurrentModeKit3 != null && dtCurrentModeKit3.Rows.Count > 0)
                    {
                        //校验任务
                        DataTable dtManageTo3f = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION IN ('81056','81070','81084','81098')");
                        if (dtManageTo3f != null && dtManageTo3f.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为【81056/81070/81084/81098】的任务_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='One' where PARAMETER_KEY = 'PlanMode-KitOut3'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新三楼齐套出库单据模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "三楼齐套出库单据模式更新为[混单模式]");
                        }
                    }
                }
                else if (this.rbtnOne3.IsChecked == false && this.rbtnMix3.IsChecked == true)
                {
                    //获取当前模式
                    DataTable dtCurrentModeKit1 = MainApp._I_BaseService.GetList("select * from SYS_PARAMETER where PARAMETER_VALUE!='Mix' and PARAMETER_KEY = 'PlanMode-KitOut3'");
                    if (dtCurrentModeKit1 != null && dtCurrentModeKit1.Rows.Count > 0)
                    {
                        //校验任务
                        DataTable dtManageTo3f = MainApp._I_BaseService.GetList("select * from V_MANAGE where END_POSITION IN ('81056','81070','81084','81098')");
                        if (dtManageTo3f != null && dtManageTo3f.Rows.Count > 0)
                        {
                            result = false;
                            message = "存在终点位置为【81056/81070/81084/81098】的任务_无法切换模式";
                            return;
                        }

                        int reffcount = MainApp._I_BaseService.ExecuteNonQuery_ReturnInt("update SYS_PARAMETER set PARAMETER_VALUE='Mix' where PARAMETER_KEY = 'PlanMode-KitOut3'");
                        if (reffcount < 1)
                        {
                            result = false;
                            message = "更新三楼齐套出库单据模式未成功";
                            return;
                        }
                        else
                        {
                            MainApp._I_BaseService.CreateSysLog(Enum.LogThread.System, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, "三楼齐套出库单据模式更新为[混单模式]");
                        }
                    }
                }
                else
                {
                    result = false;
                    message = "三楼齐套出库单据模式选择有误";
                    return;
                }

            }
            catch (Exception ex)
            {
                result = false;
                message = string.Format("更新工作模式时异常_信息[{0}]", ex.Message);
            }
            finally
            {
                MainApp._MessageDialog.ShowResult(result, message);
                LoadData();
            }
        }

    }
}
