﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucDataGridRowGroupControl.xaml 的交互逻辑
    /// </summary>
    public partial class ucDataGridRowGroupControl : UserControl
    {
        #region     ------基本变量与属性

        string strTableName = string.Empty;
        string strWindowName = string.Empty;
        string strWhere = string.Empty;
        List<Model.FIELD_DESCRIPTION> listDescription = new List<Model.FIELD_DESCRIPTION>();
        List<DataRowView> listDataRowView = new List<DataRowView>();
        int intCount = 0;

        /// <summary>
        /// 关联窗体名
        /// </summary>
        public string U_WindowName
        {
            get { return strWindowName; }
            set { strWindowName = value; }
        }

        /// <summary>
        /// 表名
        /// </summary>
        public string U_TableName
        {
            get { return strTableName; }
            set { strTableName = value; }
        }

        /// <summary>
        /// 筛选条件
        /// </summary>
        public string U_Where
        {
            get { return strWhere; }
            set { strWhere = value; }
        }

        /// <summary>
        /// 描述列表
        /// </summary>
        public List<Model.FIELD_DESCRIPTION> U_FieldDescriptionList
        {
            get { return listDescription; }
            set { listDescription = value; }
        }

        /// <summary>
        /// 选中行数
        /// </summary>
        public int U_CheckedCount
        {
            get { return this.intCount; }
            set { this.intCount = value; }
        }

        #endregion
        
        /// <summary>
        /// 构造函数
        /// </summary>
        public ucDataGridRowGroupControl()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public void U_InitControl()
        {
            //初始化
            listDataRowView.Clear();
            //数据绑定
            this.StorageGroupBind();
        }

        /// <summary>
        /// 绑定库存信息
        /// </summary>
        private void StorageGroupBind()
        {
            if (this.listDescription.Count > 0)
            {
                try
                {
                    //清空
                    this.gridStorageGroup.Columns.Clear();
                    //样式
                    this.gridStorageGroup.U_TranslateDataGridViewStyle(strTableName, this.listDescription, false);

                    //获得查询字符串
                    strWhere = string.IsNullOrEmpty(strWhere) ? "1=1" : strWhere;
                    string strSql = string.Format("SELECT  {0} FROM {1} WHERE {2} and STOCK_BARCODE <> 'T10009' GROUP BY {0}", this.GetGroupColumns(), strTableName, strWhere, this.listDescription[0].Column);
                    //数据源
                    
                    this.gridStorageGroup.ItemsSource = MainApp._I_BaseService.GetList(strSql).DefaultView;

                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
            }
        }

        #region     ------组合字符串

        /// <summary>
        /// 获得组合查询字段集合
        /// </summary>
        private string GetGroupColumns()
        {
            string strColumns = null;
            foreach (Model.FIELD_DESCRIPTION mFIELD_DESCRIPTION in this.listDescription)
            {
                strColumns += string.IsNullOrEmpty(strColumns) ? mFIELD_DESCRIPTION.Column : string.Format(",{0}", mFIELD_DESCRIPTION.Column);
            }
            return strColumns;
        }

        
        #endregion

        #region     ------伸缩面板事件

        /// <summary>
        /// 加载伸缩面板时加载控件
        /// </summary>
        private void Expander_Loaded(object sender, RoutedEventArgs e)
        {
            Expander pder = e.OriginalSource as Expander;
            if (pder != null)
            {
                DataRowView rowView = pder.DataContext as DataRowView;
                if (rowView != null)
                {
                    //获得托盘和货位的值
                    WrapPanel panelHeader = pder.Header as WrapPanel;
                    if (panelHeader != null)
                    {
                        foreach (DependencyObject item in panelHeader.Children)
                        {
                            if (item is CheckBox)
                            {
                                CheckBox chk = item as CheckBox;
                                if (this.listDataRowView.Count < this.intCount)
                                {
                                    chk.IsChecked = true;
                                }
                            }
                            else if (item is ucModelValuePanel)
                            {
                                ucModelValuePanel panelValue = item as ucModelValuePanel;
                                panelValue.U_InitControl(this.gridStorageGroup, rowView.Row);
                            }
                        }
                    }
                }
            }
        }

        /// <summary>
        /// 展开时，加载明细数据
        /// </summary>
        private void Expander_Expanded(object sender, RoutedEventArgs e)
        {
            Expander pder = e.OriginalSource as Expander;
            if (pder != null)
            {
                string strAppendWhere = null;
                //获得托盘和货位的值
                WrapPanel panelHeader = pder.Header as WrapPanel;
                if (panelHeader != null)
                {
                    foreach (DependencyObject item in panelHeader.Children)
                    {
                        if (item is ucModelValuePanel)
                        {
                            ucModelValuePanel ucValuePanel = item as ucModelValuePanel;
                            strAppendWhere= ucValuePanel.U_GetWhere();
                            break;
                        }
                    }

                    ucSplitPropertyGridTab splitGrid = pder.Content as ucSplitPropertyGridTab;
                    if (splitGrid != null)
                    {
                        this.StorageListBind(splitGrid, strAppendWhere);
                    }
                }
            }
        }

        /// <summary>
        /// 加载库存明细数据
        /// </summary>
        private void StorageListBind(ucSplitPropertyGridTab splitGrid, string AppentWhere)
        {
            splitGrid.U_WindowName = this.strWindowName;
            splitGrid.U_XmlTableName = "V_STORAGE_LIST";
            splitGrid.U_TableName = "V_STORAGE_LIST";
            splitGrid.U_OrderField = "STORAGE_LIST_ID";
            splitGrid.U_Where = AppentWhere;

            splitGrid.U_AllowOperatData = false;
            splitGrid.U_AllowChecked = false;
            splitGrid.U_AllowShowPage = false;

            splitGrid.U_SplitGroupColumn = "GOODS_TYPE_ID";
            splitGrid.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            //splitGrid.U_SplitPropertyColumn = "GOODS_PROPERTY";
            splitGrid.U_SplitPropertyType = "GOODS_TYPE";

            splitGrid.U_InitControl();
        }        
        #endregion

        #region     ------选中行
        /// <summary>
        /// 获得选定的行记录
        /// </summary>
        public List<DataRowView> U_GetCheckedRow()
        {
            return this.listDataRowView;
        }

        /// <summary>
        /// 勾选中行记录
        /// </summary>
        private void chkBoxSelect_Checked(object sender, RoutedEventArgs e)
        {
            CheckBox chk = e.OriginalSource as CheckBox;
            if (chk != null)
            {
                WrapPanel panel = chk.Parent as WrapPanel;
                if (panel != null)
                {
                    Expander pder = panel.Parent as Expander;
                    if (pder != null)
                    {
                        DataRowView rowView = pder.DataContext as DataRowView;
                        if (rowView != null)
                        {
                            if (chk.IsChecked == true)
                            {
                                if (!this.listDataRowView.Contains(rowView))
                                {
                                    this.listDataRowView.Add(rowView);
                                }
                            }
                            else
                            {
                                if (this.listDataRowView.Contains(rowView))
                                {
                                    this.listDataRowView.Remove(rowView);
                                }
                            }
                            //选中数量
                            this.txtChecked.Text = this.listDataRowView.Count.ToString();
                        }
                    }
                }
            }
        }
        
        #endregion

        /// <summary>
        /// 工具栏按钮
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnRefresh":
                        this.intCount = 0;
                        this.U_InitControl();
                        break;
                }
            }
        }
    }
}
