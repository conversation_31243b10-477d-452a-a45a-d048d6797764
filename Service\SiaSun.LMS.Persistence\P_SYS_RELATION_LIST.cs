﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_RELATION_LIST
	/// </summary>
	public class P_SYS_RELATION_LIST : P_Base_House
	{
		public P_SYS_RELATION_LIST ()
		{
			//
			// TODO: 此处添加SYS_RELATION_LIST的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_RELATION_LIST> GetList()
		{
			return ExecuteQueryForList<SYS_RELATION_LIST>("SYS_RELATION_LIST_SELECT",null);
		}


        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<SYS_RELATION_LIST> GetListRelationIDRelaionID1(int RELATION_ID, int RELATION_ID1)
        {
            Hashtable hsParam = new Hashtable();
            hsParam.Add("RELATION_ID", RELATION_ID);
            hsParam.Add("RELATION_ID1", RELATION_ID1);
            return ExecuteQueryForList<SYS_RELATION_LIST>("SYS_RELATION_LIST_SELECT_BY_RELATION_ID1", hsParam);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_RELATION_LIST sys_relation_list)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("SYS_RELATION_LIST");
                sys_relation_list.RELATION_LIST_ID = id;
            }

            return ExecuteInsert("SYS_RELATION_LIST_INSERT",sys_relation_list);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_RELATION_LIST sys_relation_list)
		{
			return ExecuteUpdate("SYS_RELATION_LIST_UPDATE",sys_relation_list);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_RELATION_LIST GetModel(System.Int32 RELATION_LIST_ID)
		{
			return ExecuteQueryForObject<SYS_RELATION_LIST>("SYS_RELATION_LIST_SELECT_BY_ID",RELATION_LIST_ID);
		}


        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public SYS_RELATION_LIST GetModelRelationIDRelationID1RelationID2(System.Int32 RELATION_ID, int RELATION_ID1, int RELATION_ID2)
        {
            Hashtable hsParam = new Hashtable();
            hsParam.Add("RELATION_ID", RELATION_ID);
            hsParam.Add("RELATION_ID1", RELATION_ID1);
            hsParam.Add("RELATION_ID2", RELATION_ID2);

            return ExecuteQueryForObject<SYS_RELATION_LIST>("SYS_RELATION_LIST_SELECT_BY_RELATION_ID_RELATION_ID1_RELATION_ID2", hsParam);
        }


		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 RELATION_LIST_ID)
		{
			return ExecuteDelete("SYS_RELATION_LIST_DELETE",RELATION_LIST_ID);
		}
		

	}
}
