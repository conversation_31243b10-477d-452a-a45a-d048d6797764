﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.PICK.View
{
    /// <summary>
    /// GoodsImageCheckWindowView.xaml 的交互逻辑
    /// </summary>
    public partial class GoodsImageCheckWindowView : Window
    {
        public string GoodsCode { get; set; }
        public string GoodsName { get; set; }
        public int ManageId { get;set; }
        public bool isCheckSuccess { get; set; }
        public GoodsImageCheckWindowView(string goodsCode,string goodsName,int manageId)
        {
            InitializeComponent();
            this.isCheckSuccess = false;
            this.GoodsCode = goodsCode;
            this.ManageId = manageId;


            this.GoodsName = goodsName;

            Bind();
        }

        private void Bind()
        {
            this.tbGoodsCode.Text = this.GoodsCode;
            this.tbGoodsName.Text = this.GoodsName;
            this.tBoxCheckCode.Text = string.Empty;
            
            BitmapImage img = new BitmapImage();
            img.BeginInit();
            img.UriSource = new Uri(string.Format("http://localhost:6001/images/{0}.png",this.GoodsCode));
            img.EndInit();
            this.imageGoods.Source = img;
            this.tBoxCheckCode.Focus();
        }

        private void btnClear_Click(object sender, RoutedEventArgs e)
        {
            this.tBoxCheckCode.Clear();
            this.tbGoodsCode.Focus();
        }

        private void btnComfirm_Click(object sender, RoutedEventArgs e)
        {
            if (this.tbGoodsCode.Text.Equals(this.tBoxCheckCode.Text))
            {
                this.isCheckSuccess = true;
                this.Close();
            }
            else
            {
                this.isCheckSuccess = false;
                this.tBoxCheckCode.Clear();
                this.tbGoodsCode.Focus();
            }
        }

        private void tBoxCheckCode_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.Key == Key.Enter)
            {
                btnComfirm_Click(sender, e); // 直接调用按钮的点击事件方法
                e.Handled = true; // 阻止默认行为（如换行）
            }
        }
    }
}
