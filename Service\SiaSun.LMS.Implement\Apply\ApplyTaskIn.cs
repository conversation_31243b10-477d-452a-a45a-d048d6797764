﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement
{
    public class ApplyTaskIn:ApplyBase
    {
        public bool ApplyHandle(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            Model.MANAGE_MAIN mMANAGE_MAIN = null;

            try            
            {
                this._S_SystemService.CreateSYS_LOG("设备申请", string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), "1", string.Format("开始处理申请 条码{0}", mIO_CONTROL_APPLY.STOCK_BARCODE));

                //初始化任务申请类型的参数
                //申请类型的参数通过表APPLY_TYPE和APPLY_TYPE_PARAM进行配置
                bResult = this.InitApplyParam(mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                //校验调度写入表IO_CONTROL_APPLY的数据
                bResult = this.Validate(mIO_CONTROL_APPLY, out sResult);
                if (!bResult)
                {
                    return bResult;
                }

                ////xcjt comment 2017-04-11
                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode("%" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "%");
                //if (mMANAGE_MAIN == null)
                //{
                //    bResult = false;
                //    sResult = string.Format("托盘条码{0}未下达任务 请查看当前任务中是否有此条码的记录", mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd());
                //    return bResult;
                //}

                if (mMANAGE_MAIN != null)
                {
                    //校验入库选择的高低货位与申请是否统一
                    if (mMANAGE_MAIN.CELL_MODEL == null || mMANAGE_MAIN.CELL_MODEL != mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER)
                    {
                        bResult = false;
                        sResult = string.Format("高低货位校验失败");
                        return bResult;
                    }

                    ////对于有终点的管理任务不处理
                    //if (mMANAGE_MAIN.END_CELL_ID == 0)
                    //{
                        mMANAGE_MAIN.CELL_MODEL = mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER.TrimEnd();

                        this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);

                        Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)this.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", mMANAGE_MAIN.MANAGE_TYPE_CODE).RequestObject;

                        bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), "ManageDownLoad", new object[] { mMANAGE_MAIN.MANAGE_ID, mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd(), true }, out sResult);
                    //}
                }
                else
                {
                    if (this._S_StorageService.StorageCheck(mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd(), Enum.AREA_TYPE.XuNiKu.ToString(), out sResult))
                    {
                        mMANAGE_MAIN = new Model.MANAGE_MAIN();

                        mMANAGE_MAIN.MANAGE_TYPE_CODE = "ManageUp";
                        mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd();
                        mMANAGE_MAIN.CELL_MODEL = mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER.TrimEnd();
                        mMANAGE_MAIN.START_CELL_ID = mSTART_CELL.CELL_ID;
                        mMANAGE_MAIN.END_CELL_ID = 0;
                        mMANAGE_MAIN.MANAGE_OPERATOR = "扫码申请";
                        mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                        mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                        mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;
                        mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                        bResult = new ManageUp().ManageCreate(mMANAGE_MAIN, true, true, false, out sResult);
                    } //根据托盘条码 查找暂存区库存 根据库存生成上架任务
                    else
                    {
                        ////系统运行初期，托盘集中上架时使用。取消下面语句注释。
                        //bResult = this.EmptyPalletLoading(mIO_CONTROL_APPLY, mSTART_CELL, out sResult);

                        bResult = false;
                        sResult = string.Format("申请托盘条码{0}不存在任务或者暂存区库存", mIO_CONTROL_APPLY.STOCK_BARCODE);
                    }
                }
            }
            catch(Exception ex)
            {
                bResult = false;
                sResult = string.Format("处理申请时发生异常 {0}_ApplyTaskIn", ex.Message);
            }
            finally
            {
                if (this.applyTypeParam.U_SendLedMessage)
                {
                    this.SendLEDMessage(sResult, out sResult);
                }
                if (this.applyTypeParam.U_CreateExceptionTask && !bResult)
                {
                    //tzyg comment 2017-03-17
                    //测试时注释，如果需要回退则取消注释即可

                    //配置了生成退回任务并且处理结果为FALSE时才生成退回控制任务
                    //this.CreateApplyExceptionTask(mIO_CONTROL_APPLY, this.applyTypeParam.U_ExceptionStation);
                }
                if (this.applyTypeParam.U_WriteHisData)
                {
                    this.WriteHisData(mIO_CONTROL_APPLY, sResult);
                }

                this._S_SystemService.CreateSYS_LOG("设备申请", string.Format("Apply_{0}", mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE), "1", string.Format("结束处理申请 条码{0}，处理{1} {2}", mIO_CONTROL_APPLY.STOCK_BARCODE, bResult ? "成功" : "失败", sResult));
            }

            return bResult;
        }



        /// <summary>
        /// 系统运行初期，托盘集中上架时使用。
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        bool EmptyPalletLoading(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY,Model.WH_CELL mWH_CELL, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();
            mMANAGE_MAIN.MANAGE_TYPE_CODE = "StockIn";
            mMANAGE_MAIN.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd();
            mMANAGE_MAIN.CELL_MODEL = mIO_CONTROL_APPLY.CONTROL_APPLY_REMARK.TrimEnd();
            mMANAGE_MAIN.START_CELL_ID = mSTART_CELL.CELL_ID;
            mMANAGE_MAIN.END_CELL_ID = 0;
            mMANAGE_MAIN.MANAGE_OPERATOR = "扫码申请";
            mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
            mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
            mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;
            mMANAGE_MAIN.MANAGE_REMARK = string.Empty;
            List<Model.MANAGE_LIST> listMANAGE_LIST = new List<Model.MANAGE_LIST>();
            Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
            if (Convert.ToInt32(mMANAGE_MAIN.STOCK_BARCODE) < 2200)
                mMANAGE_LIST.GOODS_ID = 1;
            else
                mMANAGE_LIST.GOODS_ID = -1;
            mMANAGE_LIST.MANAGE_LIST_QUANTITY = 1;

            listMANAGE_LIST.Add(mMANAGE_LIST);

            //bResult = new ManageIn().ManageCreate(mMANAGE_MAIN, listMANAGE_LIST, true, true, false, true, out sResult);

            return bResult;
        }



    }
}
