﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// FLOW_PARA
	/// </summary>
	public class P_FLOW_PARA : P_Base_House
	{
		public P_FLOW_PARA ()
		{
			//
			// TODO: 此处添加FLOW_PARA的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<FLOW_PARA> GetList()
		{
			return ExecuteQueryForList<FLOW_PARA>("FLOW_PARA_SELECT",null);
		}

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<FLOW_PARA> GetListFlowTypeCode(string FLOW_TYPE_CODE)
        {
            return this.ExecuteQueryForList<FLOW_PARA>("FLOW_PARA_SELECT_BY_FLOW_TYPE_CODE", FLOW_TYPE_CODE);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public int Add(FLOW_PARA flow_para)
        {
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("FLOW_PARA");
                flow_para.FLOW_PARA_ID = id;
            }
            return ExecuteInsert("FLOW_PARA_INSERT", flow_para);
        }

		/// <summary>
		/// 修改
		/// </summary>
		public int Update(FLOW_PARA flow_para)
		{
			return ExecuteUpdate("FLOW_PARA_UPDATE",flow_para);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public FLOW_PARA GetModel(System.Int32 FLOW_PARA_ID)
		{
			return ExecuteQueryForObject<FLOW_PARA>("FLOW_PARA_SELECT_BY_ID",FLOW_PARA_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 FLOW_PARA_ID)
		{
			return ExecuteDelete("FLOW_PARA_DELETE",FLOW_PARA_ID);
		}
		

	}
}
