﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.Text;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;
using System.Reflection;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
     InstanceContextMode = InstanceContextMode.Single,
     ConcurrencyMode = ConcurrencyMode.Multiple,
     MaxItemsInObjectGraph = int.MaxValue)]
    public partial class S_StorageService : S_BaseService, SiaSun.LMS.Interface.I_StorageService
    {
        private static object lockobj = new object();
        private WDZ.Message.SignalServerSrvClient signalServerClient = new WDZ.Message.SignalServerSrvClient();

        public S_StorageService()
        {
        }
        /// <summary>
        /// 校验托盘是否是指定库存类型
        /// </summary>
        /// <param name="STOCK_BARCODE"></param>
        /// <param name="AREA_TYPE"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool StorageCheck(string STOCK_BARCODE,
                                 string AREA_TYPE,
                                 out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            DataTable dt = this.GetList(string.Format("SELECT * FROM V_STORAGE_LIST WHERE STOCK_BARCODE='{0}' AND AREA_TYPE = '{1}' ", STOCK_BARCODE, AREA_TYPE.ToString()));

            if (dt.Rows.Count == 0)
            {
                bResult = false;
            }

            return bResult;
        }

        /// <summary>
        /// 校验物料是否是存在库存
        /// </summary>
        public bool StorageCheckGoods(int GOODS_ID, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            DataTable dt = this.GetList(string.Format("SELECT * FROM V_STORAGE_LIST WHERE GOODS_ID={0} ", GOODS_ID));

            if (dt.Rows.Count == 0)
            {
                bResult = false;
            }

            return bResult;
        }

        /// <summary>
        /// 设置库存是否异常标志
        /// wdz add 2018-04-18
        /// </summary>
        /// <param name="stockBarcode"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool SetStorageException(Model.SYS_USER user, string stockBarcode, bool isException, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("设置库存异常状态失败_未找到库存信息_箱条码[{0}]", stockBarcode);
                    this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetStorageException():设置库存异常状态失败_未找到库存信息_箱条码[{0}]", stockBarcode));
                    return bResult;
                }

                mSTORAGE_MAIN.IS_EXCEPTION = isException ? "1" : "0";
                this._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("S_StorageService.SetStorageException():设置库存异常状态成功_箱条码[{0}]_设置值[{1}]", stockBarcode, isException.ToString()));
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("设置库存异常状态时程序发生异常_异常信息[{0}]", ex.Message);
                this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetStorageException():设置库存异常状态时程序发生异常_异常信息[{0}]_条码[{1}]_设置值[{2}]", ex.Message, stockBarcode, isException.ToString()));
                //throw;
            }

            return bResult;
        }


        /// <summary>
        /// 设置物料优先出库
        /// wdz add 2018-05-01
        /// </summary>
        /// <param name="user"></param>
        /// <param name="stockBarcode"></param>
        /// <param name="goodsCode"></param>
        /// <param name="isPriorOut"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool SetGoodsPriorOut(Model.SYS_USER user, string stockBarcode, string goodsCode, bool isPriorOut, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(stockBarcode);
                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("设置物料优先出库失败_未找到库存信息_箱条码[{0}]", stockBarcode);
                    this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetGoodsPriorOut():设置物料优先出库失败_未找到库存信息_箱条码[{0}]", stockBarcode));
                    return bResult;
                }

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(goodsCode);
                if (mGOODS_MAIN == null)
                {
                    bResult = false;
                    sResult = string.Format("设置物料优先出库失败_未找到物料信息_物料编码[{0}]", goodsCode);
                    this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetGoodsPriorOut():设置物料优先出库失败_未找到物料信息_物料编码[{0}]", goodsCode));
                    return bResult;
                }

                IList<Model.STORAGE_LIST> lsSTORAGE_LIST = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID);
                Model.STORAGE_LIST priorOutStorageList = lsSTORAGE_LIST.First(r => r.GOODS_ID == mGOODS_MAIN.GOODS_ID);
                if (priorOutStorageList == null)
                {
                    bResult = false;
                    sResult = string.Format("设置物料优先出库失败_未找到物料对应的库存列表_物料编码[{0}]_库存ID[{1}]", goodsCode, mSTORAGE_MAIN.STORAGE_ID);
                    this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetGoodsPriorOut():设置物料优先出库失败_未找到物料对应的库存列表_箱条码[{0}]_物料编码[{1}]_库存ID[{2}]", stockBarcode, goodsCode, mSTORAGE_MAIN.STORAGE_ID));
                    return bResult;
                }

                priorOutStorageList.GOODS_PROPERTY1 = isPriorOut ? "1" : "0";
                this._P_STORAGE_LIST.Update(priorOutStorageList);

                this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetGoodsPriorOut():设置物料优先出库成功_箱条码[{0}]_物料编码[{1}]_是否优先[{2}]", stockBarcode, goodsCode, isPriorOut.ToString()));
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("设置物料优先出库时程序发生异常_异常信息[{0}]", ex.Message);
                this.CreateSysLog(Enum.LogThread.Storage, user.USER_NAME, false, string.Format("S_StorageService.SetGoodsPriorOut():设置物料优先出库时程序发生异常_异常信息[{0}]_箱条码[{1}]_物料编码[{2}]_是否优先[{3}]", ex.Message, stockBarcode, goodsCode, isPriorOut.ToString()));
                //throw;
            }

            return bResult;
        }


        #region 拣选工作站相关
        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站IP获得拣选工作站实例
        /// hejiaji 
        /// 20180101
        /// done
        /// </summary>
        /// <param name="ip">拣选工作站IP</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>拣选工作站实例</returns>
        public T_PICK_STATION Get_T_PICK_STATION_BY_IP(string ip, out string sResult)
        {
            sResult = string.Empty;
            try
            {
                return this._P_T_PICK_STATION.GetModel_BY_IP(ip);
            }
            catch (Exception ex)
            {
                sResult = ex.Message;
                return null;
            }

        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID获得拣选工作站下面的所有拣选工作点集合实例
        /// hejiaji 
        /// 20180101
        /// done
        /// <param name="STATION_ID">拣选工作站ID</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>拣选工作点实例集合</returns>
        public List<T_PICK_POSITION> Get_T_PICK_POSITION_BY_STATION_ID(int STATION_ID, out string sResult)
        {
            sResult = string.Empty;

            try
            {
                return this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(STATION_ID).ToList();
            }
            catch (Exception ex)
            {
                sResult = ex.Message;
                return null;
            }
        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID获得拣选工作站上正在执行拣选的料箱的库存
        /// hejiaji 
        /// 20180101
        /// testing
        /// <param name="STATION_ID">拣选工作站ID</param>
        /// <param name="mSTORAGE_MAIN">库存MAIN</param>
        /// <param name="mSTORAGE_LISTS">库存LIST</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool Get_PICK_STATION_STORAGE_BY_STATION_ID(int STATION_ID, out STORAGE_MAIN mSTORAGE_MAIN, out List<STORAGE_LIST> mSTORAGE_LISTS,
            out MANAGE_MAIN mMANAGE_MAIN, out List<MANAGE_LIST> mMANAGE_LISTS, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;
            mSTORAGE_MAIN = null;
            mSTORAGE_LISTS = null;
            mMANAGE_MAIN = null;
            mMANAGE_LISTS = null;

            try
            {
                Model.T_PICK_STATION mT_PICK_STATION = this._P_T_PICK_STATION.GetModel(STATION_ID);
                if (mT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到STATION_ID:{0}对应的拣选工作站", STATION_ID);
                    return bResult;
                }
                //Model.WH_CELL mWH_CELL = this._P_WH_CELL.GetModel(mT_PICK_STATION.WH_CELL_ID);
                //if (mWH_CELL == null)
                //{
                //    sResult = string.Format("未找到STATION_ID:{0}对应的拣选工作站CELL_ID:{1}对应的货位",
                //        mT_PICK_STATION.STATION_ID, mT_PICK_STATION.WH_CELL_ID);
                //    return bResult;
                //}
                //IList<STORAGE_MAIN> mSTORAGE_MAINS = this._P_STORAGE_MAIN.GetListCellID(mWH_CELL.CELL_ID);
                //if (mSTORAGE_MAINS.Count != 1)
                //{
                //    sResult = string.Format("货位{0}上存在的库存不符规定", mWH_CELL.CELL_CODE);
                //    return bResult;
                //}
                if (!string.IsNullOrEmpty(mT_PICK_STATION.PROPERTY4))
                {
                    //mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(mT_PICK_STATION.PROPERTY4);
                    mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelCellIDStockBarcode(mT_PICK_STATION.WH_CELL_ID, mT_PICK_STATION.PROPERTY4);
                }

                if (mSTORAGE_MAIN == null)
                {
                    bResult = false;
                    return bResult;
                }

                mSTORAGE_LISTS = this._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).ToList();

                if (mSTORAGE_MAIN != null && mSTORAGE_LISTS.Count > 0)
                {
                    bResult = true;
                }


                int manage_id = Convert.ToInt32(mT_PICK_STATION.PROPERTY5);

                mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(manage_id);
                if (mMANAGE_MAIN != null)
                {
                    mMANAGE_LISTS = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID).ToList();
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过CELL_ID获得货位上的库存
        /// hejiaji 
        /// 20180101
        /// testing
        /// <param name="WH_CELL_ID">cell_id</param>
        /// <returns>库存实例</returns>
        public STORAGE_MAIN Get_STORAGE_MAIN_BY_CELL_ID(int WH_CELL_ID)
        {
            STORAGE_MAIN rSTORAGE_MAIN = null;
            try
            {
                IList<STORAGE_MAIN> rSTORAGE_MAINS = this._P_STORAGE_MAIN.GetListCellID(WH_CELL_ID);

                if (rSTORAGE_MAINS.Count == 1)
                {
                    rSTORAGE_MAIN = rSTORAGE_MAINS[0];
                    return rSTORAGE_MAIN;
                }
                else
                {
                    return null;
                }
            }
            catch
            {
                return null;
            }
        }

        /// <summary>
        /// 拣选工作站相关
        /// 获得盘点工作点与计划的绑定关系
        /// hejiaji 
        /// 20180101
        /// done
        /// <param name="PICK_POSITION_ID">拣选工作点ID</param>
        /// <returns>绑定关系实例</returns>
        public T_PICK_POSITION_PLAN_BIND Get_T_PICK_POSITION_PLAN_BIND_BY_PICK_POSITION_ID(int PICK_POSITION_ID)
        {
            return this._P_T_PICK_POSITION_PLAN_BIND.GetModel_By_PICK_POSITION_ID(PICK_POSITION_ID);
        }


        /// <summary>
        /// 拣选工作站相关
        /// 将拣选工作站与订单绑定
        /// hejiaji 
        /// 20180101
        /// done
        /// <param name="mSYS_USER">用户</param>
        /// <param name="PickStationID">拣选工作站</param>
        /// <param name="OrderCode">订单号</param>
        /// <param name="Plan_Group">订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool BindOrderCodeToPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string Plan_Group, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    #region 判断是否可以进行绑定
                    List<Model.T_PICK_STATION> CheckT_PICK_STATIONs = this._P_T_PICK_STATION.GetList().ToList();
                    if (CheckT_PICK_STATIONs.Count > 0)
                    {
                        foreach (Model.T_PICK_STATION ccT_PICK_STATION in CheckT_PICK_STATIONs)
                        {
                            if (ccT_PICK_STATION.STATION_ID != uT_PICK_STATION.STATION_ID)
                            {
                                if (!string.IsNullOrEmpty(ccT_PICK_STATION.PLAN_GROUP_CODE))
                                {
                                    if (ccT_PICK_STATION.PLAN_GROUP_CODE.Equals(Plan_Group))
                                    {
                                        //该唯一码已经被绑定
                                        bResult = false;
                                        sResult = string.Format("该唯一码:{0}已经被{1}绑定，请绑定其他唯一码", Plan_Group, ccT_PICK_STATION.STATION_NAME);
                                        return bResult;
                                    }
                                }

                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(uT_PICK_STATION.PLAN_GROUP_NAME) || uT_PICK_STATION.FLAG == 1 || uT_PICK_STATION.PROPERTY3 == Enum.FLAG.Disable.ToString("d"))
                    {
                        bResult = false;
                        sResult = string.Format("拣选工作站当前已绑定订单，未处在空闲状态，请先对其进行解绑操作，绑定订单唯一码为：{0}", uT_PICK_STATION.PLAN_GROUP_NAME);
                        return bResult;
                    }
                    #endregion

                    uT_PICK_STATION.PLAN_GROUP_NAME = Plan_Group;
                    uT_PICK_STATION.PLAN_GROUP_CODE = Plan_Group;
                    uT_PICK_STATION.FLAG = 1;//拣选状态
                    uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Disable.ToString("d");
                    this._P_T_PICK_STATION.Update(uT_PICK_STATION);

                    #region wbs与PICK_POSITION绑定

                    List<T_PICK_POSITION> T_PICK_POSITIONs
                        = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(uT_PICK_STATION.STATION_ID)
                        .OrderBy(p => p.POSTION_STATUS).ToList();
                    List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(Plan_Group).OrderBy(t => t.PLAN_CODE).ToList();

                    this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_STATION_ID(uT_PICK_STATION.STATION_ID);

                    int PLAN_MAIN_INDEX = 0;

                    foreach (T_PICK_POSITION fT_PICK_POSITION in T_PICK_POSITIONs)
                    {
                        if (PLAN_MAIN_INDEX <= PLAN_MAINs.Count - 1)
                        {
                            T_PICK_POSITION_PLAN_BIND aT_PICK_POSITION_PLAN_BIND = new T_PICK_POSITION_PLAN_BIND();

                            aT_PICK_POSITION_PLAN_BIND.PICK_POSITION_ID = fT_PICK_POSITION.POSITION_ID;
                            aT_PICK_POSITION_PLAN_BIND.PLAN_ID = PLAN_MAINs[PLAN_MAIN_INDEX].PLAN_ID;
                            aT_PICK_POSITION_PLAN_BIND.PICK_STATION_ID = uT_PICK_STATION.STATION_ID;
                            aT_PICK_POSITION_PLAN_BIND.CELL_ID = fT_PICK_POSITION.WH_CELL_ID;
                            aT_PICK_POSITION_PLAN_BIND.USER_ID = mSYS_USER.USER_ID;
                            aT_PICK_POSITION_PLAN_BIND.BIND_TIME = Common.StringUtil.GetDateTime();
                            aT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Disable.ToString("d");//0 表示还没有被锁定

                            this._P_T_PICK_POSITION_PLAN_BIND.Add(aT_PICK_POSITION_PLAN_BIND);

                            //PLAN_MAINs[PLAN_MAIN_INDEX].PLAN_FLAG = Enum.FLAG.Enable.ToString("d");
                            this._P_PLAN_MAIN.Update(PLAN_MAINs[PLAN_MAIN_INDEX]);

                            PLAN_MAIN_INDEX++;
                        }
                    }
                    #endregion

                    bResult = true;
                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    if (bResult)
                    {
                        this._P_Base_House.CommitTransaction();
                    }
                    else
                    {
                        this._P_Base_House.RollBackTransaction();
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 对拣选工作站进行订单锁定、出库操作
        /// hejiaji 
        /// 20180101
        /// doing
        /// <param name="mSYS_USER">用户</param>
        /// <param name="PickStationID">拣选工作站</param>
        /// <param name="OrderCode">订单号</param>
        /// <param name="Plan_Group">订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool LockAndOutOrderByPickStation(SYS_USER mSYS_USER, int PickStationID, string Plan_Group, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                bResult = !(uT_PICK_STATION.PLAN_GROUP_CODE == Plan_Group);
                if (bResult)
                {
                    sResult = string.Format("操作失败,{0}信息错误,已绑定订单唯一码{1}与当前唯一码{2}不符",
                        uT_PICK_STATION.STATION_NAME,
                        uT_PICK_STATION.PLAN_GROUP_CODE,
                        Plan_Group);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    uT_PICK_STATION.REMARK = "已绑定订单，已经锁定库存";
                    this._P_T_PICK_STATION.Update(uT_PICK_STATION);
                    //锁定
                    bResult = new Plan.PlanPick().PlanLock(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                    ////下达下架任务
                    //bResult = new Plan.PlanPick().PlanCreateManageOut(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    //if (!bResult)
                    //{
                    //    return bResult;
                    //}

                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 对拣选工作站进行订单锁定、出库操作(允许缺料锁定)
        /// hejiaji 
        /// 20180518
        /// doing
        /// <param name="mSYS_USER">用户</param>
        /// <param name="PickStationID">拣选工作站</param>
        /// <param name="OrderCode">订单号</param>
        /// <param name="Plan_Group">订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool LockAndOutOrderPartlyByPickStation(SYS_USER mSYS_USER, int PickStationID, string Plan_Group, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                bResult = !(uT_PICK_STATION.PLAN_GROUP_CODE == Plan_Group);
                if (bResult)
                {
                    sResult = string.Format("操作失败,{0}信息错误,已绑定订单唯一码{1}与当前唯一码{2}不符",
                        uT_PICK_STATION.STATION_NAME,
                        uT_PICK_STATION.PLAN_GROUP_CODE,
                        Plan_Group);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    uT_PICK_STATION.REMARK = "已绑定订单，已经锁定库存";
                    this._P_T_PICK_STATION.Update(uT_PICK_STATION);
                    //锁定
                    bResult = new Plan.PlanPick().PlanLockPartly(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 对拣选工作站进行订单锁定、出库操作
        /// hejiaji 
        /// 20180101
        /// doing
        /// <param name="mSYS_USER">用户</param>
        /// <param name="PickStationID">拣选工作站</param>
        /// <param name="OrderCode">订单号</param>
        /// <param name="Plan_Group">订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool ManageDownOutByPickStation(SYS_USER mSYS_USER, int PickStationID, string Plan_Group, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                bResult = !(uT_PICK_STATION.PLAN_GROUP_CODE == Plan_Group);
                if (bResult)
                {
                    sResult = string.Format("操作失败,{0}信息错误,已绑定订单唯一码{1}与当前唯一码{2}不符",
                        uT_PICK_STATION.STATION_NAME,
                        uT_PICK_STATION.PLAN_GROUP_CODE,
                        Plan_Group);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    uT_PICK_STATION.REMARK = "已绑定订单，正在出库";
                    this._P_T_PICK_STATION.Update(uT_PICK_STATION);
                    ////锁定
                    //bResult = new Plan.PlanPick().PlanLock(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    //if (!bResult)
                    //{
                    //    return bResult;
                    //}
                    //下达下架任务
                    bResult = new Plan.PlanPick().PlanCreateManageOut(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }
        /// <summary>
        /// 拣选工作站相关
        /// 解除拣选工作站上绑定的订单
        /// testing
        /// 2018-4-7更改 实现订单解绑后计划状态分配数量和锁定数量重置的要求
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickStationID"></param>
        /// <param name="OrderCode"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool UnBindOrderPickStation(SYS_USER mSYS_USER, int PickStationID, out string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            weiyima = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                #region 清除锁定库存 更新计划数量
                List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(uT_PICK_STATION.PLAN_GROUP_CODE).ToList();
                if (PLAN_MAINs.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站绑定的唯一码{0}对应的拣选计划", uT_PICK_STATION.PLAN_GROUP_CODE);
                    return bResult;
                }

                foreach (Model.PLAN_MAIN mPLAN_MAIN in PLAN_MAINs)
                {
                    List<Model.PLAN_LIST> PLAN_LISTs = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID).ToList();

                    foreach (Model.PLAN_LIST mPLAN_LIST in PLAN_LISTs)
                    {
                        if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > 0)
                        {
                            bResult = false;
                            sResult = string.Format("该拣选订单已经开始拣选，无法解除锁定");
                            return bResult;
                        }

                        mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = 0;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);

                        List<Model.STORAGE_LOCK> STORAGE_LOCKs = this._P_STORAGE_LOCK.GetList_PLAN_LIST_ID(mPLAN_LIST.PLAN_LIST_ID).ToList();
                        foreach (Model.STORAGE_LOCK mSTORAGE_LOCK in STORAGE_LOCKs)
                        {
                            List<Model.MANAGE_LIST> MANAGE_LISTs = this._P_MANAGE_LIST.GetListBySTORAGE_LOCK_ID(mSTORAGE_LOCK.STORAGE_LOCK_ID).ToList();
                            foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTs)
                            {
                                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mMANAGE_LIST.MANAGE_ID);

                                if (mMANAGE_MAIN != null)
                                {
                                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);
                                    this._P_MANAGE_MAIN.Delete(mMANAGE_LIST.MANAGE_ID);
                                }
                            }
                            this._P_STORAGE_LOCK.Delete(mSTORAGE_LOCK.STORAGE_LOCK_ID);
                        }
                    }
                    mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                    this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                #endregion

                //OrderCode = uT_PICK_STATION.PLAN_GROUP_NAME;
                weiyima = uT_PICK_STATION.PLAN_GROUP_CODE;
                uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                uT_PICK_STATION.PROPERTY4 = string.Empty;
                uT_PICK_STATION.PROPERTY5 = string.Empty;
                uT_PICK_STATION.REMARK = string.Empty;
                uT_PICK_STATION.FLAG = 0;//非拣选状态
                uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲

                this._P_T_PICK_STATION.Update(uT_PICK_STATION);

                List<T_PICK_POSITION> T_PICK_POSITIONs = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(uT_PICK_STATION.STATION_ID).ToList();

                this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_STATION_ID(uT_PICK_STATION.STATION_ID);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        public bool CreateManageDetailsForManagePick(SYS_USER mSYS_USER, List<MANAGE_DETAIL> lsMANAGE_DETAIL, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();
                if (lsMANAGE_DETAIL == null)
                {
                    bResult = false;
                    sResult = string.Format("传入的序列号为空");
                    return bResult;
                }

                if (lsMANAGE_DETAIL.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("传入的序列号数量为0");
                    return bResult;
                }
                int manage_list_id = lsMANAGE_DETAIL[0].MANAGE_LIST_ID;

                Model.MANAGE_LIST mMANAGE_LIST = this._P_MANAGE_LIST.GetModel(manage_list_id);

                if (mMANAGE_LIST == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到传入的拣选任务明细MANAGE_LIST_ID:{0}", manage_list_id);
                    return bResult;
                }

                if (!mMANAGE_LIST.MANAGE_LIST_QUANTITY.Equals(lsMANAGE_DETAIL.Count))
                {
                    bResult = false;
                    sResult = string.Format("传入的序列号数量与该拣选任务要求的拣选数量不符!");
                    return bResult;
                }

                List<Model.MANAGE_DETAIL> MANAGE_DETAILs = this._P_MANAGE_DETAIL.GetListManageListID(manage_list_id).ToList();
                if (MANAGE_DETAILs.Count > 0)
                {
                    foreach (Model.MANAGE_DETAIL deleMANAGE_DETAIL in MANAGE_DETAILs)
                    {
                        this._P_MANAGE_DETAIL.Delete(deleMANAGE_DETAIL.MANAGE_DETAIL_ID);

                        this._log.DebugFormat(@"[拣选工作站] A类物料拣选，删除原有序列号
MANAGE_DETAIL_ID:{0} 
MANAGE_LIST_ID:{1} 
GOODS_BARCODE{2} 
MANAGE_DETAIL_REMARK:{3}
提交人员:{4}"
                           , deleMANAGE_DETAIL.MANAGE_DETAIL_ID
                           , deleMANAGE_DETAIL.MANAGE_LIST_ID
                           , deleMANAGE_DETAIL.GOODS_BARCODE
                           , deleMANAGE_DETAIL.MANAGE_DETAIL_REMARK
                           , mSYS_USER.USER_CODE
                           );
                    }

                    foreach (Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                    {
                        this._P_MANAGE_DETAIL.Add(mMANAGE_DETAIL);
                        this._log.DebugFormat(@"[拣选工作站] A类物料拣选，删除原有序列号后，新增提交的序列号。
MANAGE_DETAIL_ID:{0} 
MANAGE_LIST_ID:{1} 
GOODS_BARCODE{2} 
MANAGE_DETAIL_REMARK:{3}
提交人员:{4}"
                            , mMANAGE_DETAIL.MANAGE_DETAIL_ID
                            , mMANAGE_DETAIL.MANAGE_LIST_ID
                            , mMANAGE_DETAIL.GOODS_BARCODE
                            , mMANAGE_DETAIL.MANAGE_DETAIL_REMARK
                            , mSYS_USER.USER_CODE
                            );
                    }
                }
                else
                {
                    foreach (Model.MANAGE_DETAIL mMANAGE_DETAIL in lsMANAGE_DETAIL)
                    {
                        this._P_MANAGE_DETAIL.Add(mMANAGE_DETAIL);

                        this._log.DebugFormat(@"[拣选工作站] A类物料拣选，新增提交的序列号。
MANAGE_DETAIL_ID:{0} 
MANAGE_LIST_ID:{1} 
GOODS_BARCODE{2} 
MANAGE_DETAIL_REMARK:{3}
提交人员:{4}"
                            , mMANAGE_DETAIL.MANAGE_DETAIL_ID
                            , mMANAGE_DETAIL.MANAGE_LIST_ID
                            , mMANAGE_DETAIL.GOODS_BARCODE
                            , mMANAGE_DETAIL.MANAGE_DETAIL_REMARK
                            , mSYS_USER.USER_CODE
                            );
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                    this._log.DebugFormat(@"[拣选工作站] A类物料拣选 提交序列号事务提交成功！");
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                    this._log.DebugFormat(@"[拣选工作站] A类物料拣选 提交序列号事务回滚！");

                }
            }
            return bResult;
        }

        public bool ErrorOperationUnBindOrder(SYS_USER mSYS_USER, int PickStationID, out string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            weiyima = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                #region 清除锁定库存 更新计划数量
                List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(uT_PICK_STATION.PLAN_GROUP_CODE).ToList();
                if (PLAN_MAINs.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站绑定的唯一码{0}对应的拣选计划", uT_PICK_STATION.PLAN_GROUP_CODE);
                    return bResult;
                }

                foreach (Model.PLAN_MAIN mPLAN_MAIN in PLAN_MAINs)
                {
                    List<Model.PLAN_LIST> PLAN_LISTs = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID).ToList();

                    foreach (Model.PLAN_LIST mPLAN_LIST in PLAN_LISTs)
                    {
                        //if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > 0)
                        //{
                        //    bResult = false;
                        //    sResult = string.Format("该拣选订单已经开始拣选，无法解除锁定");
                        //    return bResult;
                        //}

                        mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);

                        List<Model.STORAGE_LOCK> STORAGE_LOCKs = this._P_STORAGE_LOCK.GetList_PLAN_LIST_ID(mPLAN_LIST.PLAN_LIST_ID).ToList();
                        foreach (Model.STORAGE_LOCK mSTORAGE_LOCK in STORAGE_LOCKs)
                        {
                            List<Model.MANAGE_LIST> MANAGE_LISTs = this._P_MANAGE_LIST.GetListBySTORAGE_LOCK_ID(mSTORAGE_LOCK.STORAGE_LOCK_ID).ToList();
                            foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTs)
                            {
                                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mMANAGE_LIST.MANAGE_ID);

                                if (mMANAGE_MAIN != null)
                                {
                                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);
                                    this._P_MANAGE_MAIN.Delete(mMANAGE_LIST.MANAGE_ID);
                                }
                            }
                            this._P_STORAGE_LOCK.Delete(mSTORAGE_LOCK.STORAGE_LOCK_ID);
                        }
                    }


                    if (mPLAN_MAIN.PLAN_STATUS != Enum.PLAN_STATUS.Finish.ToString())
                    {
                        mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                        //this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                    }
                    //mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                    this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                #endregion

                //OrderCode = uT_PICK_STATION.PLAN_GROUP_NAME;
                weiyima = uT_PICK_STATION.PLAN_GROUP_CODE;
                uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                uT_PICK_STATION.PROPERTY4 = string.Empty;
                uT_PICK_STATION.PROPERTY5 = string.Empty;
                uT_PICK_STATION.REMARK = string.Empty;
                uT_PICK_STATION.FLAG = 0;//非拣选状态
                uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲

                this._P_T_PICK_STATION.Update(uT_PICK_STATION);

                List<T_PICK_POSITION> T_PICK_POSITIONs = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(uT_PICK_STATION.STATION_ID).ToList();

                this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_STATION_ID(uT_PICK_STATION.STATION_ID);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                    this._log.Info(string.Format("用户:{0} 拣选工作站ID:{1} 强断解除唯一码:{2},成功!", mSYS_USER.USER_CODE, PickStationID, weiyima));
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                    this._log.Info(string.Format("用户:{0} 拣选工作站ID:{1} 强断解除唯一码:{2},失败!,原因:{3}", mSYS_USER.USER_CODE, PickStationID, weiyima, sResult));
                }
            }
            return bResult;
        }

        public bool ErrorOperationUnLock(SYS_USER mSYS_USER, int PickStationID, out string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            weiyima = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                #region 清除锁定库存 更新计划数量
                List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(uT_PICK_STATION.PLAN_GROUP_CODE).ToList();
                if (PLAN_MAINs.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站绑定的唯一码{0}对应的拣选计划", uT_PICK_STATION.PLAN_GROUP_CODE);
                    return bResult;
                }

                foreach (Model.PLAN_MAIN mPLAN_MAIN in PLAN_MAINs)
                {
                    List<Model.PLAN_LIST> PLAN_LISTs = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID).ToList();

                    foreach (Model.PLAN_LIST mPLAN_LIST in PLAN_LISTs)
                    {
                        //if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > 0)
                        //{
                        //    bResult = false;
                        //    sResult = string.Format("该拣选订单已经开始拣选，无法解除锁定");
                        //    return bResult;
                        //}

                        mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);

                        List<Model.STORAGE_LOCK> STORAGE_LOCKs = this._P_STORAGE_LOCK.GetList_PLAN_LIST_ID(mPLAN_LIST.PLAN_LIST_ID).ToList();
                        foreach (Model.STORAGE_LOCK mSTORAGE_LOCK in STORAGE_LOCKs)
                        {
                            List<Model.MANAGE_LIST> MANAGE_LISTs = this._P_MANAGE_LIST.GetListBySTORAGE_LOCK_ID(mSTORAGE_LOCK.STORAGE_LOCK_ID).ToList();
                            foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTs)
                            {
                                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mMANAGE_LIST.MANAGE_ID);

                                if (mMANAGE_MAIN != null)
                                {
                                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);
                                    this._P_MANAGE_MAIN.Delete(mMANAGE_LIST.MANAGE_ID);
                                }
                            }
                            this._P_STORAGE_LOCK.Delete(mSTORAGE_LOCK.STORAGE_LOCK_ID);
                        }
                    }
                    //mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                    //this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                #endregion

                //OrderCode = uT_PICK_STATION.PLAN_GROUP_NAME;
                weiyima = uT_PICK_STATION.PLAN_GROUP_CODE;
                //uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                //uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                //uT_PICK_STATION.PROPERTY4 = string.Empty;
                //uT_PICK_STATION.PROPERTY5 = string.Empty;
                //uT_PICK_STATION.REMARK = string.Empty;
                //uT_PICK_STATION.FLAG = 0;//非拣选状态
                //uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲

                //this._P_T_PICK_STATION.Update(uT_PICK_STATION);

                //List<T_PICK_POSITION> T_PICK_POSITIONs = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(uT_PICK_STATION.STATION_ID).ToList();

                //this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_STATION_ID(uT_PICK_STATION.STATION_ID);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                    this._log.Info(string.Format("用户:{0} 拣选工作站ID:{1} 解锁库存 唯一码:{2},成功!", mSYS_USER.USER_CODE, PickStationID, weiyima));
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                    this._log.Info(string.Format("用户:{0} 拣选工作站ID:{1} 解锁库存 唯一码:{2},失败!,原因:{3}", mSYS_USER.USER_CODE, PickStationID, weiyima, sResult));
                }
            }
            return bResult;
        }

        #region 预绑定

        /// <summary>
        /// 拣选工作站相关
        /// 将拣选工作站与订单预绑定
        /// hejiaji 
        /// 20180101
        /// done
        /// <param name="mSYS_USER">用户</param>
        /// <param name="PickStationID">拣选工作站</param>
        /// <param name="OrderCode">订单号</param>
        /// <param name="Plan_Group">订单唯一码</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool PrevBindOrderCodeToPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string PrevBindingPlanGroup, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();


                #region 判断是否可以预进行绑定

                #region 检查该拣选工作站是否可以进行预绑定
                if (string.IsNullOrEmpty(uT_PICK_STATION.PLAN_GROUP_CODE))
                {
                    //必须是已经绑定了唯一码的工作站才能进行预绑定
                    bResult = false;
                    sResult = string.Format("预绑定的唯一码:{0}无法绑定至工作站上，因为该拣选工作站当前无订单", PrevBindingPlanGroup);
                    return bResult;

                }

                if (!string.IsNullOrEmpty(uT_PICK_STATION.STATION_MAC))
                {
                    //拣选工作站已经进行了预绑定
                    bResult = false;
                    sResult = string.Format("预绑定的唯一码:{0}无法绑定至工作站上，因为该拣选工作站已经预绑定了唯一码:{1}",
                        PrevBindingPlanGroup, uT_PICK_STATION.STATION_MAC);
                    return bResult;
                }
                #endregion


                #region 检查当前订单是否可以进行预绑定
                bResult = PrevBindOrderCheckAllowToBind(uT_PICK_STATION, PrevBindingPlanGroup, out sResult);

                if (!bResult)
                {
                    return bResult;
                }
                #endregion

                #endregion

                uT_PICK_STATION.STATION_MAC = PrevBindingPlanGroup;

                this._P_T_PICK_STATION.Update(uT_PICK_STATION);

                #region wbs与PICK_POSITION绑定

                List<T_PICK_POSITION> T_PICK_POSITIONs = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(uT_PICK_STATION.STATION_ID).ToList();
                List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(PrevBindingPlanGroup).OrderBy(t => t.PLAN_CODE).ToList();

                if (PLAN_MAINs.Count > 5)
                {
                    bResult = false;
                    sResult = string.Format("预绑定的唯一码:{0}包含WBS计划数量{1}不符合预绑定规范", PrevBindingPlanGroup, PLAN_MAINs.Count);
                    return bResult;
                }
                //this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_STATION_ID(uT_PICK_STATION.STATION_ID);

                int PLAN_MAIN_INDEX = 0;

                foreach (T_PICK_POSITION fT_PICK_POSITION in T_PICK_POSITIONs)
                {
                    if (PLAN_MAIN_INDEX <= PLAN_MAINs.Count - 1)
                    {
                        T_PICK_POSITION_PLAN_BIND aT_PICK_POSITION_PLAN_BIND = new T_PICK_POSITION_PLAN_BIND();

                        aT_PICK_POSITION_PLAN_BIND.PICK_POSITION_ID = fT_PICK_POSITION.POSITION_ID;
                        aT_PICK_POSITION_PLAN_BIND.PLAN_ID = PLAN_MAINs[PLAN_MAIN_INDEX].PLAN_ID;
                        aT_PICK_POSITION_PLAN_BIND.PICK_STATION_ID = uT_PICK_STATION.STATION_ID;
                        aT_PICK_POSITION_PLAN_BIND.CELL_ID = fT_PICK_POSITION.WH_CELL_ID;
                        aT_PICK_POSITION_PLAN_BIND.USER_ID = mSYS_USER.USER_ID;
                        aT_PICK_POSITION_PLAN_BIND.BIND_TIME = Common.StringUtil.GetDateTime();
                        aT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Disable.ToString("d");//0 表示还没有被锁定

                        this._P_T_PICK_POSITION_PLAN_BIND.Add(aT_PICK_POSITION_PLAN_BIND);

                        PLAN_MAIN_INDEX++;
                    }
                }
                #endregion


                #region lock
                bResult = PrevLockAndDownOrderPartlyByPickStation(mSYS_USER, uT_PICK_STATION, PrevBindingPlanGroup, false, out sResult);
                if (!bResult)
                {
                    return bResult;
                }
                #endregion
                bResult = true;
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        /// <summary>
        /// 拣选工作站相关
        /// 预绑定
        /// </summary>
        /// <param name="cT_PICK_STATION"></param>
        /// <param name="PrevBindingPlanGroup"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool PrevBindOrderCheckAllowToBind(Model.T_PICK_STATION cT_PICK_STATION, string PrevBindingPlanGroup, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            #region 检查预绑定的唯一码是否已经被其他拣选工作站绑定或预绑定
            List<Model.T_PICK_STATION> CheckT_PICK_STATIONs = this._P_T_PICK_STATION.GetList().ToList();
            if (CheckT_PICK_STATIONs.Count > 0)
            {
                foreach (Model.T_PICK_STATION ccT_PICK_STATION in CheckT_PICK_STATIONs)
                {
                    if (ccT_PICK_STATION.STATION_ID != cT_PICK_STATION.STATION_ID)
                    {
                        if (!string.IsNullOrEmpty(ccT_PICK_STATION.PLAN_GROUP_CODE))
                        {
                            if (ccT_PICK_STATION.PLAN_GROUP_CODE.Equals(PrevBindingPlanGroup))
                            {
                                //该唯一码已经被绑定
                                bResult = false;
                                sResult = string.Format("预绑定的唯一码:{0}已经被{1}绑定，请绑定其他唯一码", PrevBindingPlanGroup, ccT_PICK_STATION.STATION_NAME);
                                return bResult;
                            }
                        }
                        if (!string.IsNullOrEmpty(ccT_PICK_STATION.STATION_MAC))
                        {
                            if (ccT_PICK_STATION.STATION_MAC.Equals(PrevBindingPlanGroup))
                            {
                                //该唯一码已经被绑定
                                bResult = false;
                                sResult = string.Format("预绑定的唯一码:{0}已经被{1}预绑定，请绑定其他唯一码", PrevBindingPlanGroup, ccT_PICK_STATION.STATION_NAME);
                                return bResult;
                            }
                        }
                    }
                }
            }

            #endregion

            //当前订单长度符合标准
            #region current
            bResult = PrevBindOrderCheckCurrentOrder(cT_PICK_STATION.PLAN_GROUP_CODE, out sResult);

            if (!bResult)
            {
                return bResult;
            }

            #endregion

            //预绑定订单长度符合标准
            #region prevBinding
            bResult = PrevBindOrderCheckCurrentOrder(PrevBindingPlanGroup, out sResult);

            if (!bResult)
            {
                return bResult;
            }
            #endregion

            //当前订单必须是完全锁定
            #region current totally ordered_quantity==list_quantity
            DataTable currentOrderListCheckDt
                       = this.GetList(string.Format(@"select count(*) as quantity from plan_list where plan_id in 
(select plan_id from  plan_main where plan_group='{0}' and plan_type_code='PlanPick' )
and plan_list_quantity <> plan_list_ordered_quantity", cT_PICK_STATION.PLAN_GROUP_CODE));

            int currentOrderListCheckInt = 1;

            if (currentOrderListCheckDt != null)
            {
                if (currentOrderListCheckDt.Rows.Count == 1)
                {
                    currentOrderListCheckInt = Convert.ToInt32(currentOrderListCheckDt.Rows[0][0]);
                }
            }

            if (currentOrderListCheckInt != 0)
            {
                bResult = false;
                sResult = string.Format("错误，拣选工作站当前订单唯一码:{0}不是完全绑定，不可以进行预绑定", cT_PICK_STATION.PLAN_GROUP_CODE);
                return bResult;
            }
            #endregion


            //当前订单必须全部出库，不能有立库至该拣选工作站的下架任务(ManageDown)
            #region Check ManageDown To Pick Station
            DataTable manageDownDt = this.GetList(string.Format(@"select count(*) as quantity from manage_main where end_cell_id={0}", cT_PICK_STATION.WH_CELL_ID));
            int currentPickStationManageCount = 1;
            if (manageDownDt != null)
            {
                if (manageDownDt.Rows.Count == 1)
                {
                    currentPickStationManageCount = Convert.ToInt32(manageDownDt.Rows[0][0]);
                }
            }

            if (currentPickStationManageCount != 0)
            {
                bResult = false;
                sResult = string.Format("错误，拣选工作站当前订单唯一码:{0}还有未完成的下架任务，数量:{1}，不可以进行预绑定", cT_PICK_STATION.PLAN_GROUP_CODE, currentPickStationManageCount);
                return bResult;
            }
            #endregion
            return bResult;
        }

        /// <summary>
        /// 当前订单长度符合标准
        /// </summary>
        /// <param name="PlanGroup">当前订单唯一码</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool PrevBindOrderCheckCurrentOrder(string PlanGroup, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;

            string currentLengthLimit = string.Empty;
            bResult = _S_SystemService.GetSysParameter("PickStationCurrentOrderLengthLimit", out currentLengthLimit);
            if (!bResult)
            {
                bResult = false;
                sResult = string.Format("获取参数错误，无法获得PickStationCurrentOrderLengthLimit参数");
                return bResult;
            }

            DataTable currentOrderLengthDt
                        = this.GetList(string.Format(@"select count(*) as quantity from plan_list where plan_id in 
(select plan_id from  plan_main where plan_group='{0}' and plan_type_code='PlanPick' )", PlanGroup));

            int currentOrderLengthInt = 0;

            if (currentOrderLengthDt != null)
            {
                if (currentOrderLengthDt.Rows.Count == 1)
                {
                    currentOrderLengthInt = Convert.ToInt32(currentOrderLengthDt.Rows[0][0]);



                    int currentOrderLengthLimit = 0;

                    bResult = Int32.TryParse(currentLengthLimit, out currentOrderLengthLimit);

                    if (!bResult)
                    {
                        bResult = false;
                        sResult = string.Format("参数转化错误，{0}", currentLengthLimit);
                        return bResult;
                    }

                    if (currentOrderLengthLimit < currentOrderLengthInt)
                    {
                        bResult = false;
                        sResult = string.Format("错误，当前订单行数{0}大于预绑定操作对当前订单行数{1}要求", currentOrderLengthInt, currentOrderLengthLimit);
                        return bResult;
                    }
                    else
                    {
                        bResult = true;
                    }
                }
            }

            return bResult;
        }

        /// <summary>
        /// 预绑定订单长度符合标准
        /// </summary>
        /// <param name="PrevBindingPlanGroup">预绑定订单唯一码</param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        private bool PrevBindOrderCheckPrevOrder(string PrevBindingPlanGroup, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;

            string prevBindingLengthLimit = string.Empty;
            bResult = _S_SystemService.GetSysParameter("PickStationPrevBindOrderLengthLimit", out prevBindingLengthLimit);
            if (bResult)
            {
                bResult = false;
                sResult = string.Format("获取参数错误，无法获得PickStationPrevBindOrderLengthLimit参数");
                return bResult;
            }

            DataTable prevBindingOrderLengthDt
                        = this.GetList(string.Format(@"select count(*) as quantity from plan_list where plan_id in 
(select plan_id from  plan_main where plan_group='{0}' and plan_type_code='PlanPick' )", PrevBindingPlanGroup));

            int prevBindingOrderLengthInt = 0;

            if (prevBindingOrderLengthDt != null)
            {
                if (prevBindingOrderLengthDt.Rows.Count == 1)
                {
                    prevBindingOrderLengthInt = Convert.ToInt32(prevBindingOrderLengthDt.Rows[0][0]);

                    int prevBindingOrderLengthLimit = 0;

                    bResult = Int32.TryParse(prevBindingLengthLimit, out prevBindingOrderLengthLimit);

                    if (bResult)
                    {
                        bResult = false;
                        sResult = string.Format("参数转化错误，{0}", prevBindingLengthLimit);
                        return bResult;
                    }

                    if (prevBindingOrderLengthLimit < prevBindingOrderLengthInt)
                    {
                        bResult = false;
                        sResult = string.Format("错误，期望预绑定订单行数{0}大于预绑定操作对预绑定订单行数{1}要求", prevBindingOrderLengthInt, prevBindingOrderLengthLimit);
                        return bResult;
                    }
                    else
                    {
                        bResult = true;
                    }
                }
            }

            return bResult;
        }


        /// <summary>
        /// 拣选工作站相关
        /// 对拣选工作站进行订单预锁定，出库操作(允许缺料锁定)
        /// hejiaji 
        /// 20190216
        /// doing
        /// <param name="mSYS_USER">用户</param>
        /// <param name="pT_PICK_STATION">拣选工作站</param>
        /// <param name="prevBindPlanGroup">预绑定订单唯一码</param>
        /// <param name="bTrans">开启事务</param>
        /// <param name="sResult">返回原因</param>
        /// <returns>返回结果</returns>
        public bool PrevLockAndDownOrderPartlyByPickStation(SYS_USER mSYS_USER, T_PICK_STATION pT_PICK_STATION, string prevBindPlanGroup, bool bTrans, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction(bTrans);

                pT_PICK_STATION.REMARK = "已绑定订单，已经锁定库存";
                this._P_T_PICK_STATION.Update(pT_PICK_STATION);
                //锁定
                bResult = new Plan.PlanPick().PrevPlanLockPartly(mSYS_USER, pT_PICK_STATION.STATION_ID, prevBindPlanGroup, false, out sResult);

                if (!bResult)
                {
                    return bResult;
                }

                //出库

                bResult = new Plan.PlanPick().PrevPlanCreateManageOut(mSYS_USER, pT_PICK_STATION.STATION_ID, prevBindPlanGroup, false, out sResult);

                if (!bResult)
                {
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }

            finally
            {
                this._P_Base_House.SaveTransaction(bResult, bTrans);
            }

            return bResult;
        }

        /// <summary>
        /// bak
        /// 2024改造备份
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool OperatePickStationBindOrderBak(out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            string plan_group_target = string.Empty;
            lock (lockobj)
            {
                try
                {
                    this._P_Base_House.BeginTransaction();
                    
                    #region 从数据库获得所有空闲的拣选工作站
                    string idlePickStationSqlstr = string.Format("SELECT STATION_CODE,STATION_NAME,FLAG,PROPERTY3 FROM T_PICK_STATION WHERE FLAG=0 AND PROPERTY3=1  AND (PLAN_GROUP_CODE IS NULL OR LENGTH(PLAN_GROUP_CODE)=0)");

                    DataTable idlePickStationsDt = this.GetList(idlePickStationSqlstr);
                    if (idlePickStationsDt == null)
                    {
                        bResult = false;
                        sResult = string.Format("T_PICK_STATION查询错误");
                        return bResult;
                    }

                    if (idlePickStationsDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("没有空闲的拣选工作站");
                        return bResult;
                    }

                    List<string> idlePickStations = idlePickStationsDt.Rows.Cast<DataRow>().Select(
                        (t) =>
                        {
                            return t["STATION_CODE"].ToString();
                        }).ToList<string>();

                    #endregion

                    #region 从SignalServer 获得所有已连接注册的拣选工作站
                    string[] connectNdLoginPickStationCodesArray;

                    bResult = signalServerClient.ConnnectNdLoginPickStations(string.Empty, out connectNdLoginPickStationCodesArray, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    List<string> connectNdLoginPickStationCodes = connectNdLoginPickStationCodesArray.ToList();

                    if (connectNdLoginPickStationCodes.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("在SignalRServer端，没有拣选工作站处于连接登录状态");
                        return bResult;
                    }
                    #endregion

                    #region result
                    List<string> enablePickStationCodes = new List<string>();
                    enablePickStationCodes = idlePickStations.Intersect(connectNdLoginPickStationCodes).ToList();

                    if (enablePickStationCodes.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("无空闲拣选工作站处于连接登录状态");
                        return bResult;
                    }

                    #region 由唯一码缓冲池获得唯一码
                    string strSql = string.Format("SELECT PLAN_GROUP,ORDER_TIME,P_QTY FROM V_PLAN_PICK_GROUP_WAITING  ORDER BY ORDER_TIME ASC,HEAD_TEXT,PLAN_TO_USER");

                    DataTable resultDt = this.GetList(strSql);
                    if (resultDt == null)
                    {
                        bResult = false;
                        sResult = "V_PLAN_PICK_GROUP_WAITING查询错误";
                        return bResult;
                    }
                    if (resultDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = "订单唯一码缓存池为空";
                        return bResult;
                    }

                    //plan_group_target = resultDt.Rows[0]["PLAN_GROUP"].ToString();
                    #endregion

                    Dictionary<string, string> informDict = new Dictionary<string, string>();
                    int rowIndex = 0;

                    if (resultDt.Rows.Count >= enablePickStationCodes.Count)
                    {
                        foreach (string pickStationCode in enablePickStationCodes)
                        {
                            string sPlanGroup = resultDt.Rows[rowIndex]["PLAN_GROUP"].ToString();
                            informDict.Add(pickStationCode, sPlanGroup);
                            Model.T_PICK_STATION sPickStation = this._P_T_PICK_STATION.GetModel_BY_CODE(pickStationCode);
                            if (sPickStation == null)
                            {
                                bResult = false;
                                sResult = string.Format("未找到编码:{0}的拣选工作站",pickStationCode);
                                return bResult;
                            }
                            sPickStation.PLAN_GROUP_CODE = sPlanGroup;
                            this._P_T_PICK_STATION.Update(sPickStation);

                            rowIndex++;
                        }
                    }
                    else
                    {
                        foreach(DataRow dr in resultDt.Rows)
                        {
                            string rPlanGroup = dr["PLAN_GROUP"].ToString();
                            string code = enablePickStationCodes[rowIndex];
                            informDict.Add(code, rPlanGroup);
                            Model.T_PICK_STATION sPickStation = this._P_T_PICK_STATION.GetModel_BY_CODE(code);
                            if (sPickStation == null)
                            {
                                bResult = false;
                                sResult = string.Format("未找到编码:{0}的拣选工作站", code);
                                return bResult;
                            }
                            sPickStation.PLAN_GROUP_CODE = rPlanGroup;
                            this._P_T_PICK_STATION.Update(sPickStation);
                            rowIndex++;
                        }
                    }

                    if (informDict.Keys.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("通讯字典组装失败");
                        return bResult;
                    }

                    bResult=signalServerClient.InformPickStationBindNewOrderMultiple(informDict,out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                    #endregion

                    return bResult;

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.Message;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }
            }
                
            return bResult;
        }

        /// <summary>
        /// 2024改造暂行
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool OperatePickStationBindOrder(out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            string plan_group_target = string.Empty;
            lock (lockobj)
            {
                try
                {
                    this._P_Base_House.BeginTransaction();

                    #region 从数据库获得所有空闲的拣选工作站
                    string idlePickStationSqlstr = string.Format(@"SELECT STATION_CODE,STATION_NAME,FLAG,PROPERTY3 FROM T_PICK_STATION 
WHERE FLAG=0 AND PROPERTY3=1 AND PROPERTY9=0  AND (PLAN_GROUP_CODE IS NULL OR LENGTH(PLAN_GROUP_CODE)=0)");

                    DataTable idlePickStationsDt = this.GetList(idlePickStationSqlstr);
                    if (idlePickStationsDt == null)
                    {
                        bResult = false;
                        sResult = string.Format("T_PICK_STATION查询错误");
                        return bResult;
                    }

                    if (idlePickStationsDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("没有空闲的拣选工作站");
                        return bResult;
                    }

                    List<string> idlePickStations = idlePickStationsDt.Rows.Cast<DataRow>().Select(
                        (t) =>
                        {
                            return t["STATION_CODE"].ToString();
                        }).ToList<string>();

                    #endregion

                    #region 从SignalServer 获得所有已连接注册的拣选工作站
                    string[] connectNdLoginPickStationCodesArray;

                    bResult = signalServerClient.ConnnectNdLoginPickStations(string.Empty, out connectNdLoginPickStationCodesArray, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    List<string> connectNdLoginPickStationCodes = connectNdLoginPickStationCodesArray.ToList();

                    if (connectNdLoginPickStationCodes.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("在SignalRServer端，没有拣选工作站处于连接登录状态");
                        return bResult;
                    }
                    #endregion

                    #region result
                    List<string> enablePickStationCodes = new List<string>();
                    enablePickStationCodes = idlePickStations.Intersect(connectNdLoginPickStationCodes).ToList();

                    if (enablePickStationCodes.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("无空闲拣选工作站处于连接登录状态");
                        return bResult;
                    }

                    #region 由唯一码缓冲池获得唯一码
                    string strSql = string.Format(@"select DISTINCT PLAN_GROUP from (	SELECT PLAN_GROUP,ORDER_TIME,P_QTY FROM V_PLAN_PICK_GROUP_WAITING  ORDER BY ORDER_TIME ASC,HEAD_TEXT,PLAN_TO_USER)");

                    DataTable resultDt = this.GetList(strSql);
                    if (resultDt == null)
                    {
                        bResult = false;
                        sResult = "V_PLAN_PICK_GROUP_WAITING查询错误";
                        return bResult;
                    }
                    if (resultDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = "订单唯一码缓存池为空";
                        return bResult;
                    }
                    
                    //plan_group_target = resultDt.Rows[0]["PLAN_GROUP"].ToString();
                    #endregion

                    Dictionary<string, string> informDict = new Dictionary<string, string>();
                    int rowIndex = 0;

                    if (resultDt.Rows.Count >= enablePickStationCodes.Count)
                    {
                        foreach (string pickStationCode in enablePickStationCodes)
                        {
                            string sPlanGroup = resultDt.Rows[rowIndex]["PLAN_GROUP"].ToString();
                            informDict.Add(pickStationCode, sPlanGroup);
                            Model.T_PICK_STATION sPickStation = this._P_T_PICK_STATION.GetModel_BY_CODE(pickStationCode);
                            if (sPickStation == null)
                            {
                                bResult = false;
                                sResult = string.Format("未找到编码:{0}的拣选工作站", pickStationCode);
                                return bResult;
                            }
                            sPickStation.PLAN_GROUP_CODE = sPlanGroup;
                            this._P_T_PICK_STATION.Update(sPickStation);

                            rowIndex++;
                        }
                    }
                    else
                    {
                        foreach (DataRow dr in resultDt.Rows)
                        {
                            string rPlanGroup = dr["PLAN_GROUP"].ToString();
                            string code = enablePickStationCodes[rowIndex];
                            informDict.Add(code, rPlanGroup);
                            Model.T_PICK_STATION sPickStation = this._P_T_PICK_STATION.GetModel_BY_CODE(code);
                            if (sPickStation == null)
                            {
                                bResult = false;
                                sResult = string.Format("未找到编码:{0}的拣选工作站", code);
                                return bResult;
                            }
                            sPickStation.PLAN_GROUP_CODE = rPlanGroup;
                            this._P_T_PICK_STATION.Update(sPickStation);
                            rowIndex++;
                        }
                    }

                    if (informDict.Keys.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("通讯字典组装失败");
                        return bResult;
                    }

                    bResult = signalServerClient.InformPickStationBindNewOrderMultiple(informDict, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                    #endregion

                    return bResult;

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.Message;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }
            }

            return bResult;
        }


        /// <summary>
        /// 2024改造
        /// 测试
        /// </summary>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool OperatePickStationBindOrderNewTest(out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            string plan_group_target = string.Empty;
            lock (lockobj)
            {
                try
                {
                    this._P_Base_House.BeginTransaction();

                    #region 从数据库获得所有空闲的拣选工作站
                    //PROPERTY9=1 表示新模式拣选工作站
                    string idlePickStationSqlstr = string.Format(@"
SELECT
	* 
FROM
	(
	SELECT
		S.*,
		NVL( B.PLAN_QTY, 0 ) AS WORK_QTY 
	FROM
		T_PICK_STATION S
		LEFT JOIN ( SELECT PICK_STATION_ID, COUNT( * ) AS PLAN_QTY FROM T_PICK_POSITION_PLAN_BIND GROUP BY PICK_STATION_ID ) B ON S.STATION_ID = B.PICK_STATION_ID 
	WHERE
		S.PROPERTY9 = 1 
		AND S.FLAG = 0 
		AND S.PROPERTY3 = 1 
		AND ( S.PLAN_GROUP_CODE IS NULL OR LENGTH( S.PLAN_GROUP_CODE ) = 0 ) 
	) 
WHERE
	WORK_QTY <5
");

                    DataTable idlePickStationsDt = this.GetList(idlePickStationSqlstr);
                    if (idlePickStationsDt == null)
                    {
                        bResult = false;
                        sResult = string.Format("T_PICK_STATION查询错误");
                        return bResult;
                    }

                    if (idlePickStationsDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("没有空闲的新拣选工作站");
                        return bResult;
                    }

                    List<string> idlePickStations = idlePickStationsDt.Rows.Cast<DataRow>().Select(
                        (t) =>
                        {
                            return t["STATION_CODE"].ToString();
                        }).ToList<string>();

                    #endregion

                    #region 从SignalServer 获得所有已连接注册的拣选工作站
                    string[] connectNdLoginPickStationCodesArray;

                    //connectNdLoginPickStationCodesArray = new string[] { "PICK_STATION3", "PICK_STATION6" };
                    bResult = signalServerClient.ConnnectNdLoginPickStations(string.Empty, out connectNdLoginPickStationCodesArray, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                    List<string> connectNdLoginPickStationCodes = connectNdLoginPickStationCodesArray.ToList();

                    if (connectNdLoginPickStationCodes.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("在SignalRServer端，没有拣选工作站处于连接登录状态");
                        return bResult;
                    }
                    #endregion

                    #region result
                    List<string> enablePickStationCodes = new List<string>();
                    enablePickStationCodes = idlePickStations.Intersect(connectNdLoginPickStationCodes).ToList();

                    if (enablePickStationCodes.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("无空闲拣选工作站处于连接登录状态");
                        return bResult;
                    }

                    #region 由唯一码缓冲池获得唯一码

                    //唯一码下PLAN_MAIN数量必须为1 才可以应用此逻辑
                    //增加车间 PLAN_SHOPNO
                    string strSql = string.Format(@"SELECT PLAN_GROUP,ORDER_TIME,P_QTY,PLAN_SHOPNO,STATION_CODE FROM V_PLAN_PICK_GROUP_WAITING 
WHERE P_QTY=1 ORDER BY ORDER_TIME ASC,HEAD_TEXT,PLAN_TO_USER");

                    DataTable resultDt = this.GetList(strSql);
                    if (resultDt == null)
                    {
                        bResult = false;
                        sResult = "V_PLAN_PICK_GROUP_WAITING查询错误";
                        return bResult;
                    }
                    if (resultDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = "订单唯一码缓存池为空";
                        return bResult;
                    }

                    //plan_group_target = resultDt.Rows[0]["PLAN_GROUP"].ToString();
                    #endregion
                    Dictionary<string, string> informDict = new Dictionary<string, string>();

                    //2024 改造 推单逻辑实现

                    foreach(DataRow fDr in resultDt.Rows)
                    {
                        #region foreach
                        string planStationCode = fDr["STATION_CODE"].ToString();
                        string sPlanGroup = fDr["PLAN_GROUP"].ToString();

                        if (enablePickStationCodes.Count == 0)
                        {
                            break;
                        }

                        if (enablePickStationCodes.Contains(planStationCode))
                        {
                            //匹配
                            if (informDict.ContainsValue(sPlanGroup))
                            {
                                continue;
                            }
                            informDict.Add(planStationCode, sPlanGroup);

                            Model.T_PICK_STATION sPickStation = this._P_T_PICK_STATION.GetModel_BY_CODE(planStationCode);
                            if (sPickStation == null)
                            {
                                bResult = false;
                                sResult = string.Format("未找到编码:{0}的拣选工作站", planStationCode);
                                return bResult;
                            }
                            sPickStation.PLAN_GROUP_CODE = sPlanGroup;
                            this._P_T_PICK_STATION.Update(sPickStation);

                            enablePickStationCodes.Remove(planStationCode);
                        }
                        #endregion
                    }

                    if (informDict.Keys.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format("通讯字典组装失败");
                        return bResult;
                    }

                    bResult = signalServerClient.InformPickStationBindNewOrderMultiple(informDict, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                    #endregion

                    return bResult;

                }
                catch (Exception ex)
                {
                    bResult = false;
                    sResult = ex.Message;
                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }
            }

            return bResult;
        }


        /// <summary>
        /// 
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickStationID"></param>
        /// <param name="weiyimaUnBind"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool UnAtuoBindOrderPickStation(Model.SYS_USER mSYS_USER, int PickStationID, string weiyimaUnBind, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
           
            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                if (uT_PICK_STATION.PLAN_GROUP_CODE != weiyimaUnBind)
                {
                    bResult = false;
                    sResult = string.Format("需要拒绝绑定的唯一码{0}与工作站绑定的唯一码{1}不符",
                        weiyimaUnBind,uT_PICK_STATION.PLAN_GROUP_CODE);
                    return bResult;
                }
                
                uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                uT_PICK_STATION.PROPERTY4 = string.Empty;
                uT_PICK_STATION.PROPERTY5 = string.Empty;
                uT_PICK_STATION.REMARK = string.Empty;
                uT_PICK_STATION.FLAG = 0;//非拣选状态
                uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲

                this._P_T_PICK_STATION.Update(uT_PICK_STATION);
                
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        /// <summary>
        /// 
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool AgentAutoBindOrder(SYS_USER mSYS_USER, string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction();
                this._log.Info(string.Format("用户{0}加急唯一码为{1}订单准备",mSYS_USER.USER_CODE,weiyima));

                List<Model.PLAN_MAIN> cPlanMains = this._P_PLAN_MAIN.GetList_PLAN_GROUP(weiyima).ToList();

                if (cPlanMains.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未找到唯一码为{0}的计划信息",weiyima);
                    return bResult;
                }
                
                foreach(Model.PLAN_MAIN main in cPlanMains)
                {
                    main.PLAN_FROM_DEPT = string.Format("0{0}", main.PLAN_FROM_DEPT);

                    this._P_PLAN_MAIN.Update(main);
                }

            }
            catch(Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
            }
            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                    this._log.Info(string.Format("用户{0}加急唯一码为{1}订单成功", mSYS_USER.USER_CODE, weiyima));
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                    this._log.Info(string.Format("用户{0}加急唯一码为{1}订单失败，原因:{2}", mSYS_USER.USER_CODE, weiyima,sResult));

                }
            }
            return bResult;
        }


        /// <summary>
        /// 改造2024
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickStationID"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        /// <exception cref="NotImplementedException"></exception>
        public bool BindOrderCodeToPickStationNew2024(SYS_USER mSYS_USER, int PickStationID, string Plan_Group, out string sResult)
        {
            bool bResult = false;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    #region 判断是否可以进行绑定
                    List<Model.T_PICK_STATION> CheckT_PICK_STATIONs = this._P_T_PICK_STATION.GetList().ToList();
                    if (CheckT_PICK_STATIONs.Count > 0)
                    {
                        foreach (Model.T_PICK_STATION ccT_PICK_STATION in CheckT_PICK_STATIONs)
                        {
                            if (ccT_PICK_STATION.STATION_ID != uT_PICK_STATION.STATION_ID)
                            {
                                if (!string.IsNullOrEmpty(ccT_PICK_STATION.PLAN_GROUP_CODE))
                                {
                                    if (ccT_PICK_STATION.PLAN_GROUP_CODE.Equals(Plan_Group))
                                    {
                                        //该唯一码已经被绑定
                                        bResult = false;
                                        sResult = string.Format("该唯一码:{0}已经被{1}绑定准备，请绑定其他唯一码", Plan_Group, ccT_PICK_STATION.STATION_NAME);
                                        return bResult;
                                    }
                                }

                            }
                        }
                    }

                    if (!string.IsNullOrEmpty(uT_PICK_STATION.PLAN_GROUP_NAME) || uT_PICK_STATION.FLAG == 1 || uT_PICK_STATION.PROPERTY3 == Enum.FLAG.Disable.ToString("d"))
                    {
                        bResult = false;
                        sResult = string.Format("拣选工作站当前已绑定订单，未处在空闲状态，请先对其进行解绑操作，绑定订单唯一码为：{0}", uT_PICK_STATION.PLAN_GROUP_NAME);
                        return bResult;
                    }



                    string checkPositionPlanGroupSql = string.Format(@"
SELECT
	POSITION_CODE 
FROM
	T_PICK_POSITION 
WHERE
	POSITION_CODE = '{0}'
",Plan_Group);

                    DataTable checkPositionPlanGroupDt = this.GetList(checkPositionPlanGroupSql);
                    if(checkPositionPlanGroupDt!=null && checkPositionPlanGroupDt.Rows.Count > 0)
                    {
                        bResult = false;
                        sResult = string.Format("唯一码{0}已经[新]绑定,无进行本次绑定",Plan_Group);

                        return bResult;
                    }
                    #endregion

                   

                    #region wbs与PICK_POSITION绑定

                    List<T_PICK_POSITION> T_PICK_POSITIONs
                        = this._P_T_PICK_POSITION.GetModelList_BY_STATION_ID(uT_PICK_STATION.STATION_ID)
                        .OrderBy(p => p.POSTION_STATUS).ToList();


                    if(T_PICK_POSITIONs.Where(p => string.IsNullOrEmpty(p.POSITION_CODE)).Count() == 0)
                    {
                        bResult = false;
                        sResult = string.Format("本拣选工作站没有空闲的拣选工位,无法绑定");
                        return bResult;
                    }

                    List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(Plan_Group).OrderBy(t => t.PLAN_CODE).ToList();

                    if(PLAN_MAINs.Count!=1)
                    {
                        bResult = false;
                        sResult = string.Format("唯一码{0}计划数量不唯一",Plan_Group);
                        return bResult;
                    }
                    Model.PLAN_MAIN mPlanMain = PLAN_MAINs.First();

                    
                    foreach (T_PICK_POSITION fT_PICK_POSITION in T_PICK_POSITIONs)
                    {

                        if(!string.IsNullOrEmpty(fT_PICK_POSITION.POSITION_CODE))
                        {
                            continue;
                        }

                        this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_POSITION_ID(fT_PICK_POSITION.POSITION_ID);

                        T_PICK_POSITION_PLAN_BIND aT_PICK_POSITION_PLAN_BIND = new T_PICK_POSITION_PLAN_BIND();

                        aT_PICK_POSITION_PLAN_BIND.PICK_POSITION_ID = fT_PICK_POSITION.POSITION_ID;
                        aT_PICK_POSITION_PLAN_BIND.PLAN_ID = mPlanMain.PLAN_ID;
                        aT_PICK_POSITION_PLAN_BIND.PICK_STATION_ID = uT_PICK_STATION.STATION_ID;
                        aT_PICK_POSITION_PLAN_BIND.CELL_ID = fT_PICK_POSITION.WH_CELL_ID;
                        aT_PICK_POSITION_PLAN_BIND.USER_ID = mSYS_USER.USER_ID;
                        aT_PICK_POSITION_PLAN_BIND.BIND_TIME = Common.StringUtil.GetDateTime();
                        aT_PICK_POSITION_PLAN_BIND.FLAG = Enum.FLAG.Disable.ToString("d");//0 表示还没有被锁定

                        this._P_T_PICK_POSITION_PLAN_BIND.Add(aT_PICK_POSITION_PLAN_BIND);

                        fT_PICK_POSITION.POSITION_CODE = Plan_Group;
                        this._P_T_PICK_POSITION.Update(fT_PICK_POSITION);
                        break;
                    }
                    #endregion


                    string checkEmptyPositionCountSql = string.Format(@"
SELECT
	COUNT(*) AS EMPTY_QTY 
FROM
	T_PICK_POSITION 
WHERE
	( POSITION_CODE IS NULL OR LENGTH( POSITION_CODE ) = 0 ) 
	AND STATION_ID ={0}
", PickStationID);
                    DataTable checkEmptyPositionCountDt = this.GetList(checkEmptyPositionCountSql);

                    if(checkEmptyPositionCountDt == null || checkEmptyPositionCountDt.Rows.Count == 0)
                    {
                        bResult = false;
                        sResult = string.Format(@"验证拣选工作站是否绑定满语句检索错误:{0}",checkEmptyPositionCountSql);
                        return bResult;
                    }

                    int emptyQty = 0;
                    emptyQty = Convert.ToInt32(checkEmptyPositionCountDt.Rows[0]["EMPTY_QTY"]);

                    if (emptyQty==0)
                    {
                        uT_PICK_STATION.PLAN_GROUP_NAME = "绑定已满";
                        uT_PICK_STATION.PLAN_GROUP_CODE = "绑定已满";
                        uT_PICK_STATION.FLAG = 1;//拣选状态
                        uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Disable.ToString("d");
                        this._P_T_PICK_STATION.Update(uT_PICK_STATION);
                    }
                    else
                    {
                        uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                        uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                        uT_PICK_STATION.FLAG = 0;//拣选状态
                        uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");
                        this._P_T_PICK_STATION.Update(uT_PICK_STATION);
                    }

                    bResult = true;
                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    if (bResult)
                    {
                        this._P_Base_House.CommitTransaction();
                    }
                    else
                    {
                        this._P_Base_House.RollBackTransaction();
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }


        /// <summary>
        /// 改造2024
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickStationID"></param>
        /// <param name="weiyimaUnBind"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool UnAtuoBindOrderPickStationNew2024(Model.SYS_USER mSYS_USER, int PickStationID, string weiyimaUnBind, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(PickStationID);
                if (uT_PICK_STATION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                if (uT_PICK_STATION.PLAN_GROUP_CODE != weiyimaUnBind)
                {
                    bResult = false;
                    sResult = string.Format("需要拒绝绑定的唯一码{0}与工作站绑定的唯一码{1}不符",
                        weiyimaUnBind, uT_PICK_STATION.PLAN_GROUP_CODE);
                    return bResult;
                }

                uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                uT_PICK_STATION.PROPERTY4 = string.Empty;
                uT_PICK_STATION.PROPERTY5 = string.Empty;
                uT_PICK_STATION.REMARK = string.Empty;
                uT_PICK_STATION.FLAG = 0;//非拣选状态
                uT_PICK_STATION.PROPERTY3 = Enum.FLAG.Enable.ToString("d");//空闲

                this._P_T_PICK_STATION.Update(uT_PICK_STATION);

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        public bool UnBindOrderPickPosition(SYS_USER mSYS_USER, int PickPositionID, out string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            weiyima = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_POSITION uT_PICK_POSITION = this._P_T_PICK_POSITION.GetModel(PickPositionID);
                if (uT_PICK_POSITION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                #region 清除锁定库存 更新计划数量
                List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(uT_PICK_POSITION.POSITION_CODE).ToList();
                if (PLAN_MAINs.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站绑定的唯一码{0}对应的拣选计划", uT_PICK_POSITION.POSITION_CODE);
                    return bResult;
                }

                foreach (Model.PLAN_MAIN mPLAN_MAIN in PLAN_MAINs)
                {
                    List<Model.PLAN_LIST> PLAN_LISTs = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID).ToList();

                    foreach (Model.PLAN_LIST mPLAN_LIST in PLAN_LISTs)
                    {
                        if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > 0)
                        {
                            bResult = false;
                            sResult = string.Format("该拣选订单已经开始拣选，无法解除锁定");
                            return bResult;
                        }

                        mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = 0;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);

                        List<Model.STORAGE_LOCK> STORAGE_LOCKs = this._P_STORAGE_LOCK.GetList_PLAN_LIST_ID(mPLAN_LIST.PLAN_LIST_ID).ToList();
                        foreach (Model.STORAGE_LOCK mSTORAGE_LOCK in STORAGE_LOCKs)
                        {
                            List<Model.MANAGE_LIST> MANAGE_LISTs = this._P_MANAGE_LIST.GetListBySTORAGE_LOCK_ID(mSTORAGE_LOCK.STORAGE_LOCK_ID).ToList();
                            foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTs)
                            {
                                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mMANAGE_LIST.MANAGE_ID);

                                if (mMANAGE_MAIN != null)
                                {
                                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);
                                    this._P_MANAGE_MAIN.Delete(mMANAGE_LIST.MANAGE_ID);
                                }
                            }
                            this._P_STORAGE_LOCK.Delete(mSTORAGE_LOCK.STORAGE_LOCK_ID);
                        }
                    }
                    mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                    this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                #endregion

                //OrderCode = uT_PICK_STATION.PLAN_GROUP_NAME;
                weiyima = uT_PICK_POSITION.POSITION_CODE;
                uT_PICK_POSITION.POSITION_CODE = string.Empty;
                uT_PICK_POSITION.REMARK = string.Empty;

                this._P_T_PICK_POSITION.Update(uT_PICK_POSITION);

                this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_POSITION_ID(uT_PICK_POSITION.POSITION_ID);

                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(uT_PICK_POSITION.STATION_ID);

                uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                uT_PICK_STATION.FLAG = 0;
                uT_PICK_STATION.PROPERTY3 = "1";
                this._P_T_PICK_STATION.Update(uT_PICK_STATION);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                }
            }
            return bResult;
        }

        /// <summary>
        /// 2024改造
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickPositionID"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool LockAndOutOrderByPickPosition(SYS_USER mSYS_USER, int PickPositionID, string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_POSITION uT_PICK_POSITION = this._P_T_PICK_POSITION.GetModel(PickPositionID);
                if (uT_PICK_POSITION == null)
                {
                    sResult = string.Format("未找到拣选工作工位信息");
                    return bResult;
                }

                bResult = !(uT_PICK_POSITION.POSITION_CODE == weiyima);
                if (bResult)
                {
                    sResult = string.Format("操作失败,{0}信息错误,已绑定订单唯一码{1}与当前唯一码{2}不符",
                        uT_PICK_POSITION.POSITON_NAME,
                        uT_PICK_POSITION.POSITION_CODE,
                        weiyima);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    uT_PICK_POSITION.REMARK = "已绑定订单，已经锁定库存";
                    this._P_T_PICK_POSITION.Update(uT_PICK_POSITION);
                    //锁定
                    bResult = new Plan.PlanPick().PlanLockNew2024(mSYS_USER, uT_PICK_POSITION.POSITION_ID, false, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                    ////下达下架任务
                    //bResult = new Plan.PlanPick().PlanCreateManageOut(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    //if (!bResult)
                    //{
                    //    return bResult;
                    //}

                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }
        /// <summary>
        /// 2024改造
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickPositionID"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool LockAndOutOrderPartlyByPickPosition(SYS_USER mSYS_USER, int PickPositionID, string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_POSITION uT_PICK_POSITION = this._P_T_PICK_POSITION.GetModel(PickPositionID);
                if (uT_PICK_POSITION == null)
                {
                    sResult = string.Format("未找到拣选工作工位信息");
                    return bResult;
                }

                bResult = !(uT_PICK_POSITION.POSITION_CODE == weiyima);
                if (bResult)
                {
                    sResult = string.Format("操作失败,{0}信息错误,已绑定订单唯一码{1}与当前唯一码{2}不符",
                        uT_PICK_POSITION.POSITON_NAME,
                        uT_PICK_POSITION.POSITION_CODE,
                        weiyima);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    uT_PICK_POSITION.REMARK = "已绑定订单，已经锁定库存";
                    this._P_T_PICK_POSITION.Update(uT_PICK_POSITION);
                    //锁定
                    bResult = new Plan.PlanPick().PlanLockPartlyNew2024(mSYS_USER, uT_PICK_POSITION.POSITION_ID, false, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }
                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }

        public bool ManageDownOutByPickPosition(SYS_USER mSYS_USER, int PickPositionID, string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            try
            {
                Model.T_PICK_POSITION uT_PICK_POSITION = this._P_T_PICK_POSITION.GetModel(PickPositionID);
                if (uT_PICK_POSITION == null)
                {
                    sResult = string.Format("未找到拣选工作站工位信息");
                    return bResult;
                }

                bResult = !(uT_PICK_POSITION.POSITION_CODE == weiyima);
                if (bResult)
                {
                    sResult = string.Format("操作失败,{0}信息错误,已绑定订单唯一码{1}与当前唯一码{2}不符",
                        uT_PICK_POSITION.POSITION_CODE,
                        uT_PICK_POSITION.POSITON_NAME,
                        weiyima);
                    return bResult;
                }

                this._P_Base_House.BeginTransaction();

                try
                {
                    uT_PICK_POSITION.REMARK = "已绑定订单，正在出库";
                    this._P_T_PICK_POSITION.Update(uT_PICK_POSITION);
                    ////锁定
                    //bResult = new Plan.PlanPick().PlanLock(mSYS_USER, uT_PICK_STATION.STATION_ID, false, out sResult);

                    //if (!bResult)
                    //{
                    //    return bResult;
                    //}
                    //下达下架任务
                    bResult = new Plan.PlanPick().PlanCreateManageOutByPickPosition(mSYS_USER, uT_PICK_POSITION.POSITION_ID,uT_PICK_POSITION.STATION_ID,false, out sResult);

                    if (!bResult)
                    {
                        return bResult;
                    }

                }
                catch (Exception ex)
                {
                    sResult = ex.Message;
                    bResult = false;

                }
                finally
                {
                    this._P_Base_House.SaveTransaction(bResult);
                }

            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }
            return bResult;
        }

        /// <summary>
        /// 改造2024
        /// </summary>
        /// <param name="mSYS_USER"></param>
        /// <param name="PickPositionID"></param>
        /// <param name="weiyima"></param>
        /// <param name="sResult"></param>
        /// <returns></returns>
        public bool ErrorOperationUnBindOrderPosition(SYS_USER mSYS_USER, int PickPositionID, out string weiyima, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            weiyima = string.Empty;
            try
            {
                this._P_Base_House.BeginTransaction();
                Model.T_PICK_POSITION uT_PICK_POSITION = this._P_T_PICK_POSITION.GetModel(PickPositionID);
                if (uT_PICK_POSITION == null)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站信息");
                    return bResult;
                }

                #region 清除锁定库存 更新计划数量
                List<PLAN_MAIN> PLAN_MAINs = this._P_PLAN_MAIN.GetList_PLAN_GROUP(uT_PICK_POSITION.POSITION_CODE).ToList();
                if (PLAN_MAINs.Count == 0)
                {
                    bResult = false;
                    sResult = string.Format("未找到拣选工作站绑定的唯一码{0}对应的拣选计划", uT_PICK_POSITION.POSITION_CODE);
                    return bResult;
                }

                foreach (Model.PLAN_MAIN mPLAN_MAIN in PLAN_MAINs)
                {
                    List<Model.PLAN_LIST> PLAN_LISTs = this._P_PLAN_LIST.GetListPlanID(mPLAN_MAIN.PLAN_ID).ToList();

                    foreach (Model.PLAN_LIST mPLAN_LIST in PLAN_LISTs)
                    {
                        //if (mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY > 0)
                        //{
                        //    bResult = false;
                        //    sResult = string.Format("该拣选订单已经开始拣选，无法解除锁定");
                        //    return bResult;
                        //}

                        mPLAN_LIST.PLAN_LIST_ORDERED_QUANTITY = mPLAN_LIST.PLAN_LIST_PICKED_QUANTITY;
                        this._P_PLAN_LIST.Update(mPLAN_LIST);

                        List<Model.STORAGE_LOCK> STORAGE_LOCKs = this._P_STORAGE_LOCK.GetList_PLAN_LIST_ID(mPLAN_LIST.PLAN_LIST_ID).ToList();
                        foreach (Model.STORAGE_LOCK mSTORAGE_LOCK in STORAGE_LOCKs)
                        {
                            List<Model.MANAGE_LIST> MANAGE_LISTs = this._P_MANAGE_LIST.GetListBySTORAGE_LOCK_ID(mSTORAGE_LOCK.STORAGE_LOCK_ID).ToList();
                            foreach (Model.MANAGE_LIST mMANAGE_LIST in MANAGE_LISTs)
                            {
                                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(mMANAGE_LIST.MANAGE_ID);

                                if (mMANAGE_MAIN != null)
                                {
                                    this._P_MANAGE_LIST.Delete(mMANAGE_LIST.MANAGE_LIST_ID);
                                    this._P_MANAGE_MAIN.Delete(mMANAGE_LIST.MANAGE_ID);
                                }
                            }
                            this._P_STORAGE_LOCK.Delete(mSTORAGE_LOCK.STORAGE_LOCK_ID);
                        }
                    }


                    //if (mPLAN_MAIN.PLAN_STATUS != Enum.PLAN_STATUS.Finish.ToString())
                    //{
                    //    mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                    //    //this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                    //}
                    ////mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                    //this._P_PLAN_MAIN.Update(mPLAN_MAIN);
                }
                #endregion

                //OrderCode = uT_PICK_STATION.PLAN_GROUP_NAME;
                weiyima = uT_PICK_POSITION.POSITION_CODE;
                uT_PICK_POSITION.POSITION_CODE = string.Empty;
                uT_PICK_POSITION.REMARK = string.Empty;

                this._P_T_PICK_POSITION.Update(uT_PICK_POSITION);

                this._P_T_PICK_POSITION_PLAN_BIND.Delete_By_PICK_POSITION_ID(uT_PICK_POSITION.POSITION_ID);

                Model.T_PICK_STATION uT_PICK_STATION = this._P_T_PICK_STATION.GetModel(uT_PICK_POSITION.STATION_ID);

                uT_PICK_STATION.PLAN_GROUP_CODE = string.Empty;
                uT_PICK_STATION.PLAN_GROUP_NAME = string.Empty;
                uT_PICK_STATION.FLAG = 0;
                uT_PICK_STATION.PROPERTY3 = "1";
                this._P_T_PICK_STATION.Update(uT_PICK_STATION);
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = ex.Message;
                return bResult;
            }

            finally
            {
                if (bResult)
                {
                    this._P_Base_House.CommitTransaction();
                    this._log.Info(string.Format("用户:{0} 拣选工作站POSITOIN_ID:{1} 强断解除唯一码:{2},成功!", mSYS_USER.USER_CODE, PickPositionID, weiyima));
                }
                else
                {
                    this._P_Base_House.RollBackTransaction();
                    this._log.Info(string.Format("用户:{0} 拣选工作站POSITOIN_ID:{1} 强断解除唯一码:{2},失败!,原因:{3}", mSYS_USER.USER_CODE, PickPositionID, weiyima, sResult));
                }
            }
            return bResult;
        }

        #endregion

        #endregion
    }
}
