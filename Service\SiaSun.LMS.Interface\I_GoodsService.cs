﻿using System;
using System.Collections.Generic;

using System.Text;
using System.Data;
using System.ServiceModel;
using SiaSun.LMS.Model;
using System.Drawing;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    public interface I_GoodsService
    {
        [OperationContract]
        SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsID(int GOODS_ID);

        [OperationContract]
        SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCode(string GOODS_CODE);

        [OperationContract]
        SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassID(int GOODS_CLASS_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_MAIN> GoodsGetListGoodsClassID(int GOODS_CLASS_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_PROPERTY> GoodsPropertyGetListGoodsTypeID(int GOODS_TYPE_ID);

        [OperationContract]
        SiaSun.LMS.Model.GOODS_TEMPLATE GoodsTemplateGetModel(int TEMPLATE_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_TEMPLATE> GoodsTemplateGetList(int GOODS_ID);

        [OperationContract]
        IList<SiaSun.LMS.Model.GOODS_TEMPLATE_LIST> GoodsTemplateListGetList(int TEMPLATE_ID);


        [OperationContract]
        SiaSun.LMS.Model.GOODS_MAIN GoodsGetModelGoodsCodeContract(string GOODS_CODE,string CONTRACT);


        [OperationContract]
        SiaSun.LMS.Model.GOODS_CLASS GoodsClassGetModelGoodsClassCode(string GOODS_CLASS_CODE);

        [OperationContract]
        Bitmap GetGoodsPositionTemplateImage(string parmFileUrl);

    }
}
