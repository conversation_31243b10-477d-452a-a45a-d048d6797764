﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Jacky He
 *       日期：     2017/9/19
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using System.Data;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;

    using SiaSun.LMS.Model;

    /// <summary>
    /// T_PICK_POSITION
    /// </summary>
    public class P_T_PICK_POSITION :P_Base_House
    {
        public P_T_PICK_POSITION()
        {
            //
            // TODO: 此处添加T_PICK_POSITION的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<T_PICK_POSITION> GetList()
        {
            return ExecuteQueryForList<T_PICK_POSITION>("T_PICK_POSITION_SELECT", null);
        }

        /// <summary>
        /// 得到数据表
        /// </summary>
        public DataTable GetTable()
        {
            return ExecuteQueryForDataTable("T_PICK_POSITION_SELECT", null);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(T_PICK_POSITION t_pick_position)
        {
            //int id = this.GetId("T_PICK_POSITION", "T_PICK_POSITION_ID");
            //t_pick_position.POSITION_ID = id;

            ExecuteInsert("T_PICK_POSITION_INSERT", t_pick_position);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(T_PICK_POSITION t_pick_position)
        {
            ExecuteUpdate("T_PICK_POSITION_UPDATE", t_pick_position);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION GetModel(System.Int32 POSITION_ID)
        {
            return ExecuteQueryForObject<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_ID", POSITION_ID);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION GetModelByWH_CELL_ID(System.Int32 WH_CELL_ID)
        {
            return ExecuteQueryForObject<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_WH_CELL_ID", WH_CELL_ID);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION GetModel(string DZBQ_MAC)
        {
            return ExecuteQueryForObject<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_DZBQ_MAC", DZBQ_MAC);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public IList<T_PICK_POSITION> GetModelList_BY_STATION_ID(System.Int32 STATION_ID)
        {
            return ExecuteQueryForList<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_STATION_ID", STATION_ID);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public void Delete(System.Int32 POSITION_ID)
        {
            ExecuteDelete("T_PICK_POSITION_DELETE", POSITION_ID);
        }
       
        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION GetModelByPLAN_LIST_ID(int  PLAN_LIST_ID)
        {
            return ExecuteQueryForObject<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_PLAN_LIST_ID", PLAN_LIST_ID);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION GetModelByDZBQ_MAC(string DZBQ_MAC)
        {
            return ExecuteQueryForObject<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_DZBQ_MAC", DZBQ_MAC);
        }

        /// <summary>
        /// 二期改造
        /// 通过dzbq_property5得到实例
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public T_PICK_POSITION GetModelByDZBQ_PROPERTY5(string DZBQ_PROPERTY5)
        {
            return ExecuteQueryForObject<T_PICK_POSITION>("T_PICK_POSITION_SELECT_BY_DZBQ_PROPERTY5", DZBQ_PROPERTY5);
        }
    }
}
