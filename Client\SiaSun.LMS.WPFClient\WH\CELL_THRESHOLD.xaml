﻿<ad:DocumentContent  x:Class="SiaSun.LMS.WPFClient.WH.CELL_THRESHOLD"
      xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
      xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
      xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
      xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
      xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
      xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
      mc:Ignorable="d" 
      d:DesignHeight="300" d:DesignWidth="600"
	Title="CELL_ENABLE_ALERT" Loaded="DocumentContent_Loaded">
    
    <ad:DocumentContent.Resources>
        <ContextMenu x:Key="menuMain">
            <MenuItem Name="menuItemRefresh" Header="刷新"></MenuItem>
        </ContextMenu>
    </ad:DocumentContent.Resources>
    
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
        </Grid.RowDefinitions>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>

        <StackPanel Grid.Row="0" Grid.Column="0" Margin="10" Orientation="Horizontal" ToggleButton.Checked="StackPanel_Checked">
            <RadioButton x:Name="rbtnGroupByArea" Content="按区域统计"  VerticalAlignment="Center" Margin="10"/>
            <RadioButton x:Name="rbtnGroupByLaneWay" Content="按巷道统计"  VerticalAlignment="Center" Margin="10"/>
        </StackPanel>

        <GridSplitter  Grid.Row="1" Grid.Column="0" Height="2" HorizontalAlignment="Stretch" ></GridSplitter>

        <uc:ucCommonDataGrid x:Name="commonDataGrid"  Grid.Row="2" Grid.Column="0" Margin="1"></uc:ucCommonDataGrid>
        
    </Grid>
</ad:DocumentContent >
