﻿using System;
using System.Collections.Generic;
using System.Text;
using SiaSun.LMS.Interface;
using System.Data;
using SiaSun.LMS.Persistence;
using System.ServiceModel;
using System.Reflection;
using System.IO;
using System.Xml;
using System.Linq;
using IBatisNet.Common;
using log4net;
using SiaSun.LMS.Model;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true,
     InstanceContextMode = InstanceContextMode.Single,
     ConcurrencyMode = ConcurrencyMode.Multiple,
     MaxItemsInObjectGraph = int.MaxValue)]
    public partial class S_WESJsonService : S_BaseService, I_WESJsonService
    {
        Interface.InterfaceBase _InterfaceBase = null;

        public S_WESJsonService()
        {
            this._InterfaceBase = new Interface.InterfaceBase();
        }

        #region WES

        /// <summary>
        /// 非立库输送任务接口
        /// </summary>
        /// <param name="jsonstring">Json任务信息</param>
        /// <returns></returns>
        public string NoneMiniloadTrans(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }

        /// <summary>
        /// 组箱入立库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string CreateBoxIn(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }

        /// <summary>
        /// 空箱出库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string EmptyBoxOut(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 空箱入库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string EmptyBoxIn(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 盘点任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string StockCheckTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// Miniload拣选任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string MiniloadSortTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 紧急配料出库接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string EmergSortOutTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 齐套箱出库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string KitBoxOutTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 整理未满箱任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string ArrangeEmptyBoxTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 余箱回库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string BoxReturnInTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 齐套箱出库时余料回库任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string KitBoxReturnInTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        /// <summary>
        /// 物料主数据同步任务下发接口
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string MaterialSyn(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }

        /// <summary>
        /// 取消Miniload拣选任务
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string CancelMiniloadSortTask(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }



        /// <summary>
        /// 取消Miniload拣选任务
        /// 2024改造新增
        /// </summary>
        /// <param name="jsonstring"></param>
        /// <returns></returns>
        public string MiniloadSortTaskCancel(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }
        #endregion

        #region WMS

        /// <summary>
        /// 盘点结果回传接口
        /// </summary>
        public bool countResultReturnFromWCS(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternal(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        }

        /// <summary>
        /// 执行结果回传接口
        /// </summary>
        public bool handleResultReturnFromWCS(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternal(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        }

        /// <summary>
        /// Miniload拣货确认回传接口
        /// </summary>
        public bool miniloadPickConfirmFromWCS(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternal(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        }

        /// <summary>
        /// 齐套箱出库箱号信息回传接口
        /// </summary>
        public bool neatBoxInfoReceiveFromWCS(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternal(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        }

        /// <summary>
        /// 紧急配料出库箱号信息回传接口
        /// </summary>
        public bool urgentBoxReceiveFromWCS(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternal(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        }

        /// <summary>
        /// 非立库输送任务结果回传接口
        /// </summary>
        public bool noneMiniloadTranResultReturnFromWCS(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternal(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        }

        /// <summary>
        /// 非立库输送任务结果回传接口_测试接口
        /// </summary>
        public bool noneMiniloadTranResultReturnFromWCSTest(string inParm, out string outParm)
        {
            return this._InterfaceBase.InvokeExternalTest("noneMiniloadTranResultReturnFromWCS", inParm, out outParm);
        }

        #endregion

        #region WCS

        ///// <summary>
        ///// 调用WCS接口下发Control任务
        ///// wdz add 2018-03-01
        ///// </summary>
        //public bool SendInputTaskByID(int inParm, out string outParm)
        //{
        //    return this._InterfaceBase.InvokeWcs(System.Reflection.MethodBase.GetCurrentMethod().Name, inParm, out outParm);
        //}

        /// <summary>
        /// 一楼齐套出库任务完成
        /// wdz add 2022-09-05
        /// </summary>
        public string KitOutComplete(string jsonstring)
        {
            string outParm = string.Empty;
            this._InterfaceBase.InvokeHouse(System.Reflection.MethodBase.GetCurrentMethod().Name, jsonstring, out outParm);
            return outParm;
        }


        #endregion

    }
}
