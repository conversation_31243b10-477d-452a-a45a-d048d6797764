﻿using Quartz;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.WinService
{
    [DisallowConcurrentExecution]
    public class CaluCellOutScoreJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            string sResult = string.Empty;

            try
            {
                sResult = MainApp.BaseService._S_CellService.CalculateCellOutScore();
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("CaluCellOutScoreJob.Execute:异常", ex);
                throw;
            }
            finally
            {
                if (!string.IsNullOrEmpty(sResult))
                {
                    Program.sysLog.WarnFormat("CaluCellOutScoreJob.Execute:处理申请失败_{0}", sResult);
                }
            }
        }
    }
}
