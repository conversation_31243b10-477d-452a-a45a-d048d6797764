﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003" ToolsVersion="4.0">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProductVersion>9.0.21022</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}</ProjectGuid>
    <OutputType>Library</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>SiaSun.LMS.Model</RootNamespace>
    <AssemblyName>SiaSun.LMS.Model</AssemblyName>
    <FileUpgradeFlags>
    </FileUpgradeFlags>
    <OldToolsVersion>3.5</OldToolsVersion>
    <UpgradeBackupLocation>
    </UpgradeBackupLocation>
    <TargetFrameworkVersion>v4.0</TargetFrameworkVersion>
    <TargetFrameworkProfile />
    <PublishUrl>http://localhost/SiaSun.LMS.Model/</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Web</InstallFrom>
    <UpdateEnabled>true</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ApplicationRevision>0</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <IsWebBootstrapper>true</IsWebBootstrapper>
    <UseApplicationTrust>false</UseApplicationTrust>
    <BootstrapperEnabled>true</BootstrapperEnabled>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <CodeAnalysisRuleSet>AllRules.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core">
      <RequiredTargetFramework>3.5</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Data" />
    <Reference Include="System.Runtime.Serialization">
      <RequiredTargetFramework>3.0</RequiredTargetFramework>
    </Reference>
    <Reference Include="System.Xml" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="APPLY_TYPE.cs" />
    <Compile Include="APPLY_TYPE_PARAM.cs" />
    <Compile Include="Enum.cs" />
    <Compile Include="EnumMessage.cs" />
    <Compile Include="ERP\MID_GOODS.cs" />
    <Compile Include="FIELD_DESCRIPTION.cs" />
    <Compile Include="FLOW_ACTION.cs" />
    <Compile Include="FLOW_NODE.cs" />
    <Compile Include="FLOW_PARA.cs" />
    <Compile Include="FLOW_TYPE.cs" />
    <Compile Include="GOODS_CLASS.cs" />
    <Compile Include="GOODS_MAIN.cs" />
    <Compile Include="ERP\MID_TASK.cs" />
    <Compile Include="GOODS_PROPERTY.cs" />
    <Compile Include="GOODS_TEMPLETE.cs" />
    <Compile Include="GOODS_TEMPLATE_LIST.cs" />
    <Compile Include="GOODS_TYPE.cs" />
    <Compile Include="IO_CONTROL_APPLY.cs" />
    <Compile Include="IO_CONTROL.cs" />
    <Compile Include="IO_CONTROL_APPLY_HIS.cs" />
    <Compile Include="IO_CONTROL_ROUTE.cs" />
    <Compile Include="LCD_MAIN.cs" />
    <Compile Include="LED_LIST.cs" />
    <Compile Include="LED_MAIN.cs" />
    <Compile Include="Log.cs" />
    <Compile Include="MANAGE_ACTION_EXCUTE.cs" />
    <Compile Include="MANAGE_DETAIL.cs" />
    <Compile Include="MANAGE_LIST.cs" />
    <Compile Include="MANAGE_MAIN.cs" />
    <Compile Include="MANAGE_TYPE.cs" />
    <Compile Include="EnumMessageConverter.cs" />
    <Compile Include="MANAGE_TYPE_PARAM.cs" />
    <Compile Include="PLAN_ACTION_EXCUTE.cs" />
    <Compile Include="PLAN_DETAIL.cs" />
    <Compile Include="PLAN_LIST.cs" />
    <Compile Include="PLAN_MAIN.cs" />
    <Compile Include="PLAN_TYPE.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="QueryObject.cs" />
    <Compile Include="RECORD_DETAIL.cs" />
    <Compile Include="RECORD_LIST.cs" />
    <Compile Include="RECORD_MAIN.cs" />
    <Compile Include="RequestData.cs" />
    <Compile Include="ServiceMessage.cs" />
    <Compile Include="ServiceResponse.cs" />
    <Compile Include="STORAGE_DETAIL.cs" />
    <Compile Include="STORAGE_LIST.cs" />
    <Compile Include="STORAGE_LOCK.cs" />
    <Compile Include="STORAGE_MAIN.cs" />
    <Compile Include="SYS_ITEM.cs" />
    <Compile Include="SYS_ITEM_LIST.cs" />
    <Compile Include="MANAGE_OUT_LOG.cs" />
    <Compile Include="SYS_LOG.cs" />
    <Compile Include="SYS_MENU.cs" />
    <Compile Include="SYS_RELATION.cs" />
    <Compile Include="SYS_RELATION_LIST.cs" />
    <Compile Include="SYS_ROLE.cs" />
    <Compile Include="SYS_ROLE_WINDOW.cs" />
    <Compile Include="SYS_TABLE_CONVERTER_LIST.cs" />
    <Compile Include="SYS_USER.cs" />
    <Compile Include="TECHNICS_MAIN.cs" />
    <Compile Include="TECHNICS_ROUTE.cs" />
    <Compile Include="T_PICK_POSITION.cs" />
    <Compile Include="T_PICK_POSITION_PLAN_BIND.cs" />
    <Compile Include="T_PICK_STATION.cs" />
    <Compile Include="WDZ\Enum.cs" />
    <Compile Include="WESJson.cs" />
    <Compile Include="WH_CELL_GROUP.cs" />
    <Compile Include="WH_AREA.cs" />
    <Compile Include="WH_CELL.cs" />
    <Compile Include="WH_DESCRIPTION.cs" />
    <Compile Include="WH_LOGIC.cs" />
    <Compile Include="WH_WAREHOUSE.cs" />
    <Compile Include="SYS_TABLE_CONVERTER.cs" />
    <Compile Include="RequestDataList.cs" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include="Microsoft.Net.Client.3.5">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1 Client Profile</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Windows.Installer.3.1">
      <Visible>False</Visible>
      <ProductName>Windows Installer 3.1</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildBinPath)\Microsoft.CSharp.targets" />
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it. 
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>