﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// FLOW_NODE 
	/// </summary>
    [Serializable]
    [DataContract]
	public class FLOW_NODE
	{
		public FLOW_NODE()
		{
			
		}
		
		private int _flow_node_id;
		private int _flow_type_id;
		private string _flow_node_code;
		private string _flow_node_name;
		private string _flow_node_remark;
		private int _flow_node_order;
		private string _flow_node_flag;
		
		///<sumary>
		/// 流程节点编号
        ///</sumary>
        [DataMember]
		public int FLOW_NODE_ID
		{
			get{return _flow_node_id;}
			set{_flow_node_id = value;}
		}
		///<sumary>
		/// 流程类型编号
        ///</sumary>
        [DataMember]
		public int FLOW_TYPE_ID
		{
			get{return _flow_type_id;}
			set{_flow_type_id = value;}
		}
		///<sumary>
		/// 编码
        ///</sumary>
        [DataMember]
		public string FLOW_NODE_CODE
		{
			get{return _flow_node_code;}
			set{_flow_node_code = value;}
		}
		///<sumary>
		/// 名称
        ///</sumary>
        [DataMember]
		public string FLOW_NODE_NAME
		{
			get{return _flow_node_name;}
			set{_flow_node_name = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string FLOW_NODE_REMARK
		{
			get{return _flow_node_remark;}
			set{_flow_node_remark = value;}
		}
		///<sumary>
		/// 排序
        ///</sumary>
        [DataMember]
		public int FLOW_NODE_ORDER
		{
			get{return _flow_node_order;}
			set{_flow_node_order = value;}
		}
		///<sumary>
		/// 标记
        ///</sumary>
        [DataMember]
		public string FLOW_NODE_FLAG
		{
			get{return _flow_node_flag;}
			set{_flow_node_flag = value;}
		}
	}
}
