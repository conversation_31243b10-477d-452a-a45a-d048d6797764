﻿<ad:DocumentContent x:Class="SiaSun.LMS.WPFClient.Dialog.DateSectSplitQueryWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
        Title="DateBetweenSplitQueryWindow" Height="300" Width="472" Loaded="DocumentContent_Loaded">
    <!--<Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition MaxWidth="200"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        
    
        <GridSplitter Width="2" VerticalAlignment="Stretch" Grid.Column="1"></GridSplitter>-->

    <Grid Grid.Column="2">
            <Grid.RowDefinitions>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="Auto"></RowDefinition>
                <RowDefinition Height="*"></RowDefinition>
            </Grid.RowDefinitions>
            <Border Name="panelDateTime" Grid.Row="0">
                <WrapPanel VerticalAlignment="Center" MinWidth="120">
                    <CheckBox Name="chkboxDate" IsChecked="True" VerticalAlignment="Center" Margin="5,5,0,5"></CheckBox>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="从" VerticalAlignment="Center" Margin="5"></TextBlock>
                        <DatePicker Name="dtpStart" Margin="0,5,0,5" Width="100"></DatePicker>
                    </StackPanel>
                    <StackPanel Orientation="Horizontal">
                        <TextBlock Text="至" VerticalAlignment="Center" Margin="5"></TextBlock>
                        <DatePicker Name="dtpEnd" Margin="0,5,0,5" Width="100"></DatePicker>
                    </StackPanel>
                </WrapPanel>
            </Border>
            <uc:ucQuickQuery x:Name="ucQuery" Grid.Row="1" ></uc:ucQuickQuery>
            <uc:ucSplitPropertyPanel x:Name="ucSplitPanel" Grid.Row="2"></uc:ucSplitPropertyPanel>

        <!--<WrapPanel>-->
        <uc:ucSplitPropertyGridTab x:Name="ucSplitGrid" Grid.Row="3"></uc:ucSplitPropertyGridTab>
        <!--</WrapPanel>-->
        <!--<Grid Grid.Row="0">
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                
            </Grid>-->
            
        </Grid>
    <!--</Grid>-->
</ad:DocumentContent>
