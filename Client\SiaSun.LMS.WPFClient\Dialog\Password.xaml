﻿<Window x:Class="SiaSun.LMS.WPFClient.Dialog.Password"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
        Title="修改密码"  Height="230" Width="345" WindowStyle="None" WindowStartupLocation="CenterScreen" Loaded="Window_Loaded" SizeToContent="Height">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        <uc:ucWindowTopHeader Grid.Row="0"></uc:ucWindowTopHeader>
        <Border Grid.Row="1"  CornerRadius="0" BorderThickness="0">
            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="50"></ColumnDefinition>
                    <ColumnDefinition Width="Auto"></ColumnDefinition>
                    <ColumnDefinition Width="*"></ColumnDefinition>
                </Grid.ColumnDefinitions>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Image Grid.Column="0" Grid.Row="1" Grid.RowSpan="2" Source="/@Images/role.png"></Image>

                <TextBlock Grid.Column="1" Grid.Row="0" VerticalAlignment="Center" Margin="5,10,5,2">用户名：</TextBlock>
                <TextBox Grid.Column="2" Grid.Row="0" Name="txtUserCode" TabIndex="0" Width="179" VerticalAlignment="Center" Margin="2,10,20,2"></TextBox>
                <TextBlock Grid.Column="1" Grid.Row="1" VerticalAlignment="Center"  Margin="5,5,5,2">旧密码：</TextBlock>
                <PasswordBox Grid.Column="2" VerticalAlignment="Center" Grid.Row="1" Name="txtOldPwd"  Width="179" PasswordChar="*" Margin="2,2,20,3" />
                <TextBlock Grid.Column="1" Grid.Row="2" VerticalAlignment="Center" Margin="5,5,5,2">新密码：</TextBlock>
                <PasswordBox Grid.Column="2" VerticalAlignment="Center" Grid.Row="2" Name="txtNewPwd"  Width="179" PasswordChar="*" Margin="2,2,20,3" />
                <TextBlock Grid.Column="1" Grid.Row="3" VerticalAlignment="Center" Margin="5,5,5,10">确认密码：</TextBlock>
                <PasswordBox Grid.Column="2" VerticalAlignment="Center" Grid.Row="3" Name="txtConfirmPwd"  Width="179" PasswordChar="*" Margin="2,2,20,3" />
                
            </Grid>   
        </Border>
        
        <Border Grid.Row="2"  CornerRadius="0" BorderThickness="0">
            <WrapPanel HorizontalAlignment="Center" ButtonBase.Click="WrapPanel_Click">
                <Button Name="btnOK" Width="70" Margin="5">确定</Button>
                <Button Name="btnCancel" Width="70" Margin="5">取消</Button>
            </WrapPanel>            
        </Border>

    </Grid>
</Window>
