﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace SSLMS.MobileUI.uc
{
    public partial class ucSTOCK_OUT : ucBase
    {
        public ucSTOCK_OUT()
        {
            this.InitializeComponent();

            this.cbWAREHOUSE_Bind();

            this.cbSTOCK_Bind();
        }

        private void cbWAREHOUSE_Bind()
        {
            try
            {
                cbWAREHOUSE.ValueMember = "WAREHOUSE_ID";

                cbWAREHOUSE.DisplayMember = "WAREHOUSE_NAME";

                cbWAREHOUSE.DataSource = this._i_st.WAREHOUSE_GetList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！\n" + ex.Message);
            }
        }

        private void cbWAREHOUSE_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.cbWAREHOUSE.SelectedValue != null)
            {
                string WAREHOUSE_ID = Convert.ToString(this.cbWAREHOUSE.SelectedValue);

                this.cbAREA_Bind(WAREHOUSE_ID);

                this.cbLANYWAY_Bind(WAREHOUSE_ID);
            }
        }

        private void cbAREA_Bind(string WAREHOUSE_ID)
        {
            try
            {
                cbAREA.ValueMember = "AREA_ID";

                cbAREA.DisplayMember = "AREA_NAME";

                cbAREA.DataSource = this._i_st.AREA_GetList(WAREHOUSE_ID);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！\n" + ex.Message);
            }
        }

        private void cbSTOCK_Bind()
        {
            try
            {
                this.cbSTOCK.ValueMember = "GOODS_ID";

                this.cbSTOCK.DisplayMember = "GOODS_NAME";

                this.cbSTOCK.DataSource = this._i_st.GOODS_GetList(this.cbAREA.SelectedValue.ToString(), "2");
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！\n" + ex.Message);
            }
        }

        private void cbLANYWAY_Bind(string WAREHOUSE_ID)
        {
            try
            {
                this.cbLANEWAY.ValueMember = "DEVICE_CODE";

                this.cbLANEWAY.DisplayMember = "LANEWAY";

                Hashtable ht = new Hashtable();

                ht.Add("DEVICE_CODE", 0);

                ht.Add("LANEWAY", "-");

                this.cbLANEWAY.DataSource = SSLMS.MobileUI.Common.AddAll(this._i_st.LANEWAY_GetList(WAREHOUSE_ID), ht);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！\n" + ex.Message);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                if (this.cbSTOCK.SelectedValue.ToString().Equals("0"))
                {
                    MessageBox.Show("请选择搬运容器！");

                    return;
                }

                if (string.IsNullOrEmpty(this.txtCELL.Text))
                {
                    MessageBox.Show("请选择出库站台!");

                    return;
                }

                ST_CELL mST_CELL_END = this._i_st.CELL_GetModel_1(this.txtCELL.Text.Trim());

                if (null == mST_CELL_END)
                {
                    MessageBox.Show("请选择正确的出库站台!");

                    return;
                }

                if(DialogResult.OK != MessageBox.Show("确认?","提示" ,MessageBoxButtons.OKCancel,0,MessageBoxDefaultButton.Button1))
                {
                    return;
                }
                bool
                    bResult = true;

                string sResult = string.Empty;

                string sqlStockOut = @"SELECT  TOP {0} * FROM V_ST_STORAGE_LIST  WHERE  1=1 {1} {2} order by area_id desc, entry_time";

                DataTable dtStockOut = null;

                string sComon = string.Format(" AND AREA_ID = {0} ", this.cbAREA.SelectedValue.ToString()) + string.Format(" AND RUN_STATUS='{0}' AND CELL_STATUS = '{1}' AND CELL_TYPE= '{2}' AND STORAGE_LIST_TYPE = 'none'", SSLMS.Model.RUN_STATUS.enable.ToString(), SSLMS.Model.CELL_STATUS.have.ToString(), SSLMS.Model.CELL_TYPE.cell.ToString());

                string routerpath = string.Format("AND DEVICE_CODE IN (SELECT START_DEVICE FROM IO_CONTROL_ROUTE  WHERE /*START_DEVICE = '{0}' AND*/ END_DEVICE = '{1}' AND CONTROL_ROUTE_STATUS = 1 and CONTROL_ROUTE_TYPE =2 )", this.cbLANEWAY.SelectedValue.ToString(), null != mST_CELL_END ? mST_CELL_END._cell_code : string.Empty);

                sqlStockOut = string.Format(sqlStockOut, this.txtMANAGER_LIST_QUANTITY.Value.ToString(), sComon, routerpath);

                if (this.cbLANEWAY.SelectedValue.ToString() != "0")
                {
                    sqlStockOut = sqlStockOut.Replace("/*", "").Replace("*/", "");
                }

               dtStockOut = this._i_base.GetList(sqlStockOut);

                if (dtStockOut.Rows.Count == 0)
                {
                    MessageBox.Show("搬运容器出库失败！\n请检查库内是否有搬运容器或设备是否可用！");

                    return;
                }

                int START_CELL_ID = 0;

                foreach (DataRow rowSTART_CELL_ID in dtStockOut.Rows)
                {
                    int MANAGE_ID = 0;

                    START_CELL_ID = Convert.ToInt32(rowSTART_CELL_ID["CELL_ID"]);

                    ST_CELL mST_CELL_START = this._i_st.CELL_GetModel(START_CELL_ID);

                    if (null == mST_CELL_START || !SSLMS.Model.RUN_STATUS.enable.ToString().Equals(mST_CELL_START._run_status))
                    {
                        continue;
                    }

                    if (!this._i_io.MANAGE_Create_1(this._USER, START_CELL_ID, mST_CELL_END._cell_id, out sResult, out MANAGE_ID))
                    {
                        continue;
                    }

                    bResult = this._i_io.MANAGE_SendControl(MANAGE_ID, out sResult);

                    if (!bResult)
                    {
                        this._i_io.MANAGE_Cancel(MANAGE_ID, out sResult);
                    }
                }

                MessageBox.Show("任务下达成功！");
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常！\n" + ex.Message);
            }
        }

        private void ucSTOCK_OUT_Resize(object sender, EventArgs e)
        {
            this.lbWAREHOUSE.Width = (int)(this.Width * 0.2);

            this.lbWAREHOUSE.Height = (int)(this.Height * 0.1);

            this.lbWAREHOUSE.Top = 0;

            this.lbWAREHOUSE.Left = 0;


            this.cbWAREHOUSE.Width = (int)(this.Width * 0.75);

            this.cbWAREHOUSE.Height = (int)(this.Height * 0.1);

            this.cbWAREHOUSE.Top = 0;

            this.cbWAREHOUSE.Left = this.lbWAREHOUSE.Right;


            this.lbAREA.Width = (int)(this.Width * 0.2);

            this.lbAREA.Height = (int)(this.Height * 0.1);

            this.lbAREA.Top = this.lbWAREHOUSE.Bottom;

            this.lbAREA.Left = 0;


            this.cbAREA.Width = (int)(this.Width * 0.75);

            this.cbAREA.Height = (int)(this.Height * 0.1);

            this.cbAREA.Top = this.lbWAREHOUSE.Bottom;

            this.cbAREA.Left = this.lbAREA.Right;


            this.lbLANEWAY.Width = (int)(this.Width * 0.2);

            this.lbLANEWAY.Height = (int)(this.Height * 0.1);

            this.lbLANEWAY.Top = this.lbAREA.Bottom;

            this.lbLANEWAY.Left = 0;


            this.cbLANEWAY.Width = (int)(this.Width * 0.75);

            this.cbLANEWAY.Height = (int)(this.Height * 0.1);

            this.cbLANEWAY.Top = this.lbAREA.Bottom;

            this.cbLANEWAY.Left = this.lbLANEWAY.Right;


            this.lbSTOCK.Width = (int)(this.Width * 0.2);

            this.lbSTOCK.Height = (int)(this.Height * 0.1);

            this.lbSTOCK.Top = this.lbLANEWAY.Bottom;

            this.lbSTOCK.Left = 0;


            this.cbSTOCK.Width = (int)(this.Width * 0.75);

            this.cbSTOCK.Height = (int)(this.Height * 0.1);

            this.cbSTOCK.Top = this.lbLANEWAY.Bottom;

            this.cbSTOCK.Left = this.lbSTOCK.Right;


            this.lbMANAGER_LIST_QUANTITY.Width = (int)(this.Width * 0.2);

            this.lbMANAGER_LIST_QUANTITY.Height = (int)(this.Height * 0.1);

            this.lbMANAGER_LIST_QUANTITY.Top = this.lbSTOCK.Bottom;

            this.lbMANAGER_LIST_QUANTITY.Left = 0;


            this.txtMANAGER_LIST_QUANTITY.Width = (int)(this.Width * 0.75) - 1;

            this.txtMANAGER_LIST_QUANTITY.Height = (int)(this.Height * 0.1);

            this.txtMANAGER_LIST_QUANTITY.Top = this.lbSTOCK.Bottom;

            this.txtMANAGER_LIST_QUANTITY.Left = this.lbMANAGER_LIST_QUANTITY.Right;


            this.lbCELL.Width = (int)(this.Width * 0.2);

            this.lbCELL.Height = (int)(this.Height * 0.1);

            this.lbCELL.Top = this.lbMANAGER_LIST_QUANTITY.Bottom;

            this.lbCELL.Left = 0;


            this.txtCELL.Width = (int)(this.Width * 0.75);

            this.txtCELL.Height = (int)(this.Height * 0.1);

            this.txtCELL.Top = this.lbMANAGER_LIST_QUANTITY.Bottom;

            this.txtCELL.Left = this.lbCELL.Right;


            this.btnOK.Width = (int)(this.Width * 0.75);

            this.btnOK.Height = (int)(this.Height * 0.1);

            this.btnOK.Left = this.lbCELL.Right;

            this.btnOK.Top = this.txtCELL.Bottom;
        }

        public override void OnRead(object sender, Symbol.Barcode.ReaderData readerData)
        {
            if (this.txtCELL.Focused)
            {
                this.txtCELL.Text = readerData.Text;
            }
        }
    }
}
