﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 空箱出库
    /// </summary>
    public class EmptyBoxOut : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _quantity = string.Empty;                      //数量
            private string _boxType = string.Empty;                       //箱子类型
            private string _toPosition = string.Empty;                    //终点位置
            private string _uniqueCode = string.Empty;                    //唯一码
            private string _interfaceType = string.Empty;                 //接口类型
            private string _interfaceSource = string.Empty;               //接口来源

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 终点位置
            /// </summary>
            public string toPosition
            {
                get { return _toPosition; }
                set { _toPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();

            int iPlanId = 0;

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxOut.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.quantity) || string.IsNullOrEmpty(taskInfo.boxType) || string.IsNullOrEmpty(taskInfo.toPosition) || string.IsNullOrEmpty(taskInfo.uniqueCode))
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxOut.NotifyMethod:入参存在空值");
                    return bResult;
                }

                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(taskInfo.toPosition);
                if (mWH_CELL_END == null)
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxOut.NotifyMethod:入参 终点位置有误 传入值_{0}", taskInfo.toPosition);
                    return bResult;
                }

                double quantity = 0;
                if(!double.TryParse(taskInfo.quantity,out quantity))
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxOut.NotifyMethod:入参 数量有误 传入值_{0}", taskInfo.quantity);
                    return bResult;
                }

                Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModelPlanCode(taskInfo.uniqueCode,string.Empty, string.Empty);
                if (mPLAN_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("EmptyBoxOut.NotifyMethod:传入唯一码已存在计划 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                mPLAN_MAIN = new Model.PLAN_MAIN();
                mPLAN_MAIN.PLAN_CODE = taskInfo.uniqueCode;
                mPLAN_MAIN.PLAN_CREATER = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mPLAN_MAIN.PLAN_CREATE_TIME = Common.StringUtil.GetDateTime();
                mPLAN_MAIN.PLAN_FLAG = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mPLAN_MAIN.PLAN_INOUT_STATION = mWH_CELL_END.CELL_CODE;
                mPLAN_MAIN.PLAN_STATUS = Enum.PLAN_STATUS.Waiting.ToString();
                mPLAN_MAIN.PLAN_TYPE_CODE = Enum.PLAN_TYPE_CODE.PlanOutEmptyBox.ToString();
                string planLevel = string.Empty;
                mPLAN_MAIN.PLAN_LEVEL = this._S_SystemService.GetSysParameter("EmptyBoxOutPlanLevel", out planLevel) ? planLevel : "0";

                Model.PLAN_LIST mPLAN_LIST = new Model.PLAN_LIST();
                mPLAN_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID;
                //空箱出库时忽略WMS提供的箱型 限定为0
                //mPLAN_LIST.GOODS_PROPERTY1 = taskInfo.boxType;
                mPLAN_LIST.GOODS_PROPERTY1 = "0";
                mPLAN_LIST.GOODS_PROPERTY2 = "";//颜色待指定
                mPLAN_LIST.PLAN_LIST_QUANTITY = Convert.ToDecimal(quantity);

                object[] invokeOutParams = new object[] { };
                bResult = this.Invoke("PlanBase", "PlanCreate", new object[] {mPLAN_MAIN,new List<Model.PLAN_LIST>() { mPLAN_LIST}, iPlanId, outJson },out invokeOutParams);
                int.TryParse(invokeOutParams[2].ToString(), out iPlanId);
                if (!bResult)
                {
                    outJson = invokeOutParams[3].ToString();
                    return bResult;
                }

                bResult = this.Invoke("PlanBase", "PlanOutDownLoad", new object[] { iPlanId },out outJson);
                if(!bResult)
                {
                    string strTemp = string.Empty;
                    if (!this.Invoke("PlanBase", "PlanCancel", new object[] { iPlanId ,false}, out strTemp))
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("EmptyBoxOut.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划失败_{1}_计划ID[{2}]", outJson, strTemp, iPlanId));
                    }
                    else
                    {
                        //log
                        this.CreateSysLog(Enum.LogThread.Plan, "System", false, string.Format("EmptyBoxOut.NotifyMethod():计划生成成功_执行计划失败_{0}_删除计划成功_计划ID[{1}]", outJson, iPlanId));
                    }

                    outJson = string.Format("EmptyBoxOut.NotifyMethod:执行计划失败,{0}", outJson);
                    return bResult;
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("EmptyBoxOut.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
