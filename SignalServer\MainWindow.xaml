﻿<Window x:Class="SignalServer.MainWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
        xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
        xmlns:local="clr-namespace:SignalServer"
        xmlns:models="clr-namespace:SignalServer.Models"
        xmlns:materialDesign="http://materialdesigninxaml.net/winfx/xaml/themes"
        WindowStartupLocation="CenterScreen"
        WindowStyle="None"
        AllowsTransparency="True"
        BorderBrush="{x:Null}"
        ResizeMode="NoResize"
        mc:Ignorable="d"
        Background="{StaticResource MaterialDesignBackground}"
        Closing="Window_Closing"
        Title="拣选工作站订单推送服务" Height="400" Width="750" Icon="Resources/Pics/server_red96.ico">
    <Grid>
        <materialDesign:DialogHost
            DialogClosing="Sample1_DialogHost_OnDialogClosing"
            DialogTheme="Inherit"
            HorizontalAlignment="Stretch"
            VerticalAlignment="Stretch">
            <materialDesign:DialogHost.DialogContent>
                <StackPanel  Margin="16">
                    <TextBlock>
                        请输入关闭口令：Pa$Sw0rd!`/*^
                    </TextBlock>
                    <TextBox
                        Margin="0 8 0 0"
                        HorizontalAlignment="Stretch"
                        x:Name="CloseWindowPasswordTextBox" />
                    <StackPanel
                        Orientation="Horizontal"
                        HorizontalAlignment="Right">
                        <Button
                            Style="{StaticResource MaterialDesignFlatButton}"
                            IsDefault="True"
                            Margin="0 8 8 0"
                            Command="materialDesign:DialogHost.CloseDialogCommand">
                            <Button.CommandParameter>
                                <system:Boolean xmlns:system="clr-namespace:System;assembly=mscorlib">
                                    True
                                </system:Boolean>
                            </Button.CommandParameter>
                            确定
                        </Button>
                        <Button
                            Style="{StaticResource MaterialDesignFlatButton}"
                            IsCancel="True"
                            Margin="0 8 8 0"
                            Command="materialDesign:DialogHost.CloseDialogCommand">
                            <Button.CommandParameter>
                                <system:Boolean xmlns:system="clr-namespace:System;assembly=mscorlib">
                                    False
                                </system:Boolean>
                            </Button.CommandParameter>
                            取消
                        </Button>
                    </StackPanel>
                </StackPanel>
            </materialDesign:DialogHost.DialogContent>

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"></RowDefinition>
                    <RowDefinition Height="*"></RowDefinition>
                </Grid.RowDefinitions>
                <materialDesign:ColorZone Mode="PrimaryMid"
                                  Padding="5" MouseDown="ColorZone_MouseDown">
                    <Grid Height="50" x:Name="HeaderBarGrid">
                        <TextBlock Text="不要关闭此程序" VerticalAlignment="Center"
                                   HorizontalAlignment="Center" 
                           Style="{StaticResource MaterialDesignHeadline5TextBlock}"></TextBlock>

                        <StackPanel Orientation="Horizontal" VerticalAlignment="Center"
                        HorizontalAlignment="Right">
                            <!--<materialDesign:PopupBox HorizontalAlignment="Right" 
                                     VerticalAlignment="Stretch"
                                     Margin="5"
                                     PlacementMode="BottomAndAlignRightEdges"
                                     StaysOpen="False">
                                <StackPanel>
                                    <Button Content="Account"></Button>
                                    <Button Content="Settings"></Button>
                                    <Button Content="Help"></Button>
                                    <Separator></Separator>
                                    <Button x:Name="Logout" Content="Logout" Click="Logout_Click"></Button>
                                </StackPanel>


                            </materialDesign:PopupBox>-->
                            <Button
                                        Style="{StaticResource MaterialDesignIconForegroundButton}"
                                        Command="{x:Static materialDesign:DialogHost.OpenDialogCommand}">
                                <materialDesign:PackIcon
                                    Kind="Shutdown"
                                    Height="22"
                                    Width="22" />
                            </Button>
                        </StackPanel>

                    </Grid>
                </materialDesign:ColorZone>


                <Grid Grid.Row="1">
                    <Grid>
                        <Grid.ColumnDefinitions>
                            <ColumnDefinition Width="Auto"></ColumnDefinition>
                            <ColumnDefinition Width="*"></ColumnDefinition>
                        </Grid.ColumnDefinitions>
                        <ItemsControl Grid.Column="0" Width="200"
                            Name="myListBox" ItemsSource="{Binding Clients}" 
                            Grid.IsSharedSizeScope="True">
                            <ItemsControl.ItemTemplate>
                                <DataTemplate DataType="{x:Type models:Client}">
                                    <Border x:Name="Border" Padding="8" 
                                            BorderBrush="{StaticResource MaterialDesignDivider}"
                                            BorderThickness="2"
                                            CornerRadius="10">
                                        <Grid>
                                            <Grid.ColumnDefinitions>
                                                <ColumnDefinition SharedSizeGroup="Checkerz" />
                                                <ColumnDefinition />
                                            </Grid.ColumnDefinitions>
                                            <ToggleButton                                        
                                                Style="{StaticResource MaterialDesignActionToggleButton}"
                                                IsChecked="{Binding IsLogin}"
                                                >
                                                <ToggleButton.Content>
                                                    <materialDesign:PackIcon Kind="LanDisconnect" 
                                                                             Foreground="Yellow"/>
                                                </ToggleButton.Content>
                                                <materialDesign:ToggleButtonAssist.OnContent>
                                                    <materialDesign:PackIcon Kind="LanConnect"
                                                                             Foreground="{StaticResource SecondaryHueMidBrush}"/>
                                                </materialDesign:ToggleButtonAssist.OnContent>
                                            </ToggleButton>
                                            <StackPanel Margin="8 0 0 0" Grid.Column="1">
                                                <TextBlock FontWeight="Bold" FontSize="16"
                                                       Text="{Binding PickStationName}" 
                                                       Foreground="{StaticResource PrimaryHueDarkForegroundBrush}"/>
                                                <TextBlock Text="{Binding LoginUserName}" FontSize="12"
                                                       Foreground="{StaticResource PrimaryHueDarkForegroundBrush}"/>
                                            </StackPanel>
                                        </Grid>
                                    </Border>
                                    <DataTemplate.Triggers>
                                        <DataTrigger Binding="{Binding Status}" Value="Offline">
                                            <Setter
                                            TargetName="Border"
                                            Property="Background"
                                            Value="{DynamicResource PrimaryHueDarkBrush}" />
                                        </DataTrigger>
                                        <DataTrigger Binding="{Binding Status}" Value="Online">
                                            <Setter
                                            TargetName="Border"
                                            Property="Background"
                                            Value="{DynamicResource SecondaryHueMidBrush}" />
                                        </DataTrigger>
                                    </DataTemplate.Triggers>
                                </DataTemplate>
                            </ItemsControl.ItemTemplate>
                        </ItemsControl>

                        <Grid Grid.Column="1">
                            <Grid.RowDefinitions>
                                <RowDefinition Height="Auto"></RowDefinition>
                                <RowDefinition Height="Auto"></RowDefinition>
                                <RowDefinition Height="*"></RowDefinition>
                            </Grid.RowDefinitions>

                            <WrapPanel Orientation="Horizontal" Margin="10">
                                <!--<ToggleButton Style="{StaticResource MaterialDesignSwitchToggleButton}"
                                      ToolTip="开关服务状态" IsChecked="{Binding IsServerAlive}"
                                              Checked="ToggleButton_Checked"
                                             Margin="0,0,5,0">
                                    <materialDesign:ToggleButtonAssist.SwitchTrackOnBackground>
                                        <SolidColorBrush Color="Green" />
                                    </materialDesign:ToggleButtonAssist.SwitchTrackOnBackground>
                                    <materialDesign:ToggleButtonAssist.SwitchTrackOffBackground>
                                        <SolidColorBrush Color="Red" />
                                    </materialDesign:ToggleButtonAssist.SwitchTrackOffBackground>
                                </ToggleButton>-->

                                <!--<Button
                                            materialDesign:ButtonProgressAssist.Value="-1"
                                            materialDesign:ButtonProgressAssist.IsIndicatorVisible="{Binding IsServerAlive}"
                                            materialDesign:ButtonProgressAssist.IsIndeterminate="{Binding IsServerAlive}"
                                            Content="服务端状态"
                                            Margin="2,0" />-->

                                <Button Name="OpenSever" Style="{StaticResource MaterialDesignRaisedButton}"
                                        Margin="5"
                                        Height="30" 
                                        Content="开启服务"
                                        materialDesign:ButtonProgressAssist.Value="-1"
                                        materialDesign:ButtonProgressAssist.IsIndicatorVisible="True"
                                        materialDesign:ButtonProgressAssist.IsIndeterminate="{Binding IsServerAlive}"
                                    Click="OpenSever_Click"></Button>

                                <Button Name="CloseServer" 
                                        Margin="5" 
                                        Height="30" 
                                        Content="关闭服务"
                                        Style="{StaticResource MaterialDesignOutlinedButton}"
                                        Click="Close_Click"></Button>

                                <Button Name="Boardcast" Content="测试广播"
                                    Height="30" Width="100"
                                    Margin="5"
                                    Style="{StaticResource MaterialDesignRaisedAccentButton}"
                                    Click="Boardcast_Click"></Button>

                                <TextBox Name="TargetPKCodeTextBox"
                                         materialDesign:HintAssist.HelperText="PK1,PK2..."
                                         Margin="5"
                                         Height="30"
                                         Width="150"></TextBox>
                            </WrapPanel>


                            <StackPanel Orientation="Vertical" Grid.Row="1" Margin="10,10,10,10">
                                <ProgressBar IsIndeterminate="{Binding IsServerAlive}"
                                             ToolTip="光标波动代表服务运行中" />
                            </StackPanel>
                            <RichTextBox Name="InfoToShow" Grid.Row="2"
                                         Margin="10,2,10,20" Padding="2"
                                         >

                            </RichTextBox>
                        </Grid>
                    </Grid>


                </Grid>
            </Grid>
        </materialDesign:DialogHost>
    </Grid>
</Window>
