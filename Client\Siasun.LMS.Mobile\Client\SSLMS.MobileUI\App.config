﻿<?xml version="1.0" encoding="utf-8" ?>
<Fields>
  <Table Name="SiaSunSrvUrl">
    <Field Name="http://192.168.3.105:8001/Service"></Field>
  </Table>
  <Table Name="PLAN_MAIN">
    <Field Column="PLAN_CODE" dbType="string" Header="单号" FieldType="text"  key="" Remark="" index ="10">
    </Field>
    <Field Column="PLAN_CREATE_TIME" dbType="string" Header="制单时间" FieldType="text" key=""  Remark="" index ="30">
    </Field>
    <Field Column="PLAN_BEGIN_TIME" dbType="string" Header="开始时间" FieldType="text" key=""  Remark="" index ="40">
    </Field>
    <Field Column="PLAN_END_TIME" dbType="string" Header="完成时间" FieldType="text" key=""  Remark="" index ="50">
    </Field>
    <Field Column="PLAN_OPERATOR" dbType="string" Header="操作员" FieldType="text" key=""  Remark="" index ="60">
    </Field>
    <Field Column="PLAN_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="70">
    </Field>
  </Table>


  <Table Name="PLAN_LIST">
    <Field Column="GOODS_CODE" dbType="string" Header="物料编码" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="物料名称" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="GOODS_INFORMATION" dbType="string" Header="物料规格" FieldType="text" key="" Remark="" index ="21">
    </Field>
    <Field Column="PLAN_LIST_QUANTITY" dbType="decimal" Header="数量" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="GOODS_UNITS" dbType="string" Header="单位" FieldType="text" key="" Remark="" index ="40">
    </Field>
    <Field Column="PLAN_LIST_ORDERED_QUANTITY" dbType="decimal" Header="" FieldType="text" key="" Remark="" index ="50">
    </Field>
    <Field Column="PLAN_LIST_FINISHED_QUANTITY" dbType="decimal" Header="完成数量" FieldType="text" key="" Remark="" index ="60">
    </Field>
    <Field Column="PLAN_LIST_REMARK" dbType="string" Header="备注" FieldType="text" key="" Remark="" index ="70">
    </Field>
  </Table>


  <Table Name="STORAGE_LIST">
    <Field Column="GOODS_CODE" dbType="string" Header="物料编码" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="物料名称" FieldType="text" key="" Remark="" index ="11">
    </Field>
    <Field Column="GOODS_INFORMATION" dbType="string" Header="物料规格" FieldType="text" key="" Remark="" index ="12">
    </Field>
    <Field Column="STORAGE_LIST_QUANTITY" dbType="deciaml" Header="数量"  FieldType="text" key="" Remark="" index ="13">
    </Field>
    <Field Column="GOODS_UNITS" dbType="string" Header="单位" FieldType="text" key="" Remark="" index ="15">
    </Field>
    <Field Column="WAREHOUSE_NAME" dbType="string" Header="存储仓库" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="AREA_NAME" dbType="string" Header="存储库区" FieldType="text" key="" Remark="" index ="21">
    </Field>
    <Field Column="CELL_NAME" dbType="string" Header="存储货位" FieldType="text" key="" Remark="" index ="22">
    </Field>
    <Field Column="STOCK_BARCODE" dbType="string" Header="托盘条码" FieldType="text" key="" Remark="" index ="23">
    </Field>
    <Field Column="ENTRY_TIME" dbType="string" Header="入库时间" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="UPDATE_TIME" dbType="string" Header="" FieldType="text" key="" Remark="" index ="31">
    </Field>
    <Field Column="STORAGE_LIST_REMARK" dbType="string" Header="" FieldType="text" key="" Remark="" index ="32">
    </Field>
  </Table>


  <Table Name="V_IO_MANAGE">
    <Field Column="MANAGE_ID" dbType="string" Header="task index" FieldType="text" key="" Remark="" index ="1">
    </Field>
    <Field Column="PLAN_ID" dbType="string" Header="" FieldType="text" key="" Remark="" index ="2">
    </Field>
    <Field Column="MANAGE_TASKTYPE" dbType="string" Header="" FieldType="text" key="MANAGE_TASKTYPE" Remark="" index ="3">
    </Field>
    <Field Column="MANAGE_MODE" dbType="string" Header="" FieldType="text" key="MANAGE_MODE" Remark="" index ="4">
    </Field>
    <Field Column="MANAGE_STATUS" dbType="string" Header="" FieldType="text" key="MANAGE_STATUS" Remark="" index ="5">
    </Field>
    <Field Column="STOCK_BARCODE" dbType="string" Header="Pallet Barcode" FieldType="text" key="" Remark="" index ="6">
    </Field>
    <Field Column="START_POSITION" dbType="string" Header="Start Position" FieldType="text" key="CELL" Remark="" index ="7">
    </Field>
    <Field Column="END_POSITION" dbType="string" Header="End Position" FieldType="text" key="CELL" Remark="" index ="8">
    </Field>
    <Field Column="MANAGE_OPERATOR" dbType="string" Header="Operator" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="MANAGE_BEGIN_TIME" dbType="string" Header="" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="MANAGE_END_TIME" dbType="string" Header="" FieldType="text" key="" Remark="" index ="11">
    </Field>
    <Field Column="MANAGE_REMARK" dbType="string" Header="" FieldType="text" key="" Remark="" index ="12">
    </Field>
  </Table>

  <Table Name="V_IO_MANAGE_LIST">
    <Field Column="LOTNO" dbType="string" Header="LotNo" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="GOODS_CODE" dbType="string" Header="Item Code" FieldType="text" key="" Remark="" index ="9">
    </Field>
    <Field Column="GOODS_NAME" dbType="string" Header="Item Name" FieldType="text" key="" Remark="" index ="10">
    </Field>
    <Field Column="MANAGE_LIST_QUANTITY" dbType="deciaml" Header="Quantity" FieldType="text" key="" Remark="" index ="20">
    </Field>
    <Field Column="GOODS_UNITS" dbType="string" Header="UOM" FieldType="text" key="" Remark="" index ="30">
    </Field>
    <Field Column="GOODS_PROPERTY" dbType="string" Header="" FieldType="text" key="" Remark="" index ="100">
    </Field>
    <Field Column="MANAGE_LIST_REMARK" dbType="string" Header="Comment" FieldType="text" key="" Remark="" index ="200">
    </Field>
  </Table>


</Fields>
