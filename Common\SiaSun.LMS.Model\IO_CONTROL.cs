﻿/***************************************************************************
 * 
 *       功能：     实体类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;
	
	/// <summary>
	/// IO_CONTROL 
	/// </summary>
    [Serializable]
    [DataContract]
	public class IO_CONTROL
	{
		public IO_CONTROL()
		{
			
		}
		
		private int _control_id;
		private int _relative_control_id;
		private int _manage_id;
		private string _cell_group;
		private string _stock_barcode;
		private string _pre_control_status;
		private int _control_task_type;
		private string _control_task_level;
		private string _start_warehouse_code;
		private string _end_warehouse_code;
		private string _start_device_code;
		private string _end_device_code;
		private int _control_status;
		private string _error_text;
		private string _control_begin_time;
		private string _control_end_time;
		private string _control_remark;

        private string _relevant_code;
        private string _task_id;
        private string _step_no;
        private string _action_type;
        private string _final_position;
        private string _agv_task;
        private int _agv_no;
        private int _aaaa;
		private string _rcs_task_code;
		private string _task_group;
		private string _task_count;

        ///<sumary>
        /// 控制编号
        ///</sumary>
        [DataMember]
		public int CONTROL_ID
		{
			get{return _control_id;}
			set{_control_id = value;}
		}
		///<sumary>
		/// 关联控制编号
        ///</sumary>
        [DataMember]
		public int RELATIVE_CONTROL_ID
		{
			get{return _relative_control_id;}
			set{_relative_control_id = value;}
		}
		///<sumary>
		/// 任务编号
        ///</sumary>
        [DataMember]
		public int MANAGE_ID
		{
			get{return _manage_id;}
			set{_manage_id = value;}
		}
		///<sumary>
		/// 
        ///</sumary>
        [DataMember]
		public string CELL_GROUP
		{
			get{return _cell_group;}
			set{_cell_group = value;}
		}
		///<sumary>
		/// 托盘条码
        ///</sumary>
        [DataMember]
		public string STOCK_BARCODE
		{
			get{return _stock_barcode;}
			set{_stock_barcode = value;}
		}
		///<sumary>
		/// 管理任务类型
        ///</sumary>
        [DataMember]
		public string PRE_CONTROL_STATUS
		{
			get{return _pre_control_status;}
			set{_pre_control_status = value;}
		}
		///<sumary>
		/// 控制类型
        ///</sumary>
        [DataMember]
		public int CONTROL_TASK_TYPE
		{
			get{return _control_task_type;}
			set{_control_task_type = value;}
		}
		///<sumary>
		/// 优先级
        ///</sumary>
        [DataMember]
		public string CONTROL_TASK_LEVEL
		{
			get{return _control_task_level;}
			set{_control_task_level = value;}
		}
		///<sumary>
		/// 起始仓库
        ///</sumary>
        [DataMember]
		public string START_WAREHOUSE_CODE
		{
			get{return _start_warehouse_code;}
			set{_start_warehouse_code = value;}
		}
		///<sumary>
		/// 终止仓库
        ///</sumary>
        [DataMember]
		public string END_WAREHOUSE_CODE
		{
			get{return _end_warehouse_code;}
			set{_end_warehouse_code = value;}
		}
		///<sumary>
		/// 起始设备
        ///</sumary>
        [DataMember]
		public string START_DEVICE_CODE
		{
			get{return _start_device_code;}
			set{_start_device_code = value;}
		}
		///<sumary>
		/// 终止设备
        ///</sumary>
        [DataMember]
		public string END_DEVICE_CODE
		{
			get{return _end_device_code;}
			set{_end_device_code = value;}
		}
		///<sumary>
		/// 状态
        ///</sumary>
        [DataMember]
		public int CONTROL_STATUS
		{
			get{return _control_status;}
			set{_control_status = value;}
		}
		///<sumary>
		/// 错误描述
        ///</sumary>
        [DataMember]
		public string ERROR_TEXT
		{
			get{return _error_text;}
			set{_error_text = value;}
		}
		///<sumary>
		/// 开始时间
        ///</sumary>
        [DataMember]
		public string CONTROL_BEGIN_TIME
		{
			get{return _control_begin_time;}
			set{_control_begin_time = value;}
		}
		///<sumary>
		/// 结束时间
        ///</sumary>
        [DataMember]
		public string CONTROL_END_TIME
		{
			get{return _control_end_time;}
			set{_control_end_time = value;}
		}
		///<sumary>
		/// 备注
        ///</sumary>
        [DataMember]
		public string CONTROL_REMARK
		{
			get{return _control_remark;}
			set{_control_remark = value;}
		}

        ///<sumary>
        /// AGV拣选单号
        ///</sumary>
        [DataMember]
        public string RELEVANT_CODE
        {
            get { return _relevant_code; }
            set { _relevant_code = value; }
        }
        ///<sumary>
        /// AGV拣选任务号
        ///</sumary>
        [DataMember]
        public string TASK_ID
        {
            get { return _task_id; }
            set { _task_id = value; }
        }
        ///<sumary>
        /// AGV拣选步号
        ///</sumary>
        [DataMember]
        public string STEP_NO
        {
            get { return _step_no; }
            set { _step_no = value; }
        }
        ///<sumary>
        /// AGV拣选动作
        ///</sumary>
        [DataMember]
        public string ACTION_TYPE
        {
            get { return _action_type; }
            set { _action_type = value; }
        }
        ///<sumary>
        /// 最终位置 显示用
        ///</sumary>
        [DataMember]
        public string FINAL_POSITION
        {
            get { return _final_position; }
            set { _final_position = value; }
        }
        ///<sumary>
        /// 标识任务是否包含AGV
        ///</sumary>
        public string AGV_TASK
        {
            get { return _agv_task; }
            set { _agv_task = value; }
        }
        ///<sumary>
        /// AGV车号
        ///</sumary>
        public int AGV_NO
        {
            get { return _agv_no; }
            set { _agv_no = value; }
        }
        ///<sumary>
        /// aaaa
        ///</sumary>
        public int AAAA
        {
            get { return _aaaa; }
            set { _aaaa = value; }
        }

		/// <summary>
		/// rcs任务号2021-11-07 14:25:36
		/// </summary>
		public string RCS_TASK_CODE
        {
			get { return _rcs_task_code; }
			set { _rcs_task_code = value; }
		}

		/// <summary>
		/// 齐套出库组号，集货单号
		/// </summary>
		public string TASK_GROUP
		{
			get { return _task_group; }
			set { _task_group = value; }
		}

        /// <summary>
        /// 集货单号下总任务数
        /// </summary>
        public string TASK_COUNT
        {
            get { return _task_count; }
            set { _task_count = value; }
        }
    }
}
