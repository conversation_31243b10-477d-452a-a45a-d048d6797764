﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using Quartz;
namespace SiaSun.LMS.WinService
{
    //DisallowConcurrentExecution特性，程序会等任务执行完毕以后再去执行，否则会在任务的时间间隔 [Interval]时再启用新的线程执行
    [DisallowConcurrentExecution]
    public class ApplyJob : IJob
    {
        public void Execute(IJobExecutionContext context)
        {
            Program.sysLog.Debug("进入ApplyJob");
            bool bResult = true;
            string sResult = string.Empty;

            try
            {
                bResult = MainApp.BaseService._S_ManageService.ControlApplyTask(out sResult);
            }
            catch (Exception ex)
            {
                Program.sysLog.Error("TaskApplyJob.Execute:异常", ex);
                throw;
            }
            finally
            {
                if(!bResult)
                {
                    Program.sysLog.WarnFormat("TaskApplyJob.Execute:处理申请失败 {0}",sResult);
                }
            }

            Program.sysLog.Debug(string.Format("离开ApplyJob[{0}][{1}]", bResult,sResult));
        }
    }
}
