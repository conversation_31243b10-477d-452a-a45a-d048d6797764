﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     LaiHaMa
 *       日期：     2010-9-7
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
    using System;
    using System.Collections;
    using System.Collections.Generic;
    using IBatisNet.Common;
    using IBatisNet.DataMapper;
    using IBatisNet.Common.Exceptions;
    using SiaSun.LMS.Model;

    /// <summary>
    /// P_STORAGE_LOCK
    /// </summary>
    public class P_STORAGE_LOCK : P_Base_House
    {
        public P_STORAGE_LOCK()
        {
            //
            // TODO: 此处添加STORAGE_LOCK的构造函数
            //
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LOCK> GetList()
        {
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT", null);
        }

        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LOCK> GetList_PLAN_LIST_ID(int PLAN_LIST_ID)
        {
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_PLAN_LIST_ID", PLAN_LIST_ID);
        }
        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LOCK> GetList_PLAN_LIST_ID_STORAGE_LOCK_FLAG(int PLAN_LIST_ID, string STORAGE_LOCK_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PLAN_LIST_ID", PLAN_LIST_ID);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_PLAN_LIST_ID_STORAGE_LOCK_FLAG", ht);
        }
        /// <summary>
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LOCK> GetList_STORAGE_LIST_ID(int STORAGE_LIST_ID)
        {
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_STORAGE_LIST_ID", STORAGE_LIST_ID);
        }

        /// <summary>
        /// 新建
        /// </summary>
        public void Add(STORAGE_LOCK storage_lock)
        {
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("STORAGE_LOCK");
                storage_lock.STORAGE_LOCK_ID = id;
            }

            this.ExecuteInsert("STORAGE_LOCK_INSERT", storage_lock);
        }
        /// <summary>
        /// 修改
        /// </summary>
        public void Update(STORAGE_LOCK storage_lock)
        {
            this.ExecuteUpdate("STORAGE_LOCK_UPDATE", storage_lock);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public STORAGE_LOCK GetModel(System.Int32 STORAGE_LOCK_ID)
        {
            return this.ExecuteQueryForObject<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_STORAGE_LOCK_ID", STORAGE_LOCK_ID);
        }

        /// <summary>
        /// 删除
        /// </summary>
        /// <param name="id"></param>
        public int Delete(System.Int32 STORAGE_LOCK_ID)
        {
            return this.ExecuteDelete("STORAGE_LOCK_DELETE", STORAGE_LOCK_ID);
        }


        /// <summary>
        /// 合并程序增加
        /// xutao
        /// 得到列表
        /// </summary>
        public IList<STORAGE_LOCK> GetList_PICK_STATION(string STATION_GROUP, string STORAGE_LOCK_FLAG,string PLAN_ID)
        {
            Hashtable ht = new Hashtable();
            ht.Add("STATION_GROUP", STATION_GROUP);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            ht.Add("PLAN_ID", PLAN_ID);
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_STATION_GROUP_STORAGE_LOCK_FLAG", ht);
        }
        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID和锁定库存标志获得锁定库存表序列
        /// done
        /// </summary>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="STORAGE_LOCK_FLAG"></param>
        /// <returns></returns>
        public IList<STORAGE_LOCK> GetListByPickStationIdAndFlag(int PICK_STATION_ID, string STORAGE_LOCK_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_STATION_ID", PICK_STATION_ID);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_PICK_STATION_ID_STORAGE_LOCK_FLAG", ht);
        }

        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID和锁定库存标志获得锁定库存表序列
        /// done
        /// </summary>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="STORAGE_LOCK_FLAG"></param>
        /// <returns></returns>
        public IList<STORAGE_LOCK> GetListByPrevPlanGroupAndFlag(string prevPlanGroup, string STORAGE_LOCK_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PLAN_GROUP", prevPlanGroup);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_PLAN_GROUP_STORAGE_LOCK_FLAG", ht);
        }
        /// <summary>
        /// 拣选工作站相关
        /// 通过拣选工作站ID和锁定库存标志获得锁定库存表序列
        /// done
        /// </summary>
        /// <param name="PICK_STATION_ID"></param>
        /// <param name="STORAGE_LOCK_FLAG"></param>
        /// <returns></returns>
        public IList<STORAGE_LOCK> GetListByPickPositionIdAndFlag(int PICK_POSITION_ID, string STORAGE_LOCK_FLAG)
        {
            Hashtable ht = new Hashtable();
            ht.Add("PICK_POSITION_ID", PICK_POSITION_ID);
            ht.Add("STORAGE_LOCK_FLAG", STORAGE_LOCK_FLAG);
            return this.ExecuteQueryForList<STORAGE_LOCK>("STORAGE_LOCK_SELECT_BY_PICK_POSITION_ID_STORAGE_LOCK_FLAG", ht);
        }
    }
}
