<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
  xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
  xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
  mc:Ignorable="d"
  xmlns:d="http://schemas.microsoft.com/expression/blend/2008">
    
    <ResourceDictionary.MergedDictionaries>
        <ResourceDictionary Source="Shared.xaml" />
    </ResourceDictionary.MergedDictionaries>

    <Style TargetType="{x:Type Slider}">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Slider}">
                    <Grid x:Name="GridRoot">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="Auto" MinHeight="{TemplateBinding MinHeight}" />
                            <RowDefinition Height="Auto" />
                        </Grid.RowDefinitions>

                        <TickBar Visibility="Collapsed" x:Name="TopTick" Height="4" SnapsToDevicePixels="True" Placement="Top" Fill="#FF405A78" />
                        <Rectangle Margin="7.5,0,7.5,0" Grid.Column="0" Height="6" RadiusX="3" RadiusY="3" Grid.Row="1" Fill="{DynamicResource SliderBackgroundBrush}"/>

                        <Track Grid.Row="1" x:Name="PART_Track">
                            <Track.Thumb>
                                <Thumb Style="{DynamicResource NuclearSliderThumb}" />
                            </Track.Thumb>
                            <Track.IncreaseRepeatButton>
                                <RepeatButton Style="{DynamicResource NuclearScrollRepeatButtonStyle}" Command="Slider.IncreaseLarge" />
                            </Track.IncreaseRepeatButton>
                            <Track.DecreaseRepeatButton>
                                <RepeatButton Style="{DynamicResource NuclearScrollRepeatButtonStyle}" Command="Slider.DecreaseLarge" />
                            </Track.DecreaseRepeatButton>
                        </Track>

                        <TickBar Visibility="Collapsed" Grid.Row="2" x:Name="BottomTick" Height="4" SnapsToDevicePixels="True" Placement="Bottom" Fill="{TemplateBinding Foreground}" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="TickPlacement" Value="TopLeft">
                            <Setter Property="Visibility" Value="Visible" TargetName="TopTick" />
                        </Trigger>
                        <Trigger Property="TickPlacement" Value="BottomRight">
                            <Setter Property="Visibility" Value="Visible" TargetName="BottomTick" />
                        </Trigger>
                        <Trigger Property="TickPlacement" Value="Both">
                            <Setter Property="Visibility" Value="Visible" TargetName="TopTick" />
                            <Setter Property="Visibility" Value="Visible" TargetName="BottomTick" />
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" TargetName="GridRoot" Value="0.65" />
                        </Trigger>

                        <Trigger Property="Orientation" Value="Vertical">
                            <Setter Property="LayoutTransform" TargetName="GridRoot">
                                <Setter.Value>
                                    <RotateTransform Angle="-90" />
                                </Setter.Value>
                            </Setter>
                            <Setter TargetName="PART_Track" Property="Orientation" Value="Horizontal" />
                        </Trigger>

                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="NuclearSliderThumb" d:IsControlPart="True" TargetType="{x:Type Thumb}">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Height" Value="21" />
        <Setter Property="Width" Value="15" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Thumb}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HoverOn">
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="1" />
                        </Storyboard>
                        <Storyboard x:Key="HoverOff">
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Over" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                        <Storyboard x:Key="PressedOn">
                            <DoubleAnimation Duration="00:00:00.1000000" Storyboard.TargetName="Press" Storyboard.TargetProperty="Opacity" To="1" />
                        </Storyboard>
                        <Storyboard x:Key="PressedOff">
                            <DoubleAnimation Duration="00:00:00.4000000" Storyboard.TargetName="Press" Storyboard.TargetProperty="Opacity" To="0" />
                        </Storyboard>
                        <Storyboard x:Key="FocusedOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="FocusedOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="FocusVisualElement" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Grid>
                        <Path x:Name="Base" Margin="1,1.312,1,0.375" Fill="{DynamicResource SliderThumbBrush}" Stretch="Fill" Stroke="{DynamicResource SliderThumbBorderBrush}" StrokeThickness="1" StrokeLineJoin="Round" Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4266928 L-6.6465902,1.7712332 L-9.9818907,1.433604 z" />
                        <Path x:Name="Over" Margin="2,2.312,2,1.375" Fill="{DynamicResource MouseOverBrush}" Stretch="Fill" Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z" Opacity="0" />
                        <Path x:Name="Press" Margin="2,2.312,2,1.375" Fill="{DynamicResource PressedBrush}" Stretch="Fill" Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z" Opacity="0" />
                        <Path x:Name="whiteGradient" Margin="2,2.312,2,1.375" Stretch="Fill" Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z">
                            <Path.Fill>
                                <LinearGradientBrush EndPoint="0.5,0" StartPoint="0.563,0.979">
                                    <GradientStop Color="#5FFFFFFF" Offset="0" />
                                    <GradientStop Color="#5FFFFFFF" Offset="0.259" />
                                    <GradientStop Color="#00FFFFFF" Offset="0.393" />
                                    <GradientStop Color="#00FFFFFF" Offset="0.643" />
                                    <GradientStop Color="#75FFFFFF" Offset="0.75" />
                                    <GradientStop Color="#99FFFFFF" Offset="1" />
                                </LinearGradientBrush>
                            </Path.Fill>
                        </Path>
                        <Path x:Name="Line" Margin="-1,-2,0,0" Height="10" Width="1" Stretch="Fill" Stroke="#FF6B81A0" StrokeThickness="1" Data="M5.4375,2.6875 L5.4375,12.1875" VerticalAlignment="Center" HorizontalAlignment="Center" />
                        <Path x:Name="Line2" Margin="0,-2,-1,0" Height="10" Width="1" Stretch="Fill" Stroke="#FFFFFFFF" StrokeThickness="1" Data="M5.4375,2.6875 L5.4375,12.1875" VerticalAlignment="Center" HorizontalAlignment="Center" />
                        <Path x:Name="DisabledVisualElement" Margin="1,1.312,1,0.375" Fill="#FFFFFFFF" Stroke="#FFFFFFFF" StrokeThickness="1" StrokeLineJoin="Round" Stretch="Fill" Opacity="0" Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4266928 L-6.6465902,1.7712332 L-9.9818907,1.433604 z" IsHitTestVisible="false" />
                        <Path x:Name="FocusVisualElement" Stretch="Fill" Stroke="{DynamicResource FocusBrush}" StrokeThickness="1" StrokeLineJoin="Round" Data="M-9.958333,0.78716499 L-3.204694,0.78717428 L-3.2052999,1.4276805 L-6.6465902,1.7722208 L-9.9818907,1.4345917 z" IsHitTestVisible="false" Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsFocused" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOff}" x:Name="FocusedOff_BeginStoryboard1" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource FocusedOn}" x:Name="FocusedOn_BeginStoryboard1" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsDragging" Value="True">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource PressedOff}" x:Name="PressedOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource PressedOn}" x:Name="PressedOn_BeginStoryboard" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsMouseOver" Value="True">

                            <Trigger.ExitActions>
                                <BeginStoryboard x:Name="HoverOff_BeginStoryboard" Storyboard="{StaticResource HoverOff}" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HoverOn}" />
                            </Trigger.EnterActions>

                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Opacity" TargetName="DisabledVisualElement" Value="0.5" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style x:Key="NuclearScrollRepeatButtonStyle" d:IsControlPart="True" TargetType="{x:Type RepeatButton}">
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="BorderBrush" Value="Transparent" />
        <Setter Property="IsTabStop" Value="false" />
        <Setter Property="Focusable" Value="false" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type RepeatButton}">
                    <Grid>
                        <Rectangle Fill="{TemplateBinding Background}" Stroke="{TemplateBinding BorderBrush}" StrokeThickness="{TemplateBinding BorderThickness}" />
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>