﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 非立库输送任务执行结果回传接口
    /// </summary>
    public class noneMiniloadTranResultReturnFromWCS : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _uniqueCode = string.Empty;            //唯一码
            private string _resultCode = string.Empty;      //结果代码
            private string _resultMessage = string.Empty;        //回传消息
            private string _relevantCode = string.Empty;        //相关单号
            private string _taskID = string.Empty;               //任务标识
            private string _stepNo = string.Empty;               //步号
            private string _actionType = string.Empty;        //动作
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源
            private string _agvNo = string.Empty;                 //agv车号

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 结果代码
            /// </summary>
            public string resultCode
            {
                get { return _resultCode; }
                set { _resultCode = value; }
            }

            /// <summary>
            /// 回传消息
            /// </summary>
            public string resultMessage
            {
                get { return _resultMessage; }
                set { _resultMessage = value; }
            }


            /// <summary>
            /// 相关单号
            /// </summary>
            public string relevantCode
            {
                get { return _relevantCode; }
                set { _relevantCode = value; }
            }

            /// <summary>
            /// 任务标识
            /// </summary>
            public string taskID
            {
                get { return _taskID; }
                set { _taskID = value; }
            }

            /// <summary>
            /// 步号
            /// </summary>
            public string stepNo
            {
                get { return _stepNo; }
                set { _stepNo = value; }
            }
            /// <summary>
            /// 动作
            /// </summary>
            public string actionType
            {
                get { return _actionType; }
                set { _actionType = value; }
            }            

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// AGV车号
            /// </summary>
            public string agvNo
            {
                get { return _agvNo; }
                set { _agvNo = value; }
            }

        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string uniqueCode, string relevantCode,string taskID,string stepNo,string actionType,bool actionResult,string errorText,string agvNo, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            InputParaMain inputPara = new InputParaMain();

            try
            {
                inputPara.uniqueCode = uniqueCode;
                inputPara.relevantCode = relevantCode;
                inputPara.taskID = taskID;
                inputPara.stepNo = stepNo;
                inputPara.actionType = actionType;
                inputPara.agvNo = agvNo;
                if(actionResult)
                {
                    inputPara.resultCode = "1";
                    inputPara.resultMessage = "新松提示 成功";
                }
                else
                {
                    inputPara.resultCode = "0";
                    inputPara.resultMessage = string.Format("新松提示 失败 {0}", errorText);
                }
                inputPara.interfaceSource = "WES";
                inputPara.interfaceType = "1";

                string strInputParaJson = Common.JsonHelper.Serializer(inputPara);

                string noneMinloadWorkMode = string.Empty;
                if (this._S_SystemService.GetSysParameter("NoneMiniloadTaskInterfaceWorkMode", out noneMinloadWorkMode) && noneMinloadWorkMode == "Test")
                {
                    bResult = this._S_WESJsonService.noneMiniloadTranResultReturnFromWCSTest(strInputParaJson, out sResult);
                }
                else
                {
                    bResult = this._S_WESJsonService.noneMiniloadTranResultReturnFromWCS(strInputParaJson, out sResult);
                }

                if (bResult)
                {
                    OutputPara outputPara = Common.JsonHelper.Deserialize<OutputPara>(sResult);
                    if(outputPara.responseCode!="1")
                    {
                        bResult = false;
                        sResult = string.Format(" noneMiniloadTranResultReturnFromWCS.NotifyMethod:唯智返回失败 {0}", outputPara.responseMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                sResult = string.Format("noneMiniloadTranResultReturnFromWCS.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {

            }

            return bResult;
        }

    }
}
