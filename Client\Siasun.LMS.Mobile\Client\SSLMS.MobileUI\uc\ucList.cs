﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace SSLMS.MobileUI
{
    public partial class ucList : UserControl
    {

        private string _goodsTypeSQL =@"SELECT B_GOODS_TYPE.GOODS_TYPE_ID,B_GOODS_TYPE.GOODS_TYPE_NAME FROM (SELECT DISTINCT {0} FROM {1} WHERE 1=1 {2}) a INNER JOIN B_GOODS_TYPE ON (a.{0} = B_GOODS_TYPE.GOODS_TYPE_ID)";


        /// <summary>
        /// 分离列
        /// </summary>
        private string _colSplit = string.Empty;

        public string colSplit
        {
            get { return _colSplit; }
            set { _colSplit = value; }
        }

        private string _listXml;

        public string listXml
        {
            get { return _listXml; }
            set { _listXml = value; }
        }

        //是否编辑
        private bool _bNew = false;
        public bool bNew
        {
            get { return _bNew; }
            set { _bNew = value; }
        }

        private string _colGroup = string.Empty;

        public string colGroup
        {
            get { return _colGroup; }
            set { _colGroup = value; }
        }

        /// <summary>
        /// 列表名
        /// </summary>
        private string _listTable;

        public string listTable
        {
            get { return _listTable; }
            set { _listTable = value; }
        }

        /// <summary>
        /// 列表条件
        /// </summary>
        private string _listWhere;

        public string listWhere
        {
            get { return _listWhere; }
            set { _listWhere = value; }
        }

        public ucList()
        {
            InitializeComponent();
        }


        public void Init()
        {
            this.tabGoodsType.TabPages.Clear();

            if (this._bNew)
            {
                this._goodsTypeSQL = "SELECT GOODS_TYPE_ID,GOODS_TYPE_NAME FROM B_GOODS_TYPE";
            }
            
            this.InitType();
        }


        public void InitType()
        {
            //MessageBox.Show(string.Format(_goodsTypeSQL, colGroup, _listTable, _listWhere));

            DataTable dtGoodsType = Program._i_base.GetList(string.Format(_goodsTypeSQL, colGroup, _listTable, _listWhere));

            //DataTable dtGoodsType = Program._i_base.GetList(@"SELECT B_GOODS_TYPE.GOODS_TYPE_ID,B_GOODS_TYPE.GOODS_TYPE_NAME FROM (SELECT DISTINCT GOODS_TYPE_ID FROM V_IO_PLAN_LIST WHERE 1=1  AND PLAN_ID =27) a INNER JOIN B_GOODS_TYPE ON (a.GOODS_TYPE_ID = B_GOODS_TYPE.GOODS_TYPE_ID)");

            for (int i = 0; i < dtGoodsType.Rows.Count; i++)
            {
                string strGoodsType = dtGoodsType.Rows[i]["GOODS_TYPE_ID"].ToString();

                //MessageBox.Show(strGoodsType.ToString());

                TabPage tbp = new TabPage();

                tbp.Name = "tbp_" + strGoodsType;

                tbp.TabIndex = i;

                tbp.Text = dtGoodsType.Rows[i]["GOODS_TYPE_NAME"].ToString();

                this.tabGoodsType.Controls.Add(tbp);

                InitList(tbp, strGoodsType);
            }
        }


        private void InitList(TabPage tbp, string strGoodsType)
        {
            MyDataGrid ucGrid = new MyDataGrid();

            tbp.Tag = ucGrid;

            ucGrid.Name = "ucGrid_" + strGoodsType;

            ucGrid.Dock = DockStyle.Fill;



            //ucGrid.sXml = string.Format(this._listXml, strGoodsType);

            ucGrid.sTable = this._listTable;

            ucGrid.sWhere = this._listWhere + string.Format(" AND {0}='{1}'", colGroup, strGoodsType);

            ucGrid.sXml = this._listXml;

            ucGrid.colGroup = this._colGroup;

            ucGrid.colSplit = this._colSplit;

           
            ucGrid.Visible = true;

            ucGrid.TabIndex = 0;

            //ucGrid.colGroup = this._colGroup;

           // ucGrid.colSplit = this._colSplit;



            

           
            tbp.Controls.Add(ucGrid);

            ucGrid.Init();


           
        }

        public DataTable GetTable()
        {
            DataTable dt = new DataTable();

            for (int i = 0; i < this.tabGoodsType.TabPages.Count; i++)
            {
                DataTable dtgrid = ((this.tabGoodsType.TabPages[i].Tag as MyDataGrid).DataSource) as DataTable;

                dt.Merge(dtgrid);
            }

            return dt;
        }

       



    }
}
