﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucMdiTreeViewDataGridControl"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="485">
    <Grid>
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>
        
        <uc:ucTreeView x:Name="tvwParent" Margin="1" Grid.Column="0" MinWidth="150"></uc:ucTreeView>
        <GridSplitter Grid.Column="1" VerticalAlignment="Stretch" FlowDirection="LeftToRight" Margin="1"></GridSplitter>
        <GroupBox Grid.Column="2" Name="grpboxChild" Tag=" {0}" Header="子列表" Margin="1">
        </GroupBox>
     </Grid>
</UserControl>
