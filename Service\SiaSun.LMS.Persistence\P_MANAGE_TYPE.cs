﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// MANAGE_TYPE
	/// </summary>
	public class P_MANAGE_TYPE : P_Base_House
	{
		public P_MANAGE_TYPE ()
		{
			//
			// TODO: 此处添加MANAGE_TYPE的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<MANAGE_TYPE> GetList()
		{
			return ExecuteQueryForList<MANAGE_TYPE>("MANAGE_TYPE_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(MANAGE_TYPE manage_type)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("MANAGE_TYPE");
                manage_type.MANAGE_TYPE_ID = id;
            }

            return ExecuteInsert("MANAGE_TYPE_INSERT",manage_type);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(MANAGE_TYPE manage_type)
		{
			return ExecuteUpdate("MANAGE_TYPE_UPDATE",manage_type);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public MANAGE_TYPE GetModel(System.Int32 MANAGE_TYPE_ID)
		{
			return ExecuteQueryForObject<MANAGE_TYPE>("MANAGE_TYPE_SELECT_BY_ID",MANAGE_TYPE_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public MANAGE_TYPE GetModelManageTypeCode(string MANAGE_TYPE_CODE)
        {
            return ExecuteQueryForObject<MANAGE_TYPE>("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 MANAGE_TYPE_ID)
		{
			return ExecuteDelete("MANAGE_TYPE_DELETE",MANAGE_TYPE_ID);
		}
		

	}
}
