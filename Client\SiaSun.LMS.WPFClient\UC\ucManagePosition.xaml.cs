﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;
using System.Reflection;
using System.Collections;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucManagePosition.xaml 的交互逻辑
    /// </summary>
    public partial class ucManagePosition : UserControl
    {
        //判断是否加载完成
        bool boolIsLoaded = false;
        bool boolAllowChooseStartPosition = false;
        bool boolAllowChooseEndPosition = false;

        #region     ------定义代理和事件

        public delegate void U_StockBarcodeKeyDownHandler();

        public event U_StockBarcodeKeyDownHandler U_StockBarcodeKeyDown;

        /// <summary>
        /// 输送位置更改
        /// </summary>
        public delegate void U_StartPositionValueChangedHandler();

        //输送位置更改
        public event U_StartPositionValueChangedHandler U_StartPositionChanged;

        /// <summary>
        /// 输送位置更改
        /// </summary>
        public delegate void U_EndPositionValueChangedHandler();

        //输送位置更改
        public event U_EndPositionValueChangedHandler U_EndPositionChanged;

        /// <summary>
        /// 货位规格类型更改
        /// </summary>
        public delegate void U_CellModelValueChangedHandler();

        //货位规格类型更改
        public event U_CellModelValueChangedHandler U_CellModelChanged;


        #endregion

        #region     ------可在数据库表MANAGE_TYPE_PARAM中设备的属性

        bool boolShowStockBarcode = true;

        /// <summary>
        /// 是否显示托盘条码
        /// </summary>
        public bool U_AllowShowStockBarcode
        {
            get { return boolShowStockBarcode; }
            set
            {
                boolShowStockBarcode = value;
                this.panelStockBarcode.Visibility = this.boolShowStockBarcode ? Visibility.Visible : System.Windows.Visibility.Collapsed;
            }
        }


        bool boolAllowShowCellModel = true;

        /// <summary>
        /// 是否显示货位规格
        /// </summary>
        public bool U_AllowShowCellModel
        {
            get { return boolAllowShowCellModel; }
            set { boolAllowShowCellModel = value; this.panelCellModel.Visibility = this.boolAllowShowCellModel ? Visibility.Visible : System.Windows.Visibility.Collapsed; }
        }


        bool boolAllowShowOccupyPercent = false;

        /// <summary>
        /// 是否显示托盘占用百分比
        /// </summary>
        public bool U_AllowShowOccupyPercent
        {
            get { return boolAllowShowOccupyPercent; }
            set { boolAllowShowOccupyPercent = value; this.panelOccupyPercent.Visibility = this.boolAllowShowOccupyPercent ? Visibility.Visible : Visibility.Collapsed; }
        }


        bool boolShowStartPostion = true;

        /// <summary>
        /// 是否显示起始位置
        /// </summary>
        public bool U_AllowShowStartPosition
        {
            get { return boolShowStartPostion; }
            set
            {
                boolShowStartPostion = value;
                this.panelStartPosition.Visibility = value ? Visibility.Visible : System.Windows.Visibility.Collapsed;
            }
        }

        bool boolShowEndPosition = true;

        /// <summary>
        /// 是否显示终止位置
        /// </summary>
        public bool U_AllowShowEndPosition
        {
            get { return boolShowEndPosition; }
            set
            {
                boolShowEndPosition = value;
                this.panelEndPosition.Visibility = value ? Visibility.Visible : System.Windows.Visibility.Collapsed;
            }
        }

        string strAssembleResource = "1";

        public string U_AssembleResource
        {
            get { return this.strAssembleResource; }
            set { this.strAssembleResource = value; }
        }


        string strStartCellType = string.Empty;

        public string U_StartCellType
        {
            get { return this.strStartCellType; }
            set { this.strStartCellType = value; }
        }


        string strStartCellInOut = string.Empty;

        public string U_StartCellInOut
        {
            get { return this.strStartCellInOut; }
            set { this.strStartCellInOut = value; }
        }

        string strEndCellType = string.Empty;

        public string U_EndCellType
        {
            get { return this.strEndCellType; }
            set { this.strEndCellType = value; }
        }


        string strEndCellInOut = string.Empty;

        public string U_EndCellInOut
        {
            get { return this.strEndCellInOut; }
            set { this.strEndCellInOut = value; }
        }


        bool boolCheckStockExistStorage = false;

        public bool U_CheckStockExistStorage
        {
            get { return this.boolCheckStockExistStorage; }
            set { this.boolCheckStockExistStorage = value; }
        }


        bool boolAutoDownloadControlTask = false;


        public bool U_AutoDownloadControlTask
        {
            get { return this.boolAutoDownloadControlTask; }
            set { this.boolAutoDownloadControlTask = value; }
        }


        bool boolAutoCompleteTask = false;

        public bool U_AutoCompleteTask
        {
            get { return this.boolAutoCompleteTask; }
            set { this.boolAutoCompleteTask = value; }
        }

        bool boolAllowAutoStartPostion = false;

        /// <summary>
        /// 是否允许自动选择起始位置
        /// </summary>
        public bool U_AllowAutoStartPostion
        {
            get { return boolAllowAutoStartPostion; }
            set { boolAllowAutoStartPostion = value; }
        }

        bool boolAllowAutoEndPostion = false;

        /// <summary>
        /// 是否允许自动选择终止位置
        /// </summary>
        public bool U_AllowAutoEndPostion
        {
            get { return boolAllowAutoEndPostion; }
            set { boolAllowAutoEndPostion = value; }
        }

        string strStartPositionDefault = string.Empty;
        /// <summary>
        ///
        /// </summary>
        public string U_StartPositionDefault
        {
            get { return this.strStartPositionDefault; }
            set
            {
                this.cmbStartPosition.IsEnabled = !(value!=string.Empty);
                this.strStartPositionDefault = value;
            }
        }

        string strEndPositionDefault = string.Empty;

        /// <summary>
        /// 设置目标位置是否可用
        /// </summary>
        public string U_EndPositionDefault
        {
            get { return this.strEndPositionDefault; }
            set
            {
                this.cmbEndPosition.IsEnabled = !(value !=string.Empty);
                this.strEndPositionDefault = value;
            }
        }


        string strCellModelDefault = string.Empty;
        /// <summary>
        /// 设置货位规格是否可用
        /// </summary>
        public string U_CellModelDefault
        {
            get { return strCellModelDefault; }
            set
            {
                if (!String.IsNullOrEmpty(value))
                {
                    this.cmbCellModel.IsEnabled = false;
                    this.strCellModelDefault = value;
                }
            }
        }


        string strWarehouseType = string.Empty;
        /// <summary>
        /// 加载货位选择时的仓库类型
        /// </summary>
        public string U_WarehouseType
        {
            get { return strWarehouseType; }
            set { strWarehouseType = value; }
        }


        string strStartPositonCellStatus = string.Empty;
        /// <summary>
        /// 选择起始位置要求的货位状态
        /// </summary>
        public string U_StartPositionCellStatus
        {
            get { return strStartPositonCellStatus; }
            set { strStartPositonCellStatus = value; }
        }

        string strEndPositonCellStatus = string.Empty;
        /// <summary>
        /// 选择起始位置要求的货位状态
        /// </summary>
        public string U_EndPositionCellStatus
        {
            get { return strEndPositonCellStatus; }
            set { strEndPositonCellStatus = value; }
        }

        #endregion




        /// <summary>
        /// 货位规格
        /// </summary>
        public string U_CELL_MODEL
        {
            get { return this.cmbCellModel.SelectedValue == null ? string.Empty : this.cmbCellModel.SelectedValue.ToString(); }
            set { if (value != null)this.cmbCellModel.SelectedValue = value; }
        }

        /// <summary>
        /// 货位占用百分比
        /// </summary>
        public string U_OCCUPY_PERCENT
        {
            get { return this.cmbOccupyPercent.SelectedValue == null ? string.Empty : this.cmbOccupyPercent.SelectedValue.ToString(); }
            set { if (value != null)this.cmbOccupyPercent.SelectedValue = value; }
        }


        /// <summary>
        /// 起始输送位置编号
        /// </summary>
        public int U_START_POSITION_ID
        {
            get { return cmbStartPosition.SelectedValue == null ? 0 : Convert.ToInt32(this.cmbStartPosition.SelectedValue); }
            set { this.cmbStartPosition.SelectedValue = value; }
        }

        /// <summary>
        /// 终止输送位置编号
        /// </summary>
        public int U_END_POSITION_ID
        {
            get { return cmbEndPosition.SelectedValue == null ? 0 : Convert.ToInt32(this.cmbEndPosition.SelectedValue); }
            set { this.cmbEndPosition.SelectedValue = value; }
        }

        public string U_STOCK_BARCODE
        {
            get { return this.txtStockBarcode.Text.TrimEnd(); }
            set { this.txtStockBarcode.Text = value; }
        }

        /// <summary>
        /// 构造函数
        /// </summary>
        public ucManagePosition()
        {
            InitializeComponent();
        }

        /// <summary>
        /// 初始化控件
        /// </summary>
        public Model.MANAGE_TYPE_PARAM U_InitControl(int MANAGE_TYPE_ID)
        {
            Model.MANAGE_TYPE_PARAM mMANAGE_TYPE_PARAM = new Model.MANAGE_TYPE_PARAM();

            Model.MANAGE_TYPE mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_ID", MANAGE_TYPE_ID).RequestObject;

            Model.GOODS_TYPE mGOODS_TYPE = (Model.GOODS_TYPE)MainApp._I_BaseService.GetModel("GOODS_TYPE_SELECT_BY_GOODS_TYPE_REMARK", "MANAGE_TYPE").RequestObject;
            Hashtable ht = new Hashtable();
            string[] arStrProperty = mMANAGE_TYPE.MANAGE_TYPE_PROPERTY.ToString().Split('|');
            using (DataTable dataTableSplit = this.GetGoodsPropertyTable(mGOODS_TYPE.GOODS_TYPE_ID.ToString()))
            {
                for (int i = 0; i < dataTableSplit.Rows.Count; i++)
                {
                    ht.Add(dataTableSplit.Rows[i]["column"].ToString(), arStrProperty[i].ToString());
                }
            }


            PropertyInfo[] propertys_info = this.GetType().GetProperties();

            foreach (PropertyInfo pi in propertys_info)
            {
                if (ht.Contains(pi.Name))
                {
                    if (pi.PropertyType.Name == "Boolean")
                        pi.SetValue(this, Convert.ChangeType(ht[pi.Name].ToString() == "1" ? "True" : "False", pi.PropertyType), null);
                    else
                        pi.SetValue(this, Convert.ChangeType(ht[pi.Name] == null ? string.Empty : ht[pi.Name].ToString(), pi.PropertyType), null);

                }
            }


            PropertyInfo[] property_info_param = mMANAGE_TYPE_PARAM.GetType().GetProperties();
            foreach (PropertyInfo pi in property_info_param)
            {
                if (ht.Contains(pi.Name))
                {
                    if (pi.PropertyType.Name == "Boolean")
                        pi.SetValue(mMANAGE_TYPE_PARAM, Convert.ChangeType(ht[pi.Name].ToString() == "1" ? "True" : "False", pi.PropertyType), null);
                    else
                        pi.SetValue(mMANAGE_TYPE_PARAM, Convert.ChangeType(ht[pi.Name] == null ? string.Empty : ht[pi.Name].ToString(), pi.PropertyType), null);

                }
            }



            this.boolIsLoaded = false;

            //货位规格
            this.CELL_MODEL_BIND();

            if (this.U_AllowShowOccupyPercent)
                this.OCCUPY_PERCENT_BIND();

            this.START_POSITION_BIND();

            this.END_POSITION_BIND();

            //设置默认值
            this.LoadDefaultValue();
            //设置是否显示选择位置按钮
            this.SetEnableChoosePositon();

            return mMANAGE_TYPE_PARAM;
        }

        /// <summary>
        /// 设置是否允许设置显示选择位置按钮
        /// </summary>
        private void SetEnableChoosePositon()
        {
            try
            {

                this.boolAllowChooseStartPosition = this.U_StartCellType == Enum.CELL_TYPE.Cell.ToString();
                this.btnStartPosition.Visibility = this.boolAllowChooseStartPosition ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
                this.cmbStartPosition.IsEnabled = !this.boolAllowChooseStartPosition;


                this.boolAllowChooseEndPosition = this.U_EndCellType == Enum.CELL_TYPE.Cell.ToString();
                this.btnEndPosition.Visibility = this.boolAllowChooseEndPosition ? System.Windows.Visibility.Visible : System.Windows.Visibility.Collapsed;
                this.cmbEndPosition.IsEnabled = !this.boolAllowChooseEndPosition;


            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        #region     ------根据加载规则，加载起始库区和目标库区

        /// <summary>
        /// 清除数据绑定
        /// </summary>
        private void DataBindClear()
        {
            this.cmbStartPosition.ItemsSource = null;

            this.cmbEndPosition.ItemsSource = null;
        }


        /// <summary>
        /// 加载默认的库区
        /// </summary>
        private void LoadDefaultValue()
        {
            this.boolIsLoaded = false;

            //CellModel
            if (this.cmbCellModel.HasItems && this.cmbCellModel.SelectedValue == null)
            {
                if (!string.IsNullOrEmpty(this.strCellModelDefault))
                    this.cmbCellModel.SelectedValue = strCellModelDefault;
            }

            this.SetStartPositionDefault();


            this.SetEndPositionDefault();


            this.boolIsLoaded = true;
        }

        /// <summary>
        /// 加载默认的库区
        /// </summary>
        public void U_Refresh()
        {
            this.boolIsLoaded = false;

            this.txtStockBarcode.Text = string.Empty;

            this.cmbCellModel.SelectedValue = null;

            this.cmbOccupyPercent.SelectedValue = null;

            this.cmbStartPosition.SelectedValue = null;

            this.cmbEndPosition.SelectedValue = null;

            this.boolIsLoaded = true;
        }
        
        #endregion

        #region     ------CELL_MODEL绑定及变更

        /// <summary>
        /// 货位规格绑定
        /// </summary>
        private void CELL_MODEL_BIND()
        {
            try
            {

                IList<SiaSun.LMS.Model.SYS_ITEM_LIST> listITEM_LIST = MainApp._I_SystemService.ITEM_LIST_GetList_ITEM_CODE("CELL_MODEL");

                this.cmbCellModel.DisplayMemberPath = "ITEM_LIST_NAME";
                this.cmbCellModel.SelectedValuePath = "ITEM_LIST_CODE";
                this.cmbCellModel.ItemsSource = listITEM_LIST;
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        /// <summary>
        /// 货位规格绑定
        /// </summary>
        private void OCCUPY_PERCENT_BIND()
        {
            try
            {

                IList<SiaSun.LMS.Model.SYS_ITEM_LIST> listITEM_LIST = MainApp._I_SystemService.ITEM_LIST_GetList_ITEM_CODE("OCCUPY_PERCENT");

                this.cmbOccupyPercent.DisplayMemberPath = "ITEM_LIST_NAME";
                this.cmbOccupyPercent.SelectedValuePath = "ITEM_LIST_CODE";
                this.cmbOccupyPercent.ItemsSource = listITEM_LIST;
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// CELL_MODEL变更，更改其他流程属性
        /// </summary>
        private void cmbCellModel_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            //如果没有加载完成，不处理
            if (this.boolIsLoaded)
            {
                //默认值
                this.LoadDefaultValue();

                //激活注册事件
                if (this.U_CellModelChanged != null)
                {
                    this.U_CellModelChanged();
                }
            }
        }

        #endregion



        #region     ------START_POSITION绑定及变更
        
        /// <summary>
        /// 根据流程加载起始位置
        /// </summary>
        private void START_POSITION_BIND()
        {

            this.cmbStartPosition.DisplayMemberPath = "CELL_NAME";
            this.cmbStartPosition.SelectedValuePath = "CELL_ID";


            if (this.U_StartCellType != Enum.CELL_TYPE.Cell.ToString())
            {
                this.cmbStartPosition.ItemsSource = MainApp._I_BaseService.GetList(
                                                                                  string.Format("select * from wh_cell where cell_flag=1 {0} {1}",
                                                                                  this.U_StartCellType == string.Empty ? string.Empty : string.Format(" and cell_type like '%{0}%' ", this.U_StartCellType),
                                                                                  this.U_StartCellInOut == string.Empty ? string.Empty : string.Format(" and cell_inout like '%{0}%' ", this.U_StartCellInOut))
                                                                                  ).DefaultView;
            }
            //设置起始位置
            this.SetStartPositionDefault();
            //判断是否显示选择位置的按钮
            btnStartPosition.Visibility = (this.boolAllowChooseStartPosition && cmbStartPosition.HasItems) ? Visibility.Visible : System.Windows.Visibility.Hidden;
        }

        /// <summary>
        /// 设置默认起始位置值
        /// </summary>
        private void SetStartPositionDefault()
        {
            if (this.cmbStartPosition.HasItems && this.cmbStartPosition.SelectedValue == null)
            {
                if (this.strStartPositionDefault != string.Empty)
                {
                    Model.WH_CELL mSTART_CELL = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_CELL_CODE", strStartPositionDefault).RequestObject;
                    if (mSTART_CELL != null)
                        this.cmbStartPosition.SelectedValue = mSTART_CELL.CELL_ID;
                }
            }
        }

        /// <summary>
        /// 起始位置变更
        /// </summary>
        private void cmbStartPosition_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.boolIsLoaded)
            {
                //判断是否注册相应的事件
                if (this.U_StartPositionChanged != null)
                {
                    this.U_StartPositionChanged();
                }
            }
        }

        #endregion

        #region     ------END_POSITION绑定及变更

        /// <summary>
        /// 根据流程加载目标位置
        /// </summary>
        private void END_POSITION_BIND()
        {
            this.cmbEndPosition.DisplayMemberPath = "CELL_NAME";
            this.cmbEndPosition.SelectedValuePath = "CELL_ID";

            if (this.U_EndCellType != Enum.CELL_TYPE.Cell.ToString())
            {
                this.cmbEndPosition.ItemsSource = MainApp._I_BaseService.GetList(
                                                                                  string.Format("select * from wh_cell where CELL_FLAG=1 {0} {1}",
                                                                                  this.U_EndCellType == string.Empty ? string.Empty : string.Format(" and cell_type like '%{0}%' ", this.U_EndCellType),
                                                                                  this.U_EndCellInOut == string.Empty ? string.Empty : string.Format(" and cell_inout like '%{0}%' ", this.U_EndCellInOut))
                                                                                  ).DefaultView;
            }
            //设置目标位置
            this.SetEndPositionDefault();

            //判断是否显示选择位置的按钮
            btnEndPosition.Visibility = (this.boolAllowChooseEndPosition && cmbEndPosition.Items.Count > 0) ? Visibility.Visible : System.Windows.Visibility.Hidden;
        }

        /// <summary>
        /// 设置默认目标位置值
        /// </summary>
        private void SetEndPositionDefault()
        {
            if (this.cmbEndPosition.HasItems && this.cmbEndPosition.SelectedValue == null)
            {
                if (this.strEndPositionDefault != string.Empty)
                {
                    Model.WH_CELL mEND_CELL = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_CELL_CODE", strEndPositionDefault).RequestObject;
                    if (mEND_CELL != null)
                        this.cmbEndPosition.SelectedValue = mEND_CELL.CELL_ID;
                }

            }
        }

        /// <summary>
        /// 目标位置变更
        /// </summary>
        private void cmbEndPosition_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            if (this.boolIsLoaded)
            {
                //判断是否注册相应的事件
                if (this.U_EndPositionChanged != null)
                {
                    this.U_EndPositionChanged();
                }
            }
        }

        #endregion

        #region     ------选择起始位置、目标位置

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnStartPosition":
                        this.SelectStartPosition();
                        break;
                    case "btnEndPosition":
                        this.SelectEndPosition();
                        break;
                }
            }
        }

        /// <summary>
        /// 选择起始位置
        /// </summary>
        private void SelectStartPosition()
        {
            //判断可选择货位

            try
            {
                //获得起始库区
                //Model.WH_AREA mWH_AREA_Start = MainApp._I_CellService.AREA_GetModel(Convert.ToInt32(this.cmbStartArea.SelectedValue));
                //获得选定的货位
                Model.WH_CELL mWH_CELL_Selected = new Model.WH_CELL();// cmbStartPosition.SelectedValue == null ? null : MainApp._I_CellService.CELL_GetModel(Convert.ToInt32(cmbStartPosition.SelectedValue));
                mWH_CELL_Selected.CELL_STATUS = this.U_StartPositionCellStatus;
                WH.WAREHOUSE_CELL_SELECT window = new WH.WAREHOUSE_CELL_SELECT(this.U_WarehouseType, mWH_CELL_Selected);
                if (window.ShowDialog() == true)
                {
                    SiaSun.LMS.Model.WH_CELL mWH_CELL = window.U_WH_CELL;
                    if (mWH_CELL != null)
                    {
                        this.cmbStartPosition.ItemsSource = MainApp._I_BaseService.GetList(
                                                              string.Format("select * from wh_cell where cell_id={0}",
                                                              mWH_CELL.CELL_ID)
                                                              ).DefaultView;


                        cmbStartPosition.SelectedValue = mWH_CELL.CELL_ID;

                        //判断是否正确
                        if (cmbStartPosition.SelectedValue == null)
                        {
                            MainApp._MessageDialog.Show("CheckStartPosition", null);
                            return;
                        }
                        else
                        {
                            //触发事件
                            if (this.U_StartPositionChanged != null)
                            {
                                this.U_StartPositionChanged();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }

        }

        /// <summary>
        /// 选择目标位置
        /// </summary>
        private void SelectEndPosition()
        {

                try
                {
                    //获得起始库区
                    //Model.WH_AREA mWH_AREA_End = MainApp._I_CellService.AREA_GetModel(Convert.ToInt32(this.cmbEndArea.SelectedValue));
                    //获得选定的货位
                    Model.WH_CELL mWH_CELL_Selected = new Model.WH_CELL();// MainApp._I_CellService.CELL_GetModel(Convert.ToInt32(cmbEndPosition.SelectedValue));
                    mWH_CELL_Selected.CELL_STATUS = this.U_EndPositionCellStatus;
                    WH.WAREHOUSE_CELL_SELECT window = new WH.WAREHOUSE_CELL_SELECT(this.U_WarehouseType, mWH_CELL_Selected);
                    if (window.ShowDialog() == true)
                    {
                        SiaSun.LMS.Model.WH_CELL mWH_CELL = window.U_WH_CELL;
                        if (mWH_CELL != null)
                        {

                            this.cmbEndPosition.ItemsSource = MainApp._I_BaseService.GetList(
                                                                  string.Format("select * from wh_cell where cell_id={0}",
                                                                  mWH_CELL.CELL_ID)
                                                                  ).DefaultView;
                            

                            cmbEndPosition.SelectedValue = mWH_CELL.CELL_ID;

                            //判断是否正确
                            if (cmbEndPosition.SelectedValue == null)
                            {
                                MainApp._MessageDialog.Show(Enum.MessageConverter.CheckEndPosition);
                                return;
                            }
                            else
                            {
                                //触发事件
                                if (this.U_EndPositionChanged != null)
                                {
                                    this.U_EndPositionChanged();
                                }
                            }
                        }
                    }
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }

        }

        #endregion

        #region     -------设置各个属性值

        /// <summary>
        /// 设置货位规格值
        /// </summary>
        public void U_SetCellModel(string CELL_MODEL)
        {
            this.cmbCellModel.SelectedValue = CELL_MODEL;
        }


        #endregion

        #region     ------校验填写的值是否合法

        //校验信息是否正确
        public bool U_CHECK_WAREHOUSE()
        {
            Enum.MessageConverter messageType;
            //货位规格
            if (this.boolAllowShowCellModel && this.cmbCellModel.SelectedValue == null)
            {
                messageType = Enum.MessageConverter.CheckCellModel;
                MainApp._MessageDialog.Show(messageType);
                return false;
            }
            //托盘占用百分比
            if (this.boolAllowShowOccupyPercent && this.cmbOccupyPercent.SelectedValue == null)
            {
                messageType = Enum.MessageConverter.CheckOccupyPercent;
                MainApp._MessageDialog.Show(messageType);
                return false;
            }

            //起始位置
            if (boolShowStartPostion && !boolAllowAutoStartPostion && cmbStartPosition.SelectedValue == null)
            {
                messageType = Enum.MessageConverter.CheckStartPosition;
                MainApp._MessageDialog.Show(messageType);
                return false;
            }

            //终止位置
            if (boolShowEndPosition && !boolAllowAutoEndPostion && cmbEndPosition.SelectedValue == null)
            {
                messageType = Enum.MessageConverter.CheckEndPosition;
                MainApp._MessageDialog.Show(messageType);
                return false;
            }

            if (boolShowStockBarcode && !SiaSun.LMS.Common.RegexValid.GetCodeCheck(this.txtStockBarcode.Text, "StockCodeCheck"))
            {
                messageType = Enum.MessageConverter.CheckStockBarCode;
                MainApp._MessageDialog.Show(messageType);
                return false;
            }
            return true;
        }

        /// <summary>
        /// 检查起始位置是否合法,检查货位编码是否等于设备编码
        /// </summary>
        public bool U_CheckStartPosition(string START_POSITION_CODE)
        {
            bool boolResult = false;
            using (DataTable tableStartPosition = (this.cmbStartPosition.ItemsSource as DataView).Table)
            {
                if (tableStartPosition != null)
                {
                    boolResult = tableStartPosition.Select(string.Format("DEVICE_CODE='{0}'", START_POSITION_CODE)).Length > 0;
                }
            }
            return boolResult;
        }

        /// <summary>
        /// 检查目标位置是否合法,检查货位编码是否等于设备编码
        /// </summary>
        public bool U_CheckEndPosition(string END_POSITION_CODE)
        {
            bool boolResult = false;
            using (DataTable tableEndPosition = (this.cmbEndPosition.ItemsSource as DataView).Table)
            {
                if (tableEndPosition != null)
                {
                    boolResult = tableEndPosition.Select(string.Format("DEVICE_CODE='{0}'", END_POSITION_CODE)).Length > 0;
                }
            }
            return boolResult;
        }

        #endregion

        /// <summary>
        /// 刷新控件显示
        /// </summary>
        public void U_Update()
        {
            this.LoadDefaultValue();
        }

        /// <summary>
        /// 清除操作
        /// </summary>
        public void U_Clear()
        {
            this.boolIsLoaded = false;
            this.cmbCellModel.ItemsSource = null;
            this.DataBindClear();
            this.boolIsLoaded = true;
        }

        private void StockBarcode_KeyDown(object sender, KeyEventArgs e)
        {
            if (this.U_StockBarcodeKeyDown != null && e.Key == Key.Enter)
            {
                this.U_StockBarcodeKeyDown();
            }
        }

        private void cmbOccupyPercent_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {

        }

        /// <summary>
        /// 获得拆分属性数据源
        /// </summary>
        private DataTable GetGoodsPropertyTable(string splitpropertykey)
        {
            DataTable tableSplitSource = MainApp._I_BaseService.GetList(string.Format("select * from goods_property where goods_property_flag='1'and goods_type_id = {0} order by goods_property_order", splitpropertykey));
            //设置别名
            tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Column";
            tableSplitSource.Columns["GOODS_PROPERTY_NAME"].ColumnName = "Header";
            tableSplitSource.Columns["GOODS_PROPERTY_DATASOURCE"].ColumnName = "DataBind";
            tableSplitSource.Columns["GOODS_PROPERTY_FIELDTYPE"].ColumnName = "ControlType";
            //tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Field";
            tableSplitSource.Columns["GOODS_PROPERTY_VALID"].ColumnName = "Validation";

            return tableSplitSource;
        }
    }
}
