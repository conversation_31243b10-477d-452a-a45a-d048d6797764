﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_MOVE.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_UP : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        /// <summary>
        /// 构造函数
        /// </summary>
        public MANAGE_UP(string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            this.mMANAGE_TYPE = (Model.MANAGE_TYPE)MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;
        }
        /// <summary>
        /// 窗体加载
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitManagePosotion();

            this.ucManagePosition.U_StockBarcodeKeyDown += new UC.ucManagePosition.U_StockBarcodeKeyDownHandler(ucManagePosition_U_StockBarcodeKeyDown);

        }

        void ucManagePosition_U_StockBarcodeKeyDown()
        {
            //校验托盘条码是否合法
            if (!SiaSun.LMS.Common.RegexValid.GetCodeCheck(this.ucManagePosition.U_STOCK_BARCODE, "StockCodeCheck"))
            {
                MainApp._MessageDialog.Show(Enum.MessageConverter.CheckStockBarCode, this.ucManagePosition.U_STOCK_BARCODE);
                return;
            }

            //显示库存
            StorageListBind();
        }

        /// <summary>
        /// 初始化输送位置控件
        /// </summary>
        private void InitManagePosotion()
        {
            //设置输送任务控件参数
            this.ucManagePosition.U_AllowAutoEndPostion = true;
            this.ucManagePosition.U_AllowShowCellModel = false;
            this.ucManagePosition.U_AllowAutoStartPostion = false;
            this.ucManagePosition.U_AllowShowOccupyPercent = false;
            //初始化
            this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);

        }

        /// <summary>
        /// 库存明细绑定
        /// </summary>
        private void StorageListBind()
        {
            this.gridStorageList.U_WindowName = this.GetType().Name;
            this.gridStorageList.U_TableName = "V_STORAGE_LIST";
            this.gridStorageList.U_XmlTableName = "V_STORAGE_LIST";
            this.gridStorageList.U_TotalColumnName = "STORAGE_LIST_QUANTITY";
            this.gridStorageList.U_OrderField = "STORAGE_LIST_ID";
            this.gridStorageList.U_Where = string.Format("STOCK_BARCODE ='{0}' AND AREA_TYPE= 'XuNiKu'", this.ucManagePosition.U_STOCK_BARCODE) ;
            this.gridStorageList.U_AllowOperatData = false;
            this.gridStorageList.U_AllowChecked = false;
            this.gridStorageList.U_AllowShowPage = false;

            //拆分列属性
            this.gridStorageList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridStorageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridStorageList.U_SplitGroupHeader = "GOODS_TYPE.GOODS_TYPE_NAME";
            this.gridStorageList.U_SplitPropertyColumn = "GOODS_PROPERTY";

            this.gridStorageList.U_InitControl();
        }

        /// <summary>
        /// 点击按钮确认
        /// </summary>
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnConfirm":
                        this.CreateTask();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 创建输送任务
        /// </summary>
        private void CreateTask()
        {
            bool bResult = false;
            string strResult = string.Empty;

            try
            {
              
                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                if (this.gridStorageList.U_DataSource == null || this.gridStorageList.U_DataSource.Rows.Count == 0)
                {
                    MainApp._MessageDialog.Show(string.Format("{0}未找到库存",this.ucManagePosition.U_STOCK_BARCODE));
                    return ;
                }

                //提示确认
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmCreateTask, this.ucManagePosition.U_STOCK_BARCODE) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();
                    
                    mMANAGE_MAIN.CELL_MODEL = this.gridStorageList.U_DataSource.Rows[0]["CELL_MODEL"].ToString();
                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;
                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                    mMANAGE_MAIN.MANAGE_LEVEL = string.IsNullOrEmpty(MainApp._SYS_PARAMETER["ManualOutLevel"]) ? "0" : MainApp._SYS_PARAMETER["ManualOutLevel"];
                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;
                    mMANAGE_MAIN.MANAGE_RELATE_CODE = "";
                    mMANAGE_MAIN.MANAGE_SOURCE = Enum.TASK_SOURCE.WES.ToString();
                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();
                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();
                    mMANAGE_MAIN.PLAN_ID = 0;
                    mMANAGE_MAIN.PLAN_TYPE_CODE = "";
                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;
                    mMANAGE_MAIN.STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE;

                    if (mMANAGE_MAIN.END_CELL_ID != 0)
                    {
                        //验证高低货位
                        Model.WH_CELL mWH_CELL_END = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", mMANAGE_MAIN.END_CELL_ID).RequestObject;
                        if (mWH_CELL_END == null)
                        {
                            MainApp._MessageDialog.ShowResult(false, "用户指定终点位置不合法");
                            return;
                        }
                        //if (mMANAGE_MAIN.CELL_MODEL == "Height" && mWH_CELL_END.CELL_MODEL == "Height%")
                        //{
                        //    MainApp._MessageDialog.ShowResult(false, "高尺寸物料在指定终点位置时请选择高货位");
                        //    return;
                        //}
                        //验证库区
                        Model.WH_CELL mWH_CELL_START = (Model.WH_CELL)MainApp._I_BaseService.GetModel("WH_CELL_SELECT_BY_ID", mMANAGE_MAIN.START_CELL_ID).RequestObject;
                        if (mWH_CELL_START == null)
                        {
                            MainApp._MessageDialog.ShowResult(false, "起始位置位置不合法");
                            return;
                        }
                        if (mWH_CELL_START.WAREHOUSE_ID != mWH_CELL_END.WAREHOUSE_ID)
                        {
                            MainApp._MessageDialog.ShowResult(false, "指定终点位置的库区不正确");
                            return;
                        }
                    }

                    bResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(), 
                                                              "ManageCreate", 
                                                               new object[] {mMANAGE_MAIN,
                                                                              true, 
                                                                              this.ucManagePosition.U_AutoDownloadControlTask, 
                                                                              this.ucManagePosition.U_AutoCompleteTask }, 
                                                               out strResult);


                    //检验执行结果
                    if (bResult)
                    {
                        this.gridStorageList.U_Update();
                    }

                    MainApp._MessageDialog.ShowResult(bResult, strResult);

                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        /// <summary>
        /// 刷新
        /// </summary>
        private void Refresh()
        {
            //校验填写仓库信息是否合法
            if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                return;

            this.StorageListBind();
        }


    }
}
