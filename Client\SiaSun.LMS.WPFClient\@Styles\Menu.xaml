﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:d="http://schemas.microsoft.com/expression/blend/2008"
                    xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006"
                    mc:Ignorable="d">

  <ResourceDictionary.MergedDictionaries>
    <ResourceDictionary Source="Shared.xaml" />
  </ResourceDictionary.MergedDictionaries>
 
    <!--默认ContextMenu样式-->
    <Style TargetType="{x:Type ContextMenu}">
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Grid.IsSharedSizeScope" Value="true"/>
        <Setter Property="HasDropShadow" Value="True"/>
        <Setter Property="Padding" Value="2,1,5,1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContextMenu}">
                    <Border  Name="Border"   BorderBrush="DarkBlue"   BorderThickness="1" >
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"></ColumnDefinition>
                                <ColumnDefinition Width="Auto"></ColumnDefinition>
                            </Grid.ColumnDefinitions>
                            <Rectangle Grid.Column="0" Width="15">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="1,0" EndPoint="0,0">
                                        <GradientStop Color="Black" Offset="0.1"></GradientStop>
                                        <GradientStop Color="White" Offset="1"></GradientStop>
                                        <GradientStop></GradientStop>
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <StackPanel Grid.Column="1" IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Cycle" Background="{StaticResource ControlNormalBackGround}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasDropShadow" Value="true">
                            <Setter TargetName="Border" Property="Padding" Value="0,3,0,3"/>
                            <Setter TargetName="Border" Property="CornerRadius" Value="4"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type Menu}">
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Menu}">
                    <Grid>
                        <Border Margin="1" x:Name="Border" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}" />
                        <StackPanel IsItemsHost="True" ClipToBounds="True" Orientation="Horizontal" Background="{DynamicResource TabControlHeaderBrush}" />
                        <Border x:Name="DisabledVisualElement" IsHitTestVisible="false" Background="#A5FFFFFF" BorderBrush="#66FFFFFF" BorderThickness="1" Opacity="0" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsEnabled" Value="False">
                            <Setter Property="Opacity" TargetName="DisabledVisualElement" Value="1" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <Style TargetType="{x:Type MenuItem}">
        <Setter Property="HorizontalContentAlignment" Value="{Binding Path=HorizontalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}" />
        <Setter Property="VerticalContentAlignment" Value="{Binding Path=VerticalContentAlignment, RelativeSource={RelativeSource AncestorType={x:Type ItemsControl}}}" />
        <Setter Property="Background" Value="Transparent" />
        <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.MenuTextBrushKey}}" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type MenuItem}">
                    <ControlTemplate.Resources>
                        <Storyboard x:Key="HighlightOn">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundGradientOver" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.1000000" Value="1" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                        <Storyboard x:Key="HighlightOff">
                            <DoubleAnimationUsingKeyFrames BeginTime="00:00:00" Storyboard.TargetName="BackgroundGradientOver" Storyboard.TargetProperty="(UIElement.Opacity)">
                                <SplineDoubleKeyFrame KeyTime="00:00:00.3000000" Value="0" />
                            </DoubleAnimationUsingKeyFrames>
                        </Storyboard>
                    </ControlTemplate.Resources>
                    <Border x:Name="Border" Background="{TemplateBinding Background}" BorderBrush="{TemplateBinding BorderBrush}" BorderThickness="{TemplateBinding BorderThickness}">
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition MinWidth="17" Width="Auto" SharedSizeGroup="MenuItemIconColumnGroup" />
                                <ColumnDefinition Width="*" />
                                <ColumnDefinition Width="Auto" SharedSizeGroup="MenuItemIGTColumnGroup" />
                                <ColumnDefinition Width="14" />
                            </Grid.ColumnDefinitions>

                            <ContentPresenter Margin="4,0,6,0" x:Name="Icon" VerticalAlignment="Center" ContentSource="Icon" />

                            <Grid Visibility="Hidden" Margin="4,0,6,0" x:Name="GlyphPanel" VerticalAlignment="Center">
                                <Path x:Name="GlyphPanelpath" VerticalAlignment="Center" Fill="{TemplateBinding Foreground}" Data="M0,2 L0,4.8 L2.5,7.4 L7.1,2.8 L7.1,0 L2.5,4.6 z" FlowDirection="LeftToRight" />
                            </Grid>
                            <Rectangle Grid.Column="0" Grid.ColumnSpan="4" x:Name="BackgroundGradientOver" Stroke="#FFDBCE99" Opacity="0" Fill="{DynamicResource MouseOverBrush}"/>

                            <ContentPresenter Grid.Column="1" Margin="{TemplateBinding Padding}" x:Name="HeaderHost" RecognizesAccessKey="True" ContentSource="Header" />

                            <Grid Grid.Column="3" Margin="4,0,6,0" x:Name="ArrowPanel" VerticalAlignment="Center">
                                <Path x:Name="ArrowPanelPath" VerticalAlignment="Center" Fill="{TemplateBinding Foreground}" Data="M0,0 L0,8 L4,4 z" />
                            </Grid>

                            <Popup IsOpen="{Binding Path=IsSubmenuOpen, RelativeSource={RelativeSource TemplatedParent}}" Placement="Right" x:Name="SubMenuPopup" Focusable="false" AllowsTransparency="true" PopupAnimation="{DynamicResource {x:Static SystemParameters.MenuPopupAnimationKey}}" VerticalOffset="-3">
                                <Grid x:Name="SubMenu">
                                    <Border x:Name="SubMenuBorder" BorderBrush="{DynamicResource ControlBorderBrush}" BorderThickness="1" >
                                        <Border.Background>
                                            <SolidColorBrush Color="{DynamicResource WhiteColor}"/>
                                        </Border.Background>
                                    </Border>
                                    <StackPanel IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Cycle" />
                                </Grid>
                            </Popup>
                        </Grid>
                    </Border>

                    <ControlTemplate.Triggers>
                        <Trigger Property="Role" Value="TopLevelHeader">
                            <Setter Property="Margin" Value="0,1,0,1" />
                            <Setter Property="Padding" Value="6,3,6,3" />
                            <Setter Property="Grid.IsSharedSizeScope" Value="true" />
                            <Setter Property="Placement" Value="Bottom" TargetName="SubMenuPopup" />
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel" />
                        </Trigger>

                        <Trigger Property="Role" Value="TopLevelItem">
                            <Setter Property="Margin" Value="0,1,0,1" />
                            <Setter Property="Padding" Value="6,3,6,3" />
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel" />
                        </Trigger>

                        <Trigger Property="Role" Value="SubmenuHeader">
                            <Setter Property="DockPanel.Dock" Value="Top" />
                            <Setter Property="Padding" Value="0,2,0,2" />
                            <Setter Property="Grid.IsSharedSizeScope" Value="true" />
                        </Trigger>

                        <Trigger Property="Role" Value="SubmenuItem">
                            <Setter Property="DockPanel.Dock" Value="Top" />
                            <Setter Property="Padding" Value="0,2,0,2" />
                            <Setter Property="Visibility" Value="Collapsed" TargetName="ArrowPanel" />
                        </Trigger>
                        <Trigger Property="IsSuspendingPopupAnimation" Value="true">
                            <Setter Property="PopupAnimation" Value="None" TargetName="SubMenuPopup" />
                        </Trigger>

                        <Trigger Property="Icon" Value="{x:Null}">
                            <Setter Property="Visibility" Value="Collapsed" TargetName="Icon" />
                        </Trigger>

                        <Trigger Property="IsChecked" Value="true">
                            <Setter Property="Visibility" Value="Visible" TargetName="GlyphPanel" />
                            <Setter Property="Visibility" Value="Collapsed" TargetName="Icon" />
                        </Trigger>

                        <Trigger Property="AllowsTransparency" SourceName="SubMenuPopup" Value="true">
                            <Setter Property="Margin" Value="0,0,3,3" TargetName="SubMenu" />
                            <Setter Property="SnapsToDevicePixels" Value="true" TargetName="SubMenu" />
                            <Setter Property="BitmapEffect" Value="{DynamicResource PopupDropShadow}" TargetName="SubMenuBorder" />
                        </Trigger>

                        <Trigger Property="IsHighlighted" Value="true">
                            <Trigger.ExitActions>
                                <BeginStoryboard Storyboard="{StaticResource HighlightOff}" x:Name="HighlightOff_BeginStoryboard" />
                            </Trigger.ExitActions>
                            <Trigger.EnterActions>
                                <BeginStoryboard Storyboard="{StaticResource HighlightOn}" />
                            </Trigger.EnterActions>
                        </Trigger>
                        <Trigger Property="IsEnabled" Value="false">
                            <Setter Property="Foreground" Value="{DynamicResource {x:Static SystemColors.GrayTextBrushKey}}" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

</ResourceDictionary>