﻿//------------------------------------------------------------------------------
// <auto-generated>
//     此代码由工具生成。
//     运行时版本:4.0.30319.18063
//
//     对此文件的更改可能会导致不正确的行为，并且如果
//     重新生成代码，这些更改将会丢失。
// </auto-generated>
//------------------------------------------------------------------------------

#pragma warning disable 1591

namespace SiaSun.LMS.WPFClient.REPORT {
    
    
    /// <summary>
    ///Represents a strongly typed in-memory cache of data.
    ///</summary>
    [global::System.Serializable()]
    [global::System.ComponentModel.DesignerCategoryAttribute("code")]
    [global::System.ComponentModel.ToolboxItem(true)]
    [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedDataSetSchema")]
    [global::System.Xml.Serialization.XmlRootAttribute("Report")]
    [global::System.ComponentModel.Design.HelpKeywordAttribute("vs.data.DataSet")]
    public partial class Report : global::System.Data.DataSet {
        
        private V_GOODS_OUTDataTable tableV_GOODS_OUT;
        
        private InOutQueryDataTable tableInOutQuery;
        
        private global::System.Data.SchemaSerializationMode _schemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public Report() {
            this.BeginInit();
            this.InitClass();
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            base.Relations.CollectionChanged += schemaChangedHandler;
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected Report(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                base(info, context, false) {
            if ((this.IsBinarySerialized(info, context) == true)) {
                this.InitVars(false);
                global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler1 = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
                this.Tables.CollectionChanged += schemaChangedHandler1;
                this.Relations.CollectionChanged += schemaChangedHandler1;
                return;
            }
            string strSchema = ((string)(info.GetValue("XmlSchema", typeof(string))));
            if ((this.DetermineSchemaSerializationMode(info, context) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
                if ((ds.Tables["V_GOODS_OUT"] != null)) {
                    base.Tables.Add(new V_GOODS_OUTDataTable(ds.Tables["V_GOODS_OUT"]));
                }
                if ((ds.Tables["InOutQuery"] != null)) {
                    base.Tables.Add(new InOutQueryDataTable(ds.Tables["InOutQuery"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXmlSchema(new global::System.Xml.XmlTextReader(new global::System.IO.StringReader(strSchema)));
            }
            this.GetSerializationData(info, context);
            global::System.ComponentModel.CollectionChangeEventHandler schemaChangedHandler = new global::System.ComponentModel.CollectionChangeEventHandler(this.SchemaChanged);
            base.Tables.CollectionChanged += schemaChangedHandler;
            this.Relations.CollectionChanged += schemaChangedHandler;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public V_GOODS_OUTDataTable V_GOODS_OUT {
            get {
                return this.tableV_GOODS_OUT;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.Browsable(false)]
        [global::System.ComponentModel.DesignerSerializationVisibility(global::System.ComponentModel.DesignerSerializationVisibility.Content)]
        public InOutQueryDataTable InOutQuery {
            get {
                return this.tableInOutQuery;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.BrowsableAttribute(true)]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Visible)]
        public override global::System.Data.SchemaSerializationMode SchemaSerializationMode {
            get {
                return this._schemaSerializationMode;
            }
            set {
                this._schemaSerializationMode = value;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataTableCollection Tables {
            get {
                return base.Tables;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        [global::System.ComponentModel.DesignerSerializationVisibilityAttribute(global::System.ComponentModel.DesignerSerializationVisibility.Hidden)]
        public new global::System.Data.DataRelationCollection Relations {
            get {
                return base.Relations;
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override void InitializeDerivedDataSet() {
            this.BeginInit();
            this.InitClass();
            this.EndInit();
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public override global::System.Data.DataSet Clone() {
            Report cln = ((Report)(base.Clone()));
            cln.InitVars();
            cln.SchemaSerializationMode = this.SchemaSerializationMode;
            return cln;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override bool ShouldSerializeTables() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override bool ShouldSerializeRelations() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override void ReadXmlSerializable(global::System.Xml.XmlReader reader) {
            if ((this.DetermineSchemaSerializationMode(reader) == global::System.Data.SchemaSerializationMode.IncludeSchema)) {
                this.Reset();
                global::System.Data.DataSet ds = new global::System.Data.DataSet();
                ds.ReadXml(reader);
                if ((ds.Tables["V_GOODS_OUT"] != null)) {
                    base.Tables.Add(new V_GOODS_OUTDataTable(ds.Tables["V_GOODS_OUT"]));
                }
                if ((ds.Tables["InOutQuery"] != null)) {
                    base.Tables.Add(new InOutQueryDataTable(ds.Tables["InOutQuery"]));
                }
                this.DataSetName = ds.DataSetName;
                this.Prefix = ds.Prefix;
                this.Namespace = ds.Namespace;
                this.Locale = ds.Locale;
                this.CaseSensitive = ds.CaseSensitive;
                this.EnforceConstraints = ds.EnforceConstraints;
                this.Merge(ds, false, global::System.Data.MissingSchemaAction.Add);
                this.InitVars();
            }
            else {
                this.ReadXml(reader);
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        protected override global::System.Xml.Schema.XmlSchema GetSchemaSerializable() {
            global::System.IO.MemoryStream stream = new global::System.IO.MemoryStream();
            this.WriteXmlSchema(new global::System.Xml.XmlTextWriter(stream, null));
            stream.Position = 0;
            return global::System.Xml.Schema.XmlSchema.Read(new global::System.Xml.XmlTextReader(stream), null);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        internal void InitVars() {
            this.InitVars(true);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        internal void InitVars(bool initTable) {
            this.tableV_GOODS_OUT = ((V_GOODS_OUTDataTable)(base.Tables["V_GOODS_OUT"]));
            if ((initTable == true)) {
                if ((this.tableV_GOODS_OUT != null)) {
                    this.tableV_GOODS_OUT.InitVars();
                }
            }
            this.tableInOutQuery = ((InOutQueryDataTable)(base.Tables["InOutQuery"]));
            if ((initTable == true)) {
                if ((this.tableInOutQuery != null)) {
                    this.tableInOutQuery.InitVars();
                }
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private void InitClass() {
            this.DataSetName = "Report";
            this.Prefix = "";
            this.Namespace = "http://tempuri.org/Report.xsd";
            this.EnforceConstraints = true;
            this.SchemaSerializationMode = global::System.Data.SchemaSerializationMode.IncludeSchema;
            this.tableV_GOODS_OUT = new V_GOODS_OUTDataTable();
            base.Tables.Add(this.tableV_GOODS_OUT);
            this.tableInOutQuery = new InOutQueryDataTable();
            base.Tables.Add(this.tableInOutQuery);
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private bool ShouldSerializeV_GOODS_OUT() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private bool ShouldSerializeInOutQuery() {
            return false;
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        private void SchemaChanged(object sender, global::System.ComponentModel.CollectionChangeEventArgs e) {
            if ((e.Action == global::System.ComponentModel.CollectionChangeAction.Remove)) {
                this.InitVars();
            }
        }
        
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedDataSetSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
            Report ds = new Report();
            global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
            global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
            global::System.Xml.Schema.XmlSchemaAny any = new global::System.Xml.Schema.XmlSchemaAny();
            any.Namespace = ds.Namespace;
            sequence.Items.Add(any);
            type.Particle = sequence;
            global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
            if (xs.Contains(dsSchema.TargetNamespace)) {
                global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                try {
                    global::System.Xml.Schema.XmlSchema schema = null;
                    dsSchema.Write(s1);
                    for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                        schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                        s2.SetLength(0);
                        schema.Write(s2);
                        if ((s1.Length == s2.Length)) {
                            s1.Position = 0;
                            s2.Position = 0;
                            for (; ((s1.Position != s1.Length) 
                                        && (s1.ReadByte() == s2.ReadByte())); ) {
                                ;
                            }
                            if ((s1.Position == s1.Length)) {
                                return type;
                            }
                        }
                    }
                }
                finally {
                    if ((s1 != null)) {
                        s1.Close();
                    }
                    if ((s2 != null)) {
                        s2.Close();
                    }
                }
            }
            xs.Add(dsSchema);
            return type;
        }
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public delegate void V_GOODS_OUTRowChangeEventHandler(object sender, V_GOODS_OUTRowChangeEvent e);
        
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public delegate void InOutQueryRowChangeEventHandler(object sender, InOutQueryRowChangeEvent e);
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class V_GOODS_OUTDataTable : global::System.Data.TypedTableBase<V_GOODS_OUTRow> {
            
            private global::System.Data.DataColumn columnGOODS_NAME;
            
            private global::System.Data.DataColumn columnRECORD_LIST_QUANTITY;
            
            private global::System.Data.DataColumn columnQUANTITY;
            
            private global::System.Data.DataColumn columnGOODS_CODE;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY1;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY2;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY3;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY4;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY5;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY6;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY7;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY8;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public V_GOODS_OUTDataTable() {
                this.TableName = "V_GOODS_OUT";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal V_GOODS_OUTDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected V_GOODS_OUTDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_NAMEColumn {
                get {
                    return this.columnGOODS_NAME;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn RECORD_LIST_QUANTITYColumn {
                get {
                    return this.columnRECORD_LIST_QUANTITY;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn QUANTITYColumn {
                get {
                    return this.columnQUANTITY;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_CODEColumn {
                get {
                    return this.columnGOODS_CODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY1Column {
                get {
                    return this.columnGOODS_PROPERTY1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY2Column {
                get {
                    return this.columnGOODS_PROPERTY2;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY3Column {
                get {
                    return this.columnGOODS_PROPERTY3;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY4Column {
                get {
                    return this.columnGOODS_PROPERTY4;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY5Column {
                get {
                    return this.columnGOODS_PROPERTY5;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY6Column {
                get {
                    return this.columnGOODS_PROPERTY6;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY7Column {
                get {
                    return this.columnGOODS_PROPERTY7;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY8Column {
                get {
                    return this.columnGOODS_PROPERTY8;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public V_GOODS_OUTRow this[int index] {
                get {
                    return ((V_GOODS_OUTRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event V_GOODS_OUTRowChangeEventHandler V_GOODS_OUTRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event V_GOODS_OUTRowChangeEventHandler V_GOODS_OUTRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event V_GOODS_OUTRowChangeEventHandler V_GOODS_OUTRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event V_GOODS_OUTRowChangeEventHandler V_GOODS_OUTRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void AddV_GOODS_OUTRow(V_GOODS_OUTRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public V_GOODS_OUTRow AddV_GOODS_OUTRow(string GOODS_NAME, string RECORD_LIST_QUANTITY, string QUANTITY, string GOODS_CODE, string GOODS_PROPERTY1, string GOODS_PROPERTY2, string GOODS_PROPERTY3, string GOODS_PROPERTY4, string GOODS_PROPERTY5, string GOODS_PROPERTY6, string GOODS_PROPERTY7, string GOODS_PROPERTY8) {
                V_GOODS_OUTRow rowV_GOODS_OUTRow = ((V_GOODS_OUTRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        GOODS_NAME,
                        RECORD_LIST_QUANTITY,
                        QUANTITY,
                        GOODS_CODE,
                        GOODS_PROPERTY1,
                        GOODS_PROPERTY2,
                        GOODS_PROPERTY3,
                        GOODS_PROPERTY4,
                        GOODS_PROPERTY5,
                        GOODS_PROPERTY6,
                        GOODS_PROPERTY7,
                        GOODS_PROPERTY8};
                rowV_GOODS_OUTRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowV_GOODS_OUTRow);
                return rowV_GOODS_OUTRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                V_GOODS_OUTDataTable cln = ((V_GOODS_OUTDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new V_GOODS_OUTDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal void InitVars() {
                this.columnGOODS_NAME = base.Columns["GOODS_NAME"];
                this.columnRECORD_LIST_QUANTITY = base.Columns["RECORD_LIST_QUANTITY"];
                this.columnQUANTITY = base.Columns["QUANTITY"];
                this.columnGOODS_CODE = base.Columns["GOODS_CODE"];
                this.columnGOODS_PROPERTY1 = base.Columns["GOODS_PROPERTY1"];
                this.columnGOODS_PROPERTY2 = base.Columns["GOODS_PROPERTY2"];
                this.columnGOODS_PROPERTY3 = base.Columns["GOODS_PROPERTY3"];
                this.columnGOODS_PROPERTY4 = base.Columns["GOODS_PROPERTY4"];
                this.columnGOODS_PROPERTY5 = base.Columns["GOODS_PROPERTY5"];
                this.columnGOODS_PROPERTY6 = base.Columns["GOODS_PROPERTY6"];
                this.columnGOODS_PROPERTY7 = base.Columns["GOODS_PROPERTY7"];
                this.columnGOODS_PROPERTY8 = base.Columns["GOODS_PROPERTY8"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            private void InitClass() {
                this.columnGOODS_NAME = new global::System.Data.DataColumn("GOODS_NAME", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_NAME);
                this.columnRECORD_LIST_QUANTITY = new global::System.Data.DataColumn("RECORD_LIST_QUANTITY", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnRECORD_LIST_QUANTITY);
                this.columnQUANTITY = new global::System.Data.DataColumn("QUANTITY", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnQUANTITY);
                this.columnGOODS_CODE = new global::System.Data.DataColumn("GOODS_CODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_CODE);
                this.columnGOODS_PROPERTY1 = new global::System.Data.DataColumn("GOODS_PROPERTY1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY1);
                this.columnGOODS_PROPERTY2 = new global::System.Data.DataColumn("GOODS_PROPERTY2", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY2);
                this.columnGOODS_PROPERTY3 = new global::System.Data.DataColumn("GOODS_PROPERTY3", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY3);
                this.columnGOODS_PROPERTY4 = new global::System.Data.DataColumn("GOODS_PROPERTY4", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY4);
                this.columnGOODS_PROPERTY5 = new global::System.Data.DataColumn("GOODS_PROPERTY5", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY5);
                this.columnGOODS_PROPERTY6 = new global::System.Data.DataColumn("GOODS_PROPERTY6", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY6);
                this.columnGOODS_PROPERTY7 = new global::System.Data.DataColumn("GOODS_PROPERTY7", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY7);
                this.columnGOODS_PROPERTY8 = new global::System.Data.DataColumn("GOODS_PROPERTY8", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY8);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public V_GOODS_OUTRow NewV_GOODS_OUTRow() {
                return ((V_GOODS_OUTRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new V_GOODS_OUTRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(V_GOODS_OUTRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.V_GOODS_OUTRowChanged != null)) {
                    this.V_GOODS_OUTRowChanged(this, new V_GOODS_OUTRowChangeEvent(((V_GOODS_OUTRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.V_GOODS_OUTRowChanging != null)) {
                    this.V_GOODS_OUTRowChanging(this, new V_GOODS_OUTRowChangeEvent(((V_GOODS_OUTRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.V_GOODS_OUTRowDeleted != null)) {
                    this.V_GOODS_OUTRowDeleted(this, new V_GOODS_OUTRowChangeEvent(((V_GOODS_OUTRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.V_GOODS_OUTRowDeleting != null)) {
                    this.V_GOODS_OUTRowDeleting(this, new V_GOODS_OUTRowChangeEvent(((V_GOODS_OUTRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void RemoveV_GOODS_OUTRow(V_GOODS_OUTRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                Report ds = new Report();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "V_GOODS_OUTDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents the strongly named DataTable class.
        ///</summary>
        [global::System.Serializable()]
        [global::System.Xml.Serialization.XmlSchemaProviderAttribute("GetTypedTableSchema")]
        public partial class InOutQueryDataTable : global::System.Data.TypedTableBase<InOutQueryRow> {
            
            private global::System.Data.DataColumn columnGOODS_ID;
            
            private global::System.Data.DataColumn columnGOODS_CODE;
            
            private global::System.Data.DataColumn columnGOODS_NAME;
            
            private global::System.Data.DataColumn columnGOODS_UNIT;
            
            private global::System.Data.DataColumn columnGOODS_CONST_PROPERTY1;
            
            private global::System.Data.DataColumn columnGOODS_CONST_PROPERTY2;
            
            private global::System.Data.DataColumn columnGOODS_CONST_PROPERTY3;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY1;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY2;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY3;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY4;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY5;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY6;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY7;
            
            private global::System.Data.DataColumn columnGOODS_PROPERTY8;
            
            private global::System.Data.DataColumn columnSTORAGE_QUANTITY_START;
            
            private global::System.Data.DataColumn columnIN_QUANTITY_StartToEnd;
            
            private global::System.Data.DataColumn columnOUT_QUANTITY_StartToEnd;
            
            private global::System.Data.DataColumn columnSTORAGE_QUANTITY_END;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public InOutQueryDataTable() {
                this.TableName = "InOutQuery";
                this.BeginInit();
                this.InitClass();
                this.EndInit();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal InOutQueryDataTable(global::System.Data.DataTable table) {
                this.TableName = table.TableName;
                if ((table.CaseSensitive != table.DataSet.CaseSensitive)) {
                    this.CaseSensitive = table.CaseSensitive;
                }
                if ((table.Locale.ToString() != table.DataSet.Locale.ToString())) {
                    this.Locale = table.Locale;
                }
                if ((table.Namespace != table.DataSet.Namespace)) {
                    this.Namespace = table.Namespace;
                }
                this.Prefix = table.Prefix;
                this.MinimumCapacity = table.MinimumCapacity;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected InOutQueryDataTable(global::System.Runtime.Serialization.SerializationInfo info, global::System.Runtime.Serialization.StreamingContext context) : 
                    base(info, context) {
                this.InitVars();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_IDColumn {
                get {
                    return this.columnGOODS_ID;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_CODEColumn {
                get {
                    return this.columnGOODS_CODE;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_NAMEColumn {
                get {
                    return this.columnGOODS_NAME;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_UNITColumn {
                get {
                    return this.columnGOODS_UNIT;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_CONST_PROPERTY1Column {
                get {
                    return this.columnGOODS_CONST_PROPERTY1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_CONST_PROPERTY2Column {
                get {
                    return this.columnGOODS_CONST_PROPERTY2;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_CONST_PROPERTY3Column {
                get {
                    return this.columnGOODS_CONST_PROPERTY3;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY1Column {
                get {
                    return this.columnGOODS_PROPERTY1;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY2Column {
                get {
                    return this.columnGOODS_PROPERTY2;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY3Column {
                get {
                    return this.columnGOODS_PROPERTY3;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY4Column {
                get {
                    return this.columnGOODS_PROPERTY4;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY5Column {
                get {
                    return this.columnGOODS_PROPERTY5;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY6Column {
                get {
                    return this.columnGOODS_PROPERTY6;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY7Column {
                get {
                    return this.columnGOODS_PROPERTY7;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn GOODS_PROPERTY8Column {
                get {
                    return this.columnGOODS_PROPERTY8;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn STORAGE_QUANTITY_STARTColumn {
                get {
                    return this.columnSTORAGE_QUANTITY_START;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn IN_QUANTITY_StartToEndColumn {
                get {
                    return this.columnIN_QUANTITY_StartToEnd;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn OUT_QUANTITY_StartToEndColumn {
                get {
                    return this.columnOUT_QUANTITY_StartToEnd;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataColumn STORAGE_QUANTITY_ENDColumn {
                get {
                    return this.columnSTORAGE_QUANTITY_END;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            [global::System.ComponentModel.Browsable(false)]
            public int Count {
                get {
                    return this.Rows.Count;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public InOutQueryRow this[int index] {
                get {
                    return ((InOutQueryRow)(this.Rows[index]));
                }
            }
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event InOutQueryRowChangeEventHandler InOutQueryRowChanging;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event InOutQueryRowChangeEventHandler InOutQueryRowChanged;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event InOutQueryRowChangeEventHandler InOutQueryRowDeleting;
            
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public event InOutQueryRowChangeEventHandler InOutQueryRowDeleted;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void AddInOutQueryRow(InOutQueryRow row) {
                this.Rows.Add(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public InOutQueryRow AddInOutQueryRow(
                        string GOODS_ID, 
                        string GOODS_CODE, 
                        string GOODS_NAME, 
                        string GOODS_UNIT, 
                        string GOODS_CONST_PROPERTY1, 
                        string GOODS_CONST_PROPERTY2, 
                        string GOODS_CONST_PROPERTY3, 
                        string GOODS_PROPERTY1, 
                        string GOODS_PROPERTY2, 
                        string GOODS_PROPERTY3, 
                        string GOODS_PROPERTY4, 
                        string GOODS_PROPERTY5, 
                        string GOODS_PROPERTY6, 
                        string GOODS_PROPERTY7, 
                        string GOODS_PROPERTY8, 
                        decimal STORAGE_QUANTITY_START, 
                        decimal IN_QUANTITY_StartToEnd, 
                        decimal OUT_QUANTITY_StartToEnd, 
                        decimal STORAGE_QUANTITY_END) {
                InOutQueryRow rowInOutQueryRow = ((InOutQueryRow)(this.NewRow()));
                object[] columnValuesArray = new object[] {
                        GOODS_ID,
                        GOODS_CODE,
                        GOODS_NAME,
                        GOODS_UNIT,
                        GOODS_CONST_PROPERTY1,
                        GOODS_CONST_PROPERTY2,
                        GOODS_CONST_PROPERTY3,
                        GOODS_PROPERTY1,
                        GOODS_PROPERTY2,
                        GOODS_PROPERTY3,
                        GOODS_PROPERTY4,
                        GOODS_PROPERTY5,
                        GOODS_PROPERTY6,
                        GOODS_PROPERTY7,
                        GOODS_PROPERTY8,
                        STORAGE_QUANTITY_START,
                        IN_QUANTITY_StartToEnd,
                        OUT_QUANTITY_StartToEnd,
                        STORAGE_QUANTITY_END};
                rowInOutQueryRow.ItemArray = columnValuesArray;
                this.Rows.Add(rowInOutQueryRow);
                return rowInOutQueryRow;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public override global::System.Data.DataTable Clone() {
                InOutQueryDataTable cln = ((InOutQueryDataTable)(base.Clone()));
                cln.InitVars();
                return cln;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataTable CreateInstance() {
                return new InOutQueryDataTable();
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal void InitVars() {
                this.columnGOODS_ID = base.Columns["GOODS_ID"];
                this.columnGOODS_CODE = base.Columns["GOODS_CODE"];
                this.columnGOODS_NAME = base.Columns["GOODS_NAME"];
                this.columnGOODS_UNIT = base.Columns["GOODS_UNIT"];
                this.columnGOODS_CONST_PROPERTY1 = base.Columns["GOODS_CONST_PROPERTY1"];
                this.columnGOODS_CONST_PROPERTY2 = base.Columns["GOODS_CONST_PROPERTY2"];
                this.columnGOODS_CONST_PROPERTY3 = base.Columns["GOODS_CONST_PROPERTY3"];
                this.columnGOODS_PROPERTY1 = base.Columns["GOODS_PROPERTY1"];
                this.columnGOODS_PROPERTY2 = base.Columns["GOODS_PROPERTY2"];
                this.columnGOODS_PROPERTY3 = base.Columns["GOODS_PROPERTY3"];
                this.columnGOODS_PROPERTY4 = base.Columns["GOODS_PROPERTY4"];
                this.columnGOODS_PROPERTY5 = base.Columns["GOODS_PROPERTY5"];
                this.columnGOODS_PROPERTY6 = base.Columns["GOODS_PROPERTY6"];
                this.columnGOODS_PROPERTY7 = base.Columns["GOODS_PROPERTY7"];
                this.columnGOODS_PROPERTY8 = base.Columns["GOODS_PROPERTY8"];
                this.columnSTORAGE_QUANTITY_START = base.Columns["STORAGE_QUANTITY_START"];
                this.columnIN_QUANTITY_StartToEnd = base.Columns["IN_QUANTITY_StartToEnd"];
                this.columnOUT_QUANTITY_StartToEnd = base.Columns["OUT_QUANTITY_StartToEnd"];
                this.columnSTORAGE_QUANTITY_END = base.Columns["STORAGE_QUANTITY_END"];
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            private void InitClass() {
                this.columnGOODS_ID = new global::System.Data.DataColumn("GOODS_ID", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_ID);
                this.columnGOODS_CODE = new global::System.Data.DataColumn("GOODS_CODE", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_CODE);
                this.columnGOODS_NAME = new global::System.Data.DataColumn("GOODS_NAME", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_NAME);
                this.columnGOODS_UNIT = new global::System.Data.DataColumn("GOODS_UNIT", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_UNIT);
                this.columnGOODS_CONST_PROPERTY1 = new global::System.Data.DataColumn("GOODS_CONST_PROPERTY1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_CONST_PROPERTY1);
                this.columnGOODS_CONST_PROPERTY2 = new global::System.Data.DataColumn("GOODS_CONST_PROPERTY2", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_CONST_PROPERTY2);
                this.columnGOODS_CONST_PROPERTY3 = new global::System.Data.DataColumn("GOODS_CONST_PROPERTY3", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_CONST_PROPERTY3);
                this.columnGOODS_PROPERTY1 = new global::System.Data.DataColumn("GOODS_PROPERTY1", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY1);
                this.columnGOODS_PROPERTY2 = new global::System.Data.DataColumn("GOODS_PROPERTY2", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY2);
                this.columnGOODS_PROPERTY3 = new global::System.Data.DataColumn("GOODS_PROPERTY3", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY3);
                this.columnGOODS_PROPERTY4 = new global::System.Data.DataColumn("GOODS_PROPERTY4", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY4);
                this.columnGOODS_PROPERTY5 = new global::System.Data.DataColumn("GOODS_PROPERTY5", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY5);
                this.columnGOODS_PROPERTY6 = new global::System.Data.DataColumn("GOODS_PROPERTY6", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY6);
                this.columnGOODS_PROPERTY7 = new global::System.Data.DataColumn("GOODS_PROPERTY7", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY7);
                this.columnGOODS_PROPERTY8 = new global::System.Data.DataColumn("GOODS_PROPERTY8", typeof(string), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnGOODS_PROPERTY8);
                this.columnSTORAGE_QUANTITY_START = new global::System.Data.DataColumn("STORAGE_QUANTITY_START", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSTORAGE_QUANTITY_START);
                this.columnIN_QUANTITY_StartToEnd = new global::System.Data.DataColumn("IN_QUANTITY_StartToEnd", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnIN_QUANTITY_StartToEnd);
                this.columnOUT_QUANTITY_StartToEnd = new global::System.Data.DataColumn("OUT_QUANTITY_StartToEnd", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnOUT_QUANTITY_StartToEnd);
                this.columnSTORAGE_QUANTITY_END = new global::System.Data.DataColumn("STORAGE_QUANTITY_END", typeof(decimal), null, global::System.Data.MappingType.Element);
                base.Columns.Add(this.columnSTORAGE_QUANTITY_END);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public InOutQueryRow NewInOutQueryRow() {
                return ((InOutQueryRow)(this.NewRow()));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Data.DataRow NewRowFromBuilder(global::System.Data.DataRowBuilder builder) {
                return new InOutQueryRow(builder);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override global::System.Type GetRowType() {
                return typeof(InOutQueryRow);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanged(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanged(e);
                if ((this.InOutQueryRowChanged != null)) {
                    this.InOutQueryRowChanged(this, new InOutQueryRowChangeEvent(((InOutQueryRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowChanging(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowChanging(e);
                if ((this.InOutQueryRowChanging != null)) {
                    this.InOutQueryRowChanging(this, new InOutQueryRowChangeEvent(((InOutQueryRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleted(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleted(e);
                if ((this.InOutQueryRowDeleted != null)) {
                    this.InOutQueryRowDeleted(this, new InOutQueryRowChangeEvent(((InOutQueryRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            protected override void OnRowDeleting(global::System.Data.DataRowChangeEventArgs e) {
                base.OnRowDeleting(e);
                if ((this.InOutQueryRowDeleting != null)) {
                    this.InOutQueryRowDeleting(this, new InOutQueryRowChangeEvent(((InOutQueryRow)(e.Row)), e.Action));
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void RemoveInOutQueryRow(InOutQueryRow row) {
                this.Rows.Remove(row);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public static global::System.Xml.Schema.XmlSchemaComplexType GetTypedTableSchema(global::System.Xml.Schema.XmlSchemaSet xs) {
                global::System.Xml.Schema.XmlSchemaComplexType type = new global::System.Xml.Schema.XmlSchemaComplexType();
                global::System.Xml.Schema.XmlSchemaSequence sequence = new global::System.Xml.Schema.XmlSchemaSequence();
                Report ds = new Report();
                global::System.Xml.Schema.XmlSchemaAny any1 = new global::System.Xml.Schema.XmlSchemaAny();
                any1.Namespace = "http://www.w3.org/2001/XMLSchema";
                any1.MinOccurs = new decimal(0);
                any1.MaxOccurs = decimal.MaxValue;
                any1.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any1);
                global::System.Xml.Schema.XmlSchemaAny any2 = new global::System.Xml.Schema.XmlSchemaAny();
                any2.Namespace = "urn:schemas-microsoft-com:xml-diffgram-v1";
                any2.MinOccurs = new decimal(1);
                any2.ProcessContents = global::System.Xml.Schema.XmlSchemaContentProcessing.Lax;
                sequence.Items.Add(any2);
                global::System.Xml.Schema.XmlSchemaAttribute attribute1 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute1.Name = "namespace";
                attribute1.FixedValue = ds.Namespace;
                type.Attributes.Add(attribute1);
                global::System.Xml.Schema.XmlSchemaAttribute attribute2 = new global::System.Xml.Schema.XmlSchemaAttribute();
                attribute2.Name = "tableTypeName";
                attribute2.FixedValue = "InOutQueryDataTable";
                type.Attributes.Add(attribute2);
                type.Particle = sequence;
                global::System.Xml.Schema.XmlSchema dsSchema = ds.GetSchemaSerializable();
                if (xs.Contains(dsSchema.TargetNamespace)) {
                    global::System.IO.MemoryStream s1 = new global::System.IO.MemoryStream();
                    global::System.IO.MemoryStream s2 = new global::System.IO.MemoryStream();
                    try {
                        global::System.Xml.Schema.XmlSchema schema = null;
                        dsSchema.Write(s1);
                        for (global::System.Collections.IEnumerator schemas = xs.Schemas(dsSchema.TargetNamespace).GetEnumerator(); schemas.MoveNext(); ) {
                            schema = ((global::System.Xml.Schema.XmlSchema)(schemas.Current));
                            s2.SetLength(0);
                            schema.Write(s2);
                            if ((s1.Length == s2.Length)) {
                                s1.Position = 0;
                                s2.Position = 0;
                                for (; ((s1.Position != s1.Length) 
                                            && (s1.ReadByte() == s2.ReadByte())); ) {
                                    ;
                                }
                                if ((s1.Position == s1.Length)) {
                                    return type;
                                }
                            }
                        }
                    }
                    finally {
                        if ((s1 != null)) {
                            s1.Close();
                        }
                        if ((s2 != null)) {
                            s2.Close();
                        }
                    }
                }
                xs.Add(dsSchema);
                return type;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class V_GOODS_OUTRow : global::System.Data.DataRow {
            
            private V_GOODS_OUTDataTable tableV_GOODS_OUT;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal V_GOODS_OUTRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableV_GOODS_OUT = ((V_GOODS_OUTDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_NAME {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_NAMEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_NAME”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_NAMEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string RECORD_LIST_QUANTITY {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.RECORD_LIST_QUANTITYColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“RECORD_LIST_QUANTITY”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.RECORD_LIST_QUANTITYColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string QUANTITY {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.QUANTITYColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“QUANTITY”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.QUANTITYColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_CODE {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_CODEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_CODE”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_CODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY1 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY1Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY1”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY2 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY2Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY2”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY2Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY3 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY3Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY3”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY3Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY4 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY4Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY4”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY4Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY5 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY5Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY5”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY5Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY6 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY6Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY6”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY6Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY7 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY7Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY7”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY7Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY8 {
                get {
                    try {
                        return ((string)(this[this.tableV_GOODS_OUT.GOODS_PROPERTY8Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“V_GOODS_OUT”中列“GOODS_PROPERTY8”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableV_GOODS_OUT.GOODS_PROPERTY8Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_NAMENull() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_NAMEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_NAMENull() {
                this[this.tableV_GOODS_OUT.GOODS_NAMEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsRECORD_LIST_QUANTITYNull() {
                return this.IsNull(this.tableV_GOODS_OUT.RECORD_LIST_QUANTITYColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetRECORD_LIST_QUANTITYNull() {
                this[this.tableV_GOODS_OUT.RECORD_LIST_QUANTITYColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsQUANTITYNull() {
                return this.IsNull(this.tableV_GOODS_OUT.QUANTITYColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetQUANTITYNull() {
                this[this.tableV_GOODS_OUT.QUANTITYColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_CODENull() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_CODEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_CODENull() {
                this[this.tableV_GOODS_OUT.GOODS_CODEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY1Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY1Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY1Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY1Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY2Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY2Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY2Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY2Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY3Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY3Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY3Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY3Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY4Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY4Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY4Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY4Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY5Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY5Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY5Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY5Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY6Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY6Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY6Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY6Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY7Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY7Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY7Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY7Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY8Null() {
                return this.IsNull(this.tableV_GOODS_OUT.GOODS_PROPERTY8Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY8Null() {
                this[this.tableV_GOODS_OUT.GOODS_PROPERTY8Column] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Represents strongly named DataRow class.
        ///</summary>
        public partial class InOutQueryRow : global::System.Data.DataRow {
            
            private InOutQueryDataTable tableInOutQuery;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            internal InOutQueryRow(global::System.Data.DataRowBuilder rb) : 
                    base(rb) {
                this.tableInOutQuery = ((InOutQueryDataTable)(this.Table));
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_ID {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_IDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_ID”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_IDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_CODE {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_CODEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_CODE”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_CODEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_NAME {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_NAMEColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_NAME”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_NAMEColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_UNIT {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_UNITColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_UNIT”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_UNITColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_CONST_PROPERTY1 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_CONST_PROPERTY1Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_CONST_PROPERTY1”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_CONST_PROPERTY1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_CONST_PROPERTY2 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_CONST_PROPERTY2Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_CONST_PROPERTY2”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_CONST_PROPERTY2Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_CONST_PROPERTY3 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_CONST_PROPERTY3Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_CONST_PROPERTY3”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_CONST_PROPERTY3Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY1 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY1Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY1”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY1Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY2 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY2Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY2”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY2Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY3 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY3Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY3”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY3Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY4 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY4Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY4”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY4Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY5 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY5Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY5”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY5Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY6 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY6Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY6”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY6Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY7 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY7Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY7”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY7Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public string GOODS_PROPERTY8 {
                get {
                    try {
                        return ((string)(this[this.tableInOutQuery.GOODS_PROPERTY8Column]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“GOODS_PROPERTY8”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.GOODS_PROPERTY8Column] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public decimal STORAGE_QUANTITY_START {
                get {
                    try {
                        return ((decimal)(this[this.tableInOutQuery.STORAGE_QUANTITY_STARTColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“STORAGE_QUANTITY_START”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.STORAGE_QUANTITY_STARTColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public decimal IN_QUANTITY_StartToEnd {
                get {
                    try {
                        return ((decimal)(this[this.tableInOutQuery.IN_QUANTITY_StartToEndColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“IN_QUANTITY_StartToEnd”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.IN_QUANTITY_StartToEndColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public decimal OUT_QUANTITY_StartToEnd {
                get {
                    try {
                        return ((decimal)(this[this.tableInOutQuery.OUT_QUANTITY_StartToEndColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“OUT_QUANTITY_StartToEnd”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.OUT_QUANTITY_StartToEndColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public decimal STORAGE_QUANTITY_END {
                get {
                    try {
                        return ((decimal)(this[this.tableInOutQuery.STORAGE_QUANTITY_ENDColumn]));
                    }
                    catch (global::System.InvalidCastException e) {
                        throw new global::System.Data.StrongTypingException("表“InOutQuery”中列“STORAGE_QUANTITY_END”的值为 DBNull。", e);
                    }
                }
                set {
                    this[this.tableInOutQuery.STORAGE_QUANTITY_ENDColumn] = value;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_IDNull() {
                return this.IsNull(this.tableInOutQuery.GOODS_IDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_IDNull() {
                this[this.tableInOutQuery.GOODS_IDColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_CODENull() {
                return this.IsNull(this.tableInOutQuery.GOODS_CODEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_CODENull() {
                this[this.tableInOutQuery.GOODS_CODEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_NAMENull() {
                return this.IsNull(this.tableInOutQuery.GOODS_NAMEColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_NAMENull() {
                this[this.tableInOutQuery.GOODS_NAMEColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_UNITNull() {
                return this.IsNull(this.tableInOutQuery.GOODS_UNITColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_UNITNull() {
                this[this.tableInOutQuery.GOODS_UNITColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_CONST_PROPERTY1Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_CONST_PROPERTY1Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_CONST_PROPERTY1Null() {
                this[this.tableInOutQuery.GOODS_CONST_PROPERTY1Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_CONST_PROPERTY2Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_CONST_PROPERTY2Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_CONST_PROPERTY2Null() {
                this[this.tableInOutQuery.GOODS_CONST_PROPERTY2Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_CONST_PROPERTY3Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_CONST_PROPERTY3Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_CONST_PROPERTY3Null() {
                this[this.tableInOutQuery.GOODS_CONST_PROPERTY3Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY1Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY1Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY1Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY1Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY2Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY2Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY2Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY2Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY3Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY3Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY3Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY3Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY4Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY4Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY4Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY4Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY5Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY5Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY5Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY5Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY6Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY6Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY6Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY6Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY7Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY7Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY7Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY7Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsGOODS_PROPERTY8Null() {
                return this.IsNull(this.tableInOutQuery.GOODS_PROPERTY8Column);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetGOODS_PROPERTY8Null() {
                this[this.tableInOutQuery.GOODS_PROPERTY8Column] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsSTORAGE_QUANTITY_STARTNull() {
                return this.IsNull(this.tableInOutQuery.STORAGE_QUANTITY_STARTColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetSTORAGE_QUANTITY_STARTNull() {
                this[this.tableInOutQuery.STORAGE_QUANTITY_STARTColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsIN_QUANTITY_StartToEndNull() {
                return this.IsNull(this.tableInOutQuery.IN_QUANTITY_StartToEndColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetIN_QUANTITY_StartToEndNull() {
                this[this.tableInOutQuery.IN_QUANTITY_StartToEndColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsOUT_QUANTITY_StartToEndNull() {
                return this.IsNull(this.tableInOutQuery.OUT_QUANTITY_StartToEndColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetOUT_QUANTITY_StartToEndNull() {
                this[this.tableInOutQuery.OUT_QUANTITY_StartToEndColumn] = global::System.Convert.DBNull;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public bool IsSTORAGE_QUANTITY_ENDNull() {
                return this.IsNull(this.tableInOutQuery.STORAGE_QUANTITY_ENDColumn);
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public void SetSTORAGE_QUANTITY_ENDNull() {
                this[this.tableInOutQuery.STORAGE_QUANTITY_ENDColumn] = global::System.Convert.DBNull;
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public class V_GOODS_OUTRowChangeEvent : global::System.EventArgs {
            
            private V_GOODS_OUTRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public V_GOODS_OUTRowChangeEvent(V_GOODS_OUTRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public V_GOODS_OUTRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
        
        /// <summary>
        ///Row event argument class
        ///</summary>
        [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
        public class InOutQueryRowChangeEvent : global::System.EventArgs {
            
            private InOutQueryRow eventRow;
            
            private global::System.Data.DataRowAction eventAction;
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public InOutQueryRowChangeEvent(InOutQueryRow row, global::System.Data.DataRowAction action) {
                this.eventRow = row;
                this.eventAction = action;
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public InOutQueryRow Row {
                get {
                    return this.eventRow;
                }
            }
            
            [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
            [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Data.Design.TypedDataSetGenerator", "4.0.0.0")]
            public global::System.Data.DataRowAction Action {
                get {
                    return this.eventAction;
                }
            }
        }
    }
}

#pragma warning restore 1591