﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.Dialog
{
    /// <summary>
    /// DateBetweenSplitQueryWindow.xaml 的交互逻辑
    /// </summary>
    public partial class DateSectSplitQueryWindow : AvalonDock.DocumentContent
    {
        string strStartDateTimeColumn = string.Empty;
        string strEndDateTimeColumn = string.Empty;
        string strTableName = string.Empty;
        string strWhere = string.Empty;
        string strOrder = string.Empty;
        string strTotal = string.Empty;

        string strSplitGroupColumn = string.Empty;
        string strSplitGroupHeader = string.Empty;
        string strSplitPropertyType = string.Empty;
        string strSplitPropertyColumn = string.Empty;

        //xcjt add 2017-01-03
        bool isAllowEditData = false;
        string strXMLTableName = string.Empty;
        private string strSaveDataTable = string.Empty;

        //wdz add 2018-01-23
        string strDetailTableName = string.Empty;
        string strDetailColumnName = string.Empty;


        public DateSectSplitQueryWindow(string _StartDateTimeColumn,string _EndDateTimeColumn,string _TableName, string _Where, 
                                        string _Order,string _strTotal,string _SplitPropertyType,string _SplitGroupColumn, 
                                        string _SplitGroupHeader,  string _SplitPropertyColumn ,string _DetailTableName, string _DetailColumnName)
        {
            InitializeComponent();

            strStartDateTimeColumn = _StartDateTimeColumn;
            strEndDateTimeColumn = _EndDateTimeColumn;
            strTableName = _TableName;
            strWhere = _Where;
            strOrder = _Order;
            strTotal = _strTotal;

            strSplitPropertyType = _SplitPropertyType;
            strSplitGroupColumn = _SplitGroupColumn;
            strSplitGroupHeader = _SplitGroupHeader;
            strSplitPropertyColumn = _SplitPropertyColumn;

            strDetailTableName = _DetailTableName;
            strDetailColumnName = _DetailColumnName;

            //判断是否显示时间段
            panelDateTime.Visibility = (string.IsNullOrEmpty(strStartDateTimeColumn) && string.IsNullOrEmpty(strEndDateTimeColumn)) ? Visibility.Collapsed : System.Windows.Visibility.Visible;

            this.ucQuery.U_Query += new UC.ucQuickQuery.U_QueryEventHandler(ucQuery_U_Query);
        }

        /// <summary>
        /// 增加构造函数里的_XMLTableName参数
        /// xcjt add 2017-01-03
        /// </summary>
        public DateSectSplitQueryWindow(string _StartDateTimeColumn, string _EndDateTimeColumn, string _TableName, string _XMLTableName,bool _isEditData,string _SaveDataTable, string _Where,
                                        string _Order, string _strTotal, string _SplitPropertyType, string _SplitGroupColumn,
                                        string _SplitGroupHeader, string _SplitPropertyColumn, string _DetailTableName , string _DetailColumnName) :
            this(_StartDateTimeColumn, _EndDateTimeColumn, _TableName, _Where, _Order, _strTotal, _SplitPropertyType, _SplitGroupColumn, _SplitGroupHeader, _SplitPropertyColumn, _DetailTableName, _DetailColumnName)
        {            
            strXMLTableName = _XMLTableName;
            isAllowEditData = _isEditData;
            strSaveDataTable = _SaveDataTable;
        }

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //默认日期
            this.dtpStart.SelectedDate = DateTime.Now;
            this.dtpEnd.SelectedDate = DateTime.Now;

            //初始化属性面板
            this.InitSplitPropertyPanel();
            //初始化查询控件
            this.InitQueryControl();
            //加载查询
            //this.ucQuery_U_Query(null);
            
        }

        /// <summary>
        /// 初始化属性面板
        /// </summary>
        private void InitSplitPropertyPanel()
        {
            this.ucSplitPanel.U_SplitPropertyType = strSplitPropertyType;
            this.ucSplitPanel.U_SplitGroupColumn = strSplitGroupColumn;
            this.ucSplitPanel.U_SplitGroupHeader = strSplitGroupHeader;
            this.ucSplitPanel.U_SplitPropertyColumn = strSplitPropertyColumn;
            this.ucSplitPanel.U_InitControl();
        }

        #region     ------查询

        /// <summary>
        /// 初始化查询控件
        /// </summary>
        private void InitQueryControl()
        {
            this.ucQuery.U_WindowName = this.GetType().Name;
            this.ucQuery.U_XmlTableName = strTableName;
            this.ucQuery.U_InitControl();
        }

        /// <summary>
        /// 开始查询
        /// </summary>
        void ucQuery_U_Query(string QueryWhere)
        {
            MainWindow.mainWin.Cursor = Cursors.Wait;
            string strDateWhere = (this.panelDateTime.Visibility == System.Windows.Visibility.Visible ? this.GetDateStr() : string.Empty);
            string strSplitWhere = this.ucSplitPanel.U_GetSplitPropertyWhere();
            string strAppendWhere = string.Format("{0} AND {1} AND {2}",
                                                            string.IsNullOrEmpty(QueryWhere)?"1=1":QueryWhere,
                                                            string.IsNullOrEmpty(strDateWhere)?"1=1":strDateWhere,
                                                            string.IsNullOrEmpty(strSplitWhere)?"1=1":strSplitWhere);
            
            this.ucSplitGrid.U_WindowName = this.GetType().Name;
            //xcjt alter 2017-01-03
            //this.ucSplitGrid.U_XmlTableName = strTableName;
            this.ucSplitGrid.U_XmlTableName = string.IsNullOrEmpty(strXMLTableName) ? strTableName : strXMLTableName;
            this.ucSplitGrid.U_SaveDataTable = strSaveDataTable;

            this.ucSplitGrid.U_TableName = strTableName;
            this.ucSplitGrid.U_AppendWhere = strAppendWhere;
            this.ucSplitGrid.U_Where = strWhere;
            this.ucSplitGrid.U_OrderField = strOrder;
            this.ucSplitGrid.U_TotalColumnName = strTotal;

            this.ucSplitGrid.U_AllowOperatData = isAllowEditData;
            this.ucSplitGrid.U_AllowChecked = false;
            //xcjt add 2017-01-03
            this.ucSplitGrid.U_AllowSave = Visibility.Visible;
            this.ucSplitGrid.U_AllowDelete = Visibility.Collapsed;

            this.ucSplitGrid.U_SplitPropertyType = this.strSplitPropertyType;
            this.ucSplitGrid.U_SplitGroupColumn = this.strSplitGroupColumn;
            this.ucSplitGrid.U_SplitGroupHeader = this.strSplitGroupHeader;
            this.ucSplitGrid.U_SplitPropertyColumn = this.strSplitPropertyColumn;
            this.ucSplitGrid.U_SplitPropertyValue = this.ucSplitPanel.U_GetSplitProertyTypeValue();

            //wdz add 2018-01-23
            this.ucSplitGrid.U_DetailTableName = strDetailTableName;
            this.ucSplitGrid.U_DetailRelatvieColumn = strDetailColumnName;

            this.ucSplitGrid.U_InitControl();
            MainWindow.mainWin.Cursor = Cursors.Arrow;

            //xcjt add 2017-01-03
            //获取Tab页面中的内容，并注册“保存”按钮点击事件
            foreach (TabItem item in this.ucSplitGrid.tabSplitProperty.Items)
            {
                if (item.Content is UC.ucCommonDataGrid)
                {
                    (item.Content as UC.ucCommonDataGrid).SaveClicked += 
                        new UC.ucCommonDataGrid.SavedEventHandler(DateSectSplitQueryWindow_SaveClicked);
                }
                
            }
        }

        /// <summary>
        /// 获得时间查询条件
        /// </summary>
        private string GetDateStr()
        {
            string strDateWhere = string.Empty;

            //判断是否添加时间查询
            if (chkboxDate.IsChecked == true)
            {
                //判断选择的值
                if (this.dtpStart.SelectedDate.HasValue && !this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00')",
                                                                               strStartDateTimeColumn,
                                                                               this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if (!this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}<='{1} 24:60:60')",
                                                                              strEndDateTimeColumn,
                                                                              this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));
                }
                else if (this.dtpStart.SelectedDate.HasValue && this.dtpEnd.SelectedDate.HasValue)
                {
                    strDateWhere = string.Format("({0}>='{1} 00:00:00' AND {2}<='{3} 24:60:60')",
                                                                            strStartDateTimeColumn,
                                                                            this.dtpStart.SelectedDate.Value.ToString("yyyy-MM-dd"),
                                                                            strEndDateTimeColumn,
                                                                            this.dtpEnd.SelectedDate.Value.ToString("yyyy-MM-dd"));

                }
            }
            return strDateWhere;
        }


        /// <summary>
        /// xcjt add 2017-01-03
        /// ucCommonDataGrid控件中的“保存”按钮点击事件处理方法
        /// </summary>
        void DateSectSplitQueryWindow_SaveClicked(object sender, UC.ucCommonDataGrid.SaveEventArgs e)
        {
            //对于库存维护页面要记录日志
            if (this.strSaveDataTable == "STORAGE_LIST")
            {
                MainApp._I_BaseService.CreateSysLog(Enum.LogThread.Task, MainApp._USER.USER_NAME, Enum.LOG_LEVEL.Critical, string.Format("DateSectSplitQueryWindow.DateSectSplitQueryWindow_SaveClicked():用户[{0}]修改库存_影响行数[{1}]", MainApp._USER.USER_NAME,e.AffectCount));
            }
        }

        #endregion
    }
}
