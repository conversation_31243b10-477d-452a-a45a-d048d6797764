<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Ptl.Device</name>
    </assembly>
    <members>
        <member name="T:Ptl.Device.Communication.Command.Direction">
            <summary>
            物品在标签的方向。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.None">
            <summary>
            不启用。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.Center">
            <summary>
            中间。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.Top">
            <summary>
            上方。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.TopRight">
            <summary>
            右上角。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.Right">
            <summary>
            右边。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.BottomRight">
            <summary>
            右下角。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.Bottom">
            <summary>
            下方。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.BottomLeft">
            <summary>
            左下角。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.Left">
            <summary>
            左边。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Direction.TopLeft">
            <summary>
            左上角。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Display600UContent">
            <summary>
            600U 的显示内容。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.Tag">
            <summary>
            获取或设置相关联数据。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.RowIndex">
            <summary>
            获取或设置基于 0 的行序号，用于多行点阵标签。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.Text">
            <summary>
            获取或设置 ASCII 文本。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.Title">
            <summary>
            获取或设置标题，可中文。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.SubTitle">
            <summary>
            获取或设置副标题，可中文。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.Description">
            <summary>
            获取或设置描述，可中文。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.Picture">
            <summary>
            获取或设置图片。
            </summary>
            <remarks>
            此属性本质上是一组图片的索引，值域为 [0, 255]。
            </remarks>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display600UContent.Background">
            <summary>
            获取或设置彩屏背景。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.F5OkColorAfterPress">
            <summary>
            无任务时 OK 按钮按下后的按钮灯色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Off">
            <summary>
            熄灭。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Red">
            <summary>
            红色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Green">
            <summary>
            绿色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Blue">
            <summary>
            蓝色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Yellow">
            <summary>
            黄色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Magenta">
            <summary>
            品红。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.Cyan">
            <summary>
            青色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.White">
            <summary>
            白色；因功耗限制，不推荐使用。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F5OkColorAfterPress.DifferentFromTheLatest">
            <summary>
            不同于最近出现的灯色。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.F9InfraredTimeSensitivities">
            <summary>
            红外时间灵敏度。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.F9InfraredTimeSensitivities.EnterSensitivity">
            <summary>
            获取或设置进入遮挡灵敏度。
            </summary>
            <value>取值范围：0~9；超出范围会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.F9InfraredTimeSensitivities.ExitSensitivity">
            <summary>
            获取或设置离开无遮挡灵敏度。
            </summary>
            <value>取值范围：0~9；超出范围会被截断。</value>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.ClearTeraCommand">
            <summary>
            清除 Tera。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.SimpleCommand">
            <summary>
            简单的命令，只需执行一个指定的单播协议即可完成任务。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.CommandBase">
            <summary>
            表示针对 PTL 设备和输入输出口的命令。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.CommandBase.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
            <remarks>会捕获可能出现的协议执行异常来引发 device.Error 事件。</remarks>
            <exception cref="T:System.NotImplementedException">方法未实现。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Command.CommandBase.Execute(Ptl.Device.IOPort,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="ioPort">执行命令的输入输出口。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
            <remarks>会捕获可能出现的协议执行异常来引发 ioPort.Owner.Error 事件。</remarks>
            <exception cref="T:System.ArgumentNullException">ioPort 或 communicationClient 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.SimpleCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.SimpleCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol)">
            <summary>
            使用指定的单播协议初始化一个简单命令。
            </summary>
            <param name="protocol">一个单播协议。</param>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.SimpleCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
            <remarks>会捕获可能出现的协议执行异常来引发 device.Error 事件。</remarks>
            <exception cref="T:System.ArgumentNullException">device 为空或 communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.SimpleCommand.Protocol">
            <summary>
            获取需执行的单播协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearTeraCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearTeraCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.ClearTera)">
            <summary>
            使用清除 Tera 协议初始化清除 Tera 命令。
            </summary>
            <param name="protocol">清除 Tera 协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearTeraCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.CollectScannerCommand">
            <summary>
            采集扫描枪，成功采集后引发 Scanner.ScannedBarcode 事件并清除设备条码缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.CollectScannerCommand.Execute(Ptl.Device.IOPort,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="ioPort">扫描枪。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
            <exception cref="T:System.ArgumentNullException">ioPort 为空或 communicationClient 为空。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.Clear600UCommand">
            <summary>
            清除 600U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Clear600UCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Clear600UCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.Clear600U)">
            <summary>
            使用清除 600U 协议初始化清除 600U 命令。
            </summary>
            <param name="protocol">清除 600U 协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Clear600UCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.Clear900UCommand">
            <summary>
            清除 900U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Clear900UCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Clear900UCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.Clear900U)">
            <summary>
            使用清除 900U 协议初始化清除 900U 命令。
            </summary>
            <param name="protocol">清除 900U 协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Clear900UCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.ClearLighthouseCommand">
            <summary>
            熄灭灯塔。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearLighthouseCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearLighthouseCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.ClearLighthouse)">
            <summary>
            使用熄灭灯塔协议初始化熄灭灯塔命令。
            </summary>
            <param name="protocol">熄灭灯塔协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearLighthouseCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.CommandExecuteResult">
            <summary>
            命令的执行结果。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.CommandExecuteResult.Error">
            <summary>
            获取或设置执行错误。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.CommandExecuteResult.CommunicationStatistics">
            <summary>
            获取或设置通讯统计。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.CommandExecuteResult.Finished">
            <summary>
            获取或设置命令是否已完全执行完毕。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.ClearScannerCommand">
            <summary>
            清除扫描枪。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearScannerCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearScannerCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.ClearScanner)">
            <summary>
            使用清除扫描枪协议初始化清除扫描枪命令。
            </summary>
            <param name="protocol">清除扫描枪协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.ClearScannerCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.Display600UCommand">
            <summary>
            显示 600U 的内容，并决定是否采集 OK 按钮；
            如果启用采集，则成功采集到 OK 按钮按下动作后引发 Ptl600U.Pressed 并清除按钮状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display600UCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display600UCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.Display600U,System.Boolean)">
            <summary>
            使用指定的显示协议和 OK 按钮采集参数初始化 600U 显示命令。
            </summary>
            <param name="protocol">显示协议。</param>
            <param name="enableCollect">是否需采集 OK 按钮按下动作。</param>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display600UCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
            <remarks>会捕获可能出现的协议执行异常来引发 device.Error 事件。</remarks>
            <exception cref="T:System.ArgumentNullException">device 为空或 communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display600UCommand.Protocol">
            <summary>
            获取显示协议。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display600UCommand.EnableCollect">
            <summary>
            获取是否需要采集 OK 按钮。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display600UCommand.IsPartialFinished">
            <summary>
            获取是否执行了部分协议。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.DisplayLighthouseCommand">
            <summary>
            点亮灯塔。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.DisplayLighthouseCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.DisplayLighthouseCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse)">
            <summary>
            使用点亮灯塔协议初始化点亮灯塔命令。
            </summary>
            <param name="protocol">点亮灯塔协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.DisplayLighthouseCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.DisplayTeraCommand">
            <summary>
            显示 Tera。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.DisplayTeraCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.DisplayTeraCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.DisplayTera)">
            <summary>
            使用显示 Tera 协议初始化显示 Tera 命令。
            </summary>
            <param name="protocol">显示 Tera 协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.DisplayTeraCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.HeartbeatCommand">
            <summary>
            心跳。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.HeartbeatCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.HeartbeatCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.HeartBeat)">
            <summary>
            使用指定的心跳协议初始化命令。
            </summary>
            <param name="protocol">心跳协议。</param>
        </member>
        <member name="T:Ptl.Device.Communication.Command.LightMode">
            <summary>
            标签上 OK 按钮的亮灯模式。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.LightMode.FromByte(System.Byte)">
            <summary>
            从指定字节中解析亮灯模式。
            </summary>
            <param name="data">高三位：LightColor，中两位：LightOnOffPerio，第三位：LightOnOffRatio。</param>
            <returns>亮灯模式。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Command.LightMode.ToByte">
            <summary>
            转换成单个字节。
            </summary>
            <returns>高三位：LightColor，中两位：LightOnOffPerio，第三位：LightOnOffRatio。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Command.LightMode.Default">
            <summary>
            默认模式，即不亮。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.LightMode.Color">
            <summary>
            获取或设置灯色。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.LightMode.Period">
            <summary>
            获取或设置亮灭周期。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.LightMode.Ratio">
            <summary>
            获取或设置亮灭比例。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.LockUnlockCommand">
            <summary>
            锁定或解锁设备。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.LockUnlockCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.LockUnlockCommand.#ctor(Ptl.Device.Communication.Protocol.Unicast.LockUnlock)">
            <summary>
            使用指定的锁定或解锁协议初始化锁定或解锁命令。
            </summary>
            <param name="protocol">锁定或解锁协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.LockUnlockCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Command.SurfaceBackground">
            <summary>
            彩屏标签背景。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Default">
            <summary>
            默认，蓝色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.RedImage">
            <summary>
            红色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.GreenImage">
            <summary>
            绿色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.BlueImage">
            <summary>
            蓝色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.YellowImage">
            <summary>
            黄色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.MagentaImage">
            <summary>
            品红背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.CyanImage">
            <summary>
            青色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.GrayImage">
            <summary>
            灰色背景图。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Red">
            <summary>
            红色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Green">
            <summary>
            绿色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Blue">
            <summary>
            蓝色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Yellow">
            <summary>
            黄色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Magenta">
            <summary>
            品红。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Cyan">
            <summary>
            青色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfaceBackground.Gray">
            <summary>
            灰色。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.SurfacePicture">
            <summary>
            彩屏标签图片。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.None">
            <summary>
            无图片。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Information">
            <summary>
            一般信息。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Free">
            <summary>
            空闲。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Orders">
            <summary>
            订单。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.User">
            <summary>
            操作员。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.LogOn">
            <summary>
            登录。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.LogOff">
            <summary>
            注销。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.BarCode">
            <summary>
            条码。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.QRCode">
            <summary>
            二维码。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Card">
            <summary>
            卡片。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Press">
            <summary>
            按。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Cart">
            <summary>
            购物车。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.CartFull">
            <summary>
            购物车满。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.CartAdd">
            <summary>
            添加购物车。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Back">
            <summary>
            后退。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Forward">
            <summary>
            前进。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Good">
            <summary>
            好。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Timeout">
            <summary>
            超时。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Assemble">
            <summary>
            汇集。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Package">
            <summary>
            打包。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Refresh">
            <summary>
            刷新。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Lock">
            <summary>
            锁定。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Unlock">
            <summary>
            解锁。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Voice">
            <summary>
            耳机声音。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.VolumnOn">
            <summary>
            音量开。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.VolumnOff">
            <summary>
            静音。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.In">
            <summary>
            入。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Out">
            <summary>
            出。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.CheckList">
            <summary>
            核对。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SurfacePicture.Loop">
            <summary>
            循环。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.CommunicationClientBase">
            <summary>
            通讯客户端基类。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.ICommunicationClient">
            <summary>
            表示可执行协议的通讯客户端。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ICommunicationClient.Execute(Ptl.Device.Communication.Protocol.Broadcast.IBroadcastProtocol)">
            <summary>
            执行无返回值的广播协议。
            </summary>
            <param name="protocol">无返回值的广播协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ICommunicationClient.Execute``1(Ptl.Device.Communication.Protocol.Broadcast.IBroadcastProtocol{``0})">
            <summary>
            执行有返回值的广播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="protocol">有返回值的广播协议。</param>
            <returns>协议的执行结果。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.ICommunicationClient.Execute(Ptl.Device.Communication.Protocol.Multicast.IMulticastProtocol)">
            <summary>
            执行无返回值的组播协议。
            </summary>
            <param name="protocol">无返回值的组播协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ICommunicationClient.Execute``1(Ptl.Device.Communication.Protocol.Multicast.IMulticastProtocol{``0})">
            <summary>
            执行有返回值的组播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="protocol">有返回值的组播协议。</param>
            <returns>协议的执行结果。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.ICommunicationClient.Execute(System.Byte,Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol)">
            <summary>
            执行无返回值的单播协议。
            </summary>
            <param name="address">设备地址。</param>
            <param name="protocol">无返回值的单播协议。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ICommunicationClient.Execute``1(System.Byte,Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol{``0})">
            <summary>
            执行有返回值的单播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="address">设备地址。</param>
            <param name="protocol">有返回值的单播协议。</param>
            <returns>协议的执行结果。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.ICommunicationClient.Name">
            <summary>
            获取客户端名称。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ICommunicationClient.Connected">
            <summary>
            获取是否处于已连接状态。
            </summary>
        </member>
        <member name="E:Ptl.Device.Communication.ICommunicationClient.ConnectedChanged">
            <summary>
            连接状态更改事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.Communication.ICommunicationClient.ConnectionError">
            <summary>
            通讯连接出错事件。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.OnConnectedChanged(System.EventArgs)">
            <summary>
            引发连接状态更改事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.OnConnectionError(Ptl.Device.Communication.ConnectionErrorEventArgs)">
            <summary>
            引发通讯连接出错事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.OnInterceptorPrefixReceived(Ptl.Device.Communication.ProtocolInterceptorAdditionalDataEventArgs)">
            <summary>
            引发协议前缀收到事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.OnInterceptorSuffixReceived(Ptl.Device.Communication.ProtocolInterceptorAdditionalDataEventArgs)">
            <summary>
            引发协议后缀收到事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Execute(Ptl.Device.Communication.Protocol.Broadcast.IBroadcastProtocol)">
            <summary>
            执行无返回值的广播协议。
            </summary>
            <param name="protocol">无返回值的广播协议。</param>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Execute``1(Ptl.Device.Communication.Protocol.Broadcast.IBroadcastProtocol{``0})">
            <summary>
            执行有返回值的广播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="protocol">有返回值的广播协议。</param>
            <returns>协议的执行结果。</returns>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Execute(Ptl.Device.Communication.Protocol.Multicast.IMulticastProtocol)">
            <summary>
            执行无返回值的组播协议。
            </summary>
            <param name="protocol">无返回值的组播协议。</param>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Execute``1(Ptl.Device.Communication.Protocol.Multicast.IMulticastProtocol{``0})">
            <summary>
            执行有返回值的组播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="protocol">有返回值的组播协议。</param>
            <returns>协议的执行结果。</returns>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Execute(System.Byte,Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol)">
            <summary>
            执行无返回值的单播协议。
            </summary>
            <param name="address">设备地址。</param>
            <param name="protocol">无返回值的单播协议。</param>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Execute``1(System.Byte,Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol{``0})">
            <summary>
            执行有返回值的单播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="address">设备地址。</param>
            <param name="protocol">有返回值的单播协议。</param>
            <returns>协议的执行结果。</returns>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.DoExecute(System.Byte,Ptl.Device.Communication.Protocol.IProtocol,System.Boolean)">
            <summary>
            执行协议。
            </summary>
            <param name="address">设备地址。</param>
            <param name="protocol">协议。</param>
            <param name="hasResponse">设备是否有返回。</param>
            <returns>设备有返回时的返回值。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Dispose">
            <summary>
            释放托管和非托管资源。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientBase.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="P:Ptl.Device.Communication.CommunicationClientBase.Name">
            <summary>
            获取客户端名称。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.CommunicationClientBase.ProtocolInterceptor">
            <summary>
            获取或设置协议拦截器。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.CommunicationClientBase.Connected">
            <summary>
            获取是否处于已连接状态。
            </summary>
        </member>
        <member name="E:Ptl.Device.Communication.CommunicationClientBase.ConnectedChanged">
            <summary>
            连接状态更改事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.Communication.CommunicationClientBase.ConnectionError">
            <summary>
            通讯连接出错事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.Communication.CommunicationClientBase.InterceptorPrefixReceived">
            <summary>
            在拦截器收到协议前缀时引发。
            </summary>
        </member>
        <member name="E:Ptl.Device.Communication.CommunicationClientBase.InterceptorSuffixReceived">
            <summary>
            在拦截器收到协议后缀时引发。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.CommunicationClientConnectException">
            <summary>
            通讯客户端连接异常，用于显式区分出通讯通道成功建立前的异常。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientConnectException.#ctor">
            <summary>
            初始化新实例。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientConnectException.#ctor(System.String)">
            <summary>
            使用指定的错误消息初始化新实例。
            </summary>
            <param name="message">描述错误的消息。</param>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientConnectException.#ctor(System.String,System.Exception)">
            <summary>
            使用指定错误消息和对作为此异常原因的内部异常的引用来初始化新实例。
            </summary>
            <param name="message">解释异常原因的错误消息。</param>
            <param name="inner">导致当前异常的异常。</param>
        </member>
        <member name="M:Ptl.Device.Communication.CommunicationClientConnectException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            用序列化数据初始化新实例。
            </summary>
            <param name="info">存有有关所引发异常的序列化的对象数据。</param>
            <param name="context">包含有关源或目标的上下文信息。</param>
        </member>
        <member name="T:Ptl.Device.CommunicationStatistics">
            <summary>
            设备的通讯统计。
            </summary>
        </member>
        <member name="M:Ptl.Device.CommunicationStatistics.Add(System.Boolean,System.TimeSpan)">
            <summary>
            添加通讯统计信息。
            </summary>
            <param name="successful">是否成功的通讯。</param>
            <param name="span">耗时。</param>
        </member>
        <member name="M:Ptl.Device.CommunicationStatistics.Add(System.Boolean,System.Int32)">
            <summary>
            添加通讯统计信息。
            </summary>
            <param name="successful">是否成功的通讯。</param>
            <param name="milliseconds">耗时。</param>
        </member>
        <member name="M:Ptl.Device.CommunicationStatistics.Merge(Ptl.Device.CommunicationStatistics)">
            <summary>
            将给定的统计信息与当前信息合并。
            </summary>
            <param name="another">待合并的统计信息。</param>
        </member>
        <member name="M:Ptl.Device.CommunicationStatistics.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.CommunicationStatistics.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.CommunicationStatistics.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.CommunicationStatistics.SyncRoot">
            <summary>
            获取集合访问同步根。
            </summary>
        </member>
        <member name="P:Ptl.Device.CommunicationStatistics.SuccessfulCountByMilliseconds">
            <summary>
            获取按通讯耗时分组的成功次数。
            </summary>
        </member>
        <member name="P:Ptl.Device.CommunicationStatistics.FailedCountByMilliseconds">
            <summary>
            获取按通讯耗时分组的失败次数。
            </summary>
        </member>
        <member name="P:Ptl.Device.CommunicationStatistics.TotalSuccessfulCount">
            <summary>
            获取成功通信总次数。
            </summary>
        </member>
        <member name="P:Ptl.Device.CommunicationStatistics.TotalFailedCount">
            <summary>
            获取通信失败总次数。
            </summary>
        </member>
        <member name="P:Ptl.Device.CommunicationStatistics.TotalCount">
            <summary>
            获取通信总次数。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.DefaultRS485AddressMapper">
            <summary>
            预设的 RS485 地址映射方法枚举。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.DefaultRS485AddressMapper.Original">
            <summary>
            按原样返回真实地址。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.DefaultRS485AddressMapper.SplittedBy50">
            <summary>
            按 50 分割的地址映射方法。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.DefaultRS485AddressMapper.SplittedBy100">
            <summary>
            按 100 分割的地址映射方法。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.DefaultRS485AddressMapperImplements">
            <summary>
            预设的 RS485 地址映射方法实现。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.DefaultRS485AddressMapperImplements.Get(Ptl.Device.Communication.DefaultRS485AddressMapper)">
            <summary>
            按预设的枚举类型获取映射方法。
            </summary>
            <param name="name">预设的枚举类型。</param>
            <returns>映射方法。</returns>
            <exception cref="T:System.ArgumentOutOfRangeException">name 不是预设的。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.DefaultRS485AddressMapperImplements.Original(System.Byte,System.Byte)">
            <summary>
            按原样返回真实地址。
            </summary>
            <param name="busIndex">总线端口。</param>
            <param name="actualAddress">真实地址。</param>
            <returns>原样返回真实地址。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.DefaultRS485AddressMapperImplements.SplittedBy50(System.Byte,System.Byte)">
            <summary>
            按 50 分割的地址映射方法。
            </summary>
            <param name="busIndex">总线端口。</param>
            <param name="actualAddress">真实地址。</param>
            <returns>映射后的地址。</returns>
            <remarks>
            真实地址等于 0 或大于 50 时返回真实地址，
            busIndex 为 0 时返回真实地址，
            busIndex 为 1 时返回真实地址加 50，
            busIndex 为 2 时返回真实地址加 100，
            busIndex 为 3 时返回真实地址加 150，
            不支持其他情况。
            </remarks>
            <exception cref="T:System.NotSupportedException">busIndex 不是 0 或 1 或 2 或 3。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.DefaultRS485AddressMapperImplements.SplittedBy100(System.Byte,System.Byte)">
            <summary>
            按 100 分割的地址映射方法。
            </summary>
            <param name="busIndex">总线端口。</param>
            <param name="actualAddress">真实地址。</param>
            <returns>映射后的地址。</returns>
            <remarks>
            真实地址等于 0 或大于 100 时返回真实地址，
            busIndex 为 0 时返回真实地址，
            busIndex 为 1 时返回真实地址加 100，
            不支持其他情况。
            </remarks>
            <exception cref="T:System.NotSupportedException">busIndex 不是 0 或 1。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.HeartbeatGenerator">
            <summary>
            设备心跳发生器，为指定的所有 PTL 设备定时生成心跳命令。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.HeartbeatGenerator.#ctor">
            <summary>
            初始化新实例。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.HeartbeatGenerator.#ctor(Ptl.Device.InstallProject)">
            <summary>
            使用安装工程中所有的设备初始化新实例。
            </summary>
            <param name="project">安装工程。</param>
        </member>
        <member name="M:Ptl.Device.Communication.HeartbeatGenerator.#ctor(Ptl.Device.XConverter)">
            <summary>
            使用 RS232/RS485 协议转换器上所有的设备初始化新实例。
            </summary>
            <param name="xConverter">RS232/RS485 协议转换器。</param>
        </member>
        <member name="M:Ptl.Device.Communication.HeartbeatGenerator.#ctor(System.Collections.Generic.IEnumerable{Ptl.Device.PtlDevice})">
            <summary>
            使用指定的所有设备初始化新实例。
            </summary>
            <param name="devices">需执行心跳的设备列表。</param>
        </member>
        <member name="P:Ptl.Device.Communication.HeartbeatGenerator.Enable">
            <summary>
            获取或设置是否启用心跳。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.HeartbeatGenerator.Period">
            <summary>
            获取或设置心跳周期。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.IProtocolInterceptor">
            <summary>
            为协议提供拦截功能。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.IProtocolInterceptor.PrepareForWrapRequest(System.Int32)">
            <summary>
            为原始请求数据准备好包装信息。
            </summary>
            <param name="requestDataLength">原始请求数据的长度。</param>
            <returns>为请求准备的包装数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.IProtocolInterceptor.GetResponsePrefixLength(System.Int32,Ptl.Device.Communication.ProtocolInterceptorPreparedWrapDatas)">
            <summary>
            获取响应数据的前缀长度。
            </summary>
            <param name="requestDataLength">原始请求数据的长度。</param>
            <param name="requestWrapDatas">为请求准备的包装数据。</param>
            <returns>响应数据的前缀长度。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.IProtocolInterceptor.GetResponseSuffixLength(System.Int32,Ptl.Device.Communication.ProtocolInterceptorPreparedWrapDatas)">
            <summary>
            获取响应数据的后缀长度。
            </summary>
            <param name="requestDataLength">原始请求数据的长度。</param>
            <param name="requestWrapDatas">为请求准备的包装数据。</param>
            <returns>响应数据的后缀长度。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.ModbusTcpTransactionIdGenerator">
            <summary>
            Modbus Tcp 事务编号生成器。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusTcpTransactionIdGenerator.Reset">
            <summary>
            重置计数，以便下次获取新编号时从 1 开始。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusTcpTransactionIdGenerator.NextTransactionId">
            <summary>
            获取下一个事务编号。
            </summary>
            <returns>值域：[1, 65535]。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.ProtocolInterceptorAdditionalDataEventArgs">
            <summary>
            为 InterceptorPrefixReceived 和 InterceptorSuffixReceived 事件提供事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ProtocolInterceptorAdditionalDataEventArgs.#ctor(System.Byte[])">
            <summary>
            使用指定的附加数据初始化事件参数。
            </summary>
            <param name="additionalData">附加数据。</param>
        </member>
        <member name="P:Ptl.Device.Communication.ProtocolInterceptorAdditionalDataEventArgs.AdditionalData">
            <summary>
            获取附加数据。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.ProtocolInterceptorPreparedWrapDatas">
            <summary>
            协议拦截器为请求准备的包装数据。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ProtocolInterceptorPreparedWrapDatas.Prefix">
            <summary>
            获取或设置附加到原始协议前面的前缀。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ProtocolInterceptorPreparedWrapDatas.Suffix">
            <summary>
            获取或设置附加到原始协议后面的后缀。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ProtocolInterceptorPreparedWrapDatas.Tag">
            <summary>
            获取或设置与当前协议关联的自定义数据。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF5OkColorAfterPress">
            <summary>
            设置无任务时 OK 按钮按下后的按钮灯色。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.IBroadcastProtocol">
            <summary>
            表示广播协议。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.IProtocol">
            <summary>
            表示无返回值的协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.IProtocol.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据，不包含地址和 CRC。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF5OkColorAfterPress.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF5OkColorAfterPress.Color">
            <summary>
            获取或设置无任务时 OK 按钮按下后的按钮灯色。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF6TouchSensitivity">
            <summary>
            设置触摸灵敏度。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF6TouchSensitivity.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF6TouchSensitivity.Sensitivity">
            <summary>
            获取或设置触摸灵敏度。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF7InfraredDistanceSensitivity">
            <summary>
            设置红外距离灵敏度。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF7InfraredDistanceSensitivity.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF7InfraredDistanceSensitivity.Sensitivity">
            <summary>
            获取或设置红外距离灵敏度。
            </summary>
            <value>取值范围：0~9；超出范围会被截断。</value>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF8OkUsability">
            <summary>
            设置 OK 按钮可用性。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF8OkUsability.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF8OkUsability.IsEnabled">
            <summary>
            获取或设置是否可用，默认可用。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF9InfraredTimeSensitivities">
            <summary>
            设置红外时间灵敏度。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF9InfraredTimeSensitivities.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF9InfraredTimeSensitivities.Sensitivities">
            <summary>
            获取或设置红外时间灵敏度。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Multicast.Clear900U">
            <summary>
            组播清除 900U。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Multicast.MulticastProtocol">
            <summary>
            组播协议的基类。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.MulticastProtocol.BusIndex">
            <summary>
            获取或设置总线索引。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.MulticastProtocol.Addresses">
            <summary>
            获取组播到的设备地址集。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Multicast.IMulticastProtocol">
            <summary>
            表示组播协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Multicast.Clear900U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Multicast.Display900U">
            <summary>
            900U 的组播显示协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Multicast.Display900U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.Display900U.DeviceType">
            <summary>
            获取或设置设备类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.Display900U.LightMode">
            <summary>
            获取或设置按钮亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.Display900U.Sound">
            <summary>
            获取或设置声音，暂不支持。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.Display900U.Background">
            <summary>
            获取或设置彩屏背景。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Multicast.Display900U.Item">
            <summary>
            获取或设置显示项。
            </summary>
            <value>为空时会被新实例取代。</value>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Multicast.IMulticastProtocol`1">
            <summary>
            表示有返回值的组播协议，此时只应挂载一个设备。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.IProtocol`1">
            <summary>
            表示有返回值的协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.IProtocol`1.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据，不包含地址和 CRC。</param>
            <returns>解析结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.ClearLighthouse">
            <summary>
            熄灭灯塔。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol">
            <summary>
            表示无返回值的单播协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.ClearLighthouse.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.ClearLighthouse.OwnerDeviceType">
            <summary>
            获取或设置所属 Ptl 设备的类型，默认 PtlXMXP1O5。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.ClearLighthouse.LightIndex">
            <summary>
            获取或设置基于 0 的灯塔的索引。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.ClearTera">
            <summary>
            清除 Tera。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.ClearTera.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Collect900UResult">
            <summary>
            900U 的采集结果。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Collect900UResult.CountCollection">
            <summary>
            获取采集到的数量集。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Collect900UResult.FnLongPressed">
            <summary>
            获取或设置 Fn 键是否被长按。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.LightColor">
            <summary>
            灯色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Off">
            <summary>
            不亮。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Red">
            <summary>
            红色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Green">
            <summary>
            绿色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Blue">
            <summary>
            蓝色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Yellow">
            <summary>
            黄色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Magenta">
            <summary>
            品红。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.Cyan">
            <summary>
            青色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightColor.White">
            <summary>
            白色；因功耗限制，不推荐使用。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.LightOnOffPeriod">
            <summary>
            亮灭周期。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffPeriod.Period100">
            <summary>
            100 ms。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffPeriod.Period200">
            <summary>
            200 ms。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffPeriod.Period500">
            <summary>
            500 ms。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffPeriod.Period1000">
            <summary>
            1000 ms。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.LightOnOffRatio">
            <summary>
            亮灭比例。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP1V0">
            <summary>
            不闪烁。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP1V1">
            <summary>
            亮 1 灭 1。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP1V2">
            <summary>
            亮 1 灭 2。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP1V5">
            <summary>
            亮 1 灭 5。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP1V10">
            <summary>
            亮 1 灭 10。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP2V1">
            <summary>
            亮 2 灭 1。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP5V1">
            <summary>
            亮 5 灭 1。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.LightOnOffRatio.RatioP10V1">
            <summary>
            亮 10 灭 1。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.DisplayTera">
            <summary>
            显示 Tera。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.DisplayTera.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.DisplayTera.LightModes">
            <summary>
            指示灯的亮灯模式的集合。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.HeartBeat">
            <summary>
            心跳。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.HeartBeat.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.DisplayLight">
            <summary>
            点亮前面板灯。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.IXGateProtocol">
            <summary>
            表示 XGate 协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.DisplayLight.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.DisplayLight.LightMode">
            <summary>
            获取或设置亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.GetCommunicationSecurity">
            <summary>
            获取通讯安全设置。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.IXGateProtocol`1">
            <summary>
            表示有返回值的 XGate 协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetCommunicationSecurity.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetCommunicationSecurity.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <exception cref="T:System.ArgumentNullException">data 为空。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.GetDeviceAddressRange">
            <summary>
            获取总线上设备的地址范围。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetDeviceAddressRange.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetDeviceAddressRange.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <exception cref="T:System.ArgumentNullException">data 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.GetDeviceAddressRange.BusIndex">
            <summary>
            获取或设置总线索引。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.GetInFrameDelay">
            <summary>
            获取帧间延时。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetInFrameDelay.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetInFrameDelay.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <exception cref="T:System.ArgumentNullException">data 为空。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.GetInputPorts">
            <summary>
            获取所有输入口的状态。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetInputPorts.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetInputPorts.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <exception cref="T:System.ArgumentNullException">data 为空。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.GetSerialPortSettings">
            <summary>
            获取串口设置。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetSerialPortSettings.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.GetSerialPortSettings.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <exception cref="T:System.ArgumentNullException">data 为空。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.Reboot">
            <summary>
            重启。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.Reboot.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.SetCommunicationSecurity">
            <summary>
            获取通讯安全设置。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.SetCommunicationSecurity.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.SetCommunicationSecurity.CommunicationSecurity">
            <summary>
            获取或设置安全设置。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.SetDeviceAddressRange">
            <summary>
            设置总线上设备的地址范围。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.SetDeviceAddressRange.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.SetDeviceAddressRange.Range">
            <summary>
            获取或设置地址范围。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.SetInFrameDelay">
            <summary>
            设置帧间延时。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.SetInFrameDelay.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.SetInFrameDelay.Delay">
            <summary>
            获取或设置帧间延时。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.SetOutputPort">
            <summary>
            设置指定的输出口状态。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.SetOutputPort.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.SetOutputPort.Index">
            <summary>
            获取或设置基于 0 的索引。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.SetOutputPort.Status">
            <summary>
            获取或设置状态。
            </summary>
            <value>为空时会被默认新实例取代。</value>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.XGateProtocols.SetSerialPortSettings">
            <summary>
            设置串口参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.XGateProtocols.SetSerialPortSettings.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.XGateProtocols.SetSerialPortSettings.Settings">
            <summary>
            获取或设置串口参数。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.RS485BusCommandQueueExecutor">
            <summary>
            总线命令队列执行者，轮流为线上各设备执行单播命令，可被组播命令抢占时间片。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.#ctor(Ptl.Device.RS485Bus)">
            <summary>
            初始化总线队列执行者。
            </summary>
            <param name="bus">总线。</param>
            <exception cref="T:System.ArgumentNullException">bus 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.Start">
            <summary>
            使用后台线程开始执行单播命令。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.Stop">
            <summary>
            停止后台执行线程，不去等待其退出。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.Stop(System.Boolean)">
            <summary>
            停止后台执行线程。
            </summary>
            <param name="waitThreadQuit">是否等待执行线程退出。</param>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.MulticastDisplay900U(System.Collections.Generic.ICollection{System.Byte},Ptl.Device.Ptl900UType,Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,Ptl.Device.Communication.Command.SurfaceBackground,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            组播显示 900U，设备的小类型需一致。
            </summary>
            <param name="addresses">需组播的地址集。</param>
            <param name="deviceType">小类型。</param>
            <param name="displayItem">显示信息。</param>
            <param name="lightMode">OK 按钮灯色。</param>
            <param name="sound">声音。</param>
            <param name="background">彩屏背景。</param>
            <param name="mustCollect">是否必须采集 OK 按钮按下动作。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
            <param name="waitMillisecondsForDisplay">等待屏幕显示的时间，用于等待 TFT 屏显示。</param>
            <exception cref="T:System.ArgumentException">给定设备集中有的不在当前总线上、或不是 Ptl900U、或小类型不匹配。</exception>
            <remarks>
            调用者需确保在组播期间不会执行任何单播命令。
            </remarks>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.MulticastClear900U(System.Collections.Generic.ICollection{System.Byte})">
            <summary>
            组播清除 900U。
            </summary>
            <param name="addresses">需组播的地址集。</param>
            <exception cref="T:System.ArgumentException">给定设备集中有的不在当前总线上或不是 Ptl900U。</exception>
            <remarks>
            调用者需确保在组播期间不会执行任何单播命令。
            </remarks>
        </member>
        <member name="M:Ptl.Device.Communication.RS485BusCommandQueueExecutor.RemoveUnfinishedCommand(System.Collections.Generic.IEnumerable{System.Byte},System.Boolean)">
            <summary>
            为指定设备移除未完成的单播命令。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.SpecialAddress">
            <summary>
            定义一些特殊的设备地址。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.SpecialAddress.BroadcastProtocolAddress">
            <summary>
            广播播协议中的设备地址 0。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.SpecialAddress.UnicastcastProtocolMinAddress">
            <summary>
            单播协议中的最小设备地址 1。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.SpecialAddress.UnicastcastProtocolMaxAddress">
            <summary>
            单播协议中的最大设备地址 247。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.SpecialAddress.MulticastProtocolAddress">
            <summary>
            组播协议中的设备地址 248。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.SpecialAddress.XGateProtocolAddress">
            <summary>
            XGate 协议中的设备地址 249。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.TcpTransparentClient">
            <summary>
            TCP-RS232/485 透传通讯客户端。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.#ctor(System.Net.IPEndPoint)">
            <summary>
            初始化 TCP-RS232/485 透传通讯客户端。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.#ctor(System.Net.IPEndPoint,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            初始化 TCP-RS232/485 透传通讯客户端。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <param name="connectTimeoutInMilliseconds">连接超时时间。</param>
            <param name="sendTimeoutInMilliseconds">发送超时时间。</param>
            <param name="receiveTimeoutInMilliseconds">接收超时时间。</param>
            <param name="interframeDelayInMilliseconds">帧间延时，用于无返回值的命令之后。</param>
            <exception cref="T:System.ArgumentNullException">ipEndPoint 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.EnsureSocketConnectedAndClean">
            <summary>
            确保套接字已连接且没有缓冲数据。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.ReleaseSocket">
            <summary>
            主动释放套接字。
            </summary>
            <remarks>用于客户端的计划内重启之后。</remarks>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.DoExecute(System.Byte,Ptl.Device.Communication.Protocol.IProtocol,System.Boolean)">
            <summary>
            执行协议。
            </summary>
            <param name="address">设备地址。</param>
            <param name="protocol">协议。</param>
            <param name="hasResponse">设备是否有返回。</param>
            <returns>设备有返回时的返回值。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.Communication.TcpTransparentClient.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.TcpTransparentClient.Name">
            <summary>
            返回 IP 终结点。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.TcpTransparentClient.IPEndPoint">
            <summary>
            获取远程端 IP 终结点。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.TcpTransparentClient.ConnectTimeout">
            <summary>
            获取连接超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.TcpTransparentClient.SendTimeout">
            <summary>
            获取发送超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.TcpTransparentClient.ReceiveTimeout">
            <summary>
            获取接收超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.TcpTransparentClient.InterframeDelay">
            <summary>
            获取帧间延时，用于无返回值的命令之后。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity">
            <summary>
            无线 XGate 的配置时信息。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.OldIP">
            <summary>
            获取或设置老 IP。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.ApSsid">
            <summary>
            获取或设置接入点的名称。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.ApPassword">
            <summary>
            获取或设置接入点的密码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.NewIP">
            <summary>
            获取或设置 IP。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.Mask">
            <summary>
            获取或设置子网掩码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.Gateway">
            <summary>
            获取或设置默认网关。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.OldPassword">
            <summary>
            获取或设置老密码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity.NewPassword">
            <summary>
            获取或设置新密码。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateConfig.WifiXGateConfigExecutor">
            <summary>
            无线 XGate 配置协议执行者。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.WifiXGateConfigExecutor.Config(Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity)">
            <summary>
            按 OldIP 配置无线 XGate。
            </summary>
            <param name="entity">配置信息。</param>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.WifiXGateConfigExecutor.Reboot(Ptl.Device.Communication.XGateConfig.WifiXGateConfigEntity)">
            <summary>
            按 OldIP 重启无线 XGate。
            </summary>
            <param name="entity">携带 OldIP 和密码的配置信息。</param>
        </member>
        <member name="T:Ptl.Device.Communication.XGateConfig.WifiXGateConstants">
            <summary>
            无线 XGate 相关常量。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateConfig.WifiXGateConstants.SsidPrefix">
            <summary>
            SSID 前缀。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateConfig.WifiXGateConstants.WebUserName">
            <summary>
            Web 登录名。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateConfig.XGateConfigEntity">
            <summary>
            XGate 的配置时信息。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.MAC">
            <summary>
            获取或设置 MAC。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.IP">
            <summary>
            获取或设置 IP。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.Mask">
            <summary>
            获取或设置子网掩码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.Gateway">
            <summary>
            获取或设置默认网关。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.DeviceType">
            <summary>
            获取或设置型号。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.Version">
            <summary>
            获取或设置固件版本。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.SerialNumber">
            <summary>
            获取或设置序列号。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.HasPassword">
            <summary>
            获取或设置是否有密码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.OldPassword">
            <summary>
            获取或设置老密码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateConfig.XGateConfigEntity.NewPassword">
            <summary>
            获取或设置新密码。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor">
            <summary>
            XGate 组播配置协议执行者。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.DefaultMulticastPort">
            <summary>
            默认的组播端口。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.DefaultMulticastAddress">
            <summary>
            默认的组播地址。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.DefaultSearchProtocolTimeout">
            <summary>
            默认的查询协议超时时间。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.DefaultUsualProtocolTimeout">
            <summary>
            默认的普通协议超时时间。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.#ctor">
            <summary>
            新建默认的执行者。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.#ctor(System.Net.IPAddress)">
            <summary>
            使用指定的本机 IP 新建执行者。
            </summary>
            <param name="localIP">本机 IP。</param>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.#ctor(System.Net.IPEndPoint,System.Net.IPEndPoint,System.TimeSpan,System.TimeSpan)">
            <summary>
            使用指定的本机终结点和远程终结点新建执行者。
            </summary>
            <param name="localEndPoint">本机终结点。</param>
            <param name="multicastEndPoint">组播终结点。</param>
            <param name="searchProtocolTimeout">查询协议超时时间。</param>
            <param name="usualProtocolTimeout">普通协议超时时间。</param>
            <exception cref="T:System.ArgumentNullException">localEndPoint 或 multicastEndPoint 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.Initialize(Ptl.Device.Communication.XGateConfig.XGateConfigEntity)">
            <summary>
            使用指定的 MAC 和序列号初始化 XGate。
            </summary>
            <param name="entity">携带 MAC、序列号和厂家密码的配置信息。</param>
            <returns>是否成功配置。</returns>
            <remarks>不应同时连接多个未初始化的 XGate。</remarks>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.Search">
            <summary>
            查询 XGate。
            </summary>
            <returns>找到的 XGate 集合。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.Config(Ptl.Device.Communication.XGateConfig.XGateConfigEntity)">
            <summary>
            按 MAC 配置 XGate。
            </summary>
            <param name="entity">配置信息。</param>
            <returns>是否成功配置。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.Reboot(Ptl.Device.Communication.XGateConfig.XGateConfigEntity)">
            <summary>
            按 MAC 重启 XGate。
            </summary>
            <param name="entity">携带 MAC 和密码的配置信息。</param>
            <returns>是否成功重启。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigExecutor.ExecuteUsualCommand(System.Byte[])">
            <summary>
            执行常规协议。
            </summary>
            <param name="request">请求。</param>
            <returns>响应。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols">
            <summary>
            提供 XGate 组播配置协议的生成与解析。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.BuildInitialize(Ptl.Device.Communication.XGateConfig.XGateConfigEntity)">
            <summary>
            生成初始化协议。
            </summary>
            <param name="entity">携带 MAC、序列号和厂家密码的配置信息。</param>
            <returns>请求协议。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.BuildSearch">
            <summary>
            生成查询协议。
            </summary>
            <returns>请求协议。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.BuildConfig(Ptl.Device.Communication.XGateConfig.XGateConfigEntity)">
            <summary>
            生成配置协议。
            </summary>
            <param name="entity">配置信息。</param>
            <returns>请求协议。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.BuildReboot(Ptl.Device.Communication.XGateConfig.XGateConfigEntity)">
            <summary>
            生成重启协议。
            </summary>
            <param name="entity">携带 MAC 和密码的配置信息。</param>
            <returns>请求协议。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.BuildProtocol(System.Collections.Generic.IEnumerable{System.Byte})">
            <summary>
            生成协议。
            </summary>
            <param name="content">协议内容，不包含头尾。</param>
            <returns>整个协议。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.ParseInitializeResult(Ptl.Device.Communication.XGateConfig.XGateConfigEntity,System.Byte[])">
            <summary>
            解析初始化结果。
            </summary>
            <param name="entity">初始化信息。</param>
            <param name="response">响应协议。</param>
            <returns>是否成功初始化。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.ParseSearchResult(System.Byte[])">
            <summary>
            解析查询结果。
            </summary>
            <param name="response">响应协议。</param>
            <returns>成功解析时返回配置信息，否则返回空。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.ParseConfigResult(Ptl.Device.Communication.XGateConfig.XGateConfigEntity,System.Byte[])">
            <summary>
            解析配置结果。
            </summary>
            <param name="entity">配置信息。</param>
            <param name="response">响应协议。</param>
            <returns>是否成功配置。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.ParseRebootResult(Ptl.Device.Communication.XGateConfig.XGateConfigEntity,System.Byte[])">
            <summary>
            解析重启结果。
            </summary>
            <param name="entity">重启信息。</param>
            <param name="response">响应协议。</param>
            <returns>是否成功重启。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.ParseUsualResult(System.String,Ptl.Device.Communication.XGateConfig.XGateConfigEntity,System.Byte[])">
            <summary>
            解析常规响应结果。
            </summary>
            <param name="protocolType">协议类型。</param>
            <param name="entity">配置信息。</param>
            <param name="response">响应协议。</param>
            <returns>是否成功执行。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToProtocolMac(System.String)">
            <summary>
            11-22-33-AA-BB-CC => 112233AABBCC。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToProtocolIP(System.String)">
            <summary>
            *********** => 192168001001。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToProtocolBool(System.Boolean)">
            <summary>
            true/false => Y/N。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToNormalMac(System.String)">
            <summary>
            112233AABBCC => 11-22-33-AA-BB-CC。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToNormalIP(System.String)">
            <summary>
            192168001001 => ***********。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToNormalBool(System.String)">
            <summary>
            Y/N => true/false。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateConfig.XGateConfigProtocols.FormatToFixedLength(System.String,System.Int32)">
            <summary>
            将文本右补空格到指定长度，如果大于指定长度则截取。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateSettings.CommunicationSecurity">
            <summary>
            通讯安全设置。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateSettings.CommunicationSecurity.MaxHostIPsCount">
            <summary>
            设置时的最多 IP 数量。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.CommunicationSecurity.Port">
            <summary>
            获取或设置端口。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.CommunicationSecurity.HostIPs">
            <summary>
            获取合法的主站 IP 地址集。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateSettings.DeviceAddressRange">
            <summary>
            总线上设备的地址范围。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.XGateSettings.DeviceAddressRange.MaxAddressesCount">
            <summary>
            设置时的最大地址数量。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.DeviceAddressRange.BusIndex">
            <summary>
            获取或设置所属总线索引。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.DeviceAddressRange.Addresses">
            <summary>
            获取可使用的地址集。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateSettings.InFrameDelay">
            <summary>
            帧间延时。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.InFrameDelay.AfterSent">
            <summary>
            获取或设置发后再发延时。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.InFrameDelay.AfterReceived">
            <summary>
            获取或设置收后再发延时。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateSettings.OutputPortStatus">
            <summary>
            输出口状态。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.OutputPortStatus.ON">
            <summary>
            获取或设置是否处于点亮状态。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.OutputPortStatus.OnOffPeriod">
            <summary>
            获取或设置亮灭周期。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.OutputPortStatus.OnOffRatio">
            <summary>
            获取或设置亮灭比例。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateSettings.SerialPortSettings">
            <summary>
            串口参数。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.SerialPortSettings.BaudRate">
            <summary>
            获取或设置波特率。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.SerialPortSettings.Parity">
            <summary>
            获取或设置校验位。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.SerialPortSettings.DataBits">
            <summary>
            获取或设置数据位数。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.XGateSettings.SerialPortSettings.StopBits">
            <summary>
            获取或设置停止位数。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor">
            <summary>
            XGate 工作参数设置协议执行者。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.#ctor(Ptl.Device.Communication.ModbusClient)">
            <summary>
            初始化设置协议执行者。
            </summary>
            <param name="client">通讯客户端。</param>
            <exception cref="T:System.ArgumentNullException">client 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.GetSerialPortSettings">
            <summary>
            获取串口参数。
            </summary>
            <returns>串口参数。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.SetSerialPortSettings(Ptl.Device.Communication.XGateSettings.SerialPortSettings)">
            <summary>
            设置串口参数。
            </summary>
            <param name="settings">串口参数。</param>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.GetInFrameDelay">
            <summary>
            获取帧间延时。
            </summary>
            <returns>帧间延时。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.SetInFrameDelay(Ptl.Device.Communication.XGateSettings.InFrameDelay)">
            <summary>
            设置帧间延时。
            </summary>
            <param name="delay">帧间延时。</param>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.GetCommunicationSecurity">
            <summary>
            获取通讯安全设置。
            </summary>
            <returns>通讯安全设置。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.SetCommunicationSecurity(Ptl.Device.Communication.XGateSettings.CommunicationSecurity)">
            <summary>
            设置通讯安全设置。
            </summary>
            <param name="communicationSecurity">通讯安全设置。</param>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.GetDeviceAddressRange(System.Byte)">
            <summary>
            获取指定总线上的地址范围。
            </summary>
            <param name="busIndex">基于 0 的总线索引。</param>
            <returns>指定总线上的地址范围。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.SetDeviceAddressRange(Ptl.Device.Communication.XGateSettings.DeviceAddressRange)">
            <summary>
            设置指定总线上的地址范围。
            </summary>
            <param name="range">指定总线上的地址范围。</param>
        </member>
        <member name="M:Ptl.Device.Communication.XGateSettings.XGateSettingsExecutor.Reboot">
            <summary>
            重启。
            </summary>
            <remarks>配置工作参数后需重启才能生效。</remarks>
        </member>
        <member name="T:Ptl.Device.EventDispatcher">
            <summary>
            回调事件的调度者。
            </summary>
        </member>
        <member name="M:Ptl.Device.EventDispatcher.#ctor">
            <summary>
            屏蔽外部实例化。
            </summary>
        </member>
        <member name="M:Ptl.Device.EventDispatcher.Raise(System.EventHandler,System.Object,System.EventArgs)">
            <summary>
            引发指定的事件。
            </summary>
            <param name="handler">事件处理方法。</param>
            <param name="sender">事件源。</param>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.EventDispatcher.Raise``1(System.EventHandler{``0},System.Object,``0)">
            <summary>
            引发指定的事件。
            </summary>
            <param name="handler">事件处理方法。</param>
            <param name="sender">事件源。</param>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.EventDispatcher.Dispose">
            <summary>
            释放托管和非托管资源。
            </summary>
        </member>
        <member name="M:Ptl.Device.EventDispatcher.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.EventDispatcher.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="P:Ptl.Device.EventDispatcher.Instance">
            <summary>
            获取唯一的实例。
            </summary>
        </member>
        <member name="T:Ptl.Device.EventDispatcher.Action">
            <summary>
            表示一个操作。
            </summary>
        </member>
        <member name="T:Ptl.Device.EventSubscriber">
            <summary>
            为本类库中的事件订阅者记录处理异常。
            </summary>
        </member>
        <member name="M:Ptl.Device.EventSubscriber.OnUnhandledException(Ptl.Device.UnhandledEventExceptionEventArgs)">
            <summary>
            引发未捕获异常事件，捕获本事件的处理程序引发的异常。
            </summary>
            <param name="e">事件参数。</param>
            <exception cref="T:System.ArgumentNullException">e 为空。</exception>
        </member>
        <member name="E:Ptl.Device.EventSubscriber.UnhandledException">
            <summary>
            在其他事件的订阅者未捕获异常时发生；会捕获本事件的处理程序引发的异常。
            </summary>
        </member>
        <member name="T:Ptl.Device.EventThreadConfiguration">
            <summary>
            提供对回调事件的线程模型配置。
            </summary>
        </member>
        <member name="P:Ptl.Device.EventThreadConfiguration.Mode">
            <summary>
            获取或设置回调事件的线程模型。
            </summary>
        </member>
        <member name="P:Ptl.Device.EventThreadConfiguration.DispatcherSynchronizationContext">
            <summary>
            获取调度到的线程的同步上下文。
            </summary>
        </member>
        <member name="T:Ptl.Device.EventThreadMode">
            <summary>
            事件的线程模式。
            </summary>
        </member>
        <member name="F:Ptl.Device.EventThreadMode.CommunicationThread">
            <summary>
            在通讯线程引发；阻塞通讯。
            </summary>
        </member>
        <member name="F:Ptl.Device.EventThreadMode.RandomThread">
            <summary>
            使用随机的线程池线程引发；异步无序。
            </summary>
        </member>
        <member name="F:Ptl.Device.EventThreadMode.IsolatedThread">
            <summary>
            使用某独立线程引发；排队、不阻塞通讯。
            </summary>
        </member>
        <member name="F:Ptl.Device.EventThreadMode.CurrentThread">
            <summary>
            使用当前线程引发；排队、不阻塞通讯。
            </summary>
        </member>
        <member name="T:Ptl.Device.InputPortStatusChangedEventArgs">
            <summary>
            输入口状态改变事件的事件参数。
            </summary>
        </member>
        <member name="P:Ptl.Device.InputPortStatusChangedEventArgs.Index">
            <summary>
            获取或设置基于 0 的索引。
            </summary>
        </member>
        <member name="P:Ptl.Device.InputPortStatusChangedEventArgs.Status">
            <summary>
            获取或设置状态。
            </summary>
        </member>
        <member name="T:Ptl.Device.IOPort">
            <summary>
            Ptl 设备上的输入输出口。
            </summary>
        </member>
        <member name="M:Ptl.Device.IOPort.OnAppearanceChanged(System.EventArgs)">
            <summary>
            引发端口的当前外观更改事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.IOPort.PerformAppearanceChanged">
            <summary>
            引发端口的当前外观更改事件。
            </summary>
        </member>
        <member name="M:Ptl.Device.IOPort.ClearCommandQueue">
            <summary>
            清空协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.IOPort.Initialize">
            <summary>
            清空未完成协议以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.IOPort.Refresh">
            <summary>
            刷新显示。
            </summary>
        </member>
        <member name="M:Ptl.Device.IOPort.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
        </member>
        <member name="P:Ptl.Device.IOPort.Tag">
            <summary>
            获取或设置相关联数据。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPort.Owner">
            <summary>
            获取或设置所属 Ptl 设备。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPort.Index">
            <summary>
            获取或设置端口序号。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPort.CommandQueueSyncRoot">
            <summary>
            获取协议队列的同步根。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPort.NormalCommandQueue">
            <summary>
            获取 非加锁解锁协议 队列。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPort.CommandCount">
            <summary>
            获取队列中未完成的命令数。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPort.CommunicateSyncRoot">
            <summary>
            获取通讯操作的同步根。
            </summary>
        </member>
        <member name="E:Ptl.Device.IOPort.AppearanceChanged">
            <summary>
            端口的当前外观更改事件。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Broadcaster">
            <summary>
            广播协议执行者。
            </summary>
            <remarks>广播会影响通道下的所有设备，推荐只在设备安装时使用。</remarks>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.#ctor(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            初始化广播协议执行者。
            </summary>
            <param name="client">通讯客户端。</param>
            <exception cref="T:System.ArgumentNullException">client 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetAddressZero">
            <summary>
            地址清零。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SuggestAddress(System.Byte)">
            <summary>
            建议地址。
            </summary>
            <param name="address">建议的地址。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.CollectSuggestAddress(System.Byte)">
            <summary>
            采集建议地址判断是否已确认。
            </summary>
            <param name="mappedAddress">建议的地址。</param>
            <returns>是否有设备采用了建议的地址。</returns>
            <remarks>这是一个单播协议，但使用广播通讯客户端，所以参数传入映射后的地址。</remarks>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetXIOPortAddress(System.Byte)">
            <summary>
            设置 PtlXIOPort 的地址。
            </summary>
            <param name="address">新地址。</param>
            <returns>是否收到并成功执行协议。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF1OkColorAfterPress(Ptl.Device.Communication.Command.F1OkColorAfterPress)">
            <summary>
            设置 OK 按钮按下后的按钮灯色。
            </summary>
            <param name="color">OK 按钮按下后的按钮灯色。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF2ProgressBarTimeSpan(System.Int32)">
            <summary>
            设置多任务间的进度条时间。
            </summary>
            <param name="timeSpanInMilliseconds">多任务间的进度条时间。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF3FnLongPressResult(Ptl.Device.Communication.Command.F3FnLongPressResult)">
            <summary>
            设置长按 Fn 键的结果。
            </summary>
            <param name="result">长按 Fn 键的结果。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF4Display900UCommandType(Ptl.Device.Communication.Command.F4Display900UCommandType)">
            <summary>
            设置 900U 显示协议的类型。
            </summary>
            <param name="commandType">900U 显示协议的类型。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF5OkColorAfterPress(Ptl.Device.Communication.Command.F5OkColorAfterPress)">
            <summary>
            设置无任务时 OK 按钮按下后的按钮灯色。
            </summary>
            <param name="color">无任务时 OK 按钮按下后的按钮灯色。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF6TouchSensitivity(System.Byte)">
            <summary>
            设置触摸灵敏度。
            </summary>
            <param name="sensitivity">触摸灵敏度。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF7InfraredDistanceSensitivity(System.Byte)">
            <summary>
            设置红外距离灵敏度。
            </summary>
            <param name="sensitivity">距离灵敏度。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF8OkUsability(System.Boolean)">
            <summary>
            设置 OK 按钮可用性。
            </summary>
            <param name="isEnabled">是否可用。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.SetF9InfraredTimeSensitivities(Ptl.Device.Communication.Command.F9InfraredTimeSensitivities)">
            <summary>
            设置红外时间灵敏度。
            </summary>
            <param name="sensitivities">时间灵敏度。</param>
            <exception cref="T:System.ArgumentNullException">settings 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.DisplayAll(Ptl.Device.Communication.Command.DisplayAllTextType)">
            <summary>
            显示所有内容。
            </summary>
            <param name="textType">文本内容的类型。</param>
        </member>
        <member name="M:Ptl.Device.Communication.Broadcaster.ClearAll">
            <summary>
            清空所有显示。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.CollectSuggestAddress">
            <summary>
            建议地址后的采集协议。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol`1">
            <summary>
            表示有返回值的单播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.CollectSuggestAddress.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.CollectSuggestAddress.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <returns>是否已接受建议的地址。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.ClearAll">
            <summary>
            清空所有显示。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.ClearAll.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.DisplayAll">
            <summary>
            广播给所有设备，让其显示指定的内容。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.DisplayAll.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.DisplayAll.TextType">
            <summary>
            获取或设置内容的类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.DisplayAllTextType">
            <summary>
            广播 DisplayAll 协议中指定的显示内容的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.Address">
            <summary>
            地址。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F1OkColorAfterPress">
            <summary>
            F1 值（OK 按钮按下后的按钮灯色）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F2ProgressBarTimeSpan">
            <summary>
            F2 值（多任务间的进度条时间）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F3FnLongPressResult">
            <summary>
            F3 值（长按 Fn 键的结果）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F4Display900UCommandType">
            <summary>
            F4 值（900U 显示协议的类型）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F5OkColorAfterPress">
            <summary>
            F5 值（无任务时 OK 按钮按下后的按钮灯色）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F6TouchSensitivity">
            <summary>
            F6 值（触摸灵敏度）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F7InfraredDistanceSensitivity">
            <summary>
            F7 值（红外距离灵敏度）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F8OkUsability">
            <summary>
            F8 值（OK 按钮可用性）。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.DisplayAllTextType.F9InfraredTimeSensitivities">
            <summary>
            F7 值（红外时间灵敏度）。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.F1OkColorAfterPress">
            <summary>
            OK 按钮按下后的按钮灯色和文本保留。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Off">
            <summary>
            熄灭。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Red">
            <summary>
            红色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Green">
            <summary>
            绿色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Blue">
            <summary>
            蓝色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Yellow">
            <summary>
            黄色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Magenta">
            <summary>
            品红。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Cyan">
            <summary>
            青色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.White">
            <summary>
            白色；因功耗限制，不推荐使用。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.Text">
            <summary>
            保留文本。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndRed">
            <summary>
            保留文本和红色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndGreen">
            <summary>
            保留文本和绿色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndBlue">
            <summary>
            保留文本和蓝色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndYellow">
            <summary>
            保留文本和黄色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndMagenta">
            <summary>
            保留文本和品红。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndCyan">
            <summary>
            保留文本和青色。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F1OkColorAfterPress.TextAndWhite">
            <summary>
            保留文本和白色；因功耗限制，不推荐使用。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.F3FnLongPressResult">
            <summary>
            长按 Fn 键的结果。 
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F3FnLongPressResult.None">
            <summary>
            未设置。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F3FnLongPressResult.Break">
            <summary>
            显示进度条并中断后备任务的显示。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.F4Display900UCommandType">
            <summary>
            900U 显示命令的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F4Display900UCommandType.Task">
            <summary>
            常规任务型。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F4Display900UCommandType.Text">
            <summary>
            文本型，即被当作 Ptl900UType.H424 并显示 Display900UItem.BatchCode。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F4Display900UCommandType.ExtendedCount">
            <summary>
            数码管标签行列码位置被扩展到数量。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F4Display900UCommandType.SingleTaskVertical">
            <summary>
            中文标签单任务竖屏。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F4Display900UCommandType.DoubleTaskHorizontal">
            <summary>
            中文标签双任务横屏。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.F4Display900UCommandType.DoubleTaskVertical">
            <summary>
            中文标签双任务竖屏。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.IBroadcastProtocol`1">
            <summary>
            表示有返回值的广播协议，此时只应挂载一个设备。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetAddressZero">
            <summary>
            地址清零。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetAddressZero.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF1OkColorAfterPress">
            <summary>
            设置 OK 按钮按下后的按钮灯色。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF1OkColorAfterPress.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF1OkColorAfterPress.Color">
            <summary>
            获取或设置 OK 按钮按下后的按钮灯色。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF2ProgressBarTimeSpan">
            <summary>
            设置多明细间的进度条时间。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF2ProgressBarTimeSpan.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF2ProgressBarTimeSpan.TimeSpanInMilliseconds">
            <summary>
            获取或设置多明细间的进度条时间，默认 600 毫秒。
            </summary>
            <value>取值范围：100 至 25500 间的 100 的整数倍；不符合格式会被格式化，超出范围会被截断。</value>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF3FnLongPressResult">
            <summary>
            设置长按 Fn 键的结果。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF3FnLongPressResult.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF3FnLongPressResult.Result">
            <summary>
            获取或设置长按 Fn 键的结果。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetF4Display900UCommandType">
            <summary>
            设置 900U 显示命令的类型。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetF4Display900UCommandType.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetF4Display900UCommandType.CommandType">
            <summary>
            获取或设置命令的类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SuggestAddress">
            <summary>
            向设备建议指定的地址。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SuggestAddress.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SuggestAddress.Address">
            <summary>
            获取或设置建议的地址。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Broadcast.SetXIOPortAddress">
            <summary>
            设置 PtlXIOPort 的地址。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetXIOPortAddress.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Broadcast.SetXIOPortAddress.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <returns>是否收到并成功执行协议。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Broadcast.SetXIOPortAddress.Address">
            <summary>
            获取或设置新地址。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.ConnectionErrorEventArgs">
            <summary>
            为 ICommunicationClient.ConnectionError 事件提供事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ConnectionErrorEventArgs.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ConnectionErrorEventArgs.#ctor(System.Exception)">
            <summary>
            使用指定的异常信息初始化通讯连接出错事件的事件参数。
            </summary>
            <param name="exception">异常信息。</param>
            <exception cref="T:System.ArgumentNullException">exception 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Communication.ConnectionErrorEventArgs.Exception">
            <summary>
            获取或设置异常信息。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.IOPortCollection`1">
            <summary>
            输入输出口集合。
            </summary>
            <typeparam name="T">具体的输入或输出口。</typeparam>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.#ctor">
            <summary>
            初始化 空 输入输出口集合。
            </summary>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.#ctor(Ptl.Device.PtlDevice,System.Byte)">
            <summary>
            初始化 RS485 总线集合。
            </summary>
            <param name="owner">宿主设备。</param>
            <param name="count">集合大小。</param>
            <exception cref="T:System.ArgumentNullException">owner 为空。</exception>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按序号正序循环访问集合的 System.Collections.Generic.IEnumerator&lt;T&gt;。</returns>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按序号正序循环访问集合的 System.Collections.IEnumerator 对象。</returns>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.IOPortCollection`1.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.IOPortCollection`1.Count">
            <summary>
            获取集合的大小。
            </summary>
        </member>
        <member name="P:Ptl.Device.IOPortCollection`1.Item(System.Byte)">
            <summary>
            按站点号获取设备。
            </summary>
            <param name="index">基于 0 的端口索引。</param>
            <exception cref="T:System.ArgumentOutOfRangeException">index 大于等于集合大小。</exception>
        </member>
        <member name="T:Ptl.Device.Lighthouse">
            <summary>
            灯塔输出口。
            </summary>
        </member>
        <member name="M:Ptl.Device.Lighthouse.Display">
            <summary>
            点亮灯塔。
            </summary>
        </member>
        <member name="M:Ptl.Device.Lighthouse.Display(Ptl.Device.Communication.Command.LightOnOffPeriod,Ptl.Device.Communication.Command.LightOnOffRatio)">
            <summary>
            点亮灯塔。
            </summary>
            <param name="period">亮灭周期。</param>
            <param name="ratio">亮灭比例。</param>
        </member>
        <member name="M:Ptl.Device.Lighthouse.Clear">
            <summary>
            熄灭灯塔。
            </summary>
        </member>
        <member name="M:Ptl.Device.Lighthouse.Refresh">
            <summary>
            刷新显示。
            </summary>
        </member>
        <member name="M:Ptl.Device.Lighthouse.Initialize">
            <summary>
            清空未完成协议以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Lighthouse.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Lighthouse.CurrentAppearance">
            <summary>
            获取当前外观。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Implementation.Display900UCommand">
            <summary>
            封装一次 900U 显示工作，可包含多条明细，并指定是否采集 OK 按钮、是否允许 F1 功能和等待屏幕显示的时间；
            如果必须采集 OK 按钮，则必须为所有明细采集到按下动作并引发 Ptl900U.Pressed 事件，
            如果未启用 F1 功能，则采集完所有明细后自动清除设备状态缓存。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Implementation.Display900UCommand.hasSentConfirmProtocol">
            <summary>
            是否已发送或准备发送确认协议，最多一次。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Implementation.Display900UCommand.needSendConfirmProtocol">
            <summary>
            是否需要发送确认协议。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Implementation.Display900UCommand.needConfirmCount">
            <summary>
            确认的已采集数量。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Implementation.Display900UCommand.needSendClearProtocol">
            <summary>
            是否需要发送清除协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display900UCommand.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display900UCommand.#ctor(System.Collections.Generic.ICollection{Ptl.Device.Communication.Command.Display900UItem},Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,Ptl.Device.Communication.Command.SurfaceBackground,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            使用指定的显示信息、是否采集 OK 按钮参数和是否启用 F1 功能初始化 900U 显示命令。
            </summary>
            <param name="items">需依次显示的明细项集合。</param>
            <param name="lightMode">OK 按钮灯色。</param>
            <param name="sound">声音。</param>
            <param name="background">彩屏背景。</param>
            <param name="mustCollect">是否必须采集 OK 按钮按下动作。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
            <param name="waitMillisecondsForDisplay">等待屏幕显示的时间，用于等待 TFT 屏显示。</param>
            <exception cref="T:System.ArgumentNullException">items 为空或任意元素为空或元素数为零。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display900UCommand.MoveToDevice(System.Int32)">
            <summary>
            移动指定数量的明细到设备区，因为这些明细已通过其他方式发往设备。
            </summary>
            <param name="count">移动的明细数量。</param>
            <exception cref="T:System.ArgumentOutOfRangeException">移动的数量小于等于零或大于剩余未下发项数或大于设备空位数。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display900UCommand.CreateUnFinishedItemsAsNewCommand">
            <summary>
            将未完成的项创建为新的显示命令。
            </summary>
            <returns>只包含未完成的项，其他显示参数和当前命令一致。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Command.Implementation.Display900UCommand.Execute(Ptl.Device.PtlDevice,Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            执行命令。
            </summary>
            <param name="device">执行命令的设备。</param>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果。</returns>
            <remarks>会捕获可能出现的协议执行异常来引发 device.Error 事件。</remarks>
            <exception cref="T:System.ArgumentNullException">device 为空或 communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.IsPartialFinished">
            <summary>
            获取是否执行了部分协议。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.Items">
            <summary>
            获取显示明细项集合。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.LightMode">
            <summary>
            获取按钮灯色。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.Sound">
            <summary>
            获取声音。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.Background">
            <summary>
            获取彩屏背景。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.MustCollect">
            <summary>
            获取是否必须采集 OK 按钮。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.EnableF1OkColorAfterPress">
            <summary>
            获取是否启用 F1 功能。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Implementation.Display900UCommand.WaitMillisecondsForDisplay">
            <summary>
            获取等待屏幕显示的时间。
            </summary>
        </member>
        <member name="T:Ptl.Device.LighthouseAppearance">
            <summary>
            灯塔的外观。
            </summary>
        </member>
        <member name="P:Ptl.Device.LighthouseAppearance.ON">
            <summary>
            获取或设置是否处于点亮状态。
            </summary>
        </member>
        <member name="P:Ptl.Device.LighthouseAppearance.Period">
            <summary>
            获取或设置的亮灭周期。
            </summary>
        </member>
        <member name="P:Ptl.Device.LighthouseAppearance.Ratio">
            <summary>
            获取或设置的亮灭比例。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl600UAppearance">
            <summary>
            文本标签的外观。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Text">
            <summary>
            获取或设置 ASCII 文本。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Title">
            <summary>
            获取或设置标题，可中文。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.SubTitle">
            <summary>
            获取或设置副标题，可中文。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Description">
            <summary>
            获取或设置描述，可中文。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Picture">
            <summary>
            获取或设置图片。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Background">
            <summary>
            获取或设置彩屏背景。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Light">
            <summary>
            获取或设置亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="P:Ptl.Device.Ptl600UAppearance.Sound">
            <summary>
            获取或设置声音模式。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl900UAppearance">
            <summary>
            拣货标签的外观。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.BatchCode">
            <summary>
            获取或设置批号。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Name">
            <summary>
            获取或设置名称。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Description">
            <summary>
            获取或设置描述。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Direction">
            <summary>
            获取或设置方向。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Unit">
            <summary>
            获取或设置单位。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Background">
            <summary>
            获取或设置彩屏背景。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.SubLocation">
            <summary>
            获取或设置行列码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.LongSubLocation">
            <summary>
            获取或设置长行列码。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Count">
            <summary>
            获取或设置数量。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Light">
            <summary>
            获取或设置亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.Sound">
            <summary>
            获取或设置声音。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UAppearance.EnableF1OkColorAfterPress">
            <summary>
            获取或设置是否启用 F1 功能。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlDeviceErrorEventArgs">
            <summary>
            设备错误事件的事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDeviceErrorEventArgs.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDeviceErrorEventArgs.#ctor(System.Exception)">
            <summary>
            使用指定的异常信息初始化设备错误事件的事件参数。
            </summary>
            <param name="exception">异常信息。</param>
            <exception cref="T:System.ArgumentNullException">exception 为空。</exception>
        </member>
        <member name="M:Ptl.Device.PtlDeviceErrorEventArgs.#ctor(Ptl.Device.Communication.ICommunicationClient,System.Exception)">
            <summary>
            使用指定的通讯客户端和异常信息初始化设备错误事件的事件参数。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <param name="exception">异常信息。</param>
            <exception cref="T:System.ArgumentNullException">communicationClient 或 exception 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDeviceErrorEventArgs.CommunicationClient">
            <summary>
            获取或设置可能存在的通讯客户端。
            </summary>
            <remarks>不是通讯错误时，此属性为空。</remarks>
        </member>
        <member name="P:Ptl.Device.PtlDeviceErrorEventArgs.Exception">
            <summary>
            获取或设置异常信息。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Collect600U">
            <summary>
            采集 600U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Collect600U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Collect600U.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <returns>OK 按钮是否被按下。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Collect900U">
            <summary>
            采集 900U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Collect900U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Collect900U.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <returns>采集结果。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.CollectScanner">
            <summary>
            采集条码缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.CollectScanner.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.CollectScanner.FromReceivedData(System.Byte[])">
            <summary>
            从返回的协议数据解析出返回值。
            </summary>
            <param name="data">移除了头和尾的协议数据。</param>
            <returns>为空或成功采集到的条码。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.CollectScanner.OwnerDeviceType">
            <summary>
            获取或设置所属 Ptl 设备的类型，默认 PtlXMXP1O5。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Clear600U">
            <summary>
            清除 600U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Clear600U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Clear600U.Mode">
            <summary>
            获取或设置清空方式。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Clear600UMode">
            <summary>
            600U 的清空方式。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Clear600UMode.All">
            <summary>
            所有。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Clear600UMode.FirstRow">
            <summary>
            第一行。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Clear600UMode.SecondRow">
            <summary>
            第二行。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Clear600UMode.Sound">
            <summary>
            蜂鸣，暂不支持。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.Clear600UMode.Button">
            <summary>
            按钮事件缓存。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Clear900U">
            <summary>
            清除 900U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Clear900U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.ClearScanner">
            <summary>
            清空设备条码缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.ClearScanner.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.ClearScanner.OwnerDeviceType">
            <summary>
            获取或设置所属 Ptl 设备的类型，默认 PtlXMXP1O5。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.ConfirmCollected900U">
            <summary>
            成功采集 900U 的最后一组部分任务时的确认协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.ConfirmCollected900U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.ConfirmCollected900U.Count">
            <summary>
            获取或设置本次采集到的明细数。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse">
            <summary>
            点亮灯塔。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse.OwnerDeviceType">
            <summary>
            获取或设置所属 Ptl 设备的类型，默认 PtlXMXP1O5。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse.LightIndex">
            <summary>
            获取或设置基于 0 的灯塔的索引。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse.LightOnOffPeriod">
            <summary>
            获取或设置亮灭周期。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.DisplayLighthouse.LightOnOffRatio">
            <summary>
            获取或设置亮灭比例。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Display600U">
            <summary>
            600U 的显示协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Display600U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display600U.DeviceType">
            <summary>
            获取或设置设备类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display600U.Content">
            <summary>
            获取或设置显示内容。
            </summary>
            <value>为空时会被默认新示例取代。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display600U.LightMode">
            <summary>
            获取或设置按钮亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display600U.Sound">
            <summary>
            获取或设置声音。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.Display900U">
            <summary>
            900U 的显示协议。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.Display900U.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
            <exception cref="T:System.InvalidOperationException">需下发的项集合为空或项数超出设备可驻留的最大项数。</exception>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.DeviceType">
            <summary>
            获取或设置设备类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.LightMode">
            <summary>
            获取或设置按钮亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.Sound">
            <summary>
            获取或设置声音，暂不支持。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.Background">
            <summary>
            获取或设置彩屏背景。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.LastCollectedItemsCount">
            <summary>
            获取或设置上次采集的项数。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.RemainedItemsCount">
            <summary>
            获取或设置剩余的项数。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.Display900U.Items">
            <summary>
            获取本次需下发的项集合。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.Display900UItem">
            <summary>
            900U 的显示项。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.Tag">
            <summary>
            获取或设置相关联数据。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.BatchCode">
            <summary>
            获取或设置批号，常用于 H424 的前段。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.Name">
            <summary>
            获取或设置品名，可中文。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.Description">
            <summary>
            获取或设置描述，可中文。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.Direction">
            <summary>
            获取或设置物品相对于标签的方向。
            </summary>
            <remarks>
            此属性本质上是一组图片的索引，值域为 [0, 255]；
            其中 0~9 已在 Direction 枚举中定义，另外还有：16-多规，17-看似，18-听似，19-看听。
            </remarks>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.Unit">
            <summary>
            获取或设置单位，可中文。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断，长度不足后补空格。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.SubLocation">
            <summary>
            获取或设置行列码，十六进制显示。
            </summary>
            <remarks>空白使用 Ptl900U.EmptySubLocation = 0xFF 表示。</remarks>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.LongSubLocation">
            <summary>
            获取或设置长行列码，最多 4 个 ASCII 字符。
            </summary>
            <value>为空时会被 System.String.Empty 取代，超出长度会被截断，长度不足后补空格。</value>
        </member>
        <member name="P:Ptl.Device.Communication.Command.Display900UItem.Count">
            <summary>
            获取或设置数量。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Protocol.Unicast.LockUnlock">
            <summary>
            加锁或解锁。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Protocol.Unicast.LockUnlock.ToSendData">
            <summary>
            协议信息转换到协议数据。
            </summary>
            <returns>协议数据。</returns>
        </member>
        <member name="P:Ptl.Device.Communication.Protocol.Unicast.LockUnlock.IsLock">
            <summary>
            或者或设置是否加锁，反之解锁。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Command.SoundMode">
            <summary>
            声音模式。
            </summary>
        </member>
        <member name="F:Ptl.Device.Communication.Command.SoundMode.Silent">
            <summary>
            无声。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.Crc">
            <summary>
            提供 Crc 的计算功能。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.Crc.Calculate(System.Byte[],System.Int32,System.Int32)">
            <summary>
            计算 CRC。
            </summary>
            <param name="data">待计算的源。</param>
            <param name="index">计算的开始处。</param>
            <param name="count">计算的字节数。</param>
            <returns>计算后的 CRC。</returns>
            <exception cref="T:System.ArgumentNullException">data 为空。</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">index 小于零或大于等于 data 的长度；count 小于零或大于剩余的可计算字节数。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.ModbusClient">
            <summary>
            Modbus Tcp 通讯客户端。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.#ctor(System.Net.IPEndPoint)">
            <summary>
            初始化 Modbus 客户端。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.#ctor(System.Net.IPEndPoint,System.Byte)">
            <summary>
            初始化 Modbus 客户端。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <param name="busIndex">总线端口。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.#ctor(System.Net.IPEndPoint,System.Byte,System.Int32,System.Int32,System.Int32)">
            <summary>
            初始化 Modbus 客户端。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <param name="busIndex">总线端口。</param>
            <param name="connectTimeoutInMilliseconds">连接超时时间。</param>
            <param name="sendTimeoutInMilliseconds">发送超时时间。</param>
            <param name="receiveTimeoutInMilliseconds">接收超时时间。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.#ctor(System.Net.IPEndPoint,System.Byte,System.Int32,System.Int32,System.Int32,Ptl.Device.Communication.MapRS485Address)">
            <summary>
            初始化 Modbus 客户端。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <param name="busIndex">总线端口。</param>
            <param name="connectTimeoutInMilliseconds">连接超时时间。</param>
            <param name="sendTimeoutInMilliseconds">发送超时时间。</param>
            <param name="receiveTimeoutInMilliseconds">接收超时时间。</param>
            <param name="mapAddress">RS485 地址映射方法。</param>
            <exception cref="T:System.ArgumentNullException">ipEndPoint 或 mapAddress 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.EnsureSocketConnectedAndClean">
            <summary>
            确保套接字已连接且没有缓冲数据。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.ReleaseSocket">
            <summary>
            主动释放套接字。
            </summary>
            <remarks>用于客户端的计划内重启之后。</remarks>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.Execute(Ptl.Device.Communication.Protocol.XGateProtocols.IXGateProtocol)">
            <summary>
            执行无返回值的 XGate 单播协议。
            </summary>
            <param name="protocol">无返回值的单播协议。</param>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.Execute``1(Ptl.Device.Communication.Protocol.XGateProtocols.IXGateProtocol{``0})">
            <summary>
            执行有返回值的 XGate 单播协议。
            </summary>
            <typeparam name="TResult">返回值类型。</typeparam>
            <param name="protocol">有返回值的单播协议。</param>
            <returns>协议的执行结果。</returns>
            <exception cref="T:System.ArgumentNullException">protocol 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.DoExecute(System.Byte,Ptl.Device.Communication.Protocol.IProtocol,System.Boolean)">
            <summary>
            执行协议。
            </summary>
            <param name="address">设备地址。</param>
            <param name="protocol">协议。</param>
            <param name="hasResponse">设备是否有返回。</param>
            <returns>设备有返回时的返回值。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.Communication.ModbusClient.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.Name">
            <summary>
            返回 IP 终结点及总线端口。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.IPEndPoint">
            <summary>
            获取远程端 IP 终结点。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.BusIndex">
            <summary>
            获取总线的端口。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.ConnectTimeout">
            <summary>
            获取连接超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.SendTimeout">
            <summary>
            获取发送超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.ReceiveTimeout">
            <summary>
            获取接收超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.MapAddress">
            <summary>
            获取地址映射方法。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.ModbusClient.TransactionIdGenerator">
            <summary>
            获取或设置事务编号发生器。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.Communication.SerialPortClient">
            <summary>
            串口通讯客户端。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.#ctor(System.String)">
            <summary>
            初始化串口通讯客户端。
            </summary>
            <param name="portName">端口名称。</param>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.#ctor(System.String,System.Int32,System.IO.Ports.Parity,System.Int32,System.IO.Ports.StopBits,System.Int32,System.Int32,System.Int32)">
            <summary>
            初始化串口通讯客户端。
            </summary>
            <param name="portName">端口名称。</param>
            <param name="baudRate">波特率。</param>
            <param name="parity">校验位。</param>
            <param name="dataBits">数据位。</param>
            <param name="stopBits">停止位。</param>
            <param name="writeTimeoutInMilliseconds">发送超时时间。</param>
            <param name="readTimeoutInMilliseconds">接收超时时间。</param>
            <param name="interframeDelayInMilliseconds">帧间延时，用于无返回值的命令之后。</param>
            <exception cref="T:System.ArgumentNullException">portName 为空或为 System.String.Empty。</exception>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.EnsureSerialPortOpenedAndClean">
            <summary>
            确保串口已打开且没有缓冲数据。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.ReleaseSerialPort">
            <summary>
            释放串口。
            </summary>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.DoExecute(System.Byte,Ptl.Device.Communication.Protocol.IProtocol,System.Boolean)">
            <summary>
            执行协议。
            </summary>
            <param name="address">设备地址。</param>
            <param name="protocol">协议。</param>
            <param name="hasResponse">设备是否有返回。</param>
            <returns>设备有返回时的返回值。</returns>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.Communication.SerialPortClient.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.Name">
            <summary>
            返回串口名称。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.PortName">
            <summary>
            获取串口名。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.BaudRate">
            <summary>
            获取波特率。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.Parity">
            <summary>
            获取奇偶校验位。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.DataBits">
            <summary>
            获取数据位数。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.StopBits">
            <summary>
            获取停止位数。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.WriteTimeout">
            <summary>
            获取写入超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.ReadTimeout">
            <summary>
            获取读取超时时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.Communication.SerialPortClient.InterframeDelay">
            <summary>
            获取帧间延时，用于无返回值的命令之后。
            </summary>
        </member>
        <member name="T:Ptl.Device.Communication.MapRS485Address">
            <summary>
            提供 RS485 地址的映射功能。
            </summary>
            <param name="busIndex">总线端口。</param>
            <param name="actualAddress">真实地址。</param>
            <returns>映射后的地址。</returns>
            <remarks>Modbus 服务器下可能有多个 RS485 总线，ModbusClient 使用映射后的地址通讯。</remarks>
        </member>
        <member name="T:Ptl.Device.PtlDeviceExecuteProtocolEventArgs">
            <summary>
            设备协议被成功执行事件的事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDeviceExecuteProtocolEventArgs.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDeviceExecuteProtocolEventArgs.#ctor(Ptl.Device.Communication.ICommunicationClient,Ptl.Device.Communication.Protocol.Unicast.IUnicastProtocol,System.DateTime,System.DateTime)">
            <summary>
            初始化协议成功执行事件的事件参数。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <param name="protocol">协议。</param>
            <param name="beginTime">开始时间。</param>
            <param name="endTime">结束时间。</param>
            <exception cref="T:System.ArgumentNullException">communicationClient 或 command 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDeviceExecuteProtocolEventArgs.CommunicationClient">
            <summary>
            获取或设置通讯客户端。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDeviceExecuteProtocolEventArgs.Protocol">
            <summary>
            获取或设置协议。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDeviceExecuteProtocolEventArgs.BeginTime">
            <summary>
            获取或设置开始时间。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDeviceExecuteProtocolEventArgs.EndTime">
            <summary>
            获取或设置结束时间。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlIOPort">
            <summary>
            提供扫描枪和灯塔的接入和控制功能。
            </summary>
            <remarks>
            扫描枪不受锁定操作影响。
            </remarks>
        </member>
        <member name="T:Ptl.Device.PtlDevice">
            <summary>
            PTL 设备的基类。
            </summary>
            <remarks>对设备的控制是异步的，协议会加入队列，由 CommandQueueExecutor 调用 ExecuteFirstCommand 方法来异步执行。</remarks>
        </member>
        <member name="F:Ptl.Device.PtlDevice.commandQueueSyncRoot">
            <summary>
            命令队列同步根，服务于以下三个队列的元素增改。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDevice.lockUnlockCommandQueue">
            <summary>
            加锁解锁命令队列，优先执行。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDevice.normalCommandQueue">
            <summary>
            普通的业务命令队列。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDevice.heartbeatCommandQueue">
            <summary>
            心跳协议队列，其实只有 0 或 1 个元素，心跳的执行是业务透明的。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.OnExecuteProtocol(Ptl.Device.PtlDeviceExecuteProtocolEventArgs)">
            <summary>
            引发设备协议被成功执行事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.PerformExecuteProtocol(Ptl.Device.PtlDeviceExecuteProtocolEventArgs)">
            <summary>
            引发设备协议被成功执行事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.OnError(Ptl.Device.PtlDeviceErrorEventArgs)">
            <summary>
            引发设备错误事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.PerformError(Ptl.Device.PtlDeviceErrorEventArgs)">
            <summary>
            引发设备错误事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.OnIsLockedChanged(System.EventArgs)">
            <summary>
            引发设备锁定状态更改事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.OnInErrorChanged(System.EventArgs)">
            <summary>
            引发设备错误状态更改事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.OnAppearanceChanged(System.EventArgs)">
            <summary>
            引发设备的当前外观更改事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.PtlDevice.PerformAppearanceChanged">
            <summary>
            引发设备的当前外观更改事件。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.ClearCommandQueue">
            <summary>
            清空协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.ResetCommunicationStatistics">
            <summary>
            使用新的统计信息重新开始统计。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.Lock">
            <summary>
            锁定设备按键。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.Unlock">
            <summary>
            解锁设备按键。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.Refresh">
            <summary>
            刷新显示。
            </summary>
            <remarks>
            常用于设备断电后更换设备后。
            </remarks>
        </member>
        <member name="M:Ptl.Device.PtlDevice.Initialize">
            <summary>
            清空未完成的协议、清零通讯统计、解锁处于锁定状态的设备以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDevice.TryAddHeartbeat">
            <summary>
            尝试加入心跳命令，只在命令队列为空时才会加入。
            </summary>
            <returns>是否成功加入心跳命令。</returns>
        </member>
        <member name="M:Ptl.Device.PtlDevice.RemoveHeartbeat">
            <summary>
            移除心跳命令。
            </summary>
            <remarks>此方法常用于在业务命令下发前清空未执行的心跳命令，减少通讯耗时。</remarks>
        </member>
        <member name="M:Ptl.Device.PtlDevice.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议，加锁解锁协议需要先执行。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDevice.Tag">
            <summary>
            获取或设置相关联数据。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.Address">
            <summary>
            获取或设置站点号。
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">设置时值小于 1 或大于 247。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDevice.MajorType">
            <summary>
            获取或设置大类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.CommandQueueSyncRoot">
            <summary>
            获取协议队列的同步根。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.LockUnlockCommandQueue">
            <summary>
            获取 加锁解锁协议 队列。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.NormalCommandQueue">
            <summary>
            获取 非加锁解锁协议 队列。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.CommandCount">
            <summary>
            获取队列中未完成的命令数。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.CommunicateSyncRoot">
            <summary>
            获取通讯操作的同步根。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.CommunicationStatistics">
            <summary>
            获取通讯统计信息。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.IsLocked">
            <summary>
            获取设备是否锁定。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDevice.InError">
            <summary>
            获取设备是否处于错误状态。
            </summary>
        </member>
        <member name="E:Ptl.Device.PtlDevice.ExecuteProtocol">
            <summary>
            设备协议被成功执行事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.PtlDevice.Error">
            <summary>
            设备错误事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.PtlDevice.IsLockedChanged">
            <summary>
            设备锁定状态更改事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.PtlDevice.InErrorChanged">
            <summary>
            设备错误状态更改事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.PtlDevice.AppearanceChanged">
            <summary>
            设备的当前外观更改事件。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPort.ClearCommandQueue">
            <summary>
            清空协议队列，包括其上挂接的设备的协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPort.Initialize">
            <summary>
            清空未完成的协议、清零通讯统计、解锁处于锁定状态的设备以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPort.Refresh">
            <summary>
            刷新显示，包括其上挂接的设备。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPort.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议，加锁解锁协议需要先执行。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <remarks>对于这种挂接多个子级设备的设备，为每个子级设备执行第一个协议。</remarks>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlIOPort.Scanner">
            <summary>
            获取或设置扫描枪输入口；设置时会改写 Scanner 的 Owner。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlIOPort.Lighthouses">
            <summary>
            获取或设置灯塔输出口集合；设置时会改写 Lighthouse 的 Owner。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.PtlIOPort.CommandCount">
            <summary>
            获取队列中未完成的命令数。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlIOPortWith600U">
            <summary>
            提供扫描枪、灯塔、600U 的接入和控制。
            </summary>
            <remarks>
            不可单独锁定解锁其上的 600U，即 PtlIOPortWith600U.Lock()/UnLock() 和 PtlIOPortWith600U.Ptl600U.Lock()/UnLock() 是一致的。
            与此类似，PtlIOPortWith600U.Ptl600U.Clear(Clear600UMode.All) 也会额外清除宿主设备上的条码缓存、灯塔显示。
            </remarks>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.#ctor">
            <summary>
            初始化链接器。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.ClearCommandQueue">
            <summary>
            清空协议队列，包括其上挂接的设备的协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.ResetCommunicationStatistics">
            <summary>
            使用新的统计信息重新开始统计，包括其上挂接的 Ptl600U。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.Initialize">
            <summary>
            清空未完成的协议、清零通讯统计、解锁处于锁定状态的设备以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.Refresh">
            <summary>
            刷新显示，包括其上挂接的设备。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.Ptl600U_ClearAll(System.Object,System.EventArgs)">
            <summary>
            Clear600UMode.All 会清除宿主设备。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlIOPortWith600U.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议，加锁解锁协议需要先执行。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlIOPortWith600U.Ptl600U">
            <summary>
            获取 600U 单元。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlIOPortWith600U.Address">
            <summary>
            获取或设置站点号，会覆盖其下的 600U 站点号。
            </summary>
            <exception cref="T:System.ArgumentOutOfRangeException">设置时 value 小于 1 或大于 255。</exception>
        </member>
        <member name="P:Ptl.Device.PtlIOPortWith600U.CommandCount">
            <summary>
            获取队列中未完成的命令数。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlMXP1O5">
            <summary>
            提供扫描枪、灯塔、600U 的接入和控制。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlMXP1O5.#ctor">
            <summary>
            初始化 MXP1O5。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlMXP1O5.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlMXP1O5Type">
            <summary>
            MXP1O5 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlMXP1O5Type.M3">
            <summary>
            3 位数码管。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlMXP1O5Type.M8">
            <summary>
            8 位点阵。
            </summary>
        </member>
        <member name="T:Ptl.Device.InstallProject">
            <summary>
            表示一个安装工程，记录着设备的安装状况。
            </summary>
        </member>
        <member name="P:Ptl.Device.InstallProject.Name">
            <summary>
            获取或设置名称。
            </summary>
        </member>
        <member name="P:Ptl.Device.InstallProject.Description">
            <summary>
            获取或设置描述。
            </summary>
        </member>
        <member name="P:Ptl.Device.InstallProject.XGates">
            <summary>
            获取或设置控制器集合。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.InstallProject.HeartbeatGenerator">
            <summary>
            获取设备心跳发生器。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl600UType">
            <summary>
            Ptl600U 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl600UType.P1101">
            <summary>
            11 列 1 行点阵。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl600UType.P1102">
            <summary>
            11 列 2 行点阵。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl600UType.D3">
            <summary>
            3 位数码管。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl600UType.D6">
            <summary>
            6 位数码管。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl600UType.Surface">
            <summary>
            彩屏。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl900UPressedEventArgs">
            <summary>
            为 Ptl900U.Pressed 事件提供事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl900UPressedEventArgs.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.Ptl900UPressedEventArgs.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.Ptl900UPressedEventArgs.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Ptl900UPressedEventArgs.ResultByItem">
            <summary>
            获取显示项及其返回值集合。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900UPressedEventArgs.FnLongPressed">
            <summary>
            获取或设置 Fn 键是否被长按。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlDeviceCollection">
            <summary>
            RS485Bus 下的设备集合；同一 RS485Bus 下的 PtlDevice.Address 是唯一的。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.AddOrUpdate(Ptl.Device.PtlDevice)">
            <summary>
            添加或按站点号更新设备。
            </summary>
            <param name="device">待接入的设备。</param>
            <exception cref="T:System.ArgumentNullException">device 为空。</exception>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.Remove(Ptl.Device.PtlDevice)">
            <summary>
            删除设备。
            </summary>
            <param name="device">待删除设备。</param>
            <returns>是否成功删除。</returns>
            <exception cref="T:System.ArgumentNullException">device 为空。</exception>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.Remove(System.Byte)">
            <summary>
            按站点号删除设备。
            </summary>
            <param name="address">待删除设备的站点号。</param>
            <returns>是否成功删除。</returns>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按站点号正序循环访问集合的 System.Collections.Generic.IEnumerator&lt;PtlDevice&gt;。</returns>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按站点号正序循环访问集合的 System.Collections.IEnumerator 对象。</returns>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.PtlDeviceCollection.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlDeviceCollection.Count">
            <summary>
            获取集合的大小。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlDeviceCollection.Item(System.Byte)">
            <summary>
            按站点号获取设备。
            </summary>
            <param name="address">设备站点号。</param>
        </member>
        <member name="T:Ptl.Device.PtlDeviceType">
            <summary>
            PTL 设备类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.Ptl600U">
            <summary>
            文本标签，常用于发布操作指示信息。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.Ptl900U">
            <summary>
            拣货标签，有 数量 概念。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.PtlXIOPort">
            <summary>
            提供扫描枪和灯塔的接入和控制。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.PtlXMatrixPort">
            <summary>
            提供扫描枪、灯塔、600U 的接入和控制。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.PtlMXP1O5">
            <summary>
            提供扫描枪、灯塔、600U 的接入和控制。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.PtlXDO10RFID">
            <summary>
            提供 RFID、灯塔、600U 按钮的接入和控制。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlDeviceType.PtlTera">
            <summary>
            多灯指示型标签。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl900UType">
            <summary>
            Ptl900U 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P0">
            <summary>
            无位数。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P01">
            <summary>
            1 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P02">
            <summary>
            2 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P03">
            <summary>
            3 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P04">
            <summary>
            4 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P05">
            <summary>
            5 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P11">
            <summary>
            2 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P12">
            <summary>
            3 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P13">
            <summary>
            4 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P14">
            <summary>
            5 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P15">
            <summary>
            6 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P21">
            <summary>
            3 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P22">
            <summary>
            4 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P23">
            <summary>
            5 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P24">
            <summary>
            6 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P25">
            <summary>
            7 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P31">
            <summary>
            4 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P32">
            <summary>
            5 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P33">
            <summary>
            6 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P34">
            <summary>
            7 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.P35">
            <summary>
            8 位。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.H424">
            <summary>
            三窗点阵。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.S1">
            <summary>
            单彩屏。
            </summary>
        </member>
        <member name="F:Ptl.Device.Ptl900UType.S2">
            <summary>
            双彩屏。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlTera">
            <summary>
            多灯指示型标签。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlTera.#ctor">
            <summary>
            初始化 PtlTera。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlTera.Display(System.Collections.Generic.IList{Ptl.Device.Communication.Command.LightColor})">
            <summary>
            点亮指示灯。
            </summary>
            <param name="lightColors">所有指示灯的颜色。</param>
        </member>
        <member name="M:Ptl.Device.PtlTera.Display(System.Collections.Generic.IList{Ptl.Device.Communication.Command.LightMode})">
            <summary>
            点亮指示灯。
            </summary>
            <param name="lightModes">所有指示灯的亮灯模式。</param>
        </member>
        <member name="M:Ptl.Device.PtlTera.Clear">
            <summary>
            熄灭指示灯。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlTera.Initialize">
            <summary>
            清空未完成的协议、清零通讯统计、解锁处于锁定状态的设备以及清空可能存在的显示内容。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlTera.Refresh">
            <summary>
            刷新显示。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlTera.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议，加锁解锁协议需要先执行。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.PtlTera.MaxLightsCount">
            <summary>
            获取或设置指示灯的最大数量，默认 100。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlTera.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlTera.CurrentAppearance">
            <summary>
            获取当前外观。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlTeraAppearance">
            <summary>
            Tera 的外观。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlTeraAppearance.LightModes">
            <summary>
            获取所有指示灯的亮灯模式的集合。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlTeraType">
            <summary>
            Tera 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlTeraType.A_B04002">
            <summary>
            总长 40 厘米、间隔 2 厘米的长条型。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlXDO10RFID">
            <summary>
            提供 RFID、灯塔、600U 的接入和控制。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlXDO10RFID.#ctor">
            <summary>
            初始化 XDO10RFID。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlXDO10RFID.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlXDO10RFIDType">
            <summary>
            XDO10RFID 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlXDO10RFIDType.Default">
            <summary>
            默认类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlXIOPortType">
            <summary>
            PtlXIOPort 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlXIOPortType.P1L4">
            <summary>
            1 个扫描枪接口，4 个灯塔输出控制口。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlXIOPortType.P1">
            <summary>
            1 个扫描枪接口。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlXIOPortType.L4">
            <summary>
            4 个灯塔输出控制口。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlXMatrixPort">
            <summary>
            提供扫描枪、600U 的接入和控制。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlXMatrixPort.#ctor">
            <summary>
            初始化 XMatrixPort。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlXMatrixPort.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlXMatrixPortType">
            <summary>
            PtlXMatrixPort 的类型。
            </summary>
        </member>
        <member name="F:Ptl.Device.PtlXMatrixPortType.Default">
            <summary>
            默认类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.RS485Bus">
            <summary>
            RS485 总线，上可挂接多个设备。
            </summary>
        </member>
        <member name="F:Ptl.Device.RS485Bus.lastConnectionErrorLogTime">
            <summary>
            最后记录连接错误的时间，连续的连接错误不用一直记录到日志。
            </summary>
        </member>
        <member name="M:Ptl.Device.RS485Bus.#ctor">
            <summary>
            初始化总线。
            </summary>
        </member>
        <member name="M:Ptl.Device.RS485Bus.#ctor(System.Byte)">
            <summary>
            使用指定的端口序号初始化总线。
            </summary>
            <param name="index">端口序号。</param>
        </member>
        <member name="M:Ptl.Device.RS485Bus.StartUnicastCommandQueue(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            开始执行当前总线上的所有设备的协议队列。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="M:Ptl.Device.RS485Bus.StopUnicastCommandQueue">
            <summary>
            停止执行当前总线上的所有设备的协议队列，但不等待执行线程退出。
            </summary>
        </member>
        <member name="M:Ptl.Device.RS485Bus.StopUnicastCommandQueue(System.Boolean)">
            <summary>
            停止执行当前总线上的所有设备的协议队列。
            </summary>
            <param name="waitExecuteThreadQuit">是否等待执行线程退出。</param>
        </member>
        <member name="M:Ptl.Device.RS485Bus.MulticastDisplay900U(System.Collections.Generic.ICollection{System.Byte},Ptl.Device.Ptl900UType,Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightMode)">
            <summary>
            组播显示并采集 900U，设备的小类型需一致。
            </summary>
            <param name="addresses">需组播的地址集。</param>
            <param name="deviceType">小类型。</param>
            <param name="displayItem">显示信息。</param>
            <param name="lightMode">OK 按钮灯色。</param>
            <exception cref="T:System.InvalidOperationException">没有调用 StartUnicastCommandQueue 方法来启动通讯。</exception>
            <remarks>
            调用者需确保在组播期间不会执行任何单播命令。
            </remarks>
        </member>
        <member name="M:Ptl.Device.RS485Bus.MulticastDisplay900U(System.Collections.Generic.ICollection{System.Byte},Ptl.Device.Ptl900UType,Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,Ptl.Device.Communication.Command.SurfaceBackground,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            组播显示 900U，设备的小类型需一致。
            </summary>
            <param name="addresses">需组播的地址集。</param>
            <param name="deviceType">小类型。</param>
            <param name="displayItem">显示信息。</param>
            <param name="lightMode">OK 按钮灯色。</param>
            <param name="sound">声音。</param>
            <param name="background">彩屏背景。</param>
            <param name="mustCollect">是否必须采集 OK 按钮按下动作。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
            <param name="waitMillisecondsForDisplay">等待屏幕显示的时间，用于等待 TFT 屏显示。</param>
            <exception cref="T:System.InvalidOperationException">没有调用 StartUnicastCommandQueue 方法来启动通讯。</exception>
            <remarks>
            调用者需确保在组播期间不会执行任何单播命令。
            </remarks>
        </member>
        <member name="M:Ptl.Device.RS485Bus.MulticastClear900U(System.Collections.Generic.ICollection{System.Byte})">
            <summary>
            组播清除 900U，且使用单播清除命令来确保成功。
            </summary>
            <param name="addresses">需组播的地址集。</param>
            <remarks>
            调用者需确保在组播期间不会执行任何单播命令。
            </remarks>
        </member>
        <member name="M:Ptl.Device.RS485Bus.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.RS485Bus.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.RS485Bus.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.RS485Bus.Index">
            <summary>
            获取或设置所在端口序号。
            </summary>
        </member>
        <member name="P:Ptl.Device.RS485Bus.Devices">
            <summary>
            获取其上的设备集合。
            </summary>
        </member>
        <member name="P:Ptl.Device.RS485Bus.CommunicationClient">
            <summary>
            获取通讯客户端，非通讯时此值为空。
            </summary>
            <remarks>
            在 StartUnicastCommandQueue 方法内赋值，在 StopUnicastCommandQueue 方法内清空；
            即只在通讯时此属性非空。
            </remarks>
        </member>
        <member name="T:Ptl.Device.RS485BusCollection">
            <summary>
            XGate 中的 RS485 总线集合。
            </summary>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.#ctor">
            <summary>
            使用默认总线数 2 初始化 RS485 总线集合。
            </summary>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.#ctor(System.Byte)">
            <summary>
            初始化 RS485 总线集合。
            </summary>
            <param name="count">集合大小。</param>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按接插口正序循环访问集合的 System.Collections.Generic.IEnumerator&lt;RS485Bus&gt;。</returns>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按接插口正序循环访问集合的 System.Collections.IEnumerator 对象。</returns>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.RS485BusCollection.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.RS485BusCollection.Count">
            <summary>
            获取总线数量。
            </summary>
        </member>
        <member name="P:Ptl.Device.RS485BusCollection.Item(System.Byte)">
            <summary>
            按接插口获取总线。
            </summary>
            <param name="index">基于 0 的总线索引。</param>
            <exception cref="T:System.ArgumentOutOfRangeException">index 大于等于集合大小。</exception>
        </member>
        <member name="T:Ptl.Device.Scanner">
            <summary>
            扫描枪输入口。
            </summary>
        </member>
        <member name="M:Ptl.Device.Scanner.OnScannedBarcode(Ptl.Device.ScannedBarcodeEventArgs)">
            <summary>
            引发 ScannedBarcode 事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Scanner.PerformScannedBarcode(Ptl.Device.ScannedBarcodeEventArgs)">
            <summary>
            引发 ScannedBarcode 事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Scanner.Initialize">
            <summary>
            清空未完成协议以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Scanner.Clear">
            <summary>
            显式清空条码缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Scanner.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Scanner.EnableCollect">
            <summary>
            获取或设置是否需要采集条码。
            </summary>
            <remarks>采集成功后，会自动清除设备中的条码缓存。</remarks>
        </member>
        <member name="E:Ptl.Device.Scanner.ScannedBarcode">
            <summary>
            扫描枪扫到条码事件。
            </summary>
        </member>
        <member name="T:Ptl.Device.UnhandledEventExceptionEventArgs">
            <summary>
            为 EventSubscriber.UnhandledException 事件提供事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.UnhandledEventExceptionEventArgs.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.UnhandledEventExceptionEventArgs.#ctor(System.Object,System.EventArgs,System.Exception)">
            <summary>
            使用指定的异常信息初始化未捕获的异常事件的事件参数。
            </summary>
            <param name="sourceSender">源事件发送者。</param>
            <param name="sourceEventArgs">源事件的事件参数。</param>
            <param name="exception">异常信息。</param>
            <exception cref="T:System.ArgumentNullException">exception 为空。</exception>
        </member>
        <member name="P:Ptl.Device.UnhandledEventExceptionEventArgs.SourceSender">
            <summary>
            获取或设置源事件发送者。
            </summary>
        </member>
        <member name="P:Ptl.Device.UnhandledEventExceptionEventArgs.SourceEventArgs">
            <summary>
            获取或设置源事件的事件参数。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.UnhandledEventExceptionEventArgs.Exception">
            <summary>
            获取或设置异常信息。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="T:Ptl.Device.XConverter">
            <summary>
            RS232 与 RS485 协议转换器，连接单条 RS485 总线。
            </summary>
        </member>
        <member name="M:Ptl.Device.XConverter.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.XConverter.#ctor(System.String)">
            <summary>
            使用默认的初始化控制器。
            </summary>
            <param name="portName">端口名称。</param>
        </member>
        <member name="M:Ptl.Device.XConverter.#ctor(System.String,System.Int32,System.IO.Ports.Parity,System.Int32,System.IO.Ports.StopBits,System.Int32,System.Int32)">
            <summary>
            使用指定的通讯参数初始化控制器。
            </summary>
            <param name="portName">端口名称。</param>
            <param name="baudRate">波特率。</param>
            <param name="parity">校验位。</param>
            <param name="dataBits">数据位。</param>
            <param name="stopBits">停止位。</param>
            <param name="writeTimeoutInMilliseconds">发送超时时间。</param>
            <param name="readTimeoutInMilliseconds">接收超时时间。</param>
        </member>
        <member name="M:Ptl.Device.XConverter.#ctor(System.String,System.Int32,System.IO.Ports.Parity,System.Int32,System.IO.Ports.StopBits,System.Int32,System.Int32,System.Int32)">
            <summary>
            使用指定的通讯参数初始化控制器。
            </summary>
            <param name="portName">端口名称。</param>
            <param name="baudRate">波特率。</param>
            <param name="parity">校验位。</param>
            <param name="dataBits">数据位。</param>
            <param name="stopBits">停止位。</param>
            <param name="writeTimeoutInMilliseconds">发送超时时间。</param>
            <param name="readTimeoutInMilliseconds">接收超时时间。</param>
            <param name="interframeDelayInMilliseconds">帧间延时，用于无返回值的命令之后。</param>
            <exception cref="T:System.ArgumentNullException">portName 为空或为 System.String.Empty。</exception>
        </member>
        <member name="M:Ptl.Device.XConverter.StartUnicastCommandQueue">
            <summary>
            开始执行总线上的所有设备的协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.XConverter.StopUnicastCommandQueue">
            <summary>
            停止执行总线上的所有设备的协议队列，但不等待执行线程退出。
            </summary>
        </member>
        <member name="M:Ptl.Device.XConverter.StopUnicastCommandQueue(System.Boolean)">
            <summary>
            停止执行总线上的所有设备的协议队列。
            </summary>
            <param name="waitExecuteThreadQuit">是否等待执行线程退出。</param>
        </member>
        <member name="M:Ptl.Device.XConverter.ToString">
            <summary>
            返回对象的字符串表示。
            </summary>
            <returns>串口名。</returns>
        </member>
        <member name="M:Ptl.Device.XConverter.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.XConverter.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XConverter.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XConverter.Dispose">
            <summary>
            释放托管和非托管资源。
            </summary>
        </member>
        <member name="M:Ptl.Device.XConverter.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.XConverter.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="P:Ptl.Device.XConverter.PortName">
            <summary>
            获取串口名。
            </summary>
        </member>
        <member name="P:Ptl.Device.XConverter.Bus">
            <summary>
            获取 RS485 总线。
            </summary>
        </member>
        <member name="P:Ptl.Device.XConverter.CommunicationClient">
            <summary>
            获取单播、广播公用的通讯客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XConverter.BroadcastCommunicationClient">
            <summary>
            获取广播协议执行客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XConverter.Broadcaster">
            <summary>
            获取广播协议执行者。
            </summary>
        </member>
        <member name="P:Ptl.Device.XConverter.HeartbeatGenerator">
            <summary>
            获取设备心跳发生器。
            </summary>
        </member>
        <member name="T:Ptl.Device.XGate">
            <summary>
            <para xml:lang="zh-CHS">Modbus TCP 与 Modbus RTU/ASCII 协议转换器，可接插多条 RS485 总线。</para>
            <para xml:lang="en"></para>
            </summary>
            <remarks>
            <para xml:lang="zh-CHS">设备在断网后自行决定显示内容，此时的外观不能被跟踪。</para>
            <para xml:lang="en"></para>
            </remarks>
        </member>
        <member name="F:Ptl.Device.XGate.DefaultIPAddress">
            <summary>
            默认的 IP 地址：************。
            </summary>
        </member>
        <member name="F:Ptl.Device.XGate.DefaultIPEndPointPort">
            <summary>
            默认的 IP 终结点端口：502。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.#ctor">
            <summary>
            使用默认的 IP ************、端口 502 和总线数 4 初始化控制器。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.#ctor(System.String)">
            <summary>
            使用指定的 IP，默认的端口 502 和总线数 4 初始化控制器。
            </summary>
            <param name="ip">IP 地址。</param>
        </member>
        <member name="M:Ptl.Device.XGate.#ctor(System.Net.IPAddress)">
            <summary>
            使用指定的 IP，默认的端口 502 和总线数 4 初始化控制器。
            </summary>
            <param name="ipAddress">IP 地址。</param>
        </member>
        <member name="M:Ptl.Device.XGate.#ctor(System.Net.IPAddress,System.Byte)">
            <summary>
            使用指定的 IP，默认的端口 502 和指定的总线数量初始化控制器。
            </summary>
            <param name="ipAddress">IP 地址。</param>
            <param name="busCount">总线数量。</param>
        </member>
        <member name="M:Ptl.Device.XGate.#ctor(System.Net.IPEndPoint,System.Byte)">
            <summary>
            使用指定的 IP 终结点和总线数量初始化控制器。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <param name="busCount">总线数量。</param>
            <exception cref="T:System.ArgumentNullException">ipEndPoint 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGate.StartUnicastCommandQueue">
            <summary>
            开始执行自己及所有总线上的所有设备的协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.StartUnicastCommandQueue(System.Int32,System.Int32,System.Int32)">
            <summary>
            开始执行自己及所有总线上的所有设备的协议队列。
            </summary>
            <param name="connectTimeoutInMilliseconds">连接超时时间。</param>
            <param name="sendTimeoutInMilliseconds">发送超时毫秒数。</param>
            <param name="receivedTimeoutInMilliseconds">接收超时毫秒数。</param>
        </member>
        <member name="M:Ptl.Device.XGate.StartUnicastCommandQueue(System.Int32,System.Int32,System.Int32,Ptl.Device.Communication.MapRS485Address)">
            <summary>
            开始执行自己及所有总线上的所有设备的协议队列。
            </summary>
            <param name="connectTimeoutInMilliseconds">连接超时时间。</param>
            <param name="sendTimeoutInMilliseconds">发送超时毫秒数。</param>
            <param name="receivedTimeoutInMilliseconds">接收超时毫秒数。</param>
            <param name="mapAddress">RS485 地址映射方法。</param>
        </member>
        <member name="M:Ptl.Device.XGate.StopUnicastCommandQueue">
            <summary>
            停止执行自己及所有总线上的所有设备的协议队列，但不等待执行线程退出。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.StopUnicastCommandQueue(System.Boolean)">
            <summary>
            停止执行自己及所有总线上的所有设备的协议队列。
            </summary>
            <param name="waitExecuteThreadQuit">是否等待执行线程退出。</param>
        </member>
        <member name="M:Ptl.Device.XGate.ToString">
            <summary>
            返回对象的字符串表示。
            </summary>
            <returns>IP 终结点。</returns>
        </member>
        <member name="M:Ptl.Device.XGate.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.XGate.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGate.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGate.Dispose">
            <summary>
            释放托管和非托管资源。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.XGate.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="F:Ptl.Device.XGate.lastConnectionErrorLogTime">
            <summary>
            最后记录单播通讯客户端连接错误的时间，连续的连接错误不用一直记录到日志。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.OnInputPortStatusChanged(Ptl.Device.InputPortStatusChangedEventArgs)">
            <summary>
            引发输入口状态改变事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.XGate.OnAppearanceChanged(System.EventArgs)">
            <summary>
            引发设备的当前外观更改事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.XGate.DisplayLight(Ptl.Device.Communication.Command.LightMode)">
            <summary>
            显示前面板灯。
            </summary>
            <param name="lightMode">点亮模式。</param>
        </member>
        <member name="M:Ptl.Device.XGate.ClearLight">
            <summary>
            清除前面板灯。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.GetInputPortStatuses">
            <summary>
            获取所有输入口状态的集合。
            </summary>
            <returns>所有输入口状态的集合。</returns>
        </member>
        <member name="M:Ptl.Device.XGate.DisplayOutputPort(System.Byte,Ptl.Device.Communication.Command.LightOnOffPeriod,Ptl.Device.Communication.Command.LightOnOffRatio)">
            <summary>
            点亮指定的输出口。
            </summary>
            <param name="portIndex">基于 0 的索引。</param>
            <param name="onOffPeriod">亮灭周期。</param>
            <param name="onOffRatio">亮灭比例。</param>
        </member>
        <member name="M:Ptl.Device.XGate.ClearOutputPort(System.Byte)">
            <summary>
            清除指定的输出口。
            </summary>
            <param name="portIndex">基于 0 的索引。</param>
        </member>
        <member name="M:Ptl.Device.XGate.SetOutputPort(System.Byte,Ptl.Device.Communication.XGateSettings.OutputPortStatus)">
            <summary>
            设置指定的输出口。
            </summary>
            <param name="portIndex">基于 0 的索引。</param>
            <param name="status">状态。</param>
        </member>
        <member name="M:Ptl.Device.XGate.Refresh">
            <summary>
            刷新显示面板。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.ClearCommandQueue">
            <summary>
            清空协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGate.Initialize">
            <summary>
            清空未完成的协议、清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.IPEndPoint">
            <summary>
            获取或设置控制器的 IP 终结点。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.Buses">
            <summary>
            获取 RS485 总线集合。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.DefaultRS485AddressMapper">
            <summary>
            获取或设置默认的 RS485 地址映射方式。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.BroadcastCommunicationClient">
            <summary>
            获取广播协议执行客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.Broadcaster">
            <summary>
            获取广播协议执行者。
            </summary>
        </member>
        <member name="E:Ptl.Device.XGate.InputPortStatusChanged">
            <summary>
            输入口状态改变事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.XGate.AppearanceChanged">
            <summary>
            设备的当前外观更改事件。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.SettingsExecutorCommunicationClient">
            <summary>
            获取设置协议执行客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.SettingsExecutor">
            <summary>
            获取设置协议执行者。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.XGateUnicastCommunicationClient">
            <summary>
            获取单播协议执行客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.CommandCount">
            <summary>
            获取队列中未完成的命令数。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.EnableLight">
            <summary>
            获取或设置是否启用前面板灯。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.InputPortsCount">
            <summary>
            获取或设置是否启用输入口。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.OutputPortsCount">
            <summary>
            获取或设置是否启用输出口。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGate.CurrentAppearance">
            <summary>
            获取当前外观。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl600U">
            <summary>
            文本标签，常用于发布操作指示信息。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl600U.#ctor">
            <summary>
            初始化 Ptl600U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl600U.OnPressed(System.EventArgs)">
            <summary>
            引发 Pressed 事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.PerformPressed">
            <summary>
            引发 Pressed 事件。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl600U.OnClearAll(System.EventArgs)">
            <summary>
            引发 ClearAll 事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(System.String,Ptl.Device.Communication.Command.LightColor)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="text">ASCII 文本。</param>
            <param name="lightColor">按钮灯色。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(System.String,Ptl.Device.Communication.Command.LightColor,System.Boolean)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="text">ASCII 文本。</param>
            <param name="lightColor">按钮灯色。</param>
            <param name="enableCollect">是否启用采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(System.String,Ptl.Device.Communication.Command.LightMode)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="text">ASCII 文本。</param>
            <param name="lightMode">按钮亮灯模式。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(System.String,Ptl.Device.Communication.Command.LightMode,System.Boolean)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="text">ASCII 文本。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="enableCollect">是否启用采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(System.Byte,System.String,Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,System.Boolean)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="rowIndex">行号。</param>
            <param name="text">ASCII 文本。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="sound">声音。</param>
            <param name="enableCollect">是否启用采集。</param>
            <remarks>调用本方法前调用 ClearCommandQueue() 能清除上一次未完成的任务；调用 Clear(Clear600UMode.Button) 能清除非预期的按钮状态缓存。</remarks>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(Ptl.Device.Communication.Command.Display600UContent,Ptl.Device.Communication.Command.LightColor)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="content">内容。</param>
            <param name="lightColor">按钮灯色。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(Ptl.Device.Communication.Command.Display600UContent,Ptl.Device.Communication.Command.LightColor,System.Boolean)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="content">内容。</param>
            <param name="lightColor">按钮灯色。</param>
            <param name="enableCollect">是否启用采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(Ptl.Device.Communication.Command.Display600UContent,Ptl.Device.Communication.Command.LightMode)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="content">内容。</param>
            <param name="lightMode">按钮亮灯模式。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(Ptl.Device.Communication.Command.Display600UContent,Ptl.Device.Communication.Command.LightMode,System.Boolean)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="content">内容。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="enableCollect">是否启用采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Display(Ptl.Device.Communication.Command.Display600UContent,Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,System.Boolean)">
            <summary>
            显示指定的内容。
            </summary>
            <param name="content">内容。</param>
            <param name="lightMode">按钮的亮灯模式。</param>
            <param name="sound">声音。</param>
            <param name="enableCollect">是否启用采集。</param>
            <remarks>调用本方法前调用 ClearCommandQueue() 能清除上一次未完成的任务；调用 Clear(Clear600UMode.Button) 能清除非预期的按钮状态缓存。</remarks>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Initialize">
            <summary>
            清空未完成的协议、清零通讯统计、解锁处于锁定状态的设备以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Clear(Ptl.Device.Communication.Command.Clear600UMode)">
            <summary>
            清空指定的内容。
            </summary>
            <param name="mode">清空方式。</param>
            <remarks>
            推荐使用 Display(string.Empty, LightColor.Off) 代替。
            </remarks>
        </member>
        <member name="M:Ptl.Device.Ptl600U.Refresh">
            <summary>
            刷新显示。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl600U.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议，加锁解锁协议需要先执行。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Ptl600U.SurfaceTextEncoding">
            <summary>
            获取或设置 Surface 型标签上文本的编码，默认 gb2312。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.Ptl600U.MaxTextLength">
            <summary>
            获取或设置 ASCII 文本的最大长度，默认 20。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600U.MaxTitleLength">
            <summary>
            获取或设置标题的最大字节数，默认 36。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600U.MaxSubTitleLength">
            <summary>
            获取或设置副标题的最大字节数，默认 36。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600U.MaxDescriptionLength">
            <summary>
            获取或设置描述的最大字节数，默认 60。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600U.MaxDescriptionLineCount">
            <summary>
            获取或设置描述的最大行数，默认 3。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600U.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl600U.CurrentAppearance">
            <summary>
            获取当前外观。
            </summary>
        </member>
        <member name="E:Ptl.Device.Ptl600U.Pressed">
            <summary>
            OK 按钮被按下事件。
            </summary>
        </member>
        <member name="E:Ptl.Device.Ptl600U.ClearAll">
            <summary>
            执行了 Clear(Clear600UMode.All) 方法的事件。
            </summary>
        </member>
        <member name="T:Ptl.Device.Ptl900U">
            <summary>
            拣货标签，有 数量 概念。
            </summary>
            <remarks>
            设备自行灭灯并显示后备明细；
            所以在不启用采集时，单个显示项的熄灭和多个显示项中最后一项的当前外观不能被跟踪。
            </remarks>
        </member>
        <member name="M:Ptl.Device.Ptl900U.#ctor">
            <summary>
            初始化 Ptl900U。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl900U.OnPressed(Ptl.Device.Ptl900UPressedEventArgs)">
            <summary>
            引发 Pressed 事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.PerformPressed(Ptl.Device.Ptl900UPressedEventArgs)">
            <summary>
            引发 Pressed 事件。
            </summary>
            <param name="e">事件参数。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightColor)">
            <summary>
            显示指定内容。
            </summary>
            <param name="item">显示项。</param>
            <param name="lightColor">按钮灯色。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightColor,System.Boolean)">
            <summary>
            显示指定内容。
            </summary>
            <param name="item">显示项。</param>
            <param name="lightColor">按钮灯色。</param>
            <param name="mustCollect">是否必须采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightMode,System.Boolean)">
            <summary>
            显示指定内容。
            </summary>
            <param name="item">显示项。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="mustCollect">是否必须采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(System.Collections.Generic.ICollection{Ptl.Device.Communication.Command.Display900UItem},Ptl.Device.Communication.Command.LightColor)">
            <summary>
            显示指定内容。
            </summary>
            <param name="items">显示项集合。</param>
            <param name="lightColor">按钮灯色。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(System.Collections.Generic.ICollection{Ptl.Device.Communication.Command.Display900UItem},Ptl.Device.Communication.Command.LightColor,System.Boolean)">
            <summary>
            显示指定内容。
            </summary>
            <param name="items">显示项集合。</param>
            <param name="lightColor">按钮灯色。</param>
            <param name="mustCollect">是否必须采集。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(System.Collections.Generic.ICollection{Ptl.Device.Communication.Command.Display900UItem},Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            显示指定内容。
            </summary>
            <param name="items">显示项集合。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="sound">声音。</param>
            <param name="mustCollect">是否必须采集。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
            <param name="waitMillisecondsForDisplay">等待屏幕显示的时间，用于等待 TFT 屏显示。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Display(System.Collections.Generic.ICollection{Ptl.Device.Communication.Command.Display900UItem},Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,Ptl.Device.Communication.Command.SurfaceBackground,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            显示指定内容。
            </summary>
            <param name="items">显示项集合。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="sound">声音。</param>
            <param name="background">彩屏背景。</param>
            <param name="mustCollect">是否必须采集。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
            <param name="waitMillisecondsForDisplay">等待屏幕显示的时间，用于等待 TFT 屏显示。</param>
            <exception cref="T:System.ArgumentNullException">items 为空或任意元素为空或元素数为零。</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">items 元素数大于 255。</exception>
            <remarks>调用本方法前依次调用 ClearCommandQueue() 和 Clear() 能清除上一次未完成任务的设备状态缓存。</remarks>
        </member>
        <member name="M:Ptl.Device.Ptl900U.BuildDisplayCommand(System.Collections.Generic.ICollection{Ptl.Device.Communication.Command.Display900UItem},Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,Ptl.Device.Communication.Command.SurfaceBackground,System.Boolean,System.Boolean,System.Int32)">
            <summary>
            创建显示命令。
            </summary>
            <param name="items">显示项集合。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="sound">声音。</param>
            <param name="background">彩屏背景。</param>
            <param name="mustCollect">是否必须采集。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
            <param name="waitMillisecondsForDisplay">等待屏幕显示的时间，用于等待 TFT 屏显示。</param>
            <returns>显示命令。</returns>
            <exception cref="T:System.ArgumentNullException">items 为空或任意元素为空或元素数为零。</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">items 元素数大于 255。</exception>
        </member>
        <member name="M:Ptl.Device.Ptl900U.DisplayAfterMulticast(Ptl.Device.Communication.Command.Display900UItem,Ptl.Device.Communication.Command.LightMode,Ptl.Device.Communication.Command.SoundMode,Ptl.Device.Communication.Command.SurfaceBackground,System.Boolean,System.Boolean)">
            <summary>
            在组播显示命令之后加入已完成部分作业的单播显示命令。
            </summary>
            <param name="displayItem">显示项。</param>
            <param name="lightMode">按钮亮灯模式。</param>
            <param name="sound">声音。</param>
            <param name="background">彩屏背景。</param>
            <param name="mustCollect">是否必须采集。</param>
            <param name="enableF1OkColorAfterPress">是否启用 F1 功能。</param>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Initialize">
            <summary>
            清空未完成的协议、清零通讯统计、解锁处于锁定状态的设备以及清空可能存在的显示内容和状态缓存。
            </summary>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Clear">
            <summary>
            清空内容。
            </summary>
            <remarks>常在此方法之前调用 ClearCommandQueue() 以清除上一次未完成的任务。</remarks>
        </member>
        <member name="M:Ptl.Device.Ptl900U.Refresh">
            <summary>
            刷新显示。
            </summary>
            <remarks>
            无法刷新因启用 F1 功能而保留的灯色。
            </remarks>
        </member>
        <member name="M:Ptl.Device.Ptl900U.ExecuteFirstCommand(Ptl.Device.Communication.ICommunicationClient)">
            <summary>
            使用指定的通讯客户端执行第一个协议，加锁解锁协议需要先执行。
            </summary>
            <param name="communicationClient">通讯客户端。</param>
            <returns>执行结果统计。</returns>
            <exception cref="T:System.ArgumentNullException">communicationClient 为空。</exception>
        </member>
        <member name="P:Ptl.Device.Ptl900U.SurfaceTextEncoding">
            <summary>
            获取或设置 Surface 型标签上文本的编码，默认 gb2312。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.Ptl900U.MaxBatchCodeLength">
            <summary>
            获取或设置批号的最大长度，默认 12。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.MaxNameLength">
            <summary>
            获取或设置品名的最大字节数，默认 36。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.MaxDescriptionLength">
            <summary>
            获取或设置描述的最大字节数，默认 60。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.MaxDescriptionLineCount">
            <summary>
            获取或设置描述的最大行数，默认 3。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.FixLongSubLocationLength">
            <summary>
            获取或设置长行列码的固定字节数，默认 4。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.FixUnitLength">
            <summary>
            获取或设置单位的固定字节数，默认 2。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.MaxDeviceStoredItemsCount">
            <summary>
            获取或设置设备上可驻留的最大项数，默认 2。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.EmptySubLocation">
            <summary>
            获取或设置空白行列码，默认 0xFF。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.F1OkColorAfterPress">
            <summary>
            获取或设置 F1 值。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="P:Ptl.Device.Ptl900U.CurrentAppearance">
            <summary>
            获取当前外观。
            </summary>
        </member>
        <member name="E:Ptl.Device.Ptl900U.Pressed">
            <summary>
            OK 按钮被按下事件。
            </summary>
        </member>
        <member name="T:Ptl.Device.PtlXIOPort">
            <summary>
            提供扫描枪和灯塔的接入和控制功能；灯塔不支持闪烁。
            </summary>
        </member>
        <member name="M:Ptl.Device.PtlXIOPort.#ctor">
            <summary>
            初始化 XIOPort。
            </summary>
        </member>
        <member name="P:Ptl.Device.PtlXIOPort.MinorType">
            <summary>
            获取或设置小类型。
            </summary>
        </member>
        <member name="T:Ptl.Device.ScannedBarcodeEventArgs">
            <summary>
            为 Scanner.ScannedBarcode 事件提供事件参数。
            </summary>
        </member>
        <member name="M:Ptl.Device.ScannedBarcodeEventArgs.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.ScannedBarcodeEventArgs.#ctor(System.String)">
            <summary>
            使用指定的条码初始化扫到条码事件的事件参数。
            </summary>
            <param name="barcode">条码。</param>
            <exception cref="T:System.ArgumentNullException">barcode 为空或空字符串。</exception>
        </member>
        <member name="P:Ptl.Device.ScannedBarcodeEventArgs.Barcode">
            <summary>
            获取或设置扫描到的条码。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值或空字符串。</exception>
        </member>
        <member name="T:Ptl.Device.Log.DefaultTextFileLogger">
            <summary>
            默认的文本文件日志记录器。
            </summary>
        </member>
        <member name="T:Ptl.Device.Log.ILogger">
            <summary>
            提供简单的日志纪录功能；作为运行状态的记录者，此接口的实现类不应抛出异常来干扰运行。
            </summary>
        </member>
        <member name="M:Ptl.Device.Log.ILogger.Write(System.String,System.String,Ptl.Device.Log.LogLevel)">
            <summary>
            尝试纪录日志。
            </summary>
            <param name="source">源。</param>
            <param name="message">信息。</param>
            <param name="level">级别。</param>
        </member>
        <member name="M:Ptl.Device.Log.ILogger.Delete(System.DateTime)">
            <summary>
            删除给定有效期前的记录。
            </summary>
            <param name="expiryDate">有效期。</param>
        </member>
        <member name="P:Ptl.Device.Log.ILogger.IsInformationEnabled">
            <summary>
            获取或设置是否纪录普通信息。
            </summary>
        </member>
        <member name="P:Ptl.Device.Log.ILogger.IsWarningEnabled">
            <summary>
            获取或设置是否纪录警告。
            </summary>
        </member>
        <member name="P:Ptl.Device.Log.ILogger.IsErrorEnabled">
            <summary>
            获取或设置是否纪录错误。
            </summary>
        </member>
        <member name="M:Ptl.Device.Log.DefaultTextFileLogger.#ctor">
            <summary>
            使用默认的目录 App_Data\PtlDeviceLog 初始化文本文件日志记录器。
            </summary>
        </member>
        <member name="M:Ptl.Device.Log.DefaultTextFileLogger.#ctor(System.String)">
            <summary>
            使用指定的目录初始化文本文件日志记录器。
            </summary>
            <param name="folder">日志将按日期、源-级别被分层纪录到此目录下。</param>
            <remarks>folder 为本日志记录器专用，在其下放置其他的型如日期的目录可能会被误删。</remarks>
        </member>
        <member name="M:Ptl.Device.Log.DefaultTextFileLogger.EnableDebug">
            <summary>
            仅在调试模式中启用调试日志。
            </summary>
        </member>
        <member name="M:Ptl.Device.Log.DefaultTextFileLogger.WriteDebug(System.String,System.String)">
            <summary>
            纪录调试日志。
            </summary>
            <param name="source">源。</param>
            <param name="message">信息。</param>
        </member>
        <member name="M:Ptl.Device.Log.DefaultTextFileLogger.Write(System.String,System.String,Ptl.Device.Log.LogLevel)">
            <summary>
            纪录日志。
            </summary>
            <param name="source">源。</param>
            <param name="message">信息。</param>
            <param name="level">级别。</param>
        </member>
        <member name="M:Ptl.Device.Log.DefaultTextFileLogger.Delete(System.DateTime)">
            <summary>
            删除给定有效期前的记录。
            </summary>
            <param name="expiryDate">有效期。</param>
        </member>
        <member name="P:Ptl.Device.Log.DefaultTextFileLogger.Folder">
            <summary>
            获取日志文件所在目录。
            </summary>
        </member>
        <member name="P:Ptl.Device.Log.DefaultTextFileLogger.IsInformationEnabled">
            <summary>
            获取或设置是否纪录普通信息。
            </summary>
        </member>
        <member name="P:Ptl.Device.Log.DefaultTextFileLogger.IsWarningEnabled">
            <summary>
            获取或设置是否纪录警告。
            </summary>
        </member>
        <member name="P:Ptl.Device.Log.DefaultTextFileLogger.IsErrorEnabled">
            <summary>
            获取或设置是否纪录错误。
            </summary>
        </member>
        <member name="T:Ptl.Device.Log.Logger">
            <summary>
            提供对日志功能的全局访问。
            </summary>
        </member>
        <member name="P:Ptl.Device.Log.Logger.Instance">
            <summary>
            获取或设置日志记录器，默认为 DefaultTextFileLogger。
            </summary>
            <exception cref="T:System.ArgumentNullException">设置时使用空值。</exception>
        </member>
        <member name="P:Ptl.Device.Log.Logger.HoldingPeriodInDays">
            <summary>
            获取或设置日志的保存天数，小于或等于零时始终保存。
            </summary>
        </member>
        <member name="T:Ptl.Device.Log.LogLevel">
            <summary>
            日志的级别。
            </summary>
        </member>
        <member name="F:Ptl.Device.Log.LogLevel.Debug">
            <summary>
            调试信息。
            </summary>
        </member>
        <member name="F:Ptl.Device.Log.LogLevel.Information">
            <summary>
            普通信息。
            </summary>
        </member>
        <member name="F:Ptl.Device.Log.LogLevel.Wraning">
            <summary>
            警告。
            </summary>
        </member>
        <member name="F:Ptl.Device.Log.LogLevel.Error">
            <summary>
            错误。
            </summary>
        </member>
        <member name="T:Ptl.Device.Properties.Resources">
            <summary>
              一个强类型的资源类，用于查找本地化的字符串等。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ResourceManager">
            <summary>
              返回此类使用的缓存的 ResourceManager 实例。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Culture">
            <summary>
              使用此强类型资源类，为所有资源查找
              重写当前线程的 CurrentUICulture 属性。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Clear600USuccessfulResultFormat">
            <summary>
              查找类似 已清除 600U，方式：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Clear900USuccessfulResult">
            <summary>
              查找类似 已清除 900U。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ClearLighthouseSuccessfulResultFormat">
            <summary>
              查找类似 已清除灯塔，灯索引：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ClearScannerSuccessfulResult">
            <summary>
              查找类似 已清除条码缓存。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ClearTeraSuccessfulResult">
            <summary>
              查找类似 已清除 Tera。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Collect600USuccessfulResult">
            <summary>
              查找类似 已按下 600U 按钮。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Collect900USuccessfulResultFormat">
            <summary>
              查找类似 已按下 900U 按钮，明细：{0}，Fn 被按下：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Collect900USuccessfulResultItemFormat">
            <summary>
              查找类似 [{0}] 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.CollectScannerSuccessfulResultFormat">
            <summary>
              查找类似 已采集到条码：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.CrcNotTheSameFormat">
            <summary>
              查找类似 Crc 不一致；实际值：{0}，期望值：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.DeviceAddressNotTheSameFormat">
            <summary>
              查找类似 设备的站点号不一致；实际值：{0}，期望值：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.DeviceStoredCountLitterThanCollectedCount">
            <summary>
              查找类似 设备内的项数小于采集项数；设备内：{0}，采集结果：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display600USuccessfulResultFormat">
            <summary>
              查找类似 600U 已显示，行索引：{0}，ASCII 文本：{1}，标题：{2}，副标题：{3}，描述：{4}，图片：{5}，背景：{6}，按钮灯色：{7}，按钮亮灭周期：{8}，按钮亮灭比例：{9}，蜂鸣：{10}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display900UItemCanNotBeNull">
            <summary>
              查找类似 900U 显示协议中需下发的项集合中不能有空元素。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display900UItemsCanNotBeEmpty">
            <summary>
              查找类似 900U 显示协议中需下发的项集合不可为空。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display900UItemsCountCanNotBigger255">
            <summary>
              查找类似 900U 显示项数不能大于 255。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display900UItemsCountCanNotBiggerThanMaxDeviceStoredItemsCount">
            <summary>
              查找类似 900U 显示协议中需下发的项集合数不能超出设备可存储的项数。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display900USuccessfulResultFormat">
            <summary>
              查找类似 900U 已显示，明细：{0}，按钮灯色：{1}，按钮亮灭周期：{2}，按钮亮灭比例：{3}，蜂鸣：{4}，背景：{5}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.Display900USuccessfulResultItemFormat">
            <summary>
              查找类似 [批号：{0}，名称：{1}，描述：{2}，方向：{3}，单位：{4}，行列码：{5:X2}，长行列码：{6}，数量：{7}] 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.DisplayLighthouseSuccessfulResultFormat">
            <summary>
              查找类似 已点亮灯塔，灯索引：{0}，亮灭周期：{1}，亮灭比例：{2}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.DisplayTeraSuccessfulResultFormat">
            <summary>
              查找类似 Tera 已显示，共 {0} 项：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.FnLongPressedFormat">
            <summary>
              查找类似 Fn 键被长按；跳过 {0} 条待下发明细，设备自行删除 {1} 条未作业明细。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.LockSuccessfulResult">
            <summary>
              查找类似 已锁定。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ModbusCommandBodyIsEmpty">
            <summary>
              查找类似 Modbus 协议内容是空的。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ModbusTransactionIdNotTheSameFormat">
            <summary>
              查找类似 Modbus 事务编号不一致；实际值：{0}，期望值：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.MulticastDeviceHasUnfinishedCommandFormat">
            <summary>
              查找类似 组播列表中地址为 {0} 的设备还有未完成的命令，已移除。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.MulticastDeviceMinorTypeNotTheSame">
            <summary>
              查找类似 组播列表中有的设备类型不一致。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.MulticastDeviceNotPtl900U">
            <summary>
              查找类似 组播列表中有的设备不是 Ptl900U。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.MulticastDeviceOffline">
            <summary>
              查找类似 组播列表中有的设备不在当前总线。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.NeedStartUnicastCommandQueue">
            <summary>
              查找类似 需调用 StartUnicastCommandQueue 启动通讯。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.ReceivedDataFormat">
            <summary>
              查找类似 接收；地址：{0}，内容：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.SentDataFormat">
            <summary>
              查找类似 发送；地址：{0}，内容：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.StringOutOfRangeFormat">
            <summary>
              查找类似 字符串长度超出范围，截断后：{0}，原值：{1}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.UnlockSuccessfulResult">
            <summary>
              查找类似 已解锁。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.XConverterStartFormat">
            <summary>
              查找类似 XConverter 已启动，端口：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.XConverterStopFormat">
            <summary>
              查找类似 XConverter 已停止，端口：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.XGateStartFormat">
            <summary>
              查找类似 XGate 已启动，地址：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.XGateStopFormat">
            <summary>
              查找类似 XGate 已停止，地址：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.XRelayStartFormat">
            <summary>
              查找类似 XRelay 已启动，地址：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="P:Ptl.Device.Properties.Resources.XRelayStopFormat">
            <summary>
              查找类似 XRelay 已停止，地址：{0}。 的本地化字符串。
            </summary>
        </member>
        <member name="T:Ptl.Device.XGateAppearance">
            <summary>
            XGate 的外观。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGateAppearance.Light">
            <summary>
            获取或设置亮灯模式。
            </summary>
            <value>为空时会被 LightMode.Default 取代。</value>
        </member>
        <member name="P:Ptl.Device.XGateAppearance.InputPortStatuses">
            <summary>
            获取所有输入口状态的集合.
            </summary>
        </member>
        <member name="P:Ptl.Device.XGateAppearance.OutputPortStatuses">
            <summary>
            获取所有输出口状态的集合.
            </summary>
        </member>
        <member name="T:Ptl.Device.XGateCollection">
            <summary>
            XGate 集合。
            </summary>
        </member>
        <member name="M:Ptl.Device.XGateCollection.AddOrUpdate(Ptl.Device.XGate)">
            <summary>
            添加或按 IP 终结点更新 XGate。
            </summary>
            <param name="xgate">待加入的 XGate。</param>
            <exception cref="T:System.ArgumentNullException">xgate 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGateCollection.Remove(Ptl.Device.XGate)">
            <summary>
            删除 XGate。
            </summary>
            <param name="xgate">待删除的 XGate。</param>
            <returns>是否成功删除。</returns>
            <exception cref="T:System.ArgumentNullException">xgate 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGateCollection.Remove(System.String)">
            <summary>
            按 IP 地址和默认端口 502 删除 XGate。
            </summary>
            <param name="ip">待删除 XGate 的 IP 地址。</param>
            <returns>是否成功删除。</returns>
            <exception cref="T:System.ArgumentNullException">ip 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGateCollection.Remove(System.Net.IPAddress)">
            <summary>
            按 IP 地址和默认端口 502 删除 XGate。
            </summary>
            <param name="ip">待删除 XGate 的 IP 地址。</param>
            <returns>是否成功删除。</returns>
            <exception cref="T:System.ArgumentNullException">ip 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGateCollection.Remove(System.Net.IPEndPoint)">
            <summary>
            按 IP 终结点删除 XGate。
            </summary>
            <param name="ipEndPoint">待删除 XGate 的 IP 终结点。</param>
            <returns>是否成功删除。</returns>
            <exception cref="T:System.ArgumentNullException">ipEndPoint 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGateCollection.GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按接插口正序循环访问集合的 System.Collections.Generic.IEnumerator&lt;XGate&gt;。</returns>
        </member>
        <member name="M:Ptl.Device.XGateCollection.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            获取迭代器。
            </summary>
            <returns>可用于按接插口正序循环访问集合的 System.Collections.IEnumerator 对象。</returns>
        </member>
        <member name="M:Ptl.Device.XGateCollection.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.XGateCollection.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XGateCollection.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="P:Ptl.Device.XGateCollection.Count">
            <summary>
            获取 XGate 数量。
            </summary>
        </member>
        <member name="P:Ptl.Device.XGateCollection.Item(System.String)">
            <summary>
            按 IP 地址和默认端口 502 获取 XGate 。
            </summary>
            <param name="ip">IP 地址。</param>
            <exception cref="T:System.ArgumentNullException">ip 为空。</exception>
        </member>
        <member name="P:Ptl.Device.XGateCollection.Item(System.Net.IPAddress)">
            <summary>
            按 IP 地址和默认端口 502 获取 XGate 。
            </summary>
            <param name="ip">IP 地址。</param>
            <exception cref="T:System.ArgumentNullException">ip 为空。</exception>
        </member>
        <member name="P:Ptl.Device.XGateCollection.Item(System.Net.IPEndPoint)">
            <summary>
            按 IP 终结点获取 XGate 。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <exception cref="T:System.ArgumentNullException">ipEndPoint 为空。</exception>
        </member>
        <member name="T:Ptl.Device.XRelay">
            <summary>
            TCP-RS232/485 透传中继器，连接单条 RS485 总线。
            </summary>
        </member>
        <member name="M:Ptl.Device.XRelay.#ctor">
            <summary>
            为了可序列化。
            </summary>
        </member>
        <member name="M:Ptl.Device.XRelay.#ctor(System.String,System.Int32)">
            <summary>
            使用指定的通讯参数初始化中继器。
            </summary>
            <param name="ip">IP 地址。</param>
            <param name="port">TCP 端口。</param>
        </member>
        <member name="M:Ptl.Device.XRelay.#ctor(System.Net.IPAddress,System.Int32)">
            <summary>
            使用指定的通讯参数初始化中继器。
            </summary>
            <param name="iPAddress">IP 地址。</param>
            <param name="port">TCP 端口。</param>
        </member>
        <member name="M:Ptl.Device.XRelay.#ctor(System.Net.IPEndPoint)">
            <summary>
            使用指定的通讯参数初始化中继器。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
        </member>
        <member name="M:Ptl.Device.XRelay.#ctor(System.Net.IPEndPoint,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
            使用指定的通讯参数初始化中继器。
            </summary>
            <param name="ipEndPoint">IP 终结点。</param>
            <param name="connectTimeoutInMilliseconds">连接超时时间。</param>
            <param name="sendTimeoutInMilliseconds">发送超时时间。</param>
            <param name="receiveTimeoutInMilliseconds">接收超时时间。</param>
            <param name="interframeDelayInMilliseconds">帧间延时，用于无返回值的命令之后。</param>
            <exception cref="T:System.ArgumentNullException">ipEndPoint 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XRelay.StartUnicastCommandQueue">
            <summary>
            开始执行总线上的所有设备的协议队列。
            </summary>
        </member>
        <member name="M:Ptl.Device.XRelay.StopUnicastCommandQueue">
            <summary>
            停止执行总线上的所有设备的协议队列，但不等待执行线程退出。
            </summary>
        </member>
        <member name="M:Ptl.Device.XRelay.StopUnicastCommandQueue(System.Boolean)">
            <summary>
            停止执行总线上的所有设备的协议队列。
            </summary>
            <param name="waitExecuteThreadQuit">是否等待执行线程退出。</param>
        </member>
        <member name="M:Ptl.Device.XRelay.ToString">
            <summary>
            返回对象的字符串表示。
            </summary>
            <returns>IP 终结点。</returns>
        </member>
        <member name="M:Ptl.Device.XRelay.GetSchema">
            <summary>
            此方法是保留方法，请不要使用。
            </summary>
            <returns>始终返回空。</returns>
        </member>
        <member name="M:Ptl.Device.XRelay.WriteXml(System.Xml.XmlWriter)">
            <summary>
            将对象转换为其 XML 表示形式。
            </summary>
            <param name="writer">对象要序列化为的 System.Xml.XmlWriter 流。</param>
            <exception cref="T:System.ArgumentNullException">writer 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XRelay.ReadXml(System.Xml.XmlReader)">
            <summary>
            从对象的 XML 表示形式生成该对象。
            </summary>
            <param name="reader">对象从中进行反序列化的 System.Xml.XmlReader 流。</param>
            <exception cref="T:System.ArgumentNullException">reader 为空。</exception>
        </member>
        <member name="M:Ptl.Device.XRelay.Dispose">
            <summary>
            释放托管和非托管资源。
            </summary>
        </member>
        <member name="M:Ptl.Device.XRelay.Dispose(System.Boolean)">
            <summary>
            释放非托管资源并可选地释放托管资源。
            </summary>
            <param name="disposing">是否释放托管资源。</param>
        </member>
        <member name="M:Ptl.Device.XRelay.Finalize">
            <summary>
            释放非托管资源。
            </summary>
        </member>
        <member name="P:Ptl.Device.XRelay.IPEndPoint">
            <summary>
            获取或设置中继器的 IP 终结点。
            </summary>
        </member>
        <member name="P:Ptl.Device.XRelay.Bus">
            <summary>
            获取 RS485 总线。
            </summary>
        </member>
        <member name="P:Ptl.Device.XRelay.CommunicationClient">
            <summary>
            获取单播、广播公用的通讯客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XRelay.BroadcastCommunicationClient">
            <summary>
            获取广播协议执行客户端。
            </summary>
        </member>
        <member name="P:Ptl.Device.XRelay.Broadcaster">
            <summary>
            获取广播协议执行者。
            </summary>
        </member>
    </members>
</doc>
