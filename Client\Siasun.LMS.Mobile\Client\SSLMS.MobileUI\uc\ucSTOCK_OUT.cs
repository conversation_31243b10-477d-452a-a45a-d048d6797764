﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;

namespace SSLMS.MobileUI.uc
{
    public partial class ucSTOCK_OUT : UserControl
    {
        public ucSTOCK_OUT()
        {
            this.InitializeComponent();

            this.ucCell1.Init();

            this.ucCell1.CellDoubleClick += new ucCell.CELLDoubleClickHandler(ucCell1_CellDoubleClick);

            this.ucCell1.Dock = DockStyle.Fill;

            this.ucCell1.BringToFront();

            this.ucCell1.PalletSelect = true;

            this.SET_CONTROL(true);

            this.cbWAREHOUSE_Bind();

            this.cbCELL_Bind();
        }

        void ucCell1_CellDoubleClick(WH_CELL mST_CELL)
        {
            this.txtRow.Text = mST_CELL._cell_z.ToString().PadLeft(2, '0');

            this.txtLine.Text = mST_CELL._cell_x.ToString().PadLeft(2, '0');

            this.txtFloor.Text = mST_CELL._cell_y.ToString().PadLeft(2, '0');

            this.SET_CONTROL(true);
        }

        private void SET_CONTROL(bool bVisible)
        {

            this.ucCell1.Visible = !bVisible;
        }

        private void cbWAREHOUSE_Bind()
        {
            try
            {
                cmbWAREHOUSE.ValueMember = "WAREHOUSE_ID";

                cmbWAREHOUSE.DisplayMember = "WAREHOUSE_NAME";

                cmbWAREHOUSE.DataSource = Program._I_WHService.WAREHOUSE_GetList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void cbCELL_Bind()
        {
            try
            {
                this.cmbStation.ValueMember = "CELL_ID";

                cmbStation.DisplayMember = "CELL_NAME";

                System.Collections.Hashtable ht = new System.Collections.Hashtable();

                ht.Add("CELL_ID", "0");

                ht.Add("CELL_NAME", "-");

                this.cmbStation.DataSource = SSLMS.MobileUI.Common.AddAll(Program._I_WHService.CELL_GetList_1(string.Empty, string.Empty, SSLMS.Model.CELL_TYPE.station.ToString(), string.Empty, SSLMS.Model.CELL_INOUT.IN.ToString()), ht);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }
       
        
        private void btnOK_Click(object sender, EventArgs e)
        {
            try
            {
                this.Save();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }


        private void btnSelect_Click(object sender, EventArgs e)
        {
            this.SET_CONTROL(this.ucCell1.Visible);
        }

        private void Save()
        {
            bool bResult = true;

            string sResult = string.Empty;

            string strMessageShow = string.Empty;

            if (string.IsNullOrEmpty(txtRow.Text.TrimEnd()) || string.IsNullOrEmpty(txtLine.Text.TrimEnd()) || string.IsNullOrEmpty(txtFloor.Text.TrimEnd()))
            {

                MessageBox.Show("Please Select Storage Cell!");

                return;
            }

            if (null == this.cmbStation.SelectedValue || this.cmbStation.SelectedValue.ToString() == "0")
            {

                MessageBox.Show("Please Select Loading Station!");

                return;
            }

            int START_CELL_ID = 0;

            string START_CELL_CODE = string.Format("{0}-{1}-{2}", this.txtRow.Text.TrimEnd(), this.txtLine.Text.TrimEnd(), this.txtFloor.Text.TrimEnd());



            int END_CELL_ID = Convert.ToInt32(this.cmbStation.SelectedValue);


            int MANAGE_ID = 0;


            WH_CELL mST_CELL_START = Program._I_WHService.CELL_GetModel_CODE(START_CELL_CODE);

            START_CELL_ID = mST_CELL_START._cell_id;

            if (null == mST_CELL_START || !SSLMS.Model.RUN_STATUS.enable.ToString().Equals(mST_CELL_START._run_status))
            {
                MessageBox.Show(string.Format("{0} is not available",START_CELL_CODE));

                return;
            }

            if (null == mST_CELL_START || !SSLMS.Model.CELL_STATUS.pallet.ToString().Equals(mST_CELL_START._cell_status))
            {
                MessageBox.Show(string.Format("{0} is not pallet cell", START_CELL_CODE));

                return;
            }

            if (mST_CELL_START._shelf_type.ToString() == SSLMS.Model.SHELF_TYPE.deep.ToString())
            {
                WH_CELL mST_CELL_OUTER = Program._I_WHService.CELL_GetModel_CODE(string.Format("{0}-{1}-{2}",
                                                                                                                             mST_CELL_START._shelf_neighbour.ToString().PadLeft(2, '0'),
                                                                                                                             mST_CELL_START._cell_x.ToString().PadLeft(2, '0'),
                                                                                                                             mST_CELL_START._cell_y.ToString().PadLeft(2, '0')));

                if (mST_CELL_OUTER != null
                    && mST_CELL_OUTER._cell_status == SSLMS.Model.CELL_STATUS.pallet.ToString()
                    && mST_CELL_OUTER._cell_status == SSLMS.Model.RUN_STATUS.enable.ToString())
                {

                    MessageBox.Show(string.Format("please unload outer cell {0} priority", mST_CELL_OUTER._cell_code));

                    return;

                }

                else if (mST_CELL_OUTER != null
                    && mST_CELL_OUTER._cell_status == SSLMS.Model.CELL_STATUS.have.ToString()
                    && mST_CELL_OUTER._cell_status == SSLMS.Model.RUN_STATUS.enable.ToString())
                {
                    bResult = Program._I_ManageService.ManageCreateDoubleDeepRackMoveTask(Program._USER,
                                                                               mST_CELL_START._cell_id,
                                                                               END_CELL_ID,
                                                                               true,
                                                                               out sResult);

                    strMessageShow += sResult + "\n";
                }

                else if (mST_CELL_OUTER != null
                          && mST_CELL_OUTER._cell_status == SSLMS.Model.CELL_STATUS.nohave.ToString()
                          && mST_CELL_OUTER._run_status == SSLMS.Model.RUN_STATUS.enable.ToString())
                {
                    bResult = Program._I_ManageService.ManageCreateSingleDeepRackMoveTask(Program._USER,
                                                                                                                 SSLMS.Model.MANAGE_TYPE_CODE.MOVE.ToString("d"),
                                                                                                                 mST_CELL_START._cell_id,
                                                                                                                 END_CELL_ID,
                                                                                                                 true,
                                                                                                                 true,
                                                                                                                 out sResult,
                                                                                                                 out MANAGE_ID);

                    strMessageShow += sResult + "\n";

                }
                else
                {
                    bResult = false;

                    strMessageShow += "can't unload because of other relate task is running" + "\n";

                }
            }
            else
            {
                bResult = Program._I_ManageService.ManageCreateSingleDeepRackMoveTask(Program._USER,
                                                                                                             SSLMS.Model.MANAGE_TYPE_CODE.MOVE.ToString("d"),
                                                                                                             mST_CELL_START._cell_id,
                                                                                                             END_CELL_ID,
                                                                                                             true,
                                                                                                             true,
                                                                                                             out sResult,
                                                                                                             out MANAGE_ID);
                strMessageShow += sResult + "\n";
            }



            if( !bResult)
                MessageBox.Show(strMessageShow);



        }



    }
}
