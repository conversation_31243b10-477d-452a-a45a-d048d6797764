﻿using System;
using System.Collections.Generic;
using System.Data;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    
    /// <summary>
    /// 齐套箱出库任务完成
    /// </summary>
    public class KitOutComplete : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private int _manage_id = 0; //任务ID       
            private string _stock_barcode = string.Empty;//箱条码

            public int MANAGE_ID
            {
                get { return _manage_id; }
                set { _manage_id = value; }
            }
            /// <summary>
            /// 箱条码
            /// </summary>
            public string STOCK_BARCODE
            {
                get { return _stock_barcode; }
                set { _stock_barcode = value; }
            }

           
        }
        
        public class OutPara
        {
            private string _status;
            private string _mess;
            private string _mode;
            private string _bill_code;
            private string _wbs;
            private string _stock_barcode;
            private int _all_count;
            private int _num;
            private int _plan_mode;
            private int _box_flag;

            /// <summary>
            /// 状态
            /// </summary>
            public string STATUS
            {
                get { return _status; }
                set { _status = value; }
            }

            /// <summary>
            /// 信息
            /// </summary>
            public string MESS
            {
                get { return _mess; }
                set { _mess = value; }
            }

            /// <summary>
            /// 模式
            /// </summary>
            public string MODE
            {
                get { return _mode; }
                set { _mode = value; }
            }

            /// <summary>
            /// 集货单号
            /// </summary>
            public string BILL_CODE
            {
                get { return _bill_code; }
                set { _bill_code = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string WBS
            {
                get { return _wbs; }
                set { _wbs = value; }
            }

            /// <summary>
            /// 箱号
            /// </summary>
            public string STOCK_BARCODE
            {
                get { return _stock_barcode; }
                set { _stock_barcode = value; }
            }

            /// <summary>
            /// 总箱数
            /// </summary>
            public int ALL_COUNT
            {
                get { return _all_count; }
                set { _all_count = value; }
            }

            /// <summary>
            /// 当前箱数
            /// </summary>
            public int NUM
            {
                get { return _num; }
                set { _num = value; }
            }

            /// <summary>
            /// 订单模式
            /// </summary>
            public int PLAN_MODE
            {
                get { return _plan_mode; }
                set { _plan_mode = value; }
            }

            /// <summary>
            /// 料箱标志位
            /// </summary>
            public int BOX_FLAG
            {
                get { return _box_flag; }
                set { _box_flag = value; }
            }

        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            string sResult = string.Empty;
            outJson = string.Empty;
            OutPara outPutPara = new OutPara();

            try
            {
                //string wesInterfaceStatus = string.Empty;
                //if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                //{
                //    bResult = false;
                //    outJson = string.Format("KitBoxOutTask.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                //    return bResult;
                //}

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.STOCK_BARCODE))
                {
                    bResult = false;
                    outJson = string.Format("KitOutComplete.NotifyMethod:箱号为空");
                    return bResult;
                }

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelStockBarcode(taskInfo.STOCK_BARCODE);
                if (mMANAGE_MAIN == null)
                {
                    bResult = false;
                    outJson = string.Format("未找到箱号为【{0}】的任务", taskInfo.STOCK_BARCODE);
                    return bResult;
                }

                IList<Model.MANAGE_LIST> lsMANAGE_LIST = this._P_MANAGE_LIST.GetListManageID(mMANAGE_MAIN.MANAGE_ID);
                if (lsMANAGE_LIST.Count == 0)
                {
                    bResult = false;
                    outJson = string.Format("未找到箱号为【{0}】的任务明细", taskInfo.STOCK_BARCODE);
                    return bResult;
                }

                //查询模式
                string strSql = string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'WorkingMode-KitOut1'");
                DataTable dtWorkModeKitOut1 = this.GetList(strSql);
                if (dtWorkModeKitOut1 != null && dtWorkModeKitOut1.Rows.Count > 0)
                {
                    if (dtWorkModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.WorkModeKitOut.Auto.ToString())
                    {
                        outPutPara.MODE = "1";
                        Model.PLAN_MAIN mPLAN_MAIN = this._P_PLAN_MAIN.GetModel(mMANAGE_MAIN.PLAN_ID);
                        if (mPLAN_MAIN == null)
                        {
                            bResult = false;
                            outJson = string.Format("未找到箱号为【{0}】任务的出库计划单", taskInfo.STOCK_BARCODE);
                            return bResult;
                        }
                        outPutPara.BILL_CODE = mPLAN_MAIN.BACKUP_FILED3;//集货单号
                        outPutPara.WBS = lsMANAGE_LIST[0].GOODS_PROPERTY8;//WBS号
                        outPutPara.STOCK_BARCODE = mMANAGE_MAIN.STOCK_BARCODE;//箱号
                        outPutPara.ALL_COUNT = Convert.ToInt32(mMANAGE_MAIN.BACKUP_FIELD1);//总箱数
                        //outPutPara.NUM = Convert.ToInt32(mMANAGE_MAIN.BACKUP_FIELD2);//当前箱数
                        outPutPara.NUM = this.GetCurrNum(mPLAN_MAIN.PLAN_ID, Convert.ToInt32(mMANAGE_MAIN.BACKUP_FIELD2));//当前箱数
                        //查询订单模式
                        strSql = string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY = 'PlanMode-KitOut1'");
                        DataTable dtPlanModeKitOut1 = this.GetList(strSql);
                        if (dtPlanModeKitOut1 != null && dtPlanModeKitOut1.Rows.Count > 0)
                        {
                            if (dtPlanModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.One.ToString())
                            {
                                outPutPara.PLAN_MODE = 1;//非混单
                            }
                            else if(dtPlanModeKitOut1.Rows[0]["PARAMETER_VALUE"].ToString() == Enum.PlanModeKitOut.Mix.ToString())
                            {
                                outPutPara.PLAN_MODE = 2;//混单
                            }
                            else
                            {
                                outPutPara.PLAN_MODE = 0;//无订单
                            }                              
                        }
                        outPutPara.BOX_FLAG = 1;
                    }
                    else
                    {
                        outPutPara.MODE = "2";
                    }
                }

                bResult = new ManageOut().ManageComplete(mMANAGE_MAIN.MANAGE_ID, true, out sResult);

                if (bResult)
                {
                    outJson = "成功！";

                }
                else
                {
                    outJson = "任务完成失败！" + sResult;
                    return bResult;
                }

                
           
        }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("KitOutComplete.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outPutPara.STATUS = "S";
                }
                else
                {
                    outPutPara.STATUS = "E";
                    outPutPara.MESS = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outPutPara);
            }

            return bResult;
        }


        public int GetCurrNum(int PLAN_ID,int Num)
        {
            int CurNum = 0;
            string strSql = string.Empty;
            strSql = string.Format("select MANAGE_ID, BACKUP_FIELD2 from MANAGE_MAIN where PLAN_ID = {0} order by BACKUP_FIELD2 ",PLAN_ID);
            DataTable dt = this.GetList(strSql);
            if (dt == null || dt.Rows.Count == 0)
            {
                CurNum = Num;
                return CurNum;
            }
            else
            {
                CurNum = Convert.ToInt32(dt.Rows[0]["BACKUP_FIELD2"]);
                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModel(Convert.ToInt32(dt.Rows[0]["MANAGE_ID"]));
                mMANAGE_MAIN.BACKUP_FIELD2 = Num.ToString();
                this._P_MANAGE_MAIN.Update(mMANAGE_MAIN);
            }

            return CurNum;
        }

    }
}
