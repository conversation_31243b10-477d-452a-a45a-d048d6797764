﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.UC
{
    /// <summary>
    /// ucWindowTopHeader.xaml 的交互逻辑
    /// </summary>
    public partial class ucWindowTopHeader : UserControl
    {
        public ucWindowTopHeader()
        {
            InitializeComponent();
        }

        //鼠标点击事件
        private void Path_MouseDown(object sender, MouseButtonEventArgs e)
        {
            if (e.ClickCount == 1)
            {
                Path path = e.OriginalSource as Path;
                if (path != null)
                {
                    Window window = this.GetWindow(this);
                    if (window != null)
                    {
                        switch (path.Name)
                        {
                            case "pathMiniSize":
                                window.WindowState = WindowState.Minimized;
                                break;
                            case "pathClose":
                                window.Close();
                                break;
                        }
                    }
                }
            }
        }

        private Window GetWindow(DependencyObject dpyObj)
        {
            Window window = null;
            DependencyObject dpyParent = LogicalTreeHelper.GetParent(dpyObj == null ? this : dpyObj);
            if (dpyParent != null)
            {
                if (dpyParent is Window)
                {
                    window = (dpyParent as Window);
                    return window;
                }
                else
                {
                    return GetWindow(dpyParent);
                }
            }
            return window;
        }
    }
}
