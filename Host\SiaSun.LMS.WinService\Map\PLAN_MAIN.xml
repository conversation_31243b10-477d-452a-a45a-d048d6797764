﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="PLAN_MAIN" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
	<alias>
		<typeAlias alias="PLAN_MAIN" type="SiaSun.LMS.Model.PLAN_MAIN, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="PLAN_MAIN">
			<result property="PLAN_ID" column="plan_id" />
			<result property="PLAN_RELATIVE_ID" column="plan_relative_id" />
			<result property="PLAN_CODE" column="plan_code" />
			<result property="PLAN_TYPE_CODE" column="plan_type_code" />
			<result property="PLAN_CREATE_TIME" column="plan_create_time" />
			<result property="PLAN_BEGIN_TIME" column="plan_begin_time" />
			<result property="PLAN_END_TIME" column="plan_end_time" />
			<result property="PLAN_BILL_DATE" column="plan_bill_date" />
			<result property="PLAN_STATUS" column="plan_status" />
			<result property="PLAN_CREATER" column="plan_creater" />
			<result property="PLAN_FROM_DEPT" column="plan_from_dept" />
			<result property="PLAN_TO_DEPT" column="plan_to_dept" />
			<result property="PLAN_FROM_USER" column="plan_from_user" />
			<result property="PLAN_TO_USER" column="plan_to_user" />
			<result property="PLAN_REMARK" column="plan_remark" />
			<result property="PLAN_FLAG" column="plan_flag" />
			<result property="PLAN_CONFIRM_TIME" column="plan_confirm_time" />
			<result property="PLAN_CONFIRM_USER" column="plan_confirm_user" />

			<result property="PLAN_GROUP" column="plan_group" />
			<result property="PLAN_RELATIVE_CODE" column="plan_relative_code" />
			<result property="PLAN_INOUT_STATION" column="plan_inout_station" />
			<result property="PLAN_PICK_TYPE" column="plan_pick_type" />
			<result property="PLAN_PROJECT_CODE" column="plan_project_code" />
			<result property="PLAN_DESCRIPTION" column="plan_description" />
			<result property="PLAN_LEVEL" column="plan_level" />
			<result property="PLAN_HEADTEXT" column="plan_headtext" />

			<result property="PLAN_RELATEDBILL" column="plan_relatedbill" />
			<result property="PLAN_SHOPNO" column="plan_shopno" />
			<result property="PLAN_WORKINGSEATL" column="plan_workingseatl" />
			<result property="BACKUP_FILED1" column="backup_filed1" />
			<result property="BACKUP_FILED2" column="backup_filed2" />
			<result property="BACKUP_FILED3" column="backup_filed3" />
			<result property="BACKUP_FILED4" column="backup_filed4" />
			<result property="BACKUP_FILED5" column="backup_filed5" />

		</resultMap>
	</resultMaps>

	<statements>

		<select id="PLAN_MAIN_SELECT" parameterClass="int" resultMap="SelectResult">
			Select
			plan_id,
			plan_relative_id,
			plan_code,
			plan_type_code,
			plan_create_time,
			plan_begin_time,
			plan_end_time,
			plan_bill_date,
			plan_status,
			plan_creater,
			plan_from_dept,
			plan_to_dept,
			plan_from_user,
			plan_to_user,
			plan_remark,
			plan_flag,
			plan_confirm_time,
			plan_confirm_user,
			plan_group,
			plan_relative_code,
			plan_inout_station,
			plan_pick_type,
			plan_project_code,
			plan_description,
			plan_level,
			plan_headtext,
			plan_relatedbill,
			plan_shopno,
			plan_workingseatl,
			backup_filed1,
			backup_filed2,
			backup_filed3,
			backup_filed4,
			backup_filed5
			From PLAN_MAIN
		</select>

		<select id="PLAN_MAIN_SELECT_BY_ID" parameterClass="int" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">

			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_id=#PLAN_ID#
				</isParameterPresent>
			</dynamic>
		</select>


		<select id="PLAN_MAIN_SELECT_BY_PLAN_CODE" parameterClass="string" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					PLAN_CODE=#PLAN_CODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="PLAN_MAIN_SELECT_BY_PLAN_GROUP" parameterClass="string" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					PLAN_GROUP=#PLAN_GROUP#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="PLAN_MAIN_SELECT_BY_PLAN_RELATIVE_CODE" parameterClass="string" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					PLAN_RELATIVE_CODE=#PLAN_RELATIVE_CODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="PLAN_MAIN_SELECT_BY_PLAN_CODE_PLAN_GROUP_PLAN_RELATIVE_CODE" parameterClass="string" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					PLAN_CODE=#PLAN_CODE# and PLAN_GROUP=#PLAN_GROUP# and PLAN_RELATIVE_CODE=#PLAN_RELATIVE_CODE#
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="PLAN_MAIN_SELECT_BY_PLAN_GROUP_NotBindToPickStation" parameterClass="string" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_group=#PLAN_GROUP# and plan_id not in (select plan_id from t_pick_position_plan_bind where pick_station_id=#STATION_ID#)
					and plan_id not in (select plan_id from plan_list where plan_list_quantity=plan_list_picked_quantity)
				</isParameterPresent>
			</dynamic>
		</select>

		<select id="PLAN_MAIN_SELECT_BY_PLAN_LIST_ID" parameterClass="string" extends = "PLAN_MAIN_SELECT" resultMap="SelectResult">
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_id in (select distinct plan_id from plan_list where plan_list_id =#planListId#)
				</isParameterPresent>
			</dynamic>
		</select>

		<insert id="PLAN_MAIN_INSERT" parameterClass="PLAN_MAIN">
			Insert Into PLAN_MAIN (
			plan_id,
			plan_relative_id,
			plan_code,
			plan_type_code,
			plan_create_time,
			plan_begin_time,
			plan_end_time,
			plan_bill_date,
			plan_status,
			plan_creater,
			plan_from_dept,
			plan_to_dept,
			plan_from_user,
			plan_to_user,
			plan_remark,
			plan_flag,
			plan_confirm_time,
			plan_confirm_user,
			plan_group,
			plan_relative_code,
			plan_inout_station,
			plan_pick_type,
			plan_project_code,
			plan_description,
			plan_level,
			plan_headtext,
			plan_relatedbill,
			plan_shopno,
			plan_workingseatl,
			backup_filed1,
			backup_filed2,
			backup_filed3,
			backup_filed4,
			backup_filed5
			)Values(
			#PLAN_ID#,
			#PLAN_RELATIVE_ID#,
			#PLAN_CODE#,
			#PLAN_TYPE_CODE#,
			#PLAN_CREATE_TIME#,
			#PLAN_BEGIN_TIME#,
			#PLAN_END_TIME#,
			#PLAN_BILL_DATE#,
			#PLAN_STATUS#,
			#PLAN_CREATER#,
			#PLAN_FROM_DEPT#,
			#PLAN_TO_DEPT#,
			#PLAN_FROM_USER#,
			#PLAN_TO_USER#,
			#PLAN_REMARK#,
			#PLAN_FLAG#,
			#PLAN_CONFIRM_TIME#,
			#PLAN_CONFIRM_USER#,
			#PLAN_GROUP#,
			#PLAN_RELATIVE_CODE#,
			#PLAN_INOUT_STATION#,
			#PLAN_PICK_TYPE#,
			#PLAN_PROJECT_CODE#,
			#PLAN_DESCRIPTION#,
			#PLAN_LEVEL#,
			#PLAN_HEADTEXT#,
			#PLAN_RELATEDBILL#,
			#PLAN_SHOPNO#,
			#PLAN_WORKINGSEATL#,
			#BACKUP_FILED1#,
			#BACKUP_FILED2#,
			#BACKUP_FILED3#,
			#BACKUP_FILED4#,
			#BACKUP_FILED5#
			)
			<!--<selectKey  resultClass="int" type="post" property="PLAN_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>

		<update id="PLAN_MAIN_UPDATE" parameterClass="PLAN_MAIN">
			Update PLAN_MAIN Set
			plan_id=#PLAN_ID#,
			plan_relative_id=#PLAN_RELATIVE_ID#,
			plan_code=#PLAN_CODE#,
			plan_type_code=#PLAN_TYPE_CODE#,
			plan_create_time=#PLAN_CREATE_TIME#,
			plan_begin_time=#PLAN_BEGIN_TIME#,
			plan_end_time=#PLAN_END_TIME#,
			plan_bill_date=#PLAN_BILL_DATE#,
			plan_status=#PLAN_STATUS#,
			plan_creater=#PLAN_CREATER#,
			plan_from_dept=#PLAN_FROM_DEPT#,
			plan_to_dept=#PLAN_TO_DEPT#,
			plan_from_user=#PLAN_FROM_USER#,
			plan_to_user=#PLAN_TO_USER#,
			plan_remark=#PLAN_REMARK#,
			plan_flag=#PLAN_FLAG#,
			plan_confirm_time=#PLAN_CONFIRM_TIME#,
			plan_confirm_user=#PLAN_CONFIRM_USER#,
			plan_group=#PLAN_GROUP#,
			plan_relative_code =#PLAN_RELATIVE_CODE#,
			plan_inout_station =#PLAN_INOUT_STATION#,
			plan_pick_type=#PLAN_PICK_TYPE#,
			plan_project_code=#PLAN_PROJECT_CODE#,
			plan_description=#PLAN_DESCRIPTION#,
			plan_level=#PLAN_LEVEL#,
			plan_headtext=#PLAN_HEADTEXT#,
			plan_relatedbill=#PLAN_RELATEDBILL#,
			plan_shopno=#PLAN_SHOPNO#,
			plan_workingseatl=#PLAN_WORKINGSEATL#,
			backup_filed1=#BACKUP_FILED1#,
			backup_filed2=#BACKUP_FILED2#,
			backup_filed3=#BACKUP_FILED3#,
			backup_filed4=#BACKUP_FILED4#,
			backup_filed5=#BACKUP_FILED5#
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_id=#PLAN_ID#
				</isParameterPresent>
			</dynamic>
		</update>

		<delete id="PLAN_MAIN_DELETE" parameterClass="int">
			Delete From PLAN_MAIN
			<dynamic prepend="WHERE">
				<isParameterPresent>
					plan_id=#PLAN_ID#
				</isParameterPresent>
			</dynamic>
		</delete>

	</statements>
</sqlMap>