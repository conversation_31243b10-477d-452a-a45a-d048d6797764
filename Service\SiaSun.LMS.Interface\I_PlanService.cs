﻿using System;
using System.Collections.Generic;

using System.Text;
using System.Data;
using System.ServiceModel;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.PLAN_MAIN))]
    [ServiceKnownType(typeof(DataTable))]
    [ServiceKnownType(typeof(List<object>))]
    [ServiceKnownType(typeof(List<SiaSun.LMS.Model.PLAN_LIST>))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.GOODS_MAIN))]
    public partial interface I_PlanService 
    {
        [OperationContract]
        DataTable PlanTypeGetData();

        [OperationContract]
        SiaSun.LMS.Model.PLAN_TYPE PlanTypeGetModel(string PLAN_TYPE_CODE);

        [OperationContract]
        SiaSun.LMS.Model.PLAN_MAIN PlanGetModel(int PLAN_ID);

        [OperationContract]
        SiaSun.LMS.Model.PLAN_DETAIL PlanDetailGetModel(int PLAN_DETAIL_ID);

        [OperationContract]
        SiaSun.LMS.Model.PLAN_LIST PlanListGetModel(int PLAN_LIST_ID);

        //[OperationContract]
        //bool PlanDownLoad(int PLAN_ID, out string sResult);

        //[OperationContract]
        //bool PlanListDownLoad(Model.SYS_USER mSYS_USER, int PLAN_LIST_ID, int END_CELL_ID, bool bTrans, out string sResult);

        //[OperationContract]
        //bool PlanDetailDownLoad(int PLAN_ID, out string sResult);



        /// <summary>
        /// 重置计划状态和计划单的order数量
        /// wdz add 2018-03-05
        /// </summary>
        [OperationContract]
        bool PlanResetStatus(int planId, out string message);

    }
}
