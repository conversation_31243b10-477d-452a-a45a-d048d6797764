﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml">

    <!--========================BureauBlue Skin==================-->
    <Color x:Key="BlackColor">#FF000000</Color>
    <Color x:Key="WhiteColor">#FFFFFFFF</Color>
    <SolidColorBrush x:Key="OutsideFontColor" Color="#FF000000" />
    <SolidColorBrush x:Key="NormalHighlightBrush" Color="#FFFFFFFF"/>
    <SolidColorBrush x:Key="ControlBorderBrush" Color="#FFB1703C"/>
    <SolidColorBrush x:Key="GlyphBrush" Color="#FF527DB5"/>
    <SolidColorBrush x:Key="WindowBackgroundBrush" Color="#FFF" />
    <SolidColorBrush x:Key="DisabledForegroundBrush" Color="#888" />
    <SolidColorBrush x:Key="DisabledBackgroundBrush" Color="#A5FFFFFF"/>
    <SolidColorBrush x:Key="DisabledBorderBrush" Color="#66FFFFFF"/>
    <SolidColorBrush x:Key="FocusBrush" Color="#FFE99862"/>
    
    <LinearGradientBrush x:Key="ControlBackgroundBrush" EndPoint="1.204,0.5" StartPoint="0.056,0.5">
        <GradientStop Color="#FFFFFFFF" Offset="0" />
        <GradientStop Color="#FFD4D7DB" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="NormalBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFFFFFFF" Offset="0" />
        <GradientStop Color="#FF8AAEDA" Offset="0.521" />
        <GradientStop Color="#FFC6D6EC" Offset="0.194" />
        <GradientStop Color="#FFB4C9E5" Offset="0.811" />
        <GradientStop Color="#FFB7C8E0" Offset="0.507" />
        <GradientStop Color="#FFD1DEF0" Offset="1" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="NormalBorderBrush" EndPoint="0.5,0" StartPoint="0.5,1">
        <GradientStop Color="#FF84B2D4" />
        <GradientStop Color="#FFADC7DE" Offset="1" />
    </LinearGradientBrush>        
    <LinearGradientBrush x:Key="MouseOverBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFFFFFFF" Offset="0" />
        <GradientStop Color="#FFFEF3B5" Offset="0.318" />
        <GradientStop Color="#FFFFEB70" Offset="0.488" />
        <GradientStop Color="#FFFFD02E" Offset="0.502" />
        <GradientStop Color="#FFFFD932" Offset="0.834" />
        <GradientStop Color="#FFFFF48B" Offset="1" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="MouseOverBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFEEE8CF" Offset="0" />
        <GradientStop Color="#FFC4AF8C" Offset="0.536" />
        <GradientStop Color="#FFDCD1BF" Offset="1" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="MouseOverHighlightBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFFFFFFB" Offset="0" />
        <GradientStop Color="#FFFEF3B5" Offset="1" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="PressedBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFC3BCAE" Offset="0" />
        <GradientStop Color="#FFFDCE9D" Offset="0.046" />
        <GradientStop Color="#FFFFA35B" Offset="0.452" />
        <GradientStop Color="#FFFF8A2C" Offset="0.461" />
        <GradientStop Color="#FFFF9F30" Offset="0.724" />
        <GradientStop Color="#FFFFC472" Offset="1" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="PressedBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FF8E8165" Offset="0" />
        <GradientStop Color="#FFC3BCAE" Offset="1" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="PressedHighlightBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFFFFFFF" Offset="0.665" />
        <GradientStop Color="#FFC3BCAE" Offset="0" />
    </LinearGradientBrush>

    <!-- CheckBox Brushes -->
    <SolidColorBrush x:Key="CheckBoxBackgroundBrush" Color="#FFF4F4F4"/>
    <SolidColorBrush x:Key="CheckBoxBorderBrush" Color="#FF868686"/>
    <SolidColorBrush x:Key="CheckBoxInnerBoxBackgroundBrush" Color="#FFCACFD5"/>
    <LinearGradientBrush x:Key="CheckBoxInnerBoxBorderBrush" EndPoint="-0.007,-0.012" StartPoint="0.915,0.92">
        <GradientStop Color="#FFE4E5E9" />
        <GradientStop Color="#FFA2ACB9" Offset="1" />
    </LinearGradientBrush>
    <SolidColorBrush x:Key="CheckBoxBackgroundFillBrush" Color="#FFDEEAFA"/>
    <SolidColorBrush x:Key="CheckBoxMouseOverBrush" Color="#FFFCE7AF"/>
    <LinearGradientBrush x:Key="CheckBoxPressBorderBrush" EndPoint="0.055,0.119" StartPoint="0.886,0.808">
        <GradientStop Color="#FFF4D9BE" />
        <GradientStop Color="#FFF28A27" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="CheckBoxInnerBoxGradientBrush" StartPoint="0.238,0.228" EndPoint="0.752,0.749">
        <GradientStop Color="#00F6F6F6" Offset="0.254" />
        <GradientStop Color="#53F8F8F8" Offset="0.54" />
        <GradientStop Color="#BFFFFFFF" Offset="0.996" />
    </LinearGradientBrush>

    <!-- RadioButton Brushes -->
    <SolidColorBrush x:Key="RadioButtonCheckIconBorderBrush" Color="#FFDA8229"/>
    <SolidColorBrush x:Key="RadioButtonBackgroundBrush" Color="#FFEDEDEE"/>
    <SolidColorBrush x:Key="RadioButtonBorderBrush" Color="#FF597AA5"/>
    <LinearGradientBrush x:Key="RadioButtonInnerCircleBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFC8CDD2" />
        <GradientStop Color="#FFF2F2F2" Offset="0.531" />
        <GradientStop Color="#FFF5F5F5" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonInnerCircleBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFB3B8BD" Offset="0.004" />
        <GradientStop Color="#FFE0E0E0" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonMouseOverBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFE3DBA9" />
        <GradientStop Color="#FFFEF5DD" Offset="0.531" />
        <GradientStop Color="#FFFEF5DD" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonMouseOverBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFE3CF87" Offset="0.004" />
        <GradientStop Color="#FFFCF0D3" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonPressBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFC8CDD2" />
        <GradientStop Color="#FFF2F2F2" Offset="0.531" />
        <GradientStop Color="#FFF5F5F5" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonPressBorderBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFAC773F" Offset="0.004" />
        <GradientStop Color="#FFC8B5A3" Offset="0.987" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="RadioButtonCheckIconBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFFDDC8B" Offset="0.013" />
        <GradientStop Color="#FFFDDC8B" Offset="0.188" />
        <GradientStop Color="#FFF9952F" Offset="0.491" />
        <GradientStop Color="#FFF9954A" Offset="1" />
    </LinearGradientBrush>

    <!-- ScrollBar RepeatButtonBrushes -->
    <SolidColorBrush x:Key="ScrollBarThumbPressedBorderBrush" Color="#FF17498A"/>
    <SolidColorBrush x:Key="ScrollBarThumbMouseOverBorderBrush" Color="#FF3C6EB0"/>
    <SolidColorBrush x:Key="ScrollBarThumbBorderBrush" Color="#FF606F94"/>
    <SolidColorBrush x:Key="ScrollBarRepeatButtonBorderBrush" Color="#FF8C97A5"/>
    <LinearGradientBrush x:Key="ScrollBarRepeatButtonBrush" EndPoint="0.5,0" StartPoint="0.5,1">
        <GradientStop Color="#FFF1F6FE" Offset="0.5" />
        <GradientStop Color="#FFC7D9F1" Offset="0.513" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ScrollBarRepeatButtonPressedBrush" EndPoint="0.5,0" StartPoint="0.5,1">
        <GradientStop Color="#FFF1F6FE" Offset="0.5" />
        <GradientStop Color="#FFD1D6DD" Offset="0.513" />
    </LinearGradientBrush>
    <SolidColorBrush x:Key="ScrollBarRepeatButtonPressedBorderBrush" Color="#FF19598A"/>

    <!-- ScrollBar ThumbBrushes -->
    <LinearGradientBrush x:Key="ScrollBarThumbBrush" EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
        <GradientStop Color="#FFD1DBE6" Offset="0" />
        <GradientStop Color="#FFD1DAE4" Offset="0.5" />
        <GradientStop Color="#FFE6E9F0" Offset="0.513" />
        <GradientStop Color="#FFE8E9E9" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ScrollBarThumbMouseOverBrush" EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
        <GradientStop Color="#FFB4D1F7" Offset="0" />
        <GradientStop Color="#FFAACBF6" Offset="0.5" />
        <GradientStop Color="#FFCADFFA" Offset="0.513" />
        <GradientStop Color="#FFBED0E8" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ScrollBarThumbPressedBrush" EndPoint="-0.062,0.5" StartPoint="1.062,0.5">
        <GradientStop Color="#FFB4D1F7" Offset="0" />
        <GradientStop Color="#FF6EA6F0" Offset="0.5" />
        <GradientStop Color="#FFA4C7F6" Offset="0.513" />
        <GradientStop Color="#FF9CBBE5" Offset="1" />
    </LinearGradientBrush>

    <!-- ListItem Brushes -->
    <LinearGradientBrush x:Key="ListItemSelectedBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFFFFFFF" Offset="0.046" />
        <GradientStop Color="#FFD7E0EA" Offset="0.194" />
        <GradientStop Color="#FFBCC5D5" Offset="0.507" />
        <GradientStop Color="#FFA4ADBB" Offset="0.521" />
        <GradientStop Color="#FFBAC1CF" Offset="0.811" />
        <GradientStop Color="#FFE3E4E6" Offset="0.982" />
    </LinearGradientBrush>    
    <LinearGradientBrush x:Key="ListItemSelectedBorderBrush" EndPoint="0.5,0" StartPoint="0.5,1">
        <GradientStop Color="#FF8B8B8B" />
        <GradientStop Color="#FFADADAD" Offset="1" />
    </LinearGradientBrush>

    <!-- ProgressBar Brushes -->
    <LinearGradientBrush x:Key="ProgressBarIndicatorBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFC6D6EC" Offset="0" />
        <GradientStop Color="#FFBDD6FF" Offset="0.502" />
        <GradientStop Color="#FF71A7FD" Offset="0.522" />
        <GradientStop Color="#FF94BDFD" Offset="0.763" />
        <GradientStop Color="#FFA9CAFF" Offset="1" />
    </LinearGradientBrush>   
    
    <!-- SliderBrushes -->
    <SolidColorBrush x:Key="SliderThumbBorderBrush" Color="#FF496FA2"/>
    <SolidColorBrush x:Key="SliderThumbBrush" Color="#FFC1C1C1"/>
    <LinearGradientBrush x:Key="SliderBackgroundBrush" EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FF324D70" Offset="0.493" />
        <GradientStop Color="#FF6A8BB6" Offset="1" />
    </LinearGradientBrush>
    
    <!-- TextControlsBrushes-->
    <LinearGradientBrush x:Key="TextControlBorderBrush"  EndPoint="0.5,1" StartPoint="0.5,0">
        <GradientStop Color="#FFABAEB3"/>
        <GradientStop Color="#FFE2E8EE" Offset="1"/>
    </LinearGradientBrush>

    <!--ToolBar Brushes-->
    <SolidColorBrush x:Key="ToolBarButtonHover" Color="#FF125E7C" />
    <SolidColorBrush x:Key="ToolBarGripper" Color="#C6C3C6" />
    <SolidColorBrush x:Key="ToolBarSubMenuBackground" Color="#FFFDFDFD" />
    <SolidColorBrush x:Key="ToolBarMenuBorder" Color="#FFFFFFFF" />
    <LinearGradientBrush x:Key="ToolBarHorizontalBackground" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FFFFFF" Offset="0" />
        <GradientStop Color="#FFFBFF" Offset="0.5" />
        <GradientStop Color="#F7F7F7" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ToolBarToggleButtonHorizontalBackground" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#ECECEC" Offset="0" />
        <GradientStop Color="#DDDDDD" Offset="0.5" />
        <GradientStop Color="#A0A0A0" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ToolBarToggleButtonVerticalBackground" EndPoint="1,0" StartPoint="0,0">
        <GradientStop Color="#ECECEC" Offset="0" />
        <GradientStop Color="#DDDDDD" Offset="0.5" />
        <GradientStop Color="#A0A0A0" Offset="1" />
    </LinearGradientBrush>
    <LinearGradientBrush x:Key="ToolBarVerticalBackground" EndPoint="1,0" StartPoint="0,0">
        <GradientStop Color="#FFFFFF" Offset="0" />
        <GradientStop Color="#FFFBFF" Offset="0.5" />
        <GradientStop Color="#F7F7F7" Offset="1" />
    </LinearGradientBrush>

    <BorderGapMaskConverter x:Key="BorderGapMaskConverter" />

    <Style x:Key="NuclearButtonFocusVisual">
        <Setter Property="Control.Template">
            <Setter.Value>
                <ControlTemplate>
                    <Border>
                        <Rectangle Margin="2" Stroke="#60000000" StrokeThickness="1" StrokeDashArray="1 2" />
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--======================Effect===================================-->
    <DropShadowBitmapEffect x:Key="PopupDropShadow" ShadowDepth="1.5" Softness="0.15" />

    <!--=========================Customer Color==========================-->
  <Color x:Key="ControlLightColor">White</Color>
  <Color x:Key="ControlMediumColor">#FF7381F9</Color>
    
  <Color x:Key="BorderLightColor">#FFCCCCCC</Color>
  <Color x:Key="BorderMediumColor">#FF888888</Color>

    <!--======================Customer Brush=============================-->    
    <LinearGradientBrush x:Key="BorderBackground" StartPoint="0,0" EndPoint="1,1">
        <GradientBrush.GradientStops>
            <GradientStopCollection>
                <GradientStop Color="AliceBlue" Offset="0.0"/>
                <GradientStop Color="WhiteSmoke" Offset="0.6"/>
                <GradientStop Color="AliceBlue" Offset="1.0"/>
            </GradientStopCollection>
        </GradientBrush.GradientStops>
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ControlNormalBackGround" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="White" Offset="0.0" />
        <GradientStop Color="Lavender" Offset=".7" />
        <GradientStop Color="LightSteelBlue" Offset="1.0" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="ControlMouseOverBackGround" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="CornflowerBlue" Offset="0.0" />
        <GradientStop Color="Lavender" Offset=".7" />
        <GradientStop Color="White" Offset="1.0" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridColumnHeader" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FFFFFFFF" Offset="0" />
        <GradientStop Color="#FF8AAEDA" Offset="0.521" />
        <GradientStop Color="#FFC6D6EC" Offset="0.194" />
        <GradientStop Color="#FFB4C9E5" Offset="0.811" />
        <GradientStop Color="#FFB7C8E0" Offset="0.507" />
        <GradientStop Color="#FFD1DEF0" Offset="1" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRowHeader" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="#FFFFFFFF" Offset="0" />
        <GradientStop Color="#FF8AAEDA" Offset="0.521" />
        <GradientStop Color="#FFC6D6EC" Offset="0.194" />
        <GradientStop Color="#FFB4C9E5" Offset="0.811" />
        <GradientStop Color="#FFB7C8E0" Offset="0.507" />
        <GradientStop Color="#FFD1DEF0" Offset="1" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRow" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="Snow" Offset="0" />
        <GradientStop Color="LightBlue" Offset="0.5" />
        <GradientStop Color="Snow" Offset="1" />
    </LinearGradientBrush>

    <LinearGradientBrush x:Key="DataGridRowMouseOver" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="White" Offset="0" />
        <GradientStop Color="#FFE2F1FF" Offset="1" />
    </LinearGradientBrush>
    
    <LinearGradientBrush x:Key="DataGridRowSelected" EndPoint="0,1" StartPoint="0,0">
        <GradientStop Color="LightBlue" Offset="0" />
        <GradientStop Color="Lavender" Offset="1" />
    </LinearGradientBrush>
    
</ResourceDictionary>