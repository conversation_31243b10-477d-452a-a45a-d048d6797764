﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;

namespace SSLMS.MobileUI.uc
{
    public partial class ucMANAGE_PLAN_IN : UserControl
    {

        private ucList  ucPLAN_LIST= new ucList();

        private ucManagePosition ucMANAGE_POSITION = new ucManagePosition();

        int intPlanID;

        MANAGE_TYPE mMANAGE_TYPE = null;

        public ucMANAGE_PLAN_IN()
        {
            InitializeComponent();

            ucPLAN_LIST.Dock = DockStyle.Fill;

            this.panelPLANLIST.Controls.Add(ucPLAN_LIST);

            ucMANAGE_POSITION.Dock = DockStyle.Fill;

            this.panelPosition.Controls.Add(ucMANAGE_POSITION);
        }

        public ucMANAGE_PLAN_IN(int PLAN_ID, string MANAGE_TYPE_CODE)
        {
            InitializeComponent();

            ucPLAN_LIST.Dock = DockStyle.Fill;

            this.panelPLANLIST.Controls.Add(ucPLAN_LIST);

            ucMANAGE_POSITION.Dock = DockStyle.Fill;

            this.ucMANAGE_POSITION.U_SaveButtonClicked += new ucManagePosition.U_SaveButtonClickHandler(ucMANAGE_POSITION_U_SaveButtonClicked);

            this.panelPosition.Controls.Add(ucMANAGE_POSITION);
            
            this.intPlanID = PLAN_ID;

            this.mMANAGE_TYPE =(MANAGE_TYPE) Program._I_PDAService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;

            this.InitManagePosition();

            this.PLAN_LIST_Bind();
        }

        void ucMANAGE_POSITION_U_SaveButtonClicked()
        {
            string strResult = string.Empty;

            try
            {
                for (int i = 0; i < this.ucPLAN_LIST.tabGoodsType.TabPages.Count; i++)
                {
                    MyDataGrid mdgv = (this.ucPLAN_LIST.tabGoodsType.TabPages[i].Tag as ucGrid).dgv;

                    DataTable dt = mdgv.DataSource as DataTable;
                    if (null == dt)
                    {
                        MessageBox.Show("请选择要入库的物料");

                        return;
                    }

                    if (dt.HasErrors)
                    {
                        string sError = string.Empty;

                        foreach (DataRow dr in dt.Rows)
                        {
                            sError += "\n" + dr.RowError;
                        }

                        MessageBox.Show("数据校验失败" + sError);

                        return;
                    }
                }

                if (!this.ucMANAGE_POSITION.U_CHECK_WAREHOUSE())
                    return;



                if (MessageBox.Show("是否保存?", "系统提示", MessageBoxButtons.YesNo, MessageBoxIcon.Question, MessageBoxDefaultButton.Button1) == DialogResult.Yes)
                {

                    MANAGE_MAIN mMANAGE_MAIN = new MANAGE_MAIN();

                    mMANAGE_MAIN.PLAN_ID = this.intPlanID;
                    mMANAGE_MAIN.PLAN_IDSpecified = true;

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                    mMANAGE_MAIN.STOCK_BARCODE = this.ucMANAGE_POSITION.U_STOCK_BARCODE;

                    mMANAGE_MAIN.CELL_MODEL = this.ucMANAGE_POSITION.U_CELL_MODEL;

                    mMANAGE_MAIN.START_CELL_ID = this.ucMANAGE_POSITION.U_START_POSITION_ID;
                    mMANAGE_MAIN.START_CELL_IDSpecified = true;

                    mMANAGE_MAIN.END_CELL_ID = this.ucMANAGE_POSITION.U_END_POSITION_ID;
                    mMANAGE_MAIN.END_CELL_IDSpecified = true;

                    mMANAGE_MAIN.MANAGE_OPERATOR = Program._USER.USER_NAME;

                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");

                    mMANAGE_MAIN.MANAGE_STATUS = Model.MANAGE_STATUS.WaitingSend.ToString();

                    mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;


                    List<MANAGE_LIST> listMANAGE_LIST = new List<MANAGE_LIST>();

                    foreach (TabPage tp in this.ucPLAN_LIST.tabGoodsType.TabPages)
                    {
                        MyDataGrid dgvPLAN_LIST = (tp.Tag as ucGrid).dgv;

                        foreach (DataRow drPLAN_LIST in (tp.Tag as ucGrid).dt.Rows)
                        {
                            if (!drPLAN_LIST.IsNull("check") && Convert.ToBoolean(drPLAN_LIST["check"]) == true)
                            {
                                MANAGE_LIST mMANAGE_LIST = new MANAGE_LIST();

                                mMANAGE_LIST.GOODS_ID = Convert.ToInt32(drPLAN_LIST["GOODS_ID"]);
                                mMANAGE_LIST.GOODS_IDSpecified = true;


                                mMANAGE_LIST.GOODS_PROPERTY1 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY1"]);

                                mMANAGE_LIST.GOODS_PROPERTY2 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY2"]);
                                mMANAGE_LIST.GOODS_PROPERTY3 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY3"]);
                                mMANAGE_LIST.GOODS_PROPERTY4 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY4"]);
                                mMANAGE_LIST.GOODS_PROPERTY5 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY5"]);
                                mMANAGE_LIST.GOODS_PROPERTY6 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY6"]);
                                mMANAGE_LIST.GOODS_PROPERTY7 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY7"]);
                                mMANAGE_LIST.GOODS_PROPERTY8 = Convert.ToString(drPLAN_LIST["GOODS_PROPERTY8"]);


                                mMANAGE_LIST.PLAN_LIST_ID = Convert.ToInt32(drPLAN_LIST["PLAN_LIST_ID"]);
                                mMANAGE_LIST.PLAN_LIST_IDSpecified = true;

                                mMANAGE_LIST.MANAGE_LIST_QUANTITY = Convert.ToDecimal(drPLAN_LIST["MANAGE_LIST_QUANTITY"]);

                                mMANAGE_LIST.MANAGE_LIST_QUANTITYSpecified = true;

                                if (mMANAGE_LIST.MANAGE_LIST_QUANTITY <= 0)
                                {
                                    MessageBox.Show("入库数量错误");

                                    return;
                                }

                                mMANAGE_LIST.MANAGE_LIST_REMARK = Convert.ToString(drPLAN_LIST["PLAN_LIST_REMARK"]);

                                listMANAGE_LIST.Add(mMANAGE_LIST);
                            }
                        }
                    }

                    bool bResult = false;

                    bResult = Program._I_PDAService.ManageCreate(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                             "ManageCreate",
                                                             mMANAGE_MAIN,
                                                             listMANAGE_LIST.ToArray<MANAGE_LIST>(),
                                                             true,
                                                             this.ucMANAGE_POSITION.U_CheckStockExistStorage,
                                                             this.ucMANAGE_POSITION.U_AutoCompleteTask,
                                                             this.ucMANAGE_POSITION.U_AutoDownloadControlTask,
                                                             out strResult);

                    if (bResult)
                    {
                        this.ucMANAGE_POSITION.U_Refresh();
                    }

                    if (bResult)
                        MessageBox.Show("保存成功\n" + strResult);
                    else
                        MessageBox.Show("保存失败\n" + strResult);


                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            
        }


        private void InitManagePosition()
        {
            try
            {
                this.ucMANAGE_POSITION.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
        }


        private void PLAN_LIST_Bind()
        {
            this.ucPLAN_LIST.listColumn = "<Field Column=\"MANAGE_LIST_QUANTITY\" fieldType=\"text\" bedit=\"true\" dbType=\"Decimal\" Header=\"入库数量\" index=\"31\" />";


            this.ucPLAN_LIST.bCheck = true;

            try
            {
                this.ucPLAN_LIST.listTable = "V_PLAN_LIST";

                this.ucPLAN_LIST.listXml = "PLAN_LIST";

                this.ucPLAN_LIST.listWhere = string.Format("AND PLAN_ID ={0}  /*AND PLAN_LIST_QUANTITY>PLAN_LIST_FINISHED_QUANTITY */  ", this.intPlanID);

                this.ucPLAN_LIST.colGroup = "GOODS_TYPE_ID";

                this.ucPLAN_LIST.colSplit = "GOODS_PROPERTY";

                this.ucPLAN_LIST.Init();

                foreach (TabPage tp in this.ucPLAN_LIST.tabGoodsType.TabPages)
                {
                    DataTable dt = (tp.Tag as ucGrid).dgv.DataSource as DataTable;

                    if (null != dt)
                    {
                        dt.ColumnChanged += new DataColumnChangeEventHandler(this.dtIO_PLAN_LIST_ColumnChanged);
                    }
                }

                this.ucPLAN_LIST.SetEditColumns("MANAGE_LIST_QUANTITY", Color.AliceBlue);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常" + ex.Message);
            }
        }



        private void dtIO_PLAN_LIST_ColumnChanged(object sender, DataColumnChangeEventArgs e)
        {
            try
            {
                bool bResult = true;

                string sResult = string.Empty;

                switch (e.Column.ColumnName)
                {
                    case "MANAGE_LIST_QUANTITY":

                        bResult = Convert.ToDecimal(e.Row["PLAN_LIST_QUANTITY"]) - Convert.ToDecimal(e.Row["PLAN_LIST_ORDERED_QUANTITY"]) >= Convert.ToDecimal(e.ProposedValue);

                        sResult = string.Format("入库数量错误");

                        if (!bResult)
                        {
                            break;
                        }

                        break;
                }

                if (bResult)
                {
                    e.Row.RowError = null;

                    e.Row.SetColumnError(e.Column, null);
                }
                else
                {
                    e.Row.RowError = sResult;

                    e.Row.SetColumnError(e.Column, sResult);
                }

            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常" + ex.Message);
            }
        }



    }
}
