﻿using System;
using System.Collections.Generic;
using SiaSun.LMS.Model;
using System.Text;
using System.ServiceModel;
using System.Data;

namespace SiaSun.LMS.Interface
{

    [ServiceContract()]
    public partial interface I_PDAService
    {
        [OperationContract]
        DataTable GetList(string strSQL);

        [OperationContract]
        ObjectT GetModel(string statementName, object parameterObject);

        [OperationContract]
        bool ManageCreate(string sClassName, string sMethodName,
                                 SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN,
                                 List<SiaSun.LMS.Model.MANAGE_LIST> lsMANAGE_LIST,
                                 bool bTrans,
                                 bool bCheckStorage,
                                 bool bComplete,
                                 bool bAutoSendControl,
                                 out string sResult);

        [OperationContract]
        SiaSun.LMS.Model.WH_CELL GetModelCellCode(string CELL_CODE);

        [OperationContract]
        bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SiaSun.LMS.Model.SYS_USER USER);

        [OperationContract]
        IList<SiaSun.LMS.Model.MANAGE_TYPE_PARAM> ManageTypeParamGetList(int MANAGE_TYPE_ID);
    }
}
