﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio 14
VisualStudioVersion = 14.0.25420.1
MinimumVisualStudioVersion = 10.0.40219.1
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Host", "Host", "{24F471AA-4B91-4223-8881-4F7614E2D5CC}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Client", "Client", "{E519D1DA-60B7-4960-94DF-13468233388C}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Service", "Service", "{21F94BB3-E992-4893-B91E-84F57214A97D}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Common", "Common", "{79C8ADB9-B998-4CF4-B81C-D1BDAD4D319D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.WPFClient", "Client\SiaSun.LMS.WPFClient\SiaSun.LMS.WPFClient.csproj", "{FC347234-7FF2-4758-9C63-BC8E1553C844}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.Model", "Common\SiaSun.LMS.Model\SiaSun.LMS.Model.csproj", "{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.Common", "Common\SiaSun.LMS.Common\SiaSun.LMS.Common.csproj", "{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.WinService", "Host\SiaSun.LMS.WinService\SiaSun.LMS.WinService.csproj", "{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "WinServiceConsole", "Host\WinServiceConsole\WinServiceConsole.csproj", "{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.Interface", "Service\SiaSun.LMS.Interface\SiaSun.LMS.Interface.csproj", "{612D8C56-ECEC-48B9-87F9-AF80BF81E070}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.Implement", "Service\SiaSun.LMS.Implement\SiaSun.LMS.Implement.csproj", "{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.Persistence", "Service\SiaSun.LMS.Persistence\SiaSun.LMS.Persistence.csproj", "{B820FA47-3514-49DB-8D33-96367A80234E}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{20775644-514F-4DE3-8D43-6B2378E051AB}"
	ProjectSection(SolutionItems) = preProject
		Remind.txt = Remind.txt
	EndProjectSection
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.Scaner", "Host\SiaSun.LMS.Scaner\SiaSun.LMS.Scaner.csproj", "{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SiaSun.LMS.MonitorMemory", "Host\SiaSun.LMS.MonitorMemory\SiaSun.LMS.MonitorMemory.csproj", "{6BE96445-94A2-4BB8-A442-E96E38513CCA}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Hub", "Hub", "{051EDB4B-F58C-4643-8D00-374E8F440202}"
EndProject
Project("{FAE04EC0-301F-11D3-BF4B-00C04F79EFBC}") = "SignalServer", "SignalServer\SignalServer.csproj", "{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}"
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Debug|Mixed Platforms = Debug|Mixed Platforms
		Debug|x86 = Debug|x86
		Release|Any CPU = Release|Any CPU
		Release|Mixed Platforms = Release|Mixed Platforms
		Release|x86 = Release|x86
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Debug|Any CPU.ActiveCfg = Debug|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Debug|x86.ActiveCfg = Debug|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Debug|x86.Build.0 = Debug|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Release|Any CPU.ActiveCfg = Release|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Release|Mixed Platforms.Build.0 = Release|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Release|x86.ActiveCfg = Release|x86
		{FC347234-7FF2-4758-9C63-BC8E1553C844}.Release|x86.Build.0 = Release|x86
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Debug|x86.ActiveCfg = Debug|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Release|Any CPU.Build.0 = Release|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40}.Release|x86.ActiveCfg = Release|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Debug|x86.ActiveCfg = Debug|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Release|Any CPU.Build.0 = Release|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF}.Release|x86.ActiveCfg = Release|Any CPU
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Debug|Any CPU.ActiveCfg = Debug|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Debug|x86.ActiveCfg = Debug|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Debug|x86.Build.0 = Debug|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Release|Any CPU.ActiveCfg = Release|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Release|Mixed Platforms.Build.0 = Release|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Release|x86.ActiveCfg = Release|x86
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0}.Release|x86.Build.0 = Release|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Debug|Any CPU.ActiveCfg = Debug|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Debug|Mixed Platforms.ActiveCfg = Debug|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Debug|Mixed Platforms.Build.0 = Debug|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Debug|x86.ActiveCfg = Debug|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Debug|x86.Build.0 = Debug|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Release|Any CPU.ActiveCfg = Release|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Release|Mixed Platforms.ActiveCfg = Release|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Release|Mixed Platforms.Build.0 = Release|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Release|x86.ActiveCfg = Release|x86
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D}.Release|x86.Build.0 = Release|x86
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Debug|x86.ActiveCfg = Debug|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Release|Any CPU.Build.0 = Release|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070}.Release|x86.ActiveCfg = Release|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Debug|x86.ActiveCfg = Debug|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Release|Any CPU.Build.0 = Release|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850}.Release|x86.ActiveCfg = Release|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Debug|x86.ActiveCfg = Debug|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Release|Any CPU.Build.0 = Release|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{B820FA47-3514-49DB-8D33-96367A80234E}.Release|x86.ActiveCfg = Release|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Debug|x86.ActiveCfg = Debug|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Debug|x86.Build.0 = Debug|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Release|Any CPU.Build.0 = Release|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Release|x86.ActiveCfg = Release|Any CPU
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F}.Release|x86.Build.0 = Release|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Debug|x86.ActiveCfg = Debug|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Debug|x86.Build.0 = Debug|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Release|Any CPU.Build.0 = Release|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Release|x86.ActiveCfg = Release|Any CPU
		{6BE96445-94A2-4BB8-A442-E96E38513CCA}.Release|x86.Build.0 = Release|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Debug|Mixed Platforms.ActiveCfg = Debug|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Debug|Mixed Platforms.Build.0 = Debug|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Debug|x86.ActiveCfg = Debug|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Debug|x86.Build.0 = Debug|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Release|Any CPU.Build.0 = Release|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Release|Mixed Platforms.ActiveCfg = Release|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Release|Mixed Platforms.Build.0 = Release|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Release|x86.ActiveCfg = Release|Any CPU
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B}.Release|x86.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(NestedProjects) = preSolution
		{FC347234-7FF2-4758-9C63-BC8E1553C844} = {E519D1DA-60B7-4960-94DF-13468233388C}
		{67D2BF1B-CF66-4657-91B3-8EEDFC46BD40} = {79C8ADB9-B998-4CF4-B81C-D1BDAD4D319D}
		{FD10F3E5-A233-4F52-B4EE-33189D84DBEF} = {79C8ADB9-B998-4CF4-B81C-D1BDAD4D319D}
		{9DF5FDDC-943E-4571-B02D-7F96E833D8A0} = {24F471AA-4B91-4223-8881-4F7614E2D5CC}
		{CA4DC5A3-015D-4C60-A4BB-6C38C1C7801D} = {24F471AA-4B91-4223-8881-4F7614E2D5CC}
		{612D8C56-ECEC-48B9-87F9-AF80BF81E070} = {21F94BB3-E992-4893-B91E-84F57214A97D}
		{10C5DA09-0D1E-4AE2-80C4-FB7B916FA850} = {21F94BB3-E992-4893-B91E-84F57214A97D}
		{B820FA47-3514-49DB-8D33-96367A80234E} = {21F94BB3-E992-4893-B91E-84F57214A97D}
		{942147EF-BFB6-462E-A3CC-31F9ED41EF5F} = {24F471AA-4B91-4223-8881-4F7614E2D5CC}
		{6BE96445-94A2-4BB8-A442-E96E38513CCA} = {24F471AA-4B91-4223-8881-4F7614E2D5CC}
		{E0E4A956-C7ED-403D-89C5-5FA7D04A9E8B} = {051EDB4B-F58C-4643-8D00-374E8F440202}
	EndGlobalSection
EndGlobal
