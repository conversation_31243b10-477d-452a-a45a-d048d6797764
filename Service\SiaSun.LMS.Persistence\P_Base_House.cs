﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using IBatisNet.DataMapper;
using System.Collections;
using System.Data;
using IBatisNet.DataMapper.Configuration;

namespace SiaSun.LMS.Persistence
{
    public class P_Base_House : P_Base
    {
        public static volatile ISqlMapper _sqlMap;

        //wdz add 2017-08-18
        public static bool _isOracelProvider;

        public P_Base_House()
        {
            if (_sqlMap == null)
            {
                if (_sqlMap == null)
                {
                    DomSqlMapBuilder builder = new DomSqlMapBuilder();
                    _sqlMap = builder.Configure("HouseMap.config");
                }
            }

            base._sqlMap = _sqlMap;

            //wdz add 2017-08-18
            _isOracelProvider = _sqlMap.DataSource.DbProvider.ToString().ToLower().Contains("oracle");
        }



    
    }
}