﻿<ResourceDictionary xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
                    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
                    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock">
    
    <!--默认Button样式-->
    <Style x:Key="styleDefaultButton" TargetType="Button">
        <Setter Property="Height" Value="80"></Setter>
        <Setter Property="Padding" Value="2,0,2,0"></Setter>
        <Setter Property="Margin" Value="10,2,10,2"></Setter>
        <Setter Property="Background" Value="{StaticResource buttonBackgroundDefaultBrush}"></Setter>
        <Style.Triggers>
            <Trigger Property="IsMouseOver" Value="True">
                <Setter Property="Background" Value="{StaticResource mouseOverDefaultBrush}"></Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--默认TextBox样式-->
    <Style x:Key="styleDefaultTextBox" TargetType="TextBox">
        <Setter Property="ToolTip" Value="{Binding RelativeSource={RelativeSource self}, Path=(Validation.Errors)[0].ErrorContent}"></Setter>
        <Setter Property="MaxHeight" Value="23"></Setter>
        <Setter Property="MinHeight" Value="21"></Setter>
        <Setter Property="VerticalAlignment" Value="Center"></Setter>
        <Style.Triggers>
            <Trigger Property="IsReadOnly" Value="True">
                <Setter Property="Background" Value="LightGray"></Setter>
            </Trigger>
            <MultiTrigger>
                <MultiTrigger.Conditions>
                    <Condition Property="IsFocused" Value="True"></Condition>
                    <Condition Property="IsEnabled" Value="True"></Condition>
                    <Condition Property="IsReadOnly" Value="False"></Condition>
                </MultiTrigger.Conditions>
                <Setter Property="Background" Value="LightGreen"></Setter>
            </MultiTrigger>
        </Style.Triggers>
    </Style>
    
    <!--默认Window样式-->
    <Style x:Key="styleDefaultWindow" TargetType="Window">
        <Setter Property="SnapsToDevicePixels" Value="true" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="Window">
                    <Grid Background="{StaticResource windowBackgroundDefaultBrush}">                        
                        <AdornerDecorator>
                            <ContentPresenter />
                        </AdornerDecorator>
                        <ResizeGrip x:Name="WindowResizeGrip" HorizontalAlignment="Right" VerticalAlignment="Bottom" Visibility="Collapsed" IsTabStop="false" />
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="ResizeMode" Value="CanResizeWithGrip">
                            <Setter TargetName="WindowResizeGrip" Property="Visibility" Value="Visible" />
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认DockWindow样式-->
    <Style x:Key="styleDefaultDockWindow" TargetType="ad:DocumentContent">
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush EndPoint="0.5,1"  StartPoint="0.5,0">
                    <GradientStop Color="White" Offset="0" />
                    <GradientStop Color="White" Offset="0.5" />
                    <GradientStop Color="White"  Offset="1" />
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认Menu样式-->
    <Style x:Key="styleDefaultMenu" TargetType="Menu">
        <Setter Property="FontSize" Value="14"></Setter>
        <Setter Property="FontFamily" Value="微软雅黑"></Setter>
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Menu}">
                    <Border  Background="{StaticResource menuBackgroundDefaultBrush}"  BorderBrush="{StaticResource NormalBorderBrush}"   BorderThickness="1">
                        <StackPanel ClipToBounds="True" Orientation="Horizontal" IsItemsHost="True"/>
                    </Border>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>    

    <!--默认MenuItem样式-->
    <Style x:Key="styleDefaultMenuItem" TargetType="MenuItem">
        <Setter Property="FontSize" Value="13"></Setter>
        <Style.Triggers>
            <Trigger Property="Role" Value="SubmenuHeader">
                <Setter Property="Background" Value="{StaticResource menuItemBackgroundDefaultBrush}"></Setter>
            </Trigger>
            <Trigger Property="Role" Value="SubmenuItem">
                <Setter Property="Background" Value="{StaticResource menuItemBackgroundDefaultBrush}"></Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--带有CheckBox的MenuItem-->
    <ControlTemplate x:Key="templateCheckBoxMenuItem" TargetType="MenuItem">
        <Grid>
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="Auto"></ColumnDefinition>
                <ColumnDefinition Width="*"></ColumnDefinition>
            </Grid.ColumnDefinitions>
            <CheckBox Grid.Column="0" IsChecked="{TemplateBinding IsChecked}" Margin="5"></CheckBox>
            <TextBlock Grid.Column="1" Text="{TemplateBinding Header}" Margin="5"></TextBlock>
        </Grid>
    </ControlTemplate>

    <!--默认ContextMenu样式-->
    <Style x:Key="styleDefaultContextMenu" TargetType="ContextMenu">
        <Setter Property="SnapsToDevicePixels" Value="True"/>
        <Setter Property="OverridesDefaultStyle" Value="True"/>
        <Setter Property="Grid.IsSharedSizeScope" Value="true"/>
        <Setter Property="HasDropShadow" Value="True"/>
        <Setter Property="Padding" Value="2,1,5,1"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type ContextMenu}">
                    <Border  Name="Border"   BorderBrush="DarkBlue"   BorderThickness="1" >
                        <Grid>
                            <Grid.ColumnDefinitions>
                                <ColumnDefinition Width="Auto"></ColumnDefinition>
                                <ColumnDefinition Width="Auto"></ColumnDefinition>
                            </Grid.ColumnDefinitions>
                            <Rectangle Grid.Column="0" Width="15">
                                <Rectangle.Fill>
                                    <LinearGradientBrush StartPoint="1,0" EndPoint="0,0">
                                        <GradientStop Color="Black" Offset="0.1"></GradientStop>
                                        <GradientStop Color="White" Offset="1"></GradientStop>
                                        <GradientStop></GradientStop>
                                    </LinearGradientBrush>
                                </Rectangle.Fill>
                            </Rectangle>
                            <StackPanel Grid.Column="1" IsItemsHost="True" KeyboardNavigation.DirectionalNavigation="Cycle" Background="{StaticResource menuItemBackgroundDefaultBrush}" />
                        </Grid>
                    </Border>
                    <ControlTemplate.Triggers>
                        <Trigger Property="HasDropShadow" Value="true">
                            <Setter TargetName="Border" Property="Padding" Value="0,3,0,3"/>
                            <Setter TargetName="Border" Property="CornerRadius" Value="4"/>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认Separator样式-->
    <Style x:Key="styleDefaultSeparator" TargetType="Separator">
        <Setter Property="Height" Value="1"/>
        <Setter Property="Margin" Value="0,2,2,4"/>
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type Separator}">
                    <Border BorderBrush="#888" BorderThickness="1"/>
                </ControlTemplate>
            </Setter.Value>
        </Setter>

    </Style>
    
    <!--默认StatusBar样式-->
    <Style x:Key="styleDefaultStatusBar" TargetType="StatusBar">
        <Setter Property="FontFamily" Value="微软雅黑"></Setter>
        <Setter Property="FontSize" Value="12"></Setter>
        <Setter Property="Background" Value="{StaticResource menuBackgroundDefaultBrush}" ></Setter>
        <Setter Property="BitmapEffect">
            <Setter.Value>
                <DropShadowBitmapEffect Direction="0.2"></DropShadowBitmapEffect>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认ToolBar样式-->
    <Style x:Key="styleDefaultToolBar" TargetType="ToolBar">
        <Setter Property="FontFamily" Value="微软雅黑"></Setter>
        <Setter Property="FontSize" Value="12"></Setter>
        <Setter Property="Background" Value="{StaticResource statusBarBackgroundDefaultBrush}" ></Setter>
        <Setter Property="BitmapEffect">
            <Setter.Value>
                <DropShadowBitmapEffect Direction="0.2"></DropShadowBitmapEffect>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认GroupBox样式-->
     <Style x:Key="styleDefaultGroupBox" TargetType="GroupBox">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="GroupBox">
                    <Grid>
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <Border Grid.Row="0" BorderThickness="1" CornerRadius="2,2,0,0">
                            <Border.BorderBrush>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Color="{DynamicResource BorderLightColor}" Offset="0.0" />
                                            <GradientStop Color="{DynamicResource BorderDarkColor}" Offset="1.0" />
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.BorderBrush>

                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Color="White" Offset="0.0" />
                                            <GradientStop Color="PaleGoldenrod" Offset="1.0" />
                                            <!--<GradientStop Color="{DynamicResource ControlLightColor}" Offset="0.0" />-->
                                            <!--<GradientStop Color="{DynamicResource ControlMediumColor}" Offset="1.0" />-->
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.Background>

                            <ContentPresenter Margin="4" ContentSource="Header" RecognizesAccessKey="True" />
                        </Border>

                        <Border Grid.Row="1" BorderThickness="1,0,1,1" CornerRadius="0,0,2,2" Background="{StaticResource ControlBackBrush}">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="{StaticResource BorderMediumColor}" />
                            </Border.BorderBrush>
                            <ContentPresenter Margin="1" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认GridSplitter样式-->
    <Style x:Key="styleDefaultGridSplitter" TargetType="GridSplitter">
        <Setter Property="ShowsPreview" Value="False"></Setter>
        <Style.Triggers>
            <Trigger Property="HorizontalAlignment" Value="Stretch">
                <Setter Property="VerticalAlignment" Value="Center"></Setter>
                <Setter Property="Margin" Value="20,2,20,2"></Setter>
                <Setter Property="Height" Value="3"></Setter>
                <Setter Property="BitmapEffect">
                    <Setter.Value>
                        <DropShadowBitmapEffect></DropShadowBitmapEffect>
                    </Setter.Value>
                </Setter>
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="1,0.5">
                            <GradientStop Color="Gray" Offset="0.3"></GradientStop>
                            <GradientStop Color="Black" Offset="0.5"></GradientStop>
                            <GradientStop Color="Gray" Offset="0.8"></GradientStop>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
            <Trigger Property="VerticalAlignment" Value="Stretch">
                <Setter Property="HorizontalAlignment" Value="Center"></Setter>
                <Setter Property="Margin" Value="2,20,2,20"></Setter>
                <Setter Property="Width" Value="3"></Setter>
                <Setter Property="BitmapEffect">
                    <Setter.Value>
                        <DropShadowBitmapEffect></DropShadowBitmapEffect>
                    </Setter.Value>
                </Setter>
                <Setter Property="Background">
                    <Setter.Value>
                        <LinearGradientBrush StartPoint="0,0" EndPoint="0.5,1">
                            <GradientStop Color="Gray" Offset="0.3"></GradientStop>
                            <GradientStop Color="Black" Offset="0.5"></GradientStop>
                            <GradientStop Color="Gray" Offset="0.8"></GradientStop>
                        </LinearGradientBrush>
                    </Setter.Value>
                </Setter>
            </Trigger>
        </Style.Triggers>
    </Style>

    <!--默认TabControl样式-->
    <Style x:Key="styleDefaultTabControl"  TargetType="TabControl">
        <Setter Property="OverridesDefaultStyle" Value="True" />
        <Setter Property="SnapsToDevicePixels" Value="True" />
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="{x:Type TabControl}">
                    <Grid KeyboardNavigation.TabNavigation="Local">
                        <Grid.RowDefinitions>
                            <RowDefinition Height="Auto" />
                            <RowDefinition Height="*" />
                        </Grid.RowDefinitions>
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="#FFAAAAAA" />
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <TabPanel x:Name="HeaderPanel" Grid.Row="0" Panel.ZIndex="1" Margin="0,0,4,-1" IsItemsHost="True" KeyboardNavigation.TabIndex="1" Background="Transparent" />
                        <Border x:Name="Border" Grid.Row="1" Background="{StaticResource ControlBackBrush}" BorderThickness="1" CornerRadius="2" KeyboardNavigation.TabNavigation="Local" KeyboardNavigation.DirectionalNavigation="Contained" KeyboardNavigation.TabIndex="2">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="{DynamicResource BorderMediumColor}"/>
                            </Border.BorderBrush>
                            <ContentPresenter x:Name="PART_SelectedContentHost" Margin="4" ContentSource="SelectedContent" />
                        </Border>
                    </Grid>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认TabItem样式-->
    <Style x:Key="styleDefaultTabItem" TargetType="TabItem">
        <Setter Property="Template">
            <Setter.Value>
                <ControlTemplate TargetType="TabItem">
                    <Grid x:Name="Root">
                        <VisualStateManager.VisualStateGroups>
                            <VisualStateGroup x:Name="SelectionStates">
                                <VisualState x:Name="Unselected" />
                                <VisualState x:Name="Selected">
                                    <Storyboard>
                                        <!--<ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Panel.Background).(GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{StaticResource ControlPressedColor}" />
                                        </ColorAnimationUsingKeyFrames>-->
                                        <ThicknessAnimationUsingKeyFrames Storyboard.TargetProperty="(Border.BorderThickness)" Storyboard.TargetName="Border">
                                            <EasingThicknessKeyFrame KeyTime="0" Value="1,1,1,0" />
                                        </ThicknessAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                            <VisualStateGroup x:Name="CommonStates">
                                <VisualState x:Name="Normal" />
                                <VisualState x:Name="MouseOver" />
                                <VisualState x:Name="Disabled">
                                    <Storyboard>
                                        <!--<ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Panel.Background).(GradientBrush.GradientStops)[1].(GradientStop.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{StaticResource DisabledControlDarkColor}" />
                                        </ColorAnimationUsingKeyFrames>-->
                                        <ColorAnimationUsingKeyFrames Storyboard.TargetName="Border" Storyboard.TargetProperty="(Border.BorderBrush).(SolidColorBrush.Color)">
                                            <EasingColorKeyFrame KeyTime="0" Value="{StaticResource DisabledBorderLightColor}"/>
                                        </ColorAnimationUsingKeyFrames>
                                    </Storyboard>
                                </VisualState>
                            </VisualStateGroup>
                        </VisualStateManager.VisualStateGroups>
                        <Border x:Name="Border" Margin="0,0,-4,0" BorderThickness="1,1,1,1" CornerRadius="2,12,0,0">
                            <Border.BorderBrush>
                                <SolidColorBrush Color="{DynamicResource BorderMediumColor}" />
                            </Border.BorderBrush>
                            <Border.Background>
                                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                    <LinearGradientBrush.GradientStops>
                                        <GradientStopCollection>
                                            <GradientStop Color="{DynamicResource ControlLightColor}" Offset="0.0" />
                                            <GradientStop Color="{DynamicResource ControlMediumColor}" Offset="1.0" />
                                        </GradientStopCollection>
                                    </LinearGradientBrush.GradientStops>
                                </LinearGradientBrush>
                            </Border.Background>
                            <ContentPresenter x:Name="ContentSite" VerticalAlignment="Center" HorizontalAlignment="Center" ContentSource="Header" Margin="12,2,12,2"  RecognizesAccessKey="True" />
                        </Border>
                    </Grid>
                    <ControlTemplate.Triggers>
                        <Trigger Property="IsSelected" Value="True">
                            <Setter Property="Panel.ZIndex" Value="100" />
                            <Setter TargetName="Border" Property="Background">
                                <Setter.Value>
                                    <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                                        <LinearGradientBrush.GradientStops>
                                            <GradientStopCollection>
                                                <GradientStop Color="White" Offset="0.2" />
                                                <GradientStop Color="PaleGoldenrod" Offset="1.0" />
                                            </GradientStopCollection>
                                        </LinearGradientBrush.GradientStops>
                                    </LinearGradientBrush>
                                </Setter.Value>
                            </Setter>
                        </Trigger>
                    </ControlTemplate.Triggers>
                </ControlTemplate>
            </Setter.Value>
        </Setter>
    </Style>

    <!--默认Border样式-->
    <Style x:Key="styleDefaultBorder" TargetType="Border">
        <Setter Property="BorderThickness" Value="1"></Setter>
        <Setter Property="BorderBrush" Value=" DarkGray"></Setter>
        <Setter Property="Background" Value="{StaticResource ControlBackBrush}"></Setter>
        <Setter Property="CornerRadius" Value="4"></Setter>
        <Setter Property="Effect">
            <Setter.Value>
                    <DropShadowEffect Color="DarkBlue" ShadowDepth="2"></DropShadowEffect>
            </Setter.Value>
        </Setter> 
    </Style>
    
    <!--Expander-->
    <Style x:Key="styleDefaultExpander" TargetType="Expander">
        <Setter Property="Padding" Value="5,0,5,0"></Setter>
        <Setter Property="Background">
            <Setter.Value>
                <LinearGradientBrush StartPoint="0,0" EndPoint="0,1">
                    <LinearGradientBrush.GradientStops>
                        <GradientStopCollection>
                            <GradientStop Color="White" Offset="0.0" />
                            <GradientStop Color="PaleGoldenrod" Offset="1.0" />
                        </GradientStopCollection>
                    </LinearGradientBrush.GradientStops>
                </LinearGradientBrush>
            </Setter.Value>
        </Setter>
    </Style>
</ResourceDictionary>