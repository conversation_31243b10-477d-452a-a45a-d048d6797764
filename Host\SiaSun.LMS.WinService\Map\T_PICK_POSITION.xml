﻿<?xml version="1.0" encoding="UTF-8" ?>
<sqlMap namespace="T_PICK_POSITION" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <alias>
    <typeAlias alias="T_PICK_POSITION" type="SiaSun.LMS.Model.T_PICK_POSITION, SiaSun.LMS.Model" />
  </alias>
  <resultMaps>
    <resultMap id="SelectResult" class="T_PICK_POSITION">
      <result property="POSITION_ID" column="position_id" />
      <result property="POSITION_CODE" column="position_code" />
      <result property="POSITON_NAME" column="positon_name" />
      <result property="STATION_ID" column="station_id" />
      <result property="WH_CELL_ID" column="wh_cell_id" />
      <result property="REMARK" column="remark" />
      <result property="DZBQ_MAC" column="dzbq_mac" />
      <result property="DZBQ_STATUS" column="dzbq_status" />
      <result property="DZBQ_SHOW" column="dzbq_show" />
      <result property="DZBQ_STORE" column="dzbq_store" />
      <result property="DZBQ_FLAG" column="dzbq_flag" />
      <result property="DZBQ_PROPERTY1" column="dzbq_property1" />
      <result property="DZBQ_PROPERTY2" column="dzbq_property2" />
      <result property="DZBQ_PROPERTY3" column="dzbq_property3" />
      <result property="DZBQ_PROPERTY4" column="dzbq_property4" />
      <result property="DZBQ_PROPERTY5" column="dzbq_property5" />
      <result property="POSITION_FLAG" column="position_flag" />
      <result property="POSTION_STATUS" column="postion_status" />
    </resultMap>
  </resultMaps>

  <statements>

    <select id="T_PICK_POSITION_SELECT" parameterClass="int" resultMap="SelectResult">
      Select
      position_id,
      position_code,
      positon_name,
      station_id,
      wh_cell_id,
      remark,
      dzbq_mac,
      dzbq_status,
      dzbq_show,
      dzbq_store,
      dzbq_flag,
      dzbq_property1,
      dzbq_property2,
      dzbq_property3,
      dzbq_property4,
      dzbq_property5,
      position_flag,
      postion_status
      From T_PICK_POSITION
    </select>

    <select id="T_PICK_POSITION_SELECT_BY_ID" parameterClass="int" extends = "T_PICK_POSITION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          position_id=#POSITION_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="T_PICK_POSITION_SELECT_BY_DZBQ_MAC" parameterClass="int" extends = "T_PICK_POSITION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          dzbq_mac=#DZBQ_MAC#
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_SELECT_BY_DZBQ_PROPERTY5" parameterClass="int" extends = "T_PICK_POSITION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          dzbq_property5=#DZBQ_PROPERTY5#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="T_PICK_POSITION_SELECT_BY_STATION_ID" parameterClass="int" extends = "T_PICK_POSITION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          station_id=#STATION_ID#
        </isParameterPresent>
      </dynamic>
    </select>
    
    <select id="T_PICK_POSITION_SELECT_BY_PLAN_LIST_ID" parameterClass="int" extends = "T_PICK_POSITION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          position_id in (select pick_position_id from T_PICK_POSITION_PLAN_BIND where plan_id in (select plan_id from plan_list where plan_list_id=#PLAN_LIST_ID#))
        </isParameterPresent>
      </dynamic>
    </select>

    <select id="T_PICK_POSITION_SELECT_BY_WH_CELL_ID" parameterClass="int" extends = "T_PICK_POSITION_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          wh_cell_id=#WH_CELL_ID#
        </isParameterPresent>
      </dynamic>
    </select>

    <insert id="T_PICK_POSITION_INSERT" parameterClass="T_PICK_POSITION">
      Insert Into T_PICK_POSITION (
      <!--position_id,-->
      position_code,
      positon_name,
      station_id,
      wh_cell_id,
      remark,
      dzbq_mac,
      dzbq_status,
      dzbq_show,
      dzbq_store,
      dzbq_flag,
      dzbq_property1,
      dzbq_property2,
      dzbq_property3,
      dzbq_property4,
      dzbq_property5,
      position_flag,
      postion_status
      )Values(
      <!--#POSITION_ID#,-->
      #POSITION_CODE#,
      #POSITON_NAME#,
      #STATION_ID#,
      #WH_CELL_ID#,
      #REMARK#,
      #DZBQ_MAC#,
      #DZBQ_STATUS#,
      #DZBQ_SHOW#,
      #DZBQ_STORE#,
      #DZBQ_FLAG#,
      #DZBQ_PROPERTY1#,
      #DZBQ_PROPERTY2#,
      #DZBQ_PROPERTY3#,
      #DZBQ_PROPERTY4#,
      #DZBQ_PROPERTY5#,
      #POSITION_FLAG#,
      #POSTION_STATUS#
      )
    </insert>

    <update id="T_PICK_POSITION_UPDATE" parameterClass="T_PICK_POSITION">
      Update T_PICK_POSITION Set
      <!--position_id=#POSITION_ID#,-->
      position_code=#POSITION_CODE#,
      positon_name=#POSITON_NAME#,
      station_id=#STATION_ID#,
      wh_cell_id=#WH_CELL_ID#,
      remark=#REMARK#,
      dzbq_mac=#DZBQ_MAC#,
      dzbq_status=#DZBQ_STATUS#,
      dzbq_show=#DZBQ_SHOW#,
      dzbq_store=#DZBQ_STORE#,
      dzbq_flag=#DZBQ_FLAG#,
      dzbq_property1=#DZBQ_PROPERTY1#,
      dzbq_property2=#DZBQ_PROPERTY2#,
      dzbq_property3=#DZBQ_PROPERTY3#,
      dzbq_property4=#DZBQ_PROPERTY4#,
      dzbq_property5=#DZBQ_PROPERTY5#,
      position_flag=#POSITION_FLAG#,
      postion_status=#POSTION_STATUS#
      <dynamic prepend="WHERE">
        <isParameterPresent>
          position_id=#POSITION_ID#
        </isParameterPresent>
      </dynamic>
    </update>

    <delete id="T_PICK_POSITION_DELETE" parameterClass="int">
      Delete From T_PICK_POSITION
      <dynamic prepend="WHERE">
        <isParameterPresent>
          position_id=#POSITION_ID#
        </isParameterPresent>
      </dynamic>
    </delete>

  </statements>
</sqlMap>