﻿using System;
using System.Collections.Generic;
using SiaSun.LMS.Model;
using System.Text;
using System.ServiceModel;
using System.Data;

namespace SiaSun.LMS.Interface
{
    [ServiceContract()]

    [ServiceKnownType(typeof(Object))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.GOODS_TEMPLATE))]
    [ServiceKnownType(typeof(DataTable))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.PLAN_MAIN))]
    [ServiceKnownType(typeof(DataTable))]
    [ServiceKnownType(typeof(List<object>))]

    [ServiceKnownType(typeof(GOODS_MAIN))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.SYS_USER))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_LIST))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_MAIN))]
    [ServiceKnownType(typeof(List<MANAGE_LIST>))]
    [ServiceKnownType(typeof(List<PLAN_LIST>))]
    [ServiceKnownType(typeof(SiaSun.LMS.Model.MANAGE_TYPE_PARAM))]

    public partial interface I_BaseService
    {
        [OperationContract]
        bool Invoke(string sPlanType, string sMethod, object[] inParams, out string sResult);

        [OperationContract(Name = "Invoke1")]
        bool Invoke(string sPlanType, string sMethod, object[] inParams, out object[] outParams);

        [OperationContract]
        void ExecuteNonQuery_ReturnVoid(string strSQL, string DataAccess = "HouseMap");

        [OperationContract]
        int ExecuteNonQuery_ReturnInt(string strSQL, string DataAccess = "HouseMap");

        [OperationContract]
        DataTable GetList(string strSQL, string DataAccess = "HouseMap");

        [OperationContract]
        DataTable GetTableXmlSql(string statementsql, object paramObject,string DataAccess = "HouseMap");

        [OperationContract]
        int Save(DataTable dt, string tablename, string DataAccess = "HouseMap");

        [OperationContract]
        ObjectT GetModel(string statementName, object parameterObject,string DataAccess = "HouseMap");

        [OperationContract]
        ObjectTList GetListObject(string statementName, object parameterObject, string DataAccess = "HouseMap");

        [OperationContract]
        string MessageConverter_GetKeyValue(string Key, params object[] Param);

        [OperationContract]
        string GetDataBaseConnectionString();
        
        /// <summary>系统初始化
        /// 系统初始化
        /// </summary>
        [OperationContract]
        void InitSystem();

        /// <summary>
        /// 生成系统日志
        /// </summary>
        [OperationContract]
        void CreateSysLog(Enum.LogThread thread, string logger, Enum.LOG_LEVEL level, string message);
        
    }
}
