﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucSplitPropertyPanel"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             mc:Ignorable="d" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC.Validate"
             d:DesignHeight="189" d:DesignWidth="260" >
    <Grid>
        <GroupBox  Header="属性编辑面板">            
            <Border>
                <Grid>
                    <Grid.RowDefinitions>
                        <RowDefinition Height="Auto"></RowDefinition>
                        <RowDefinition Height="*"></RowDefinition>
                    </Grid.RowDefinitions>

                    <StackPanel Orientation="Horizontal" Grid.Row="0">
                        <TextBlock Text="类型："  Margin="5"></TextBlock>
                        <ComboBox Name="cmbGroup" Margin="5,5,5,5" MinWidth="120" SelectionChanged="cmbGroup_SelectionChanged"></ComboBox>
                    </StackPanel>
                    <ScrollViewer Grid.Row="1" >
                        <WrapPanel Name="panelItem" ></WrapPanel>
                    </ScrollViewer>
                </Grid>
            </Border>
        </GroupBox>
    </Grid>
</UserControl>
