﻿<?xml version="1.0" encoding="UTF-8" ?> 
<sqlMap namespace="SYS_TABLE_CONVERTER" xmlns="http://ibatis.apache.org/mapping" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"> 
	<alias>
		<typeAlias alias="SYS_TABLE_CONVERTER" type="SiaSun.LMS.Model.SYS_TABLE_CONVERTER, SiaSun.LMS.Model" />
	</alias>
	<resultMaps>
		<resultMap id="SelectResult" class="SYS_TABLE_CONVERTER">
			<result property="TABLE_CONVERTER_ID" column="table_converter_id" />
			<result property="TABLE_CONVERTER_CODE" column="table_converter_code" />
			<result property="TABLE_CONVERTER_NAME" column="table_converter_name" />
			<result property="PARENT_TABLE" column="parent_table" />
			<result property="PARENT_KEY" column="parent_key" />
			<result property="CHILD_TABLE" column="child_table" />
			<result property="CHILD_FOREIGN_KEY" column="child_foreign_key" />
			<result property="GOODS_TYPE_ID" column="goods_type_id" />
			<result property="REMARK" column="remark" />
		</resultMap>
	</resultMaps>
	
	<statements>
	
	    <select id="SYS_TABLE_CONVERTER_SELECT" parameterClass="int" resultMap="SelectResult">
			Select 
				  table_converter_id,
				  table_converter_code,
				  table_converter_name,
				  parent_table,
				  parent_key,
				  child_table,
				  child_foreign_key,
				  goods_type_id,
				  remark
			From SYS_TABLE_CONVERTER
		</select>
		
		<select id="SYS_TABLE_CONVERTER_SELECT_BY_ID" parameterClass="int" extends = "SYS_TABLE_CONVERTER_SELECT" resultMap="SelectResult">
			
			<dynamic prepend="WHERE">
				<isParameterPresent>
					table_converter_id=#TABLE_CONVERTER_ID# 
				</isParameterPresent>
			</dynamic>
		</select>

    <select id="SYS_TABLE_CONVERTER_SELECT_CONVERTER_CODE" parameterClass="string" extends = "SYS_TABLE_CONVERTER_SELECT" resultMap="SelectResult">

      <dynamic prepend="WHERE">
        <isParameterPresent>
          TABLE_CONVERTER_CODE=#TABLE_CONVERTER_CODE#
        </isParameterPresent>
      </dynamic>
    </select>

				
		<insert id="SYS_TABLE_CONVERTER_INSERT" parameterClass="SYS_TABLE_CONVERTER">
      Insert Into SYS_TABLE_CONVERTER (
      table_converter_id,
      table_converter_code,
      table_converter_name,
      parent_table,
      parent_key,
      child_table,
      child_foreign_key,
      goods_type_id,
      remark
      )Values(
      #TABLE_CONVERTER_ID#,
      #TABLE_CONVERTER_CODE#,
      #TABLE_CONVERTER_NAME#,
      #PARENT_TABLE#,
      #PARENT_KEY#,
      #CHILD_TABLE#,
      #CHILD_FOREIGN_KEY#,
      #GOODS_TYPE_ID#,
      #REMARK#
      )
      <!--<selectKey  resultClass="int" type="post" property="TABLE_CONVERTER_ID">
        select @@IDENTITY as value
      </selectKey>-->
		</insert>
		
		<update id="SYS_TABLE_CONVERTER_UPDATE" parameterClass="SYS_TABLE_CONVERTER">
      Update SYS_TABLE_CONVERTER Set
      table_converter_id=#TABLE_CONVERTER_ID#,
      table_converter_code=#TABLE_CONVERTER_CODE#,
      table_converter_name=#TABLE_CONVERTER_NAME#,
      parent_table=#PARENT_TABLE#,
      parent_key=#PARENT_KEY#,
      child_table=#CHILD_TABLE#,
      child_foreign_key=#CHILD_FOREIGN_KEY#,
      goods_type_id=#GOODS_TYPE_ID#,
      remark=#REMARK#
      <dynamic prepend="WHERE">
				<isParameterPresent>
					table_converter_id=#TABLE_CONVERTER_ID#
				</isParameterPresent>
			</dynamic>
		</update>
		
		<delete id="SYS_TABLE_CONVERTER_DELETE" parameterClass="int">
			Delete From SYS_TABLE_CONVERTER
			<dynamic prepend="WHERE">
				<isParameterPresent>
					table_converter_id=#TABLE_CONVERTER_ID#
				</isParameterPresent>
			</dynamic>
		</delete>
		
	</statements>
</sqlMap>