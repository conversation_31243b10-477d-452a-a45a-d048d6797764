﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;

namespace SiaSun.LMS.WPFClient.SYS
{
    /// <summary>
    /// MENU_EDIT.xaml 的交互逻辑
    /// </summary>
    public partial class MENU_EDIT : AvalonDock.DocumentContent
    {
        public MENU_EDIT()
        {
            InitializeComponent();

            this.tvwMenu.U_ItemSelectedChanged += new UC.ucTreeView.U_ItemSelectedChangedHandler(tvwMenu_U_ItemSelectedChanged);
        }

        /// <summary>
        /// 加载节点
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            //添加根节点
            this.tvwMenu.U_Header = "功能菜单";
            this.tvwMenu.U_AllowCheck = false;
            this.tvwMenu.U_LoadTreeViewItems("功能菜单",0, "SYS_MENU", "MENU_NAME AS HEADER", "MENU_ID", "MENU_PARENT_ID", "0", "", "MENU_ORDER");
        }

        /// <summary>
        /// 选择节点
        /// </summary>
        void tvwMenu_U_ItemSelectedChanged(TreeViewItem itemSelected)
        {
            if (itemSelected == null) this.gridMenu.U_DataSource = null;

            grpBoxMenu.Header = string.Format(grpBoxMenu.Tag.ToString(),itemSelected.Header.ToString());

            //加载菜单项
            Menu_Bind(itemSelected);
        }

        /// <summary>
        /// 加载菜单选项
        /// </summary>
        private void Menu_Bind(TreeViewItem item)
        {
            if (item.Tag != null)
            {
                try
                {
                    MainWindow.mainWin.Cursor = Cursors.Wait;

                    int intParentID = Convert.ToInt32(item.Tag);

                    this.gridMenu.U_Clear();
                    this.gridMenu.U_WindowName = this.GetType().Name;
                    this.gridMenu.U_TableName = "SYS_MENU";
                    this.gridMenu.U_XmlTableName = "SYS_MENU";
                    this.gridMenu.U_Where = string.Format("MENU_PARENT_ID = {0} ", intParentID);
                    this.gridMenu.U_AllowOperatData = true;
                    this.gridMenu.U_OrderField = "MENU_ORDER";
                    this.gridMenu.U_DefaultRowValues.Add("MENU_PARENT_ID", intParentID.ToString());
                    this.gridMenu.U_InitControl();
                }
                catch (Exception ex)
                {
                    MainApp._MessageDialog.ShowException(ex);
                }
                finally
                {
                    MainWindow.mainWin.Cursor = Cursors.Arrow;
                }
            }
        }     
    }
}
