﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Reflection;
using SiaSun.LMS.Model;
using System.Collections;
using System.Data;

namespace SiaSun.LMS.Implement
{
    public class ApplyBase:S_BaseService
    {

        protected Model.WH_CELL mSTART_CELL;
        
        //xcjt alter 2016-12-23
        //protected APPLY_TYPE_PARAM applyTypeParam;
        protected APPLY_TYPE_PARAM applyTypeParam = new APPLY_TYPE_PARAM();

        protected  bool Validate(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;

            mSTART_CELL = this._P_WH_CELL.GetModel(mIO_CONTROL_APPLY.DEVICE_CODE.TrimEnd());
            if (null == mSTART_CELL)
            {
                bResult = false;
                sResult = "托盘条码->" + mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() + "申请入库未提供起始站台";
                return bResult;
            }

            if (mIO_CONTROL_APPLY.STOCK_BARCODE == null || mIO_CONTROL_APPLY.STOCK_BARCODE.TrimEnd() == string.Empty )
            {
                bResult = false;
                sResult = "申请入库未提供托盘条码";
                return bResult;
            }

            string validRegexString = string.Empty;
            if (this._S_SystemService.GetSysParameter("BoxBarcodeValidRegex", out validRegexString) &&
                !Common.RegexValid.IsValidate(mIO_CONTROL_APPLY.STOCK_BARCODE.Trim(), validRegexString))
            {
                //传入的条码不符合标准
                bResult = false;
                sResult = string.Format("申请条码不符合规范 申请值-{0}", mIO_CONTROL_APPLY.STOCK_BARCODE);
                return bResult;
            }


            ////xcjt add 2017-01-06
            //if (mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER == null || mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER.TrimEnd() == string.Empty)
            //{
            //    bResult = false;
            //    sResult = "申请入库未提供托盘规格";
            //    return bResult;
            //}
            return bResult;
        }

        protected bool InitApplyParam(string sApplyType, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            Model.APPLY_TYPE mAPPLY_TYPE = (Model.APPLY_TYPE)this.GetModel("APPLY_TYPE_SELECT_BY_APPLY_TYPE_CODE", sApplyType).RequestObject;

            //xcjt alter 2016-12-23
            //Model.GOODS_TYPE mGOODS_TYPE = (Model.GOODS_TYPE)this.GetModel("GOODS_TYPE_SELECT_BY_GOODS_TYPE_REMARK", "MANAGE_TYPE").RequestObject;
            Model.GOODS_TYPE mGOODS_TYPE = (Model.GOODS_TYPE)this.GetModel("GOODS_TYPE_SELECT_BY_GOODS_TYPE_REMARK", "APPLY_TYPE").RequestObject;

            Hashtable ht = new Hashtable();
            
            string[] arStrProperty = mAPPLY_TYPE.APPLY_TYPE_PROPERTY.ToString().Split('|');

            DataTable tableSplitSource = this.GetList(string.Format("select * from goods_property where goods_property_flag='1'and goods_type_id = {0} order by goods_property_order", mGOODS_TYPE.GOODS_TYPE_ID));
            //设置别名
            tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Column";
            tableSplitSource.Columns["GOODS_PROPERTY_NAME"].ColumnName = "Header";
            tableSplitSource.Columns["GOODS_PROPERTY_DATASOURCE"].ColumnName = "DataBind";
            tableSplitSource.Columns["GOODS_PROPERTY_FIELDTYPE"].ColumnName = "ControlType";
            //tableSplitSource.Columns["GOODS_PROPERTY_FIELD"].ColumnName = "Field";
            tableSplitSource.Columns["GOODS_PROPERTY_VALID"].ColumnName = "Validation";


            for (int i = 0; i < tableSplitSource.Rows.Count; i++)
            {
                ht.Add(tableSplitSource.Rows[i]["column"].ToString(), arStrProperty[i].ToString());
            }

            PropertyInfo[] propertys_info = this.GetType().GetProperties();

            foreach (PropertyInfo pi in propertys_info)
            {
                if (ht.Contains(pi.Name))
                {
                    if (pi.PropertyType.Name == "Boolean")
                        pi.SetValue(this, Convert.ChangeType(ht[pi.Name].ToString() == "1" ? "True" : "False", pi.PropertyType), null);
                    else
                        pi.SetValue(this, Convert.ChangeType(ht[pi.Name] == null ? string.Empty : ht[pi.Name].ToString(), pi.PropertyType), null);

                }
            }

            PropertyInfo[] property_info_param = applyTypeParam.GetType().GetProperties();
            foreach (PropertyInfo pi in property_info_param)
            {
                if (ht.Contains(pi.Name))
                {
                    if (pi.PropertyType.Name == "Boolean")
                        pi.SetValue(applyTypeParam, Convert.ChangeType(ht[pi.Name].ToString() == "1" ? "True" : "False", pi.PropertyType), null);
                    else
                        pi.SetValue(applyTypeParam, Convert.ChangeType(ht[pi.Name] == null ? string.Empty : ht[pi.Name].ToString(), pi.PropertyType), null);

                }
            }

            return bResult;
        }

        protected bool SendLEDMessage(string sMessage, out string sResult)
        {
            bool bResult = true;

            sResult = string.Empty;

            return bResult;

        }

        protected void CreateApplyExceptionTask(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, string ExceptionStation)
        {
            try
            {
                IList<Model.IO_CONTROL> lsIO_CONTROL = this._P_IO_CONTROL.GetList(mIO_CONTROL_APPLY.STOCK_BARCODE);
                if (lsIO_CONTROL.Count > 0)
                {
                    this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Error, string.Format("ApplyBase.CreateApplyExceptionTask():申请处理失败_由于当前条码存在Control任务_所以未能下达到异常站台的Control任务_条码[{0}]", mIO_CONTROL_APPLY.STOCK_BARCODE));
                    return;
                }

                Model.IO_CONTROL mIO_CONTROL = new IO_CONTROL();
                //wdz add 2018-01-24
                if (string.IsNullOrEmpty(ExceptionStation))
                {
                    return;
                }

                //xcjt add 2017-01-06
                //多个异常站台配置在一个字段中，在此选择正确的站台
                string[] ExceptionStationArray = null;
                if(!string.IsNullOrEmpty(ExceptionStation))
                {
                    ExceptionStationArray = ExceptionStation.Split(';');
                }

                mIO_CONTROL.RELATIVE_CONTROL_ID = -1;
                mIO_CONTROL.MANAGE_ID = 0;
                mIO_CONTROL.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;
                mIO_CONTROL.CONTROL_TASK_LEVEL = "0";
                mIO_CONTROL.PRE_CONTROL_STATUS = "0";
                mIO_CONTROL.CONTROL_TASK_TYPE = 3;
                mIO_CONTROL.START_DEVICE_CODE = mIO_CONTROL_APPLY.DEVICE_CODE;
                mIO_CONTROL.AGV_TASK = "0";

                //xcjt add 2017-01-06
                if (ExceptionStationArray != null)
                {
                    mIO_CONTROL.END_DEVICE_CODE = (from r in ExceptionStationArray
                                                   where r.StartsWith(mIO_CONTROL.START_DEVICE_CODE)
                                                   select r.Split('-')[1]).First();
                }

                mIO_CONTROL.CONTROL_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();
                mIO_CONTROL.CONTROL_STATUS = 0;

                Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModel(mIO_CONTROL.START_DEVICE_CODE);
                if (mWH_CELL_START != null && mWH_CELL_START.WAREHOUSE_ID != 0)
                {
                    Model.WH_WAREHOUSE mWH_WAREHOUSE_START = this._P_WH_WAREHOUSE.GetModel(mWH_CELL_START.WAREHOUSE_ID);
                    if(mWH_WAREHOUSE_START!=null)
                    {
                        mIO_CONTROL.START_WAREHOUSE_CODE = mWH_WAREHOUSE_START.WAREHOUSE_CODE;
                    }
                }

                Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(mIO_CONTROL.END_DEVICE_CODE);
                if (mWH_CELL_END != null && mWH_CELL_END.WAREHOUSE_ID != 0)
                {
                    Model.WH_WAREHOUSE mWH_WAREHOUSE_END = this._P_WH_WAREHOUSE.GetModel(mWH_CELL_END.WAREHOUSE_ID);
                    if (mWH_WAREHOUSE_END != null)
                    {
                        mIO_CONTROL.END_WAREHOUSE_CODE = mWH_WAREHOUSE_END.WAREHOUSE_CODE;
                    }
                }

                this._P_IO_CONTROL.Add(mIO_CONTROL);
                this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Information, string.Format("ApplyBase.CreateApplyExceptionTask():申请处理失败_下达Control任务_ControlId[{0}]_起点[{1}]_条码[{2}]_终点[{3}]", mIO_CONTROL.CONTROL_ID, mIO_CONTROL.START_DEVICE_CODE, mIO_CONTROL.STOCK_BARCODE, mIO_CONTROL.END_DEVICE_CODE));
            }
            catch (Exception ex)
            {
                this._log.ErrorFormat("ApplyBase.CreateApplyExceptionTask：申请处理失败后下发Control任务异常");
                this.CreateSysLog(Enum.LogThread.Control, "System", Enum.LOG_LEVEL.Error, string.Format("ApplyBase.CreateApplyExceptionTask():申请处理失败后下发Control任务异常_异常信息[{0}]", ex.Message));
            }
        }

        protected void WriteHisData(Model.IO_CONTROL_APPLY mIO_CONTROL_APPLY, bool result, string sMessage)
        {
            Model.IO_CONTROL_APPLY_HIS mIO_CONTROL_APPLY_HIS = new Model.IO_CONTROL_APPLY_HIS();

            mIO_CONTROL_APPLY_HIS.CONTROL_ID = mIO_CONTROL_APPLY.CONTROL_ID;
            mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_TYPE = mIO_CONTROL_APPLY.CONTROL_APPLY_TYPE;
            mIO_CONTROL_APPLY_HIS.WAREHOUSE_CODE = mIO_CONTROL_APPLY.WAREHOUSE_CODE;

            mIO_CONTROL_APPLY_HIS.DEVICE_CODE = mIO_CONTROL_APPLY.DEVICE_CODE;
            mIO_CONTROL_APPLY_HIS.STOCK_BARCODE = mIO_CONTROL_APPLY.STOCK_BARCODE;

            mIO_CONTROL_APPLY_HIS.APPLY_TASK_STATUS = result ? 1 : 0;

            mIO_CONTROL_APPLY_HIS.CREATE_TIME = mIO_CONTROL_APPLY.CREATE_TIME;
            mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_PARAMETER = mIO_CONTROL_APPLY.CONTROL_APPLY_PARAMETER;
            mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_REMARK = mIO_CONTROL_APPLY.CONTROL_APPLY_REMARK;

            mIO_CONTROL_APPLY_HIS.MANAGE_ERROR_TEXT = sMessage;
            mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_PARA01 = mIO_CONTROL_APPLY.CONTROL_APPLY_PARA01;
            mIO_CONTROL_APPLY_HIS.CONTROL_APPLY_PARA02 = mIO_CONTROL_APPLY.CONTROL_APPLY_PARA02;

            this._P_IO_CONTROL_APPLY_HIS.Add(mIO_CONTROL_APPLY_HIS);

            this._P_IO_CONTROL_APPLY.Delete(mIO_CONTROL_APPLY.CONTROL_APPLY_ID);
        }

    }
}
