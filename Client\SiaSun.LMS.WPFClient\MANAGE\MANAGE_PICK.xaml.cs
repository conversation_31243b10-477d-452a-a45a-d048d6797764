﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
//using System.Windows.Media;
//using System.Windows.Media.Imaging;
using System.Drawing;
using System.Drawing.Printing;
using System.Data;
using SiaSun.LMS.WPFClient.PICK.Model;

namespace SiaSun.LMS.WPFClient.MANAGE
{
    /// <summary>
    /// MANAGE_PICK.xaml 的交互逻辑
    /// </summary>
    public partial class MANAGE_PICK : AvalonDock.DocumentContent
    {
        PrinterHelper printHelperForPick;
        PrintDocument printDocumentForPick;

        public MANAGE_PICK()
        {
            InitializeComponent();
        }

        int statusCode = 0; //0-未开始  1-正在拣选

        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            LoadPlanUniqueCode();
            UpdateControlStatus();

            this.cbxStationCode.ItemsSource = new List<string>() { "21098", "22141" };

            //打印初始化
            InitPrint();
        }


        private void Grid_Click(object sender, RoutedEventArgs e)
        {
            decimal temp = 0;

            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnReloadPlan":
                        LoadPlanUniqueCode();
                        break;
                    case "btnIncrease1":
                        if (!string.IsNullOrEmpty(this.tbxPickCount1.Text) && decimal.TryParse(this.tbxPickCount1.Text, out temp))
                        {
                            this.tbxPickCount1.Text = (temp + 1).ToString();
                        }
                        break;
                    case "btnReduce1":
                        if (!string.IsNullOrEmpty(this.tbxPickCount1.Text) && decimal.TryParse(this.tbxPickCount1.Text, out temp))
                        {
                            this.tbxPickCount1.Text = (temp - 1).ToString();
                        }
                        break;
                    case "btnIncrease2":
                        if (!string.IsNullOrEmpty(this.tbxPickCount2.Text) && decimal.TryParse(this.tbxPickCount2.Text, out temp))
                        {
                            this.tbxPickCount2.Text = (temp + 1).ToString();
                        }
                        break;
                    case "btnReduce2":
                        if (!string.IsNullOrEmpty(this.tbxPickCount2.Text) && decimal.TryParse(this.tbxPickCount2.Text, out temp))
                        {
                            this.tbxPickCount2.Text = (temp - 1).ToString();
                        }
                        break;
                    case "btnIncrease3":
                        if (!string.IsNullOrEmpty(this.tbxPickCount3.Text) && decimal.TryParse(this.tbxPickCount3.Text, out temp))
                        {
                            this.tbxPickCount3.Text = (temp + 1).ToString();
                        }
                        break;
                    case "btnReduce3":
                        if (!string.IsNullOrEmpty(this.tbxPickCount3.Text) && decimal.TryParse(this.tbxPickCount3.Text, out temp))
                        {
                            this.tbxPickCount3.Text = (temp - 1).ToString();
                        }
                        break;
                    case "btnIncrease4":
                        if (!string.IsNullOrEmpty(this.tbxPickCount4.Text) && decimal.TryParse(this.tbxPickCount4.Text, out temp))
                        {
                            this.tbxPickCount4.Text = (temp + 1).ToString();
                        }
                        break;
                    case "btnReduce4":
                        if (!string.IsNullOrEmpty(this.tbxPickCount4.Text) && decimal.TryParse(this.tbxPickCount4.Text, out temp))
                        {
                            this.tbxPickCount4.Text = (temp - 1).ToString();
                        }
                        break;
                    case "btnIncrease5":
                        if (!string.IsNullOrEmpty(this.tbxPickCount5.Text) && decimal.TryParse(this.tbxPickCount5.Text, out temp))
                        {
                            this.tbxPickCount5.Text = (temp + 1).ToString();
                        }
                        break;
                    case "btnReduce5":
                        if (!string.IsNullOrEmpty(this.tbxPickCount5.Text) && decimal.TryParse(this.tbxPickCount5.Text, out temp))
                        {
                            this.tbxPickCount5.Text = (temp - 1).ToString();
                        }
                        break;
                    case "btnIncrease6":
                        if (!string.IsNullOrEmpty(this.tbxPickCount6.Text) && decimal.TryParse(this.tbxPickCount6.Text, out temp))
                        {
                            this.tbxPickCount6.Text = (temp + 1).ToString();
                        }
                        break;
                    case "btnReduce6":
                        if (!string.IsNullOrEmpty(this.tbxPickCount6.Text) && decimal.TryParse(this.tbxPickCount6.Text, out temp))
                        {
                            this.tbxPickCount6.Text = (temp - 1).ToString();
                        }
                        break;
                    default:
                        break;
                }
            }
        }

        private void cbxStationCode_SelectionChanged(object sender, SelectionChangedEventArgs e)
        {
            this.cbxStationCode.IsEnabled = false;
        }

        private void Grid_KeyDown(object sender, KeyEventArgs e)
        {
            string message = string.Empty;

            TextBox txt = e.OriginalSource as TextBox;
            if (e.Key == Key.Enter && txt != null)
            {
                switch (txt.Name)
                {
                    //空箱条码回车时视作开始拣选本单
                    case "tbxEmptyBox":

                        #region 空箱条码回车 锁定库存 生成原料箱下架任务

                        this.tbxEmptyBox.Text = this.tbxEmptyBox.Text.ToUpper();
                        message = MainApp._I_ManageService.ManualPickStart(MainApp._USER, this.tbxEmptyBox.Text, this.cbxUniqueCode.SelectedValue.ToString(), this.cbxStationCode.SelectedValue.ToString());
                        MainApp._MessageDialog.ShowResult(string.IsNullOrEmpty(message), message);
                        if (string.IsNullOrEmpty(message))
                        {
                            statusCode = 1;
                            UpdateControlStatus();
                            this.tbxSourceBox.Focus();
                        }
                        break;

                        #endregion

                    case "tbxSourceBox":

                        #region 原料箱回车 生成拣选任务

                        string boxMap = string.Empty;
                        message = MainApp._I_ManageService.ManualCreatePickTask(
                            MainApp._USER, this.tbxSourceBox.Text, this.cbxStationCode.SelectedValue.ToString(), this.cbxUniqueCode.SelectedValue.ToString(), out boxMap);
                        MainApp._MessageDialog.ShowResult(string.IsNullOrEmpty(message), message);
                        if (string.IsNullOrEmpty(message))
                        {
                            var mapArray = boxMap.Split('|');
                            //if(mapArray.Length != 6)
                            //{
                            //    MainApp._MessageDialog.ShowResult(false, "返回的待检格子号有误");
                            //}
                            bool focused = false;

                            if (!string.IsNullOrEmpty(mapArray[0]) && mapArray[0]!="0")
                            {
                                this.tbxGoodsCode1.IsEnabled = true;
                                this.tbxPickCount1.Text= mapArray[0];
                                this.btnIncrease1.IsEnabled = true;
                                this.btnReduce1.IsEnabled = true;

                                if(!focused)
                                {
                                    this.tbxGoodsCode1.Focus();
                                    focused = true;
                                }
                            }
                            if (!string.IsNullOrEmpty(mapArray[1]) && mapArray[1] != "0")
                            {
                                this.tbxGoodsCode2.IsEnabled = true;
                                this.tbxPickCount2.Text = mapArray[1];
                                this.btnIncrease2.IsEnabled = true;
                                this.btnReduce2.IsEnabled = true;

                                if (!focused)
                                {
                                    this.tbxGoodsCode2.Focus();
                                    focused = true;
                                }
                            }
                            if (!string.IsNullOrEmpty(mapArray[2]) && mapArray[2] != "0")
                            {
                                this.tbxGoodsCode3.IsEnabled = true;
                                this.tbxPickCount3.Text = mapArray[2];
                                this.btnIncrease3.IsEnabled = true;
                                this.btnReduce3.IsEnabled = true;

                                if (!focused)
                                {
                                    this.tbxGoodsCode3.Focus();
                                    focused = true;
                                }
                            }
                            if (!string.IsNullOrEmpty(mapArray[3]) && mapArray[3] != "0")
                            {
                                this.tbxGoodsCode4.IsEnabled = true;
                                this.tbxPickCount4.Text = mapArray[3];
                                this.btnIncrease4.IsEnabled = true;
                                this.btnReduce4.IsEnabled = true;

                                if (!focused)
                                {
                                    this.tbxGoodsCode4.Focus();
                                    focused = true;
                                }
                            }
                            if (!string.IsNullOrEmpty(mapArray[4]) && mapArray[4] != "0")
                            {
                                this.tbxGoodsCode5.IsEnabled = true;
                                this.tbxPickCount5.Text = mapArray[4];
                                this.btnIncrease5.IsEnabled = true;
                                this.btnReduce5.IsEnabled = true;

                                if (!focused)
                                {
                                    this.tbxGoodsCode5.Focus();
                                    focused = true;
                                }
                            }
                            if (!string.IsNullOrEmpty(mapArray[5]) && mapArray[5] != "0")
                            {
                                this.tbxGoodsCode6.IsEnabled = true;
                                this.tbxPickCount6.Text = mapArray[5];
                                this.btnIncrease6.IsEnabled = true;
                                this.btnReduce6.IsEnabled = true;

                                if (!focused)
                                {
                                    this.tbxGoodsCode6.Focus();
                                    focused = true;
                                }
                            }
                        }
                        break;

                        #endregion

                    case "tbxGoodsCode1":
                    case "tbxGoodsCode2":
                    case "tbxGoodsCode3":
                    case "tbxGoodsCode4":
                    case "tbxGoodsCode5":
                    case "tbxGoodsCode6":

                        TextBox pickCountText;

                        if (txt.Name == "tbxGoodsCode1")
                        {
                            pickCountText = this.tbxPickCount1;
                        }
                        else if(txt.Name == "tbxGoodsCode2")
                        {
                            pickCountText = this.tbxPickCount2;
                        }
                        else if (txt.Name == "tbxGoodsCode3")
                        {
                            pickCountText = this.tbxPickCount3;
                        }
                        else if (txt.Name == "tbxGoodsCode4")
                        {
                            pickCountText = this.tbxPickCount4;
                        }
                        else if (txt.Name == "tbxGoodsCode5")
                        {
                            pickCountText = this.tbxPickCount5;
                        }
                        else
                        {
                            pickCountText = this.tbxPickCount6;
                        }

                        var infoData = MainApp._I_BaseService.GetList($@"select PM.PLAN_PROJECT_CODE, PM.PLAN_TO_USER, PM.PLAN_RELATEDBILL,
                                                                                ML.MANAGE_LIST_QUANTITY, GM.GOODS_CODE, GM.GOODS_NAME,
                                                                                PL.GOODS_PROPERTY3, PL.GOODS_PROPERTY4, PM.BACKUP_FILED1,
                                                                                PM.PLAN_CODE, PM.PLAN_DESCRIPTION, PM.PLAN_HEADTEXT ,PM.PLAN_SHOPNO
                                                                         from MANAGE_MAIN MM 
                                                                         inner join MANAGE_LIST ML on MM.MANAGE_ID=ML.MANAGE_ID 
                                                                         inner join PLAN_LIST PL on ML.PLAN_LIST_ID=PL.PLAN_LIST_ID 
                                                                         inner join PLAN_MAIN PM on PL.PLAN_ID=PM.PLAN_ID 
                                                                         inner join GOODS_MAIN GM on GM.GOODS_ID=PL.GOODS_ID 
                                                                         where MM.MANAGE_TYPE_CODE='ManageManualPick' 
                                                                         and MM.STOCK_BARCODE='{this.tbxSourceBox.Text}' 
                                                                         and ML.BOX_BARCODE='{txt.Name.Last()}'");
                        if(infoData == null || infoData.Rows.Count < 1)
                        {
                            MainApp._MessageDialog.ShowResult(false, "没有待拣选的任务");
                            break;
                        }

                        decimal pickCount = 0;
                        if (!decimal.TryParse(pickCountText.Text,out pickCount))
                        {
                            MainApp._MessageDialog.ShowResult(false, $"拣选数量[{pickCountText.Text}]有误");
                            break;
                        }

                        message = MainApp._I_ManageService.ManualPickGoodsFinish(MainApp._USER, this.tbxSourceBox.Text, this.tbxEmptyBox.Text, txt.Name.Last().ToString(), pickCount,this.cbxStationCode.SelectedValue.ToString());
                        MainApp._MessageDialog.ShowResult(string.IsNullOrEmpty(message), message);
                        UpdateControlStatus();

                        //成功则打印
                        if(string.IsNullOrEmpty(message))
                        {
                            PrintPlanInfo(infoData);
                        }

                        //检查唯一码是否完成拣选
                        var planCheck = MainApp._I_BaseService.GetList($@"select * from PLAN_MAIN PM inner join PLAN_LIST PL on PM.PLAN_ID=PL.PLAN_ID 
                                                                          where PLAN_TYPE_CODE='PlanPick' 
                                                                          and PLAN_GROUP='{this.cbxUniqueCode.SelectedValue.ToString()}' 
                                                                          and PLAN_LIST_QUANTITY > PLAN_LIST_PICKED_QUANTITY");
                        if (planCheck == null || planCheck.Rows.Count < 1)
                        {
                            statusCode = 0;
                            LoadPlanUniqueCode();
                            UpdateControlStatus();
                        }
                        break;

                    default:
                        break;
                }
            }
        }

        private void LoadPlanUniqueCode()
        {
            try
            {
                var threhold = "1";

                MainApp._SYS_PARAMETER.TryGetValue("ManualPickPlanCount", out threhold);

                var targetUnique = MainApp._I_BaseService.GetList($@"select * from (select PLAN_GROUP,min(BACKUP_FILED1) BACKUP_FILED1,count(0) GOODS_COUNT 
                                                                                    from PLAN_MAIN A inner join PLAN_LIST B on A.PLAN_ID=B.PLAN_ID 
                                                                                    where A.PLAN_TYPE_CODE ='PlanPick' and A.PLAN_STATUS='Waiting' 
                                                                                    group by PLAN_GROUP) 
                                                                     where GOODS_COUNT <={threhold} order by BACKUP_FILED1");
                if (targetUnique == null)
                {
                    return;
                }

                this.cbxUniqueCode.ItemsSource = targetUnique.AsEnumerable().Select(t => t["PLAN_GROUP"].ToString());
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        private void UpdateControlStatus()
        {
            if (statusCode == 0)
            {
                //this.cbxStationCode.IsEnabled = true;
                this.cbxUniqueCode.IsEnabled = true;
                this.cbxUniqueCode.SelectedValue = null;
                this.btnReloadPlan.IsEnabled = true;
                this.tbxEmptyBox.IsEnabled = true;
                this.tbxEmptyBox.Text = string.Empty;

                this.tbxSourceBox.IsEnabled = false;
                this.tbxSourceBox.Text = string.Empty;

                this.tbxGoodsCode1.IsEnabled = false;
                this.btnIncrease1.IsEnabled = false;
                this.btnReduce1.IsEnabled = false;
                //this.tbxPickCount1.IsEnabled = false;
                this.tbxGoodsCode1.Text = string.Empty;
                this.tbxPickCount1.Text = string.Empty;
                
                this.tbxGoodsCode2.IsEnabled = false;
                this.btnIncrease2.IsEnabled = false;
                this.btnReduce2.IsEnabled = false;
                //this.tbxPickCount2.IsEnabled = false;
                this.tbxGoodsCode2.Text = string.Empty;
                this.tbxPickCount2.Text = string.Empty;
                
                this.tbxGoodsCode3.IsEnabled = false;
                this.btnIncrease3.IsEnabled = false;
                this.btnReduce3.IsEnabled = false;
                //this.tbxPickCount3.IsEnabled = false;
                this.tbxGoodsCode3.Text = string.Empty;
                this.tbxPickCount3.Text = string.Empty;

                this.tbxGoodsCode4.IsEnabled = false;
                this.btnIncrease4.IsEnabled = false;
                this.btnReduce4.IsEnabled = false;
                //this.tbxPickCount4.IsEnabled = false;
                this.tbxGoodsCode4.Text = string.Empty;
                this.tbxPickCount4.Text = string.Empty;
                
                this.tbxGoodsCode5.IsEnabled = false;
                this.btnIncrease5.IsEnabled = false;
                this.btnReduce5.IsEnabled = false;
                //this.tbxPickCount5.IsEnabled = false;
                this.tbxGoodsCode5.Text = string.Empty;
                this.tbxPickCount5.Text = string.Empty;
                
                this.tbxGoodsCode6.IsEnabled = false;
                this.btnIncrease6.IsEnabled = false;
                this.btnReduce6.IsEnabled = false;
                //this.tbxPickCount6.IsEnabled = false;
                this.tbxGoodsCode6.Text = string.Empty;
                this.tbxPickCount6.Text = string.Empty;
            }
            else if (statusCode == 1)
            {
                //this.cbxStationCode.IsEnabled = false;
                this.cbxUniqueCode.IsEnabled = false;
                this.btnReloadPlan.IsEnabled = false;
                this.tbxEmptyBox.IsEnabled = false;

                this.tbxSourceBox.IsEnabled = true;
                this.tbxSourceBox.Text = string.Empty;

                this.tbxGoodsCode1.IsEnabled = false;
                this.btnIncrease1.IsEnabled = false;
                this.btnReduce1.IsEnabled = false;
                //this.tbxPickCount1.IsEnabled = false;
                this.tbxGoodsCode1.Text = string.Empty;
                this.tbxPickCount1.Text = string.Empty;

                this.tbxGoodsCode2.IsEnabled = false;
                this.btnIncrease2.IsEnabled = false;
                this.btnReduce2.IsEnabled = false;
                //this.tbxPickCount2.IsEnabled = false;
                this.tbxGoodsCode2.Text = string.Empty;
                this.tbxPickCount2.Text = string.Empty;

                this.tbxGoodsCode3.IsEnabled = false;
                this.btnIncrease3.IsEnabled = false;
                this.btnReduce3.IsEnabled = false;
                //this.tbxPickCount3.IsEnabled = false;
                this.tbxGoodsCode3.Text = string.Empty;
                this.tbxPickCount3.Text = string.Empty;

                this.tbxGoodsCode4.IsEnabled = false;
                this.btnIncrease4.IsEnabled = false;
                this.btnReduce4.IsEnabled = false;
                //this.tbxPickCount4.IsEnabled = false;
                this.tbxGoodsCode4.Text = string.Empty;
                this.tbxPickCount4.Text = string.Empty;

                this.tbxGoodsCode5.IsEnabled = false;
                this.btnIncrease5.IsEnabled = false;
                this.btnReduce5.IsEnabled = false;
                //this.tbxPickCount5.IsEnabled = false;
                this.tbxGoodsCode5.Text = string.Empty;
                this.tbxPickCount5.Text = string.Empty;

                this.tbxGoodsCode6.IsEnabled = false;
                this.btnIncrease6.IsEnabled = false;
                this.btnReduce6.IsEnabled = false;
                //this.tbxPickCount6.IsEnabled = false;
                this.tbxGoodsCode6.Text = string.Empty;
                this.tbxPickCount6.Text = string.Empty;

            }
        }


        private void InitPrint()
        {
            int width_p = 100;//单位是mm
            int height_p = 50;//单位是mm
            int margin_lr = 2;//左右边距
            int margin_tb = 2;//上下边距

            PrinterSettings printSetting = new PrinterSettings();
            printSetting.PrintRange = PrintRange.AllPages;
            int width_in = MM2Inch(width_p);
            int height_in = MM2Inch(height_p);
            PageSettings pageSetting = new PageSettings(printSetting);
            pageSetting.PaperSize = new PaperSize("customer", width_in, height_in);

            int margin_lr_in = MM2Inch(margin_lr);
            int margin_tb_in = MM2Inch(margin_tb);
            pageSetting.Margins = new Margins(margin_lr_in, margin_lr_in, margin_tb_in, margin_tb_in);

            printDocumentForPick = new PrintDocument();
            printDocumentForPick.DefaultPageSettings = pageSetting;
            printDocumentForPick.PrintPage += PrintDocumentForPick_PrintPage;
        }

        private void PrintDocumentForPick_PrintPage(object sender, PrintPageEventArgs e)
        {
            Graphics g = e.Graphics;
            g.PageScale = 1;
            g.PageUnit = GraphicsUnit.Millimeter;//单位
            //先画一个矩形
            System.Drawing.Pen lineColor = new System.Drawing.Pen(System.Drawing.Color.Black, 0.2f);

            printHelperForPick.Print(g);
        }

        private void PrintPlanInfo(DataTable infoDt)
        {
            foreach (DataRow item in infoDt.Rows)
            {
                printHelperForPick = new PrinterHelper();
                List<PrintInfo> lstPrintInfos = new List<PrintInfo>();

                //项目号 第1行
                PrintInfo p1 = new PrintInfo();
                p1.PrtType = PrintType.Text;
                p1.PrtColor = Color.Black;
                p1.Content = string.Format("项目号:{0}", item["PLAN_PROJECT_CODE"].ToString());
                p1.Size = 8;
                p1.FontStyle = System.Drawing.FontStyle.Bold;
                p1.Start = new System.Drawing.Point(5, 1);
                lstPrintInfos.Add(p1);

                //集货号 第1行
                PrintInfo p2 = new PrintInfo();
                p2.PrtType = PrintType.Text;
                p2.PrtColor = Color.Black;
                p2.Content = string.Format("集货号:{0}", item["PLAN_TO_USER"].ToString());
                p2.Size = 10;
                p2.FontStyle = System.Drawing.FontStyle.Bold;
                p2.Start = new System.Drawing.Point(70, 1);
                lstPrintInfos.Add(p2);

                //派工单 第2行
                PrintInfo p3 = new PrintInfo();
                p3.PrtType = PrintType.Text;
                p3.PrtColor = Color.Black;
                p3.Content = string.Format("派工单:{0}", item["PLAN_RELATEDBILL"].ToString());
                p3.Size = 8;
                p3.FontStyle = System.Drawing.FontStyle.Bold;
                p3.Start = new System.Drawing.Point(5, 5);
                lstPrintInfos.Add(p3);

                //物料号 第3行
                PrintInfo p4 = new PrintInfo();
                p4.PrtType = PrintType.Text;
                p4.PrtColor = Color.Black;
                p4.Content = string.Format("物料号:{0}", item["GOODS_CODE"].ToString());
                p4.Size = 8;
                p4.FontStyle = System.Drawing.FontStyle.Bold;
                p4.Start = new System.Drawing.Point(5, 9);
                lstPrintInfos.Add(p4);

                //数量 第3行
                PrintInfo p10 = new PrintInfo();
                p10.PrtType = PrintType.Text;
                p10.PrtColor = Color.Black;
                p10.Content = string.Format("数量:{0}", item["MANAGE_LIST_QUANTITY"].ToString());
                p10.Size = 10;
                p10.FontStyle = System.Drawing.FontStyle.Bold;
                p10.Start = new System.Drawing.Point(50, 9);
                lstPrintInfos.Add(p10);

                //物料号 一维码
                PrintInfo p16 = new PrintInfo();
                p16.PrtType = PrintType.BarcodeImage;
                p16.BarcodeSettings = new BarcodeSetting
                {
                    BarcodeType = BarcodeLib.TYPE.CODE128,
                    BarcodeEncodeData = item["GOODS_CODE"].ToString(),
                    BarcodeHeight = 15,
                    BarcodeWidth = 80,
                    BarcodeBackColor = Color.White,
                    BarcodeForeColor = Color.Black,
                    BarcodeIncludeLabel = false,
                    BarcodeLabelPosition = BarcodeLib.LabelPositions.BOTTOMCENTER,
                    BarcodeMinBarWidth = 0.1,
                    BarcodeLabelFont = new Font("Times New Roman", 10, System.Drawing.FontStyle.Bold),
                    BarcodePrintDpi = 300,
                    BarcodeLabelAlignmentPosition = BarcodeLib.AlignmentPositions.CENTER,
                    BarcodeAutoBarWidth = true,
                };

                p16.Start = new System.Drawing.Point(10, 13);
                lstPrintInfos.Add(p16);

                //描述 第4行
                PrintInfo p5 = new PrintInfo();
                p5.PrtType = PrintType.Text;
                p5.PrtColor = Color.Black;
                p5.Content = string.Format("描述:{0}", item["GOODS_NAME"].ToString());
                p5.Size = 7;
                p5.FontStyle = System.Drawing.FontStyle.Bold;
                p5.Start = new System.Drawing.Point(5, 17);
                lstPrintInfos.Add(p5);

                //车间 第5行
                PrintInfo p6 = new PrintInfo();
                p6.PrtType = PrintType.Text;
                p6.PrtColor = Color.Black;
                p6.Content = string.Format("车间:{0}", item["PLAN_SHOPNO"].ToString());
                p6.Size = 8;
                p6.FontStyle = System.Drawing.FontStyle.Bold;
                p6.Start = new System.Drawing.Point(5, 21);
                lstPrintInfos.Add(p6);

                //工位 第5行
                PrintInfo p7 = new PrintInfo();
                p7.PrtType = PrintType.Text;
                p7.PrtColor = Color.Black;
                p7.Content = string.Format("工位:{0}", item["GOODS_PROPERTY3"].ToString());
                p7.Size = 8;
                p7.FontStyle = System.Drawing.FontStyle.Bold;
                p7.Start = new System.Drawing.Point(30, 21);
                lstPrintInfos.Add(p7);

                //工序序号 第6行
                PrintInfo p9 = new PrintInfo();
                p9.PrtType = PrintType.Text;
                p9.PrtColor = Color.Black;
                p9.Content = string.Format("工序序号:{0}", item["GOODS_PROPERTY4"].ToString());
                p9.Size = 8;
                p9.FontStyle = System.Drawing.FontStyle.Bold;
                p9.Start = new System.Drawing.Point(5, 25);
                lstPrintInfos.Add(p9);

                //需求日期 第7行
                PrintInfo p8 = new PrintInfo();
                p8.PrtType = PrintType.Text;
                p8.PrtColor = Color.Black;
                p8.Content = string.Format("需求日期:{0}", item["BACKUP_FILED1"].ToString());
                p8.Size = 8;
                p8.FontStyle = System.Drawing.FontStyle.Bold;
                p8.Start = new System.Drawing.Point(5, 29);
                lstPrintInfos.Add(p8);

                //批次 第7行
                //PrintInfo p9 = new PrintInfo();
                //p9.PrtType = PrintType.Text;
                //p9.PrtColor = Color.Black;
                //p9.Content = string.Format("批次:{0}", "");
                //p9.Size = 8;
                //p9.FontStyle = System.Drawing.FontStyle.Bold;
                //p9.Start = new System.Drawing.Point(5, 29);
                //lstPrintInfos.Add(p9);

                //数量 第7行
                //PrintInfo p10 = new PrintInfo();
                //p10.PrtType = PrintType.Text;
                //p10.PrtColor = Color.Black;
                //p10.Content = string.Format("数量:{0}", this.goods_quantity);
                //p10.Size = 8;
                //p10.FontStyle = System.Drawing.FontStyle.Bold;
                //p10.Start = new System.Drawing.Point(45, 29);
                //lstPrintInfos.Add(p10);

                //wbs号 第8行
                PrintInfo p11 = new PrintInfo();
                p11.PrtType = PrintType.Text;
                p11.PrtColor = Color.Black;
                p11.Content = string.Format("WBS号/分组:{0}", item["PLAN_CODE"].ToString());
                p11.Size = 8;
                p11.FontStyle = System.Drawing.FontStyle.Bold;
                p11.Start = new System.Drawing.Point(5, 33);
                lstPrintInfos.Add(p11);

                //装配体名称 第9行
                PrintInfo p12 = new PrintInfo();
                p12.PrtType = PrintType.Text;
                p12.PrtColor = Color.Black;
                p12.Content = string.Format("装配体名称:{0}", item["PLAN_DESCRIPTION"].ToString());
                p12.Size = 8;
                p12.FontStyle = System.Drawing.FontStyle.Bold;
                p12.Start = new System.Drawing.Point(5, 37);
                lstPrintInfos.Add(p12);

                //抬头文本 第10行
                PrintInfo p13 = new PrintInfo();
                p13.PrtType = PrintType.Text;
                p13.PrtColor = Color.Black;
                p13.Content = string.Format("{0}", item["PLAN_HEADTEXT"].ToString());
                p13.Size = 7;
                p13.FontStyle = System.Drawing.FontStyle.Bold;
                p13.Start = new System.Drawing.Point(5, 41);
                lstPrintInfos.Add(p13);

                //打印信息 第11行
                PrintInfo p14 = new PrintInfo();
                p14.PrtType = PrintType.Text;
                p14.PrtColor = Color.Black;
                p14.Content = "由" + MainApp._USER.USER_NAME + "|" + MainApp._USER.USER_CODE + "于紧急口" + this.cbxStationCode.SelectedValue.ToString() + "拣选 " + DateTime.Now.ToString("yyyy-MM-dd HH:mm:ss");
                p14.Size = 7;
                p14.FontStyle = System.Drawing.FontStyle.Bold;
                p14.Start = new System.Drawing.Point(10, 44);
                lstPrintInfos.Add(p14);

                PrintInfo p15 = new PrintInfo();
                p15.PrtType = PrintType.QRCode;
                p15.QrCodeSettings = new QrCodeSetting
                {
                    Data = string.IsNullOrEmpty(item["PLAN_RELATEDBILL"].ToString()) ?
                    string.Format("{0}", item["GOODS_CODE"].ToString()) :
                    string.Format("{0}*{1}", item["PLAN_RELATEDBILL"].ToString(), item["GOODS_CODE"].ToString()),
                    x = 65f,
                    y = 9f,
                    width = 30f,
                    height = 30f,
                    PixelsPerModuleToUse = 5,
                };

                lstPrintInfos.Add(p15);

                printHelperForPick.PrintInfos = lstPrintInfos;

                printDocumentForPick.Print();
            }

                    
        }


        private int MM2Inch(int mm)
        {
            return (int)(mm * 100.0f / 25.4f);
        }
    }
}
