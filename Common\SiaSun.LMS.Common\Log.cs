﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.IO;
using System.Threading;

namespace SiaSun.LMS.Common
{
    public class Log
    {
        static Mutex mutLog = new Mutex();

        //保存日志
        public void SaveLog(string message)
        {
            //获得所有文本
            string strLog = message;

            try
            {
                mutLog.WaitOne();

                //Dictionary Name
                string strDicName = Path.Combine(AppDomain.CurrentDomain.BaseDirectory + @"Log\", DateTime.Now.ToString("yyyy-MM-dd"));
                //Whether Exists
                DirectoryInfo dicInfo = new DirectoryInfo(strDicName);
                if (!dicInfo.Exists)
                {
                    dicInfo.Create();
                }

                //File Count
                int intLength = dicInfo.GetFiles("*.log").Length;

                FileInfo fileInfo = null;
                if (intLength == 0)
                {
                    fileInfo = new FileInfo(Path.Combine(strDicName, intLength.ToString() + ".log"));
                    using (FileStream fileStream = fileInfo.Create())
                    {
                        fileStream.Close();
                        fileStream.Dispose();
                    }
                }
                else
                {
                    //Get File
                    fileInfo = dicInfo.GetFiles("*.log")[intLength - 1];
                    //Check Size
                    if (fileInfo.Length > 100000)
                    {
                        fileInfo = new FileInfo(Path.Combine(strDicName, intLength.ToString() + ".log"));
                        using (FileStream fileStream = fileInfo.Create())
                        {
                            fileStream.Close();
                            fileStream.Dispose();
                        }
                    }
                }

                //Write Text File
                using (StreamWriter writer = new StreamWriter(fileInfo.FullName, true))
                {
                    writer.WriteLine(strLog);
                    writer.Close();
                    writer.Dispose();
                }
            }
            finally
            {
                mutLog.ReleaseMutex();
            }
        }

        //清除AGV通讯记录,保留一个月的记录
        public void ClearAGVLog()
        {
            string strDicName = AppDomain.CurrentDomain.BaseDirectory + @"Log\";
            try
            {
                foreach (string strDic in Directory.GetDirectories(strDicName))
                {
                    DateTime dateNow = Convert.ToDateTime(DateTime.Now.ToString("yyyy-MM-dd"));
                    DirectoryInfo dicInfo = new DirectoryInfo(strDic);
                    string dicName = dicInfo.Name;
                    if (Convert.ToDateTime(dicInfo.Name).AddDays(30) < dateNow)
                    {
                        dicInfo.Delete(true);
                    }
                }
            }
            catch (Exception ex)
            {
                string message = ex.Message;
            }
        }
    }
}
