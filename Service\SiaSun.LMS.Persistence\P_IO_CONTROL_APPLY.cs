﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// IO_CONTROL_APPLY
	/// </summary>
	public class P_IO_CONTROL_APPLY : P_Base_House
	{
		public P_IO_CONTROL_APPLY ()
		{
			//
			// TODO: 此处添加IO_CONTROL_APPLY的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<IO_CONTROL_APPLY> GetList(int APPLY_TASK_STATUS)
		{
            return ExecuteQueryForList<IO_CONTROL_APPLY>("IO_CONTROL_APPLY_SELECT_BY_APPLY_TASK_STATUS", APPLY_TASK_STATUS);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(IO_CONTROL_APPLY io_control_apply)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("IO_CONTROL_APPLY");
                io_control_apply.CONTROL_APPLY_ID = id;
            }

            return ExecuteInsert("IO_CONTROL_APPLY_INSERT",io_control_apply);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(IO_CONTROL_APPLY io_control_apply)
		{
			return ExecuteUpdate("IO_CONTROL_APPLY_UPDATE",io_control_apply);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public IO_CONTROL_APPLY GetModel(System.Int32 CONTROL_APPLY_ID)
		{
			return ExecuteQueryForObject<IO_CONTROL_APPLY>("IO_CONTROL_APPLY_SELECT_BY_ID",CONTROL_APPLY_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 CONTROL_APPLY_ID)
		{
			return ExecuteDelete("IO_CONTROL_APPLY_DELETE",CONTROL_APPLY_ID);
		}
		

	}
}
