﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Data;
namespace SiaSun.LMS.WPFClient.WH
{
    /// <summary>
    /// WAREHOUSE_AREA_MANAGE.xaml 的交互逻辑
    /// </summary>
    public partial class WAREHOUSE_AREA_MANAGE : AvalonDock.DocumentContent
    {
        public WAREHOUSE_AREA_MANAGE()
        {
            InitializeComponent();
        }

        //加载窗体
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            this.InitWindow();
        }

        /// <summary>
        /// 加载仓库列表
        /// </summary>
        private void InitWindow()
        {
            this.tvwWareHouse.tvwList.Items.Clear();

            //添加根节点
            TreeViewItem itemParent = this.tvwWareHouse.U_AddTreeViewItem(null, "仓库设置", 0);

            //加载仓库
            this.tvwWareHouse.U_Header = "仓库-库区";
            this.tvwWareHouse.U_AllowCheck = false;
            this.tvwWareHouse.U_LoadTreeViewItems(itemParent, "WH_WAREHOUSE", "WAREHOUSE_NAME", "WAREHOUSE_ID", "", "WAREHOUSE_ORDER");

            //加载库区
            //foreach (TreeViewItem item in itemParent.Items)
            //{
            //    foreach (Model.WH_AREA mWH_AREA in MainApp._I_CellService.AREA_GetList_WAREHOUSE(Convert.ToInt32(item.Tag.ToString())))
            //    {
            //        this.tvwWareHouse.U_AddTreeViewItem(item,mWH_AREA.AREA_NAME,mWH_AREA.AREA_ID);
            //    }
            //    item.IsExpanded = true;
            //}
            itemParent.IsExpanded = true;
        }



        //选择仓库或库区，显示相应项目
        private void tvwWareHouse_U_ItemSelectedChanged(TreeViewItem itemSelected)
        {
            if (itemSelected == null)
                return;

            //显示初始化按钮
            this.panelButton.Visibility = System.Windows.Visibility.Collapsed;
            //显示标题
            this.grpChild.Header = string.Format(this.grpChild.Tag.ToString(),itemSelected.Header.ToString());
            Dictionary<string, string> dicDefaultValues = new Dictionary<string, string>();
            
            //判断节点类型
            if (itemSelected.Parent is TreeView)
            {
                //显示所有仓库
                dicDefaultValues.Add("WAREHOUSE_FLAG", "1)");
                this.DataGridBind("WH_WAREHOUSE", "WH_WAREHOUSE", "WAREHOUSE_ID", dicDefaultValues, string.Empty);
            }
            else
            {
                TreeViewItem itemParent = itemSelected.Parent as TreeViewItem;
                if (itemParent != null)
                {
                    //判断是否仓库节点，则显示库区
                    if (itemParent.Tag.ToString() == "0")
                    {
                        //显示库区设置
                        dicDefaultValues.Add("WAREHOUSE_ID", itemSelected.Tag.ToString());
                        dicDefaultValues.Add("AREA_FLAG", "1");
                        this.DataGridBind("WH_AREA", "WH_AREA", "AREA_ORDER", dicDefaultValues, string.Format("WAREHOUSE_ID={0}", itemSelected.Tag.ToString()));

                        //显示初始化按钮
                        this.panelButton.Visibility = System.Windows.Visibility.Visible;
                    }
                    else
                    {
                        //显示所有货位
                        dicDefaultValues.Add("AREA_ID", itemSelected.Tag.ToString());
                        dicDefaultValues.Add("AREA_FLAG", "1");
                        this.DataGridBind("WH_CELL", "WH_CELL", "CELL_CODE", dicDefaultValues, string.Format("AREA_ID={0}", itemSelected.Tag.ToString()));
                    }
                }
            }
        }

        /// <summary>
        /// 绑定所有仓库
        /// </summary>
        private void DataGridBind(string TableName,string XmlTableName,string OrderField,IDictionary<string,string> DefaultValuePairs,string Where)
        {
            try
            {
                this.gridChild.U_Clear();
                this.gridChild.U_WindowName = this.GetType().Name;
                this.gridChild.U_TableName = TableName;
                this.gridChild.U_XmlTableName = XmlTableName;
                this.gridChild.U_OrderField = OrderField;
                this.gridChild.U_Where = Where;
                //设置默认值
                foreach (KeyValuePair<string, string> valuePair in DefaultValuePairs)
                {
                    this.gridChild.U_DefaultRowValues.Add(valuePair.Key, valuePair.Value);
                }

                this.gridChild.U_AllowChecked = false;
                this.gridChild.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //点击按钮
        private void WrapPanel_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                TreeViewItem itemSelected = this.tvwWareHouse.tvwList.SelectedItem as TreeViewItem;
                if (itemSelected.Tag == null) 
                    return;

                if (MainApp._MessageDialog.ShowDialog("ConfirmExecute",new object[]{btn.Content.ToString()}) == Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    switch (btn.Name)
                    {
                        case "btnCreateCell":
                            //注释防止误操作
                            this.CreateCell(itemSelected);
                            break;
                        case "btnInitSystem":
                            //注释防止误操作
                            //this.InitSystem();
                            break;
                    }
                    MainApp._MessageDialog.ShowResult(true, null);
                }
            }
        }

        //根据货位设置生成货位
        private void CreateCell(TreeViewItem itemWarehouse)
        {
            if (itemWarehouse == null)
                return;

            int intWareHouseID = Convert.ToInt32(itemWarehouse.Tag);

            //后台线程执行调用
            System.ComponentModel.BackgroundWorker bkWorker = new System.ComponentModel.BackgroundWorker();
            bkWorker.DoWork += new System.ComponentModel.DoWorkEventHandler(bkWorker_DoWork);
            bkWorker.RunWorkerAsync(intWareHouseID);
        }

        /// <summary>
        /// 后台调用
        /// </summary>
        void bkWorker_DoWork(object sender, System.ComponentModel.DoWorkEventArgs e)
        {
            try
            {
                int intWareHouseID = Convert.ToInt32(e.Argument);

                //检查是否存在货位设置
                //if (!MainApp._I_BaseService.Exist("WH_DESCRIPTION", string.Format("AREA_ID IN(SELECT AREA_ID FROM WH_AREA WHERE WAREHOUSE_ID={0})", intWareHouseID)))
                //{
                //    MainApp._MessageDialog.ShowException("请在货位定义中设置货位的描述信息！");
                //    return;
                //}
                MainApp._I_CellService.CellInit();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }

        //根据货位设置生成Line单元
        private void InitSystem()
        {
            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;
                MainApp._I_BaseService.InitSystem();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }               

        /// <summary>
        /// 上下文菜单
        /// </summary>
        private void ContextMenu_Click(object sender, RoutedEventArgs e)
        {
            MenuItem item = e.OriginalSource as MenuItem;
            if (item != null)
            {
                switch (item.Name)
                {
                    case "menuItemRefresh":
                        //重新加载
                        this.InitWindow();
                        break;
                    case "menuItemInitCell":
                        if (MainApp._MessageDialog.ShowDialog("ConfirmExecute", new object[] { item.Header.ToString() }) == Sid.Windows.Controls.TaskDialogResult.Ok)
                        {
                            MainApp._I_CellService.CellInit();
                        }
                        break;
                }
            }
        }
    }
}
