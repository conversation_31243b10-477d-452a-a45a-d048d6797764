﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 非立库传输
    /// </summary>
    public class NoneMiniloadTrans : InterfaceBase
    {
        /// <summary>
        /// 入参
        /// </summary>
        public class InputParaMain
        {
            private string _palletNo = string.Empty;        //托盘号
            private string _fromPosition = string.Empty;    //起点位置
            private string _toPosition = string.Empty;      //终点位置
            private string _uniqueCode = string.Empty;      //唯一码
            private string _interfaceType = string.Empty;   //接口类型
            private string _interfaceSource = string.Empty; //接口来源
            private string _relevantCode = string.Empty;     //相关单号
            private string _taskId = string.Empty;         //扩展字段2
            private string _stepNo = string.Empty;         //扩展字段3
            private string _actionType = string.Empty;         //扩展字段4
            private string _finalPosition = string.Empty;         //扩展字段5

            private string _taskCode = string.Empty;         //rcs任务编码 2021-11-07 09:46:45
            private string _taskType = string.Empty;         //=RCS时先不下达 2021-11-07 09:46:48

            /// <summary>
            /// 托盘号
            /// </summary>
            public string palletNo
            {
                get { return _palletNo; }
                set { _palletNo = value; }
            }
            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 终点位置
            /// </summary>
            public string toPosition
            {
                get { return _toPosition; }
                set { _toPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 相关单号
            /// </summary>
            public string relevantCode
            {
                get { return _relevantCode; }
                set { _relevantCode = value; }
            }

            /// <summary>
            /// 任务标识
            /// </summary>
            public string taskId
            {
                get { return _taskId; }
                set { _taskId = value; }
            }

            /// <summary>
            /// 步号
            /// </summary>
            public string stepNo
            {
                get { return _stepNo; }
                set { _stepNo = value; }
            }

            /// <summary>
            /// 动作
            /// </summary>
            public string actionType
            {
                get { return _actionType; }
                set { _actionType = value; }
            }

            /// <summary>
            /// 最终位置
            /// </summary>
            public string finalPosition
            {
                get { return _finalPosition; }
                set { _finalPosition = value; }
            }

            /// <summary>
            /// rcs任务编码 2021-11-07 09:46:45
            /// </summary>
            public string taskCode
            {
                get { return _taskCode; }
                set { _taskCode = value; }
            }

            /// <summary>
            /// =RCS时先不下达 2021-11-07 09:46:48
            /// </summary>
            public string taskType
            {
                get { return _taskType; }
                set { _taskType = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("NoneMiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:非立库任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                //唯一码、起点位置+终点位置的组合 不能为空
                if (string.IsNullOrEmpty(taskInfo.uniqueCode) || string.IsNullOrEmpty(taskInfo.palletNo) ||
                    string.IsNullOrEmpty(taskInfo.fromPosition+ taskInfo.toPosition))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:入参存在空值");
                    return bResult;
                }
                //如果起始位置或者终点位置有空值 则要求相关单号、任务号、步号、动作要必填
                if ((string.IsNullOrEmpty(taskInfo.fromPosition) || string.IsNullOrEmpty(taskInfo.toPosition))&&
                    (string.IsNullOrEmpty(taskInfo.relevantCode) || string.IsNullOrEmpty(taskInfo.taskId) || string.IsNullOrEmpty(taskInfo.stepNo) || string.IsNullOrEmpty(taskInfo.actionType)))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:入参缺项");
                    return bResult;
                }

                //校验taskID和ActionType是否合法
                int tempInt = 0;
                if(!string.IsNullOrEmpty(taskInfo.taskId) && !int.TryParse(taskInfo.taskId ,out tempInt))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:拣选任务单号不合法 传入值-{0}",taskInfo.taskId);
                    return bResult;
                }
                if (!string.IsNullOrEmpty(taskInfo.toPosition) && !int.TryParse(taskInfo.toPosition,out tempInt))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:终点位置不合法 传入值-{0}", taskInfo.taskId);
                    return bResult;
                }
                if (!string.IsNullOrEmpty(taskInfo.fromPosition) && !int.TryParse(taskInfo.fromPosition, out tempInt))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:起点位置不合法 传入值-{0}", taskInfo.taskId);
                    return bResult;
                }

                if (!string.IsNullOrEmpty(taskInfo.actionType) && !new string[] { "1", "2", "3", "4" }.Contains(taskInfo.actionType))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:拣选动作类型不合法 传入值-{0}", taskInfo.actionType);
                    return bResult;
                }

                if(!string.IsNullOrEmpty(taskInfo.taskType) && taskInfo.taskType.ToLower() == "rcs" && string.IsNullOrEmpty(taskInfo.taskCode))
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:RCS任务taskCode不能传空");
                    return bResult;
                }

                //判断是否要生成Control
                bool isAutoSendControl = true;
                bool isSendControlIndependent = false;
                string endCellCode = string.Empty;

                //校验没有条码扫描器的站台当前是否存在等待执行的任务
                if (!string.IsNullOrEmpty(taskInfo.fromPosition))
                {
                    Model.WH_CELL mWH_CELL_START = this._P_WH_CELL.GetModelByDeviceCode(taskInfo.fromPosition);
                    if(mWH_CELL_START != null && mWH_CELL_START.CELL_PROPERTY=="NoScaner" 
                        && (string.IsNullOrEmpty(taskInfo.taskType) || taskInfo.taskType.ToLower() != "rcs"))
                    {
                        bResult = this._S_SystemService.GetSysParameter("MoveToFloor5EndStation", out endCellCode);
                        Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(endCellCode);
                        if (!bResult || mWH_CELL_END == null)
                        {
                            bResult = false;
                            outJson = string.Format("NoneMiniloadTrans.NotifyMethod:起点位置没有固定条码扫描器且系统未找到货位申请站台_起点位置-{0}", taskInfo.fromPosition);
                            return bResult;
                        }

                        DataTable dtWaittingTask = this.GetList(string.Format("select 0 from IO_CONTROL where START_DEVICE_CODE ='{0}' and CONTROL_STATUS in ('0','7') ", mWH_CELL_START.CELL_CODE));
                        if (dtWaittingTask != null && dtWaittingTask.Rows.Count > 0)
                        {
                            bResult = false;
                            outJson = string.Format("NoneMiniloadTrans.NotifyMethod:当前输送起点位置存在等待执行任务，请稍后再试_起点位置-{0}", taskInfo.fromPosition);
                            return bResult;
                        }

                        //特殊情况，对于非立库输送任务并且是从前方没有扫描头的设备起始到达标准件出库站台/结束的任务
                        string standardGoodsOutStation = string.Empty;
                        if(!this._S_SystemService.GetSysParameter("StandardGoodsOutStation", out standardGoodsOutStation))
                        {
                            bResult = false;
                            outJson = string.Format("NoneMiniloadTrans.NotifyMethod:未能获取标准件出库站台_Key[StandardGoodsInStation]");
                            return bResult;
                        }
                        if (taskInfo.toPosition == standardGoodsOutStation)
                        {
                            isAutoSendControl = false;
                            isSendControlIndependent = true;
                        }
                    }
                    else if (mWH_CELL_START != null && mWH_CELL_START.CELL_PROPERTY == "Scaner")
                    {
                        isAutoSendControl = false;
                        isSendControlIndependent = false;
                    }
                }

                //判断是RCS任务则先不下达 2021-11-07 11:07:02
                if (!string.IsNullOrEmpty(taskInfo.taskType) && taskInfo.taskType.ToLower() == "rcs")
                {
                    isAutoSendControl = false;
                    isSendControlIndependent = false;
                }

                //Model.WH_CELL mWH_CELL_END = this._P_WH_CELL.GetModel(taskInfo.toPosition);

                Model.MANAGE_MAIN mMANAGE_MAIN = this._P_MANAGE_MAIN.GetModelRelateCode(taskInfo.uniqueCode);
                if (mMANAGE_MAIN != null)
                {
                    bResult = false;
                    outJson = string.Format("NoneMiniloadTrans.NotifyMethod:传入唯一码已经存在任务 传入值_{0}", taskInfo.uniqueCode);
                    return bResult;
                }

                //wdz add 2018-12-18 校验是否存在库存
                if (!string.IsNullOrEmpty(taskInfo.palletNo))
                {
                    Model.STORAGE_MAIN mSTORAGE_MAIN = this._P_STORAGE_MAIN.GetModelStockBarcode(taskInfo.palletNo);
                    if (mSTORAGE_MAIN != null)
                    {
                        bResult = false;
                        outJson = string.Format("NoneMiniloadTrans.NotifyMethod:传入条码已经存在立库区库存_传入值[{0}]", taskInfo.palletNo);
                        return bResult;
                    }
                }

                mMANAGE_MAIN = new Model.MANAGE_MAIN();
                mMANAGE_MAIN.CELL_MODEL = "";
                //mMANAGE_MAIN.END_CELL_ID = mWH_CELL_END == null ? 0 : mWH_CELL_END.CELL_ID;
                mMANAGE_MAIN.MANAGE_BEGIN_TIME = Common.StringUtil.GetDateTime();
                string manageLevel = string.Empty;
                mMANAGE_MAIN.MANAGE_LEVEL = this._S_SystemService.GetSysParameter("DefaultTaskLevel", out manageLevel) ? manageLevel : "0";
                mMANAGE_MAIN.MANAGE_OPERATOR = taskInfo.interfaceSource.ToLower() == "wes" ? "SoapUI" : "WMS";
                mMANAGE_MAIN.MANAGE_RELATE_CODE = taskInfo.uniqueCode;
                mMANAGE_MAIN.MANAGE_SOURCE = taskInfo.interfaceSource.ToLower() == "wes" ? Enum.TASK_SOURCE.WES.ToString() : Enum.TASK_SOURCE.WMS.ToString();
                mMANAGE_MAIN.MANAGE_STATUS = Enum.MANAGE_STATUS.WaitingExecute.ToString();
                mMANAGE_MAIN.MANAGE_TYPE_CODE = Enum.MANAGE_TYPE.ManageTrans.ToString();
                //mMANAGE_MAIN.START_CELL_ID = mWH_CELL_START == null ? 0 : mWH_CELL_START.CELL_ID;
                mMANAGE_MAIN.STOCK_BARCODE = taskInfo.palletNo;
                //保存rcs单号 2021-11-07 11:15:18
                mMANAGE_MAIN.BACKUP_FIELD1 = taskInfo.taskCode;

                Model.MANAGE_LIST mMANAGE_LIST = new Model.MANAGE_LIST();
                mMANAGE_LIST.BOX_BARCODE = taskInfo.palletNo;
                mMANAGE_LIST.GOODS_ID = this._P_GOODS_MAIN.GetModel("transTask").GOODS_ID;
                mMANAGE_LIST.GOODS_PROPERTY1 = taskInfo.relevantCode;
                mMANAGE_LIST.GOODS_PROPERTY2 = taskInfo.taskId;
                mMANAGE_LIST.GOODS_PROPERTY3 = taskInfo.stepNo;
                mMANAGE_LIST.GOODS_PROPERTY4 = taskInfo.actionType;
                mMANAGE_LIST.GOODS_PROPERTY5 = taskInfo.finalPosition;
                mMANAGE_LIST.GOODS_PROPERTY7 = taskInfo.fromPosition;
                mMANAGE_LIST.GOODS_PROPERTY8 = taskInfo.toPosition;

                List<Model.MANAGE_LIST> lsMANAGE_LIST = new List<Model.MANAGE_LIST>();
                lsMANAGE_LIST.Add(mMANAGE_LIST);

                Model.MANAGE_TYPE mMANAGE_TYPE = this._P_MANAGE_TYPE.GetModelManageTypeCode(mMANAGE_MAIN.MANAGE_TYPE_CODE);
                bResult = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCreate", new object[] { mMANAGE_MAIN, lsMANAGE_LIST, true, isAutoSendControl }, out outJson);

                if (bResult && isSendControlIndependent)
                {
                    //下达到四楼货位分配扫描头的不关联管理任务的Control
                    bResult = this._S_ManageService.ControlCreate(1, taskInfo.palletNo, "1", taskInfo.fromPosition, "1", endCellCode, "5", out outJson);
                    if (!bResult)
                    {
                        bool bTemp = true;
                        string strTemp = string.Empty;
                        //下达Control失败后删除任务
                        bTemp = this.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS, "ManageCancel", new object[] { mMANAGE_MAIN.MANAGE_ID }, out strTemp);
                        //log
                        this.CreateSysLog(Enum.LogThread.Control, "System", bResult, string.Format("NoneMiniloadTrans.NotifyMethod():管理任务生成成功_下达非关联管理任务的Control任务失败_{0}_取消管理任务{1} {2}", outJson, bTemp ? "成功" : "失败 ", strTemp));
                    }
                }
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("NoneMiniloadTrans.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if (bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }
    }
}
