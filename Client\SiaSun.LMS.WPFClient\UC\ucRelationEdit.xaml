﻿<UserControl x:Class="SiaSun.LMS.WPFClient.UC.ucRelationEdit"
             xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
             xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
             xmlns:mc="http://schemas.openxmlformats.org/markup-compatibility/2006" 
             xmlns:d="http://schemas.microsoft.com/expression/blend/2008" 
             xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
             mc:Ignorable="d" 
             d:DesignHeight="300" d:DesignWidth="300">
    <Grid>
        <Grid.RowDefinitions>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="*"></RowDefinition>
            <RowDefinition Height="Auto"></RowDefinition>
        </Grid.RowDefinitions>
        
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*"></ColumnDefinition>
            <ColumnDefinition Width="Auto"></ColumnDefinition>
            <ColumnDefinition Width="*"></ColumnDefinition>
        </Grid.ColumnDefinitions>

        <GroupBox Name="grpboxQuery" Header="请选择{0}记录，显示{0}所属关系" Grid.Row="0" Grid.ColumnSpan="3"  Margin="1,5,1,1">
            <uc:ucCommonDataGrid x:Name="gridQuery" Margin="1"></uc:ucCommonDataGrid>
        </GroupBox>
        
        <GroupBox Name="grpboxUnSelected" Header="未选{0}-双击移除选项" Grid.Column="0" Grid.Row="1"  Margin="1,5,1,1">
            <GroupBox.Resources>
                <ContextMenu x:Key="menuUnSelectedOrder"  MenuItem.Click="ContextMenu_Click">
                    <MenuItem Name="menuItemUnSelectedOrder" Header="排序"></MenuItem>
                </ContextMenu>
            </GroupBox.Resources>
            <ListBox Name="lboxUnSelected"  ContextMenu="{StaticResource menuUnSelectedOrder}" SelectionMode="Multiple"> </ListBox>           
        </GroupBox>
        <Border Grid.Row="1" Grid.Column="1">
            <StackPanel VerticalAlignment="Center" Width="40" ButtonBase.Click="StackPanel_Click">
                <Button Name="btnSelect"  Margin="1,10,1,10">＞＞</Button>
                <Button Name="btnUnSelect"  Margin="1,10,1,10" >＜＜</Button>
            </StackPanel>
        </Border>
        <GroupBox Name="grpboxSelected" Header="已选{0}-双击移除选项" Grid.Column="2" Grid.Row="1"  Margin="1,5,1,1">
            <GroupBox.Resources>
                <ContextMenu x:Key="menuSelectedOrder"  MenuItem.Click="ContextMenu_Click">
                    <MenuItem Name="menuItemSelectedOrder" Header="排序"></MenuItem>
                </ContextMenu>
            </GroupBox.Resources>
            <ListBox Name="lboxSelected" ContextMenu="{StaticResource menuSelectedOrder}" SelectionMode="Multiple"> </ListBox>
        </GroupBox>

        <GroupBox Header="操作区" Grid.Row="2" Grid.ColumnSpan="3"  Margin="1">
            <WrapPanel HorizontalAlignment="Center" ButtonBase.Click="WrapPanel_Click">
                <Button Name="btnSave" Content="保存" Width="70" ></Button>
                <Button Name="btnRefresh" Content="刷新"  Width="70" ></Button>
            </WrapPanel>
        </GroupBox>
    </Grid>
</UserControl>
