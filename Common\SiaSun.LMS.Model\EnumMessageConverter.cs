﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Enum
{
    /// <summary>
    /// 消息提示类型
    /// </summary>
    public enum MessageConverter
    {
        /// <summary>
        /// 检查空值
        /// </summary>
        Null,
        /// <summary>
        /// 数据类型是否合法
        /// </summary>
        Type,
        /// <summary>
        /// 检查长度是否合法
        /// </summary>
        Length,
        /// <summary>
        /// 检查重量是否合法
        /// </summary>
        Weight,
        /// <summary>
        /// 是否已经输入数据
        /// </summary>
        Input,
        /// <summary>
        /// 检查数据是否合法
        /// </summary>
        Data,
        /// <summary>
        /// 检查数据是否重复
        /// </summary>
        Exists,

        /// <summary>
        /// 保存时消息
        /// </summary>
        Save,
        /// <summary>
        /// 删除时消息
        /// </summary>
        Delete,
        /// <summary>
        /// 取消时消息
        /// </summary>
        Cancel,
        /// <summary>
        /// 打印时消息
        /// </summary>
        Print,
        /// <summary>
        /// 关闭窗口时消息
        /// </summary>
        Close,
        /// <summary>
        /// 退出系统
        /// </summary>
        Exit,

        /// <summary>
        /// 组盘时未能获得Pallet实例，检查托盘组盘/组箱库存是否存在
        /// </summary>
        CheckStack,
        /// <summary>
        /// 检查托盘库存是否存在
        /// </summary>
        CheckStorage,
        /// <summary>
        /// 检查输送流程是否合法
        /// </summary>
        CheckTechnicsType,
        /// <summary>
        /// 检查库存中的输送位置是否合法
        /// </summary>
        CheckStoragePosition,
        /// <summary>
        /// 检查货位规格是否合法
        /// </summary>
        CheckCellModel,
        /// <summary>
        /// 检查托盘占用百分比
        /// </summary>
        CheckOccupyPercent,
        /// <summary>
        /// 检查托盘条码是否合法
        /// </summary>
        CheckStockBarCode,
       /// <summary>
        /// 检查托盘条码使用区域是否合法
       /// </summary>
        CheckStockBarCodeArea,
        /// <summary>
        /// 检查垛号是否合法
        /// </summary>
        CheckStackNo,
        /// <summary>
        /// 检查仓库是否合法
        /// </summary>
        CheckWareHouse,
        /// <summary>
        /// 检查起始库区或目标库区
        /// </summary>
        CheckArea,
        /// <summary>
        /// 检查巷道是否合法
        /// </summary>
        CheckLaneway,
        /// <summary>
        /// 检查起始库区是否合法
        /// </summary>
        CheckStartArea,
        /// <summary>
        /// 检查目标库区是否合法
        /// </summary>
        CheckEndArea,
        /// <summary>
        /// 检查起始位置或目标位置
        /// </summary>
        CheckPosition,
        /// <summary>
        /// 检查起始位置是否合法
        /// </summary>
        CheckStartPosition,
        /// <summary>
        /// 检查目标位置是否合法
        /// </summary>
        CheckEndPosition,
        /// <summary>
        /// 检查站台条码是否合法
        /// </summary>
        CheckStationCode,
        /// <summary>
        /// 检查站台设备条码是否合法
        /// </summary>
        CheckStationDeviceCode,
        /// <summary>
        /// 检查登录用户是否合法
        /// </summary>
        CheckLoginUser,
        /// <summary>
        /// 千米数不能为空
        /// </summary>
        CheckAssisQuantity,
        /// <summary>
        /// 箱号不能为空
        /// </summary>
        CheckBoxNo,

        /// <summary>
        /// 确认配盘操作
        /// </summary>
        ConfirmAssembly,
        /// <summary>
        /// 确认开始执行
        /// </summary>
        ConfirmExecute,
        /// <summary>
        /// 确认创建输送任务
        /// </summary>
        ConfirmCreateTask,
        /// <summary>
        /// 确认创建计划单
        /// </summary>
        ConfirmCreatePlan,

        /// <summary>
        /// 是否选中记录
        /// </summary>
        SelectCount,
        /// <summary>
        /// 仅且只能选择一个记录
        /// </summary>
        AllowSelectOneOnly,
        /// <summary>
        /// 操作影响行数为0
        /// </summary>
        AffectCount,
        /// <summary>
        /// 操作失败，任务回滚
        /// </summary>
        FailRollBack,
        /// <summary>
        /// 下达任务完成
        /// </summary>
        TaskComplete,
        /// <summary>
        /// 下达任务完成
        /// </summary>
        CheckProType,
        /// <summary>
        /// {0}未输入数据，是否保持默认并继续
        /// </summary>
        IsContinue
    }
}
