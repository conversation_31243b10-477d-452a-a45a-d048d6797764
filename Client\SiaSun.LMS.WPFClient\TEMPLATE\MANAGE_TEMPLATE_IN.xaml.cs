﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Windows;
using System.Windows.Controls;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Input;
using System.Windows.Media;
using System.Windows.Media.Imaging;
using System.Windows.Shapes;
using System.Data;

namespace SiaSun.LMS.WPFClient.TEMPLATE
{
    /// <summary>
    /// 
    /// </summary>
    public partial class MANAGE_TEMPLATE_IN : AvalonDock.DocumentContent
    {
        Model.MANAGE_TYPE mMANAGE_TYPE = null;

        public MANAGE_TEMPLATE_IN( string MANAGE_TYPE_CODE)
        {
            InitializeComponent();
            
            this.mMANAGE_TYPE =(Model.MANAGE_TYPE) MainApp._I_BaseService.GetModel("MANAGE_TYPE_SELECT_BY_MANAGE_TYPE_CODE", MANAGE_TYPE_CODE).RequestObject;
        }

        /// <summary>
        /// 
        /// </summary>
        private void DocumentContent_Loaded(object sender, RoutedEventArgs e)
        {
            try
            {
                this.GOODS_BIND();

                this.cmbGoods.SelectionChanged += new SelectionChangedEventHandler
                    ((cmdGoodssender,cmdGoodse)=>
                        {
                            if (this.cmbGoods.SelectedValue != null)
                            {
                                IList<SiaSun.LMS.Model.GOODS_TEMPLATE> listGOODS_TEMPLATE = MainApp._I_GoodsService.GoodsTemplateGetList(Convert.ToInt32(this.cmbGoods.SelectedValue));

                                this.cmbTemplate.DisplayMemberPath = "GOODS_TEMPLATE_NAME";
                                this.cmbTemplate.SelectedValuePath = "GOODS_TEMPLATE_ID";
                                this.cmbTemplate.ItemsSource = listGOODS_TEMPLATE;
                            }
                        }
                     );


                this.cmbTemplate.SelectionChanged += new SelectionChangedEventHandler
                    ((cmbTemplateSender, cmbTemplateE) =>
                        {
                            if (this.cmbTemplate.SelectedValue != null)
                            {
                                this.ManageList_Bind(Convert.ToInt32(this.cmbTemplate.SelectedValue));
                            }
                        }
                    );

                this.InitManagePosition();


            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        private void GOODS_BIND()
        {
            try
            {
                IList<SiaSun.LMS.Model.GOODS_MAIN> listGOODS_MAIN = MainApp._I_GoodsService.GoodsGetListGoodsClassID(3);

                this.cmbGoods.DisplayMemberPath = "GOODS_NAME";
                this.cmbGoods.SelectedValuePath = "GOODS_ID";
                this.cmbGoods.ItemsSource = listGOODS_MAIN;
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        /// <summary>
        /// 
        /// </summary>
        private void InitManagePosition()
        {
            try
            {
                this.ucManagePosition.U_InitControl(mMANAGE_TYPE.MANAGE_TYPE_ID);
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
        }


        /// <summary>
        /// 
        /// </summary>
        public DataTable MANAGE_LIST_Add_By_Template(IList<SiaSun.LMS.Model.GOODS_TEMPLATE_LIST> listTemplateList)
        {
            string strSql = string.Format("SELECT * FROM V_MANAGE_LIST WHERE MANAGE_ID={0}", 0);
            using (DataTable tableManageList = MainApp._I_BaseService.GetList(strSql))
            {
                foreach (SiaSun.LMS.Model.GOODS_TEMPLATE_LIST goodsTemplateList in listTemplateList)
                {
                    SiaSun.LMS.Model.GOODS_MAIN mGOODS_MAIN = MainApp._I_GoodsService.GoodsGetModelGoodsID(goodsTemplateList.GOODS_ID);

                    SiaSun.LMS.Model.GOODS_CLASS mGOODS_CLASS = MainApp._I_GoodsService.GoodsClassGetModelGoodsClassID(mGOODS_MAIN.GOODS_CLASS_ID);

                    if (mGOODS_MAIN != null && mGOODS_CLASS != null)
                    {
                        DataRow rowManageList = tableManageList.NewRow();
                        rowManageList["GOODS_ID"] = mGOODS_MAIN.GOODS_ID;
                        rowManageList["GOODS_CODE"] = mGOODS_MAIN.GOODS_CODE;
                        rowManageList["GOODS_NAME"] = mGOODS_MAIN.GOODS_NAME;
                        rowManageList["GOODS_TYPE_ID"] = mGOODS_CLASS.GOODS_TYPE_ID;
                        rowManageList["MANAGE_LIST_QUANTITY"] = goodsTemplateList.GOODS_TEMPLATE_QUANTITY;

                        tableManageList.Rows.Add(rowManageList);
                    }
                }
                return tableManageList;
            }
        }



        /// <summary>
        /// 加载
        /// </summary>
        private void ManageList_Bind(int TEMPLATE_ID)
        {

            this.gridManageList.U_Clear();

            this.gridManageList.U_WindowName = this.GetType().Name;
            this.gridManageList.U_TableName = "V_MANAGE_LIST";
            this.gridManageList.U_XmlTableName = "V_MANAGE_LIST";
            this.gridManageList.U_OrderField = "MANAGE_LIST_ID";
            this.gridManageList.U_Where = "MANAGE_LIST_ID=0";

            this.gridManageList.U_AllowAdd = System.Windows.Visibility.Collapsed;
            this.gridManageList.U_AllowSave = System.Windows.Visibility.Collapsed;
            this.gridManageList.U_AllowEdit = System.Windows.Visibility.Collapsed;
            this.gridManageList.U_AllowChecked = false;
            this.gridManageList.U_AllowShowPage = false;

            this.gridManageList.U_SplitGroupColumn = "GOODS_TYPE_ID";
            this.gridManageList.U_SplitGroupHeader = "GOODS_TYPE_NAME";
            this.gridManageList.U_SplitPropertyType = "GOODS_TYPE";
            this.gridManageList.U_SplitPropertyColumn = "GOODS_PROPERTY";

            try
            {
                this.gridManageList.U_InitControl();
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }

            DataTable tableManageList = this.MANAGE_LIST_Add_By_Template( MainApp._I_GoodsService.GoodsTemplateListGetList(TEMPLATE_ID));

            this.gridManageList.U_AddTabPageRows(tableManageList.Rows.Cast<DataRow>().ToArray());

        }

        /// <summary>
        /// 按钮事件
        /// </summary>
        private void Button_Click(object sender, RoutedEventArgs e)
        {
            Button btn = e.OriginalSource as Button;
            if (btn != null)
            {
                switch (btn.Name)
                {
                    case "btnSave":
                        this.Confirm();
                        break;
                    case "btnRefresh":
                        this.Refresh();
                        break;
                }
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void Confirm()
        {
            string strResult = string.Empty;

            try
            {
                MainWindow.mainWin.Cursor = Cursors.Wait;

                //结束当前编辑操作
                this.gridManageList.U_EndCurrentEdit();
                
                //检验数据是否合法
                DataTable tableSource = this.gridManageList.U_DataSource;
                //根据数据源获得数据列表
                List<Model.MANAGE_LIST> listMANAGE_LIST = new SiaSun.LMS.Common.CloneObjectValues().GetListFromDataTable<Model.MANAGE_LIST>(tableSource, null);

                var list = from v in listMANAGE_LIST
                           where v.MANAGE_LIST_QUANTITY > 0
                           select v;

                listMANAGE_LIST = list.ToList<Model.MANAGE_LIST>();

                #region     ------校验合法性

                //判断是否填写数据
                if (listMANAGE_LIST.Count<=0)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Input);
                    return;
                }

                //判断数据是否合法
                if (tableSource.HasErrors)
                {
                    MainApp._MessageDialog.Show(Enum.MessageConverter.Data);
                    return;
                }


                //校验填写仓库信息是否合法
                if (!this.ucManagePosition.U_CHECK_WAREHOUSE())
                    return;

                #endregion



                //提示确认
                if (MainApp._MessageDialog.ShowDialog(Enum.MessageConverter.ConfirmAssembly, this.ucManagePosition.U_STOCK_BARCODE)== Sid.Windows.Controls.TaskDialogResult.Ok)
                {
                    //调用入库函数配盘入库

                    SiaSun.LMS.Model.MANAGE_MAIN mMANAGE_MAIN = new Model.MANAGE_MAIN();

                    mMANAGE_MAIN.GOODS_TEMPLATE_ID = Convert.ToInt32(this.cmbTemplate.SelectedValue);

                    mMANAGE_MAIN.PLAN_ID = 0;

                    mMANAGE_MAIN.MANAGE_TYPE_CODE = mMANAGE_TYPE.MANAGE_TYPE_CODE.TrimEnd();

                    mMANAGE_MAIN.STOCK_BARCODE = this.ucManagePosition.U_STOCK_BARCODE;

                    mMANAGE_MAIN.CELL_MODEL = this.ucManagePosition.U_CELL_MODEL;

                    mMANAGE_MAIN.START_CELL_ID = this.ucManagePosition.U_START_POSITION_ID;

                    mMANAGE_MAIN.END_CELL_ID = this.ucManagePosition.U_END_POSITION_ID;

                    mMANAGE_MAIN.MANAGE_OPERATOR = MainApp._USER.USER_NAME;

                    mMANAGE_MAIN.MANAGE_BEGIN_TIME = SiaSun.LMS.Common.StringUtil.GetDateTime();

                    mMANAGE_MAIN.MANAGE_STATUS = SiaSun.LMS.Enum.MANAGE_STATUS.WaitingSend.ToString();

                    mMANAGE_MAIN.MANAGE_LEVEL = string.Empty;

                    mMANAGE_MAIN.MANAGE_REMARK = string.Empty;

                    bool bResult = false;
                    //创建托盘任务

                    bResult = MainApp._I_BaseService.Invoke(mMANAGE_TYPE.MANAGE_TYPE_CLASS.TrimEnd(),
                                                               "ManageCreate", 
                                                                new object[] { mMANAGE_MAIN,
                                                                               listMANAGE_LIST, 
                                                                               true, 
                                                                               this.ucManagePosition.U_CheckStockExistStorage,
                                                                               this.ucManagePosition.U_AutoCompleteTask,
                                                                               this.ucManagePosition.U_AutoDownloadControlTask, 
                                                                               }, 
                                                                 out strResult);


                    //检验执行结果
                    if (bResult)
                    {
                        this.Refresh();
                    }

                    MainApp._MessageDialog.ShowResult(bResult, strResult);

                }
            }
            catch (Exception ex)
            {
                MainApp._MessageDialog.ShowException(ex);
            }
            finally
            {
                MainWindow.mainWin.Cursor = Cursors.Arrow;
            }
        }

        /// <summary>
        /// 
        /// </summary>
        private void Refresh()
        {
            this.ucManagePosition.U_Refresh();
        }


    }
}
