﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

namespace SiaSun.LMS.Implement.Interface
{
    /// <summary>
    /// 物料主数据同步任务下发接口
    /// </summary>
    public class MaterialSyn : InterfaceBase
    {
        /// <summary>
        /// 入参_主表
        /// </summary>
        public class InputParaMain
        {
            private string _itemCode = string.Empty;          //物料号
            private string _itemName = string.Empty;        //物料名
            private string _abcNo = string.Empty;            //ABC分类
            private string _unit = string.Empty;           //单位

            private string _beSnStack = string.Empty;           //是否SN码拣选

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 物料名
            /// </summary>
            public string itemName
            {
                get { return _itemName; }
                set { _itemName = value; }
            }

            /// <summary>
            /// ABC分类
            /// </summary>
            public string abcNo
            {
                get { return _abcNo; }
                set { _abcNo = value; }
            }

            /// <summary>
            /// 单位
            /// </summary>
            public string unit
            {
                get { return _unit; }
                set { _unit = value; }
            }

            /// <summary>
            /// 是否SN码拣选
            /// </summary>
            public string beSnStack
            {
                get { return _beSnStack; }
                set { _beSnStack = value; }
            }
        }

        /// <summary>
        /// 实现方法
        /// </summary>
        public bool NotifyMethod(string inJson, out string outJson)
        {
            bool bResult = true;
            outJson = string.Empty;
            OutputPara outputPara = new OutputPara();
            bool isAddItem = true;

            try
            {
                string wesInterfaceStatus = string.Empty;
                if (!this._S_SystemService.GetSysParameter("MiniloadTaskInterfaceStatus", out wesInterfaceStatus) || wesInterfaceStatus != Enum.FLAG.Enable.ToString())
                {
                    bResult = false;
                    outJson = string.Format("MaterialSyn.NotifyMethod:立库区任务接口状态不可用,请联系管理员更改运行参数");
                    return bResult;
                }

                InputParaMain taskInfo = Common.JsonHelper.Deserialize<InputParaMain>(inJson);

                if (string.IsNullOrEmpty(taskInfo.abcNo) || string.IsNullOrEmpty(taskInfo.itemCode) || string.IsNullOrEmpty(taskInfo.itemName) || string.IsNullOrEmpty(taskInfo.unit))
                {
                    bResult = false;
                    outJson = string.Format("MaterialSyn.NotifyMethod:入参存在空值");
                    return bResult;
                }

                Model.GOODS_MAIN mGOODS_MAIN = this._P_GOODS_MAIN.GetModel(taskInfo.itemCode);
                if (mGOODS_MAIN == null)
                {
                    mGOODS_MAIN = new Model.GOODS_MAIN();
                    mGOODS_MAIN.GOODS_CLASS_ID = 2;
                    mGOODS_MAIN.LOGIC_ID = 0;
                    mGOODS_MAIN.GOODS_CODE = taskInfo.itemCode;
                    mGOODS_MAIN.GOODS_CONST_PROPERTY1 = taskInfo.abcNo;
                    //2020-11-07 09:29:10 专门字段定义是否SN拣选
                    //mGOODS_MAIN.GOODS_CONST_PROPERTY2 = taskInfo.abcNo.ToLower() == "a" ? "1" : "0";
                    mGOODS_MAIN.GOODS_CONST_PROPERTY2 = 
                        (!string.IsNullOrEmpty(taskInfo.beSnStack) && taskInfo.beSnStack == "1") ? "1" : "0";
                    mGOODS_MAIN.GOODS_FLAG = "1";
                    mGOODS_MAIN.GOODS_NAME = taskInfo.itemName;
                    mGOODS_MAIN.GOODS_UNITS = taskInfo.unit;
                    this._P_GOODS_MAIN.Add(mGOODS_MAIN);
                    mGOODS_MAIN.GOODS_ORDER = mGOODS_MAIN.GOODS_ID;
                    this._P_GOODS_MAIN.Update(mGOODS_MAIN);
                    isAddItem = true;
                }
                else
                {
                    mGOODS_MAIN.GOODS_CONST_PROPERTY1 = taskInfo.abcNo;
                    mGOODS_MAIN.GOODS_CONST_PROPERTY2 = taskInfo.abcNo.ToLower() == "a" ? "1" : "0";
                    mGOODS_MAIN.GOODS_NAME = taskInfo.itemName;
                    mGOODS_MAIN.GOODS_UNITS = taskInfo.unit;
                    this._P_GOODS_MAIN.Update(mGOODS_MAIN);
                    isAddItem = false;
                }

                this.CreateSysLog(Enum.LogThread.Plan, "System", true, string.Format("MaterialSyn.NotifyMethod():通过接口{0}物料_物料编码[{1}]", isAddItem ? "新增" : "更新", taskInfo.itemCode));
            }
            catch (Exception ex)
            {
                bResult = false;
                outJson = string.Format("MaterialSyn.NotifyMethod:程序运行中发生异常 {0}", ex.Message);
            }
            finally
            {
                if(bResult)
                {
                    outputPara.responseCode = "1";
                    outputPara.responseMessage = "成功";
                }
                else
                {
                    outputPara.responseCode = "0";
                    outputPara.responseMessage = " 失败" + outJson;
                }
                outJson = Common.JsonHelper.Serializer(outputPara);
            }

            return bResult;
        }

    }
}
