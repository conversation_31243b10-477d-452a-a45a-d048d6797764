﻿/***************************************************************************
 * 
 *       功能：     仓库货位组持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// WH_CELL_GROUP
	/// </summary>
	public class P_WH_CELL_GROUP : P_Base_House
	{
		public P_WH_CELL_GROUP ()
		{
			//
			// TODO: 此处添加WH_CELL_GROUP的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<WH_CELL_GROUP> GetList()
		{
			return ExecuteQueryForList<WH_CELL_GROUP>("WH_CELL_GROUP_SELECT",null);
		}

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(WH_CELL_GROUP wh_cell_group)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("WH_CELL_GROUP");
                wh_cell_group.GROUP_ID = id;
            }

            return ExecuteInsert("WH_CELL_GROUP_INSERT",wh_cell_group);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(WH_CELL_GROUP wh_cell_group)
		{
			return ExecuteUpdate("WH_CELL_GROUP_UPDATE",wh_cell_group);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public WH_CELL_GROUP GetModel(System.Int32 GROUP_ID)
		{
			return ExecuteQueryForObject<WH_CELL_GROUP>("WH_CELL_GROUP_SELECT_BY_ID",GROUP_ID);
		}

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public WH_CELL_GROUP GetModelByPickNo(string PICK_NO)
        {
            return ExecuteQueryForObject<WH_CELL_GROUP>("WH_CELL_GROUP_SELECT_BY_PICK_NO", PICK_NO);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public WH_CELL_GROUP GetModelByGroupCode(string GROUP_CODE)
        {
            return ExecuteQueryForObject<WH_CELL_GROUP>("WH_CELL_GROUP_SELECT_BY_GROUP_CODE", GROUP_CODE);
        }

        /// <summary>
        /// 得到明细
        /// </summary>
        /// <param name="id"></param>
        /// <returns></returns>
        public WH_CELL_GROUP GetModelByAvailLocNum(System.Int32 AVAIL_LOC_NUM)
        {
            return ExecuteQueryForObject<WH_CELL_GROUP>("WH_CELL_GROUP_SELECT_BY_AVAIL_LOC_NUM", AVAIL_LOC_NUM);
        }

        public object GetModelObject(System.Int32 GROUP_ID)
        {
            return ExecuteQueryForObject("WH_CELL_GROUP_SELECT_BY_ID", GROUP_ID);
        }

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 GROUP_ID)
		{
			return ExecuteDelete("WH_CELL_GROUP_DELETE",GROUP_ID);
		}
		

	}
}
