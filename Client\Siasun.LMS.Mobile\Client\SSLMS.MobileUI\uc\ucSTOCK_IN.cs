﻿using System;
using System.Linq;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Text;
using System.Windows.Forms;
using System.Collections;
using System.Text.RegularExpressions;

namespace SSLMS.MobileUI.uc
{
    public partial class ucSTOCK_IN : UserControl
    {
        public ucSTOCK_IN()
        {
            this.InitializeComponent();

            this.ucCell1.Init();

            this.ucCell1.CellDoubleClick += new ucCell.CELLDoubleClickHandler(ucCell1_CellDoubleClick);

            this.ucCell1.Dock = DockStyle.Fill;

            this.SET_CONTROL(true);

            this.cbWAREHOUSE_Bind();

            this.cbCELL_MODEL_Bind();

            this.cbSTOCK_Bind();

            this.txtSTOCK_BARCODE.Focus();
        }

        void ucCell1_CellDoubleClick(WH_CELL mST_CELL)
        {
            this.txtRow.Text = mST_CELL._cell_z.ToString().PadLeft(2, '0');

            this.txtLine.Text = mST_CELL._cell_x.ToString().PadLeft(2, '0');

            this.txtFloor.Text = mST_CELL._cell_y.ToString().PadLeft(2, '0');

            this.cmbCELL_MODEL.SelectedValue = mST_CELL._cell_model;

            this.SET_CONTROL(true);
        }


        private void SET_CONTROL(bool bVisible)
        {

            this.ucCell1.Visible = !bVisible;
        }

        private void cbAREA_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.cmbAREA.SelectedValue != null)
            {
                this.cbCELL_Bind(this.cmbAREA.SelectedValue.ToString());
            }
        }

        private void cbWAREHOUSE_SelectedValueChanged(object sender, EventArgs e)
        {
            if (this.cmbWAREHOUSE.SelectedValue != null)
            {
                string WAREHOUSE_ID = this.cmbWAREHOUSE.SelectedValue.ToString();

                this.cbAREA_Bind(WAREHOUSE_ID);
            }
        }

        private bool _bAddSTOCK
        {
            get
            {
                string STOCK_BARCODE = this.txtSTOCK_BARCODE.Text.Trim();

                string sResult = string.Empty; ;

                return !Program._I_StorageService.STORAGE_LIST_CheckStock_1(STOCK_BARCODE, out  sResult);
            }
        }

        private void cbWAREHOUSE_Bind()
        {
            try
            {
                cmbWAREHOUSE.ValueMember = "WAREHOUSE_ID";

                cmbWAREHOUSE.DisplayMember = "WAREHOUSE_NAME";

                cmbWAREHOUSE.DataSource = Program._I_WHService.WAREHOUSE_GetList();
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void cbAREA_Bind(string WAREHOUSE_ID)
        {
            try
            {
                cmbAREA.ValueMember = "AREA_ID";

                cmbAREA.DisplayMember = "AREA_NAME";

                //cmbAREA.DataSource = Program._I_WHService.AREA_GetList_1(WAREHOUSE_ID, SSLMS.Model.AREA_TYPE.xuniku.ToString());
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void cbCELL_Bind(string AREA_ID)
        {
            try
            {
                this.cmbStation.ValueMember = "CELL_ID";

                cmbStation.DisplayMember = "CELL_NAME";

                System.Collections.Hashtable ht = new System.Collections.Hashtable();

                ht.Add("CELL_CODE", "0");

                ht.Add("CELL_NAME", "-");

                this.cmbStation.DataSource = SSLMS.MobileUI.Common.AddAll(Program._I_WHService.CELL_GetList_1(string.Empty, string.Empty, SSLMS.Model.CELL_TYPE.station.ToString(), string.Empty, SSLMS.Model.CELL_INOUT.IN.ToString()), ht);
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void cbCELL_MODEL_Bind()
        {
            try
            {
                cmbCELL_MODEL.ValueMember = "value";

                cmbCELL_MODEL.DisplayMember = "name";

                //cmbCELL_MODEL.DataSource = Program._I_SystemService.GetD1("CELL_MODEL");
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void cbSTOCK_Bind()
        {
            try
            {
                this.cmbSTOCK.ValueMember = "GOODS_ID";

                this.cmbSTOCK.DisplayMember = "GOODS_NAME";

                //this.cmbSTOCK.DataSource = Program._I_GoodsService.GOODS_GetList(this.cmbAREA.SelectedValue.ToString(), "0") ;
            }
            catch (Exception ex)
            {
                MessageBox.Show("系统异常\n" + ex.Message);
            }
        }

        private void btnOK_Click(object sender, EventArgs e)
        {
            //try
            //{
            //    if (this.txtSTOCK_BARCODE.Text.TrimEnd().Equals(string.Empty))
            //    {
            //        MessageBox.Show( "must Input Pallet Barcode");

            //        return;
            //    }

            //    if (null == this.cmbStation.SelectedValue || this.cmbStation.SelectedValue.ToString() == "0")
            //    {

            //        MessageBox.Show("Please Select Loading Station!");

            //        return;
            //    }

            //    if (this.cmbSTOCK.SelectedValue == null)
            //    {
            //        MessageBox.Show( "must Select Container");

            //        return;
            //    }

            //    if (this.txtRow.Text.TrimEnd() == string.Empty || this.txtLine.Text.TrimEnd()==string.Empty || this.txtFloor.Text.TrimEnd() ==string.Empty)
            //    {
            //        MessageBox.Show("must Select Storage Cell");

            //        return;
            //    }

            //    int MANAGE_ID = 0;

            //    string sResult = string.Empty;

            //    bool bResult = true;

            //   WH_CELL START_CELL = Program._I_WHService.CELL_GetModel(Convert.ToInt32(this.cmbStation.SelectedValue));

            //    if (this._bAddSTOCK)
            //    {
            //        bResult = Program._I_ManageService.ManageCreatePalletEntryTask(this._USER, START_CELL._cell_id, Convert.ToInt32(this.cmbSTOCK.SelectedValue), Convert.ToInt32(this.txtQUANTITY.Text), this.txtSTOCK_BARCODE.Text.Trim(), false, true, out sResult, out MANAGE_ID);
            //    }

            //    string END_CELL_CODE = string.Format("{0}-{1}-{2}", this.txtRow.Text.TrimEnd(), this.txtLine.Text.TrimEnd(), this.txtFloor.Text.TrimEnd());

            //    if (bResult)
            //    {

            //        bResult = Program._I_ManageService.ManageCreateLoadingMoveTask(this._USER, START_CELL._cell_code, END_CELL_CODE,
            //                                                                     this.txtSTOCK_BARCODE.Text.TrimEnd(),
            //                                                                     this.cmbSTOCK.SelectedValue.ToString(),
            //                                                                     true, true, out sResult, out MANAGE_ID);
            //        if (bResult)
            //        {
            //            sResult = string.Format(" Download Pallet load task  Successed!\n{0}", sResult);
            //            MessageBox.Show(sResult);
            //        }
            //        else
            //        {
            //            sResult = string.Format(" Download Pallet load task  Failed!\n{0}", sResult);

            //            MessageBox.Show(sResult);
            //        }

            //    }
            //    else
            //    {
            //        sResult = string.Format("Pallet In Stock Failed\n{0}", sResult);

            //        MessageBox.Show(sResult);
            //    }
            //}
            //catch (Exception ex)
            //{
            //    MessageBox.Show("系统异常\n" + ex.Message);
            //}
        }

        private void btnSelect_Click(object sender, EventArgs e)
        {
            this.SET_CONTROL(this.ucCell1.Visible);
        }

    }
}
