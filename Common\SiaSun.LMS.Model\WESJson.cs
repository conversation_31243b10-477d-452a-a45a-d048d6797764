﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
// ReSharper disable InconsistentNaming

namespace SiaSun.LMS.Model
{
    using System;
    using System.Runtime.Serialization;

    [Serializable]
    [DataContract]
    public class WESJson
    {
        /// <summary>
        /// 非立库传输
        /// </summary>
        public class NoneMiniloadTrans
        { 
            private string _palletNo = string.Empty;        //托盘号
            private string _fromPosition = string.Empty;    //起点位置
            private string _toPosition = string.Empty;      //终点位置
            private string _uniqueCode = string.Empty;      //唯一码
            private string _interfaceType = string.Empty;   //接口类型
            private string _interfaceSource = string.Empty; //接口来源
            private string _extend1 = string.Empty;         //扩展字段1
            private string _extend2 = string.Empty;         //扩展字段2
            private string _extend3 = string.Empty;         //扩展字段3
            private string _extend4 = string.Empty;         //扩展字段4
            private string _extend5 = string.Empty;         //扩展字段5
            private string _relevantCode = string.Empty;     //相关单号
            
            /// <summary>
            /// 托盘号
            /// </summary>
            public string palletNo
            {
                get { return _palletNo;  }
                set { _palletNo = value; }
            }
            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 终点位置
            /// </summary>
            public string toPosition
            {
                get { return _toPosition; }
                set { _toPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 扩展字段1
            /// </summary>
            public string extend1
            {
                get { return _extend1; }
                set { _extend1 = value; }
            }

            /// <summary>
            /// 扩展字段1
            /// </summary>
            public string extend2
            {
                get { return _extend2; }
                set { _extend2 = value; }
            }

            /// <summary>
            /// 扩展字段1
            /// </summary>
            public string extend3
            {
                get { return _extend3; }
                set { _extend3 = value; }
            }

            /// <summary>
            /// 扩展字段4
            /// </summary>
            public string extend4
            {
                get { return _extend4; }
                set { _extend4 = value; }
            }

            /// <summary>
            /// 扩展字段5
            /// </summary>
            public string extend5
            {
                get { return _extend5; }
                set { _extend5 = value; }
            }

            /// <summary>
            /// 相关单号
            /// </summary>
            public string relevantCode
            {
                get { return _relevantCode; }
                set { _relevantCode = value; }
            }

        }


        /// <summary>
        /// 组箱入立库_主表
        /// </summary>
        public class CreateBoxInMain
        {
            private string _boxNo = string.Empty;                  //箱号
            private string _boxType = string.Empty;                //箱子类型
            private string _fromPosition = string.Empty;           //起始位置
            private string _uniqueCode = string.Empty;             //唯一码
            private string _interfaceType = string.Empty;          //接口类型
            private string _interfaceSource = string.Empty;        //接口来源
            private string _boxColor = string.Empty;               //箱子颜色
            public CreateBoxInList[] createBoxInList { get; set; }     //一级明细

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 起始位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 箱子颜色
            /// </summary>
            public string boxColor
            {
                get { return _boxColor; }
                set { _boxColor = value; }
            }
        }
        /// <summary>
        /// 组箱入立库_一级明细
        /// </summary>
        public class CreateBoxInList
        {
            private string _gridNo = string.Empty;    //格子号
            private string _itemCode = string.Empty;   //物料号
            private string _quantity = string.Empty;  //数量

            /// <summary>
            /// 格子号
            /// </summary>
            public string gridNo
            {
                get { return _gridNo ; }
                set { _gridNo = value; }
            }
            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode ; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity 
            {
                get { return _quantity ; }
                set { _quantity = value; }
            }


        }


        /// <summary>
        /// 空箱出库
        /// </summary>
        public class EmptyBoxOut
        {
            private string _quantity = string.Empty;                      //数量
            private string _boxType = string.Empty;                       //箱子类型
            private string _toPosition = string.Empty;                    //终点位置
            private string _uniqueCode = string.Empty;                    //唯一码
            private string _interfaceType = string.Empty;                 //接口类型
            private string _interfaceSource = string.Empty;               //接口来源

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
            
            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }
            
            /// <summary>
            /// 终点位置
            /// </summary>
            public string toPosition
            {
                get { return _toPosition; }
                set { _toPosition = value; }
            }
            
            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }
            
            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }
            
            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }         
        }


        /// <summary>
        /// 空箱入立库
        /// </summary>
        public class EmptyBoxIn
        {
            private string _boxNo = string.Empty;                     //箱号
            private string _boxType = string.Empty;                   //箱子类型
            private string _fromPosition = string.Empty;              //起点位置
            private string _uniqueCode = string.Empty;                //唯一码
            private string _interfaceType = string.Empty;             //接口类型
            private string _boxColor = string.Empty;                  //箱子颜色
            private string _interfaceSource = string.Empty;           //接口来源

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo ; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType ; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition ; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 箱子颜色
            /// </summary>
            public string boxColor
            {
                get { return _boxColor; }
                set { _boxColor = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }


        /// <summary>
        /// 盘点任务下发_主表
        /// </summary>
        public class StockCheckTaskMain
        {
            private string _countCode = string.Empty;            //盘点计划号
            private string _uniqueCode = string.Empty;           //唯一码
            private string _interfaceType = string.Empty;        //接口类型
            private string _interfaceSource = string.Empty;      //接口来源
            public StockCheckTaskList[] stockCheckTaskList { get;set; } //一级明细

            /// <summary>
            /// 盘点计划号
            /// </summary>
            public string countCode
            {
                get { return _countCode; }
                set { _countCode = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 盘点任务下发_一级明细
        /// </summary>
        public class StockCheckTaskList
        {
            private string _itemCode = string.Empty;        //物料号

            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }
        }


        /// <summary>
        /// Miniload拣选任务下发_主表
        /// </summary>
        public class MiniloadSortTaskMain
        {
            string _pickType = string.Empty;            //拣选方式
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public MiniloadSortTaskList[] miniloadSortTaskList { get; set; }//一级明细

            /// <summary>
            /// 拣选方式
            /// </summary>
            public string pickType
            {
                get { return _pickType; }
                set { _pickType = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// Miniload拣选任务下发_一级明细
        /// </summary>
        public class MiniloadSortTaskList
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号
            private string _description = string.Empty;     //WBS描述
            public MiniloadSortTaskDetail[] miniloadSortTaskDetail { get; set; }//二级明细

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// WBS描述
            /// </summary>
            public string description
            {
                get { return _description; }
                set { _description = value; }
            }
        }
        /// <summary>
        /// Miniload拣选任务下发_二级明细
        /// </summary>
        public class MiniloadSortTaskDetail
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty;     //数量

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }


        /// <summary>
        /// 紧急配料出库_主表
        /// </summary>
        public class EmergSortOutTaskMain
        {
            private string _taskNo = string.Empty;          //任务单号
            string _uniqueCode = string.Empty;          //唯一码
            string _interfaceType = string.Empty;       //接口类型
            string _interfaceSource = string.Empty;     //接口来源
            public EmergSortOutTaskList[] emergSortOutTaskList { get; set; }//一级明细

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }
        /// <summary>
        /// 紧急配料出库_一级明细
        /// </summary>
        public class EmergSortOutTaskList
        {
            private string _itemCode = string.Empty;        //物料号
            private string _quantity = string.Empty; //数量
           
            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }


        /// <summary>
        /// 齐套箱出库任务下发_主表
        /// </summary>
        public class KitBoxOutTaskMain
        {
            private string _uniqueCode = string.Empty;          //唯一码
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源
            private string _toLocation = string.Empty;          //终点位置
            public KitBoxOutTaskList[] kitBoxOutTaskList { get; set; }     //一级明细

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 终点位置
            /// </summary>
            public string toLocation
            {
                get { return _toLocation; }
                set { _toLocation = value; }
            }
        }
        /// <summary>
        /// 齐套箱出库任务下发_一级明细
        /// </summary>
        public class KitBoxOutTaskList
        {
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            private string _taskNo = string.Empty;          //任务单号

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }

            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }
        }


        /// <summary>
        /// 整理未满箱任务下发
        /// </summary>
        public class ArrangeEmptyBoxTask
        {
            private string _boxType = string.Empty;            //箱型
            private string _emptyQuantity = string.Empty;      //空格数
            private string _boxQuantity = string.Empty;        //箱数
            private string _uniqueCode = string.Empty;          //唯一码
            private string _interfaceType = string.Empty;       //接口类型
            private string _interfaceSource = string.Empty;     //接口来源

            /// <summary>
            /// 箱型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 空格数
            /// </summary>
            public string emptyQuantity
            {
                get { return _emptyQuantity; }
                set { _emptyQuantity = value; }
            }

            /// <summary>
            /// 箱数
            /// </summary>
            public string boxQuantity
            {
                get { return _boxQuantity; }
                set { _boxQuantity = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }
        }


        /// <summary>
        /// 余箱回库任务下发_主表
        /// </summary>
        public class BoxReturnInTaskMain
        {
            private string _boxNo = string.Empty;                  //箱号
            private string _boxType = string.Empty;                //箱子类型
            private string _fromPosition = string.Empty;           //起点位置
            private string _uniqueCode = string.Empty;             //唯一码
            private string _interfaceType = string.Empty;          //接口类型
            private string _interfaceSource = string.Empty;        //接口来源
            private string _boxColor = string.Empty;               //箱子颜色
            public BoxReturnInTaskList[] boxReturnInTaskList { get; set; }       //一级明细

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 箱子类型
            /// </summary>
            public string boxType
            {
                get { return _boxType; }
                set { _boxType = value; }
            }

            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 箱子颜色
            /// </summary>
            public string boxColor
            {
                get { return _boxColor; }
                set { _boxColor = value; }
            }
        }
        /// <summary>
        /// 余箱回库任务下发_一级明细
        /// </summary>
        public class BoxReturnInTaskList
        {
            private string _gridNo = string.Empty;    //格子号
            private string _itemCode = string.Empty;   //物料号
            private string _quantity = string.Empty;  //数量

            /// <summary>
            /// 格子号
            /// </summary>
            public string gridNo
            {
                get { return _gridNo; }
                set { _gridNo = value; }
            }
            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }
        }


        /// <summary>
        /// 齐套箱出库时余料回库任务下发_主表
        /// </summary>
        public class KitBoxReturnInTaskMain
        {
            private string _interfaceType = string.Empty;          //接口类型
            private string _interfaceSource = string.Empty;        //接口来源
            private string _boxNo = string.Empty;                  //箱号
            private string _fromPosition = string.Empty;           //起点位置
            private string _uniqueCode = string.Empty;             //唯一码
            public KitBoxReturnInTaskList[] kitBoxReturnInTaskList { get; set; }     //一级明细

            /// <summary>
            /// 接口类型
            /// </summary>
            public string interfaceType
            {
                get { return _interfaceType; }
                set { _interfaceType = value; }
            }

            /// <summary>
            /// 接口来源
            /// </summary>
            public string interfaceSource
            {
                get { return _interfaceSource; }
                set { _interfaceSource = value; }
            }

            /// <summary>
            /// 箱号
            /// </summary>
            public string boxNo
            {
                get { return _boxNo; }
                set { _boxNo = value; }
            }

            /// <summary>
            /// 起点位置
            /// </summary>
            public string fromPosition
            {
                get { return _fromPosition; }
                set { _fromPosition = value; }
            }

            /// <summary>
            /// 唯一码
            /// </summary>
            public string uniqueCode
            {
                get { return _uniqueCode; }
                set { _uniqueCode = value; }
            }
        }
        /// <summary>
        /// 齐套箱出库时余料回库任务下发_一级明细
        /// </summary>
        public class KitBoxReturnInTaskList
        {
            private string _taskNo = string.Empty;          //任务单号
            private string _projectNo = string.Empty;       //项目号
            private string _wbsNo = string.Empty;           //WBS号
            public KitBoxReturnInTaskDetail[] kitBoxReturnInTaskDetail { get; set; }    //二级明细
           
            /// <summary>
            /// 任务单号
            /// </summary>
            public string taskNo
            {
                get { return _taskNo; }
                set { _taskNo = value; }
            }

            /// <summary>
            /// 项目号
            /// </summary>
            public string projectNo
            {
                get { return _projectNo; }
                set { _projectNo = value; }
            }

            /// <summary>
            /// WBS号
            /// </summary>
            public string wbsNo
            {
                get { return _wbsNo; }
                set { _wbsNo = value; }
            }
        }
        /// <summary>
        /// 齐套箱出库时余料回库任务下发_二级明细
        /// </summary>
        public class KitBoxReturnInTaskDetail
        {
            private string _itemCode = string.Empty;   //物料号
            private string _quantity = string.Empty;  //数量

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 数量
            /// </summary>
            public string quantity
            {
                get { return _quantity; }
                set { _quantity = value; }
            }

        }


        /// <summary>
        /// 物料主数据同步任务下发接口
        /// </summary>
        public class MaterialSyn
        {
            private string _itemCode = string.Empty;          //物料号
            private string _itemName = string.Empty;        //物料名
            private string _abcNo = string.Empty;            //ABC分类
            private string _unit = string.Empty;           //单位

            /// <summary>
            /// 物料号
            /// </summary>
            public string itemCode
            {
                get { return _itemCode; }
                set { _itemCode = value; }
            }

            /// <summary>
            /// 物料名
            /// </summary>
            public string itemName
            {
                get { return _itemName; }
                set { _itemName = value; }
            }

            /// <summary>
            /// ABC分类
            /// </summary>
            public string abcNo
            {
                get { return _abcNo; }
                set { _abcNo = value; }
            }

            /// <summary>
            /// 单位
            /// </summary>
            public string unit
            {
                get { return _unit; }
                set { _unit = value; }
            }

        }
    }
}
