﻿<ad:DocumentContent
    x:Class="SiaSun.LMS.WPFClient.MANAGE.MANAGE_PICK"
    xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
    xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
    xmlns:ad="clr-namespace:AvalonDock;assembly=AvalonDock"
    xmlns:uc="clr-namespace:SiaSun.LMS.WPFClient.UC"
    Title="MANAGE_STOCK_IN"
    Width="1000"
    Height="650"
    Loaded="DocumentContent_Loaded">

    <Grid ButtonBase.Click="Grid_Click" KeyDown="Grid_KeyDown">
        <Grid.ColumnDefinitions>
            <ColumnDefinition Width="*" />
            <ColumnDefinition Width="800" />
            <ColumnDefinition Width="*" />
        </Grid.ColumnDefinitions>
        <Grid.RowDefinitions>
            <RowDefinition Height="50" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="10" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="10" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="10" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="10" />
            <RowDefinition Height="auto" />
            <RowDefinition Height="50" />
        </Grid.RowDefinitions>

        <GroupBox
            Grid.Row="1"
            Grid.Column="1"
            FontSize="15"
            Header="拣选位置">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Label
                    Grid.Column="1"
                    Margin="0,10"
                    Content="站台编码："
                    FontSize="18" />
                <ComboBox
                    x:Name="cbxStationCode"
                    Grid.Column="2"
                    Width="200"
                    Height="30"
                    SelectionChanged="cbxStationCode_SelectionChanged" />
            </Grid>

        </GroupBox>

        <GroupBox
            Grid.Row="3"
            Grid.Column="1"
            FontSize="15"
            Header="拣选单据">

            <Grid>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="2*" />
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                </Grid.ColumnDefinitions>
                <Label
                    Grid.Column="1"
                    Margin="0,10"
                    Content="唯一码："
                    FontSize="18" />
                <StackPanel
                    Grid.Column="2"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <ComboBox
                        x:Name="cbxUniqueCode"
                        Width="170"
                        Height="30" />
                    <Button
                        x:Name="btnReloadPlan"
                        Width="40"
                        Height="25"
                        Content="刷新"
                        FontSize="15" />
                </StackPanel>

                <Label
                    Grid.Column="3"
                    Content="空箱条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxEmptyBox"
                    Grid.Column="4"
                    Grid.ColumnSpan="2"
                    Width="150" />
                <!--<Button
                    x:Name="btnStartPick"
                    Grid.Row="0"
                    Grid.Column="5"
                    Width="100"
                    Height="30"
                    Margin="10"
                    Content="开始拣选"
                    FontSize="18" />-->

            </Grid>

        </GroupBox>


        <GroupBox
            Grid.Row="5"
            Grid.Column="1"
            FontSize="15"
            Header="原料箱拣选">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="*" />
                    <RowDefinition Height="10" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="10" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="auto" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="10" />
                </Grid.ColumnDefinitions>
                <Label
                    x:Name="lblSource"
                    Grid.Row="0"
                    Grid.Column="1"
                    Content="原料箱条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxSourceBox"
                    Grid.Column="2"
                    Margin="5" />
                <StackPanel
                    Grid.Row="0"
                    Grid.Column="3"
                    Grid.ColumnSpan="2" />

                <Label
                    Grid.Row="1"
                    Grid.Column="1"
                    Margin="0,10,0,10"
                    Content="剩余待检箱："
                    FontSize="18"
                    Visibility="Hidden" />
                <TextBox
                    x:Name="tbxBoxToPick"
                    Grid.Row="1"
                    Grid.Column="2"
                    Grid.ColumnSpan="3"
                    Margin="5"
                    IsEnabled="False"
                    Visibility="Hidden" />

                <!--  1号箱  -->
                <Label
                    Grid.Row="2"
                    Grid.Column="1"
                    Content="1#物料条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxGoodsCode1"
                    Grid.Row="2"
                    Grid.Column="2"
                    Margin="5" />
                <Label
                    Grid.Row="2"
                    Grid.Column="3"
                    Content="拣选数量："
                    FontSize="18" />
                <StackPanel
                    Grid.Row="2"
                    Grid.Column="4"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="btnIncrease1"
                        Width="45"
                        Content="-"
                        FontSize="20" />                    
                    <TextBox
                        x:Name="tbxPickCount1"
                        Width="60"
                        Height="30"
                        Margin="5"
                        FontSize="20"
                        FontStyle="Normal"
                        FontWeight="Bold"
                        IsReadOnly="True" />
                    <Button
                        x:Name="btnReduce1"
                        Width="45"
                        Content="+"
                        FontSize="20" />                
                </StackPanel>

                <!--  2号箱  -->
                <Label
                    Grid.Row="3"
                    Grid.Column="1"
                    Content="2#物料条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxGoodsCode2"
                    Grid.Row="3"
                    Grid.Column="2"
                    Margin="5" />
                <Label
                    Grid.Row="3"
                    Grid.Column="3"
                    Content="拣选数量："
                    FontSize="18" />
                <StackPanel
                    Grid.Row="3"
                    Grid.Column="4"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="btnIncrease2"
                        Width="45"
                        Content="-"
                        FontSize="20" />                    
                    <TextBox
                        x:Name="tbxPickCount2"
                        Width="60"
                        Height="30"
                        Margin="5"
                        FontSize="20"
                        FontStyle="Normal"
                        FontWeight="Bold"
                        IsReadOnly="True" />
                    <Button
                        x:Name="btnReduce2"
                        Width="45"
                        Content="+"
                        FontSize="20" />
                </StackPanel>

                <!--  3号箱  -->
                <Label
                    Grid.Row="4"
                    Grid.Column="1"
                    Content="3#物料条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxGoodsCode3"
                    Grid.Row="4"
                    Grid.Column="2"
                    Margin="5" />
                <Label
                    Grid.Row="4"
                    Grid.Column="3"
                    Content="拣选数量："
                    FontSize="18" />
                <StackPanel
                    Grid.Row="4"
                    Grid.Column="4"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="btnIncrease3"
                        Width="45"
                        Content="-"
                        FontSize="20" />
                    <TextBox
                        x:Name="tbxPickCount3"
                        Width="60"
                        Height="30"
                        Margin="5"
                        FontSize="20"
                        FontStyle="Normal"
                        FontWeight="Bold"
                        IsReadOnly="True" />
                    <Button
                        x:Name="btnReduce3"
                        Width="45"
                        Content="+"
                        FontSize="20" />                
                </StackPanel>

                <!--  4号箱  -->
                <Label
                    Grid.Row="5"
                    Grid.Column="1"
                    Content="4#物料条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxGoodsCode4"
                    Grid.Row="5"
                    Grid.Column="2"
                    Margin="5" />
                <Label
                    Grid.Row="5"
                    Grid.Column="3"
                    Content="拣选数量："
                    FontSize="18" />
                <StackPanel
                    Grid.Row="5"
                    Grid.Column="4"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="btnIncrease4"
                        Width="45"
                        Content="-"
                        FontSize="20" />                    
                    <TextBox
                        x:Name="tbxPickCount4"
                        Width="60"
                        Height="30"
                        Margin="5"
                        FontSize="20"
                        FontStyle="Normal"
                        FontWeight="Bold"
                        IsReadOnly="True" />
                    <Button
                        x:Name="btnReduce4"
                        Width="45"
                        Content="+"
                        FontSize="20" />                
                </StackPanel>

                <!--  5号箱  -->
                <Label
                    Grid.Row="6"
                    Grid.Column="1"
                    Content="5#物料条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxGoodsCode5"
                    Grid.Row="6"
                    Grid.Column="2"
                    Margin="5" />
                <Label
                    Grid.Row="6"
                    Grid.Column="3"
                    Content="拣选数量："
                    FontSize="18" />
                <StackPanel
                    Grid.Row="6"
                    Grid.Column="4"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="btnIncrease5"
                        Width="45"
                        Content="-"
                        FontSize="20" />                    
                    <TextBox
                        x:Name="tbxPickCount5"
                        Width="60"
                        Height="30"
                        Margin="5"
                        FontSize="20"
                        FontStyle="Normal"
                        FontWeight="Bold"
                        IsReadOnly="True" />
                    <Button
                        x:Name="btnReduce5"
                        Width="45"
                        Content="+"
                        FontSize="20" />                
                </StackPanel>

                <!--  6号箱  -->
                <Label
                    Grid.Row="7"
                    Grid.Column="1"
                    Content="6#物料条码："
                    FontSize="18" />
                <TextBox
                    x:Name="tbxGoodsCode6"
                    Grid.Row="7"
                    Grid.Column="2"
                    Margin="5" />
                <Label
                    Grid.Row="7"
                    Grid.Column="3"
                    Content="拣选数量："
                    FontSize="18" />
                <StackPanel
                    Grid.Row="7"
                    Grid.Column="4"
                    HorizontalAlignment="Center"
                    Orientation="Horizontal">
                    <Button
                        x:Name="btnIncrease6"
                        Width="45"
                        Content="-"
                        FontSize="20" />                    
                    <TextBox
                        x:Name="tbxPickCount6"
                        Width="60"
                        Height="30"
                        Margin="5"
                        FontSize="20"
                        FontStyle="Normal"
                        FontWeight="Bold"
                        IsReadOnly="True" />
                    <Button
                        x:Name="btnReduce6"
                        Width="45"
                        Content="+"
                        FontSize="20" />                
                </StackPanel>


            </Grid>
        </GroupBox>

        <!--<GroupBox
            Grid.Row="7"
            Grid.Column="1"
            FontSize="15"
            Header="确认操作">

            <Grid>
                <Grid.RowDefinitions>
                    <RowDefinition Height="*" />
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="100" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="*" />
                    <ColumnDefinition Width="100" />
                </Grid.ColumnDefinitions>



                <Button
                    x:Name="btnConfirmThisBox"
                    Grid.Column="2"
                    Width="150"
                    Height="30"
                    Margin="10"
                    Content="确认本箱"
                    FontSize="18" />

                <Button
                    x:Name="btnReset"
                    Grid.Column="3"
                    Width="150"
                    Height="30"
                    Margin="10"
                    Content="重新开始"
                    FontSize="18" />

            </Grid>
        </GroupBox>-->

    </Grid>
</ad:DocumentContent>