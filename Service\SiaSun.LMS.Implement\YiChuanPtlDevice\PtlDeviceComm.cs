﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;

using Ptl.Device;
using Ptl.Device.Communication;
using Ptl.Device.Communication.Command;


namespace SiaSun.LMS.Implement.YiChuanPtlDevice
{
    public class PtlDeviceComm
    {
        public XGate _XGate;
        private string _XGateIP = "************";
        public PtlDeviceComm(string IP)
        {
            this._XGateIP = IP;
            this._XGate = new XGate(this._XGateIP);
        }

        public void InitPtlDevice()
        {
            AddPickPtl(1, 16, 20);
            AddApplyPtl(1, 21, 25);

            AddPickPtl(2, 61, 65);
            AddApplyPtl(2, 66, 70);

            AddPickPtl(2, 71, 75);
            AddApplyPtl(2, 76, 80);

            this._XGate.StartUnicastCommandQueue();

            foreach (RS485Bus bus in this._XGate.Buses)
            {
                //启动通讯后才可订阅各通讯通道的错误
                bus.CommunicationClient.ConnectionError += new EventHandler<ConnectionErrorEventArgs>(this.rs485Bus_CommunicationClient_ConnectionError);

                foreach (PtlDevice ptlDevice in bus.Devices)
                {
                    //订阅设备错误
                    ptlDevice.Error += new EventHandler<PtlDeviceErrorEventArgs>(this.ptlDevice_Error);

                    //订阅 OK 按钮按下事件，只在点亮并启用了采集时有效
                    if (ptlDevice is Ptl900U)
                        ((Ptl900U)ptlDevice).Pressed += new EventHandler<Ptl900UPressedEventArgs>(this.ptl900U_Pressed);

                    //初始化设备，清空残留的显示
                    ptlDevice.Initialize();
                }
            }
        }

        private void AddPickPtl(int busIndex, int min, int max)
        {
            byte bBusIndex = byte.Parse((busIndex - 1).ToString());

            for (int address = min; address <= max; address++)
            {
                Ptl900U ptl900U = new Ptl900U();
                ptl900U.Address = byte.Parse(address.ToString());
                ptl900U.MinorType = Ptl900UType.S2;

                this._XGate.Buses[bBusIndex].Devices.AddOrUpdate(ptl900U);
            }
        }

        private void AddApplyPtl(int busIndex, int min, int max)
        {
            byte bBusIndex = byte.Parse((busIndex - 1).ToString());

            for (int address = min; address <= max; address++)
            {
                Ptl900U ptl900U = new Ptl900U();
                ptl900U.Address = byte.Parse(address.ToString());
                ptl900U.MinorType = Ptl900UType.P0;

                this._XGate.Buses[bBusIndex].Devices.AddOrUpdate(ptl900U);
            }
        }



        //此事件处理方法由通讯线程回调执行，为不阻塞通讯和支持跨线程访问界面控件，所以使用 BeginInvoke
        private void rs485Bus_CommunicationClient_ConnectionError(object sender, ConnectionErrorEventArgs e)
        {
           string log=  e.Exception.Message;
        }

        private void ptlDevice_Error(object sender, PtlDeviceErrorEventArgs e)
        {
            string log = e.Exception.Message;
        }

        private void ptl900U_Pressed(object sender, Ptl900UPressedEventArgs e)
        {
            Ptl900U ptl900U = (Ptl900U)sender;

            switch (ptl900U.MinorType)
            {
                case Ptl900UType.S2:
                    foreach (KeyValuePair<Display900UItem, ushort> pair in e.ResultByItem)
                    {
                        //string str = string.Format("{0}:{1}", pair.Key, pair.Value);
                        this.DoPick(ptl900U.Address.ToString(),  Convert.ToString(pair.Value));
                        break;
                        //SiaSun.LMS.Implement.TCP.CommunicationOperation.comm2.BoxLeave(5, 0, 1, 7106, "A10010");
                        //new Implement.S_BaseService()._log.Debug(str);
                    }
                   
                    // 拣选标签按下
                    break;
                case Ptl900UType.P0:
                    // 离开标签按下
                    this.LetBoxLeave(ptl900U.Address.ToString());
                    break;
            }
        }


        public void TestDisplay()
        {
            string strBusIndex = "1";
            string strPtlPickAddress = "16";
            try
            {
                byte busIndex = (byte)(byte.Parse(strBusIndex) - 1);
                byte ptl900UAddress = byte.Parse(strPtlPickAddress);

                ///准备显示内容，可多项明细一起下发
                List<Display900UItem> items = new List<Display900UItem>();
                int count = 1;
                for (int i = 0; i < count; i++)
                {
                    Display900UItem item = new Display900UItem();
                    item.BatchCode = "Batch123456";
                    item.Name = "物料名goods";
                    item.Description = "物料描述一种神奇的物料用于测试";
                    Direction direction = Direction.None;
                    item.Direction = direction;
                    item.Unit = "个";
                    item.SubLocation = (byte)(100);
                    item.LongSubLocation = "PICK";
                    item.Count = (ushort)Math.Min(ushort.MaxValue, Convert.ToInt32("100"));
                    items.Add(item);
                }

                LightMode lightMode = new LightMode();
                lightMode.Color = LightColor.Green;
                lightMode.Period = LightOnOffPeriod.Period100;
                lightMode.Ratio = LightOnOffRatio.RatioP1V0;

                SurfaceBackground background = SurfaceBackground.Default;

                //必须启用采集才会检测 OK 按钮是否按下
                bool mustCollect = true;

                bool enableF1 = false;
                int waitMillisecondsForDisplay = 0;

                //按总线索引和设备地址获取 Ptl900U 拣选标签
                Ptl900U ptl900U = (Ptl900U)this._XGate.Buses[busIndex].Devices[ptl900UAddress];

                //下发显示内容
                ptl900U.Display(items, lightMode, SoundMode.Silent, background, mustCollect, enableF1, waitMillisecondsForDisplay);
                
            }
            catch (Exception ex)
            {
                
            }
        }

        /// <summary>
        ///  二期电子标签显示
        ///  
        /// 可以通过地址判断是哪个总线
        /// </summary>
        /// <param name="address">标签地址</param>
        /// <param name="showInt">显示数量</param>
        /// <param name="pickMode">拣选模式 默认PICK，也可以是SCAN</param>
        /// <param name="goodsCode">物料编码</param>
        /// <param name="description">显示描述内容</param>
        /// <param name="goodsUnit">物料单位</param>
        public void PickDisplay(string address,int showInt,string pickMode,string goodsCode,string description,string goodsUnit)
        {
            byte busIndex = 0;
            try
            {
                byte ptl900UAddress = byte.Parse(address);
                if (ptl900UAddress > 50)
                {
                    busIndex = 1;
                }

                ///准备显示内容，可多项明细一起下发
                List<Display900UItem> items = new List<Display900UItem>();
                int count = 1;
                for (int i = 0; i < count; i++)
                {
                    Display900UItem item = new Display900UItem();
                    item.Count = (ushort)Math.Min(ushort.MaxValue, showInt);
                    item.Name = goodsCode;
                    item.Description = description;
                    Direction direction = Direction.None;
                    item.Direction = direction;
                    item.Unit = goodsUnit;
                    item.LongSubLocation = pickMode;
                    items.Add(item);
                }

                LightMode lightMode = new LightMode();

                if (pickMode == "SCAN")
                {
                    lightMode.Color = LightColor.Red;
                    lightMode.Period = LightOnOffPeriod.Period500;
                    lightMode.Ratio = LightOnOffRatio.RatioP1V1;
                }
                else
                {
                    lightMode.Color = LightColor.Cyan;
                    lightMode.Period = LightOnOffPeriod.Period100;
                    lightMode.Ratio = LightOnOffRatio.RatioP1V0;
                }
                

                SurfaceBackground background = SurfaceBackground.Default;

                //必须启用采集才会检测 OK 按钮是否按下
                bool mustCollect = true;

                bool enableF1 = false;
                int waitMillisecondsForDisplay = 0;

                //按总线索引和设备地址获取 Ptl900U 拣选标签
                Ptl900U ptl900U = (Ptl900U)this._XGate.Buses[busIndex].Devices[ptl900UAddress];
                
                ptl900U.ClearCommandQueue();
                ptl900U.Clear();
                //下发显示内容
                ptl900U.Display(items, lightMode, SoundMode.Silent, background, mustCollect, enableF1, waitMillisecondsForDisplay);

            }
            catch (Exception ex)
            {

            }
        }

        private void LetBoxLeave(string address)
        {
            bool bResult = false;
            S_BaseService _BaseService = new S_BaseService();

            try
            {
                Model.T_PICK_POSITION mPickPosition = _BaseService._P_T_PICK_POSITION.GetModelByDZBQ_PROPERTY5(address);

                _BaseService._P_Base_House.BeginTransaction();

                if (mPickPosition != null)
                {
                    Model.WH_CELL mWH_CELL = _BaseService._P_WH_CELL.GetModel(mPickPosition.WH_CELL_ID);
                    Model.T_PICK_STATION mPickStation = _BaseService._P_T_PICK_STATION.GetModel(mPickPosition.STATION_ID);
                    if (mPickStation != null)
                    {
                        if (mWH_CELL != null)
                        {
                            Model.STORAGE_MAIN mSTORAGE_MAIN = _BaseService._P_STORAGE_MAIN.GetModelCellID(mWH_CELL.CELL_ID);

                            if (mSTORAGE_MAIN != null)
                            {
                                #region if
                                List<Model.STORAGE_LIST> STORAGE_LISTS = _BaseService._P_STORAGE_LIST.GetListStorageID(mSTORAGE_MAIN.STORAGE_ID).ToList();

                                if (STORAGE_LISTS.Where(t => t.GOODS_ID == _BaseService._P_GOODS_MAIN.GetModel("emptyBox").GOODS_ID).ToList().Count == 0)
                                {

                                    #region if
                                    Model.WH_CELL tWH_CELL = _BaseService._P_WH_CELL.GetModel(mPickStation.PROPERTY2);
                                    if (tWH_CELL != null)
                                    {
                                        mSTORAGE_MAIN.CELL_ID = tWH_CELL.CELL_ID;

                                    }
                                    mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.KitBox.ToString("d");
                                    _BaseService._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);

                                    #endregion
                                }
                                else
                                {
                                    /*
                                     * 根据现场反映，拣选工作站拣选工作点上的空箱被人工放走，需要将库存立即移走
                                     * 以防后面的空箱补位后，页面刷新不出新到空箱
                                     */

                                    #region else
                                    Model.WH_CELL tWH_CELL = _BaseService._P_WH_CELL.GetModel(mPickStation.PROPERTY2);
                                    if (tWH_CELL != null)
                                    {
                                        mSTORAGE_MAIN.CELL_ID = tWH_CELL.CELL_ID;
                                    }
                                    //mSTORAGE_MAIN.CELL_MODEL = Enum.CellModel.KitBox.ToString("d");
                                    _BaseService._P_STORAGE_MAIN.Update(mSTORAGE_MAIN);
                                    #endregion
                                }

                                #endregion
                                bResult = true;

                                switch (mPickStation.PROPERTY1)
                                {
                                    case "4":
                                        TCP.CommunicationOperation.comm4.BoxLeave((short)4, (short)1, 1, Int32.Parse(mPickPosition.DZBQ_PROPERTY2), mSTORAGE_MAIN.STOCK_BARCODE);// 1 离开
                                        break;
                                    case "5":
                                        TCP.CommunicationOperation.comm5.BoxLeave((short)5, (short)1, 1, Int32.Parse(mPickPosition.DZBQ_PROPERTY2), mSTORAGE_MAIN.STOCK_BARCODE);// 1 离开
                                        break;
                                    case "6":
                                        TCP.CommunicationOperation.comm6.BoxLeave((short)6, (short)1, 1, Int32.Parse(mPickPosition.DZBQ_PROPERTY2), mSTORAGE_MAIN.STOCK_BARCODE);// 1 离开
                                        break;
                                }
                            }
                        }
                    }
                }

            }
            catch (Exception ex)
            {
                bResult = false;
            }
            finally
            {
                _BaseService._P_Base_House.SaveTransaction(bResult);

            }
        }

        /// <summary>
        /// 通过address可以判断是哪个总线 1-50 bus1 51-100 bus2
        /// </summary>
        /// <param name="address"></param>
        public void BoxLeaveDisplay(string address)
        {
            byte busIndex = 0;
            try
            {
                byte ptl900UAddress = byte.Parse(address);
                if (ptl900UAddress > 50)
                {
                    busIndex = 1;
                }
                
                ///准备显示内容，可多项明细一起下发
                List<Display900UItem> items = new List<Display900UItem>();
                int count = 1;
                for (int i = 0; i < count; i++)
                {
                    Display900UItem item = new Display900UItem();
                    item.Count = (ushort)Math.Min(ushort.MaxValue, Convert.ToInt32("1"));
                    items.Add(item);
                }

                LightMode lightMode = new LightMode();
                lightMode.Color = LightColor.Off;
                lightMode.Period = LightOnOffPeriod.Period200;
                lightMode.Ratio = LightOnOffRatio.RatioP1V0;

                SurfaceBackground background = SurfaceBackground.Default;

                //必须启用采集才会检测 OK 按钮是否按下
                bool mustCollect = true;

                bool enableF1 = false;
                int waitMillisecondsForDisplay = 0;

                //按总线索引和设备地址获取 Ptl900U 拣选标签
                Ptl900U ptl900U = (Ptl900U)this._XGate.Buses[busIndex].Devices[ptl900UAddress];
                ptl900U.ClearCommandQueue();
                ptl900U.Clear();
                //下发显示内容
                ptl900U.Display(items, lightMode, SoundMode.Silent, background, mustCollect, enableF1, waitMillisecondsForDisplay);

            }
            catch (Exception ex)
            {

            }
        }


        private void DoPick(string address,string value)
        {
            /*
            * 通过电子标签的ID获得该电子标签所在的拣选工作点
            * 通过拣选工作点上记录的拣选任务MANAGE_MAIN的ID，获得拣选任务，并调用拣选任务完成方法
            * 然后切换到新的拣选任务
            */
            Model.T_PICK_POSITION mT_PICK_POSITION = new Persistence.P_T_PICK_POSITION().GetModelByDZBQ_MAC(address);

            if (mT_PICK_POSITION != null)
            {
                try
                {
                    int manage_id = Convert.ToInt32(mT_PICK_POSITION.DZBQ_PROPERTY1);

                    bool bResult = false;
                    string sResult = string.Empty;


                    //bResult = new ManagePick().ManageComplete(mT_PICK_POSITION, manage_id, true, out sResult);
                    bResult = new ManagePick().ManageComplete(mT_PICK_POSITION, manage_id, value, true, out sResult);

                    if (bResult)
                    {
                        WDZ.PickStationOperation mPickStationOperation = new WDZ.PickStationOperation(mT_PICK_POSITION.STATION_ID);

                        mPickStationOperation.MoveNewManage();
                    }

                    if (bResult)
                    {
                        //new Implement.S_BaseService()._log.Debug(string.Format("[拣选工作站] 电子标签拣选任务完成成功！任务编号:{0}",manage_id));
                        new Implement.S_BaseService()._log.Debug(string.Format("[拣选工作站4-6] 电子标签拣选任务完成成功！任务编号:{0}", manage_id));

                    }
                    else
                    {
                        //new Implement.S_BaseService()._log.Debug(string.Format("[拣选工作站] 电子标签拣选任务完成失败！任务编号:{0},错误信息{1}", manage_id,sResult));
                        new Implement.S_BaseService()._log.Debug(string.Format("[拣选工作站4-6] 电子标签拣选任务完成失败！任务编号:{0},错误信息{1}", manage_id, sResult));

                    }
                }
                catch (Exception ex)
                {
                    // new Implement.S_BaseService()._log.FatalFormat("[拣选工作站] 电子标签按钮回传异常,EX401 回传参数:Tagid:{0}-Number:{1}-Ip:{2}-KeyCode:{3}-Locator:{4};error:{5}", rs.Tagid, rs.Number, rs.Ip, rs.KeyCode, rs.Locator, ex.Message);
                    new Implement.S_BaseService()._log.FatalFormat("[拣选工作站4-6] 电子标签按钮回传异常,EX40100 回传参数:address:{0}-value:{1}-error:{2}", address, value,  ex.Message);

                }

            }
        }
    }
}
