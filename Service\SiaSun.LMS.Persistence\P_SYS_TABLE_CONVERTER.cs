﻿/***************************************************************************
 * 
 *       功能：     持久层基类
 *       作者：     Siasun
 *       日期：     2013/4/11
 * 
 *       修改日期： 
 *       修改人：
 *       修改内容：
 * 
 * *************************************************************************/
namespace SiaSun.LMS.Persistence
{
	using System;
	using System.Collections;
	using System.Collections.Generic;
	using IBatisNet.Common;
	using IBatisNet.DataMapper;
	using IBatisNet.Common.Exceptions;
	using SiaSun.LMS.Model;

	/// <summary>
	/// SYS_TABLE_CONVERTER
	/// </summary>
	public class P_SYS_TABLE_CONVERTER : P_Base_House
	{
		public P_SYS_TABLE_CONVERTER ()
		{
			//
			// TODO: 此处添加SYS_TABLE_CONVERTER的构造函数
			//
		}

		/// <summary>
		/// 得到列表
		/// </summary>
		public IList<SYS_TABLE_CONVERTER> GetList()
		{
			return ExecuteQueryForList<SYS_TABLE_CONVERTER>("SYS_TABLE_CONVERTER_SELECT",null);
		}

        public IList<SYS_TABLE_CONVERTER> GetList_ConverterCode(string TABLE_CONVERTER_CODE)
        {
            return ExecuteQueryForList<SYS_TABLE_CONVERTER>("SYS_TABLE_CONVERTER_SELECT_CONVERTER_CODE", TABLE_CONVERTER_CODE);
        }

		/// <summary>
		/// 新建
		/// </summary>
		public int Add(SYS_TABLE_CONVERTER sys_table_converter)
		{
            if (_isOracelProvider)
            {
                int id = this.GetPrimaryID("SYS_TABLE_CONVERTER");
                sys_table_converter.TABLE_CONVERTER_ID = id;
            }

            return ExecuteInsert("SYS_TABLE_CONVERTER_INSERT",sys_table_converter);
		}
		/// <summary>
		/// 修改
		/// </summary>
		public int Update(SYS_TABLE_CONVERTER sys_table_converter)
		{
			return ExecuteUpdate("SYS_TABLE_CONVERTER_UPDATE",sys_table_converter);
		}
		
		/// <summary>
		/// 得到明细
		/// </summary>
		/// <param name="id"></param>
		/// <returns></returns>
		public SYS_TABLE_CONVERTER GetModel(System.Int32 TABLE_CONVERTER_ID)
		{
			return ExecuteQueryForObject<SYS_TABLE_CONVERTER>("SYS_TABLE_CONVERTER_SELECT_BY_ID",TABLE_CONVERTER_ID);
		}

		/// <summary>
		/// 删除
		/// </summary>
		/// <param name="id"></param>
		public int Delete(System.Int32 TABLE_CONVERTER_ID)
		{
			return ExecuteDelete("SYS_TABLE_CONVERTER_DELETE",TABLE_CONVERTER_ID);
		}
		

	}
}
