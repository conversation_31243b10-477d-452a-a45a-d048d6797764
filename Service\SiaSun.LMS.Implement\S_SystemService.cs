﻿using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Data;
using System.ServiceModel;
using System.Reflection;

namespace SiaSun.LMS.Implement
{
    [ServiceBehavior(IncludeExceptionDetailInFaults = true, InstanceContextMode = InstanceContextMode.Single, ConcurrencyMode = ConcurrencyMode.Multiple, MaxItemsInObjectGraph = int.MaxValue)] 
    public partial class S_SystemService : SiaSun.LMS.Implement.S_BaseService, SiaSun.LMS.Interface.I_SystemService
    {

        public S_SystemService()
        {
        }

        #region     ------ROLE

        // 角色-获取列表
        public IList<SiaSun.LMS.Model.SYS_ROLE> ROLE_GetList(int USER_ID)
        {
            return this._P_SYS_ROLE.GetList(USER_ID);
        }

        #endregion

        #region     ------ROLE_WINDOW

        /// <summary>
        /// 获得角色的窗体控件设置列表
        /// </summary>
        public IList<Model.SYS_ROLE_WINDOW> ROLE_WINDOW_GetList_ROLE_MENU(int ROLE_ID, int MENU_ID)
        {
            return this._P_SYS_ROLE_WINDOW.GetListRoleIDMenuID(ROLE_ID, MENU_ID);
        }

        /// <summary>
        /// 获得ROLE_WINDOW实例
        /// </summary>
        public Model.SYS_ROLE_WINDOW ROLE_WINDOW_GetModel_MENU_CONTROL(int ROLE_ID,int MENU_ID,string CONTROL_NAME)
        {
            return this._P_SYS_ROLE_WINDOW.GetModelRoleIDMenuIDControlName(ROLE_ID,MENU_ID, CONTROL_NAME);

        }

        /// <summary>
        /// 保存ROLE_WINDOW更改
        /// </summary>
        /// <returns></returns>
        public bool ROLE_WINDOW_Save(int ROLE_ID,int MENU_ID,IList<Model.SYS_ROLE_WINDOW> listROLE_WINDOW,out string strResult)
        {
            bool boolResult = true;
            strResult = string.Empty;
            try
            {
                if (listROLE_WINDOW.Count == 0)
                {
                    //删除记录
                    foreach (Model.SYS_ROLE_WINDOW mROLE_WINDOW in this._P_SYS_ROLE_WINDOW.GetListRoleIDMenuID(ROLE_ID, MENU_ID))
                    {
                        this._P_SYS_ROLE_WINDOW.Delete(mROLE_WINDOW.ROLE_WINDOW_ID);
                    }
                }
                else
                {
                    foreach (Model.SYS_ROLE_WINDOW roleWin in listROLE_WINDOW)
                    {
                        Model.SYS_ROLE_WINDOW mROLE_WINDOW = this._P_SYS_ROLE_WINDOW.GetModelRoleIDMenuIDControlName(ROLE_ID, roleWin.MENU_ID, roleWin.CONTROL_NAME);
                        //判断是否存在记录
                        if (mROLE_WINDOW == null)
                        {
                            this._P_SYS_ROLE_WINDOW.Add(roleWin);
                        }
                        else
                        {
                            roleWin.ROLE_WINDOW_ID = mROLE_WINDOW.ROLE_WINDOW_ID;
                            this._P_SYS_ROLE_WINDOW.Update(roleWin);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                boolResult = false;
                strResult = ex.Message;
            }
            return boolResult;
        }

        #endregion

        #region     ------USER

        //用户-登录
        public bool USER_LOGIN(string USER_CODE, string USER_PASSWORD, out SiaSun.LMS.Model.SYS_USER user)
        {
            try
            {
                user = this._P_SYS_USER.Login(USER_CODE, USER_PASSWORD);
            }
            catch (Exception ex)
            {
                user = null;
                //this._log.Fatal( string.Format("调用方法{0}发生异常。", MethodBase.GetCurrentMethod().Name), ex);
            }                       
            return user != null;

        }



        // 修改密码
        public bool USER_PASSWORD(string USER_CODE, string USER_PASSWORD_OLD, string USER_PASSWORD_NEW, out string sResult)
        {
            bool bResult = true;
            sResult = string.Empty;
            SiaSun.LMS.Model.SYS_USER USER_PASSWORD = this._P_SYS_USER.GetModel(USER_CODE);
            USER_PASSWORD.USER_PASSWORD = USER_PASSWORD_NEW;
            this._P_SYS_USER.Update(USER_PASSWORD);
            return bResult;
        }

        #endregion        

        #region     ------ITEM
        
        /// <summary>
        /// 获得编码列表
        /// </summary>
        /// <param name="ITEM_CODE">编码</param>
        public DataTable ITEM_LIST_GetDictionary(string ITEM_CODE)
        {
            using (DataTable dt = this.GetList(string.Format("SELECT ITEM_LIST_CODE AS value,ITEM_LIST_NAME AS name FROM V_SYS_ITEM WHERE ITEM_CODE='{0}' and ITEM_LIST_FLAG = '1' order by item_list_order", ITEM_CODE)))
            {
                return dt;
            }

            //return null;
        }

        /// <summary>
        /// 根据ITEM_CODE获得列表
        /// </summary>
        public IList<SiaSun.LMS.Model.SYS_ITEM_LIST> ITEM_LIST_GetList_ITEM_CODE(string ITEM_CODE)
        {
            return this._P_SYS_ITEM_LIST.GetListItemCode(ITEM_CODE);
        }

        #endregion

        #region     ------MENU

        /// <summary>
        /// 根据菜单编码获得菜单实例
        /// </summary>
        public SiaSun.LMS.Model.SYS_MENU MENU_GetModel(int MENU_ID)
        {
            return this._P_SYS_MENU.GetModel(MENU_ID);
        }         

        /// <summary>
        /// 菜单-获取列表
        /// </summary>
        public IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList()
        {
            IList<SiaSun.LMS.Model.SYS_MENU> lsSYS_MENU =null;

            lsSYS_MENU = this._P_SYS_MENU.GetList_MENU_PARAMETER();

            return lsSYS_MENU;
        }

        /// <summary>
        /// 菜单-获取列表
        /// </summary>
        /// <param name="ROLE_ID">角色编号</param>
        /// <param name="bSelect">是否选定</param>
        public IList<SiaSun.LMS.Model.SYS_MENU> MENU_GetList_ROLE_Select(int ROLE_ID, bool bSelect)
        {
            IList<SiaSun.LMS.Model.SYS_MENU> lsSYS_MENU =null;

            if (bSelect)
            {
                lsSYS_MENU = this._P_SYS_MENU.GetList_ROLE_ID(ROLE_ID);
            }
            else
            {
                lsSYS_MENU = this._P_SYS_MENU.GetList_ROLE_ID_NO(ROLE_ID);
            }

            return lsSYS_MENU;
        }

        #endregion

        #region     ------RELATION


        /// <summary>
        /// 获得关系实例
        /// </summary>
        public Model.SYS_RELATION RELATION_GetModel(string RELATION_CODE)
        {
            return this._P_SYS_RELATION.GetModelRelationCode(RELATION_CODE);


        }


        /// <summary>
        /// 获得关系列表
        /// </summary>
        public IList<Model.SYS_RELATION_LIST> RELATION_LIST_GetList_ID1(int RELATION_ID, int RELATION_ID1)
        {
            return this._P_SYS_RELATION_LIST.GetListRelationIDRelaionID1(RELATION_ID, RELATION_ID1);
        }

        /// <summary>
        /// 添加关系
        /// </summary>
        public bool RELATION_LIST_Add(string RELATION_CODE, int RELATION_ID1, int[] List_RELATION_ID2, out string Result)
        {
            Result = string.Empty;
            SiaSun.LMS.Model.SYS_RELATION mSYS_RELATION = this._P_SYS_RELATION.GetModelRelationCode(RELATION_CODE);
            if (mSYS_RELATION == null)
            {
                Result = this.MessageConverter_GetKeyValue("Relation_CheckRelationExists", string.Format("RelationCode-{0}", RELATION_CODE));
                return false;
            }

            //获得选定的列表
            IList<SiaSun.LMS.Model.SYS_RELATION_LIST> list_RELATION_LIST = this._P_SYS_RELATION_LIST.GetListRelationIDRelaionID1(mSYS_RELATION.RELATION_ID, RELATION_ID1);

            //删除取消选定的关系
            foreach (SiaSun.LMS.Model.SYS_RELATION_LIST mSYS_RELATION_LIST in list_RELATION_LIST)
            {
                if (mSYS_RELATION_LIST.RELATION_ID1 == 0 && mSYS_RELATION_LIST.RELATION_ID2 == 0)
                    continue;
                if (!List_RELATION_ID2.Contains(mSYS_RELATION_LIST.RELATION_ID2))
                {
                    this._P_SYS_RELATION_LIST.Delete(mSYS_RELATION_LIST.RELATION_LIST_ID);
                }
            }

            //添加新的关系
            for (int i = 0; i < List_RELATION_ID2.Length; i++)
            {
                //检查是否重复
                SiaSun.LMS.Model.SYS_RELATION_LIST mSYS_RELATION_LIST = this._P_SYS_RELATION_LIST.GetModelRelationIDRelationID1RelationID2(mSYS_RELATION.RELATION_ID, RELATION_ID1, List_RELATION_ID2[i]);
                if (mSYS_RELATION_LIST == null)
                {
                    mSYS_RELATION_LIST = new SiaSun.LMS.Model.SYS_RELATION_LIST();
                    mSYS_RELATION_LIST.RELATION_ID = mSYS_RELATION.RELATION_ID;
                    mSYS_RELATION_LIST.RELATION_ID1 = RELATION_ID1;
                    mSYS_RELATION_LIST.RELATION_ID2 = List_RELATION_ID2[i];
                    mSYS_RELATION_LIST.RELATION_LIST_FLAG = 1;
                    this._P_SYS_RELATION_LIST.Add(mSYS_RELATION_LIST);
                }
            }

            return true;
        }

        #endregion

        #region     ------SYS_TABLE_CONVERTER_LIST

        /// <summary>
        /// 获得所有映射列表
        /// </summary>
        public IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList()
        {
            return this._P_SYS_TABLE_CONVERTER.GetList();
        }

        /// <summary>
        /// 根据表名获得所有映射列表
        /// </summary>
        public IList<Model.SYS_TABLE_CONVERTER> TABLE_CONVERTER_GetList_ConverterCode(string TABLE_CONVERTER_CODE)
        {
            return this._P_SYS_TABLE_CONVERTER.GetList_ConverterCode(TABLE_CONVERTER_CODE);
        }

        /// <summary>
        /// 根据CONVERTER_ID获得所有列表
        /// </summary>
        /// <returns></returns>
        public IList<Model.SYS_TABLE_CONVERTER_LIST> TABLE_CONVERTER_LIST_GetList_ConverterID(int TABLE_CONVERTER_ID)
        {
            return this._P_SYS_TABLE_CONVERTER_LIST.GetList_ConverterID(TABLE_CONVERTER_ID);
        }

        /// <summary>
        /// 根据数据导入模板导入数据
        /// </summary>
        public DataSet SYS_TABLE_CONVERTER_Import(string TABLE_CONVERTER_CODE, DataTable tableImport, out string strResult)
        {
            DataSet dsImport = new DataSet();
            strResult = string.Empty;

            //判断导入数据是否空
            if (tableImport.Rows.Count > 0)
            {
                try
                {
                    //获得转换列表
                    IList<Model.SYS_TABLE_CONVERTER> listTABLE_CONVERTER = this._P_SYS_TABLE_CONVERTER.GetList_ConverterCode(TABLE_CONVERTER_CODE);
                    if (listTABLE_CONVERTER.Count == 0)
                    {
                        strResult = string.Format("TABLE_CONVERTER_CODE={0} is not exists in SYS_TABLE_CONVERTER.", TABLE_CONVERTER_CODE);
                        return dsImport;
                    }

                    //获得转换信息实例
                    Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER = listTABLE_CONVERTER[0];
                    //获得转换信息列表
                    IList<Model.SYS_TABLE_CONVERTER_LIST> listTABLE_CONVERTER_LIST = this._P_SYS_TABLE_CONVERTER_LIST.GetList_ConverterID(mTABLE_CONVERTER.TABLE_CONVERTER_ID);
                    if (listTABLE_CONVERTER_LIST.Count == 0)
                    {
                        strResult = string.Format("There is no data in SYS_TABLE_CONVERTER_LIST.");
                        return dsImport;
                    }

                    //设置表名并添加到数据集中
                    DataTable tableParent = new DataTable(mTABLE_CONVERTER.PARENT_TABLE);
                    DataTable tableChild = new DataTable(mTABLE_CONVERTER.CHILD_TABLE);
                    dsImport.Tables.AddRange(new DataTable[] { tableParent, tableChild });

                    //遍历所有转换列表，构建父级表和子级表的结构
                    foreach (Model.SYS_TABLE_CONVERTER_LIST mTABLE_CONVERTER_LIST in listTABLE_CONVERTER_LIST)
                    {
                        //创建新列并设置列属性
                        DataColumn col = new DataColumn(mTABLE_CONVERTER_LIST.COLUMN_NAME);
                        col.Unique = mTABLE_CONVERTER_LIST.UNIQUE_FLAG == "1";
                        col.AllowDBNull = mTABLE_CONVERTER_LIST.ISNULL_FLAG == "1";

                        //获得父级表结构
                        if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && mTABLE_CONVERTER.PARENT_TABLE == mTABLE_CONVERTER_LIST.TABLE_NAME)
                        {
                            if (!tableParent.Columns.Contains(mTABLE_CONVERTER_LIST.COLUMN_NAME))
                            {
                                tableParent.Columns.Add(col);
                            }
                        }

                        //获得子级表结构  
                        if (!string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE) && mTABLE_CONVERTER.CHILD_TABLE == mTABLE_CONVERTER_LIST.TABLE_NAME)
                        {
                            if (!tableChild.Columns.Contains(mTABLE_CONVERTER_LIST.COLUMN_NAME))
                            {
                                tableChild.Columns.Add(col);
                            }
                        }
                    }

                    //遍历导入数据信息，向父级表和子级表中添加数据，当前只支持父级表中存在一条数据
                    //如果只有父级表，没有子级表
                    if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                    {
                        //导入父级表数据
                        this.TABLE_CONVERTER_ImportRow(false, tableImport, mTABLE_CONVERTER.PARENT_TABLE, mTABLE_CONVERTER, listTABLE_CONVERTER_LIST, ref tableParent);
                    }

                    //如果存在父级表和子级表
                    if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && !string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                    {
                        //导入父级表--父级表中仅有一条记录
                        this.TABLE_CONVERTER_ImportRow(true, tableImport, mTABLE_CONVERTER.PARENT_TABLE, mTABLE_CONVERTER, listTABLE_CONVERTER_LIST, ref tableParent);
                        //导入子级表
                        this.TABLE_CONVERTER_ImportRow(false, tableImport, mTABLE_CONVERTER.CHILD_TABLE, mTABLE_CONVERTER, listTABLE_CONVERTER_LIST, ref tableChild);
                    }
                }
                catch (Exception ex)
                {
                    strResult = ex.Message;
                }
            }

            return dsImport;
        }

        /// <summary>
        /// 导入数据表
        /// </summary>
        private void TABLE_CONVERTER_ImportRow(bool IsOnlyOne, DataTable tableImport, string TABLE_NAME, Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER, IList<Model.SYS_TABLE_CONVERTER_LIST> listTABLE_CONVERTER_LIST, ref DataTable tableAdd)
        {
            try
            {
                //获得拆分列信息
                //using (DataTable tableSplitProperty = this._P_SYS_SPLIT_PROPERTY.GetTable_TYPE_KEY(mTABLE_CONVERTER.SPLIT_PROPERTY_TYPE, mTABLE_CONVERTER.SPLIT_PROPERTY_KEY))
                //{
                    //遍历导入数据信息，向父级表和子级表中添加数据，当前只支持父级表中存在一条数据
                    foreach (DataRow rowImport in tableImport.Rows)
                    {
                        if (IsOnlyOne && tableAdd.Rows.Count > 0)
                            break;

                        bool boolResult = true;
                        DataRow rowAdd = tableAdd.NewRow();

                        //组合拆分列属性值
                        //if (!string.IsNullOrEmpty(mTABLE_CONVERTER.SPLIT_PROPERTY_COLUMN) && tableAdd.Columns.Contains(mTABLE_CONVERTER.SPLIT_PROPERTY_COLUMN))
                        //{
                        //    if (tableSplitProperty.Rows.Count > 0)
                        //    {
                        //        string strSplitPropertyValue = null;
                        //        IDictionary<string, string> dicSplitPropertyCodeValue = new Dictionary<string, string>();
                        //        //遍历拆分属性
                        //        foreach (DataRow rowSplit in tableSplitProperty.Rows)
                        //        {
                        //            if (rowSplit.IsNull("SYS_PROPERTY_NAME") || string.IsNullOrEmpty(rowSplit["SYS_PROPERTY_NAME"].ToString()) || rowSplit.IsNull("SYS_PROPERTY_CODE") || string.IsNullOrEmpty(rowSplit["SYS_PROPERTY_CODE"].ToString()))
                        //                continue;
                        //            //判断导入列是否存在
                        //            if (tableImport.Columns.Contains(rowSplit["SYS_PROPERTY_NAME"].ToString()))
                        //            {
                        //                dicSplitPropertyCodeValue.Add(rowSplit["SYS_PROPERTY_CODE"].ToString(), rowImport[rowSplit["SYS_PROPERTY_NAME"].ToString()].ToString());
                        //            }
                        //        }
                        //        //获得组合属性
                        //        strSplitPropertyValue = this.SYS_SPLIT_PROPERTY_GetPropertyValue(mTABLE_CONVERTER.SPLIT_PROPERTY_TYPE, mTABLE_CONVERTER.SPLIT_PROPERTY_KEY, dicSplitPropertyCodeValue);
                        //        rowAdd[mTABLE_CONVERTER.SPLIT_PROPERTY_COLUMN] = strSplitPropertyValue;
                        //    }
                        //}

                        //非拆分组合列
                        foreach (DataColumn colImport in tableImport.Columns)
                        {
                            //判断该列是否存在映射
                            if (listTABLE_CONVERTER_LIST.Count(r => r.CONVERT_COLUMN_NAME == colImport.ColumnName) > 0)
                            {
                                //获得映射列表实例
                                Model.SYS_TABLE_CONVERTER_LIST mTABLE_CONVERTER_LIST = listTABLE_CONVERTER_LIST.First(r => r.CONVERT_COLUMN_NAME == colImport.ColumnName);

                                //添加表数据
                                if (mTABLE_CONVERTER_LIST.TABLE_NAME == TABLE_NAME)
                                {
                                    //校验是否唯一值
                                    if (mTABLE_CONVERTER_LIST.UNIQUE_FLAG == "1")
                                    {
                                        //获得条件字符串
                                        string strExistsWhere = string.Format("{0}='{1}'", mTABLE_CONVERTER_LIST.COLUMN_NAME, rowImport[colImport].ToString());
                                        //判断数据集和数据库表中的值是否重复
                                        if (tableAdd.Select(strExistsWhere).Length > 0 /*|| this.Exist(TABLE_NAME, strExistsWhere)*/)
                                        {
                                            boolResult = false;
                                            break;
                                        }
                                    }

                                    //校验是否空值
                                    if (mTABLE_CONVERTER_LIST.ISNULL_FLAG == "0")
                                    {
                                        //判断值是否空
                                        if (rowImport.IsNull(colImport) || string.IsNullOrEmpty(rowImport[colImport].ToString()))
                                        {
                                            boolResult = false;
                                            break;
                                        }
                                    }

                                    //行数据赋值
                                    rowAdd[mTABLE_CONVERTER_LIST.COLUMN_NAME] = rowImport[colImport];
                                }
                            }
                        }

                        //判断执行结果
                        if (boolResult)
                        {
                            tableAdd.Rows.Add(rowAdd);
                        }
                    }
                //}
            }
            catch (Exception ex)
            {
                throw new Exception(ex.Message);
            }
        }

        /// <summary>
        /// 提交保存导入数据
        /// </summary>
        public int TABLE_CONVERTER_Save(Model.SYS_TABLE_CONVERTER mTABLE_CONVERTER, DataSet dsImport, out string strResult)
        {
            int intAffect = 0;
            strResult = string.Empty;

            //判断数据集合
            if (dsImport.Tables.Count > 0)
            {
                //如果只有父级表，没有子级表
                if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                {
                    //判断是否有记录
                    if (dsImport.Tables[mTABLE_CONVERTER.PARENT_TABLE].Rows.Count > 0)
                    {
                        //提交父级表
                        intAffect = this.Save(dsImport.Tables[mTABLE_CONVERTER.PARENT_TABLE], mTABLE_CONVERTER.PARENT_TABLE);
                    }
                }

                //如果存在父级表和子级表
                if (!string.IsNullOrEmpty(mTABLE_CONVERTER.PARENT_TABLE) && !string.IsNullOrEmpty(mTABLE_CONVERTER.CHILD_TABLE))
                {
                    //父级表
                    using (DataTable tableParent = dsImport.Tables[mTABLE_CONVERTER.PARENT_TABLE])
                    {
                        //判断是否有记录
                        if (tableParent.Rows.Count > 0)
                        {
                            //提交父级表
                            intAffect = this.Save(tableParent, mTABLE_CONVERTER.PARENT_TABLE);
                            if (intAffect > 0)
                            {
                                //子级表
                                using (DataTable tableChild = dsImport.Tables[mTABLE_CONVERTER.CHILD_TABLE])
                                {
                                    //判断子表是否存在数据
                                    if (tableChild.Rows.Count > 0)
                                    {
                                        //获得关键字段值
                                        object objKeyValue = tableParent.Rows[0][mTABLE_CONVERTER.PARENT_KEY];

                                        //设置子级表值
                                        foreach (DataRow rowChild in dsImport.Tables[mTABLE_CONVERTER.CHILD_TABLE].Rows)
                                        {
                                            rowChild[mTABLE_CONVERTER.CHILD_FOREIGN_KEY] = objKeyValue;
                                        }

                                        //提交子级表数据
                                        intAffect = this.Save(tableChild, mTABLE_CONVERTER.CHILD_TABLE);
                                    }
                                }
                            }
                        }
                    }
                }
            }
            return intAffect;
        }

        #endregion


        /// <summary>
        /// 获取系统参数
        /// tzyg add 2017-02-27
        /// </summary>
        public Dictionary<string,string> GetSysParameter()
        {
            Dictionary<string, string> dicResult = new Dictionary<string, string>();

            DataTable dt = this._P_Base_House.GetDataTable(string.Format("select * from SYS_PARAMETER where PARAMETER_FLAG='1'"));
            foreach(DataRow dr in dt.Rows)
            {
                dicResult.Add(dr["PARAMETER_KEY"].ToString(), dr["PARAMETER_VALUE"].ToString());
            }

            if (dicResult == null || dicResult.Count == 0)
            {
                return null;
            }
            else
            {
                return dicResult;
            }
        }


        /// <summary>
        /// 根据编码查找系统参数
        /// </summary>
        public bool GetSysParameter(string paraKey,out string paraValue)
        {
            bool bResult = true;
            paraValue = string.Empty;

            try
            {
                DataTable dtPara = this.GetList(string.Format("select PARAMETER_VALUE from SYS_PARAMETER where PARAMETER_KEY='{0}' and PARAMETER_FLAG='1'",paraKey));
                if (dtPara != null && dtPara.Rows.Count == 1)
                {
                    paraValue = dtPara.Rows[0]["PARAMETER_VALUE"].ToString();
                }
                else
                {
                    bResult = false;
                    paraValue = string.Format("S_SystemService.GetSysParameter:未能获取数据 key-{0}", paraKey);
                }
            }
            catch(Exception ex)
            {
                bResult = false;
                paraValue = string.Format("S_SystemService.GetSysParameter:获取系统参数异常  {0}", ex.Message);
            }

            return bResult;
        }


        /// <summary>
        /// 查找系统参数，预设默认值
        /// </summary>
        public string GetSysParameter(string paraKey, string paraDefault)
        {
            string result = string.Empty;
            if (!GetSysParameter(paraKey, out result))
            {
                result = paraDefault;
            }
            return result;
        }

        /// <summary>
        /// 连接拣选工作站 不能断线重连前使用的过渡方案
        /// </summary>
        public string ConnectToPickStation(Model.SYS_USER user)
        {
            string result = "success";
            try
            {
                SiaSun.LMS.Implement.TCP.CommunicationOperation.Launch();
                this.CreateSysLog(Enum.LogThread.System, user.USER_NAME, Enum.LOG_LEVEL.Information, "S_SystemService.ConnectToPickStation:连接拣选工作站成功");
            }
            catch(Exception ex)
            {
                result = string.Format("S_SystemService.ConnectToPickStation:连接拣选工作站发生异常_异常信息[{0}]",ex.Message);
                this.CreateSysLog(Enum.LogThread.System, user.USER_NAME, Enum.LOG_LEVEL.Error, string.Format("S_SystemService.ConnectToPickStation:连接拣选工作站失败_发生异常_异常信息[{0}]", ex.Message));
            }

            return result;
        }
    }
}
